UL.jqueryFileTree {
	font:100% "Questrial", Helvetica, Arial, sans-serif;
	font-size:11pt;
	font-weight:normal;
	line-height:1.5em;
	margin:0;
	padding:0
}

UL.jqueryFileTree LI {
	list-style: none;
	padding: 0px;
	padding-left: 20px;
	margin: 0px;
	white-space: nowrap;
}

UL.jqueryFileTree A {
	color: #333;
	text-decoration: none;
	display: block;
	padding: 0px 2px;
}

UL.jqueryFileTree A:hover {
	background: #BDF;
}


/* Core Styles */
.jqueryFileTree LI.directory { background: url(../Content/images/directory.png) left top no-repeat; }
.jqueryFileTree LI.expanded { background: url(../Content/images/folder_open.png) left top no-repeat; }
.jqueryFileTree LI.file { background: url(../Content/images/file.png) left top no-repeat; }
.jqueryFileTree LI.wait { background: url(../Content/images/spinner.gif) left top no-repeat; }

/* File Extensions*/
.jqueryFileTree LI.ext_3gp { background: url(../Content/images/film.png) left top no-repeat;} 
.jqueryFileTree LI.ext_afp { background: url(../Content/images/code.png) left top no-repeat; }
.jqueryFileTree LI.ext_afpa { background: url(../Content/images/code.png) left top no-repeat; }
.jqueryFileTree LI.ext_asp { background: url(../Content/images/code.png) left top no-repeat; }
.jqueryFileTree LI.ext_aspx { background: url(../Content/images/code.png) left top no-repeat; }
.jqueryFileTree LI.ext_avi { background: url(../Content/images/film.png) left top no-repeat; }
.jqueryFileTree LI.ext_bat { background: url(../Content/images/application.png) left top no-repeat; }
.jqueryFileTree LI.ext_bmp { background: url(../Content/images/picture.png) left top no-repeat; }
.jqueryFileTree LI.ext_c { background: url(../Content/images/code.png) left top no-repeat; }
.jqueryFileTree LI.ext_cfm { background: url(../Content/images/code.png) left top no-repeat; }
.jqueryFileTree LI.ext_cgi { background: url(../Content/images/code.png) left top no-repeat; }
.jqueryFileTree LI.ext_com { background: url(../Content/images/application.png) left top no-repeat; }
.jqueryFileTree LI.ext_cpp { background: url(../Content/images/code.png) left top no-repeat; }
.jqueryFileTree LI.ext_css { background: url(../Content/images/css.png) left top no-repeat; }
.jqueryFileTree LI.ext_doc { background: url(../Content/images/doc.png) left top no-repeat; }
.jqueryFileTree LI.ext_exe { background: url(../Content/images/application.png) left top no-repeat; }
.jqueryFileTree LI.ext_gif { background: url(../Content/images/picture.png) left top no-repeat; }
.jqueryFileTree LI.ext_fla { background: url(../Content/images/flash.png) left top no-repeat; }
.jqueryFileTree LI.ext_h { background: url(../Content/images/code.png) left top no-repeat; }
.jqueryFileTree LI.ext_htm { background: url(../Content/images/html.png) left top no-repeat; }
.jqueryFileTree LI.ext_html { background: url(../Content/images/html.png) left top no-repeat; }
.jqueryFileTree LI.ext_jar { background: url(../Content/images/java.png) left top no-repeat; }
.jqueryFileTree LI.ext_jpg { background: url(../Content/images/picture.png) left top no-repeat; }
.jqueryFileTree LI.ext_jpeg { background: url(../Content/images/picture.png) left top no-repeat; }
.jqueryFileTree LI.ext_js { background: url(../Content/images/script.png) left top no-repeat; }
.jqueryFileTree LI.ext_lasso { background: url(../Content/images/code.png) left top no-repeat; }
.jqueryFileTree LI.ext_log { background: url(../Content/images/txt.png) left top no-repeat; }
.jqueryFileTree LI.ext_m4p { background: url(../Content/images/music.png) left top no-repeat; }
.jqueryFileTree LI.ext_mov { background: url(../Content/images/film.png) left top no-repeat; }
.jqueryFileTree LI.ext_mp3 { background: url(../Content/images/music.png) left top no-repeat; }
.jqueryFileTree LI.ext_mp4 { background: url(../Content/images/film.png) left top no-repeat; }
.jqueryFileTree LI.ext_mpg { background: url(../Content/images/film.png) left top no-repeat; }
.jqueryFileTree LI.ext_mpeg { background: url(../Content/images/film.png) left top no-repeat; }
.jqueryFileTree LI.ext_ogg { background: url(../Content/images/music.png) left top no-repeat; }
.jqueryFileTree LI.ext_pcx { background: url(../Content/images/picture.png) left top no-repeat; }
.jqueryFileTree LI.ext_pdf { background: url(../Content/images/pdf.png) left top no-repeat; }
.jqueryFileTree LI.ext_php { background: url(../Content/images/php.png) left top no-repeat; }
.jqueryFileTree LI.ext_png { background: url(../Content/images/picture.png) left top no-repeat; }
.jqueryFileTree LI.ext_ppt { background: url(../Content/images/ppt.png) left top no-repeat; }
.jqueryFileTree LI.ext_psd { background: url(../Content/images/psd.png) left top no-repeat; }
.jqueryFileTree LI.ext_pl { background: url(../Content/images/script.png) left top no-repeat; }
.jqueryFileTree LI.ext_py { background: url(../Content/images/script.png) left top no-repeat; }
.jqueryFileTree LI.ext_rb { background: url(../Content/images/ruby.png) left top no-repeat; }
.jqueryFileTree LI.ext_rbx { background: url(../Content/images/ruby.png) left top no-repeat; }
.jqueryFileTree LI.ext_rhtml { background: url(../Content/images/ruby.png) left top no-repeat; }
.jqueryFileTree LI.ext_rpm { background: url(../Content/images/linux.png) left top no-repeat; }
.jqueryFileTree LI.ext_ruby { background: url(../Content/images/ruby.png) left top no-repeat; }
.jqueryFileTree LI.ext_sql { background: url(../Content/images/db.png) left top no-repeat; }
.jqueryFileTree LI.ext_swf { background: url(../Content/images/flash.png) left top no-repeat; }
.jqueryFileTree LI.ext_tif { background: url(../Content/images/picture.png) left top no-repeat; }
.jqueryFileTree LI.ext_tiff { background: url(../Content/images/picture.png) left top no-repeat; }
.jqueryFileTree LI.ext_txt { background: url(../Content/images/txt.png) left top no-repeat; }
.jqueryFileTree LI.ext_vb { background: url(../Content/images/code.png) left top no-repeat; }
.jqueryFileTree LI.ext_wav { background: url(../Content/images/music.png) left top no-repeat; }
.jqueryFileTree LI.ext_wmv { background: url(../Content/images/film.png) left top no-repeat; }
.jqueryFileTree LI.ext_xls { background: url(../Content/images/xls.png) left top no-repeat; }
.jqueryFileTree LI.ext_xml { background: url(../Content/images/code.png) left top no-repeat; }
.jqueryFileTree LI.ext_zip { background: url(../Content/images/zip.png) left top no-repeat; }