/*
1.     -   FRAMEWORK
1.1   -   Initialize
1.2   -   General Settings
1.3   -   Typography
1.4   -   Icons
1.5   -   Forms & Tables

2.     -   THEME
2.1   -   General
2.2   -   Header
2.3   -   Navigation
2.4   -   Footer

3.     -   TEMPLATES
3.1   -   Login

4.     -   CUSTOMS
4.1   -   Responsive Fixes
4.2   -   Bootstrap Over-rides
4.3   -   IE Fixes
4.4   -   Print Styles
4.5   -   Tags
4.6   -   Typeahead
4.7   -   Tagsinput
4.8   -   Tile stats
4.9   -   Form wizard
4.10  -   Directory Browser
4.11  -   Bootstrap submenu
4.12  -   Kendo Over-rides
4.13  -   Wizard
*/


/*  1.1  -  Initialize 
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/
html {background:#FFF;color:#000;font-family:sans-serif;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%}
body {background:#FFF;color:#444;font:100% "Questrial", Helvetica, Arial, sans-serif;font-size:11pt;font-weight:normal;line-height:1.5em;margin:7em 0 0 0;padding:0}

/*  1.2 -  General Settings
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/
#create_exam_form_status {
    color: #ce4844;
    float: right;
}
/*  1.3 -  Typography
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/
@font-face {
    font-family: 'Novecentowide-Bold';
    src: url('../fonts/novecentowide-bold.eot');
    src: url('../fonts/novecentowide-bold.eot?#iefix') format('embedded-opentype'), url('../fonts/novecentowide-bold.woff') format('woff'), url('../fonts/novecentowide-bold.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
.novecento {
	font-family:"Novecentowide-Bold", Helvetica, Arial, sans-serif;
	font-size:inherit;
	text-rendering:auto;
	-webkit-font-smoothing:antialiased;
	-moz-osx-font-smoothing:grayscale;
}
@font-face {
	font-family:'Questrial';
	src:url('../fonts/questrial.ttf');
	src:url('../fonts/questrial.ttf') format('truetype');
	font-weight:normal;
    font-style:normal;
	-webkit-font-smoothing:antialiased;
  	-moz-osx-font-smoothing:grayscale;
}
.questrial {
	font-family:"Questrial", Helvetica, Arial, sans-serif;
	font-size:inherit;
	text-rendering:auto;
	-webkit-font-smoothing:antialiased;
	-moz-osx-font-smoothing:grayscale;
}

/*  1.4 -  Icons
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/


/*  1.5 -  Forms & Tables
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/
/*table th, table td:first-child {white-space:nowrap}*/
textarea {resize:vertical!important}
.form-group.required .control-label:after {
    content:"*";
}

.required:after {
    content:"*";
}


/*  1.7 -  Buttons
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/
/*.page-header button {margin-bottom:5px}*/



/*  2.   -  THEME
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/

/*  2.1 -  General
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/
.outline {border:#F00 solid 1px}
.no-borders {border:none!important}
.fixed {position:fixed;z-index:10;width:100%}
.top {top:0}
.bottom {bottom:0}
.web-app {padding:1em 0}
.border {border:#CF0 solid 1px}
.container {width:100%}
.page-header {border-bottom:1px solid #CCC;margin:10px 0;padding:0 0 5px 0}
.page-helper {font-size:18px;margin: 0px 0px 1em;padding: 2px 0px 1px 0px;}
.page-header-icon {margin-right: 10px}
/*.btn {border-radius:0px}*/
.btn-tagsinput-inline {width: 60px;margin-top:1px }
.panel-body-tab { padding: 0 }
.details-labels {font-size: 16px !important;  vertical-align: middle;}
dl.contacts dt { width:100px }
dl.contacts dd { margin-left:115px }
.checkboxContainer { padding-top:7px }
.evidence-list li {margin-bottom:5px}

/*  2.2  -  Header 
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/
header {background:#003b60;color:#FFF;padding:10px 7px 5px}
header img {vertical-align:top}
.menu {margin:10px 1em 0 0}
.acronym {display:inline-block;font-size:44pt;letter-spacing:-4px;margin:-13px 0 -15px 0px}
.full-name {position:relative;bottom:6px;left:3px;display:inline-block}
header h2 {font-size:15pt;margin:-3px 2px}
header h4 {font-size:9pt;margin:1px 2px}
.cyber-center {padding:0;text-align:right;text-transform:uppercase}
.cyber-center h5 {margin:0;padding:0}
.cyber-center h2 {font-size:25pt;margin:7px 10px -5px 0}
.cyber-center h4 {color:#9CC7DE;font-size:14pt;margin:0 2px 0}
#user {color:#FFF;font-size:12px;margin:1em 0}
#user .badge {background-color:#481C1C;font-weight:normal;margin-top:-4px;margin-right:-5px;border-radius:5px}
#user .btn-link {color:#FFF}
#user a:link {color:#FFF}
#user a:visited {color:#FFF}
#user a:hover {color:#9CC7DE}
#user a:active {color:#E97E83}
.condensed-menu {border:#F00 solid 1px;position:fixed;top:26px;left:6px;width:300px;z-index:12200}
.web-app header {background:#003b60;color:#FFF;padding:1em 0 .5em 0}
.web-app header .container {}
.web-app .seal img {height:40px;width:40px}
.web-app .acronym {font-size:21pt;letter-spacing:-5px;margin:0px 0 -5px 4px}
.web-app .cyber-center {text-align:left}
.web-app .acronym,
.web-app .full-name,
.web-app .dccc,
.web-app .afosi,
.web-app .cyber-center h5 {display:none;visibility:hidden}
.web-app .cyber-center h2 {font-size:15pt;margin:0px 0 0px 0}
.header-search {top:9px;left:-10px;min-width:200px}
@media (min-width:768px) {
    .header-search {
        float: right !important;
    }
}
.cyber-center {padding-right: 10px;}
@media (max-width:524px) {
    .full-name {
        display: none;
    }
}


/*  2.3 -  Navigation
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/
.navbar {border:none;margin:0;min-height:30px}
.navbar-dc3 {background:#701718;sbackground:#E6AB23;margin:0;padding:0}
.navbar-dc3 .navbar-nav > li > a {color: #FFF;font-size:12px;text-transform:uppercase}
.navbar-dc3 .navbar-nav > li > a:hover, .navbar-dc3 .navbar-nav > li > a:focus {background-color:rgba(33,33,33,0.5);color: #FFF}
.navbar-dc3 .navbar-nav > .active > a, .navbar-dc3 .navbar-nav > .active > a:hover, .navbar-dc3 .navbar-nav > .active > a:focus {background-color:rgba(33,33,33,0.8);color: #FFF}
.nav > li > a, .nav > li.demi > a, .nav > li.petite > a {
    padding: 10px 15px
}
 a.navbar-brand:link {color:#FFF}
 a.navbar-brand:visited {color:#FFF}
.navbar-inverse .navbar-brand:hover {color:#FFF}
.navbar-inverse .navbar-brand:active {color:#FFF}
.navbar-inverse .navbar-toggle {border-color:#FFF}
.nav-tabs > li > a { padding:6px 6px }
.navbar-nav > li > a {padding-top:13px !important;padding-bottom:13px !important}
@media (min-width:768px) {
    .navbar {
        height: 46px !important
    }
}
.col-nav {float:left;width:200px;position:absolute;display:flex}
.col-content {margin-left:200px;padding-right:20px}
@media (max-width: 767px) {    
    .col-nav {float:none;width:100%;margin-bottom:10px}     
        .col-content {margin-left:0px}     
}
.tasks-tab-content:not(.active) {display:none}
.submenu-header:hover{cursor:default;}

@media (min-width: 768px) {	
	.nav > li > a {padding:15px 10px}
	.nav > li.petite > a {padding:15px 25px}
	.nav > li.demi > a {padding:15px 10px}
}
@media (min-width: 991px) {
    .nav > li > a {   padding: 15px 20px}
    .nav > li.petite > a { padding: 15px 25px}
    .nav > li.demi > a {  padding: 15px 20px}
}
@media (min-width:1200px) {
    .nav > li > a {  padding: 15px 32px }
    .nav > li.petite > a { padding: 15px 32px }
    .nav > li.demi > a { padding: 15px 32px  }
}
@media (min-width:1400px){
    .nav > li > a,
    .nav > li.petite > a,
    .nav > li.demi > a {
        padding: 15px 25px
    }
}

.home { height: 46px;}
.homeicon { margin-top:-2px }


/* Navbar red button */
.btn-dc3nav {
    color: #fff;
    background-color: #701718;
    border-color: #701718;
    vertical-align: baseline;
    padding: 4px 0px 1px 0px;
}

.btn-dc3nav:focus,
.btn-dc3nav.focus {
    color: #fff;
    background-color: #460e0f;
    border-color: #060101;
}

.btn-dc3nav:hover {
    color: #fff;
    background-color: #460e0f;
    border-color: #3d0d0d;
}

.btn-dc3nav:active,
.btn-dc3nav.active,
.open > .dropdown-toggle.btn-dc3nav {
    color: #fff;
    background-color: #460e0f;
    border-color: #3d0d0d;
}

    .btn-dc3nav:active:hover,
    .btn-dc3nav.active:hover,
    .open > .dropdown-toggle.btn-dc3nav:hover,
    .btn-dc3nav:active:focus,
    .btn-dc3nav.active:focus,
    .open > .dropdown-toggle.btn-dc3nav:focus,
    .btn-dc3nav:active.focus,
    .btn-dc3nav.active.focus,
    .open > .dropdown-toggle.btn-dc3nav.focus {
        color: #fff;
        background-color: #280809;
        border-color: #060101;
    }

.btn-dc3nav:active,
.btn-dc3nav.active,
.open > .dropdown-toggle.btn-dc3nav {
    background-image: none;
}

.btn-dc3nav.disabled,
.btn-dc3nav[disabled],
fieldset[disabled] .btn-dc3nav,
.btn-dc3nav.disabled:hover,
.btn-dc3nav[disabled]:hover,
fieldset[disabled] .btn-dc3nav:hover,
.btn-dc3nav.disabled:focus,
.btn-dc3nav[disabled]:focus,
fieldset[disabled] .btn-dc3nav:focus,
.btn-dc3nav.disabled.focus,
.btn-dc3nav[disabled].focus,
fieldset[disabled] .btn-dc3nav.focus,
.btn-dc3nav.disabled:active,
.btn-dc3nav[disabled]:active,
fieldset[disabled] .btn-dc3nav:active,
.btn-dc3nav.disabled.active,
.btn-dc3nav[disabled].active,
fieldset[disabled] .btn-dc3nav.active {
    background-color: #701718;
    border-color: #701718;
}

.btn-dc3nav .badge {
    color: #701718;
    background-color: #fff;
}


/*  2.4  -  Footer 
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/
#footer {background:#333;color:#FFF;padding:10px 15px 0}
#footer a:link {color:#CCC}
#footer a:visited {color:#999}
#footer a:hover {color:#FFF}
#footer a:active {color:#FFF}
.footer-links li {border-right:#999 solid 1px;display:inline;font-size:.8em;padding:0 1em}
.footer-links .dod-ccc {font-size:1em}
.footer-links .afosi {font-size:.9em}


/*  3.     -  TEMPLATES
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/

/*  3.1  -  Login 
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/
.main-content {margin-bottom:8em; margin-top:8em; scroll-margin-top:8em;padding:0.4em 2.2em}
.login-panel {background:#EFEFEF;border:#DDD solid 1px;border-radius:8px;min-height:260px}
.intro {padding:2em 4em}
.intro h2 {margin-bottom:-15px}
.intro h3 {font-size:18px;line-height:1.5}
.sign-in {padding:2em 3em}
.usg-warning {background:none;color:#999;font-size:.8em;margin:2em 0;padding:2em}



/*  4.     -  CUSTOMS
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/

/*  4.1 -  Responsive Fixes
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/
@media (min-width:1200px) { }
@media (min-width: 992px) and (max-width: 1199px) { }
@media (min-width: 768px) and (max-width: 991px) {	}
@media (max-width: 767px) { }

@media (min-width: 992px) {
    .validation-results {
        height: 65vh;
        overflow-y: scroll;
    }
}
.validation-results {
    max-height: 900px;
    overflow-y: scroll;
}
/*  4.2 -  Bootstrap Over-rides
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/
.label {
    display: inline-block;
}
.nav-stacked > li {height: 40px;}
.nav-stacked > li > a {padding:10px 15px}
.nav-pills>li.active>a, .nav-pills>li.active>a:focus, .nav-pills>li.active>a:hover { background-color:#003b60 }
.panel-contacts .panel-body {padding-top:0}
.panel-body-title {border-bottom:#CCC solid 1px; font-size:15px;padding:3px 0;margin-bottom:15px}
.panel-body-title-first {margin-top: 0px}
.panel-default>.panel-heading {color: #333;background-color: #eee;border-color: #eee;}
.navbar-right {padding-right:15px}
.list-group-item.active {background-color:#003b60!important}
.dropdown-menu-wide{width: 190px}
.textarea-addon{
    border-bottom-left-radius: 0px !important;
    border-top-right-radius: 4px !important;
    border: 1px solid #ccc !important;
    border-bottom: none!important;
    word-wrap: break-word!important;
    text-align: left !important;
    white-space: normal !important;
    background: #f2dede !important;
    padding-top: 10px;
    padding-bottom: 10px;
    line-height: 1.5 !important;
}
.textarea-body
{
    border-top-left-radius: 0px !important;
    border-top-right-radius: 0px !important;
}
.dl-wide dt {width:200px}
.dl-wide dd {margin-left:220px}


/*  4.3 -  IE Fixes
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/

/*  4.4 -  Print Styles
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/

/*  4.5 -  Tags
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/
.tag {background-image:none;border:1px solid transparent;border-radius:4px;cursor:pointer;display:inline-block;font-size:14px;font-weight:400;margin:2px 0;padding:3px 10px 4px;line-height:1.42857143;text-align:center;touch-action:manipulation;-ms-touch-action:manipulation;user-select:none;-moz-user-select:none;-ms-user-select:none;-webkit-user-select:none;vertical-align:middle;white-space:nowrap}
.tag-default {background-color:#e6e6e6;border-color:#c8c8c8;color:#333}
.tag-primary {background-color:#337ab7;border-color:#2e6da4;color:#fff}
.tag-success {background-color:#5cb85c;border-color:#4cae4c;color:#fff}
.tag-info {background-color:#5bc0de;border-color:#46b8da;color:#fff}
.tag-warning {background-color:#f0ad4e;border-color:#eea236;color:#fff}
.tag-danger {background-color:#d9534f;border-color:#d43f3a;color:#fff}
.tag-remove {color:#000;filter:alpha(opacity=20);float:right;font-size:21px;font-weight:700;line-height:1;opacity:.2;position:relative;right:-10px;text-shadow:0 1px 0 #fff;top:-11px}
.tag-remove:focus,
.tag-remove:hover {color:#000;cursor:pointer;filter:alpha(opacity=50);opacity:.5;text-decoration:none}


/*  4.6 -  Typeahead
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/
.twitter-typeahead .tt-hint .tt-input .tt-menu { width: 100% }
.tt-hint { visibility: hidden; }
.twitter-typeahead .tt-input { padding-top: 5px }
.twitter-typeahead { display: inline!important }

/* 4.7 - Tagsinput 
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/
.bootstrap-tagsinput { width: 100%;padding:3px 6px }
.bootstrap-tagsinput .tag { margin-top: 3px; }

#comm-table th {white-space: nowrap}
#comm-table td:nth-child(0) { width: 0px }
#comm-table td:nth-child(1) { white-space: nowrap }
#comm-table td:nth-child(2) { white-space: nowrap }
#comm-table td:nth-child(3) { white-space: nowrap }
#comm-table td:nth-child(4) { white-space: nowrap }
#comm-table td:nth-child(6) { width: 0px }
#comm-table td:nth-child(10) { white-space: nowrap }
#comm-table td:last-child {width: 100%}

#exam-table td {white-space: nowrap}

#exam-comms td:nth-child(1) { white-space: nowrap }
#exam-comms td:nth-child(2) { white-space: nowrap }
#exam-comms td:nth-child(3) { white-space: nowrap }
#timesheet-table td { width: 25% }


/* 4.8 - Tile Stats 
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/
.simple-tile-stats {
    text-align: center;
    position: relative;
    display: block;
    margin-bottom: 12px;
    border: 1px solid #ddd;
    -webkit-border-radius: 5px;
    overflow: hidden;
    padding-bottom: 15px;
    padding-top: 15px;
    -webkit-background-clip: padding-box;
    -moz-border-radius: 5px;
    -moz-background-clip: padding;
    border-radius: 5px;
    background-clip: padding-box;
    background: #FFF;
    -moz-transition: all 300ms ease-in-out;
    -o-transition: all 300ms ease-in-out;
    -webkit-transition: all 300ms ease-in-out;
    transition: all 300ms ease-in-out;
    font-size: 28px;
    color: #969494; /*color: #BAB8B8;*/
}

    .simple-tile-stats p {
        margin-top: 15px;
        font-size: 14px;
        text-align: left;
        margin-left: 10%;
        color: #787676;
    }


.tile-stats {
  position: relative;
  display: block;
  margin-bottom: 12px;
  border: 1px solid #ddd;
  -webkit-border-radius: 5px;
  overflow: hidden;
  padding-bottom: 5px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 5px;
  -moz-background-clip: padding;
  border-radius: 5px;
  background-clip: padding-box;
  background: #FFF;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}

.tile-stats:hover .icon i {
  animation-name: tansformAnimation;
  animation-duration: .5s;
  animation-iteration-count: 1;
  color: rgba(58, 58, 58, 0.41);
  animation-timing-function: ease;
  animation-fill-mode: forwards;
  -webkit-animation-name: tansformAnimation;
  -webkit-animation-duration: .5s;
  -webkit-animation-iteration-count: 1;
  -webkit-animation-timing-function: ease;
  -webkit-animation-fill-mode: forwards;
  -moz-animation-name: tansformAnimation;
  -moz-animation-duration: .5s;
  -moz-animation-iteration-count: 1;
  -moz-animation-timing-function: ease;
  -moz-animation-fill-mode: forwards;
}

.tile-stats .icon {
    width: 20px;
    height: 20px;
    color: #969494;
    position: absolute;
    right: 85px;
    top: 22px;
    z-index: 1;
}

.tile-stats .count {
  font-size: 38px;
  font-weight: bold;
  line-height: 1.65857143
}

.tile-stats .count, .tile-stats h3, .tile-stats h4, .tile-stats p {
  position: relative;
  margin: 0;
  margin-left: 10px;
  z-index: 5;
  padding: 0;
}

.tile-stats h3 {
  color: #969494;/*color: #BAB8B8;*/
}

.tile-stats h4 {
    color: #787676;
}

.tile-stats p {
  margin-top: 5px;
  font-size: 14px;
}

.tile-stats > .dash-box-footer {
  position: relative;
  text-align: center;
  margin-top: 5px;
  padding: 3px 0;
  color: #fff;
  color: rgba(255, 255, 255, 0.8);
  display: block;
  z-index: 10;
  background: rgba(0, 0, 0, 0.1);
  text-decoration: none;
}

.tile-stats > .dash-box-footer:hover {
  color: #fff;
  background: rgba(0, 0, 0, 0.15);
}

.tile-stats > .dash-box-footer:hover {
  color: #fff;
  background: rgba(0, 0, 0, 0.15);
}

table.tile_info {
  padding: 10px 15px;
}

table.tile_info span.right {
  margin-right: 0;
  float: right;
  position: absolute;
  right: 4%;
}

.tile:hover {
  text-decoration: none;
}

.tile_header {
  border-bottom: transparent;
  padding: 7px 15px;
  margin-bottom: 15px;
  background: #E7E7E7;
}

.tile_head h4 {
  margin-top: 0;
  margin-bottom: 5px;
}

.tiles-bottom {
  padding: 5px 10px;
  margin-top: 10px;
  background: rgba(194, 194, 194, 0.3);
  text-align: left;
}

/* 4.9 - Form wizard
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/

.form_wizard { padding-top:18px }
.form_wizard .stepContainer { display: block; position: relative; margin: 0; padding: 0; border: 0 solid #CCC; overflow-x: hidden }
.wizard_horizontal ul.wizard_steps {display: table;list-style: none;position: relative;width: 100%;margin: 0 0 -10px}
.wizard_horizontal ul.wizard_steps li {display: table-cell;text-align: center}
.wizard_horizontal ul.wizard_steps li span.milestone {display: block;position: relative;-moz-opacity: 1;filter: alpha(opacity=100);opacity: 1;color: #666}
.wizard_horizontal ul.wizard_steps li span.milestone:before {content: "";position: absolute;height: 6px;background: #ccc;top: 18px;width: 100%;z-index: 4;left: 0}
.wizard_horizontal ul.wizard_steps li span.milestone.disabled .step_no {background: #ccc}
.wizard_horizontal ul.wizard_steps li span.milestone.ongoing .step_no {background: #003b60}
.wizard_horizontal ul.wizard_steps li span.milestone.ongoing:before {background: linear-gradient(90deg, #8fc6e9 50%, #ccc 50%)}
.wizard_horizontal ul.wizard_steps li span.milestone .step_no {width: 40px;height: 40px;line-height: 40px;border-radius: 100px;display: block;margin: 0 auto 5px;font-size: 16px;text-align: center;position: relative;z-index: 5}
.wizard_horizontal ul.wizard_steps li span.milestone.selected:before,
.step_no { background: #8fc6e9;color: #fff }
.wizard_horizontal ul.wizard_steps li:first-child span.milestone:before { left: 50% }
.wizard_horizontal ul.wizard_steps li:last-child span.milestone:before { right: 50%;width: 50%;left: auto;background: #8fc6e9 }
.wizard_horizontal ul.wizard_steps li:last-child span.milestone.disabled:before {right: 50%;width: 50%;left: auto;background: #ccc}
.step_descr { font-size: 0.9em; }
.pinched { width: 95% }
.side_padded { padding: 0px 15px 0px 15px }

/* 4.10 - Directory browser
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/

.directory-browser { padding-left:10px; padding-top:10px}


/* 4.11 - Bootstrap submenu
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/

.dropdown-submenu {
    position:relative;
}
.dropdown-submenu>.dropdown-menu {
    top:0;
    left:100%;
    margin-top:-6px;
    margin-left:-1px;
    -webkit-border-radius:0 6px 6px 6px;
    -moz-border-radius:0 6px 6px 6px;
    border-radius:0 6px 6px 6px;
}
.dropdown-submenu:hover>.dropdown-menu {
    display:block;
}
.dropdown-submenu>a:after {
    display:block;
    content:" ";
    float:right;
    width:0;
    height:0;
    border-color:transparent;
    border-style:solid;
    border-width:5px 0 5px 5px;
    border-left-color:#cccccc;
    margin-top:5px;
    margin-right:-10px;
}
.dropdown-submenu:hover>a:after {
    border-left-color:#ffffff;
}
.dropdown-submenu.pull-left {
    float:none;
}
.dropdown-submenu.pull-left>.dropdown-menu {
    left:-100%;
    margin-left:10px;
    -webkit-border-radius:6px 0 6px 6px;
    -moz-border-radius:6px 0 6px 6px;
    border-radius:6px 0 6px 6px;
}
.red-bg {
    background-color: #ff6d6a!important;
}

.yellow-bg {
    background-color: #fda!important;
}

.green-bg {
    background-color: #ced!important;
}

/*fix table color contrast for intrustions color coding */
.table-striped a,
.well a {
    color: #2168a5 !important;
}

.k-table > thead > tr > td.danger,
.k-table > tbody > tr > td.danger,
.k-table > tfoot > tr > td.danger,
.k-table > thead > tr > th.danger,
.k-table > tbody > tr > th.danger,
.k-table > tfoot > tr > th.danger,
.k-table > thead > tr.danger > td,
.k-table > tbody > tr.danger > td,
.k-table > tfoot > tr.danger > td,
.k-table > thead > tr.danger > th,
.k-table > tbody > tr.danger > th,
.k-table > tfoot > tr.danger > th,
.red-bg {
    background-color: #f2dede !important;
    /*#ff6d6a*/
}

.k-table > tbody > tr.k-table-row > td.danger:hover,
.k-table > tbody > tr.k-table-row > th.danger:hover,
.k-table > tbody > tr.k-table-row.danger:hover > td,
.k-table > tbody > tr.k-table-row:hover > .danger,
.k-table > tbody > tr.k-table-row.danger:hover > th {
    background-color: #e6c1c1 !important;
    /*8% darker f2dede*/
}


.k-table > thead > tr > td.warning,
.k-table > tbody > tr > td.warning,
.k-table > tfoot > tr > td.warning,
.k-table > thead > tr > th.warning,
.k-table > tbody > tr > th.warning,
.k-table > tfoot > tr > th.warning,
.k-table > thead > tr.warning > td,
.k-table > tbody > tr.warning > td,
.k-table > tfoot > tr.warning > td,
.k-table > thead > tr.warning > th,
.k-table > tbody > tr.warning > th,
.k-table > tfoot > tr.warning > th,
.yellow-bg {
    background-color: #fcf8e3 !important;
     /*#fda*/
}

.k-table > tbody > tr.k-table-row > td.warning:hover,
.k-table > tbody > tr.k-table-row > th.warning:hover,
.k-table > tbody > tr.k-table-row.warning:hover > td,
.k-table > tbody > tr.k-table-row:hover > .warning,
.k-table > tbody > tr.k-table-row.warning:hover > th {
    background-color: #f8efbe !important; /*8% darker*/
}

.k-table > thead > tr > td.success,
.k-table > tbody > tr > td.success,
.k-table > tfoot > tr > td.success,
.k-table > thead > tr > th.success,
.k-table > tbody > tr > th.success,
.k-table > tfoot > tr > th.success,
.k-table > thead > tr.success > td,
.k-table > tbody > tr.success > td,
.k-table > tfoot > tr.success > td,
.k-table > thead > tr.success > th,
.k-table > tbody > tr.success > th,
.k-table > tfoot > tr.success > th,
.green-bg {
    background-color: #dff0d8 !important;
    /*#ced*/
}



.k-table > tbody > tr.k-table-row > td.success:hover,
.k-table > tbody > tr.k-table-row > th.success:hover,
.k-table > tbody > tr.k-table-row.success:hover > td,
.k-table > tbody > tr.k-table-row:hover > .success,
.k-table > tbody > tr.k-table-row.success:hover > th {
    background-color: #c7e5bb !important; /*8% darker*/
}

#IntrusionsExamListIntrusionsExamGridData tr:hover a {
    color: #105794;
}

/* 4.12 - Kendo Over-rides
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/
.k-grid a, .k-grid button.btn-link {
    color: #2168a5; /*#337ab7*/
}
.k-toolbar a {
    color: inherit;
}

a.k-treeview-leaf.k-selected {
    color: white !important; 
}

/*The Kendo Bootstrap 3 version we have seems to have some strange color contrast bugs*/
.k-upload .k-dropzone .k-dropzone-hint, .k-upload .k-upload-dropzone .k-dropzone-hint, .k-upload .k-upload-status 
{
    color: #707070
}
.k-upload .k-file-success .k-file-validation-message {
    color: #2c882c;
}
.k-form-error, .k-text-error, .k-form-field-error .k-label {
    color: #ce4844;
}

.k-pager-numbers  span.k-button-text{
    color: #2d74b1;
}
.btn-success {
    background-color: #2c882c;
}
/*td > .k-button {
    padding-top: 0px;
    padding-bottom: 0px;
    padding-left: 7px;
    padding-right: 7px;
}
.k-grid-toolbar { height:0; padding:0; border:0 }
.k-grid-pdf { float:right; margin-top:9px!important; margin-right:10px!important }
.k-grid-excel { float:right; margin-top:9px!important }
.k-grid-filter { float:right; margin-top:9px!important }
.k-grid-add { float:right; margin-top:9px!important }
.k-header { background-color: #FFF }

.k-filter-row > th > span > span > span.k-widget.k-dropdown.k-header.k-dropdown-operator { display:none!important }
.k-filter-row > th > span > span > button { display:none!important }
.k-filtercell > span { padding-right: 0px!important }
.k-tabstrip-items * {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}*/
.k-window-titlebar {
    height: 35px;
}
.k-dialog-title, .k-dialog-close {
    margin-top:-10px!important;
}
div.k-widget.k-dialog.k-window > ul > li.k-button {
    padding-top: 10px;
    padding-bottom: 10px;
}
.k-widget * {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}
/*.k-textbox { width: 100% }
.k-autocomplete { 
    width: 100%;
    height: 2.43em;
}
.k-autocomplete .k-input, .k-numeric-wrap .k-input, .k-picker-wrap .k-input { 
    height: 2.2em!important;
}*/
.k-window-titlebar .k-window-action { width: 20px; height: 20px }

.examSearchInput .k-input, .examSearchInput .k-autocomplete, .examSearchInput {
    height: 1.9em!important;
    line-height: 4;
}

.k-grid * {
    font-size: 11pt;
}

.k-tabstrip-items * {
    font-size: 11pt;
}



.k-menu * {
    /*font-size: 11pt;*/
    overflow: visible;
}

.k-menu > .k-item {
    color: black;
}

.k-menu:not(.k-context-menu) > .k-item {
    color: #2168a5;
}
.k-menu:not(.k-context-menu) > .k-menu-item:hover,
.k-menu:not(.k-context-menu) > .k-menu-item.k-hover {
    color: #174973;
    text-decoration: underline;
}

.k-menu:not(.k-context-menu) > .k-menu-item.active-bg > a.k-menu-link:focus {
    color: white;
}

.k-menu:not(.k-context-menu) span.k-sprite.fa-lg {
    line-height: 16px;
}
.active-bg {
    background-color: #2168a5;
    color: white !important;
}


/*.k-window * {
    font-size: 12px;
}*/
.k-pager * {
    /*font-size: 16px !important;*/
    overflow: visible;
}
/*.k-dropdown-wrap {
    height: 2.43em;
}
.k-picker-wrap {
    height: 2.43em;
}
span.k-select {
    height: 2.43em;
}
.k-dropdown { width: 100% }
.k-datepicker { width: 100% }

.k-notification-wrap>.k-i-error, .k-notification-wrap>.k-i-info,
.k-notification-wrap>.k-i-note, .k-notification-wrap>.k-i-success, .k-notification-wrap>.k-i-warning {
    vertical-align: text-bottom;
    margin-right: 4px;
    background-image: url('kendo/Black/sprite.png')
}

.k-notification .k-i-close {
    background-image: url('kendo/Black/sprite.png')
}

.k-widget.k-notification.k-notification-info {
    background-color: #3e80ed;
    color: #ffffff;
    border-color: #3e80ed;
}

.k-widget.k-notification.k-notification-success {
    background-color: #1db62e;
    color: #ffffff;
    border-color: #11bd34;
}

.k-grid td {
    padding-top: 0.2em;
    padding-bottom: 0.2em;
    padding-left: 0.3em;
    padding-right: 0.3em;
}*/

/* 4.13 - Wizard */
/*.examWizard > .k-tabstrip-items > .k-item {
    width: 20%
}
#examCompleteness {
    margin-bottom: 0px;
    width: 100%;
    height: 15px;
=======
}*/

/*span.k-menu-link-text {
    color: #337ab7;
}*/