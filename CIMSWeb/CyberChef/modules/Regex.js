/**
 * CyberChef - The Cyber Swiss Army Knife
 *
 * @copyright Crown Copyright 2016
 * @license Apache-2.0
 *
 *   Copyright 2016 Crown Copyright
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */!function(e){var u={};function d(t){if(u[t])return u[t].exports;var n=u[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,d),n.l=!0,n.exports}d.m=e,d.c=u,d.d=function(e,u,t){d.o(e,u)||Object.defineProperty(e,u,{enumerable:!0,get:t})},d.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},d.t=function(e,u){if(1&u&&(e=d(e)),8&u)return e;if(4&u&&"object"==typeof e&&e&&e.__esModule)return e;var t=Object.create(null);if(d.r(t),Object.defineProperty(t,"default",{enumerable:!0,value:e}),2&u&&"string"!=typeof e)for(var n in e)d.d(t,n,function(u){return e[u]}.bind(null,n));return t},d.n=function(e){var u=e&&e.__esModule?function(){return e.default}:function(){return e};return d.d(u,"a",u),u},d.o=function(e,u){return Object.prototype.hasOwnProperty.call(e,u)},d.p="",d(d.s=1322)}({0:function(e,u,d){"use strict";(function(e,t){d.d(u,"c",function(){return v}),d.d(u,"d",function(){return w}),d.d(u,"e",function(){return A}),d.d(u,"a",function(){return E});var n=d(7),r=d.n(n),a=d(11),i=d.n(a),c=d(1),o=d.n(c),f=d(2),s=d.n(f),l=d(24),p=d.n(l),m=d(14),h=d(17),b=d(30),g=d(29),y=function(){function u(){o()(this,u)}var d;return s()(u,null,[{key:"chr",value:function(e){if(e>65535){e-=65536;var u=String.fromCharCode(e>>>10&1023|55296);return e=56320|1023&e,u+String.fromCharCode(e)}return String.fromCharCode(e)}},{key:"ord",value:function(e){if(2===e.length){var u=e.charCodeAt(0),d=e.charCodeAt(1);if(u>=55296&&u<56320&&d>=56320&&d<57344)return 1024*(u-55296)+d-56320+65536}return e.charCodeAt(0)}},{key:"padBytesRight",value:function(e,u){var d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,t=new Array(u);return t.fill(d),[...e].forEach(function(e,u){t[u]=e}),t}},{key:"truncate",value:function(e,u){var d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"...";return e.length>u&&(e=e.slice(0,u-d.length)+d),e}},{key:"hex",value:function(e){var d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return(e="string"==typeof e?u.ord(e):e).toString(16).padStart(d,"0")}},{key:"bin",value:function(e){var d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:8;return(e="string"==typeof e?u.ord(e):e).toString(2).padStart(d,"0")}},{key:"printable",value:function(e){var d=arguments.length>1&&void 0!==arguments[1]&&arguments[1];x()&&window.app&&!window.app.options.treatAsUtf8&&(e=u.byteArrayToChars(u.strToByteArray(e)));var t=/[\0-\x08\x0B-\x0C\x0E-\x1F\x7F-\x9F\xAD\u0378\u0379\u037F-\u0383\u038B\u038D\u03A2\u0528-\u0530\u0557\u0558\u0560\u0588\u058B-\u058E\u0590\u05C8-\u05CF\u05EB-\u05EF\u05F5-\u0605\u061C\u061D\u06DD\u070E\u070F\u074B\u074C\u07B2-\u07BF\u07FB-\u07FF\u082E\u082F\u083F\u085C\u085D\u085F-\u089F\u08A1\u08AD-\u08E3\u08FF\u0978\u0980\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09FC-\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF2-\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B55\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B78-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BFB-\u0C00\u0C04\u0C0D\u0C11\u0C29\u0C34\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5A-\u0C5F\u0C64\u0C65\u0C70-\u0C77\u0C80\u0C81\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0D01\u0D04\u0D0D\u0D11\u0D3B\u0D3C\u0D45\u0D49\u0D4F-\u0D56\u0D58-\u0D5F\u0D64\u0D65\u0D76-\u0D78\u0D80\u0D81\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DF1\u0DF5-\u0E00\u0E3B-\u0E3E\u0E5C-\u0E80\u0E83\u0E85\u0E86\u0E89\u0E8B\u0E8C\u0E8E-\u0E93\u0E98\u0EA0\u0EA4\u0EA6\u0EA8\u0EA9\u0EAC\u0EBA\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F48\u0F6D-\u0F70\u0F98\u0FBD\u0FCD\u0FDB-\u0FFF\u10C6\u10C8-\u10CC\u10CE\u10CF\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u137D-\u137F\u139A-\u139F\u13F5-\u13FF\u169D-\u169F\u16F1-\u16FF\u170D\u1715-\u171F\u1737-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17DE\u17DF\u17EA-\u17EF\u17FA-\u17FF\u180F\u181A-\u181F\u1878-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191D-\u191F\u192C-\u192F\u193C-\u193F\u1941-\u1943\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DB-\u19DD\u1A1C\u1A1D\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1A9F\u1AAE-\u1AFF\u1B4C-\u1B4F\u1B7D-\u1B7F\u1BF4-\u1BFB\u1C38-\u1C3A\u1C4A-\u1C4C\u1C80-\u1CBF\u1CC8-\u1CCF\u1CF7-\u1CFF\u1DE7-\u1DFB\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FC5\u1FD4\u1FD5\u1FDC\u1FF0\u1FF1\u1FF5\u1FFF\u200B-\u200F\u202A-\u202E\u2060-\u206F\u2072\u2073\u208F\u209D-\u209F\u20BB-\u20CF\u20F1-\u20FF\u218A-\u218F\u23F4-\u23FF\u2427-\u243F\u244B-\u245F\u2700\u2B4D-\u2B4F\u2B5A-\u2BFF\u2C2F\u2C5F\u2CF4-\u2CF8\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D71-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E3C-\u2E7F\u2E9A\u2EF4-\u2EFF\u2FD6-\u2FEF\u2FFC-\u2FFF\u3040\u3097\u3098\u3100-\u3104\u312E-\u3130\u318F\u31BB-\u31BF\u31E4-\u31EF\u321F\u32FF\u4DB6-\u4DBF\u9FCD-\u9FFF\uA48D-\uA48F\uA4C7-\uA4CF\uA62C-\uA63F\uA698-\uA69E\uA6F8-\uA6FF\uA78F\uA794-\uA79F\uA7AB-\uA7F7\uA82C-\uA82F\uA83A-\uA83F\uA878-\uA87F\uA8C5-\uA8CD\uA8DA-\uA8DF\uA8FC-\uA8FF\uA954-\uA95E\uA97D-\uA97F\uA9CE\uA9DA-\uA9DD\uA9E0-\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A\uAA5B\uAA7C-\uAA7F\uAAC3-\uAADA\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F-\uABBF\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBC2-\uFBD2\uFD40-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFE\uFDFF\uFE1A-\uFE1F\uFE27-\uFE2F\uFE53\uFE67\uFE6C-\uFE6F\uFE75\uFEFD-\uFF00\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFDF\uFFE7\uFFEF-\uFFFB\uFFFE\uFFFF]/g,n=/[\x09-\x10\x0D\u2028\u2029]/g;return e=e.replace(t,"."),d||(e=e.replace(n,".")),e}},{key:"parseEscapedChars",value:function(e){return e.replace(/(\\)?\\([bfnrtv'"]|[0-3][0-7]{2}|[0-7]{1,2}|x[\da-fA-F]{2}|u[\da-fA-F]{4}|u\{[\da-fA-F]{1,6}\}|\\)/g,function(e,u,d){if("\\"===u)return"\\"+d;switch(d[0]){case"\\":return"\\";case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":return String.fromCharCode(parseInt(d,8));case"b":return"\b";case"t":return"\t";case"n":return"\n";case"v":return"\v";case"f":return"\f";case"r":return"\r";case'"':return'"';case"'":return"'";case"x":return String.fromCharCode(parseInt(d.substr(1),16));case"u":return"{"===d[1]?String.fromCodePoint(parseInt(d.slice(2,-1),16)):String.fromCharCode(parseInt(d.substr(1),16))}})}},{key:"escapeRegex",value:function(e){return e.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}},{key:"expandAlphRange",value:function(e){for(var d=[],t=0;t<e.length;t++)if(t<e.length-2&&"-"===e[t+1]&&"\\"!==e[t]){for(var n=u.ord(e[t]),r=u.ord(e[t+2]),a=n;a<=r;a++)d.push(u.chr(a));t+=2}else t<e.length-2&&"\\"===e[t]&&"-"===e[t+1]?(d.push("-"),t++):d.push(e[t]);return d}},{key:"convertToByteArray",value:function(e,d){switch(d.toLowerCase()){case"binary":return Object(g.a)(e);case"hex":return Object(h.a)(e);case"decimal":return Object(b.a)(e);case"base64":return Object(m.a)(e,null,"byteArray");case"utf8":return u.strToUtf8ByteArray(e);case"latin1":default:return u.strToByteArray(e)}}},{key:"convertToByteString",value:function(e,d){switch(d.toLowerCase()){case"binary":return u.byteArrayToChars(Object(g.a)(e));case"hex":return u.byteArrayToChars(Object(h.a)(e));case"decimal":return u.byteArrayToChars(Object(b.a)(e));case"base64":return u.byteArrayToChars(Object(m.a)(e,null,"byteArray"));case"utf8":return p.a.encode(e);case"latin1":default:return e}}},{key:"strToArrayBuffer",value:function(e){for(var d,t=new Uint8Array(e.length),n=e.length;n--;)if(d=e.charCodeAt(n),t[n]=d,d>255)return u.strToUtf8ArrayBuffer(e);return t.buffer}},{key:"strToUtf8ArrayBuffer",value:function(e){var d=p.a.encode(e);return e.length!==d.length&&(w()?self.setOption("attemptHighlight",!1):x()&&(window.app.options.attemptHighlight=!1)),u.strToArrayBuffer(d)}},{key:"strToByteArray",value:function(e){for(var d,t=new Array(e.length),n=e.length;n--;)if(d=e.charCodeAt(n),t[n]=d,d>255)return u.strToUtf8ByteArray(e);return t}},{key:"strToUtf8ByteArray",value:function(e){var d=p.a.encode(e);return e.length!==d.length&&(w()?self.setOption("attemptHighlight",!1):x()&&(window.app.options.attemptHighlight=!1)),u.strToByteArray(d)}},{key:"strToCharcode",value:function(e){for(var d=[],t=0;t<e.length;t++){var n=e.charCodeAt(t);if(t<e.length-1&&n>=55296&&n<56320){var r=e[t+1].charCodeAt(0);r>=56320&&r<57344&&(n=u.ord(e[t]+e[++t]))}d.push(n)}return d}},{key:"byteArrayToUtf8",value:function(e){var d=u.byteArrayToChars(e);try{var t=p.a.decode(d);return d.length!==t.length&&(w()?self.setOption("attemptHighlight",!1):x()&&(window.app.options.attemptHighlight=!1)),t}catch(e){return d}}},{key:"byteArrayToChars",value:function(e){if(!e)return"";for(var u="",d=0;d<e.length;)u+=String.fromCharCode(e[d++]);return u}},{key:"arrayBufferToStr",value:function(e){var d=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],t=new Uint8Array(e);return d?u.byteArrayToUtf8(t):u.byteArrayToChars(t)}},{key:"parseCSV",value:function(e){var u,d,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[","],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:["\n","\r"],r=!1,a=!1,i="",c=[],o=[];e.length&&"\ufeff"===e[0]&&(e=e.substr(1));for(var f=0;f<e.length;f++)u=e[f],d=e[f+1]||"",r?(i+=u,r=!1):'"'!==u||a?'"'===u&&a?'"'===d?r=!0:a=!1:!a&&t.indexOf(u)>=0?(c.push(i),i=""):!a&&n.indexOf(u)>=0?(c.push(i),i="",o.push(c),c=[],n.indexOf(d)>=0&&d!==u&&f++):i+=u:a=!0;return c.length&&(c.push(i),o.push(c)),o}},{key:"stripHtmlTags",value:function(e){var u=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return u&&(e=e.replace(/<(script|style)[^>]*>.*<\/(script|style)>/gim,"")),e.replace(/<[^>]+>/g,"")}},{key:"escapeHtml",value:function(e){var u={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","/":"&#x2F;","`":"&#x60;"};return e.replace(/[&<>"'\/`]/g,function(e){return u[e]})}},{key:"unescapeHtml",value:function(e){var u={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#x27;":"'","&#x2F;":"/","&#x60;":"`"};return e.replace(/&#?x?[a-z0-9]{2,4};/gi,function(e){return u[e]||e})}},{key:"encodeURIFragment",value:function(e){var u={"%2D":"-","%2E":".","%5F":"_","%7E":"~","%21":"!","%24":"$","%27":"'","%28":"(","%29":")","%2A":"*","%2C":",","%3B":";","%3A":":","%40":"@","%2F":"/","%3F":"?"};return(e=encodeURIComponent(e)).replace(/%[0-9A-F]{2}/g,function(e){return u[e]||e})}},{key:"generatePrettyRecipe",value:function(e){var u=arguments.length>1&&void 0!==arguments[1]&&arguments[1],d="",t="",n="",r="",a="";return e.forEach(function(e){t=e.op.replace(/ /g,"_"),n=JSON.stringify(e.args).slice(1,-1).replace(/'/g,"\\'").replace(/"((?:[^"\\]|\\.)*)"/g,"'$1'").replace(/\\"/g,'"'),r=e.disabled?"/disabled":"",a=e.breakpoint?"/breakpoint":"",d+=`${t}(${n}${r}${a})`,u&&(d+="\n")}),d}},{key:"parseRecipeConfig",value:function(e){if(0===(e=e.trim()).length)return[];if("["===e[0])return JSON.parse(e);var u,d;e=e.replace(/\n/g,"");for(var t=/([^(]+)\(((?:'[^'\\]*(?:\\.[^'\\]*)*'|[^)\/'])*)(\/[^)]+)?\)/g,n=[];u=t.exec(e);){d="["+(d=u[2].replace(/"/g,'\\"').replace(/(^|,|{|:)'/g,'$1"').replace(/([^\\]|(?:\\\\)+)'(,|:|}|$)/g,'$1"$2').replace(/\\'/g,"'"))+"]";var r={op:u[1].replace(/_/g," "),args:JSON.parse(d)};u[3]&&u[3].indexOf("disabled")>0&&(r.disabled=!0),u[3]&&u[3].indexOf("breakpoint")>0&&(r.breakpoint=!0),n.push(r)}return n}},{key:"displayFilesAsHTML",value:(d=i()(r.a.mark(function e(d){var t,n,a,c,o;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t=function(e){return`<div class='card' style='white-space: normal;'>\n                    <div class='card-header'>\n                        <h6 class="mb-0">\n                            ${u.escapeHtml(e.name)}\n                        </h6>\n                    </div>\n                </div>`},n=function(e,d){if(d.startsWith("image")){var t="data:";return t+=d+";","<img style='max-width: 100%;' src='"+(t+="base64,"+Object(m.b)(e))+"'>"}return`<pre>${u.escapeHtml(u.arrayBufferToStr(e.buffer))}</pre>`},a=function(){var e=i()(r.a.mark(function e(d,t){var a,i,c,o;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,u.readFile(d);case 2:return a=e.sent,i=new Blob([a],{type:d.type||"octet/stream"}),c=URL.createObjectURL(i),o=`<div class='card' style='white-space: normal;'>\n                    <div class='card-header' id='heading${t}'>\n                        <h6 class='mb-0'>\n                            <a class='collapsed'\n                                data-toggle='collapse'\n                                href='#collapse${t}'\n                                aria-expanded='false'\n                                aria-controls='collapse${t}'\n                                title="Show/hide contents of '${u.escapeHtml(d.name)}'">\n                                ${u.escapeHtml(d.name)}</a>\n                            <span class='float-right' style="margin-top: -3px">\n                                ${d.size.toLocaleString()} bytes\n                                <a title="Download ${u.escapeHtml(d.name)}"\n                                    href="${c}"\n                                    download="${u.escapeHtml(d.name)}"\n                                    data-toggle="tooltip">\n                                    <i class="material-icons" style="vertical-align: bottom">save</i>\n                                </a>\n                                <a title="Move to input"\n                                    href="#"\n                                    blob-url="${c}"\n                                    file-name="${u.escapeHtml(d.name)}"\n                                    class="extract-file"\n                                    data-toggle="tooltip">\n                                    <i class="material-icons" style="vertical-align: bottom">open_in_browser</i>\n                                </a>\n                            </span>\n                        </h6>\n                    </div>\n                    <div id='collapse${t}' class='collapse' aria-labelledby='heading${t}' data-parent="#files">\n                        <div class='card-body'>\n                            ${n(a,d.type)}\n                        </div>\n                    </div>\n                </div>`,e.abrupt("return",o);case 7:case"end":return e.stop()}},e)}));return function(u,d){return e.apply(this,arguments)}}(),c=`<div style='padding: 5px; white-space: normal;'>\n                ${d.length} file(s) found\n            </div><div id="files" style="padding: 20px">`,o=0;case 5:if(!(o<d.length)){e.next=17;break}if(!d[o].name.endsWith("/")){e.next=10;break}c+=t(d[o]),e.next=14;break;case 10:return e.t0=c,e.next=13,a(d[o],o);case 13:c=e.t0+=e.sent;case 14:o++,e.next=5;break;case 17:return e.abrupt("return",c+="</div>");case 18:case"end":return e.stop()}},e)})),function(e){return d.apply(this,arguments)})},{key:"parseURIParams",value:function(e){if(""===e)return{};"?"!==e[0]&&"#"!==e[0]||(e=e.substr(1));for(var u=e.split("&"),d={},t=0;t<u.length;t++){var n=u[t].split("=");2!==n.length?d[u[t]]=!0:d[n[0]]=decodeURIComponent(n[1].replace(/\+/g," "))}return d}},{key:"readFile",value:function(u){return v()?e.from(u).buffer:new Promise(function(e,d){var t=new FileReader,n=new Uint8Array(u.size),r=0,a=function(){if(r>=u.size)e(n);else{var d=u.slice(r,r+10485760);t.readAsArrayBuffer(d)}};t.onload=function(e){n.set(new Uint8Array(t.result),r),r+=10485760,a()},t.onerror=function(e){d(t.error.message)},a()})}},{key:"readFileSync",value:function(e){if(!v())throw new TypeError("Browser environment cannot support readFileSync");return Uint8Array.from(e.data).buffer}},{key:"mod",value:function(e,u){return(e%u+u)%u}},{key:"gcd",value:function(e,d){return d?u.gcd(d,e%d):e}},{key:"modInv",value:function(e,u){e%=u;for(var d=1;d<u;d++)if(e*d%26==1)return d}},{key:"charRep",value:function(e){return{Space:" ",Percent:"%",Comma:",","Semi-colon":";",Colon:":",Tab:"\t","Line feed":"\n",CRLF:"\r\n","Forward slash":"/",Backslash:"\\","0x":"0x","\\x":"\\x","Nothing (separate chars)":"",None:""}[e]}},{key:"regexRep",value:function(e){return{Space:/\s+/g,Percent:/%/g,Comma:/,/g,"Semi-colon":/;/g,Colon:/:/g,"Line feed":/\n/g,CRLF:/\r\n/g,"Forward slash":/\//g,Backslash:/\\/g,"0x":/0x/g,"\\x":/\\x/g,None:/\s+/g}[e]}}]),u}();function v(){return void 0!==t&&null!=t.versions&&null!=t.versions.node}function x(){return"object"==typeof window}function w(){return"function"==typeof importScripts}function A(e){w()?self.sendStatusMessage(e):x()?app.alert(e,1e4):v()&&console.debug(e)}u.b=y,Array.prototype.unique=function(){for(var e={},u=[],d=0,t=this.length;d<t;d++)Object.prototype.hasOwnProperty.call(e,this[d])||(u.push(this[d]),e[this[d]]=1);return u},Array.prototype.max=function(){return Math.max.apply(null,this)},Array.prototype.min=function(){return Math.min.apply(null,this)},Array.prototype.sum=function(){return this.reduce(function(e,u){return e+u},0)},Array.prototype.equals=function(e){if(!e)return!1;var u=this.length;if(u!==e.length)return!1;for(;u--;)if(this[u]!==e[u])return!1;return!0},String.prototype.count=function(e){return this.split(e).length-1};var _={};function E(e,u,d,t,n){return function(){clearTimeout(_[d]),_[d]=setTimeout(function(){e.apply(t,n)},u)}}String.prototype.padStart||(String.prototype.padStart=function(e,u){return e>>=0,u=String(void 0!==u?u:" "),this.length>e?String(this):((e-=this.length)>u.length&&(u+=u.repeat(e/u.length)),u.slice(0,e)+String(this))}),String.prototype.padEnd||(String.prototype.padEnd=function(e,u){return e>>=0,u=String(void 0!==u?u:" "),this.length>e?String(this):((e-=this.length)>u.length&&(u+=u.repeat(e/u.length)),String(this)+u.slice(0,e))})}).call(this,d(12).Buffer,d(27))},1:function(e,u){e.exports=function(e,u){if(!(e instanceof u))throw new TypeError("Cannot call a class as a function")}},10:function(e,u,d){"use strict";d.d(u,"a",function(){return i});var t=d(1),n=d.n(t),r=d(2),a=d.n(r),i=function(){function e(u){n()(this,e),this.bytes=u,this.length=this.bytes.length,this.position=0,this.bitPos=0}return a()(e,[{key:"getBytes",value:function(e){if(!(this.position>this.length)){var u=this.position+e,d=this.bytes.slice(this.position,u);return this.position=u,this.bitPos=0,d}}},{key:"readString",value:function(e){if(!(this.position>this.length)){for(var u="",d=this.position;d<this.position+e;d++){var t=this.bytes[d];if(0===t)break;u+=String.fromCharCode(t)}return this.position+=e,this.bitPos=0,u}}},{key:"readInt",value:function(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"be";if(!(this.position>this.length)){var d=0;if("be"===u)for(var t=this.position;t<this.position+e;t++)d<<=8,d|=this.bytes[t];else for(var n=this.position+e-1;n>=this.position;n--)d<<=8,d|=this.bytes[n];return this.position+=e,this.bitPos=0,d}}},{key:"readBits",value:function(e){if(!(this.position>this.length)){var u=0,d=0;for(u=(this.bytes[this.position++]&256-(1<<this.bitPos))>>>this.bitPos,d=8-this.bitPos,this.bitPos=0;d<e;)u|=this.bytes[this.position++]<<d,d+=8;if(d>e){var t=d-e;u&=(1<<e)-1,d-=t,this.position--,this.bitPos=8-t}return u}}},{key:"continueUntil",value:function(e){if(!(this.position>this.length))if(this.bitPos=0,"number"!=typeof e)for(var u=!1;!u&&this.position<this.length;){for(;++this.position<this.length&&this.bytes[this.position]!==e[0];);u=!0;for(var d=1;d<e.length;d++)(this.position+d>this.length||this.bytes[this.position+d]!==e[d])&&(u=!1)}else for(;++this.position<this.length&&this.bytes[this.position]!==e;);}},{key:"consumeIf",value:function(e){this.bytes[this.position]===e&&(this.position++,this.bitPos=0)}},{key:"moveForwardsBy",value:function(e){var u=this.position+e;if(u<0||u>this.length)throw new Error("Cannot move to position "+u+" in stream. Out of bounds.");this.position=u,this.bitPos=0}},{key:"moveBackwardsBy",value:function(e){var u=this.position-e;if(u<0||u>this.length)throw new Error("Cannot move to position "+u+" in stream. Out of bounds.");this.position=u,this.bitPos=0}},{key:"moveBackwardsByBits",value:function(e){if(e<=this.bitPos)this.bitPos-=e;else for(this.bitPos>0&&(e-=this.bitPos,this.bitPos=0);e>0;)this.moveBackwardsBy(1),this.bitPos=8,this.moveBackwardsByBits(e),e-=8}},{key:"moveTo",value:function(e){if(e<0||e>this.length)throw new Error("Cannot move to position "+e+" in stream. Out of bounds.");this.position=e,this.bitPos=0}},{key:"hasMore",value:function(){return this.position<this.length}},{key:"carve",value:function(){return this.bitPos>0&&this.position++,this.bytes.slice(0,this.position)}}]),e}()},11:function(e,u){function d(e,u,d,t,n,r,a){try{var i=e[r](a),c=i.value}catch(e){return void d(e)}i.done?u(c):Promise.resolve(c).then(t,n)}e.exports=function(e){return function(){var u=this,t=arguments;return new Promise(function(n,r){var a=e.apply(u,t);function i(e){d(a,n,r,i,c,"next",e)}function c(e){d(a,n,r,i,c,"throw",e)}i(void 0)})}}},12:function(e,u,d){"use strict";(function(e){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <<EMAIL>> <http://feross.org>
 * @license  MIT
 */
var t=d(45),n=d(46),r=d(47);function a(){return c.TYPED_ARRAY_SUPPORT?**********:**********}function i(e,u){if(a()<u)throw new RangeError("Invalid typed array length");return c.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(u)).__proto__=c.prototype:(null===e&&(e=new c(u)),e.length=u),e}function c(e,u,d){if(!(c.TYPED_ARRAY_SUPPORT||this instanceof c))return new c(e,u,d);if("number"==typeof e){if("string"==typeof u)throw new Error("If encoding is specified then the first argument must be a string");return s(this,e)}return o(this,e,u,d)}function o(e,u,d,t){if("number"==typeof u)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&u instanceof ArrayBuffer?function(e,u,d,t){if(u.byteLength,d<0||u.byteLength<d)throw new RangeError("'offset' is out of bounds");if(u.byteLength<d+(t||0))throw new RangeError("'length' is out of bounds");u=void 0===d&&void 0===t?new Uint8Array(u):void 0===t?new Uint8Array(u,d):new Uint8Array(u,d,t);c.TYPED_ARRAY_SUPPORT?(e=u).__proto__=c.prototype:e=l(e,u);return e}(e,u,d,t):"string"==typeof u?function(e,u,d){"string"==typeof d&&""!==d||(d="utf8");if(!c.isEncoding(d))throw new TypeError('"encoding" must be a valid string encoding');var t=0|m(u,d),n=(e=i(e,t)).write(u,d);n!==t&&(e=e.slice(0,n));return e}(e,u,d):function(e,u){if(c.isBuffer(u)){var d=0|p(u.length);return 0===(e=i(e,d)).length?e:(u.copy(e,0,0,d),e)}if(u){if("undefined"!=typeof ArrayBuffer&&u.buffer instanceof ArrayBuffer||"length"in u)return"number"!=typeof u.length||(t=u.length)!=t?i(e,0):l(e,u);if("Buffer"===u.type&&r(u.data))return l(e,u.data)}var t;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,u)}function f(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function s(e,u){if(f(u),e=i(e,u<0?0:0|p(u)),!c.TYPED_ARRAY_SUPPORT)for(var d=0;d<u;++d)e[d]=0;return e}function l(e,u){var d=u.length<0?0:0|p(u.length);e=i(e,d);for(var t=0;t<d;t+=1)e[t]=255&u[t];return e}function p(e){if(e>=a())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+a().toString(16)+" bytes");return 0|e}function m(e,u){if(c.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var d=e.length;if(0===d)return 0;for(var t=!1;;)switch(u){case"ascii":case"latin1":case"binary":return d;case"utf8":case"utf-8":case void 0:return G(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*d;case"hex":return d>>>1;case"base64":return $(e).length;default:if(t)return G(e).length;u=(""+u).toLowerCase(),t=!0}}function h(e,u,d){var t=!1;if((void 0===u||u<0)&&(u=0),u>this.length)return"";if((void 0===d||d>this.length)&&(d=this.length),d<=0)return"";if((d>>>=0)<=(u>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return k(this,u,d);case"utf8":case"utf-8":return I(this,u,d);case"ascii":return S(this,u,d);case"latin1":case"binary":return C(this,u,d);case"base64":return F(this,u,d);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return R(this,u,d);default:if(t)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),t=!0}}function b(e,u,d){var t=e[u];e[u]=e[d],e[d]=t}function g(e,u,d,t,n){if(0===e.length)return-1;if("string"==typeof d?(t=d,d=0):d>**********?d=**********:d<-2147483648&&(d=-2147483648),d=+d,isNaN(d)&&(d=n?0:e.length-1),d<0&&(d=e.length+d),d>=e.length){if(n)return-1;d=e.length-1}else if(d<0){if(!n)return-1;d=0}if("string"==typeof u&&(u=c.from(u,t)),c.isBuffer(u))return 0===u.length?-1:y(e,u,d,t,n);if("number"==typeof u)return u&=255,c.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?n?Uint8Array.prototype.indexOf.call(e,u,d):Uint8Array.prototype.lastIndexOf.call(e,u,d):y(e,[u],d,t,n);throw new TypeError("val must be string, number or Buffer")}function y(e,u,d,t,n){var r,a=1,i=e.length,c=u.length;if(void 0!==t&&("ucs2"===(t=String(t).toLowerCase())||"ucs-2"===t||"utf16le"===t||"utf-16le"===t)){if(e.length<2||u.length<2)return-1;a=2,i/=2,c/=2,d/=2}function o(e,u){return 1===a?e[u]:e.readUInt16BE(u*a)}if(n){var f=-1;for(r=d;r<i;r++)if(o(e,r)===o(u,-1===f?0:r-f)){if(-1===f&&(f=r),r-f+1===c)return f*a}else-1!==f&&(r-=r-f),f=-1}else for(d+c>i&&(d=i-c),r=d;r>=0;r--){for(var s=!0,l=0;l<c;l++)if(o(e,r+l)!==o(u,l)){s=!1;break}if(s)return r}return-1}function v(e,u,d,t){d=Number(d)||0;var n=e.length-d;t?(t=Number(t))>n&&(t=n):t=n;var r=u.length;if(r%2!=0)throw new TypeError("Invalid hex string");t>r/2&&(t=r/2);for(var a=0;a<t;++a){var i=parseInt(u.substr(2*a,2),16);if(isNaN(i))return a;e[d+a]=i}return a}function x(e,u,d,t){return Y(G(u,e.length-d),e,d,t)}function w(e,u,d,t){return Y(function(e){for(var u=[],d=0;d<e.length;++d)u.push(255&e.charCodeAt(d));return u}(u),e,d,t)}function A(e,u,d,t){return w(e,u,d,t)}function _(e,u,d,t){return Y($(u),e,d,t)}function E(e,u,d,t){return Y(function(e,u){for(var d,t,n,r=[],a=0;a<e.length&&!((u-=2)<0);++a)d=e.charCodeAt(a),t=d>>8,n=d%256,r.push(n),r.push(t);return r}(u,e.length-d),e,d,t)}function F(e,u,d){return 0===u&&d===e.length?t.fromByteArray(e):t.fromByteArray(e.slice(u,d))}function I(e,u,d){d=Math.min(e.length,d);for(var t=[],n=u;n<d;){var r,a,i,c,o=e[n],f=null,s=o>239?4:o>223?3:o>191?2:1;if(n+s<=d)switch(s){case 1:o<128&&(f=o);break;case 2:128==(192&(r=e[n+1]))&&(c=(31&o)<<6|63&r)>127&&(f=c);break;case 3:r=e[n+1],a=e[n+2],128==(192&r)&&128==(192&a)&&(c=(15&o)<<12|(63&r)<<6|63&a)>2047&&(c<55296||c>57343)&&(f=c);break;case 4:r=e[n+1],a=e[n+2],i=e[n+3],128==(192&r)&&128==(192&a)&&128==(192&i)&&(c=(15&o)<<18|(63&r)<<12|(63&a)<<6|63&i)>65535&&c<1114112&&(f=c)}null===f?(f=65533,s=1):f>65535&&(f-=65536,t.push(f>>>10&1023|55296),f=56320|1023&f),t.push(f),n+=s}return function(e){var u=e.length;if(u<=B)return String.fromCharCode.apply(String,e);var d="",t=0;for(;t<u;)d+=String.fromCharCode.apply(String,e.slice(t,t+=B));return d}(t)}u.Buffer=c,u.SlowBuffer=function(e){+e!=e&&(e=0);return c.alloc(+e)},u.INSPECT_MAX_BYTES=50,c.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}(),u.kMaxLength=a(),c.poolSize=8192,c._augment=function(e){return e.__proto__=c.prototype,e},c.from=function(e,u,d){return o(null,e,u,d)},c.TYPED_ARRAY_SUPPORT&&(c.prototype.__proto__=Uint8Array.prototype,c.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&c[Symbol.species]===c&&Object.defineProperty(c,Symbol.species,{value:null,configurable:!0})),c.alloc=function(e,u,d){return function(e,u,d,t){return f(u),u<=0?i(e,u):void 0!==d?"string"==typeof t?i(e,u).fill(d,t):i(e,u).fill(d):i(e,u)}(null,e,u,d)},c.allocUnsafe=function(e){return s(null,e)},c.allocUnsafeSlow=function(e){return s(null,e)},c.isBuffer=function(e){return!(null==e||!e._isBuffer)},c.compare=function(e,u){if(!c.isBuffer(e)||!c.isBuffer(u))throw new TypeError("Arguments must be Buffers");if(e===u)return 0;for(var d=e.length,t=u.length,n=0,r=Math.min(d,t);n<r;++n)if(e[n]!==u[n]){d=e[n],t=u[n];break}return d<t?-1:t<d?1:0},c.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},c.concat=function(e,u){if(!r(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return c.alloc(0);var d;if(void 0===u)for(u=0,d=0;d<e.length;++d)u+=e[d].length;var t=c.allocUnsafe(u),n=0;for(d=0;d<e.length;++d){var a=e[d];if(!c.isBuffer(a))throw new TypeError('"list" argument must be an Array of Buffers');a.copy(t,n),n+=a.length}return t},c.byteLength=m,c.prototype._isBuffer=!0,c.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var u=0;u<e;u+=2)b(this,u,u+1);return this},c.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var u=0;u<e;u+=4)b(this,u,u+3),b(this,u+1,u+2);return this},c.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var u=0;u<e;u+=8)b(this,u,u+7),b(this,u+1,u+6),b(this,u+2,u+5),b(this,u+3,u+4);return this},c.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?I(this,0,e):h.apply(this,arguments)},c.prototype.equals=function(e){if(!c.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===c.compare(this,e)},c.prototype.inspect=function(){var e="",d=u.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,d).match(/.{2}/g).join(" "),this.length>d&&(e+=" ... ")),"<Buffer "+e+">"},c.prototype.compare=function(e,u,d,t,n){if(!c.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===u&&(u=0),void 0===d&&(d=e?e.length:0),void 0===t&&(t=0),void 0===n&&(n=this.length),u<0||d>e.length||t<0||n>this.length)throw new RangeError("out of range index");if(t>=n&&u>=d)return 0;if(t>=n)return-1;if(u>=d)return 1;if(this===e)return 0;for(var r=(n>>>=0)-(t>>>=0),a=(d>>>=0)-(u>>>=0),i=Math.min(r,a),o=this.slice(t,n),f=e.slice(u,d),s=0;s<i;++s)if(o[s]!==f[s]){r=o[s],a=f[s];break}return r<a?-1:a<r?1:0},c.prototype.includes=function(e,u,d){return-1!==this.indexOf(e,u,d)},c.prototype.indexOf=function(e,u,d){return g(this,e,u,d,!0)},c.prototype.lastIndexOf=function(e,u,d){return g(this,e,u,d,!1)},c.prototype.write=function(e,u,d,t){if(void 0===u)t="utf8",d=this.length,u=0;else if(void 0===d&&"string"==typeof u)t=u,d=this.length,u=0;else{if(!isFinite(u))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");u|=0,isFinite(d)?(d|=0,void 0===t&&(t="utf8")):(t=d,d=void 0)}var n=this.length-u;if((void 0===d||d>n)&&(d=n),e.length>0&&(d<0||u<0)||u>this.length)throw new RangeError("Attempt to write outside buffer bounds");t||(t="utf8");for(var r=!1;;)switch(t){case"hex":return v(this,e,u,d);case"utf8":case"utf-8":return x(this,e,u,d);case"ascii":return w(this,e,u,d);case"latin1":case"binary":return A(this,e,u,d);case"base64":return _(this,e,u,d);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return E(this,e,u,d);default:if(r)throw new TypeError("Unknown encoding: "+t);t=(""+t).toLowerCase(),r=!0}},c.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var B=4096;function S(e,u,d){var t="";d=Math.min(e.length,d);for(var n=u;n<d;++n)t+=String.fromCharCode(127&e[n]);return t}function C(e,u,d){var t="";d=Math.min(e.length,d);for(var n=u;n<d;++n)t+=String.fromCharCode(e[n]);return t}function k(e,u,d){var t=e.length;(!u||u<0)&&(u=0),(!d||d<0||d>t)&&(d=t);for(var n="",r=u;r<d;++r)n+=j(e[r]);return n}function R(e,u,d){for(var t=e.slice(u,d),n="",r=0;r<t.length;r+=2)n+=String.fromCharCode(t[r]+256*t[r+1]);return n}function T(e,u,d){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+u>d)throw new RangeError("Trying to access beyond buffer length")}function O(e,u,d,t,n,r){if(!c.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(u>n||u<r)throw new RangeError('"value" argument is out of bounds');if(d+t>e.length)throw new RangeError("Index out of range")}function L(e,u,d,t){u<0&&(u=65535+u+1);for(var n=0,r=Math.min(e.length-d,2);n<r;++n)e[d+n]=(u&255<<8*(t?n:1-n))>>>8*(t?n:1-n)}function P(e,u,d,t){u<0&&(u=4294967295+u+1);for(var n=0,r=Math.min(e.length-d,4);n<r;++n)e[d+n]=u>>>8*(t?n:3-n)&255}function D(e,u,d,t,n,r){if(d+t>e.length)throw new RangeError("Index out of range");if(d<0)throw new RangeError("Index out of range")}function N(e,u,d,t,r){return r||D(e,0,d,4),n.write(e,u,d,t,23,4),d+4}function U(e,u,d,t,r){return r||D(e,0,d,8),n.write(e,u,d,t,52,8),d+8}c.prototype.slice=function(e,u){var d,t=this.length;if((e=~~e)<0?(e+=t)<0&&(e=0):e>t&&(e=t),(u=void 0===u?t:~~u)<0?(u+=t)<0&&(u=0):u>t&&(u=t),u<e&&(u=e),c.TYPED_ARRAY_SUPPORT)(d=this.subarray(e,u)).__proto__=c.prototype;else{var n=u-e;d=new c(n,void 0);for(var r=0;r<n;++r)d[r]=this[r+e]}return d},c.prototype.readUIntLE=function(e,u,d){e|=0,u|=0,d||T(e,u,this.length);for(var t=this[e],n=1,r=0;++r<u&&(n*=256);)t+=this[e+r]*n;return t},c.prototype.readUIntBE=function(e,u,d){e|=0,u|=0,d||T(e,u,this.length);for(var t=this[e+--u],n=1;u>0&&(n*=256);)t+=this[e+--u]*n;return t},c.prototype.readUInt8=function(e,u){return u||T(e,1,this.length),this[e]},c.prototype.readUInt16LE=function(e,u){return u||T(e,2,this.length),this[e]|this[e+1]<<8},c.prototype.readUInt16BE=function(e,u){return u||T(e,2,this.length),this[e]<<8|this[e+1]},c.prototype.readUInt32LE=function(e,u){return u||T(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},c.prototype.readUInt32BE=function(e,u){return u||T(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},c.prototype.readIntLE=function(e,u,d){e|=0,u|=0,d||T(e,u,this.length);for(var t=this[e],n=1,r=0;++r<u&&(n*=256);)t+=this[e+r]*n;return t>=(n*=128)&&(t-=Math.pow(2,8*u)),t},c.prototype.readIntBE=function(e,u,d){e|=0,u|=0,d||T(e,u,this.length);for(var t=u,n=1,r=this[e+--t];t>0&&(n*=256);)r+=this[e+--t]*n;return r>=(n*=128)&&(r-=Math.pow(2,8*u)),r},c.prototype.readInt8=function(e,u){return u||T(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},c.prototype.readInt16LE=function(e,u){u||T(e,2,this.length);var d=this[e]|this[e+1]<<8;return 32768&d?4294901760|d:d},c.prototype.readInt16BE=function(e,u){u||T(e,2,this.length);var d=this[e+1]|this[e]<<8;return 32768&d?4294901760|d:d},c.prototype.readInt32LE=function(e,u){return u||T(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},c.prototype.readInt32BE=function(e,u){return u||T(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},c.prototype.readFloatLE=function(e,u){return u||T(e,4,this.length),n.read(this,e,!0,23,4)},c.prototype.readFloatBE=function(e,u){return u||T(e,4,this.length),n.read(this,e,!1,23,4)},c.prototype.readDoubleLE=function(e,u){return u||T(e,8,this.length),n.read(this,e,!0,52,8)},c.prototype.readDoubleBE=function(e,u){return u||T(e,8,this.length),n.read(this,e,!1,52,8)},c.prototype.writeUIntLE=function(e,u,d,t){(e=+e,u|=0,d|=0,t)||O(this,e,u,d,Math.pow(2,8*d)-1,0);var n=1,r=0;for(this[u]=255&e;++r<d&&(n*=256);)this[u+r]=e/n&255;return u+d},c.prototype.writeUIntBE=function(e,u,d,t){(e=+e,u|=0,d|=0,t)||O(this,e,u,d,Math.pow(2,8*d)-1,0);var n=d-1,r=1;for(this[u+n]=255&e;--n>=0&&(r*=256);)this[u+n]=e/r&255;return u+d},c.prototype.writeUInt8=function(e,u,d){return e=+e,u|=0,d||O(this,e,u,1,255,0),c.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[u]=255&e,u+1},c.prototype.writeUInt16LE=function(e,u,d){return e=+e,u|=0,d||O(this,e,u,2,65535,0),c.TYPED_ARRAY_SUPPORT?(this[u]=255&e,this[u+1]=e>>>8):L(this,e,u,!0),u+2},c.prototype.writeUInt16BE=function(e,u,d){return e=+e,u|=0,d||O(this,e,u,2,65535,0),c.TYPED_ARRAY_SUPPORT?(this[u]=e>>>8,this[u+1]=255&e):L(this,e,u,!1),u+2},c.prototype.writeUInt32LE=function(e,u,d){return e=+e,u|=0,d||O(this,e,u,4,4294967295,0),c.TYPED_ARRAY_SUPPORT?(this[u+3]=e>>>24,this[u+2]=e>>>16,this[u+1]=e>>>8,this[u]=255&e):P(this,e,u,!0),u+4},c.prototype.writeUInt32BE=function(e,u,d){return e=+e,u|=0,d||O(this,e,u,4,4294967295,0),c.TYPED_ARRAY_SUPPORT?(this[u]=e>>>24,this[u+1]=e>>>16,this[u+2]=e>>>8,this[u+3]=255&e):P(this,e,u,!1),u+4},c.prototype.writeIntLE=function(e,u,d,t){if(e=+e,u|=0,!t){var n=Math.pow(2,8*d-1);O(this,e,u,d,n-1,-n)}var r=0,a=1,i=0;for(this[u]=255&e;++r<d&&(a*=256);)e<0&&0===i&&0!==this[u+r-1]&&(i=1),this[u+r]=(e/a>>0)-i&255;return u+d},c.prototype.writeIntBE=function(e,u,d,t){if(e=+e,u|=0,!t){var n=Math.pow(2,8*d-1);O(this,e,u,d,n-1,-n)}var r=d-1,a=1,i=0;for(this[u+r]=255&e;--r>=0&&(a*=256);)e<0&&0===i&&0!==this[u+r+1]&&(i=1),this[u+r]=(e/a>>0)-i&255;return u+d},c.prototype.writeInt8=function(e,u,d){return e=+e,u|=0,d||O(this,e,u,1,127,-128),c.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[u]=255&e,u+1},c.prototype.writeInt16LE=function(e,u,d){return e=+e,u|=0,d||O(this,e,u,2,32767,-32768),c.TYPED_ARRAY_SUPPORT?(this[u]=255&e,this[u+1]=e>>>8):L(this,e,u,!0),u+2},c.prototype.writeInt16BE=function(e,u,d){return e=+e,u|=0,d||O(this,e,u,2,32767,-32768),c.TYPED_ARRAY_SUPPORT?(this[u]=e>>>8,this[u+1]=255&e):L(this,e,u,!1),u+2},c.prototype.writeInt32LE=function(e,u,d){return e=+e,u|=0,d||O(this,e,u,4,**********,-2147483648),c.TYPED_ARRAY_SUPPORT?(this[u]=255&e,this[u+1]=e>>>8,this[u+2]=e>>>16,this[u+3]=e>>>24):P(this,e,u,!0),u+4},c.prototype.writeInt32BE=function(e,u,d){return e=+e,u|=0,d||O(this,e,u,4,**********,-2147483648),e<0&&(e=4294967295+e+1),c.TYPED_ARRAY_SUPPORT?(this[u]=e>>>24,this[u+1]=e>>>16,this[u+2]=e>>>8,this[u+3]=255&e):P(this,e,u,!1),u+4},c.prototype.writeFloatLE=function(e,u,d){return N(this,e,u,!0,d)},c.prototype.writeFloatBE=function(e,u,d){return N(this,e,u,!1,d)},c.prototype.writeDoubleLE=function(e,u,d){return U(this,e,u,!0,d)},c.prototype.writeDoubleBE=function(e,u,d){return U(this,e,u,!1,d)},c.prototype.copy=function(e,u,d,t){if(d||(d=0),t||0===t||(t=this.length),u>=e.length&&(u=e.length),u||(u=0),t>0&&t<d&&(t=d),t===d)return 0;if(0===e.length||0===this.length)return 0;if(u<0)throw new RangeError("targetStart out of bounds");if(d<0||d>=this.length)throw new RangeError("sourceStart out of bounds");if(t<0)throw new RangeError("sourceEnd out of bounds");t>this.length&&(t=this.length),e.length-u<t-d&&(t=e.length-u+d);var n,r=t-d;if(this===e&&d<u&&u<t)for(n=r-1;n>=0;--n)e[n+u]=this[n+d];else if(r<1e3||!c.TYPED_ARRAY_SUPPORT)for(n=0;n<r;++n)e[n+u]=this[n+d];else Uint8Array.prototype.set.call(e,this.subarray(d,d+r),u);return r},c.prototype.fill=function(e,u,d,t){if("string"==typeof e){if("string"==typeof u?(t=u,u=0,d=this.length):"string"==typeof d&&(t=d,d=this.length),1===e.length){var n=e.charCodeAt(0);n<256&&(e=n)}if(void 0!==t&&"string"!=typeof t)throw new TypeError("encoding must be a string");if("string"==typeof t&&!c.isEncoding(t))throw new TypeError("Unknown encoding: "+t)}else"number"==typeof e&&(e&=255);if(u<0||this.length<u||this.length<d)throw new RangeError("Out of range index");if(d<=u)return this;var r;if(u>>>=0,d=void 0===d?this.length:d>>>0,e||(e=0),"number"==typeof e)for(r=u;r<d;++r)this[r]=e;else{var a=c.isBuffer(e)?e:G(new c(e,t).toString()),i=a.length;for(r=0;r<d-u;++r)this[r+u]=a[r%i]}return this};var M=/[^+\/0-9A-Za-z-_]/g;function j(e){return e<16?"0"+e.toString(16):e.toString(16)}function G(e,u){var d;u=u||1/0;for(var t=e.length,n=null,r=[],a=0;a<t;++a){if((d=e.charCodeAt(a))>55295&&d<57344){if(!n){if(d>56319){(u-=3)>-1&&r.push(239,191,189);continue}if(a+1===t){(u-=3)>-1&&r.push(239,191,189);continue}n=d;continue}if(d<56320){(u-=3)>-1&&r.push(239,191,189),n=d;continue}d=65536+(n-55296<<10|d-56320)}else n&&(u-=3)>-1&&r.push(239,191,189);if(n=null,d<128){if((u-=1)<0)break;r.push(d)}else if(d<2048){if((u-=2)<0)break;r.push(d>>6|192,63&d|128)}else if(d<65536){if((u-=3)<0)break;r.push(d>>12|224,d>>6&63|128,63&d|128)}else{if(!(d<1114112))throw new Error("Invalid code point");if((u-=4)<0)break;r.push(d>>18|240,d>>12&63|128,d>>6&63|128,63&d|128)}}return r}function $(e){return t.toByteArray(function(e){if((e=function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}(e).replace(M,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function Y(e,u,d,t){for(var n=0;n<t&&!(n+d>=u.length||n>=e.length);++n)u[n+d]=e[n];return n}}).call(this,d(28))},1255:function(e,u,d){"use strict";var t=d(197);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var n=t(d(1256)),r=t(d(1271)),a=t(d(424)),i=t(d(1293)),c="xregexp",o={astral:!1,namespacing:!1},f={exec:RegExp.prototype.exec,test:RegExp.prototype.test,match:String.prototype.match,replace:String.prototype.replace,split:String.prototype.split},s={},l={},p={},m=[],h="default",b="class",g={default:/\\(?:0(?:[0-3][0-7]{0,2}|[4-7][0-7]?)?|[1-9]\d*|x[\dA-Fa-f]{2}|u(?:[\dA-Fa-f]{4}|{[\dA-Fa-f]+})|c[A-Za-z]|[\s\S])|\(\?(?:[:=!]|<[=!])|[?*+]\?|{\d+(?:,\d*)?}\??|[\s\S]/,class:/\\(?:[0-3][0-7]{0,2}|[4-7][0-7]?|x[\dA-Fa-f]{2}|u(?:[\dA-Fa-f]{4}|{[\dA-Fa-f]+})|c[A-Za-z]|[\s\S])|[\s\S]/},y=/\$(?:{([\w$]+)}|<([\w$]+)>|(\d\d?|[\s\S]))/g,v=void 0===f.exec.call(/()??/,"")[1],x=void 0!==/x/.flags,w={}.toString;function A(e){var u=!0;try{new RegExp("",e)}catch(e){u=!1}return u}var _=A("u"),E=A("y"),F={g:!0,i:!0,m:!0,u:_,y:E};function I(e,u,d,t,n){if(e[c]={captureNames:u},n)return e;if(e.__proto__)e.__proto__=j.prototype;else for(var r in j.prototype)e[r]=j.prototype[r];return e[c].source=d,e[c].flags=t?t.split("").sort().join(""):t,e}function B(e){return f.replace.call(e,/([\s\S])(?=[\s\S]*\1)/g,"")}function S(e,u){if(!j.isRegExp(e))throw new TypeError("Type RegExp expected");var d=e[c]||{},t=function(e){return x?e.flags:f.exec.call(/\/([a-z]*)$/i,RegExp.prototype.toString.call(e))[1]}(e),n="",r="",a=null,i=null;return(u=u||{}).removeG&&(r+="g"),u.removeY&&(r+="y"),r&&(t=f.replace.call(t,new RegExp("[".concat(r,"]+"),"g"),"")),u.addG&&(n+="g"),u.addY&&(n+="y"),n&&(t=B(t+n)),u.isInternalOnly||(void 0!==d.source&&(a=d.source),null!=d.flags&&(i=n?B(d.flags+n):d.flags)),e=I(new RegExp(u.source||e.source,t),function(e){return!(!e[c]||!e[c].captureNames)}(e)?d.captureNames.slice(0):null,a,i,u.isInternalOnly)}function C(e){return(0,i.default)(e,16)}function k(e,u,d){return"("===e.input[e.index-1]||")"===e.input[e.index+e[0].length]||"|"===e.input[e.index-1]||"|"===e.input[e.index+e[0].length]||e.index<1||e.index+e[0].length>=e.input.length||f.test.call(/^\(\?[:=!]/,e.input.substr(e.index-3,3))||function(e,u,d){return f.test.call(-1!==d.indexOf("x")?/^(?:\s|#[^#\n]*|\(\?#[^)]*\))*(?:[?*+]|{\d+(?:,\d*)?})/:/^(?:\(\?#[^)]*\))*(?:[?*+]|{\d+(?:,\d*)?})/,e.slice(u))}(e.input,e.index+e[0].length,d)?"":"(?:)"}function R(e){return(0,i.default)(e,10).toString(16)}function T(e,u){return w.call(e)==="[object ".concat(u,"]")}function O(e){for(;e.length<4;)e="0".concat(e);return e}function L(e){var u={};return T(e,"String")?(j.forEach(e,/[^\s,]+/,function(e){u[e]=!0}),u):e}function P(e){if(!/^[\w$]$/.test(e))throw new Error("Flag must be a single character A-Za-z0-9_$");F[e]=!0}function D(e,u,d,t,n){for(var r,a,i=m.length,c=e[d],o=null;i--;)if(!((a=m[i]).leadChar&&a.leadChar!==c||a.scope!==t&&"all"!==a.scope||a.flag&&-1===u.indexOf(a.flag))&&(r=j.exec(e,a.regex,d,"sticky"))){o={matchLength:r[0].length,output:a.handler.call(n,r,t,u),reparse:a.reparse};break}return o}function N(e){o.astral=e}function U(e){o.namespacing=e}function M(e){if(null==e)throw new TypeError("Cannot convert null or undefined to object");return e}function j(e,u){if(j.isRegExp(e)){if(void 0!==u)throw new TypeError("Cannot supply flags when copying a RegExp");return S(e)}if(e=void 0===e?"":String(e),u=void 0===u?"":String(u),j.isInstalled("astral")&&-1===u.indexOf("A")&&(u+="A"),p[e]||(p[e]={}),!p[e][u]){for(var d,t={hasNamedCapture:!1,captureNames:[]},n=h,i="",c=0,o=function(e,u){if(B(u)!==u)throw new SyntaxError("Invalid duplicate regex flag ".concat(u));e=f.replace.call(e,/^\(\?([\w$]+)\)/,function(e,d){if(f.test.call(/[gy]/,d))throw new SyntaxError("Cannot use flag g or y in mode modifier ".concat(e));return u=B(u+d),""});var d=!0,t=!1,n=void 0;try{for(var r,i=(0,a.default)(u);!(d=(r=i.next()).done);d=!0){var c=r.value;if(!F[c])throw new SyntaxError("Unknown regex flag ".concat(c))}}catch(e){t=!0,n=e}finally{try{d||null==i.return||i.return()}finally{if(t)throw n}}return{pattern:e,flags:u}}(e,u),s=o.pattern,l=o.flags;c<s.length;){do{(d=D(s,l,c,n,t))&&d.reparse&&(s=s.slice(0,c)+d.output+s.slice(c+d.matchLength))}while(d&&d.reparse);if(d)i+=d.output,c+=d.matchLength||1;else{var m=j.exec(s,g[n],c,"sticky"),y=(0,r.default)(m,1)[0];i+=y,c+=y.length,"["===y&&n===h?n=b:"]"===y&&n===b&&(n=h)}}p[e][u]={pattern:f.replace.call(i,/(?:\(\?:\))+/g,"(?:)"),flags:f.replace.call(l,/[^gimuy]+/g,""),captures:t.hasNamedCapture?t.captureNames:null}}var v=p[e][u];return I(new RegExp(v.pattern,v.flags),v.captures,e,u)}j.prototype=/(?:)/,j.version="4.2.4",j._clipDuplicates=B,j._hasNativeFlag=A,j._dec=C,j._hex=R,j._pad4=O,j.addToken=function(e,u,d){var t=(d=d||{}).optionalFlags;if(d.flag&&P(d.flag),t){t=f.split.call(t,"");var n=!0,r=!1,i=void 0;try{for(var c,o=(0,a.default)(t);!(n=(c=o.next()).done);n=!0){P(c.value)}}catch(e){r=!0,i=e}finally{try{n||null==o.return||o.return()}finally{if(r)throw i}}}m.push({regex:S(e,{addG:!0,addY:E,isInternalOnly:!0}),handler:u,scope:d.scope||h,flag:d.flag,reparse:d.reparse,leadChar:d.leadChar}),j.cache.flush("patterns")},j.cache=function(e,u){return l[e]||(l[e]={}),l[e][u]||(l[e][u]=j(e,u))},j.cache.flush=function(e){"patterns"===e?p={}:l={}},j.escape=function(e){return f.replace.call(M(e),/[-\[\]{}()*+?.,\\^$|#\s]/g,"\\$&")},j.exec=function(e,u,d,t){var n,r,a="g",i=!1;(n=E&&!!(t||u.sticky&&!1!==t))?a+="y":t&&(i=!0,a+="FakeY"),u[c]=u[c]||{};var o=u[c][a]||(u[c][a]=S(u,{addG:!0,addY:n,source:i?"".concat(u.source,"|()"):void 0,removeY:!1===t,isInternalOnly:!0}));return d=d||0,o.lastIndex=d,r=s.exec.call(o,e),i&&r&&""===r.pop()&&(r=null),u.global&&(u.lastIndex=r?o.lastIndex:0),r},j.forEach=function(e,u,d){for(var t,n=0,r=-1;t=j.exec(e,u,n);)d(t,++r,e,u),n=t.index+(t[0].length||1)},j.globalize=function(e){return S(e,{addG:!0})},j.install=function(e){e=L(e),!o.astral&&e.astral&&N(!0),!o.namespacing&&e.namespacing&&U(!0)},j.isInstalled=function(e){return!!o[e]},j.isRegExp=function(e){return"[object RegExp]"===w.call(e)},j.match=function(e,u,d){var t=u.global&&"one"!==d||"all"===d,n=(t?"g":"")+(u.sticky?"y":"")||"noGY";u[c]=u[c]||{};var r=u[c][n]||(u[c][n]=S(u,{addG:!!t,removeG:"one"===d,isInternalOnly:!0})),a=f.match.call(M(e),r);return u.global&&(u.lastIndex="one"===d&&a?a.index+a[0].length:0),t?a||[]:a&&a[0]},j.matchChain=function(e,u){return function e(d,t){var n=u[t].regex?u[t]:{regex:u[t]},r=[];function i(e){if(n.backref){var u="Backreference to undefined group: ".concat(n.backref),d=isNaN(n.backref);if(d&&j.isInstalled("namespacing")){if(!(n.backref in e.groups))throw new ReferenceError(u)}else if(!e.hasOwnProperty(n.backref))throw new ReferenceError(u);var t=d&&j.isInstalled("namespacing")?e.groups[n.backref]:e[n.backref];r.push(t||"")}else r.push(e[0])}var c=!0,o=!1,f=void 0;try{for(var s,l=(0,a.default)(d);!(c=(s=l.next()).done);c=!0){var p=s.value;j.forEach(p,n.regex,i)}}catch(e){o=!0,f=e}finally{try{c||null==l.return||l.return()}finally{if(o)throw f}}return t!==u.length-1&&r.length?e(r,t+1):r}([e],0)},j.replace=function(e,u,d,t){var n=j.isRegExp(u),r=u.global&&"one"!==t||"all"===t,a=(r?"g":"")+(u.sticky?"y":"")||"noGY",i=u;n?(u[c]=u[c]||{},i=u[c][a]||(u[c][a]=S(u,{addG:!!r,removeG:"one"===t,isInternalOnly:!0}))):r&&(i=new RegExp(j.escape(String(u)),"g"));var o=s.replace.call(M(e),i,d);return n&&u.global&&(u.lastIndex=0),o},j.replaceEach=function(e,u){var d=!0,t=!1,n=void 0;try{for(var r,i=(0,a.default)(u);!(d=(r=i.next()).done);d=!0){var c=r.value;e=j.replace(e,c[0],c[1],c[2])}}catch(e){t=!0,n=e}finally{try{d||null==i.return||i.return()}finally{if(t)throw n}}return e},j.split=function(e,u,d){return s.split.call(M(e),u,d)},j.test=function(e,u,d,t){return!!j.exec(e,u,d,t)},j.uninstall=function(e){e=L(e),o.astral&&e.astral&&N(!1),o.namespacing&&e.namespacing&&U(!1)},j.union=function(e,u,d){var t,n,r=(d=d||{}).conjunction||"or",i=0;function o(e,u,d){var r=n[i-t];if(u){if(++i,r)return"(?<".concat(r,">")}else if(d)return"\\".concat(+d+t);return e}if(!T(e,"Array")||!e.length)throw new TypeError("Must provide a nonempty array of patterns to merge");var s=/(\()(?!\?)|\\([1-9]\d*)|\\[\s\S]|\[(?:[^\\\]]|\\[\s\S])*\]/g,l=[],p=!0,m=!1,h=void 0;try{for(var b,g=(0,a.default)(e);!(p=(b=g.next()).done);p=!0){var y=b.value;j.isRegExp(y)?(t=i,n=y[c]&&y[c].captureNames||[],l.push(f.replace.call(j(y.source).source,s,o))):l.push(j.escape(y))}}catch(e){m=!0,h=e}finally{try{p||null==g.return||g.return()}finally{if(m)throw h}}var v="none"===r?"":"|";return j(l.join(v),u)},s.exec=function(e){var u=this.lastIndex,d=f.exec.apply(this,arguments);if(d){if(!v&&d.length>1&&-1!==d.indexOf("")){var t=S(this,{removeG:!0,isInternalOnly:!0});f.replace.call(String(e).slice(d.index),t,function(){for(var e=arguments.length,u=1;u<e-2;++u)void 0===(u<0||arguments.length<=u?void 0:arguments[u])&&(d[u]=void 0)})}var r=d;if(j.isInstalled("namespacing")&&(d.groups=(0,n.default)(null),r=d.groups),this[c]&&this[c].captureNames)for(var a=1;a<d.length;++a){var i=this[c].captureNames[a-1];i&&(r[i]=d[a])}this.global&&!d[0].length&&this.lastIndex>d.index&&(this.lastIndex=d.index)}return this.global||(this.lastIndex=u),d},s.test=function(e){return!!s.exec.call(this,e)},s.match=function(e){if(j.isRegExp(e)){if(e.global){var u=f.match.apply(this,arguments);return e.lastIndex=0,u}}else e=new RegExp(e);return s.exec.call(e,M(this))},s.replace=function(e,u){var d,t,r,a=j.isRegExp(e);return a?(e[c]&&(t=e[c].captureNames),d=e.lastIndex):e+="",r=T(u,"Function")?f.replace.call(String(this),e,function(){for(var d=arguments.length,r=new Array(d),i=0;i<d;i++)r[i]=arguments[i];if(t){var c;j.isInstalled("namespacing")?(c=(0,n.default)(null),r.push(c)):(r[0]=new String(r[0]),c=r[0]);for(var o=0;o<t.length;++o)t[o]&&(c[t[o]]=r[o+1])}return a&&e.global&&(e.lastIndex=r[r.length-2]+r[0].length),u.apply(void 0,r)}):f.replace.call(null==this?this:String(this),e,function(){for(var e=arguments.length,d=new Array(e),n=0;n<e;n++)d[n]=arguments[n];return f.replace.call(String(u),y,r);function r(e,u,n,r){if(u=u||n){var a=+u;if(a<=d.length-3)return d[a]||"";if((a=t?t.indexOf(u):-1)<0)throw new SyntaxError("Backreference to undefined group ".concat(e));return d[a+1]||""}if("$"===r)return"$";if("&"===r||0==+r)return d[0];if("`"===r)return d[d.length-1].slice(0,d[d.length-2]);if("'"===r)return d[d.length-1].slice(d[d.length-2]+d[0].length);if(r=+r,!isNaN(r)){if(r>d.length-3)throw new SyntaxError("Backreference to undefined group ".concat(e));return d[r]||""}throw new SyntaxError("Invalid token ".concat(e))}}),a&&(e.global?e.lastIndex=0:e.lastIndex=d),r},s.split=function(e,u){if(!j.isRegExp(e))return f.split.apply(this,arguments);var d,t=String(this),n=[],r=e.lastIndex,a=0;return u=(void 0===u?-1:u)>>>0,j.forEach(t,e,function(e){e.index+e[0].length>a&&(n.push(t.slice(a,e.index)),e.length>1&&e.index<t.length&&Array.prototype.push.apply(n,e.slice(1)),d=e[0].length,a=e.index+d)}),a===t.length?f.test.call(e,"")&&!d||n.push(""):n.push(t.slice(a)),e.lastIndex=r,n.length>u?n.slice(0,u):n},j.addToken(/\\([ABCE-RTUVXYZaeg-mopqyz]|c(?![A-Za-z])|u(?![\dA-Fa-f]{4}|{[\dA-Fa-f]+})|x(?![\dA-Fa-f]{2}))/,function(e,u){if("B"===e[1]&&u===h)return e[0];throw new SyntaxError("Invalid escape ".concat(e[0]))},{scope:"all",leadChar:"\\"}),j.addToken(/\\u{([\dA-Fa-f]+)}/,function(e,u,d){var t=C(e[1]);if(t>1114111)throw new SyntaxError("Invalid Unicode code point ".concat(e[0]));if(t<=65535)return"\\u".concat(O(R(t)));if(_&&-1!==d.indexOf("u"))return e[0];throw new SyntaxError("Cannot use Unicode code point above \\u{FFFF} without flag u")},{scope:"all",leadChar:"\\"}),j.addToken(/\[(\^?)\]/,function(e){return e[1]?"[\\s\\S]":"\\b\\B"},{leadChar:"["}),j.addToken(/\(\?#[^)]*\)/,k,{leadChar:"("}),j.addToken(/\s+|#[^\n]*\n?/,k,{flag:"x"}),j.addToken(/\./,function(){return"[\\s\\S]"},{flag:"s",leadChar:"."}),j.addToken(/\\k<([\w$]+)>/,function(e){var u=isNaN(e[1])?this.captureNames.indexOf(e[1])+1:+e[1],d=e.index+e[0].length;if(!u||u>this.captureNames.length)throw new SyntaxError("Backreference to undefined group ".concat(e[0]));return"\\".concat(u).concat(d===e.input.length||isNaN(e.input[d])?"":"(?:)")},{leadChar:"\\"}),j.addToken(/\\(\d+)/,function(e,u){if(!(u===h&&/^[1-9]/.test(e[1])&&+e[1]<=this.captureNames.length)&&"0"!==e[1])throw new SyntaxError("Cannot use octal escape or backreference to undefined group ".concat(e[0]));return e[0]},{scope:"all",leadChar:"\\"}),j.addToken(/\(\?P?<([\w$]+)>/,function(e){if(!isNaN(e[1]))throw new SyntaxError("Cannot use integer as capture name ".concat(e[0]));if(!j.isInstalled("namespacing")&&("length"===e[1]||"__proto__"===e[1]))throw new SyntaxError("Cannot use reserved word as capture name ".concat(e[0]));if(-1!==this.captureNames.indexOf(e[1]))throw new SyntaxError("Cannot use same name for multiple groups ".concat(e[0]));return this.captureNames.push(e[1]),this.hasNamedCapture=!0,"("},{leadChar:"("}),j.addToken(/\((?!\?)/,function(e,u,d){return-1!==d.indexOf("n")?"(?:":(this.captureNames.push(null),"(")},{optionalFlags:"n",leadChar:"("});var G=j;u.default=G,e.exports=u.default},1256:function(e,u,d){e.exports=d(1257)},1257:function(e,u,d){d(1258);var t=d(199).Object;e.exports=function(e,u){return t.create(e,u)}},1258:function(e,u,d){var t=d(311);t(t.S,"Object",{create:d(668)})},1259:function(e,u,d){var t=d(1260);e.exports=function(e,u,d){if(t(e),void 0===u)return e;switch(d){case 1:return function(d){return e.call(u,d)};case 2:return function(d,t){return e.call(u,d,t)};case 3:return function(d,t,n){return e.call(u,d,t,n)}}return function(){return e.apply(u,arguments)}}},1260:function(e,u){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},1261:function(e,u,d){e.exports=!d(340)&&!d(419)(function(){return 7!=Object.defineProperty(d(666)("div"),"a",{get:function(){return 7}}).a})},1262:function(e,u,d){var t=d(418);e.exports=function(e,u){if(!t(e))return e;var d,n;if(u&&"function"==typeof(d=e.toString)&&!t(n=d.call(e)))return n;if("function"==typeof(d=e.valueOf)&&!t(n=d.call(e)))return n;if(!u&&"function"==typeof(d=e.toString)&&!t(n=d.call(e)))return n;throw TypeError("Can't convert object to primitive value")}},1263:function(e,u,d){var t=d(417),n=d(339),r=d(1264);e.exports=d(340)?Object.defineProperties:function(e,u){n(e);for(var d,a=r(u),i=a.length,c=0;i>c;)t.f(e,d=a[c++],u[d]);return e}},1264:function(e,u,d){var t=d(1265),n=d(672);e.exports=Object.keys||function(e){return t(e,n)}},1265:function(e,u,d){var t=d(341),n=d(420),r=d(1267)(!1),a=d(423)("IE_PROTO");e.exports=function(e,u){var d,i=n(e),c=0,o=[];for(d in i)d!=a&&t(i,d)&&o.push(d);for(;u.length>c;)t(i,d=u[c++])&&(~r(o,d)||o.push(d));return o}},1266:function(e,u,d){var t=d(421);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==t(e)?e.split(""):Object(e)}},1267:function(e,u,d){var t=d(420),n=d(1268),r=d(1269);e.exports=function(e){return function(u,d,a){var i,c=t(u),o=n(c.length),f=r(a,o);if(e&&d!=d){for(;o>f;)if((i=c[f++])!=i)return!0}else for(;o>f;f++)if((e||f in c)&&c[f]===d)return e||f||0;return!e&&-1}}},1268:function(e,u,d){var t=d(422),n=Math.min;e.exports=function(e){return e>0?n(t(e),9007199254740991):0}},1269:function(e,u,d){var t=d(422),n=Math.max,r=Math.min;e.exports=function(e,u){return(e=t(e))<0?n(e+u,0):r(e,u)}},127:function(e,u,d){"use strict";var t=d(197);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var n=t(d(1255)),r=t(d(1298)),a=t(d(1299)),i=t(d(1300)),c=t(d(1301)),o=t(d(1303)),f=t(d(1305)),s=t(d(1307));(0,r.default)(n.default),(0,a.default)(n.default),(0,i.default)(n.default),(0,c.default)(n.default),(0,o.default)(n.default),(0,f.default)(n.default),(0,s.default)(n.default);var l=n.default;u.default=l,e.exports=u.default},1270:function(e,u,d){var t=d(198).document;e.exports=t&&t.documentElement},1271:function(e,u,d){var t=d(1272),n=d(1277),r=d(1292);e.exports=function(e,u){return t(e)||n(e,u)||r()}},1272:function(e,u,d){var t=d(1273);e.exports=function(e){if(t(e))return e}},1273:function(e,u,d){e.exports=d(1274)},1274:function(e,u,d){d(1275),e.exports=d(199).Array.isArray},1275:function(e,u,d){var t=d(311);t(t.S,"Array",{isArray:d(1276)})},1276:function(e,u,d){var t=d(421);e.exports=Array.isArray||function(e){return"Array"==t(e)}},1277:function(e,u,d){var t=d(424);e.exports=function(e,u){var d=[],n=!0,r=!1,a=void 0;try{for(var i,c=t(e);!(n=(i=c.next()).done)&&(d.push(i.value),!u||d.length!==u);n=!0);}catch(e){r=!0,a=e}finally{try{n||null==c.return||c.return()}finally{if(r)throw a}}return d}},1278:function(e,u,d){d(1279),d(1287),e.exports=d(1289)},1279:function(e,u,d){d(1280);for(var t=d(198),n=d(312),r=d(343),a=d(217)("toStringTag"),i="CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,TextTrackList,TouchList".split(","),c=0;c<i.length;c++){var o=i[c],f=t[o],s=f&&f.prototype;s&&!s[a]&&n(s,a,o),r[o]=r.Array}},1280:function(e,u,d){"use strict";var t=d(1281),n=d(1282),r=d(343),a=d(420);e.exports=d(673)(Array,"Array",function(e,u){this._t=a(e),this._i=0,this._k=u},function(){var e=this._t,u=this._k,d=this._i++;return!e||d>=e.length?(this._t=void 0,n(1)):n(0,"keys"==u?d:"values"==u?e[d]:[d,e[d]])},"values"),r.Arguments=r.Array,t("keys"),t("values"),t("entries")},1281:function(e,u){e.exports=function(){}},1282:function(e,u){e.exports=function(e,u){return{value:u,done:!!e}}},1283:function(e,u,d){e.exports=d(312)},1284:function(e,u,d){"use strict";var t=d(668),n=d(667),r=d(674),a={};d(312)(a,d(217)("iterator"),function(){return this}),e.exports=function(e,u,d){e.prototype=t(a,{next:n(1,d)}),r(e,u+" Iterator")}},1285:function(e,u,d){var t=d(341),n=d(1286),r=d(423)("IE_PROTO"),a=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=n(e),t(e,r)?e[r]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?a:null}},1286:function(e,u,d){var t=d(342);e.exports=function(e){return Object(t(e))}},1287:function(e,u,d){"use strict";var t=d(1288)(!0);d(673)(String,"String",function(e){this._t=String(e),this._i=0},function(){var e,u=this._t,d=this._i;return d>=u.length?{value:void 0,done:!0}:(e=t(u,d),this._i+=e.length,{value:e,done:!1})})},1288:function(e,u,d){var t=d(422),n=d(342);e.exports=function(e){return function(u,d){var r,a,i=String(n(u)),c=t(d),o=i.length;return c<0||c>=o?e?"":void 0:(r=i.charCodeAt(c))<55296||r>56319||c+1===o||(a=i.charCodeAt(c+1))<56320||a>57343?e?i.charAt(c):r:e?i.slice(c,c+2):a-56320+(r-55296<<10)+65536}}},1289:function(e,u,d){var t=d(339),n=d(1290);e.exports=d(199).getIterator=function(e){var u=n(e);if("function"!=typeof u)throw TypeError(e+" is not iterable!");return t(u.call(e))}},1290:function(e,u,d){var t=d(1291),n=d(217)("iterator"),r=d(343);e.exports=d(199).getIteratorMethod=function(e){if(null!=e)return e[n]||e["@@iterator"]||r[t(e)]}},1291:function(e,u,d){var t=d(421),n=d(217)("toStringTag"),r="Arguments"==t(function(){return arguments}());e.exports=function(e){var u,d,a;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(d=function(e,u){try{return e[u]}catch(e){}}(u=Object(e),n))?d:r?t(u):"Object"==(a=t(u))&&"function"==typeof u.callee?"Arguments":a}},1292:function(e,u){e.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}},1293:function(e,u,d){e.exports=d(1294)},1294:function(e,u,d){d(1295),e.exports=d(199).parseInt},1295:function(e,u,d){var t=d(311),n=d(1296);t(t.G+t.F*(parseInt!=n),{parseInt:n})},1296:function(e,u,d){var t=d(198).parseInt,n=d(1297).trim,r=d(675),a=/^[-+]?0[xX]/;e.exports=8!==t(r+"08")||22!==t(r+"0x16")?function(e,u){var d=n(String(e),3);return t(d,u>>>0||(a.test(d)?16:10))}:t},1297:function(e,u,d){var t=d(311),n=d(342),r=d(419),a=d(675),i="["+a+"]",c=RegExp("^"+i+i+"*"),o=RegExp(i+i+"*$"),f=function(e,u,d){var n={},i=r(function(){return!!a[e]()||"​"!="​"[e]()}),c=n[e]=i?u(s):a[e];d&&(n[d]=c),t(t.P+t.F*i,"String",n)},s=f.trim=function(e,u){return e=String(n(e)),1&u&&(e=e.replace(c,"")),2&u&&(e=e.replace(o,"")),e};e.exports=f},1298:function(e,u,d){"use strict";Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;u.default=function(e){var u="xregexp",d=/(\()(?!\?)|\\([1-9]\d*)|\\[\s\S]|\[(?:[^\\\]]|\\[\s\S])*\]/g,t=e.union([/\({{([\w$]+)}}\)|{{([\w$]+)}}/,d],"g",{conjunction:"or"});function n(e){var u=/^(?:\(\?:\))*\^/,d=/\$(?:\(\?:\))*$/;return u.test(e)&&d.test(e)&&d.test(e.replace(/\\[\s\S]/g,""))?e.replace(u,"").replace(d,""):e}function r(d,t){var n=t?"x":"";return e.isRegExp(d)?d[u]&&d[u].captureNames?d:e(d.source,n):e(d,n)}function a(u){return u instanceof RegExp?u:e.escape(u)}function i(e,u,d){return e["subpattern".concat(d)]=u,e}function c(e,u,d){return e+(u<d.length-1?"{{subpattern".concat(u,"}}"):"")}e.tag=function(u){return function(d){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var o=n.map(a).reduce(i,{}),f=d.raw.map(c).join("");return e.build(f,o,u)}},e.build=function(a,i,c){var o=-1!==(c=c||"").indexOf("x"),f=/^\(\?([\w$]+)\)/.exec(a);f&&(c=e._clipDuplicates(c+f[1]));var s={};for(var l in i)if(i.hasOwnProperty(l)){var p=r(i[l],o);s[l]={pattern:n(p.source),names:p[u].captureNames||[]}}var m,h=r(a,o),b=0,g=0,y=[0],v=h[u].captureNames||[],x=h.source.replace(t,function(e,u,t,n,r){var a,i,c,o=u||t;if(o){if(!s.hasOwnProperty(o))throw new ReferenceError("Undefined property ".concat(e));u?(a=v[g],y[++g]=++b,i="(?<".concat(a||o,">")):i="(?:",m=b;var f=s[o].pattern.replace(d,function(e,u,d){if(u){if(a=s[o].names[b-m],++b,a)return"(?<".concat(a,">")}else if(d)return c=+d-1,s[o].names[c]?"\\k<".concat(s[o].names[c],">"):"\\".concat(+d+m);return e});return"".concat(i).concat(f,")")}if(n){if(a=v[g],y[++g]=++b,a)return"(?<".concat(a,">")}else if(r)return v[c=+r-1]?"\\k<".concat(v[c],">"):"\\".concat(y[+r]);return e});return e(x,c)}},e.exports=u.default},1299:function(e,u,d){"use strict";Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;u.default=function(e){function u(e,u,d,t){return{name:e,value:u,start:d,end:t}}e.matchRecursive=function(d,t,n,r,a){a=a||{};var i,c,o,f,s,l=-1!==(r=r||"").indexOf("g"),p=-1!==r.indexOf("y"),m=r.replace(/y/g,""),h=a.escapeChar,b=a.valueNames,g=[],y=0,v=0,x=0,w=0;if(t=e(t,m),n=e(n,m),h){if(h.length>1)throw new Error("Cannot use more than one escape character");h=e.escape(h),s=new RegExp("(?:".concat(h,"[\\S\\s]|(?:(?!").concat(e.union([t,n],"",{conjunction:"or"}).source,")[^").concat(h,"])+)+"),r.replace(/[^imu]+/g,""))}for(;;){if(h&&(x+=(e.exec(d,s,x,"sticky")||[""])[0].length),o=e.exec(d,t,x),f=e.exec(d,n,x),o&&f&&(o.index<=f.index?f=null:o=null),o||f)x=(v=(o||f).index)+(o||f)[0].length;else if(!y)break;if(p&&!y&&v>w)break;if(o)y||(i=v,c=x),++y;else{if(!f||!y)throw new Error("Unbalanced delimiter found in string");if(!--y&&(b?(b[0]&&i>w&&g.push(u(b[0],d.slice(w,i),w,i)),b[1]&&g.push(u(b[1],d.slice(i,c),i,c)),b[2]&&g.push(u(b[2],d.slice(c,v),c,v)),b[3]&&g.push(u(b[3],d.slice(v,x),v,x))):g.push(d.slice(c,v)),w=x,!l))break}v===x&&++x}return l&&!p&&b&&b[0]&&d.length>w&&g.push(u(b[0],d.slice(w),w,d.length)),g}},e.exports=u.default},13:function(e,u,d){var t=d(49),n=d(50),r=d(51);e.exports=function(e,u){return t(e)||n(e,u)||r()}},1300:function(e,u,d){"use strict";var t=d(197);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var n=t(d(424));
/*!
 * XRegExp Unicode Base 4.2.4
 * <xregexp.com>
 * Steven Levithan (c) 2008-present MIT License
 */u.default=function(e){var u={},d=e._dec,t=e._hex,r=e._pad4;function a(e){return e.replace(/[- _]+/g,"").toLowerCase()}function i(e){var u=/^\\[xu](.+)/.exec(e);return u?d(u[1]):e.charCodeAt("\\"===e[0]?1:0)}function c(d){var n,a,c;return u[d]["b!"]||(u[d]["b!"]=(n=u[d].bmp,a="",c=-1,e.forEach(n,/(\\x..|\\u....|\\?[\s\S])(?:-(\\x..|\\u....|\\?[\s\S]))?/,function(e){var u=i(e[1]);u>c+1&&(a+="\\u".concat(r(t(c+1))),u>c+2&&(a+="-\\u".concat(r(t(u-1))))),c=i(e[2]||e[1])}),c<65535&&(a+="\\u".concat(r(t(c+1))),c<65534&&(a+="-\\uFFFF")),a))}function o(e,d){var t=d?"a!":"a=";return u[e][t]||(u[e][t]=function(e,d){var t=u[e],n="";return t.bmp&&!t.isBmpLast&&(n="[".concat(t.bmp,"]").concat(t.astral?"|":"")),t.astral&&(n+=t.astral),t.isBmpLast&&t.bmp&&(n+="".concat(t.astral?"|":"","[").concat(t.bmp,"]")),d?"(?:(?!".concat(n,")(?:[\ud800-\udbff][\udc00-\udfff]|[\0-￿]))"):"(?:".concat(n,")")}(e,d))}e.addToken(/\\([pP])(?:{(\^?)([^}]*)}|([A-Za-z]))/,function(e,d,t){var n="P"===e[1]||!!e[2],r=-1!==t.indexOf("A"),i=a(e[4]||e[3]),f=u[i];if("P"===e[1]&&e[2])throw new SyntaxError("Invalid double negation "+e[0]);if(!u.hasOwnProperty(i))throw new SyntaxError("Unknown Unicode token "+e[0]);if(f.inverseOf){if(i=a(f.inverseOf),!u.hasOwnProperty(i))throw new ReferenceError("".concat("Unicode token missing data "+e[0]," -> ").concat(f.inverseOf));f=u[i],n=!n}if(!f.bmp&&!r)throw new SyntaxError("Astral mode required for Unicode token "+e[0]);if(r){if("class"===d)throw new SyntaxError("Astral mode does not support Unicode tokens within character classes");return o(i,n)}return"class"===d?n?c(i):f.bmp:"".concat((n?"[^":"[")+f.bmp,"]")},{scope:"all",optionalFlags:"A",leadChar:"\\"}),e.addUnicodeData=function(d){var t=!0,r=!1,i=void 0;try{for(var c,o=(0,n.default)(d);!(t=(c=o.next()).done);t=!0){var f=c.value;if(!f.name)throw new Error("Unicode token requires name");if(!(f.inverseOf||f.bmp||f.astral))throw new Error("Unicode token has no character data "+f.name);u[a(f.name)]=f,f.alias&&(u[a(f.alias)]=f)}}catch(e){r=!0,i=e}finally{try{t||null==o.return||o.return()}finally{if(r)throw i}}e.cache.flush("patterns")},e._getUnicodeProperty=function(e){var d=a(e);return u[d]}},e.exports=u.default},1301:function(e,u,d){"use strict";var t=d(197);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var n=t(d(1302));
/*!
 * XRegExp Unicode Blocks 4.2.4
 * <xregexp.com>
 * Steven Levithan (c) 2010-present MIT License
 * Unicode data by Mathias Bynens <mathiasbynens.be>
 */u.default=function(e){if(!e.addUnicodeData)throw new ReferenceError("Unicode Base must be loaded before Unicode Blocks");e.addUnicodeData(n.default)},e.exports=u.default},1302:function(e,u){e.exports=[{name:"InAdlam",astral:"\ud83a[\udd00-\udd5f]"},{name:"InAegean_Numbers",astral:"\ud800[\udd00-\udd3f]"},{name:"InAhom",astral:"\ud805[\udf00-\udf3f]"},{name:"InAlchemical_Symbols",astral:"\ud83d[\udf00-\udf7f]"},{name:"InAlphabetic_Presentation_Forms",bmp:"ﬀ-ﭏ"},{name:"InAnatolian_Hieroglyphs",astral:"\ud811[\udc00-\ude7f]"},{name:"InAncient_Greek_Musical_Notation",astral:"\ud834[\ude00-\ude4f]"},{name:"InAncient_Greek_Numbers",astral:"\ud800[\udd40-\udd8f]"},{name:"InAncient_Symbols",astral:"\ud800[\udd90-\uddcf]"},{name:"InArabic",bmp:"؀-ۿ"},{name:"InArabic_Extended_A",bmp:"ࢠ-ࣿ"},{name:"InArabic_Mathematical_Alphabetic_Symbols",astral:"\ud83b[\ude00-\udeff]"},{name:"InArabic_Presentation_Forms_A",bmp:"ﭐ-﷿"},{name:"InArabic_Presentation_Forms_B",bmp:"ﹰ-\ufeff"},{name:"InArabic_Supplement",bmp:"ݐ-ݿ"},{name:"InArmenian",bmp:"԰-֏"},{name:"InArrows",bmp:"←-⇿"},{name:"InAvestan",astral:"\ud802[\udf00-\udf3f]"},{name:"InBalinese",bmp:"ᬀ-᭿"},{name:"InBamum",bmp:"ꚠ-꛿"},{name:"InBamum_Supplement",astral:"\ud81a[\udc00-\ude3f]"},{name:"InBasic_Latin",bmp:"\0-"},{name:"InBassa_Vah",astral:"\ud81a[\uded0-\udeff]"},{name:"InBatak",bmp:"ᯀ-᯿"},{name:"InBengali",bmp:"ঀ-৿"},{name:"InBhaiksuki",astral:"\ud807[\udc00-\udc6f]"},{name:"InBlock_Elements",bmp:"▀-▟"},{name:"InBopomofo",bmp:"㄀-ㄯ"},{name:"InBopomofo_Extended",bmp:"ㆠ-ㆿ"},{name:"InBox_Drawing",bmp:"─-╿"},{name:"InBrahmi",astral:"\ud804[\udc00-\udc7f]"},{name:"InBraille_Patterns",bmp:"⠀-⣿"},{name:"InBuginese",bmp:"ᨀ-᨟"},{name:"InBuhid",bmp:"ᝀ-᝟"},{name:"InByzantine_Musical_Symbols",astral:"\ud834[\udc00-\udcff]"},{name:"InCJK_Compatibility",bmp:"㌀-㏿"},{name:"InCJK_Compatibility_Forms",bmp:"︰-﹏"},{name:"InCJK_Compatibility_Ideographs",bmp:"豈-﫿"},{name:"InCJK_Compatibility_Ideographs_Supplement",astral:"\ud87e[\udc00-\ude1f]"},{name:"InCJK_Radicals_Supplement",bmp:"⺀-⻿"},{name:"InCJK_Strokes",bmp:"㇀-㇯"},{name:"InCJK_Symbols_And_Punctuation",bmp:"　-〿"},{name:"InCJK_Unified_Ideographs",bmp:"一-鿿"},{name:"InCJK_Unified_Ideographs_Extension_A",bmp:"㐀-䶿"},{name:"InCJK_Unified_Ideographs_Extension_B",astral:"[\ud840-\ud868][\udc00-\udfff]|\ud869[\udc00-\udedf]"},{name:"InCJK_Unified_Ideographs_Extension_C",astral:"\ud869[\udf00-\udfff]|[\ud86a-\ud86c][\udc00-\udfff]|\ud86d[\udc00-\udf3f]"},{name:"InCJK_Unified_Ideographs_Extension_D",astral:"\ud86d[\udf40-\udfff]|\ud86e[\udc00-\udc1f]"},{name:"InCJK_Unified_Ideographs_Extension_E",astral:"\ud86e[\udc20-\udfff]|[\ud86f-\ud872][\udc00-\udfff]|\ud873[\udc00-\udeaf]"},{name:"InCJK_Unified_Ideographs_Extension_F",astral:"\ud873[\udeb0-\udfff]|[\ud874-\ud879][\udc00-\udfff]|\ud87a[\udc00-\udfef]"},{name:"InCarian",astral:"\ud800[\udea0-\udedf]"},{name:"InCaucasian_Albanian",astral:"\ud801[\udd30-\udd6f]"},{name:"InChakma",astral:"\ud804[\udd00-\udd4f]"},{name:"InCham",bmp:"ꨀ-꩟"},{name:"InCherokee",bmp:"Ꭰ-᏿"},{name:"InCherokee_Supplement",bmp:"ꭰ-ꮿ"},{name:"InChess_Symbols",astral:"\ud83e[\ude00-\ude6f]"},{name:"InCombining_Diacritical_Marks",bmp:"̀-ͯ"},{name:"InCombining_Diacritical_Marks_Extended",bmp:"᪰-᫿"},{name:"InCombining_Diacritical_Marks_For_Symbols",bmp:"⃐-⃿"},{name:"InCombining_Diacritical_Marks_Supplement",bmp:"᷀-᷿"},{name:"InCombining_Half_Marks",bmp:"︠-︯"},{name:"InCommon_Indic_Number_Forms",bmp:"꠰-꠿"},{name:"InControl_Pictures",bmp:"␀-␿"},{name:"InCoptic",bmp:"Ⲁ-⳿"},{name:"InCoptic_Epact_Numbers",astral:"\ud800[\udee0-\udeff]"},{name:"InCounting_Rod_Numerals",astral:"\ud834[\udf60-\udf7f]"},{name:"InCuneiform",astral:"\ud808[\udc00-\udfff]"},{name:"InCuneiform_Numbers_And_Punctuation",astral:"\ud809[\udc00-\udc7f]"},{name:"InCurrency_Symbols",bmp:"₠-⃏"},{name:"InCypriot_Syllabary",astral:"\ud802[\udc00-\udc3f]"},{name:"InCyrillic",bmp:"Ѐ-ӿ"},{name:"InCyrillic_Extended_A",bmp:"ⷠ-ⷿ"},{name:"InCyrillic_Extended_B",bmp:"Ꙁ-ꚟ"},{name:"InCyrillic_Extended_C",bmp:"ᲀ-᲏"},{name:"InCyrillic_Supplement",bmp:"Ԁ-ԯ"},{name:"InDeseret",astral:"\ud801[\udc00-\udc4f]"},{name:"InDevanagari",bmp:"ऀ-ॿ"},{name:"InDevanagari_Extended",bmp:"꣠-ꣿ"},{name:"InDingbats",bmp:"✀-➿"},{name:"InDogra",astral:"\ud806[\udc00-\udc4f]"},{name:"InDomino_Tiles",astral:"\ud83c[\udc30-\udc9f]"},{name:"InDuployan",astral:"\ud82f[\udc00-\udc9f]"},{name:"InEarly_Dynastic_Cuneiform",astral:"\ud809[\udc80-\udd4f]"},{name:"InEgyptian_Hieroglyphs",astral:"\ud80c[\udc00-\udfff]|\ud80d[\udc00-\udc2f]"},{name:"InElbasan",astral:"\ud801[\udd00-\udd2f]"},{name:"InEmoticons",astral:"\ud83d[\ude00-\ude4f]"},{name:"InEnclosed_Alphanumeric_Supplement",astral:"\ud83c[\udd00-\uddff]"},{name:"InEnclosed_Alphanumerics",bmp:"①-⓿"},{name:"InEnclosed_CJK_Letters_And_Months",bmp:"㈀-㋿"},{name:"InEnclosed_Ideographic_Supplement",astral:"\ud83c[\ude00-\udeff]"},{name:"InEthiopic",bmp:"ሀ-፿"},{name:"InEthiopic_Extended",bmp:"ⶀ-⷟"},{name:"InEthiopic_Extended_A",bmp:"꬀-꬯"},{name:"InEthiopic_Supplement",bmp:"ᎀ-᎟"},{name:"InGeneral_Punctuation",bmp:" -⁯"},{name:"InGeometric_Shapes",bmp:"■-◿"},{name:"InGeometric_Shapes_Extended",astral:"\ud83d[\udf80-\udfff]"},{name:"InGeorgian",bmp:"Ⴀ-ჿ"},{name:"InGeorgian_Extended",bmp:"Ა-Ჿ"},{name:"InGeorgian_Supplement",bmp:"ⴀ-⴯"},{name:"InGlagolitic",bmp:"Ⰰ-ⱟ"},{name:"InGlagolitic_Supplement",astral:"\ud838[\udc00-\udc2f]"},{name:"InGothic",astral:"\ud800[\udf30-\udf4f]"},{name:"InGrantha",astral:"\ud804[\udf00-\udf7f]"},{name:"InGreek_And_Coptic",bmp:"Ͱ-Ͽ"},{name:"InGreek_Extended",bmp:"ἀ-῿"},{name:"InGujarati",bmp:"઀-૿"},{name:"InGunjala_Gondi",astral:"\ud807[\udd60-\uddaf]"},{name:"InGurmukhi",bmp:"਀-੿"},{name:"InHalfwidth_And_Fullwidth_Forms",bmp:"＀-￯"},{name:"InHangul_Compatibility_Jamo",bmp:"㄰-㆏"},{name:"InHangul_Jamo",bmp:"ᄀ-ᇿ"},{name:"InHangul_Jamo_Extended_A",bmp:"ꥠ-꥿"},{name:"InHangul_Jamo_Extended_B",bmp:"ힰ-퟿"},{name:"InHangul_Syllables",bmp:"가-힯"},{name:"InHanifi_Rohingya",astral:"\ud803[\udd00-\udd3f]"},{name:"InHanunoo",bmp:"ᜠ-᜿"},{name:"InHatran",astral:"\ud802[\udce0-\udcff]"},{name:"InHebrew",bmp:"֐-׿"},{name:"InHigh_Private_Use_Surrogates",bmp:"\udb80-\udbff"},{name:"InHigh_Surrogates",bmp:"\ud800-\udb7f"},{name:"InHiragana",bmp:"぀-ゟ"},{name:"InIPA_Extensions",bmp:"ɐ-ʯ"},{name:"InIdeographic_Description_Characters",bmp:"⿰-⿿"},{name:"InIdeographic_Symbols_And_Punctuation",astral:"\ud81b[\udfe0-\udfff]"},{name:"InImperial_Aramaic",astral:"\ud802[\udc40-\udc5f]"},{name:"InIndic_Siyaq_Numbers",astral:"\ud83b[\udc70-\udcbf]"},{name:"InInscriptional_Pahlavi",astral:"\ud802[\udf60-\udf7f]"},{name:"InInscriptional_Parthian",astral:"\ud802[\udf40-\udf5f]"},{name:"InJavanese",bmp:"ꦀ-꧟"},{name:"InKaithi",astral:"\ud804[\udc80-\udccf]"},{name:"InKana_Extended_A",astral:"\ud82c[\udd00-\udd2f]"},{name:"InKana_Supplement",astral:"\ud82c[\udc00-\udcff]"},{name:"InKanbun",bmp:"㆐-㆟"},{name:"InKangxi_Radicals",bmp:"⼀-⿟"},{name:"InKannada",bmp:"ಀ-೿"},{name:"InKatakana",bmp:"゠-ヿ"},{name:"InKatakana_Phonetic_Extensions",bmp:"ㇰ-ㇿ"},{name:"InKayah_Li",bmp:"꤀-꤯"},{name:"InKharoshthi",astral:"\ud802[\ude00-\ude5f]"},{name:"InKhmer",bmp:"ក-៿"},{name:"InKhmer_Symbols",bmp:"᧠-᧿"},{name:"InKhojki",astral:"\ud804[\ude00-\ude4f]"},{name:"InKhudawadi",astral:"\ud804[\udeb0-\udeff]"},{name:"InLao",bmp:"຀-໿"},{name:"InLatin_1_Supplement",bmp:"-ÿ"},{name:"InLatin_Extended_A",bmp:"Ā-ſ"},{name:"InLatin_Extended_Additional",bmp:"Ḁ-ỿ"},{name:"InLatin_Extended_B",bmp:"ƀ-ɏ"},{name:"InLatin_Extended_C",bmp:"Ⱡ-Ɀ"},{name:"InLatin_Extended_D",bmp:"꜠-ꟿ"},{name:"InLatin_Extended_E",bmp:"ꬰ-꭯"},{name:"InLepcha",bmp:"ᰀ-ᱏ"},{name:"InLetterlike_Symbols",bmp:"℀-⅏"},{name:"InLimbu",bmp:"ᤀ-᥏"},{name:"InLinear_A",astral:"\ud801[\ude00-\udf7f]"},{name:"InLinear_B_Ideograms",astral:"\ud800[\udc80-\udcff]"},{name:"InLinear_B_Syllabary",astral:"\ud800[\udc00-\udc7f]"},{name:"InLisu",bmp:"ꓐ-꓿"},{name:"InLow_Surrogates",bmp:"\udc00-\udfff"},{name:"InLycian",astral:"\ud800[\ude80-\ude9f]"},{name:"InLydian",astral:"\ud802[\udd20-\udd3f]"},{name:"InMahajani",astral:"\ud804[\udd50-\udd7f]"},{name:"InMahjong_Tiles",astral:"\ud83c[\udc00-\udc2f]"},{name:"InMakasar",astral:"\ud807[\udee0-\udeff]"},{name:"InMalayalam",bmp:"ഀ-ൿ"},{name:"InMandaic",bmp:"ࡀ-࡟"},{name:"InManichaean",astral:"\ud802[\udec0-\udeff]"},{name:"InMarchen",astral:"\ud807[\udc70-\udcbf]"},{name:"InMasaram_Gondi",astral:"\ud807[\udd00-\udd5f]"},{name:"InMathematical_Alphanumeric_Symbols",astral:"\ud835[\udc00-\udfff]"},{name:"InMathematical_Operators",bmp:"∀-⋿"},{name:"InMayan_Numerals",astral:"\ud834[\udee0-\udeff]"},{name:"InMedefaidrin",astral:"\ud81b[\ude40-\ude9f]"},{name:"InMeetei_Mayek",bmp:"ꯀ-꯿"},{name:"InMeetei_Mayek_Extensions",bmp:"ꫠ-꫿"},{name:"InMende_Kikakui",astral:"\ud83a[\udc00-\udcdf]"},{name:"InMeroitic_Cursive",astral:"\ud802[\udda0-\uddff]"},{name:"InMeroitic_Hieroglyphs",astral:"\ud802[\udd80-\udd9f]"},{name:"InMiao",astral:"\ud81b[\udf00-\udf9f]"},{name:"InMiscellaneous_Mathematical_Symbols_A",bmp:"⟀-⟯"},{name:"InMiscellaneous_Mathematical_Symbols_B",bmp:"⦀-⧿"},{name:"InMiscellaneous_Symbols",bmp:"☀-⛿"},{name:"InMiscellaneous_Symbols_And_Arrows",bmp:"⬀-⯿"},{name:"InMiscellaneous_Symbols_And_Pictographs",astral:"\ud83c[\udf00-\udfff]|\ud83d[\udc00-\uddff]"},{name:"InMiscellaneous_Technical",bmp:"⌀-⏿"},{name:"InModi",astral:"\ud805[\ude00-\ude5f]"},{name:"InModifier_Tone_Letters",bmp:"꜀-ꜟ"},{name:"InMongolian",bmp:"᠀-᢯"},{name:"InMongolian_Supplement",astral:"\ud805[\ude60-\ude7f]"},{name:"InMro",astral:"\ud81a[\ude40-\ude6f]"},{name:"InMultani",astral:"\ud804[\ude80-\udeaf]"},{name:"InMusical_Symbols",astral:"\ud834[\udd00-\uddff]"},{name:"InMyanmar",bmp:"က-႟"},{name:"InMyanmar_Extended_A",bmp:"ꩠ-ꩿ"},{name:"InMyanmar_Extended_B",bmp:"ꧠ-꧿"},{name:"InNKo",bmp:"߀-߿"},{name:"InNabataean",astral:"\ud802[\udc80-\udcaf]"},{name:"InNew_Tai_Lue",bmp:"ᦀ-᧟"},{name:"InNewa",astral:"\ud805[\udc00-\udc7f]"},{name:"InNumber_Forms",bmp:"⅐-↏"},{name:"InNushu",astral:"\ud82c[\udd70-\udeff]"},{name:"InOgham",bmp:" -᚟"},{name:"InOl_Chiki",bmp:"᱐-᱿"},{name:"InOld_Hungarian",astral:"\ud803[\udc80-\udcff]"},{name:"InOld_Italic",astral:"\ud800[\udf00-\udf2f]"},{name:"InOld_North_Arabian",astral:"\ud802[\ude80-\ude9f]"},{name:"InOld_Permic",astral:"\ud800[\udf50-\udf7f]"},{name:"InOld_Persian",astral:"\ud800[\udfa0-\udfdf]"},{name:"InOld_Sogdian",astral:"\ud803[\udf00-\udf2f]"},{name:"InOld_South_Arabian",astral:"\ud802[\ude60-\ude7f]"},{name:"InOld_Turkic",astral:"\ud803[\udc00-\udc4f]"},{name:"InOptical_Character_Recognition",bmp:"⑀-⑟"},{name:"InOriya",bmp:"଀-୿"},{name:"InOrnamental_Dingbats",astral:"\ud83d[\ude50-\ude7f]"},{name:"InOsage",astral:"\ud801[\udcb0-\udcff]"},{name:"InOsmanya",astral:"\ud801[\udc80-\udcaf]"},{name:"InPahawh_Hmong",astral:"\ud81a[\udf00-\udf8f]"},{name:"InPalmyrene",astral:"\ud802[\udc60-\udc7f]"},{name:"InPau_Cin_Hau",astral:"\ud806[\udec0-\udeff]"},{name:"InPhags_Pa",bmp:"ꡀ-꡿"},{name:"InPhaistos_Disc",astral:"\ud800[\uddd0-\uddff]"},{name:"InPhoenician",astral:"\ud802[\udd00-\udd1f]"},{name:"InPhonetic_Extensions",bmp:"ᴀ-ᵿ"},{name:"InPhonetic_Extensions_Supplement",bmp:"ᶀ-ᶿ"},{name:"InPlaying_Cards",astral:"\ud83c[\udca0-\udcff]"},{name:"InPrivate_Use_Area",bmp:"-"},{name:"InPsalter_Pahlavi",astral:"\ud802[\udf80-\udfaf]"},{name:"InRejang",bmp:"ꤰ-꥟"},{name:"InRumi_Numeral_Symbols",astral:"\ud803[\ude60-\ude7f]"},{name:"InRunic",bmp:"ᚠ-᛿"},{name:"InSamaritan",bmp:"ࠀ-࠿"},{name:"InSaurashtra",bmp:"ꢀ-꣟"},{name:"InSharada",astral:"\ud804[\udd80-\udddf]"},{name:"InShavian",astral:"\ud801[\udc50-\udc7f]"},{name:"InShorthand_Format_Controls",astral:"\ud82f[\udca0-\udcaf]"},{name:"InSiddham",astral:"\ud805[\udd80-\uddff]"},{name:"InSinhala",bmp:"඀-෿"},{name:"InSinhala_Archaic_Numbers",astral:"\ud804[\udde0-\uddff]"},{name:"InSmall_Form_Variants",bmp:"﹐-﹯"},{name:"InSogdian",astral:"\ud803[\udf30-\udf6f]"},{name:"InSora_Sompeng",astral:"\ud804[\udcd0-\udcff]"},{name:"InSoyombo",astral:"\ud806[\ude50-\udeaf]"},{name:"InSpacing_Modifier_Letters",bmp:"ʰ-˿"},{name:"InSpecials",bmp:"￰-￿"},{name:"InSundanese",bmp:"ᮀ-ᮿ"},{name:"InSundanese_Supplement",bmp:"᳀-᳏"},{name:"InSuperscripts_And_Subscripts",bmp:"⁰-₟"},{name:"InSupplemental_Arrows_A",bmp:"⟰-⟿"},{name:"InSupplemental_Arrows_B",bmp:"⤀-⥿"},{name:"InSupplemental_Arrows_C",astral:"\ud83e[\udc00-\udcff]"},{name:"InSupplemental_Mathematical_Operators",bmp:"⨀-⫿"},{name:"InSupplemental_Punctuation",bmp:"⸀-⹿"},{name:"InSupplemental_Symbols_And_Pictographs",astral:"\ud83e[\udd00-\uddff]"},{name:"InSupplementary_Private_Use_Area_A",astral:"[\udb80-\udbbf][\udc00-\udfff]"},{name:"InSupplementary_Private_Use_Area_B",astral:"[\udbc0-\udbff][\udc00-\udfff]"},{name:"InSutton_SignWriting",astral:"\ud836[\udc00-\udeaf]"},{name:"InSyloti_Nagri",bmp:"ꠀ-꠯"},{name:"InSyriac",bmp:"܀-ݏ"},{name:"InSyriac_Supplement",bmp:"ࡠ-࡯"},{name:"InTagalog",bmp:"ᜀ-ᜟ"},{name:"InTagbanwa",bmp:"ᝠ-᝿"},{name:"InTags",astral:"\udb40[\udc00-\udc7f]"},{name:"InTai_Le",bmp:"ᥐ-᥿"},{name:"InTai_Tham",bmp:"ᨠ-᪯"},{name:"InTai_Viet",bmp:"ꪀ-꫟"},{name:"InTai_Xuan_Jing_Symbols",astral:"\ud834[\udf00-\udf5f]"},{name:"InTakri",astral:"\ud805[\ude80-\udecf]"},{name:"InTamil",bmp:"஀-௿"},{name:"InTangut",astral:"[\ud81c-\ud821][\udc00-\udfff]"},{name:"InTangut_Components",astral:"\ud822[\udc00-\udeff]"},{name:"InTelugu",bmp:"ఀ-౿"},{name:"InThaana",bmp:"ހ-޿"},{name:"InThai",bmp:"฀-๿"},{name:"InTibetan",bmp:"ༀ-࿿"},{name:"InTifinagh",bmp:"ⴰ-⵿"},{name:"InTirhuta",astral:"\ud805[\udc80-\udcdf]"},{name:"InTransport_And_Map_Symbols",astral:"\ud83d[\ude80-\udeff]"},{name:"InUgaritic",astral:"\ud800[\udf80-\udf9f]"},{name:"InUnified_Canadian_Aboriginal_Syllabics",bmp:"᐀-ᙿ"},{name:"InUnified_Canadian_Aboriginal_Syllabics_Extended",bmp:"ᢰ-᣿"},{name:"InVai",bmp:"ꔀ-꘿"},{name:"InVariation_Selectors",bmp:"︀-️"},{name:"InVariation_Selectors_Supplement",astral:"\udb40[\udd00-\uddef]"},{name:"InVedic_Extensions",bmp:"᳐-᳿"},{name:"InVertical_Forms",bmp:"︐-︟"},{name:"InWarang_Citi",astral:"\ud806[\udca0-\udcff]"},{name:"InYi_Radicals",bmp:"꒐-꓏"},{name:"InYi_Syllables",bmp:"ꀀ-꒏"},{name:"InYijing_Hexagram_Symbols",bmp:"䷀-䷿"},{name:"InZanabazar_Square",astral:"\ud806[\ude00-\ude4f]"}]},1303:function(e,u,d){"use strict";var t=d(197);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var n=t(d(1304));
/*!
 * XRegExp Unicode Categories 4.2.4
 * <xregexp.com>
 * Steven Levithan (c) 2010-present MIT License
 * Unicode data by Mathias Bynens <mathiasbynens.be>
 */u.default=function(e){if(!e.addUnicodeData)throw new ReferenceError("Unicode Base must be loaded before Unicode Categories");e.addUnicodeData(n.default)},e.exports=u.default},1304:function(e,u){e.exports=[{name:"C",alias:"Other",isBmpLast:!0,bmp:"\0--­͸͹΀-΃΋΍΢԰՗՘֋֌֐׈-׏׫-׮׵-؅؜؝۝܎܏݋݌޲-޿߻߼࠮࠯࠿࡜࡝࡟࡫-࢟ࢵࢾ-࣒࣢঄঍঎঑঒঩঱঳-঵঺঻৅৆৉৊৏-৖৘-৛৞৤৥৿਀਄਋-਎਑਒਩਱਴਷਺਻਽੃-੆੉੊੎-੐੒-੘੝੟-੥੷-઀઄઎઒઩઱઴઺઻૆૊૎૏૑-૟૤૥૲-૸଀଄଍଎଑଒଩଱଴଺଻୅୆୉୊୎-୕୘-୛୞୤୥୸-஁஄஋-஍஑஖-஘஛஝஠-஢஥-஧஫-஭஺-஽௃-௅௉௎௏௑-௖௘-௥௻-௿఍఑఩఺-఼౅౉౎-౔౗౛-౟౤౥౰-౷಍಑಩಴಺಻೅೉೎-೔೗-ೝ೟೤೥೰ೳ-೿ഄ഍഑൅൉൐-൓൤൥඀ඁ඄඗-඙඲඼඾඿෇-෉෋-෎෕෗෠-෥෰෱෵-฀฻-฾๜-຀຃຅ຆຉ຋ຌຎ-ຓຘຠ຤຦ຨຩຬ຺຾຿໅໇໎໏໚໛໠-໿཈཭-཰྘྽࿍࿛-࿿჆჈-჌჎჏቉቎቏቗቙቞቟኉኎኏኱኶኷኿዁዆዇዗጑጖጗፛፜፽-፿᎚-᎟᏶᏷᏾᏿᚝-᚟᛹-᛿ᜍ᜕-ᜟ᜷-᜿᝔-᝟᝭᝱᝴-᝿៞៟៪-៯៺-៿᠎᠏᠚-᠟᡹-᡿᢫-᢯᣶-᣿᤟᤬-᤯᤼-᤿᥁-᥃᥮᥯᥵-᥿᦬-᦯᧊-᧏᧛-᧝᨜᨝᩟᩽᩾᪊-᪏᪚-᪟᪮᪯ᪿ-᫿ᭌ-᭏᭽-᭿᯴-᯻᰸-᰺᱊-᱌Ᲊ-᲏᲻᲼᳈-᳏ᳺ-᳿᷺἖἗἞἟὆὇὎὏὘὚὜὞὾὿᾵῅῔῕῜῰῱῵῿​-‏‪-‮⁠-⁯⁲⁳₏₝-₟⃀-⃏⃱-⃿↌-↏␧-␿⑋-⑟⭴⭵⮖⮗⯉⯿Ⱟⱟ⳴-⳸⴦⴨-⴬⴮⴯⵨-⵮⵱-⵾⶗-⶟⶧⶯⶷⶿⷇⷏⷗⷟⹏-⹿⺚⻴-⻿⿖-⿯⿼-⿿぀゗゘㄀-㄄㄰㆏ㆻ-ㆿ㇤-㇯㈟㋿䶶-䶿鿰-鿿꒍-꒏꓇-꓏꘬-꘿꛸-꛿Ꞻ-ꟶ꠬-꠯꠺-꠿꡸-꡿꣆-꣍꣚-꣟꥔-꥞꥽-꥿꧎꧚-꧝꧿꨷-꨿꩎꩏꩚꩛꫃-꫚꫷-꬀꬇꬈꬏꬐꬗-꬟꬧꬯ꭦ-꭯꯮꯯꯺-꯿힤-힯퟇-퟊퟼-﩮﩯﫚-﫿﬇-﬒﬘-﬜﬷﬽﬿﭂﭅﯂-﯒﵀-﵏﶐﶑﷈-﷯﷾﷿︚-︟﹓﹧﹬-﹯﹵﻽-＀﾿-￁￈￉￐￑￘￙￝-￟￧￯-￻￾￿",astral:"\ud800[\udc0c\udc27\udc3b\udc3e\udc4e\udc4f\udc5e-\udc7f\udcfb-\udcff\udd03-\udd06\udd34-\udd36\udd8f\udd9c-\udd9f\udda1-\uddcf\uddfe-\ude7f\ude9d-\ude9f\uded1-\udedf\udefc-\udeff\udf24-\udf2c\udf4b-\udf4f\udf7b-\udf7f\udf9e\udfc4-\udfc7\udfd6-\udfff]|\ud801[\udc9e\udc9f\udcaa-\udcaf\udcd4-\udcd7\udcfc-\udcff\udd28-\udd2f\udd64-\udd6e\udd70-\uddff\udf37-\udf3f\udf56-\udf5f\udf68-\udfff]|\ud802[\udc06\udc07\udc09\udc36\udc39-\udc3b\udc3d\udc3e\udc56\udc9f-\udca6\udcb0-\udcdf\udcf3\udcf6-\udcfa\udd1c-\udd1e\udd3a-\udd3e\udd40-\udd7f\uddb8-\uddbb\uddd0\uddd1\ude04\ude07-\ude0b\ude14\ude18\ude36\ude37\ude3b-\ude3e\ude49-\ude4f\ude59-\ude5f\udea0-\udebf\udee7-\udeea\udef7-\udeff\udf36-\udf38\udf56\udf57\udf73-\udf77\udf92-\udf98\udf9d-\udfa8\udfb0-\udfff]|\ud803[\udc49-\udc7f\udcb3-\udcbf\udcf3-\udcf9\udd28-\udd2f\udd3a-\ude5f\ude7f-\udeff\udf28-\udf2f\udf5a-\udfff]|\ud804[\udc4e-\udc51\udc70-\udc7e\udcbd\udcc2-\udccf\udce9-\udcef\udcfa-\udcff\udd35\udd47-\udd4f\udd77-\udd7f\uddce\uddcf\udde0\uddf5-\uddff\ude12\ude3f-\ude7f\ude87\ude89\ude8e\ude9e\udeaa-\udeaf\udeeb-\udeef\udefa-\udeff\udf04\udf0d\udf0e\udf11\udf12\udf29\udf31\udf34\udf3a\udf45\udf46\udf49\udf4a\udf4e\udf4f\udf51-\udf56\udf58-\udf5c\udf64\udf65\udf6d-\udf6f\udf75-\udfff]|\ud805[\udc5a\udc5c\udc5f-\udc7f\udcc8-\udccf\udcda-\udd7f\uddb6\uddb7\uddde-\uddff\ude45-\ude4f\ude5a-\ude5f\ude6d-\ude7f\udeb8-\udebf\udeca-\udeff\udf1b\udf1c\udf2c-\udf2f\udf40-\udfff]|\ud806[\udc3c-\udc9f\udcf3-\udcfe\udd00-\uddff\ude48-\ude4f\ude84\ude85\udea3-\udebf\udef9-\udfff]|\ud807[\udc09\udc37\udc46-\udc4f\udc6d-\udc6f\udc90\udc91\udca8\udcb7-\udcff\udd07\udd0a\udd37-\udd39\udd3b\udd3e\udd48-\udd4f\udd5a-\udd5f\udd66\udd69\udd8f\udd92\udd99-\udd9f\uddaa-\udedf\udef9-\udfff]|\ud808[\udf9a-\udfff]|\ud809[\udc6f\udc75-\udc7f\udd44-\udfff]|[\ud80a\ud80b\ud80e-\ud810\ud812-\ud819\ud823-\ud82b\ud82d\ud82e\ud830-\ud833\ud837\ud839\ud83f\ud87b-\ud87d\ud87f-\udb3f\udb41-\udbff][\udc00-\udfff]|\ud80d[\udc2f-\udfff]|\ud811[\ude47-\udfff]|\ud81a[\ude39-\ude3f\ude5f\ude6a-\ude6d\ude70-\udecf\udeee\udeef\udef6-\udeff\udf46-\udf4f\udf5a\udf62\udf78-\udf7c\udf90-\udfff]|\ud81b[\udc00-\ude3f\ude9b-\udeff\udf45-\udf4f\udf7f-\udf8e\udfa0-\udfdf\udfe2-\udfff]|\ud821[\udff2-\udfff]|\ud822[\udef3-\udfff]|\ud82c[\udd1f-\udd6f\udefc-\udfff]|\ud82f[\udc6b-\udc6f\udc7d-\udc7f\udc89-\udc8f\udc9a\udc9b\udca0-\udfff]|\ud834[\udcf6-\udcff\udd27\udd28\udd73-\udd7a\udde9-\uddff\ude46-\udedf\udef4-\udeff\udf57-\udf5f\udf79-\udfff]|\ud835[\udc55\udc9d\udca0\udca1\udca3\udca4\udca7\udca8\udcad\udcba\udcbc\udcc4\udd06\udd0b\udd0c\udd15\udd1d\udd3a\udd3f\udd45\udd47-\udd49\udd51\udea6\udea7\udfcc\udfcd]|\ud836[\ude8c-\ude9a\udea0\udeb0-\udfff]|\ud838[\udc07\udc19\udc1a\udc22\udc25\udc2b-\udfff]|\ud83a[\udcc5\udcc6\udcd7-\udcff\udd4b-\udd4f\udd5a-\udd5d\udd60-\udfff]|\ud83b[\udc00-\udc70\udcb5-\uddff\ude04\ude20\ude23\ude25\ude26\ude28\ude33\ude38\ude3a\ude3c-\ude41\ude43-\ude46\ude48\ude4a\ude4c\ude50\ude53\ude55\ude56\ude58\ude5a\ude5c\ude5e\ude60\ude63\ude65\ude66\ude6b\ude73\ude78\ude7d\ude7f\ude8a\ude9c-\udea0\udea4\udeaa\udebc-\udeef\udef2-\udfff]|\ud83c[\udc2c-\udc2f\udc94-\udc9f\udcaf\udcb0\udcc0\udcd0\udcf6-\udcff\udd0d-\udd0f\udd6c-\udd6f\uddad-\udde5\ude03-\ude0f\ude3c-\ude3f\ude49-\ude4f\ude52-\ude5f\ude66-\udeff]|\ud83d[\uded5-\udedf\udeed-\udeef\udefa-\udeff\udf74-\udf7f\udfd9-\udfff]|\ud83e[\udc0c-\udc0f\udc48-\udc4f\udc5a-\udc5f\udc88-\udc8f\udcae-\udcff\udd0c-\udd0f\udd3f\udd71\udd72\udd77-\udd79\udd7b\udda3-\uddaf\uddba-\uddbf\uddc3-\uddcf\ude00-\ude5f\ude6e-\udfff]|\ud869[\uded7-\udeff]|\ud86d[\udf35-\udf3f]|\ud86e[\udc1e\udc1f]|\ud873[\udea2-\udeaf]|\ud87a[\udfe1-\udfff]|\ud87e[\ude1e-\udfff]|\udb40[\udc00-\udcff\uddf0-\udfff]"},{name:"Cc",alias:"Control",bmp:"\0--"},{name:"Cf",alias:"Format",bmp:"­؀-؅؜۝܏࣢᠎​-‏‪-‮⁠-⁤⁦-⁯\ufeff￹-￻",astral:"\ud804[\udcbd\udccd]|\ud82f[\udca0-\udca3]|\ud834[\udd73-\udd7a]|\udb40[\udc01\udc20-\udc7f]"},{name:"Cn",alias:"Unassigned",bmp:"͸͹΀-΃΋΍΢԰՗՘֋֌֐׈-׏׫-׮׵-׿؝܎݋݌޲-޿߻߼࠮࠯࠿࡜࡝࡟࡫-࢟ࢵࢾ-࣒঄঍঎঑঒঩঱঳-঵঺঻৅৆৉৊৏-৖৘-৛৞৤৥৿਀਄਋-਎਑਒਩਱਴਷਺਻਽੃-੆੉੊੎-੐੒-੘੝੟-੥੷-઀઄઎઒઩઱઴઺઻૆૊૎૏૑-૟૤૥૲-૸଀଄଍଎଑଒଩଱଴଺଻୅୆୉୊୎-୕୘-୛୞୤୥୸-஁஄஋-஍஑஖-஘஛஝஠-஢஥-஧஫-஭஺-஽௃-௅௉௎௏௑-௖௘-௥௻-௿఍఑఩఺-఼౅౉౎-౔౗౛-౟౤౥౰-౷಍಑಩಴಺಻೅೉೎-೔೗-ೝ೟೤೥೰ೳ-೿ഄ഍഑൅൉൐-൓൤൥඀ඁ඄඗-඙඲඼඾඿෇-෉෋-෎෕෗෠-෥෰෱෵-฀฻-฾๜-຀຃຅ຆຉ຋ຌຎ-ຓຘຠ຤຦ຨຩຬ຺຾຿໅໇໎໏໚໛໠-໿཈཭-཰྘྽࿍࿛-࿿჆჈-჌჎჏቉቎቏቗቙቞቟኉኎኏኱኶኷኿዁዆዇዗጑጖጗፛፜፽-፿᎚-᎟᏶᏷᏾᏿᚝-᚟᛹-᛿ᜍ᜕-ᜟ᜷-᜿᝔-᝟᝭᝱᝴-᝿៞៟៪-៯៺-៿᠏᠚-᠟᡹-᡿᢫-᢯᣶-᣿᤟᤬-᤯᤼-᤿᥁-᥃᥮᥯᥵-᥿᦬-᦯᧊-᧏᧛-᧝᨜᨝᩟᩽᩾᪊-᪏᪚-᪟᪮᪯ᪿ-᫿ᭌ-᭏᭽-᭿᯴-᯻᰸-᰺᱊-᱌Ᲊ-᲏᲻᲼᳈-᳏ᳺ-᳿᷺἖἗἞἟὆὇὎὏὘὚὜὞὾὿᾵῅῔῕῜῰῱῵῿⁥⁲⁳₏₝-₟⃀-⃏⃱-⃿↌-↏␧-␿⑋-⑟⭴⭵⮖⮗⯉⯿Ⱟⱟ⳴-⳸⴦⴨-⴬⴮⴯⵨-⵮⵱-⵾⶗-⶟⶧⶯⶷⶿⷇⷏⷗⷟⹏-⹿⺚⻴-⻿⿖-⿯⿼-⿿぀゗゘㄀-㄄㄰㆏ㆻ-ㆿ㇤-㇯㈟㋿䶶-䶿鿰-鿿꒍-꒏꓇-꓏꘬-꘿꛸-꛿Ꞻ-ꟶ꠬-꠯꠺-꠿꡸-꡿꣆-꣍꣚-꣟꥔-꥞꥽-꥿꧎꧚-꧝꧿꨷-꨿꩎꩏꩚꩛꫃-꫚꫷-꬀꬇꬈꬏꬐꬗-꬟꬧꬯ꭦ-꭯꯮꯯꯺-꯿힤-힯퟇-퟊퟼-퟿﩮﩯﫚-﫿﬇-﬒﬘-﬜﬷﬽﬿﭂﭅﯂-﯒﵀-﵏﶐﶑﷈-﷯﷾﷿︚-︟﹓﹧﹬-﹯﹵﻽﻾＀﾿-￁￈￉￐￑￘￙￝-￟￧￯-￸￾￿",astral:"\ud800[\udc0c\udc27\udc3b\udc3e\udc4e\udc4f\udc5e-\udc7f\udcfb-\udcff\udd03-\udd06\udd34-\udd36\udd8f\udd9c-\udd9f\udda1-\uddcf\uddfe-\ude7f\ude9d-\ude9f\uded1-\udedf\udefc-\udeff\udf24-\udf2c\udf4b-\udf4f\udf7b-\udf7f\udf9e\udfc4-\udfc7\udfd6-\udfff]|\ud801[\udc9e\udc9f\udcaa-\udcaf\udcd4-\udcd7\udcfc-\udcff\udd28-\udd2f\udd64-\udd6e\udd70-\uddff\udf37-\udf3f\udf56-\udf5f\udf68-\udfff]|\ud802[\udc06\udc07\udc09\udc36\udc39-\udc3b\udc3d\udc3e\udc56\udc9f-\udca6\udcb0-\udcdf\udcf3\udcf6-\udcfa\udd1c-\udd1e\udd3a-\udd3e\udd40-\udd7f\uddb8-\uddbb\uddd0\uddd1\ude04\ude07-\ude0b\ude14\ude18\ude36\ude37\ude3b-\ude3e\ude49-\ude4f\ude59-\ude5f\udea0-\udebf\udee7-\udeea\udef7-\udeff\udf36-\udf38\udf56\udf57\udf73-\udf77\udf92-\udf98\udf9d-\udfa8\udfb0-\udfff]|\ud803[\udc49-\udc7f\udcb3-\udcbf\udcf3-\udcf9\udd28-\udd2f\udd3a-\ude5f\ude7f-\udeff\udf28-\udf2f\udf5a-\udfff]|\ud804[\udc4e-\udc51\udc70-\udc7e\udcc2-\udccc\udcce\udccf\udce9-\udcef\udcfa-\udcff\udd35\udd47-\udd4f\udd77-\udd7f\uddce\uddcf\udde0\uddf5-\uddff\ude12\ude3f-\ude7f\ude87\ude89\ude8e\ude9e\udeaa-\udeaf\udeeb-\udeef\udefa-\udeff\udf04\udf0d\udf0e\udf11\udf12\udf29\udf31\udf34\udf3a\udf45\udf46\udf49\udf4a\udf4e\udf4f\udf51-\udf56\udf58-\udf5c\udf64\udf65\udf6d-\udf6f\udf75-\udfff]|\ud805[\udc5a\udc5c\udc5f-\udc7f\udcc8-\udccf\udcda-\udd7f\uddb6\uddb7\uddde-\uddff\ude45-\ude4f\ude5a-\ude5f\ude6d-\ude7f\udeb8-\udebf\udeca-\udeff\udf1b\udf1c\udf2c-\udf2f\udf40-\udfff]|\ud806[\udc3c-\udc9f\udcf3-\udcfe\udd00-\uddff\ude48-\ude4f\ude84\ude85\udea3-\udebf\udef9-\udfff]|\ud807[\udc09\udc37\udc46-\udc4f\udc6d-\udc6f\udc90\udc91\udca8\udcb7-\udcff\udd07\udd0a\udd37-\udd39\udd3b\udd3e\udd48-\udd4f\udd5a-\udd5f\udd66\udd69\udd8f\udd92\udd99-\udd9f\uddaa-\udedf\udef9-\udfff]|\ud808[\udf9a-\udfff]|\ud809[\udc6f\udc75-\udc7f\udd44-\udfff]|[\ud80a\ud80b\ud80e-\ud810\ud812-\ud819\ud823-\ud82b\ud82d\ud82e\ud830-\ud833\ud837\ud839\ud83f\ud87b-\ud87d\ud87f-\udb3f\udb41-\udb7f][\udc00-\udfff]|\ud80d[\udc2f-\udfff]|\ud811[\ude47-\udfff]|\ud81a[\ude39-\ude3f\ude5f\ude6a-\ude6d\ude70-\udecf\udeee\udeef\udef6-\udeff\udf46-\udf4f\udf5a\udf62\udf78-\udf7c\udf90-\udfff]|\ud81b[\udc00-\ude3f\ude9b-\udeff\udf45-\udf4f\udf7f-\udf8e\udfa0-\udfdf\udfe2-\udfff]|\ud821[\udff2-\udfff]|\ud822[\udef3-\udfff]|\ud82c[\udd1f-\udd6f\udefc-\udfff]|\ud82f[\udc6b-\udc6f\udc7d-\udc7f\udc89-\udc8f\udc9a\udc9b\udca4-\udfff]|\ud834[\udcf6-\udcff\udd27\udd28\udde9-\uddff\ude46-\udedf\udef4-\udeff\udf57-\udf5f\udf79-\udfff]|\ud835[\udc55\udc9d\udca0\udca1\udca3\udca4\udca7\udca8\udcad\udcba\udcbc\udcc4\udd06\udd0b\udd0c\udd15\udd1d\udd3a\udd3f\udd45\udd47-\udd49\udd51\udea6\udea7\udfcc\udfcd]|\ud836[\ude8c-\ude9a\udea0\udeb0-\udfff]|\ud838[\udc07\udc19\udc1a\udc22\udc25\udc2b-\udfff]|\ud83a[\udcc5\udcc6\udcd7-\udcff\udd4b-\udd4f\udd5a-\udd5d\udd60-\udfff]|\ud83b[\udc00-\udc70\udcb5-\uddff\ude04\ude20\ude23\ude25\ude26\ude28\ude33\ude38\ude3a\ude3c-\ude41\ude43-\ude46\ude48\ude4a\ude4c\ude50\ude53\ude55\ude56\ude58\ude5a\ude5c\ude5e\ude60\ude63\ude65\ude66\ude6b\ude73\ude78\ude7d\ude7f\ude8a\ude9c-\udea0\udea4\udeaa\udebc-\udeef\udef2-\udfff]|\ud83c[\udc2c-\udc2f\udc94-\udc9f\udcaf\udcb0\udcc0\udcd0\udcf6-\udcff\udd0d-\udd0f\udd6c-\udd6f\uddad-\udde5\ude03-\ude0f\ude3c-\ude3f\ude49-\ude4f\ude52-\ude5f\ude66-\udeff]|\ud83d[\uded5-\udedf\udeed-\udeef\udefa-\udeff\udf74-\udf7f\udfd9-\udfff]|\ud83e[\udc0c-\udc0f\udc48-\udc4f\udc5a-\udc5f\udc88-\udc8f\udcae-\udcff\udd0c-\udd0f\udd3f\udd71\udd72\udd77-\udd79\udd7b\udda3-\uddaf\uddba-\uddbf\uddc3-\uddcf\ude00-\ude5f\ude6e-\udfff]|\ud869[\uded7-\udeff]|\ud86d[\udf35-\udf3f]|\ud86e[\udc1e\udc1f]|\ud873[\udea2-\udeaf]|\ud87a[\udfe1-\udfff]|\ud87e[\ude1e-\udfff]|\udb40[\udc00\udc02-\udc1f\udc80-\udcff\uddf0-\udfff]|[\udbbf\udbff][\udffe\udfff]"},{name:"Co",alias:"Private_Use",bmp:"-",astral:"[\udb80-\udbbe\udbc0-\udbfe][\udc00-\udfff]|[\udbbf\udbff][\udc00-\udffd]"},{name:"Cs",alias:"Surrogate",bmp:"\ud800-\udfff"},{name:"L",alias:"Letter",bmp:"A-Za-zªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙՠ-ֈא-תׯ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࡠ-ࡪࢠ-ࢴࢶ-ࢽऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೞೠೡೱೲഅ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄງຈຊຍດ-ທນ-ຟມ-ຣລວສຫອ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛱ-ᛸᜀ-ᜌᜎ-ᜑᜠ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡸᢀ-ᢄᢇ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭋᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲈᲐ-ᲺᲽ-Ჿᳩ-ᳬᳮ-ᳱᳵᳶᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕℙ-ℝℤΩℨK-ℭℯ-ℹℼ-ℿⅅ-ⅉⅎↃↄⰀ-Ⱞⰰ-ⱞⱠ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞⸯ々〆〱-〵〻〼ぁ-ゖゝ-ゟァ-ヺー-ヿㄅ-ㄯㄱ-ㆎㆠ-ㆺㇰ-ㇿ㐀-䶵一-鿯ꀀ-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛥꜗ-ꜟꜢ-ꞈꞋ-ꞹꟷ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭥꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ",astral:"\ud800[\udc00-\udc0b\udc0d-\udc26\udc28-\udc3a\udc3c\udc3d\udc3f-\udc4d\udc50-\udc5d\udc80-\udcfa\ude80-\ude9c\udea0-\uded0\udf00-\udf1f\udf2d-\udf40\udf42-\udf49\udf50-\udf75\udf80-\udf9d\udfa0-\udfc3\udfc8-\udfcf]|\ud801[\udc00-\udc9d\udcb0-\udcd3\udcd8-\udcfb\udd00-\udd27\udd30-\udd63\ude00-\udf36\udf40-\udf55\udf60-\udf67]|\ud802[\udc00-\udc05\udc08\udc0a-\udc35\udc37\udc38\udc3c\udc3f-\udc55\udc60-\udc76\udc80-\udc9e\udce0-\udcf2\udcf4\udcf5\udd00-\udd15\udd20-\udd39\udd80-\uddb7\uddbe\uddbf\ude00\ude10-\ude13\ude15-\ude17\ude19-\ude35\ude60-\ude7c\ude80-\ude9c\udec0-\udec7\udec9-\udee4\udf00-\udf35\udf40-\udf55\udf60-\udf72\udf80-\udf91]|\ud803[\udc00-\udc48\udc80-\udcb2\udcc0-\udcf2\udd00-\udd23\udf00-\udf1c\udf27\udf30-\udf45]|\ud804[\udc03-\udc37\udc83-\udcaf\udcd0-\udce8\udd03-\udd26\udd44\udd50-\udd72\udd76\udd83-\uddb2\uddc1-\uddc4\uddda\udddc\ude00-\ude11\ude13-\ude2b\ude80-\ude86\ude88\ude8a-\ude8d\ude8f-\ude9d\ude9f-\udea8\udeb0-\udede\udf05-\udf0c\udf0f\udf10\udf13-\udf28\udf2a-\udf30\udf32\udf33\udf35-\udf39\udf3d\udf50\udf5d-\udf61]|\ud805[\udc00-\udc34\udc47-\udc4a\udc80-\udcaf\udcc4\udcc5\udcc7\udd80-\uddae\uddd8-\udddb\ude00-\ude2f\ude44\ude80-\udeaa\udf00-\udf1a]|\ud806[\udc00-\udc2b\udca0-\udcdf\udcff\ude00\ude0b-\ude32\ude3a\ude50\ude5c-\ude83\ude86-\ude89\ude9d\udec0-\udef8]|\ud807[\udc00-\udc08\udc0a-\udc2e\udc40\udc72-\udc8f\udd00-\udd06\udd08\udd09\udd0b-\udd30\udd46\udd60-\udd65\udd67\udd68\udd6a-\udd89\udd98\udee0-\udef2]|\ud808[\udc00-\udf99]|\ud809[\udc80-\udd43]|[\ud80c\ud81c-\ud820\ud840-\ud868\ud86a-\ud86c\ud86f-\ud872\ud874-\ud879][\udc00-\udfff]|\ud80d[\udc00-\udc2e]|\ud811[\udc00-\ude46]|\ud81a[\udc00-\ude38\ude40-\ude5e\uded0-\udeed\udf00-\udf2f\udf40-\udf43\udf63-\udf77\udf7d-\udf8f]|\ud81b[\ude40-\ude7f\udf00-\udf44\udf50\udf93-\udf9f\udfe0\udfe1]|\ud821[\udc00-\udff1]|\ud822[\udc00-\udef2]|\ud82c[\udc00-\udd1e\udd70-\udefb]|\ud82f[\udc00-\udc6a\udc70-\udc7c\udc80-\udc88\udc90-\udc99]|\ud835[\udc00-\udc54\udc56-\udc9c\udc9e\udc9f\udca2\udca5\udca6\udca9-\udcac\udcae-\udcb9\udcbb\udcbd-\udcc3\udcc5-\udd05\udd07-\udd0a\udd0d-\udd14\udd16-\udd1c\udd1e-\udd39\udd3b-\udd3e\udd40-\udd44\udd46\udd4a-\udd50\udd52-\udea5\udea8-\udec0\udec2-\udeda\udedc-\udefa\udefc-\udf14\udf16-\udf34\udf36-\udf4e\udf50-\udf6e\udf70-\udf88\udf8a-\udfa8\udfaa-\udfc2\udfc4-\udfcb]|\ud83a[\udc00-\udcc4\udd00-\udd43]|\ud83b[\ude00-\ude03\ude05-\ude1f\ude21\ude22\ude24\ude27\ude29-\ude32\ude34-\ude37\ude39\ude3b\ude42\ude47\ude49\ude4b\ude4d-\ude4f\ude51\ude52\ude54\ude57\ude59\ude5b\ude5d\ude5f\ude61\ude62\ude64\ude67-\ude6a\ude6c-\ude72\ude74-\ude77\ude79-\ude7c\ude7e\ude80-\ude89\ude8b-\ude9b\udea1-\udea3\udea5-\udea9\udeab-\udebb]|\ud869[\udc00-\uded6\udf00-\udfff]|\ud86d[\udc00-\udf34\udf40-\udfff]|\ud86e[\udc00-\udc1d\udc20-\udfff]|\ud873[\udc00-\udea1\udeb0-\udfff]|\ud87a[\udc00-\udfe0]|\ud87e[\udc00-\ude1d]"},{name:"LC",alias:"Cased_Letter",bmp:"A-Za-zµÀ-ÖØ-öø-ƺƼ-ƿǄ-ʓʕ-ʯͰ-ͳͶͷͻ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՠ-ֈႠ-ჅჇჍა-ჺჽ-ჿᎠ-Ᏽᏸ-ᏽᲀ-ᲈᲐ-ᲺᲽ-Ჿᴀ-ᴫᵫ-ᵷᵹ-ᶚḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼℂℇℊ-ℓℕℙ-ℝℤΩℨK-ℭℯ-ℴℹℼ-ℿⅅ-ⅉⅎↃↄⰀ-Ⱞⰰ-ⱞⱠ-ⱻⱾ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭꙀ-ꙭꚀ-ꚛꜢ-ꝯꝱ-ꞇꞋ-ꞎꞐ-ꞹꟺꬰ-ꭚꭠ-ꭥꭰ-ꮿﬀ-ﬆﬓ-ﬗＡ-Ｚａ-ｚ",astral:"\ud801[\udc00-\udc4f\udcb0-\udcd3\udcd8-\udcfb]|\ud803[\udc80-\udcb2\udcc0-\udcf2]|\ud806[\udca0-\udcdf]|\ud81b[\ude40-\ude7f]|\ud835[\udc00-\udc54\udc56-\udc9c\udc9e\udc9f\udca2\udca5\udca6\udca9-\udcac\udcae-\udcb9\udcbb\udcbd-\udcc3\udcc5-\udd05\udd07-\udd0a\udd0d-\udd14\udd16-\udd1c\udd1e-\udd39\udd3b-\udd3e\udd40-\udd44\udd46\udd4a-\udd50\udd52-\udea5\udea8-\udec0\udec2-\udeda\udedc-\udefa\udefc-\udf14\udf16-\udf34\udf36-\udf4e\udf50-\udf6e\udf70-\udf88\udf8a-\udfa8\udfaa-\udfc2\udfc4-\udfcb]|\ud83a[\udd00-\udd43]"},{name:"Ll",alias:"Lowercase_Letter",bmp:"a-zµß-öø-ÿāăąćĉċčďđēĕėęěĝğġģĥħĩīĭįıĳĵķĸĺļľŀłńņňŉŋōŏőœŕŗřśŝşšţťŧũūŭůűųŵŷźżž-ƀƃƅƈƌƍƒƕƙ-ƛƞơƣƥƨƪƫƭưƴƶƹƺƽ-ƿǆǉǌǎǐǒǔǖǘǚǜǝǟǡǣǥǧǩǫǭǯǰǳǵǹǻǽǿȁȃȅȇȉȋȍȏȑȓȕȗșțȝȟȡȣȥȧȩȫȭȯȱȳ-ȹȼȿɀɂɇɉɋɍɏ-ʓʕ-ʯͱͳͷͻ-ͽΐά-ώϐϑϕ-ϗϙϛϝϟϡϣϥϧϩϫϭϯ-ϳϵϸϻϼа-џѡѣѥѧѩѫѭѯѱѳѵѷѹѻѽѿҁҋҍҏґғҕҗҙқҝҟҡңҥҧҩҫҭүұҳҵҷҹһҽҿӂӄӆӈӊӌӎӏӑӓӕӗәӛӝӟӡӣӥӧөӫӭӯӱӳӵӷӹӻӽӿԁԃԅԇԉԋԍԏԑԓԕԗԙԛԝԟԡԣԥԧԩԫԭԯՠ-ֈა-ჺჽ-ჿᏸ-ᏽᲀ-ᲈᴀ-ᴫᵫ-ᵷᵹ-ᶚḁḃḅḇḉḋḍḏḑḓḕḗḙḛḝḟḡḣḥḧḩḫḭḯḱḳḵḷḹḻḽḿṁṃṅṇṉṋṍṏṑṓṕṗṙṛṝṟṡṣṥṧṩṫṭṯṱṳṵṷṹṻṽṿẁẃẅẇẉẋẍẏẑẓẕ-ẝẟạảấầẩẫậắằẳẵặẹẻẽếềểễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹỻỽỿ-ἇἐ-ἕἠ-ἧἰ-ἷὀ-ὅὐ-ὗὠ-ὧὰ-ώᾀ-ᾇᾐ-ᾗᾠ-ᾧᾰ-ᾴᾶᾷιῂ-ῄῆῇῐ-ΐῖῗῠ-ῧῲ-ῴῶῷℊℎℏℓℯℴℹℼℽⅆ-ⅉⅎↄⰰ-ⱞⱡⱥⱦⱨⱪⱬⱱⱳⱴⱶ-ⱻⲁⲃⲅⲇⲉⲋⲍⲏⲑⲓⲕⲗⲙⲛⲝⲟⲡⲣⲥⲧⲩⲫⲭⲯⲱⲳⲵⲷⲹⲻⲽⲿⳁⳃⳅⳇⳉⳋⳍⳏⳑⳓⳕⳗⳙⳛⳝⳟⳡⳣⳤⳬⳮⳳⴀ-ⴥⴧⴭꙁꙃꙅꙇꙉꙋꙍꙏꙑꙓꙕꙗꙙꙛꙝꙟꙡꙣꙥꙧꙩꙫꙭꚁꚃꚅꚇꚉꚋꚍꚏꚑꚓꚕꚗꚙꚛꜣꜥꜧꜩꜫꜭꜯ-ꜱꜳꜵꜷꜹꜻꜽꜿꝁꝃꝅꝇꝉꝋꝍꝏꝑꝓꝕꝗꝙꝛꝝꝟꝡꝣꝥꝧꝩꝫꝭꝯꝱ-ꝸꝺꝼꝿꞁꞃꞅꞇꞌꞎꞑꞓ-ꞕꞗꞙꞛꞝꞟꞡꞣꞥꞧꞩꞯꞵꞷꞹꟺꬰ-ꭚꭠ-ꭥꭰ-ꮿﬀ-ﬆﬓ-ﬗａ-ｚ",astral:"\ud801[\udc28-\udc4f\udcd8-\udcfb]|\ud803[\udcc0-\udcf2]|\ud806[\udcc0-\udcdf]|\ud81b[\ude60-\ude7f]|\ud835[\udc1a-\udc33\udc4e-\udc54\udc56-\udc67\udc82-\udc9b\udcb6-\udcb9\udcbb\udcbd-\udcc3\udcc5-\udccf\udcea-\udd03\udd1e-\udd37\udd52-\udd6b\udd86-\udd9f\uddba-\uddd3\uddee-\ude07\ude22-\ude3b\ude56-\ude6f\ude8a-\udea5\udec2-\udeda\udedc-\udee1\udefc-\udf14\udf16-\udf1b\udf36-\udf4e\udf50-\udf55\udf70-\udf88\udf8a-\udf8f\udfaa-\udfc2\udfc4-\udfc9\udfcb]|\ud83a[\udd22-\udd43]"},{name:"Lm",alias:"Modifier_Letter",bmp:"ʰ-ˁˆ-ˑˠ-ˤˬˮʹͺՙـۥۦߴߵߺࠚࠤࠨॱๆໆჼៗᡃᪧᱸ-ᱽᴬ-ᵪᵸᶛ-ᶿⁱⁿₐ-ₜⱼⱽⵯⸯ々〱-〵〻ゝゞー-ヾꀕꓸ-ꓽꘌꙿꚜꚝꜗ-ꜟꝰꞈꟸꟹꧏꧦꩰꫝꫳꫴꭜ-ꭟｰﾞﾟ",astral:"\ud81a[\udf40-\udf43]|\ud81b[\udf93-\udf9f\udfe0\udfe1]"},{name:"Lo",alias:"Other_Letter",bmp:"ªºƻǀ-ǃʔא-תׯ-ײؠ-ؿف-يٮٯٱ-ۓەۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪࠀ-ࠕࡀ-ࡘࡠ-ࡪࢠ-ࢴࢶ-ࢽऄ-हऽॐक़-ॡॲ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೞೠೡೱೲഅ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๅກຂຄງຈຊຍດ-ທນ-ຟມ-ຣລວສຫອ-ະາຳຽເ-ໄໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎᄀ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛱ-ᛸᜀ-ᜌᜎ-ᜑᜠ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៜᠠ-ᡂᡄ-ᡸᢀ-ᢄᢇ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᬅ-ᬳᭅ-ᭋᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱷᳩ-ᳬᳮ-ᳱᳵᳶℵ-ℸⴰ-ⵧⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ〆〼ぁ-ゖゟァ-ヺヿㄅ-ㄯㄱ-ㆎㆠ-ㆺㇰ-ㇿ㐀-䶵一-鿯ꀀ-ꀔꀖ-ꒌꓐ-ꓷꔀ-ꘋꘐ-ꘟꘪꘫꙮꚠ-ꛥꞏꟷꟻ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧠ-ꧤꧧ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩯꩱ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛꫜꫠ-ꫪꫲꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꯀ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎יִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼｦ-ｯｱ-ﾝﾠ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ",astral:"\ud800[\udc00-\udc0b\udc0d-\udc26\udc28-\udc3a\udc3c\udc3d\udc3f-\udc4d\udc50-\udc5d\udc80-\udcfa\ude80-\ude9c\udea0-\uded0\udf00-\udf1f\udf2d-\udf40\udf42-\udf49\udf50-\udf75\udf80-\udf9d\udfa0-\udfc3\udfc8-\udfcf]|\ud801[\udc50-\udc9d\udd00-\udd27\udd30-\udd63\ude00-\udf36\udf40-\udf55\udf60-\udf67]|\ud802[\udc00-\udc05\udc08\udc0a-\udc35\udc37\udc38\udc3c\udc3f-\udc55\udc60-\udc76\udc80-\udc9e\udce0-\udcf2\udcf4\udcf5\udd00-\udd15\udd20-\udd39\udd80-\uddb7\uddbe\uddbf\ude00\ude10-\ude13\ude15-\ude17\ude19-\ude35\ude60-\ude7c\ude80-\ude9c\udec0-\udec7\udec9-\udee4\udf00-\udf35\udf40-\udf55\udf60-\udf72\udf80-\udf91]|\ud803[\udc00-\udc48\udd00-\udd23\udf00-\udf1c\udf27\udf30-\udf45]|\ud804[\udc03-\udc37\udc83-\udcaf\udcd0-\udce8\udd03-\udd26\udd44\udd50-\udd72\udd76\udd83-\uddb2\uddc1-\uddc4\uddda\udddc\ude00-\ude11\ude13-\ude2b\ude80-\ude86\ude88\ude8a-\ude8d\ude8f-\ude9d\ude9f-\udea8\udeb0-\udede\udf05-\udf0c\udf0f\udf10\udf13-\udf28\udf2a-\udf30\udf32\udf33\udf35-\udf39\udf3d\udf50\udf5d-\udf61]|\ud805[\udc00-\udc34\udc47-\udc4a\udc80-\udcaf\udcc4\udcc5\udcc7\udd80-\uddae\uddd8-\udddb\ude00-\ude2f\ude44\ude80-\udeaa\udf00-\udf1a]|\ud806[\udc00-\udc2b\udcff\ude00\ude0b-\ude32\ude3a\ude50\ude5c-\ude83\ude86-\ude89\ude9d\udec0-\udef8]|\ud807[\udc00-\udc08\udc0a-\udc2e\udc40\udc72-\udc8f\udd00-\udd06\udd08\udd09\udd0b-\udd30\udd46\udd60-\udd65\udd67\udd68\udd6a-\udd89\udd98\udee0-\udef2]|\ud808[\udc00-\udf99]|\ud809[\udc80-\udd43]|[\ud80c\ud81c-\ud820\ud840-\ud868\ud86a-\ud86c\ud86f-\ud872\ud874-\ud879][\udc00-\udfff]|\ud80d[\udc00-\udc2e]|\ud811[\udc00-\ude46]|\ud81a[\udc00-\ude38\ude40-\ude5e\uded0-\udeed\udf00-\udf2f\udf63-\udf77\udf7d-\udf8f]|\ud81b[\udf00-\udf44\udf50]|\ud821[\udc00-\udff1]|\ud822[\udc00-\udef2]|\ud82c[\udc00-\udd1e\udd70-\udefb]|\ud82f[\udc00-\udc6a\udc70-\udc7c\udc80-\udc88\udc90-\udc99]|\ud83a[\udc00-\udcc4]|\ud83b[\ude00-\ude03\ude05-\ude1f\ude21\ude22\ude24\ude27\ude29-\ude32\ude34-\ude37\ude39\ude3b\ude42\ude47\ude49\ude4b\ude4d-\ude4f\ude51\ude52\ude54\ude57\ude59\ude5b\ude5d\ude5f\ude61\ude62\ude64\ude67-\ude6a\ude6c-\ude72\ude74-\ude77\ude79-\ude7c\ude7e\ude80-\ude89\ude8b-\ude9b\udea1-\udea3\udea5-\udea9\udeab-\udebb]|\ud869[\udc00-\uded6\udf00-\udfff]|\ud86d[\udc00-\udf34\udf40-\udfff]|\ud86e[\udc00-\udc1d\udc20-\udfff]|\ud873[\udc00-\udea1\udeb0-\udfff]|\ud87a[\udc00-\udfe0]|\ud87e[\udc00-\ude1d]"},{name:"Lt",alias:"Titlecase_Letter",bmp:"ǅǈǋǲᾈ-ᾏᾘ-ᾟᾨ-ᾯᾼῌῼ"},{name:"Lu",alias:"Uppercase_Letter",bmp:"A-ZÀ-ÖØ-ÞĀĂĄĆĈĊČĎĐĒĔĖĘĚĜĞĠĢĤĦĨĪĬĮİĲĴĶĹĻĽĿŁŃŅŇŊŌŎŐŒŔŖŘŚŜŞŠŢŤŦŨŪŬŮŰŲŴŶŸŹŻŽƁƂƄƆƇƉ-ƋƎ-ƑƓƔƖ-ƘƜƝƟƠƢƤƦƧƩƬƮƯƱ-ƳƵƷƸƼǄǇǊǍǏǑǓǕǗǙǛǞǠǢǤǦǨǪǬǮǱǴǶ-ǸǺǼǾȀȂȄȆȈȊȌȎȐȒȔȖȘȚȜȞȠȢȤȦȨȪȬȮȰȲȺȻȽȾɁɃ-ɆɈɊɌɎͰͲͶͿΆΈ-ΊΌΎΏΑ-ΡΣ-ΫϏϒ-ϔϘϚϜϞϠϢϤϦϨϪϬϮϴϷϹϺϽ-ЯѠѢѤѦѨѪѬѮѰѲѴѶѸѺѼѾҀҊҌҎҐҒҔҖҘҚҜҞҠҢҤҦҨҪҬҮҰҲҴҶҸҺҼҾӀӁӃӅӇӉӋӍӐӒӔӖӘӚӜӞӠӢӤӦӨӪӬӮӰӲӴӶӸӺӼӾԀԂԄԆԈԊԌԎԐԒԔԖԘԚԜԞԠԢԤԦԨԪԬԮԱ-ՖႠ-ჅჇჍᎠ-ᏵᲐ-ᲺᲽ-ᲿḀḂḄḆḈḊḌḎḐḒḔḖḘḚḜḞḠḢḤḦḨḪḬḮḰḲḴḶḸḺḼḾṀṂṄṆṈṊṌṎṐṒṔṖṘṚṜṞṠṢṤṦṨṪṬṮṰṲṴṶṸṺṼṾẀẂẄẆẈẊẌẎẐẒẔẞẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼẾỀỂỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴỶỸỺỼỾἈ-ἏἘ-ἝἨ-ἯἸ-ἿὈ-ὍὙὛὝὟὨ-ὯᾸ-ΆῈ-ΉῘ-ΊῨ-ῬῸ-Ώℂℇℋ-ℍℐ-ℒℕℙ-ℝℤΩℨK-ℭℰ-ℳℾℿⅅↃⰀ-ⰮⱠⱢ-ⱤⱧⱩⱫⱭ-ⱰⱲⱵⱾ-ⲀⲂⲄⲆⲈⲊⲌⲎⲐⲒⲔⲖⲘⲚⲜⲞⲠⲢⲤⲦⲨⲪⲬⲮⲰⲲⲴⲶⲸⲺⲼⲾⳀⳂⳄⳆⳈⳊⳌⳎⳐⳒⳔⳖⳘⳚⳜⳞⳠⳢⳫⳭⳲꙀꙂꙄꙆꙈꙊꙌꙎꙐꙒꙔꙖꙘꙚꙜꙞꙠꙢꙤꙦꙨꙪꙬꚀꚂꚄꚆꚈꚊꚌꚎꚐꚒꚔꚖꚘꚚꜢꜤꜦꜨꜪꜬꜮꜲꜴꜶꜸꜺꜼꜾꝀꝂꝄꝆꝈꝊꝌꝎꝐꝒꝔꝖꝘꝚꝜꝞꝠꝢꝤꝦꝨꝪꝬꝮꝹꝻꝽꝾꞀꞂꞄꞆꞋꞍꞐꞒꞖꞘꞚꞜꞞꞠꞢꞤꞦꞨꞪ-ꞮꞰ-ꞴꞶꞸＡ-Ｚ",astral:"\ud801[\udc00-\udc27\udcb0-\udcd3]|\ud803[\udc80-\udcb2]|\ud806[\udca0-\udcbf]|\ud81b[\ude40-\ude5f]|\ud835[\udc00-\udc19\udc34-\udc4d\udc68-\udc81\udc9c\udc9e\udc9f\udca2\udca5\udca6\udca9-\udcac\udcae-\udcb5\udcd0-\udce9\udd04\udd05\udd07-\udd0a\udd0d-\udd14\udd16-\udd1c\udd38\udd39\udd3b-\udd3e\udd40-\udd44\udd46\udd4a-\udd50\udd6c-\udd85\udda0-\uddb9\uddd4-\udded\ude08-\ude21\ude3c-\ude55\ude70-\ude89\udea8-\udec0\udee2-\udefa\udf1c-\udf34\udf56-\udf6e\udf90-\udfa8\udfca]|\ud83a[\udd00-\udd21]"},{name:"M",alias:"Mark",bmp:"̀-ͯ҃-҉֑-ׇֽֿׁׂׅׄؐ-ًؚ-ٰٟۖ-ۜ۟-۪ۤۧۨ-ܑۭܰ-݊ަ-ް߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛࣓-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣঁ-ঃ়া-ৄেৈো-্ৗৢৣ৾ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑੰੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣૺ-૿ଁ-ଃ଼ା-ୄେୈୋ-୍ୖୗୢୣஂா-ூெ-ைொ-்ௗఀ-ఄా-ౄె-ైొ-్ౕౖౢౣಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣഀ-ഃ഻഼ാ-ൄെ-ൈൊ-്ൗൢൣංඃ්ා-ුූෘ-ෟෲෳัิ-ฺ็-๎ັິ-ູົຼ່-ໍ༹༘༙༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏႚ-ႝ፝-፟ᜒ-᜔ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝᠋-᠍ᢅᢆᢩᤠ-ᤫᤰ-᤻ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼᪰-᪾ᬀ-ᬄ᬴-᭄᭫-᭳ᮀ-ᮂᮡ-ᮭ᯦-᯳ᰤ-᰷᳐-᳔᳒-᳨᳭ᳲ-᳴᳷-᳹᷀-᷹᷻-᷿⃐-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯꙯-꙲ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧꢀꢁꢴ-ꣅ꣠-꣱ꣿꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀ꧥꨩ-ꨶꩃꩌꩍꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭ﬞ︀-️︠-︯",astral:"\ud800[\uddfd\udee0\udf76-\udf7a]|\ud802[\ude01-\ude03\ude05\ude06\ude0c-\ude0f\ude38-\ude3a\ude3f\udee5\udee6]|\ud803[\udd24-\udd27\udf46-\udf50]|\ud804[\udc00-\udc02\udc38-\udc46\udc7f-\udc82\udcb0-\udcba\udd00-\udd02\udd27-\udd34\udd45\udd46\udd73\udd80-\udd82\uddb3-\uddc0\uddc9-\uddcc\ude2c-\ude37\ude3e\udedf-\udeea\udf00-\udf03\udf3b\udf3c\udf3e-\udf44\udf47\udf48\udf4b-\udf4d\udf57\udf62\udf63\udf66-\udf6c\udf70-\udf74]|\ud805[\udc35-\udc46\udc5e\udcb0-\udcc3\uddaf-\uddb5\uddb8-\uddc0\udddc\udddd\ude30-\ude40\udeab-\udeb7\udf1d-\udf2b]|\ud806[\udc2c-\udc3a\ude01-\ude0a\ude33-\ude39\ude3b-\ude3e\ude47\ude51-\ude5b\ude8a-\ude99]|\ud807[\udc2f-\udc36\udc38-\udc3f\udc92-\udca7\udca9-\udcb6\udd31-\udd36\udd3a\udd3c\udd3d\udd3f-\udd45\udd47\udd8a-\udd8e\udd90\udd91\udd93-\udd97\udef3-\udef6]|\ud81a[\udef0-\udef4\udf30-\udf36]|\ud81b[\udf51-\udf7e\udf8f-\udf92]|\ud82f[\udc9d\udc9e]|\ud834[\udd65-\udd69\udd6d-\udd72\udd7b-\udd82\udd85-\udd8b\uddaa-\uddad\ude42-\ude44]|\ud836[\ude00-\ude36\ude3b-\ude6c\ude75\ude84\ude9b-\ude9f\udea1-\udeaf]|\ud838[\udc00-\udc06\udc08-\udc18\udc1b-\udc21\udc23\udc24\udc26-\udc2a]|\ud83a[\udcd0-\udcd6\udd44-\udd4a]|\udb40[\udd00-\uddef]"},{name:"Mc",alias:"Spacing_Mark",bmp:"ःऻा-ीॉ-ौॎॏংঃা-ীেৈোৌৗਃਾ-ੀઃા-ીૉોૌଂଃାୀେୈୋୌୗாிுூெ-ைொ-ௌௗఁ-ఃు-ౄಂಃಾೀ-ೄೇೈೊೋೕೖംഃാ-ീെ-ൈൊ-ൌൗංඃා-ෑෘ-ෟෲෳ༾༿ཿါာေးျြၖၗၢ-ၤၧ-ၭႃႄႇ-ႌႏႚ-ႜាើ-ៅះៈᤣ-ᤦᤩ-ᤫᤰᤱᤳ-ᤸᨙᨚᩕᩗᩡᩣᩤᩭ-ᩲᬄᬵᬻᬽ-ᭁᭃ᭄ᮂᮡᮦᮧ᮪ᯧᯪ-ᯬᯮ᯲᯳ᰤ-ᰫᰴᰵ᳡ᳲᳳ᳷〮〯ꠣꠤꠧꢀꢁꢴ-ꣃꥒ꥓ꦃꦴꦵꦺꦻꦽ-꧀ꨯꨰꨳꨴꩍꩻꩽꫫꫮꫯꫵꯣꯤꯦꯧꯩꯪ꯬",astral:"\ud804[\udc00\udc02\udc82\udcb0-\udcb2\udcb7\udcb8\udd2c\udd45\udd46\udd82\uddb3-\uddb5\uddbf\uddc0\ude2c-\ude2e\ude32\ude33\ude35\udee0-\udee2\udf02\udf03\udf3e\udf3f\udf41-\udf44\udf47\udf48\udf4b-\udf4d\udf57\udf62\udf63]|\ud805[\udc35-\udc37\udc40\udc41\udc45\udcb0-\udcb2\udcb9\udcbb-\udcbe\udcc1\uddaf-\uddb1\uddb8-\uddbb\uddbe\ude30-\ude32\ude3b\ude3c\ude3e\udeac\udeae\udeaf\udeb6\udf20\udf21\udf26]|\ud806[\udc2c-\udc2e\udc38\ude39\ude57\ude58\ude97]|\ud807[\udc2f\udc3e\udca9\udcb1\udcb4\udd8a-\udd8e\udd93\udd94\udd96\udef5\udef6]|\ud81b[\udf51-\udf7e]|\ud834[\udd65\udd66\udd6d-\udd72]"},{name:"Me",alias:"Enclosing_Mark",bmp:"҈҉᪾⃝-⃠⃢-⃤꙰-꙲"},{name:"Mn",alias:"Nonspacing_Mark",bmp:"̀-ͯ҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-ٰٟۖ-ۜ۟-۪ۤۧۨ-ܑۭܰ-݊ަ-ް߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛࣓-ࣣ࣡-ंऺ़ु-ै्॑-ॗॢॣঁ়ু-ৄ্ৢৣ৾ਁਂ਼ੁੂੇੈੋ-੍ੑੰੱੵઁં઼ુ-ૅેૈ્ૢૣૺ-૿ଁ଼ିୁ-ୄ୍ୖୢୣஂீ்ఀఄా-ీె-ైొ-్ౕౖౢౣಁ಼ಿೆೌ್ೢೣഀഁ഻഼ു-ൄ്ൢൣ්ි-ුූัิ-ฺ็-๎ັິ-ູົຼ່-ໍཱ༹༘༙༵༷-ཾྀ-྄྆྇ྍ-ྗྙ-ྼ࿆ိ-ူဲ-့္်ွှၘၙၞ-ၠၱ-ၴႂႅႆႍႝ፝-፟ᜒ-᜔ᜲ-᜴ᝒᝓᝲᝳ឴឵ិ-ួំ៉-៓៝᠋-᠍ᢅᢆᢩᤠ-ᤢᤧᤨᤲ᤹-᤻ᨘᨗᨛᩖᩘ-ᩞ᩠ᩢᩥ-ᩬᩳ-᩿᩼᪰-᪽ᬀ-ᬃ᬴ᬶ-ᬺᬼᭂ᭫-᭳ᮀᮁᮢ-ᮥᮨᮩ᮫-ᮭ᯦ᯨᯩᯭᯯ-ᯱᰬ-ᰳᰶ᰷᳐-᳔᳒-᳢᳠-᳨᳭᳴᳸᳹᷀-᷹᷻-᷿⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〭꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠥꠦ꣄ꣅ꣠-꣱ꣿꤦ-꤭ꥇ-ꥑꦀ-ꦂ꦳ꦶ-ꦹꦼꧥꨩ-ꨮꨱꨲꨵꨶꩃꩌꩼꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫬꫭ꫶ꯥꯨ꯭ﬞ︀-️︠-︯",astral:"\ud800[\uddfd\udee0\udf76-\udf7a]|\ud802[\ude01-\ude03\ude05\ude06\ude0c-\ude0f\ude38-\ude3a\ude3f\udee5\udee6]|\ud803[\udd24-\udd27\udf46-\udf50]|\ud804[\udc01\udc38-\udc46\udc7f-\udc81\udcb3-\udcb6\udcb9\udcba\udd00-\udd02\udd27-\udd2b\udd2d-\udd34\udd73\udd80\udd81\uddb6-\uddbe\uddc9-\uddcc\ude2f-\ude31\ude34\ude36\ude37\ude3e\udedf\udee3-\udeea\udf00\udf01\udf3b\udf3c\udf40\udf66-\udf6c\udf70-\udf74]|\ud805[\udc38-\udc3f\udc42-\udc44\udc46\udc5e\udcb3-\udcb8\udcba\udcbf\udcc0\udcc2\udcc3\uddb2-\uddb5\uddbc\uddbd\uddbf\uddc0\udddc\udddd\ude33-\ude3a\ude3d\ude3f\ude40\udeab\udead\udeb0-\udeb5\udeb7\udf1d-\udf1f\udf22-\udf25\udf27-\udf2b]|\ud806[\udc2f-\udc37\udc39\udc3a\ude01-\ude0a\ude33-\ude38\ude3b-\ude3e\ude47\ude51-\ude56\ude59-\ude5b\ude8a-\ude96\ude98\ude99]|\ud807[\udc30-\udc36\udc38-\udc3d\udc3f\udc92-\udca7\udcaa-\udcb0\udcb2\udcb3\udcb5\udcb6\udd31-\udd36\udd3a\udd3c\udd3d\udd3f-\udd45\udd47\udd90\udd91\udd95\udd97\udef3\udef4]|\ud81a[\udef0-\udef4\udf30-\udf36]|\ud81b[\udf8f-\udf92]|\ud82f[\udc9d\udc9e]|\ud834[\udd67-\udd69\udd7b-\udd82\udd85-\udd8b\uddaa-\uddad\ude42-\ude44]|\ud836[\ude00-\ude36\ude3b-\ude6c\ude75\ude84\ude9b-\ude9f\udea1-\udeaf]|\ud838[\udc00-\udc06\udc08-\udc18\udc1b-\udc21\udc23\udc24\udc26-\udc2a]|\ud83a[\udcd0-\udcd6\udd44-\udd4a]|\udb40[\udd00-\uddef]"},{name:"N",alias:"Number",bmp:"0-9²³¹¼-¾٠-٩۰-۹߀-߉०-९০-৯৴-৹੦-੯૦-૯୦-୯୲-୷௦-௲౦-౯౸-౾೦-೯൘-൞൦-൸෦-෯๐-๙໐-໙༠-༳၀-၉႐-႙፩-፼ᛮ-ᛰ០-៩៰-៹᠐-᠙᥆-᥏᧐-᧚᪀-᪉᪐-᪙᭐-᭙᮰-᮹᱀-᱉᱐-᱙⁰⁴-⁹₀-₉⅐-ↂↅ-↉①-⒛⓪-⓿❶-➓⳽〇〡-〩〸-〺㆒-㆕㈠-㈩㉈-㉏㉑-㉟㊀-㊉㊱-㊿꘠-꘩ꛦ-ꛯ꠰-꠵꣐-꣙꤀-꤉꧐-꧙꧰-꧹꩐-꩙꯰-꯹０-９",astral:"\ud800[\udd07-\udd33\udd40-\udd78\udd8a\udd8b\udee1-\udefb\udf20-\udf23\udf41\udf4a\udfd1-\udfd5]|\ud801[\udca0-\udca9]|\ud802[\udc58-\udc5f\udc79-\udc7f\udca7-\udcaf\udcfb-\udcff\udd16-\udd1b\uddbc\uddbd\uddc0-\uddcf\uddd2-\uddff\ude40-\ude48\ude7d\ude7e\ude9d-\ude9f\udeeb-\udeef\udf58-\udf5f\udf78-\udf7f\udfa9-\udfaf]|\ud803[\udcfa-\udcff\udd30-\udd39\ude60-\ude7e\udf1d-\udf26\udf51-\udf54]|\ud804[\udc52-\udc6f\udcf0-\udcf9\udd36-\udd3f\uddd0-\uddd9\udde1-\uddf4\udef0-\udef9]|\ud805[\udc50-\udc59\udcd0-\udcd9\ude50-\ude59\udec0-\udec9\udf30-\udf3b]|\ud806[\udce0-\udcf2]|\ud807[\udc50-\udc6c\udd50-\udd59\udda0-\udda9]|\ud809[\udc00-\udc6e]|\ud81a[\ude60-\ude69\udf50-\udf59\udf5b-\udf61]|\ud81b[\ude80-\ude96]|\ud834[\udee0-\udef3\udf60-\udf78]|\ud835[\udfce-\udfff]|\ud83a[\udcc7-\udccf\udd50-\udd59]|\ud83b[\udc71-\udcab\udcad-\udcaf\udcb1-\udcb4]|\ud83c[\udd00-\udd0c]"},{name:"Nd",alias:"Decimal_Number",bmp:"0-9٠-٩۰-۹߀-߉०-९০-৯੦-੯૦-૯୦-୯௦-௯౦-౯೦-೯൦-൯෦-෯๐-๙໐-໙༠-༩၀-၉႐-႙០-៩᠐-᠙᥆-᥏᧐-᧙᪀-᪉᪐-᪙᭐-᭙᮰-᮹᱀-᱉᱐-᱙꘠-꘩꣐-꣙꤀-꤉꧐-꧙꧰-꧹꩐-꩙꯰-꯹０-９",astral:"\ud801[\udca0-\udca9]|\ud803[\udd30-\udd39]|\ud804[\udc66-\udc6f\udcf0-\udcf9\udd36-\udd3f\uddd0-\uddd9\udef0-\udef9]|\ud805[\udc50-\udc59\udcd0-\udcd9\ude50-\ude59\udec0-\udec9\udf30-\udf39]|\ud806[\udce0-\udce9]|\ud807[\udc50-\udc59\udd50-\udd59\udda0-\udda9]|\ud81a[\ude60-\ude69\udf50-\udf59]|\ud835[\udfce-\udfff]|\ud83a[\udd50-\udd59]"},{name:"Nl",alias:"Letter_Number",bmp:"ᛮ-ᛰⅠ-ↂↅ-ↈ〇〡-〩〸-〺ꛦ-ꛯ",astral:"\ud800[\udd40-\udd74\udf41\udf4a\udfd1-\udfd5]|\ud809[\udc00-\udc6e]"},{name:"No",alias:"Other_Number",bmp:"²³¹¼-¾৴-৹୲-୷௰-௲౸-౾൘-൞൰-൸༪-༳፩-፼៰-៹᧚⁰⁴-⁹₀-₉⅐-⅟↉①-⒛⓪-⓿❶-➓⳽㆒-㆕㈠-㈩㉈-㉏㉑-㉟㊀-㊉㊱-㊿꠰-꠵",astral:"\ud800[\udd07-\udd33\udd75-\udd78\udd8a\udd8b\udee1-\udefb\udf20-\udf23]|\ud802[\udc58-\udc5f\udc79-\udc7f\udca7-\udcaf\udcfb-\udcff\udd16-\udd1b\uddbc\uddbd\uddc0-\uddcf\uddd2-\uddff\ude40-\ude48\ude7d\ude7e\ude9d-\ude9f\udeeb-\udeef\udf58-\udf5f\udf78-\udf7f\udfa9-\udfaf]|\ud803[\udcfa-\udcff\ude60-\ude7e\udf1d-\udf26\udf51-\udf54]|\ud804[\udc52-\udc65\udde1-\uddf4]|\ud805[\udf3a\udf3b]|\ud806[\udcea-\udcf2]|\ud807[\udc5a-\udc6c]|\ud81a[\udf5b-\udf61]|\ud81b[\ude80-\ude96]|\ud834[\udee0-\udef3\udf60-\udf78]|\ud83a[\udcc7-\udccf]|\ud83b[\udc71-\udcab\udcad-\udcaf\udcb1-\udcb4]|\ud83c[\udd00-\udd0c]"},{name:"P",alias:"Punctuation",bmp:"!-#%-\\*,-\\/:;\\?@\\[-\\]_\\{\\}¡§«¶·»¿;·՚-՟։֊־׀׃׆׳״؉؊،؍؛؞؟٪-٭۔܀-܍߷-߹࠰-࠾࡞।॥॰৽੶૰಄෴๏๚๛༄-༒༔༺-༽྅࿐-࿔࿙࿚၊-၏჻፠-፨᐀᙭᙮᚛᚜᛫-᛭᜵᜶។-៖៘-៚᠀-᠊᥄᥅᨞᨟᪠-᪦᪨-᪭᭚-᭠᯼-᯿᰻-᰿᱾᱿᳀-᳇᳓‐-‧‰-⁃⁅-⁑⁓-⁞⁽⁾₍₎⌈-⌋〈〉❨-❵⟅⟆⟦-⟯⦃-⦘⧘-⧛⧼⧽⳹-⳼⳾⳿⵰⸀-⸮⸰-⹎、-〃〈-】〔-〟〰〽゠・꓾꓿꘍-꘏꙳꙾꛲-꛷꡴-꡷꣎꣏꣸-꣺꣼꤮꤯꥟꧁-꧍꧞꧟꩜-꩟꫞꫟꫰꫱꯫﴾﴿︐-︙︰-﹒﹔-﹡﹣﹨﹪﹫！-＃％-＊，-／：；？＠［-］＿｛｝｟-･",astral:"\ud800[\udd00-\udd02\udf9f\udfd0]|𐕯|\ud802[\udc57\udd1f\udd3f\ude50-\ude58\ude7f\udef0-\udef6\udf39-\udf3f\udf99-\udf9c]|\ud803[\udf55-\udf59]|\ud804[\udc47-\udc4d\udcbb\udcbc\udcbe-\udcc1\udd40-\udd43\udd74\udd75\uddc5-\uddc8\uddcd\udddb\udddd-\udddf\ude38-\ude3d\udea9]|\ud805[\udc4b-\udc4f\udc5b\udc5d\udcc6\uddc1-\uddd7\ude41-\ude43\ude60-\ude6c\udf3c-\udf3e]|\ud806[\udc3b\ude3f-\ude46\ude9a-\ude9c\ude9e-\udea2]|\ud807[\udc41-\udc45\udc70\udc71\udef7\udef8]|\ud809[\udc70-\udc74]|\ud81a[\ude6e\ude6f\udef5\udf37-\udf3b\udf44]|\ud81b[\ude97-\ude9a]|𛲟|\ud836[\ude87-\ude8b]|\ud83a[\udd5e\udd5f]"},{name:"Pc",alias:"Connector_Punctuation",bmp:"_‿⁀⁔︳︴﹍-﹏＿"},{name:"Pd",alias:"Dash_Punctuation",bmp:"\\-֊־᐀᠆‐-―⸗⸚⸺⸻⹀〜〰゠︱︲﹘﹣－"},{name:"Pe",alias:"Close_Punctuation",bmp:"\\)\\]\\}༻༽᚜⁆⁾₎⌉⌋〉❩❫❭❯❱❳❵⟆⟧⟩⟫⟭⟯⦄⦆⦈⦊⦌⦎⦐⦒⦔⦖⦘⧙⧛⧽⸣⸥⸧⸩〉》」』】〕〗〙〛〞〟﴾︘︶︸︺︼︾﹀﹂﹄﹈﹚﹜﹞）］｝｠｣"},{name:"Pf",alias:"Final_Punctuation",bmp:"»’”›⸃⸅⸊⸍⸝⸡"},{name:"Pi",alias:"Initial_Punctuation",bmp:"«‘‛“‟‹⸂⸄⸉⸌⸜⸠"},{name:"Po",alias:"Other_Punctuation",bmp:"!-#%-'\\*,\\.\\/:;\\?@\\¡§¶·¿;·՚-՟։׀׃׆׳״؉؊،؍؛؞؟٪-٭۔܀-܍߷-߹࠰-࠾࡞।॥॰৽੶૰಄෴๏๚๛༄-༒༔྅࿐-࿔࿙࿚၊-၏჻፠-፨᙭᙮᛫-᛭᜵᜶។-៖៘-៚᠀-᠅᠇-᠊᥄᥅᨞᨟᪠-᪦᪨-᪭᭚-᭠᯼-᯿᰻-᰿᱾᱿᳀-᳇᳓‖‗†-‧‰-‸※-‾⁁-⁃⁇-⁑⁓⁕-⁞⳹-⳼⳾⳿⵰⸀⸁⸆-⸈⸋⸎-⸖⸘⸙⸛⸞⸟⸪-⸮⸰-⸹⸼-⸿⹁⹃-⹎、-〃〽・꓾꓿꘍-꘏꙳꙾꛲-꛷꡴-꡷꣎꣏꣸-꣺꣼꤮꤯꥟꧁-꧍꧞꧟꩜-꩟꫞꫟꫰꫱꯫︐-︖︙︰﹅﹆﹉-﹌﹐-﹒﹔-﹗﹟-﹡﹨﹪﹫！-＃％-＇＊，．／：；？＠＼｡､･",astral:"\ud800[\udd00-\udd02\udf9f\udfd0]|𐕯|\ud802[\udc57\udd1f\udd3f\ude50-\ude58\ude7f\udef0-\udef6\udf39-\udf3f\udf99-\udf9c]|\ud803[\udf55-\udf59]|\ud804[\udc47-\udc4d\udcbb\udcbc\udcbe-\udcc1\udd40-\udd43\udd74\udd75\uddc5-\uddc8\uddcd\udddb\udddd-\udddf\ude38-\ude3d\udea9]|\ud805[\udc4b-\udc4f\udc5b\udc5d\udcc6\uddc1-\uddd7\ude41-\ude43\ude60-\ude6c\udf3c-\udf3e]|\ud806[\udc3b\ude3f-\ude46\ude9a-\ude9c\ude9e-\udea2]|\ud807[\udc41-\udc45\udc70\udc71\udef7\udef8]|\ud809[\udc70-\udc74]|\ud81a[\ude6e\ude6f\udef5\udf37-\udf3b\udf44]|\ud81b[\ude97-\ude9a]|𛲟|\ud836[\ude87-\ude8b]|\ud83a[\udd5e\udd5f]"},{name:"Ps",alias:"Open_Punctuation",bmp:"\\(\\[\\{༺༼᚛‚„⁅⁽₍⌈⌊〈❨❪❬❮❰❲❴⟅⟦⟨⟪⟬⟮⦃⦅⦇⦉⦋⦍⦏⦑⦓⦕⦗⧘⧚⧼⸢⸤⸦⸨⹂〈《「『【〔〖〘〚〝﴿︗︵︷︹︻︽︿﹁﹃﹇﹙﹛﹝（［｛｟｢"},{name:"S",alias:"Symbol",bmp:"\\$\\+<->\\^`\\|~¢-¦¨©¬®-±´¸×÷˂-˅˒-˟˥-˫˭˯-˿͵΄΅϶҂֍-֏؆-؈؋؎؏۞۩۽۾߶߾߿৲৳৺৻૱୰௳-௺౿൏൹฿༁-༃༓༕-༗༚-༟༴༶༸྾-࿅࿇-࿌࿎࿏࿕-࿘႞႟᎐-᎙៛᥀᧞-᧿᭡-᭪᭴-᭼᾽᾿-῁῍-῏῝-῟῭-`´῾⁄⁒⁺-⁼₊-₌₠-₿℀℁℃-℆℈℉℔№-℘℞-℣℥℧℩℮℺℻⅀-⅄⅊-⅍⅏↊↋←-⌇⌌-⌨⌫-␦⑀-⑊⒜-ⓩ─-❧➔-⟄⟇-⟥⟰-⦂⦙-⧗⧜-⧻⧾-⭳⭶-⮕⮘-⯈⯊-⯾⳥-⳪⺀-⺙⺛-⻳⼀-⿕⿰-⿻〄〒〓〠〶〷〾〿゛゜㆐㆑㆖-㆟㇀-㇣㈀-㈞㈪-㉇㉐㉠-㉿㊊-㊰㋀-㋾㌀-㏿䷀-䷿꒐-꓆꜀-꜖꜠꜡꞉꞊꠨-꠫꠶-꠹꩷-꩹꭛﬩﮲-﯁﷼﷽﹢﹤-﹦﹩＄＋＜-＞＾｀｜～￠-￦￨-￮￼�",astral:"\ud800[\udd37-\udd3f\udd79-\udd89\udd8c-\udd8e\udd90-\udd9b\udda0\uddd0-\uddfc]|\ud802[\udc77\udc78\udec8]|𑜿|\ud81a[\udf3c-\udf3f\udf45]|𛲜|\ud834[\udc00-\udcf5\udd00-\udd26\udd29-\udd64\udd6a-\udd6c\udd83\udd84\udd8c-\udda9\uddae-\udde8\ude00-\ude41\ude45\udf00-\udf56]|\ud835[\udec1\udedb\udefb\udf15\udf35\udf4f\udf6f\udf89\udfa9\udfc3]|\ud836[\udc00-\uddff\ude37-\ude3a\ude6d-\ude74\ude76-\ude83\ude85\ude86]|\ud83b[\udcac\udcb0\udef0\udef1]|\ud83c[\udc00-\udc2b\udc30-\udc93\udca0-\udcae\udcb1-\udcbf\udcc1-\udccf\udcd1-\udcf5\udd10-\udd6b\udd70-\uddac\udde6-\ude02\ude10-\ude3b\ude40-\ude48\ude50\ude51\ude60-\ude65\udf00-\udfff]|\ud83d[\udc00-\uded4\udee0-\udeec\udef0-\udef9\udf00-\udf73\udf80-\udfd8]|\ud83e[\udc00-\udc0b\udc10-\udc47\udc50-\udc59\udc60-\udc87\udc90-\udcad\udd00-\udd0b\udd10-\udd3e\udd40-\udd70\udd73-\udd76\udd7a\udd7c-\udda2\uddb0-\uddb9\uddc0-\uddc2\uddd0-\uddff\ude60-\ude6d]"},{name:"Sc",alias:"Currency_Symbol",bmp:"\\$¢-¥֏؋߾߿৲৳৻૱௹฿៛₠-₿꠸﷼﹩＄￠￡￥￦",astral:"𞲰"},{name:"Sk",alias:"Modifier_Symbol",bmp:"\\^`¨¯´¸˂-˅˒-˟˥-˫˭˯-˿͵΄΅᾽᾿-῁῍-῏῝-῟῭-`´῾゛゜꜀-꜖꜠꜡꞉꞊꭛﮲-﯁＾｀￣",astral:"\ud83c[\udffb-\udfff]"},{name:"Sm",alias:"Math_Symbol",bmp:"\\+<->\\|~¬±×÷϶؆-؈⁄⁒⁺-⁼₊-₌℘⅀-⅄⅋←-↔↚↛↠↣↦↮⇎⇏⇒⇔⇴-⋿⌠⌡⍼⎛-⎳⏜-⏡▷◁◸-◿♯⟀-⟄⟇-⟥⟰-⟿⤀-⦂⦙-⧗⧜-⧻⧾-⫿⬰-⭄⭇-⭌﬩﹢﹤-﹦＋＜-＞｜～￢￩-￬",astral:"\ud835[\udec1\udedb\udefb\udf15\udf35\udf4f\udf6f\udf89\udfa9\udfc3]|\ud83b[\udef0\udef1]"},{name:"So",alias:"Other_Symbol",bmp:"¦©®°҂֍֎؎؏۞۩۽۾߶৺୰௳-௸௺౿൏൹༁-༃༓༕-༗༚-༟༴༶༸྾-࿅࿇-࿌࿎࿏࿕-࿘႞႟᎐-᎙᥀᧞-᧿᭡-᭪᭴-᭼℀℁℃-℆℈℉℔№℗℞-℣℥℧℩℮℺℻⅊⅌⅍⅏↊↋↕-↙↜-↟↡↢↤↥↧-↭↯-⇍⇐⇑⇓⇕-⇳⌀-⌇⌌-⌟⌢-⌨⌫-⍻⍽-⎚⎴-⏛⏢-␦⑀-⑊⒜-ⓩ─-▶▸-◀◂-◷☀-♮♰-❧➔-➿⠀-⣿⬀-⬯⭅⭆⭍-⭳⭶-⮕⮘-⯈⯊-⯾⳥-⳪⺀-⺙⺛-⻳⼀-⿕⿰-⿻〄〒〓〠〶〷〾〿㆐㆑㆖-㆟㇀-㇣㈀-㈞㈪-㉇㉐㉠-㉿㊊-㊰㋀-㋾㌀-㏿䷀-䷿꒐-꓆꠨-꠫꠶꠷꠹꩷-꩹﷽￤￨￭￮￼�",astral:"\ud800[\udd37-\udd3f\udd79-\udd89\udd8c-\udd8e\udd90-\udd9b\udda0\uddd0-\uddfc]|\ud802[\udc77\udc78\udec8]|𑜿|\ud81a[\udf3c-\udf3f\udf45]|𛲜|\ud834[\udc00-\udcf5\udd00-\udd26\udd29-\udd64\udd6a-\udd6c\udd83\udd84\udd8c-\udda9\uddae-\udde8\ude00-\ude41\ude45\udf00-\udf56]|\ud836[\udc00-\uddff\ude37-\ude3a\ude6d-\ude74\ude76-\ude83\ude85\ude86]|𞲬|\ud83c[\udc00-\udc2b\udc30-\udc93\udca0-\udcae\udcb1-\udcbf\udcc1-\udccf\udcd1-\udcf5\udd10-\udd6b\udd70-\uddac\udde6-\ude02\ude10-\ude3b\ude40-\ude48\ude50\ude51\ude60-\ude65\udf00-\udffa]|\ud83d[\udc00-\uded4\udee0-\udeec\udef0-\udef9\udf00-\udf73\udf80-\udfd8]|\ud83e[\udc00-\udc0b\udc10-\udc47\udc50-\udc59\udc60-\udc87\udc90-\udcad\udd00-\udd0b\udd10-\udd3e\udd40-\udd70\udd73-\udd76\udd7a\udd7c-\udda2\uddb0-\uddb9\uddc0-\uddc2\uddd0-\uddff\ude60-\ude6d]"},{name:"Z",alias:"Separator",bmp:"    - \u2028\u2029  　"},{name:"Zl",alias:"Line_Separator",bmp:"\u2028"},{name:"Zp",alias:"Paragraph_Separator",bmp:"\u2029"},{name:"Zs",alias:"Space_Separator",bmp:"    -   　"}]},1305:function(e,u,d){"use strict";var t=d(197);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var n=t(d(1306));
/*!
 * XRegExp Unicode Properties 4.2.4
 * <xregexp.com>
 * Steven Levithan (c) 2012-present MIT License
 * Unicode data by Mathias Bynens <mathiasbynens.be>
 */u.default=function(e){if(!e.addUnicodeData)throw new ReferenceError("Unicode Base must be loaded before Unicode Properties");var u=n.default;u.push({name:"Assigned",inverseOf:"Cn"}),e.addUnicodeData(u)},e.exports=u.default},1306:function(e,u){e.exports=[{name:"ASCII",bmp:"\0-"},{name:"Alphabetic",bmp:"A-Za-zªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͅͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙՠ-ֈְ-ׇֽֿׁׂׅׄא-תׯ-ײؐ-ؚؠ-ٗٙ-ٟٮ-ۓە-ۜۡ-ۭۨ-ۯۺ-ۼۿܐ-ܿݍ-ޱߊ-ߪߴߵߺࠀ-ࠗࠚ-ࠬࡀ-ࡘࡠ-ࡪࢠ-ࢴࢶ-ࢽࣔ-ࣣࣟ-ࣰࣩ-ऻऽ-ौॎ-ॐॕ-ॣॱ-ঃঅ-ঌএঐও-নপ-রলশ-হঽ-ৄেৈোৌৎৗড়ঢ়য়-ৣৰৱৼਁ-ਃਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਾ-ੂੇੈੋੌੑਖ਼-ੜਫ਼ੰ-ੵઁ-ઃઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽ-ૅે-ૉોૌૐૠ-ૣૹ-ૼଁ-ଃଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽ-ୄେୈୋୌୖୗଡ଼ଢ଼ୟ-ୣୱஂஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹா-ூெ-ைொ-ௌௐௗఀ-ఃఅ-ఌఎ-ఐఒ-నప-హఽ-ౄె-ైొ-ౌౕౖౘ-ౚౠ-ౣಀ-ಃಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽ-ೄೆ-ೈೊ-ೌೕೖೞೠ-ೣೱೲഀ-ഃഅ-ഌഎ-ഐഒ-ഺഽ-ൄെ-ൈൊ-ൌൎൔ-ൗൟ-ൣൺ-ൿංඃඅ-ඖක-නඳ-රලව-ෆා-ුූෘ-ෟෲෳก-ฺเ-ๆํກຂຄງຈຊຍດ-ທນ-ຟມ-ຣລວສຫອ-ູົ-ຽເ-ໄໆໍໜ-ໟༀཀ-ཇཉ-ཬཱ-ཱྀྈ-ྗྙ-ྼက-ံးျ-ဿၐ-ၢၥ-ၨၮ-ႆႎႜႝႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚ፟ᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛸᜀ-ᜌᜎ-ᜓᜠ-ᜳᝀ-ᝓᝠ-ᝬᝮ-ᝰᝲᝳក-ឳា-ៈៗៜᠠ-ᡸᢀ-ᢪᢰ-ᣵᤀ-ᤞᤠ-ᤫᤰ-ᤸᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨛᨠ-ᩞᩡ-ᩴᪧᬀ-ᬳᬵ-ᭃᭅ-ᭋᮀ-ᮩᮬ-ᮯᮺ-ᯥᯧ-ᯱᰀ-ᰵᱍ-ᱏᱚ-ᱽᲀ-ᲈᲐ-ᲺᲽ-Ჿᳩ-ᳬᳮ-ᳳᳵᳶᴀ-ᶿᷧ-ᷴḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕℙ-ℝℤΩℨK-ℭℯ-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⒶ-ⓩⰀ-Ⱞⰰ-ⱞⱠ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞⷠ-ⷿⸯ々-〇〡-〩〱-〵〸-〼ぁ-ゖゝ-ゟァ-ヺー-ヿㄅ-ㄯㄱ-ㆎㆠ-ㆺㇰ-ㇿ㐀-䶵一-鿯ꀀ-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙴ-ꙻꙿ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꞹꟷ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠧꡀ-ꡳꢀ-ꣃꣅꣲ-ꣷꣻꣽꣾꤊ-ꤪꤰ-ꥒꥠ-ꥼꦀ-ꦲꦴ-ꦿꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨶꩀ-ꩍꩠ-ꩶꩺꩾ-ꪾꫀꫂꫛ-ꫝꫠ-ꫯꫲ-ꫵꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭥꭰ-ꯪ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ",astral:"\ud800[\udc00-\udc0b\udc0d-\udc26\udc28-\udc3a\udc3c\udc3d\udc3f-\udc4d\udc50-\udc5d\udc80-\udcfa\udd40-\udd74\ude80-\ude9c\udea0-\uded0\udf00-\udf1f\udf2d-\udf4a\udf50-\udf7a\udf80-\udf9d\udfa0-\udfc3\udfc8-\udfcf\udfd1-\udfd5]|\ud801[\udc00-\udc9d\udcb0-\udcd3\udcd8-\udcfb\udd00-\udd27\udd30-\udd63\ude00-\udf36\udf40-\udf55\udf60-\udf67]|\ud802[\udc00-\udc05\udc08\udc0a-\udc35\udc37\udc38\udc3c\udc3f-\udc55\udc60-\udc76\udc80-\udc9e\udce0-\udcf2\udcf4\udcf5\udd00-\udd15\udd20-\udd39\udd80-\uddb7\uddbe\uddbf\ude00-\ude03\ude05\ude06\ude0c-\ude13\ude15-\ude17\ude19-\ude35\ude60-\ude7c\ude80-\ude9c\udec0-\udec7\udec9-\udee4\udf00-\udf35\udf40-\udf55\udf60-\udf72\udf80-\udf91]|\ud803[\udc00-\udc48\udc80-\udcb2\udcc0-\udcf2\udd00-\udd27\udf00-\udf1c\udf27\udf30-\udf45]|\ud804[\udc00-\udc45\udc82-\udcb8\udcd0-\udce8\udd00-\udd32\udd44-\udd46\udd50-\udd72\udd76\udd80-\uddbf\uddc1-\uddc4\uddda\udddc\ude00-\ude11\ude13-\ude34\ude37\ude3e\ude80-\ude86\ude88\ude8a-\ude8d\ude8f-\ude9d\ude9f-\udea8\udeb0-\udee8\udf00-\udf03\udf05-\udf0c\udf0f\udf10\udf13-\udf28\udf2a-\udf30\udf32\udf33\udf35-\udf39\udf3d-\udf44\udf47\udf48\udf4b\udf4c\udf50\udf57\udf5d-\udf63]|\ud805[\udc00-\udc41\udc43-\udc45\udc47-\udc4a\udc80-\udcc1\udcc4\udcc5\udcc7\udd80-\uddb5\uddb8-\uddbe\uddd8-\udddd\ude00-\ude3e\ude40\ude44\ude80-\udeb5\udf00-\udf1a\udf1d-\udf2a]|\ud806[\udc00-\udc38\udca0-\udcdf\udcff\ude00-\ude32\ude35-\ude3e\ude50-\ude83\ude86-\ude97\ude9d\udec0-\udef8]|\ud807[\udc00-\udc08\udc0a-\udc36\udc38-\udc3e\udc40\udc72-\udc8f\udc92-\udca7\udca9-\udcb6\udd00-\udd06\udd08\udd09\udd0b-\udd36\udd3a\udd3c\udd3d\udd3f-\udd41\udd43\udd46\udd47\udd60-\udd65\udd67\udd68\udd6a-\udd8e\udd90\udd91\udd93-\udd96\udd98\udee0-\udef6]|\ud808[\udc00-\udf99]|\ud809[\udc00-\udc6e\udc80-\udd43]|[\ud80c\ud81c-\ud820\ud840-\ud868\ud86a-\ud86c\ud86f-\ud872\ud874-\ud879][\udc00-\udfff]|\ud80d[\udc00-\udc2e]|\ud811[\udc00-\ude46]|\ud81a[\udc00-\ude38\ude40-\ude5e\uded0-\udeed\udf00-\udf36\udf40-\udf43\udf63-\udf77\udf7d-\udf8f]|\ud81b[\ude40-\ude7f\udf00-\udf44\udf50-\udf7e\udf93-\udf9f\udfe0\udfe1]|\ud821[\udc00-\udff1]|\ud822[\udc00-\udef2]|\ud82c[\udc00-\udd1e\udd70-\udefb]|\ud82f[\udc00-\udc6a\udc70-\udc7c\udc80-\udc88\udc90-\udc99\udc9e]|\ud835[\udc00-\udc54\udc56-\udc9c\udc9e\udc9f\udca2\udca5\udca6\udca9-\udcac\udcae-\udcb9\udcbb\udcbd-\udcc3\udcc5-\udd05\udd07-\udd0a\udd0d-\udd14\udd16-\udd1c\udd1e-\udd39\udd3b-\udd3e\udd40-\udd44\udd46\udd4a-\udd50\udd52-\udea5\udea8-\udec0\udec2-\udeda\udedc-\udefa\udefc-\udf14\udf16-\udf34\udf36-\udf4e\udf50-\udf6e\udf70-\udf88\udf8a-\udfa8\udfaa-\udfc2\udfc4-\udfcb]|\ud838[\udc00-\udc06\udc08-\udc18\udc1b-\udc21\udc23\udc24\udc26-\udc2a]|\ud83a[\udc00-\udcc4\udd00-\udd43\udd47]|\ud83b[\ude00-\ude03\ude05-\ude1f\ude21\ude22\ude24\ude27\ude29-\ude32\ude34-\ude37\ude39\ude3b\ude42\ude47\ude49\ude4b\ude4d-\ude4f\ude51\ude52\ude54\ude57\ude59\ude5b\ude5d\ude5f\ude61\ude62\ude64\ude67-\ude6a\ude6c-\ude72\ude74-\ude77\ude79-\ude7c\ude7e\ude80-\ude89\ude8b-\ude9b\udea1-\udea3\udea5-\udea9\udeab-\udebb]|\ud83c[\udd30-\udd49\udd50-\udd69\udd70-\udd89]|\ud869[\udc00-\uded6\udf00-\udfff]|\ud86d[\udc00-\udf34\udf40-\udfff]|\ud86e[\udc00-\udc1d\udc20-\udfff]|\ud873[\udc00-\udea1\udeb0-\udfff]|\ud87a[\udc00-\udfe0]|\ud87e[\udc00-\ude1d]"},{name:"Any",isBmpLast:!0,bmp:"\0-￿",astral:"[\ud800-\udbff][\udc00-\udfff]"},{name:"Default_Ignorable_Code_Point",bmp:"­͏؜ᅟᅠ឴឵᠋-᠎​-‏‪-‮⁠-⁯ㅤ︀-️\ufeffﾠ￰-￸",astral:"\ud82f[\udca0-\udca3]|\ud834[\udd73-\udd7a]|[\udb40-\udb43][\udc00-\udfff]"},{name:"Lowercase",bmp:"a-zªµºß-öø-ÿāăąćĉċčďđēĕėęěĝğġģĥħĩīĭįıĳĵķĸĺļľŀłńņňŉŋōŏőœŕŗřśŝşšţťŧũūŭůűųŵŷźżž-ƀƃƅƈƌƍƒƕƙ-ƛƞơƣƥƨƪƫƭưƴƶƹƺƽ-ƿǆǉǌǎǐǒǔǖǘǚǜǝǟǡǣǥǧǩǫǭǯǰǳǵǹǻǽǿȁȃȅȇȉȋȍȏȑȓȕȗșțȝȟȡȣȥȧȩȫȭȯȱȳ-ȹȼȿɀɂɇɉɋɍɏ-ʓʕ-ʸˀˁˠ-ˤͅͱͳͷͺ-ͽΐά-ώϐϑϕ-ϗϙϛϝϟϡϣϥϧϩϫϭϯ-ϳϵϸϻϼа-џѡѣѥѧѩѫѭѯѱѳѵѷѹѻѽѿҁҋҍҏґғҕҗҙқҝҟҡңҥҧҩҫҭүұҳҵҷҹһҽҿӂӄӆӈӊӌӎӏӑӓӕӗәӛӝӟӡӣӥӧөӫӭӯӱӳӵӷӹӻӽӿԁԃԅԇԉԋԍԏԑԓԕԗԙԛԝԟԡԣԥԧԩԫԭԯՠ-ֈა-ჺჽ-ჿᏸ-ᏽᲀ-ᲈᴀ-ᶿḁḃḅḇḉḋḍḏḑḓḕḗḙḛḝḟḡḣḥḧḩḫḭḯḱḳḵḷḹḻḽḿṁṃṅṇṉṋṍṏṑṓṕṗṙṛṝṟṡṣṥṧṩṫṭṯṱṳṵṷṹṻṽṿẁẃẅẇẉẋẍẏẑẓẕ-ẝẟạảấầẩẫậắằẳẵặẹẻẽếềểễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹỻỽỿ-ἇἐ-ἕἠ-ἧἰ-ἷὀ-ὅὐ-ὗὠ-ὧὰ-ώᾀ-ᾇᾐ-ᾗᾠ-ᾧᾰ-ᾴᾶᾷιῂ-ῄῆῇῐ-ΐῖῗῠ-ῧῲ-ῴῶῷⁱⁿₐ-ₜℊℎℏℓℯℴℹℼℽⅆ-ⅉⅎⅰ-ⅿↄⓐ-ⓩⰰ-ⱞⱡⱥⱦⱨⱪⱬⱱⱳⱴⱶ-ⱽⲁⲃⲅⲇⲉⲋⲍⲏⲑⲓⲕⲗⲙⲛⲝⲟⲡⲣⲥⲧⲩⲫⲭⲯⲱⲳⲵⲷⲹⲻⲽⲿⳁⳃⳅⳇⳉⳋⳍⳏⳑⳓⳕⳗⳙⳛⳝⳟⳡⳣⳤⳬⳮⳳⴀ-ⴥⴧⴭꙁꙃꙅꙇꙉꙋꙍꙏꙑꙓꙕꙗꙙꙛꙝꙟꙡꙣꙥꙧꙩꙫꙭꚁꚃꚅꚇꚉꚋꚍꚏꚑꚓꚕꚗꚙꚛ-ꚝꜣꜥꜧꜩꜫꜭꜯ-ꜱꜳꜵꜷꜹꜻꜽꜿꝁꝃꝅꝇꝉꝋꝍꝏꝑꝓꝕꝗꝙꝛꝝꝟꝡꝣꝥꝧꝩꝫꝭꝯ-ꝸꝺꝼꝿꞁꞃꞅꞇꞌꞎꞑꞓ-ꞕꞗꞙꞛꞝꞟꞡꞣꞥꞧꞩꞯꞵꞷꞹꟸ-ꟺꬰ-ꭚꭜ-ꭥꭰ-ꮿﬀ-ﬆﬓ-ﬗａ-ｚ",astral:"\ud801[\udc28-\udc4f\udcd8-\udcfb]|\ud803[\udcc0-\udcf2]|\ud806[\udcc0-\udcdf]|\ud81b[\ude60-\ude7f]|\ud835[\udc1a-\udc33\udc4e-\udc54\udc56-\udc67\udc82-\udc9b\udcb6-\udcb9\udcbb\udcbd-\udcc3\udcc5-\udccf\udcea-\udd03\udd1e-\udd37\udd52-\udd6b\udd86-\udd9f\uddba-\uddd3\uddee-\ude07\ude22-\ude3b\ude56-\ude6f\ude8a-\udea5\udec2-\udeda\udedc-\udee1\udefc-\udf14\udf16-\udf1b\udf36-\udf4e\udf50-\udf55\udf70-\udf88\udf8a-\udf8f\udfaa-\udfc2\udfc4-\udfc9\udfcb]|\ud83a[\udd22-\udd43]"},{name:"Noncharacter_Code_Point",bmp:"﷐-﷯￾￿",astral:"[\ud83f\ud87f\ud8bf\ud8ff\ud93f\ud97f\ud9bf\ud9ff\uda3f\uda7f\udabf\udaff\udb3f\udb7f\udbbf\udbff][\udffe\udfff]"},{name:"Uppercase",bmp:"A-ZÀ-ÖØ-ÞĀĂĄĆĈĊČĎĐĒĔĖĘĚĜĞĠĢĤĦĨĪĬĮİĲĴĶĹĻĽĿŁŃŅŇŊŌŎŐŒŔŖŘŚŜŞŠŢŤŦŨŪŬŮŰŲŴŶŸŹŻŽƁƂƄƆƇƉ-ƋƎ-ƑƓƔƖ-ƘƜƝƟƠƢƤƦƧƩƬƮƯƱ-ƳƵƷƸƼǄǇǊǍǏǑǓǕǗǙǛǞǠǢǤǦǨǪǬǮǱǴǶ-ǸǺǼǾȀȂȄȆȈȊȌȎȐȒȔȖȘȚȜȞȠȢȤȦȨȪȬȮȰȲȺȻȽȾɁɃ-ɆɈɊɌɎͰͲͶͿΆΈ-ΊΌΎΏΑ-ΡΣ-ΫϏϒ-ϔϘϚϜϞϠϢϤϦϨϪϬϮϴϷϹϺϽ-ЯѠѢѤѦѨѪѬѮѰѲѴѶѸѺѼѾҀҊҌҎҐҒҔҖҘҚҜҞҠҢҤҦҨҪҬҮҰҲҴҶҸҺҼҾӀӁӃӅӇӉӋӍӐӒӔӖӘӚӜӞӠӢӤӦӨӪӬӮӰӲӴӶӸӺӼӾԀԂԄԆԈԊԌԎԐԒԔԖԘԚԜԞԠԢԤԦԨԪԬԮԱ-ՖႠ-ჅჇჍᎠ-ᏵᲐ-ᲺᲽ-ᲿḀḂḄḆḈḊḌḎḐḒḔḖḘḚḜḞḠḢḤḦḨḪḬḮḰḲḴḶḸḺḼḾṀṂṄṆṈṊṌṎṐṒṔṖṘṚṜṞṠṢṤṦṨṪṬṮṰṲṴṶṸṺṼṾẀẂẄẆẈẊẌẎẐẒẔẞẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼẾỀỂỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴỶỸỺỼỾἈ-ἏἘ-ἝἨ-ἯἸ-ἿὈ-ὍὙὛὝὟὨ-ὯᾸ-ΆῈ-ΉῘ-ΊῨ-ῬῸ-Ώℂℇℋ-ℍℐ-ℒℕℙ-ℝℤΩℨK-ℭℰ-ℳℾℿⅅⅠ-ⅯↃⒶ-ⓏⰀ-ⰮⱠⱢ-ⱤⱧⱩⱫⱭ-ⱰⱲⱵⱾ-ⲀⲂⲄⲆⲈⲊⲌⲎⲐⲒⲔⲖⲘⲚⲜⲞⲠⲢⲤⲦⲨⲪⲬⲮⲰⲲⲴⲶⲸⲺⲼⲾⳀⳂⳄⳆⳈⳊⳌⳎⳐⳒⳔⳖⳘⳚⳜⳞⳠⳢⳫⳭⳲꙀꙂꙄꙆꙈꙊꙌꙎꙐꙒꙔꙖꙘꙚꙜꙞꙠꙢꙤꙦꙨꙪꙬꚀꚂꚄꚆꚈꚊꚌꚎꚐꚒꚔꚖꚘꚚꜢꜤꜦꜨꜪꜬꜮꜲꜴꜶꜸꜺꜼꜾꝀꝂꝄꝆꝈꝊꝌꝎꝐꝒꝔꝖꝘꝚꝜꝞꝠꝢꝤꝦꝨꝪꝬꝮꝹꝻꝽꝾꞀꞂꞄꞆꞋꞍꞐꞒꞖꞘꞚꞜꞞꞠꞢꞤꞦꞨꞪ-ꞮꞰ-ꞴꞶꞸＡ-Ｚ",astral:"\ud801[\udc00-\udc27\udcb0-\udcd3]|\ud803[\udc80-\udcb2]|\ud806[\udca0-\udcbf]|\ud81b[\ude40-\ude5f]|\ud835[\udc00-\udc19\udc34-\udc4d\udc68-\udc81\udc9c\udc9e\udc9f\udca2\udca5\udca6\udca9-\udcac\udcae-\udcb5\udcd0-\udce9\udd04\udd05\udd07-\udd0a\udd0d-\udd14\udd16-\udd1c\udd38\udd39\udd3b-\udd3e\udd40-\udd44\udd46\udd4a-\udd50\udd6c-\udd85\udda0-\uddb9\uddd4-\udded\ude08-\ude21\ude3c-\ude55\ude70-\ude89\udea8-\udec0\udee2-\udefa\udf1c-\udf34\udf56-\udf6e\udf90-\udfa8\udfca]|\ud83a[\udd00-\udd21]|\ud83c[\udd30-\udd49\udd50-\udd69\udd70-\udd89]"},{name:"White_Space",bmp:"\t-\r    - \u2028\u2029  　"}]},1307:function(e,u,d){"use strict";var t=d(197);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var n=t(d(1308));
/*!
 * XRegExp Unicode Scripts 4.2.4
 * <xregexp.com>
 * Steven Levithan (c) 2010-present MIT License
 * Unicode data by Mathias Bynens <mathiasbynens.be>
 */u.default=function(e){if(!e.addUnicodeData)throw new ReferenceError("Unicode Base must be loaded before Unicode Scripts");e.addUnicodeData(n.default)},e.exports=u.default},1308:function(e,u){e.exports=[{name:"Adlam",astral:"\ud83a[\udd00-\udd4a\udd50-\udd59\udd5e\udd5f]"},{name:"Ahom",astral:"\ud805[\udf00-\udf1a\udf1d-\udf2b\udf30-\udf3f]"},{name:"Anatolian_Hieroglyphs",astral:"\ud811[\udc00-\ude46]"},{name:"Arabic",bmp:"؀-؄؆-؋؍-ؚ؜؞ؠ-ؿف-يٖ-ٯٱ-ۜ۞-ۿݐ-ݿࢠ-ࢴࢶ-ࢽ࣓-ࣣ࣡-ࣿﭐ-﯁ﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-﷽ﹰ-ﹴﹶ-ﻼ",astral:"\ud803[\ude60-\ude7e]|\ud83b[\ude00-\ude03\ude05-\ude1f\ude21\ude22\ude24\ude27\ude29-\ude32\ude34-\ude37\ude39\ude3b\ude42\ude47\ude49\ude4b\ude4d-\ude4f\ude51\ude52\ude54\ude57\ude59\ude5b\ude5d\ude5f\ude61\ude62\ude64\ude67-\ude6a\ude6c-\ude72\ude74-\ude77\ude79-\ude7c\ude7e\ude80-\ude89\ude8b-\ude9b\udea1-\udea3\udea5-\udea9\udeab-\udebb\udef0\udef1]"},{name:"Armenian",bmp:"Ա-Ֆՙ-ֈ֊֍-֏ﬓ-ﬗ"},{name:"Avestan",astral:"\ud802[\udf00-\udf35\udf39-\udf3f]"},{name:"Balinese",bmp:"ᬀ-ᭋ᭐-᭼"},{name:"Bamum",bmp:"ꚠ-꛷",astral:"\ud81a[\udc00-\ude38]"},{name:"Bassa_Vah",astral:"\ud81a[\uded0-\udeed\udef0-\udef5]"},{name:"Batak",bmp:"ᯀ-᯳᯼-᯿"},{name:"Bengali",bmp:"ঀ-ঃঅ-ঌএঐও-নপ-রলশ-হ়-ৄেৈো-ৎৗড়ঢ়য়-ৣ০-৾"},{name:"Bhaiksuki",astral:"\ud807[\udc00-\udc08\udc0a-\udc36\udc38-\udc45\udc50-\udc6c]"},{name:"Bopomofo",bmp:"˪˫ㄅ-ㄯㆠ-ㆺ"},{name:"Brahmi",astral:"\ud804[\udc00-\udc4d\udc52-\udc6f\udc7f]"},{name:"Braille",bmp:"⠀-⣿"},{name:"Buginese",bmp:"ᨀ-ᨛ᨞᨟"},{name:"Buhid",bmp:"ᝀ-ᝓ"},{name:"Canadian_Aboriginal",bmp:"᐀-ᙿᢰ-ᣵ"},{name:"Carian",astral:"\ud800[\udea0-\uded0]"},{name:"Caucasian_Albanian",astral:"\ud801[\udd30-\udd63\udd6f]"},{name:"Chakma",astral:"\ud804[\udd00-\udd34\udd36-\udd46]"},{name:"Cham",bmp:"ꨀ-ꨶꩀ-ꩍ꩐-꩙꩜-꩟"},{name:"Cherokee",bmp:"Ꭰ-Ᏽᏸ-ᏽꭰ-ꮿ"},{name:"Common",bmp:"\0-@\\[-`\\{-©«-¹»-¿×÷ʹ-˟˥-˩ˬ-˿ʹ;΅·։؅،؛؟ـ۝࣢।॥฿࿕-࿘჻᛫-᛭᜵᜶᠂᠃᠅᳓᳡ᳩ-ᳬᳮ-ᳳᳵ-᳷ -​‎-⁤⁦-⁰⁴-⁾₀-₎₠-₿℀-℥℧-℩ℬ-ℱℳ-⅍⅏-⅟↉-↋←-␦⑀-⑊①-⟿⤀-⭳⭶-⮕⮘-⯈⯊-⯾⸀-⹎⿰-⿻　-〄〆〈-〠〰-〷〼-〿゛゜゠・ー㆐-㆟㇀-㇣㈠-㉟㉿-㋏㍘-㏿䷀-䷿꜀-꜡ꞈ-꞊꠰-꠹꤮ꧏ꭛﴾﴿︐-︙︰-﹒﹔-﹦﹨-﹫\ufeff！-＠［-｀｛-･ｰﾞﾟ￠-￦￨-￮￹-�",astral:"\ud800[\udd00-\udd02\udd07-\udd33\udd37-\udd3f\udd90-\udd9b\uddd0-\uddfc\udee1-\udefb]|\ud82f[\udca0-\udca3]|\ud834[\udc00-\udcf5\udd00-\udd26\udd29-\udd66\udd6a-\udd7a\udd83\udd84\udd8c-\udda9\uddae-\udde8\udee0-\udef3\udf00-\udf56\udf60-\udf78]|\ud835[\udc00-\udc54\udc56-\udc9c\udc9e\udc9f\udca2\udca5\udca6\udca9-\udcac\udcae-\udcb9\udcbb\udcbd-\udcc3\udcc5-\udd05\udd07-\udd0a\udd0d-\udd14\udd16-\udd1c\udd1e-\udd39\udd3b-\udd3e\udd40-\udd44\udd46\udd4a-\udd50\udd52-\udea5\udea8-\udfcb\udfce-\udfff]|\ud83b[\udc71-\udcb4]|\ud83c[\udc00-\udc2b\udc30-\udc93\udca0-\udcae\udcb1-\udcbf\udcc1-\udccf\udcd1-\udcf5\udd00-\udd0c\udd10-\udd6b\udd70-\uddac\udde6-\uddff\ude01\ude02\ude10-\ude3b\ude40-\ude48\ude50\ude51\ude60-\ude65\udf00-\udfff]|\ud83d[\udc00-\uded4\udee0-\udeec\udef0-\udef9\udf00-\udf73\udf80-\udfd8]|\ud83e[\udc00-\udc0b\udc10-\udc47\udc50-\udc59\udc60-\udc87\udc90-\udcad\udd00-\udd0b\udd10-\udd3e\udd40-\udd70\udd73-\udd76\udd7a\udd7c-\udda2\uddb0-\uddb9\uddc0-\uddc2\uddd0-\uddff\ude60-\ude6d]|\udb40[\udc01\udc20-\udc7f]"},{name:"Coptic",bmp:"Ϣ-ϯⲀ-ⳳ⳹-⳿"},{name:"Cuneiform",astral:"\ud808[\udc00-\udf99]|\ud809[\udc00-\udc6e\udc70-\udc74\udc80-\udd43]"},{name:"Cypriot",astral:"\ud802[\udc00-\udc05\udc08\udc0a-\udc35\udc37\udc38\udc3c\udc3f]"},{name:"Cyrillic",bmp:"Ѐ-҄҇-ԯᲀ-ᲈᴫᵸⷠ-ⷿꙀ-ꚟ︮︯"},{name:"Deseret",astral:"\ud801[\udc00-\udc4f]"},{name:"Devanagari",bmp:"ऀ-ॐ॓-ॣ०-ॿ꣠-ꣿ"},{name:"Dogra",astral:"\ud806[\udc00-\udc3b]"},{name:"Duployan",astral:"\ud82f[\udc00-\udc6a\udc70-\udc7c\udc80-\udc88\udc90-\udc99\udc9c-\udc9f]"},{name:"Egyptian_Hieroglyphs",astral:"\ud80c[\udc00-\udfff]|\ud80d[\udc00-\udc2e]"},{name:"Elbasan",astral:"\ud801[\udd00-\udd27]"},{name:"Ethiopic",bmp:"ሀ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚ፝-፼ᎀ-᎙ⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮ"},{name:"Georgian",bmp:"Ⴀ-ჅჇჍა-ჺჼ-ჿᲐ-ᲺᲽ-Ჿⴀ-ⴥⴧⴭ"},{name:"Glagolitic",bmp:"Ⰰ-Ⱞⰰ-ⱞ",astral:"\ud838[\udc00-\udc06\udc08-\udc18\udc1b-\udc21\udc23\udc24\udc26-\udc2a]"},{name:"Gothic",astral:"\ud800[\udf30-\udf4a]"},{name:"Grantha",astral:"\ud804[\udf00-\udf03\udf05-\udf0c\udf0f\udf10\udf13-\udf28\udf2a-\udf30\udf32\udf33\udf35-\udf39\udf3c-\udf44\udf47\udf48\udf4b-\udf4d\udf50\udf57\udf5d-\udf63\udf66-\udf6c\udf70-\udf74]"},{name:"Greek",bmp:"Ͱ-ͳ͵-ͷͺ-ͽͿ΄ΆΈ-ΊΌΎ-ΡΣ-ϡϰ-Ͽᴦ-ᴪᵝ-ᵡᵦ-ᵪᶿἀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ῄῆ-ΐῖ-Ί῝-`ῲ-ῴῶ-῾Ωꭥ",astral:"\ud800[\udd40-\udd8e\udda0]|\ud834[\ude00-\ude45]"},{name:"Gujarati",bmp:"ઁ-ઃઅ-ઍએ-ઑઓ-નપ-રલળવ-હ઼-ૅે-ૉો-્ૐૠ-ૣ૦-૱ૹ-૿"},{name:"Gunjala_Gondi",astral:"\ud807[\udd60-\udd65\udd67\udd68\udd6a-\udd8e\udd90\udd91\udd93-\udd98\udda0-\udda9]"},{name:"Gurmukhi",bmp:"ਁ-ਃਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹ਼ਾ-ੂੇੈੋ-੍ੑਖ਼-ੜਫ਼੦-੶"},{name:"Han",bmp:"⺀-⺙⺛-⻳⼀-⿕々〇〡-〩〸-〻㐀-䶵一-鿯豈-舘並-龎",astral:"[\ud840-\ud868\ud86a-\ud86c\ud86f-\ud872\ud874-\ud879][\udc00-\udfff]|\ud869[\udc00-\uded6\udf00-\udfff]|\ud86d[\udc00-\udf34\udf40-\udfff]|\ud86e[\udc00-\udc1d\udc20-\udfff]|\ud873[\udc00-\udea1\udeb0-\udfff]|\ud87a[\udc00-\udfe0]|\ud87e[\udc00-\ude1d]"},{name:"Hangul",bmp:"ᄀ-ᇿ〮〯ㄱ-ㆎ㈀-㈞㉠-㉾ꥠ-ꥼ가-힣ힰ-ퟆퟋ-ퟻﾠ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ"},{name:"Hanifi_Rohingya",astral:"\ud803[\udd00-\udd27\udd30-\udd39]"},{name:"Hanunoo",bmp:"ᜠ-᜴"},{name:"Hatran",astral:"\ud802[\udce0-\udcf2\udcf4\udcf5\udcfb-\udcff]"},{name:"Hebrew",bmp:"֑-ׇא-תׯ-״יִ-זּטּ-לּמּנּסּףּפּצּ-ﭏ"},{name:"Hiragana",bmp:"ぁ-ゖゝ-ゟ",astral:"\ud82c[\udc01-\udd1e]|🈀"},{name:"Imperial_Aramaic",astral:"\ud802[\udc40-\udc55\udc57-\udc5f]"},{name:"Inherited",bmp:"̀-ًͯ҅҆-ٰٕ॒॑᪰-᪾᳐-᳔᳒-᳢᳠-᳨᳭᳴᳸᳹᷀-᷹᷻-᷿‌‍⃐-〪⃰-゙゚〭︀-️︠-︭",astral:"\ud800[\uddfd\udee0]|𑌻|\ud834[\udd67-\udd69\udd7b-\udd82\udd85-\udd8b\uddaa-\uddad]|\udb40[\udd00-\uddef]"},{name:"Inscriptional_Pahlavi",astral:"\ud802[\udf60-\udf72\udf78-\udf7f]"},{name:"Inscriptional_Parthian",astral:"\ud802[\udf40-\udf55\udf58-\udf5f]"},{name:"Javanese",bmp:"ꦀ-꧍꧐-꧙꧞꧟"},{name:"Kaithi",astral:"\ud804[\udc80-\udcc1\udccd]"},{name:"Kannada",bmp:"ಀ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹ಼-ೄೆ-ೈೊ-್ೕೖೞೠ-ೣ೦-೯ೱೲ"},{name:"Katakana",bmp:"ァ-ヺヽ-ヿㇰ-ㇿ㋐-㋾㌀-㍗ｦ-ｯｱ-ﾝ",astral:"𛀀"},{name:"Kayah_Li",bmp:"꤀-꤭꤯"},{name:"Kharoshthi",astral:"\ud802[\ude00-\ude03\ude05\ude06\ude0c-\ude13\ude15-\ude17\ude19-\ude35\ude38-\ude3a\ude3f-\ude48\ude50-\ude58]"},{name:"Khmer",bmp:"ក-៝០-៩៰-៹᧠-᧿"},{name:"Khojki",astral:"\ud804[\ude00-\ude11\ude13-\ude3e]"},{name:"Khudawadi",astral:"\ud804[\udeb0-\udeea\udef0-\udef9]"},{name:"Lao",bmp:"ກຂຄງຈຊຍດ-ທນ-ຟມ-ຣລວສຫອ-ູົ-ຽເ-ໄໆ່-ໍ໐-໙ໜ-ໟ"},{name:"Latin",bmp:"A-Za-zªºÀ-ÖØ-öø-ʸˠ-ˤᴀ-ᴥᴬ-ᵜᵢ-ᵥᵫ-ᵷᵹ-ᶾḀ-ỿⁱⁿₐ-ₜKÅℲⅎⅠ-ↈⱠ-ⱿꜢ-ꞇꞋ-ꞹꟷ-ꟿꬰ-ꭚꭜ-ꭤﬀ-ﬆＡ-Ｚａ-ｚ"},{name:"Lepcha",bmp:"ᰀ-᰷᰻-᱉ᱍ-ᱏ"},{name:"Limbu",bmp:"ᤀ-ᤞᤠ-ᤫᤰ-᤻᥀᥄-᥏"},{name:"Linear_A",astral:"\ud801[\ude00-\udf36\udf40-\udf55\udf60-\udf67]"},{name:"Linear_B",astral:"\ud800[\udc00-\udc0b\udc0d-\udc26\udc28-\udc3a\udc3c\udc3d\udc3f-\udc4d\udc50-\udc5d\udc80-\udcfa]"},{name:"Lisu",bmp:"ꓐ-꓿"},{name:"Lycian",astral:"\ud800[\ude80-\ude9c]"},{name:"Lydian",astral:"\ud802[\udd20-\udd39\udd3f]"},{name:"Mahajani",astral:"\ud804[\udd50-\udd76]"},{name:"Makasar",astral:"\ud807[\udee0-\udef8]"},{name:"Malayalam",bmp:"ഀ-ഃഅ-ഌഎ-ഐഒ-ൄെ-ൈൊ-൏ൔ-ൣ൦-ൿ"},{name:"Mandaic",bmp:"ࡀ-࡛࡞"},{name:"Manichaean",astral:"\ud802[\udec0-\udee6\udeeb-\udef6]"},{name:"Marchen",astral:"\ud807[\udc70-\udc8f\udc92-\udca7\udca9-\udcb6]"},{name:"Masaram_Gondi",astral:"\ud807[\udd00-\udd06\udd08\udd09\udd0b-\udd36\udd3a\udd3c\udd3d\udd3f-\udd47\udd50-\udd59]"},{name:"Medefaidrin",astral:"\ud81b[\ude40-\ude9a]"},{name:"Meetei_Mayek",bmp:"ꫠ-꫶ꯀ-꯭꯰-꯹"},{name:"Mende_Kikakui",astral:"\ud83a[\udc00-\udcc4\udcc7-\udcd6]"},{name:"Meroitic_Cursive",astral:"\ud802[\udda0-\uddb7\uddbc-\uddcf\uddd2-\uddff]"},{name:"Meroitic_Hieroglyphs",astral:"\ud802[\udd80-\udd9f]"},{name:"Miao",astral:"\ud81b[\udf00-\udf44\udf50-\udf7e\udf8f-\udf9f]"},{name:"Modi",astral:"\ud805[\ude00-\ude44\ude50-\ude59]"},{name:"Mongolian",bmp:"᠀᠁᠄᠆-᠎᠐-᠙ᠠ-ᡸᢀ-ᢪ",astral:"\ud805[\ude60-\ude6c]"},{name:"Mro",astral:"\ud81a[\ude40-\ude5e\ude60-\ude69\ude6e\ude6f]"},{name:"Multani",astral:"\ud804[\ude80-\ude86\ude88\ude8a-\ude8d\ude8f-\ude9d\ude9f-\udea9]"},{name:"Myanmar",bmp:"က-႟ꧠ-ꧾꩠ-ꩿ"},{name:"Nabataean",astral:"\ud802[\udc80-\udc9e\udca7-\udcaf]"},{name:"New_Tai_Lue",bmp:"ᦀ-ᦫᦰ-ᧉ᧐-᧚᧞᧟"},{name:"Newa",astral:"\ud805[\udc00-\udc59\udc5b\udc5d\udc5e]"},{name:"Nko",bmp:"߀-ߺ߽-߿"},{name:"Nushu",astral:"𖿡|\ud82c[\udd70-\udefb]"},{name:"Ogham",bmp:" -᚜"},{name:"Ol_Chiki",bmp:"᱐-᱿"},{name:"Old_Hungarian",astral:"\ud803[\udc80-\udcb2\udcc0-\udcf2\udcfa-\udcff]"},{name:"Old_Italic",astral:"\ud800[\udf00-\udf23\udf2d-\udf2f]"},{name:"Old_North_Arabian",astral:"\ud802[\ude80-\ude9f]"},{name:"Old_Permic",astral:"\ud800[\udf50-\udf7a]"},{name:"Old_Persian",astral:"\ud800[\udfa0-\udfc3\udfc8-\udfd5]"},{name:"Old_Sogdian",astral:"\ud803[\udf00-\udf27]"},{name:"Old_South_Arabian",astral:"\ud802[\ude60-\ude7f]"},{name:"Old_Turkic",astral:"\ud803[\udc00-\udc48]"},{name:"Oriya",bmp:"ଁ-ଃଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହ଼-ୄେୈୋ-୍ୖୗଡ଼ଢ଼ୟ-ୣ୦-୷"},{name:"Osage",astral:"\ud801[\udcb0-\udcd3\udcd8-\udcfb]"},{name:"Osmanya",astral:"\ud801[\udc80-\udc9d\udca0-\udca9]"},{name:"Pahawh_Hmong",astral:"\ud81a[\udf00-\udf45\udf50-\udf59\udf5b-\udf61\udf63-\udf77\udf7d-\udf8f]"},{name:"Palmyrene",astral:"\ud802[\udc60-\udc7f]"},{name:"Pau_Cin_Hau",astral:"\ud806[\udec0-\udef8]"},{name:"Phags_Pa",bmp:"ꡀ-꡷"},{name:"Phoenician",astral:"\ud802[\udd00-\udd1b\udd1f]"},{name:"Psalter_Pahlavi",astral:"\ud802[\udf80-\udf91\udf99-\udf9c\udfa9-\udfaf]"},{name:"Rejang",bmp:"ꤰ-꥓꥟"},{name:"Runic",bmp:"ᚠ-ᛪᛮ-ᛸ"},{name:"Samaritan",bmp:"ࠀ-࠭࠰-࠾"},{name:"Saurashtra",bmp:"ꢀ-ꣅ꣎-꣙"},{name:"Sharada",astral:"\ud804[\udd80-\uddcd\uddd0-\udddf]"},{name:"Shavian",astral:"\ud801[\udc50-\udc7f]"},{name:"Siddham",astral:"\ud805[\udd80-\uddb5\uddb8-\udddd]"},{name:"SignWriting",astral:"\ud836[\udc00-\ude8b\ude9b-\ude9f\udea1-\udeaf]"},{name:"Sinhala",bmp:"ංඃඅ-ඖක-නඳ-රලව-ෆ්ා-ුූෘ-ෟ෦-෯ෲ-෴",astral:"\ud804[\udde1-\uddf4]"},{name:"Sogdian",astral:"\ud803[\udf30-\udf59]"},{name:"Sora_Sompeng",astral:"\ud804[\udcd0-\udce8\udcf0-\udcf9]"},{name:"Soyombo",astral:"\ud806[\ude50-\ude83\ude86-\udea2]"},{name:"Sundanese",bmp:"ᮀ-ᮿ᳀-᳇"},{name:"Syloti_Nagri",bmp:"ꠀ-꠫"},{name:"Syriac",bmp:"܀-܍܏-݊ݍ-ݏࡠ-ࡪ"},{name:"Tagalog",bmp:"ᜀ-ᜌᜎ-᜔"},{name:"Tagbanwa",bmp:"ᝠ-ᝬᝮ-ᝰᝲᝳ"},{name:"Tai_Le",bmp:"ᥐ-ᥭᥰ-ᥴ"},{name:"Tai_Tham",bmp:"ᨠ-ᩞ᩠-᩿᩼-᪉᪐-᪙᪠-᪭"},{name:"Tai_Viet",bmp:"ꪀ-ꫂꫛ-꫟"},{name:"Takri",astral:"\ud805[\ude80-\udeb7\udec0-\udec9]"},{name:"Tamil",bmp:"ஂஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹா-ூெ-ைொ-்ௐௗ௦-௺"},{name:"Tangut",astral:"𖿠|[\ud81c-\ud820][\udc00-\udfff]|\ud821[\udc00-\udff1]|\ud822[\udc00-\udef2]"},{name:"Telugu",bmp:"ఀ-ఌఎ-ఐఒ-నప-హఽ-ౄె-ైొ-్ౕౖౘ-ౚౠ-ౣ౦-౯౸-౿"},{name:"Thaana",bmp:"ހ-ޱ"},{name:"Thai",bmp:"ก-ฺเ-๛"},{name:"Tibetan",bmp:"ༀ-ཇཉ-ཬཱ-ྗྙ-ྼ྾-࿌࿎-࿔࿙࿚"},{name:"Tifinagh",bmp:"ⴰ-ⵧⵯ⵰⵿"},{name:"Tirhuta",astral:"\ud805[\udc80-\udcc7\udcd0-\udcd9]"},{name:"Ugaritic",astral:"\ud800[\udf80-\udf9d\udf9f]"},{name:"Vai",bmp:"ꔀ-ꘫ"},{name:"Warang_Citi",astral:"\ud806[\udca0-\udcf2\udcff]"},{name:"Yi",bmp:"ꀀ-ꒌ꒐-꓆"},{name:"Zanabazar_Square",astral:"\ud806[\ude00-\ude47]"}]},1322:function(e,u,d){"use strict";d.r(u);var t=d(1),n=d.n(t),r=d(2),a=d.n(r),i=d(4),c=d.n(i),o=d(3),f=d.n(o),s=d(5),l=d.n(s),p=d(6);
/**
 * Identifier extraction functions
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2016
 * @license Apache-2.0
 *
 */
function m(e,u,d,t){for(var n,r="",a=0;n=u.exec(e);)n.index===u.lastIndex&&u.lastIndex++,d&&d.test(n[0])||(a++,r+=n[0]+"\n");return t&&(r="Total found: "+a+"\n\n"+r),r}var h=new RegExp('[A-Z]+://[-\\w]+(?:\\.\\w[-\\w]*)+(?::\\d+)?(?:/[^.!,?"<>\\[\\]{}\\s\\x7F-\\xFF]*(?:[.!,?]+[^.!,?"<>\\[\\]{}\\s\\x7F-\\xFF]+)*)?',"ig"),b=/\b((?=[a-z0-9-]{1,63}\.)(xn--)?[a-z0-9]+(-[a-z0-9]+)*\.)+[a-z]{2,63}\b/gi,g=function(e){function u(){var e;return n()(this,u),(e=c()(this,f()(u).call(this))).name="Extract dates",e.module="Regex",e.description="Extracts dates in the following formats<ul><li><code>yyyy-mm-dd</code></li><li><code>dd/mm/yyyy</code></li><li><code>mm/dd/yyyy</code></li></ul>Dividers can be any of /, -, . or space",e.inputType="string",e.outputType="string",e.args=[{name:"Display total",type:"boolean",value:!1}],e}return l()(u,e),a()(u,[{key:"run",value:function(e,u){var d=u[0];return m(e,new RegExp("(?:19|20)\\d\\d[- /.](?:0[1-9]|1[012])[- /.](?:0[1-9]|[12][0-9]|3[01])|(?:0[1-9]|[12][0-9]|3[01])[- /.](?:0[1-9]|1[012])[- /.](?:19|20)\\d\\d|(?:0[1-9]|1[012])[- /.](?:0[1-9]|[12][0-9]|3[01])[- /.](?:19|20)\\d\\d","ig"),null,d)}}]),u}(p.a),y=function(e){function u(){var e;return n()(this,u),(e=c()(this,f()(u).call(this))).name="Extract domains",e.module="Regex",e.description="Extracts fully qualified domain names.<br>Note that this will not include paths. Use <strong>Extract URLs</strong> to find entire URLs.",e.inputType="string",e.outputType="string",e.args=[{name:"Display total",type:"boolean",value:"Extract.DISPLAY_TOTAL"}],e}return l()(u,e),a()(u,[{key:"run",value:function(e,u){var d=u[0];return m(e,b,null,d)}}]),u}(p.a),v=function(e){function u(){var e;return n()(this,u),(e=c()(this,f()(u).call(this))).name="Extract email addresses",e.module="Regex",e.description="Extracts all email addresses from the input.",e.inputType="string",e.outputType="string",e.args=[{name:"Display total",type:"boolean",value:!1}],e}return l()(u,e),a()(u,[{key:"run",value:function(e,u){return m(e,/(?:[\u00A0-\uD7FF\uE000-\uFFFF-a-z0-9!#$%&'*+\/=?^_`{|}~-]+(?:\.[\u00A0-\uD7FF\uE000-\uFFFF-a-z0-9!#$%&'*+\/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[\u00A0-\uD7FF\uE000-\uFFFF-a-z0-9](?:[\u00A0-\uD7FF\uE000-\uFFFF-a-z0-9-]*[\u00A0-\uD7FF\uE000-\uFFFF-a-z0-9])?\.)+[\u00A0-\uD7FF\uE000-\uFFFF-a-z0-9](?:[\u00A0-\uD7FF\uE000-\uFFFF-a-z0-9-]*[\u00A0-\uD7FF\uE000-\uFFFF-a-z0-9])?|\[(?:(?:(2(5[0-5]|[0-4][0-9])|1[0-9][0-9]|[1-9]?[0-9]))\.){3}(?:(2(5[0-5]|[0-4][0-9])|1[0-9][0-9]|[1-9]?[0-9])|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])/gi,null,u[0])}}]),u}(p.a),x=d(13),w=d.n(x),A=function(e){function u(){var e;return n()(this,u),(e=c()(this,f()(u).call(this))).name="Extract file paths",e.module="Regex",e.description="Extracts anything that looks like a Windows or UNIX file path.<br><br>Note that if UNIX is selected, there will likely be a lot of false positives.",e.inputType="string",e.outputType="string",e.args=[{name:"Windows",type:"boolean",value:!0},{name:"UNIX",type:"boolean",value:!0},{name:"Display total",type:"boolean",value:!1}],e}return l()(u,e),a()(u,[{key:"run",value:function(e,u){var d=w()(u,3),t=d[0],n=d[1],r=d[2],a="[A-Z\\d][A-Z\\d\\- '_\\(\\)~]{0,61}",i="[A-Z]:\\\\(?:"+a+"\\\\?)*"+a+"(?:\\.[A-Z\\d]{1,6})?",c="(?:/[A-Z\\d.][A-Z\\d\\-.]{0,61})+",o="";return t&&n?o=i+"|"+c:t?o=i:n&&(o=c),o?m(e,new RegExp(o,"ig"),null,r):""}}]),u}(p.a),_=function(e){function u(){var e;return n()(this,u),(e=c()(this,f()(u).call(this))).name="Extract IP addresses",e.module="Regex",e.description="Extracts all IPv4 and IPv6 addresses.<br><br>Warning: Given a string <code>7**********6</code>, this will match <code>**********</code> so always check the original input!",e.inputType="string",e.outputType="string",e.args=[{name:"IPv4",type:"boolean",value:!0},{name:"IPv6",type:"boolean",value:!1},{name:"Remove local IPv4 addresses",type:"boolean",value:!1},{name:"Display total",type:"boolean",value:!1}],e}return l()(u,e),a()(u,[{key:"run",value:function(e,u){var d=w()(u,4),t=d[0],n=d[1],r=d[2],a=d[3],i="(?:(?:\\d|[01]?\\d\\d|2[0-4]\\d|25[0-5])\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d|\\d)(?:\\/\\d{1,2})?",c="((?=.*::)(?!.*::.+::)(::)?([\\dA-F]{1,4}:(:|\\b)|){5}|([\\dA-F]{1,4}:){6})((([\\dA-F]{1,4}((?!\\3)::|:\\b|(?![\\dA-F])))|(?!\\2\\3)){2}|(((2[0-4]|1\\d|[1-9])?\\d|25[0-5])\\.?\\b){4})",o="";if(t&&n?o=i+"|"+c:t?o=i:n&&(o=c),o){var f=new RegExp(o,"ig");if(r){return m(e,f,new RegExp("^(?:10\\..+|192\\.168\\..+|172\\.(?:1[6-9]|2\\d|3[01])\\..+|127\\..+)"),a)}return m(e,f,null,a)}return""}}]),u}(p.a),E=function(e){function u(){var e;return n()(this,u),(e=c()(this,f()(u).call(this))).name="Extract MAC addresses",e.module="Regex",e.description="Extracts all Media Access Control (MAC) addresses from the input.",e.inputType="string",e.outputType="string",e.args=[{name:"Display total",type:"boolean",value:!1}],e}return l()(u,e),a()(u,[{key:"run",value:function(e,u){return m(e,/[A-F\d]{2}(?:[:-][A-F\d]{2}){5}/gi,null,u[0])}}]),u}(p.a),F=function(e){function u(){var e;return n()(this,u),(e=c()(this,f()(u).call(this))).name="Extract URLs",e.module="Regex",e.description="Extracts Uniform Resource Locators (URLs) from the input. The protocol (http, ftp etc.) is required otherwise there will be far too many false positives.",e.inputType="string",e.outputType="string",e.args=[{name:"Display total",type:"boolean",value:!1}],e}return l()(u,e),a()(u,[{key:"run",value:function(e,u){var d=u[0];return m(e,h,null,d)}}]),u}(p.a),I=d(0),B=d(94),S=d(9),C=d(127),k=d.n(C),R=function(e){function u(){var e;return n()(this,u),(e=c()(this,f()(u).call(this))).name="Filter",e.module="Regex",e.description="Splits up the input using the specified delimiter and then filters each branch based on a regular expression.",e.inputType="string",e.outputType="string",e.args=[{name:"Delimiter",type:"option",value:B.c},{name:"Regex",type:"string",value:""},{name:"Invert condition",type:"boolean",value:!1}],e}return l()(u,e),a()(u,[{key:"run",value:function(e,u){var d,t=I.b.charRep(u[0]),n=u[2];try{d=new k.a(u[1])}catch(e){throw new S.a(`Invalid regex. Details: ${e.message}`)}return e.split(t).filter(function(e){return n^d.test(e)}).join(t)}}]),u}(p.a),T=function(e){function u(){var e;return n()(this,u),(e=c()(this,f()(u).call(this))).name="Find / Replace",e.module="Regex",e.description="Replaces all occurrences of the first string with the second.<br><br>Includes support for regular expressions (regex), simple strings and extended strings (which support \\n, \\r, \\t, \\b, \\f and escaped hex bytes using \\x notation, e.g. \\x00 for a null byte).",e.infoURL="https://wikipedia.org/wiki/Regular_expression",e.inputType="string",e.outputType="string",e.args=[{name:"Find",type:"toggleString",value:"",toggleValues:["Regex","Extended (\\n, \\t, \\x...)","Simple string"]},{name:"Replace",type:"binaryString",value:""},{name:"Global match",type:"boolean",value:!0},{name:"Case insensitive",type:"boolean",value:!1},{name:"Multiline matching",type:"boolean",value:!0},{name:"Dot matches all",type:"boolean",value:!1}],e}return l()(u,e),a()(u,[{key:"run",value:function(e,u){var d=w()(u,6),t=d[0].option,n=d[1],r=d[2],a=d[3],i=d[4],c=d[5],o=u[0].string,f="";return r&&(f+="g"),a&&(f+="i"),i&&(f+="m"),c&&(f+="s"),"Regex"===t?(o=new k.a(o,f),e.replace(o,n)):(0===t.indexOf("Extended")&&(o=I.b.parseEscapedChars(o)),o=new k.a(I.b.escapeRegex(o),f),e.replace(o,n))}}]),u}(p.a),O=d(7),L=d.n(O),P=d(11),D=d.n(P),N=d(15),U=function(e){function u(){var e;return n()(this,u),(e=c()(this,f()(u).call(this))).name="Register",e.flowControl=!0,e.module="Regex",e.description="Extract data from the input and store it in registers which can then be passed into subsequent operations as arguments. Regular expression capture groups are used to select the data to extract.<br><br>To use registers in arguments, refer to them using the notation <code>$Rn</code> where n is the register number, starting at 0.<br><br>For example:<br>Input: <code>Test</code><br>Extractor: <code>(.*)</code><br>Argument: <code>$R0</code> becomes <code>Test</code><br><br>Registers can be escaped in arguments using a backslash. e.g. <code>\\$R0</code> would become <code>$R0</code> rather than <code>Test</code>.",e.infoURL="https://wikipedia.org/wiki/Regular_expression#Syntax",e.inputType="string",e.outputType="string",e.args=[{name:"Extractor",type:"binaryString",value:"([\\s\\S]*)"},{name:"Case insensitive",type:"boolean",value:!0},{name:"Multiline matching",type:"boolean",value:!1},{name:"Dot matches all",type:"boolean",value:!1}],e}var d;return l()(u,e),a()(u,[{key:"run",value:(d=D()(L.a.mark(function e(u){var d,t,n,r,a,i,c,o,f,s,l,p,m;return L.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return l=function(e){return e.replace(/(\\*)\$R(\d{1,2})/g,function(e,d,t){var n=parseInt(t,10)+1;return n<=u.numRegisters||n>=u.numRegisters+s.length?e:d.length%2!=0?e.slice(1):d+s[n-u.numRegisters]})},d=u.opList[u.progress].ingValues,t=w()(d,4),n=t[0],r=t[1],a=t[2],i=t[3],c="",r&&(c+="i"),a&&(c+="m"),i&&(c+="s"),o=new k.a(n,c),e.next=10,u.dish.get(N.a.STRING);case 10:if(f=e.sent,s=f.match(o)){e.next=14;break}return e.abrupt("return",u);case 14:Object(I.d)()&&self.setRegisters(u.forkOffset+u.progress,u.numRegisters,s.slice(1)),p=u.progress+1;case 16:if(!(p<u.opList.length)){e.next=25;break}if(!u.opList[p].disabled){e.next=19;break}return e.abrupt("continue",22);case 19:m=(m=u.opList[p].ingValues).map(function(e){return"string"!=typeof e&&"object"!=typeof e?e:"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"string")?(e.string=l(e.string),e):l(e)}),u.opList[p].ingValues=m;case 22:p++,e.next=16;break;case 25:return u.numRegisters+=s.length-1,e.abrupt("return",u);case 27:case"end":return e.stop()}},e)})),function(e){return d.apply(this,arguments)})}]),u}(p.a);function M(e,u,d,t,n){for(var r,a="",i=0;r=u.exec(e);)if(r.index===u.lastIndex&&u.lastIndex++,i++,t&&(a+=r[0]+"\n"),n)for(var c=1;c<r.length;c++)t&&(a+="  Group "+c+": "),a+=r[c]+"\n";return d&&(a="Total found: "+i+"\n\n"+a),a.slice(0,-1)}var j=function(e){function u(){var e;return n()(this,u),(e=c()(this,f()(u).call(this))).name="Regular expression",e.module="Regex",e.description="Define your own regular expression (regex) to search the input data with, optionally choosing from a list of pre-defined patterns.<br><br>Supports extended regex syntax including the 'dot matches all' flag, named capture groups, full unicode coverage (including <code>\\p{}</code> categories and scripts as well as astral codes) and recursive matching.",e.infoURL="https://wikipedia.org/wiki/Regular_expression",e.inputType="string",e.outputType="html",e.args=[{name:"Built in regexes",type:"populateOption",value:[{name:"User defined",value:""},{name:"IPv4 address",value:"(?:(?:\\d|[01]?\\d\\d|2[0-4]\\d|25[0-5])\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d|\\d)(?:\\/\\d{1,2})?"},{name:"IPv6 address",value:"((?=.*::)(?!.*::.+::)(::)?([\\dA-Fa-f]{1,4}:(:|\\b)|){5}|([\\dA-Fa-f]{1,4}:){6})((([\\dA-Fa-f]{1,4}((?!\\3)::|:\\b|(?![\\dA-Fa-f])))|(?!\\2\\3)){2}|(((2[0-4]|1\\d|[1-9])?\\d|25[0-5])\\.?\\b){4})"},{name:"Email address",value:"\\b(\\w[-.\\w]*)@([-\\w]+(?:\\.[-\\w]+)*)\\.([A-Za-z]{2,4})\\b"},{name:"URL",value:'([A-Za-z]+://)([-\\w]+(?:\\.\\w[-\\w]*)+)(:\\d+)?(/[^.!,?"<>\\[\\]{}\\s\\x7F-\\xFF]*(?:[.!,?]+[^.!,?"<>\\[\\]{}\\s\\x7F-\\xFF]+)*)?'},{name:"Domain",value:"\\b((?=[a-z0-9-]{1,63}\\.)(xn--)?[a-z0-9]+(-[a-z0-9]+)*\\.)+[a-z]{2,63}\\b"},{name:"Windows file path",value:"([A-Za-z]):\\\\((?:[A-Za-z\\d][A-Za-z\\d\\- \\x27_\\(\\)~]{0,61}\\\\?)*[A-Za-z\\d][A-Za-z\\d\\- \\x27_\\(\\)]{0,61})(\\.[A-Za-z\\d]{1,6})?"},{name:"UNIX file path",value:"(?:/[A-Za-z\\d.][A-Za-z\\d\\-.]{0,61})+"},{name:"MAC address",value:"[A-Fa-f\\d]{2}(?:[:-][A-Fa-f\\d]{2}){5}"},{name:"Date (yyyy-mm-dd)",value:"((?:19|20)\\d\\d)[- /.](0[1-9]|1[012])[- /.](0[1-9]|[12][0-9]|3[01])"},{name:"Date (dd/mm/yyyy)",value:"(0[1-9]|[12][0-9]|3[01])[- /.](0[1-9]|1[012])[- /.]((?:19|20)\\d\\d)"},{name:"Date (mm/dd/yyyy)",value:"(0[1-9]|1[012])[- /.](0[1-9]|[12][0-9]|3[01])[- /.]((?:19|20)\\d\\d)"},{name:"Strings",value:'[A-Za-z\\d/\\-:.,_$%\\x27"()<>= !\\[\\]{}@]{4,}'}],target:1},{name:"Regex",type:"text",value:""},{name:"Case insensitive",type:"boolean",value:!0},{name:"^ and $ match at newlines",type:"boolean",value:!0},{name:"Dot matches all",type:"boolean",value:!1},{name:"Unicode support",type:"boolean",value:!1},{name:"Astral support",type:"boolean",value:!1},{name:"Display total",type:"boolean",value:!1},{name:"Output format",type:"option",value:["Highlight matches","List matches","List capture groups","List matches with capture groups"]}],e}return l()(u,e),a()(u,[{key:"run",value:function(e,u){var d=w()(u,9),t=d[1],n=d[2],r=d[3],a=d[4],i=d[5],c=d[6],o=d[7],f=d[8],s="g";if(n&&(s+="i"),r&&(s+="m"),a&&(s+="s"),i&&(s+="u"),c&&(s+="A"),!t||"^"===t||"$"===t)return I.b.escapeHtml(e);try{var l=new k.a(t,s);switch(f){case"Highlight matches":return function(e,u,d){var t="",n="",r=1,a=0,i=[];t=e.replace(u,function(e){for(var u=arguments.length,d=new Array(u>1?u-1:0),t=1;t<u;t++)d[t-1]=arguments[t];d.pop();var c=d.pop(),o=d;if(n=`Offset: ${c}\n`,o.length){n+="Groups:\n";for(var f=0;f<o.length;f++)n+=`\t${f+1}: ${I.b.escapeHtml(o[f]||"")}\n`}return r=1===r?2:1,i.push(`<span class='hl${r}' title='${n}'>${I.b.escapeHtml(e)}</span>`),`[cc_capture_group_${a++}]`}),t=(t=I.b.escapeHtml(t)).replace(/\[cc_capture_group_(\d+)\]/g,function(e,u){return i[u]}),d&&(t="Total found: "+a+"\n\n"+t);return t}(e,l,o);case"List matches":return I.b.escapeHtml(M(e,l,o,!0,!1));case"List capture groups":return I.b.escapeHtml(M(e,l,o,!1,!0));case"List matches with capture groups":return I.b.escapeHtml(M(e,l,o,!0,!0));default:return"Error: Invalid output format"}}catch(e){throw new S.a("Invalid regex. Details: "+e.message)}}}]),u}(p.a),G=function(e){function u(){var e;return n()(this,u),(e=c()(this,f()(u).call(this))).name="Strings",e.module="Regex",e.description="Extracts all strings from the input.",e.infoURL="https://wikipedia.org/wiki/Strings_(Unix)",e.inputType="string",e.outputType="string",e.args=[{name:"Encoding",type:"option",value:["Single byte","16-bit littleendian","16-bit bigendian","All"]},{name:"Minimum length",type:"number",value:4},{name:"Match",type:"option",value:["[ASCII]","Alphanumeric + punctuation (A)","All printable chars (A)","Null-terminated strings (A)","[Unicode]","Alphanumeric + punctuation (U)","All printable chars (U)","Null-terminated strings (U)"]},{name:"Display total",type:"boolean",value:!1}],e}return l()(u,e),a()(u,[{key:"run",value:function(e,u){var d=w()(u,4),t=d[0],n=d[1],r=d[2],a=d[3],i="";switch(r){case"Alphanumeric + punctuation (A)":i="[A-Z\\d/\\-:.,_$%'\"()<>= !\\[\\]{}@]";break;case"All printable chars (A)":case"Null-terminated strings (A)":i="[ -~]";break;case"Alphanumeric + punctuation (U)":i="[\\pL\\pN\\pP\\pZ]";break;case"All printable chars (U)":case"Null-terminated strings (U)":i="[\\pL\\pM\\pZ\\pS\\pN\\pP]"}switch(t){case"All":i=`(\0?${i}\0?)`;break;case"16-bit littleendian":i=`(${i}\0)`;break;case"16-bit bigendian":i=`(\0${i})`}return i=`${i}{${n},}`,r.includes("Null-terminated")&&(i+="\0"),m(e,new k.a(i,"ig"),null,a)}}]),u}(p.a),$="undefined"==typeof self?{}:self.OpModules||{};
/**
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2016
 * @license Apache-2.0
 */$.Regex={"Extract dates":g,"Extract domains":y,"Extract email addresses":v,"Extract file paths":A,"Extract IP addresses":_,"Extract MAC addresses":E,"Extract URLs":F,Filter:R,"Find / Replace":T,Register:U,"Regular expression":j,Strings:G};u.default=$},14:function(e,u,d){"use strict";d.d(u,"b",function(){return n}),d.d(u,"a",function(){return r});var t=d(0);
/**
 * Base64 functions.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2016
 * @license Apache-2.0
 */function n(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"A-Za-z0-9+/=";if(!e)return"";e instanceof ArrayBuffer&&(e=new Uint8Array(e)),"string"==typeof e&&(e=t.b.strToByteArray(e)),u=t.b.expandAlphRange(u).join("");for(var d,n,r,a,i,c,o,f="",s=0;s<e.length;)a=(d=e[s++])>>2,i=(3&d)<<4|(n=e[s++])>>4,c=(15&n)<<2|(r=e[s++])>>6,o=63&r,isNaN(n)?c=o=64:isNaN(r)&&(o=64),f+=u.charAt(a)+u.charAt(i)+u.charAt(c)+u.charAt(o);return f}function r(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"A-Za-z0-9+/=",d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"string",n=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];if(!e)return"string"===d?"":[];u=u||"A-Za-z0-9+/=",u=t.b.expandAlphRange(u).join("");var r,a,i,c,o,f,s=[],l=0;if(n){var p=new RegExp("[^"+u.replace(/[[\]\\\-^$]/g,"\\$&")+"]","g");e=e.replace(p,"")}for(;l<e.length;)r=u.indexOf(e.charAt(l++))<<2|(c=-1===(c=u.indexOf(e.charAt(l++)||"="))?64:c)>>4,a=(15&c)<<4|(o=-1===(o=u.indexOf(e.charAt(l++)||"="))?64:o)>>2,i=(3&o)<<6|(f=-1===(f=u.indexOf(e.charAt(l++)||"="))?64:f),s.push(r),64!==o&&s.push(a),64!==f&&s.push(i);return"string"===d?t.b.byteArrayToUtf8(s):s}},15:function(e,u,d){"use strict";var t=d(7),n=d.n(t),r=d(11),a=d.n(r),i=d(1),c=d.n(i),o=d(2),f=d.n(o),s=d(0),l=d(4),p=d.n(l),m=d(3),h=d.n(m),b=d(25),g=d.n(b),y=d(5),v=d.n(y);
/**
 * Custom error type for handling Dish type errors.
 * i.e. where the Dish cannot be successfully translated between types
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 */
var x=function(e){function u(){var e;c()(this,u);for(var d=arguments.length,t=new Array(d),n=0;n<d;n++)t[n]=arguments[n];return(e=p()(this,h()(u).call(this,...t))).type="DishError",Error.captureStackTrace&&Error.captureStackTrace(g()(e),u),e}return v()(u,e),u}(function(e){function u(){var u=Reflect.construct(e,Array.from(arguments));return Object.setPrototypeOf(u,Object.getPrototypeOf(this)),u}return u.prototype=Object.create(e.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),Object.setPrototypeOf?Object.setPrototypeOf(u,e):u.__proto__=e,u}(Error)),w=d(16),A=d.n(w),_=d(18),E=d(26),F=d.n(E),I=function(){function e(){c()(this,e)}return f()(e,null,[{key:"checkForValue",value:function(e){if(void 0===e)throw new Error("only use translation methods with .bind")}},{key:"toArrayBuffer",value:function(){throw new Error("toArrayBuffer has not been implemented")}},{key:"fromArrayBuffer",value:function(){arguments.length>0&&void 0!==arguments[0]&&arguments[0];throw new Error("fromArrayBuffer has not been implemented")}}]),e}(),B=function(e){function u(){return c()(this,u),p()(this,h()(u).apply(this,arguments))}return v()(u,e),f()(u,null,[{key:"toArrayBuffer",value:function(){u.checkForValue(this.value),this.value=new Uint8Array(this.value).buffer}},{key:"fromArrayBuffer",value:function(){u.checkForValue(this.value),this.value=Array.prototype.slice.call(new Uint8Array(this.value))}}]),u}(I),S=function(e){function u(){return c()(this,u),p()(this,h()(u).apply(this,arguments))}return v()(u,e),f()(u,null,[{key:"toArrayBuffer",value:function(){u.checkForValue(this.value),this.value=A.a.isBigNumber(this.value)?s.b.strToArrayBuffer(this.value.toFixed()):new ArrayBuffer}},{key:"fromArrayBuffer",value:function(e){u.checkForValue(this.value);try{this.value=new A.a(s.b.arrayBufferToStr(this.value,!e))}catch(e){this.value=new A.a(NaN)}}}]),u}(I),C=function(e){function u(){return c()(this,u),p()(this,h()(u).apply(this,arguments))}return v()(u,e),f()(u,null,[{key:"toArrayBuffer",value:function(){var e=this;if(u.checkForValue(this.value),!Object(s.c)())return new Promise(function(u,d){s.b.readFile(e.value).then(function(u){return e.value=u.buffer}).then(u).catch(d)});this.value=s.b.readFileSync(this.value)}},{key:"fromArrayBuffer",value:function(){u.checkForValue(this.value),this.value=new File(this.value,"unknown")}}]),u}(I),k=function(e){function u(){return c()(this,u),p()(this,h()(u).apply(this,arguments))}return v()(u,e),f()(u,null,[{key:"toArrayBuffer",value:function(){u.checkForValue(this.value),this.value=this.value?s.b.strToArrayBuffer(this.value):new ArrayBuffer}},{key:"fromArrayBuffer",value:function(e){u.checkForValue(this.value),this.value=this.value?s.b.arrayBufferToStr(this.value,!e):""}}]),u}(I),R=function(e){function u(){return c()(this,u),p()(this,h()(u).apply(this,arguments))}return v()(u,e),f()(u,null,[{key:"toArrayBuffer",value:function(){u.checkForValue(this.value),this.value=this.value?s.b.strToArrayBuffer(s.b.unescapeHtml(s.b.stripHtmlTags(this.value,!0))):new ArrayBuffer}}]),u}(k),T=function(e){function u(){return c()(this,u),p()(this,h()(u).apply(this,arguments))}return v()(u,e),f()(u,null,[{key:"toArrayBuffer",value:function(){u.checkForValue(this.value),this.value=this.value?s.b.strToArrayBuffer(JSON.stringify(this.value,null,4)):new ArrayBuffer}},{key:"fromArrayBuffer",value:function(e){u.checkForValue(this.value),this.value=JSON.parse(s.b.arrayBufferToStr(this.value,!e))}}]),u}(I),O=function(e){function u(){return c()(this,u),p()(this,h()(u).apply(this,arguments))}return v()(u,e),f()(u,null,[{key:"toArrayBuffer",value:function(){u.checkForValue(this.value),Object(s.c)()&&(this.value=this.value.map(function(e){return Uint8Array.from(e.data)})),this.value=u.concatenateTypedArrays(...this.value).buffer}},{key:"fromArrayBuffer",value:function(){u.checkForValue(this.value),this.value=[new File(this.value,"unknown")]}},{key:"concatenateTypedArrays",value:function(){for(var e=0,u=arguments.length,d=new Array(u),t=0;t<u;t++)d[t]=arguments[t];for(var n=0,r=d;n<r.length;n++){var a=r[n];e+=a.length}for(var i=new Uint8Array(e),c=0,o=0,f=d;o<f.length;o++){var s=f[o];i.set(s,c),c+=s.length}return i}}]),u}(I),L=function(e){function u(){return c()(this,u),p()(this,h()(u).apply(this,arguments))}return v()(u,e),f()(u,null,[{key:"toArrayBuffer",value:function(){u.checkForValue(this.value),this.value="number"==typeof this.value?s.b.strToArrayBuffer(this.value.toString()):new ArrayBuffer}},{key:"fromArrayBuffer",value:function(e){u.checkForValue(this.value),this.value=this.value?parseFloat(s.b.arrayBufferToStr(this.value,!e)):0}}]),u}(I),P=function(){function e(){var u=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(c()(this,e),this.value=new ArrayBuffer(0),this.type=e.ARRAY_BUFFER,u&&Object.prototype.hasOwnProperty.call(u,"value")&&Object.prototype.hasOwnProperty.call(u,"type"))this.set(u.value,u.type);else if(u&&null!==d)this.set(u,d);else if(u){var t=e.typeEnum(u.constructor.name);this.set(u,t)}}var u;return f()(e,[{key:"get",value:function(u){var d=this,t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return"string"==typeof u&&(u=e.typeEnum(u)),this.type!==u?Object(s.c)()?(this._translate(u,t),this.value):new Promise(function(e,n){d._translate(u,t).then(function(){e(d.value)}).catch(n)}):this.value}},{key:"set",value:function(u,d){if("string"==typeof d&&(d=e.typeEnum(d)),F.a.debug("Dish type: "+e.enumLookup(d)),this.value=u,this.type=d,!this.valid()){var t=s.b.truncate(JSON.stringify(this.value),25);throw new x(`Data is not a valid ${e.enumLookup(d)}: ${t}`)}}},{key:"presentAs",value:function(e){var u=arguments.length>1&&void 0!==arguments[1]&&arguments[1],d=this.clone();return d.get(e,u)}},{key:"detectDishType",value:function(){var e=new Uint8Array(this.value.slice(0,2048)),u=Object(_.a)(e);return u.length&&u[0].mime&&"text/plain"!==!u[0].mime?u[0].mime:null}},{key:"getTitle",value:(u=a()(n.a.mark(function u(d){var t,r;return n.a.wrap(function(u){for(;;)switch(u.prev=u.next){case 0:t="",u.t0=this.type,u.next=u.t0===e.FILE?4:u.t0===e.LIST_FILE?6:u.t0===e.JSON?8:u.t0===e.NUMBER?10:u.t0===e.BIG_NUMBER?10:u.t0===e.ARRAY_BUFFER?12:u.t0===e.BYTE_ARRAY?12:15;break;case 4:return t=this.value.name,u.abrupt("break",26);case 6:return t=`${this.value.length} file(s)`,u.abrupt("break",26);case 8:return t="application/json",u.abrupt("break",26);case 10:return t=this.value.toString(),u.abrupt("break",26);case 12:if(null===(t=this.detectDishType())){u.next=15;break}return u.abrupt("break",26);case 15:return u.prev=15,(r=this.clone()).value=r.value.slice(0,256),u.next=20,r.get(e.STRING);case 20:t=u.sent,u.next=26;break;case 23:u.prev=23,u.t1=u.catch(15),F.a.error(`${e.enumLookup(this.type)} cannot be sliced. ${u.t1}`);case 26:return u.abrupt("return",t.slice(0,d));case 27:case"end":return u.stop()}},u,this,[[15,23]])})),function(e){return u.apply(this,arguments)})},{key:"valid",value:function(){switch(this.type){case e.BYTE_ARRAY:if(!(this.value instanceof Uint8Array||this.value instanceof Array))return!1;for(var u=0;u<this.value.length;u++)if("number"!=typeof this.value[u]||this.value[u]<0||this.value[u]>255)return!1;return!0;case e.STRING:case e.HTML:return"string"==typeof this.value;case e.NUMBER:return"number"==typeof this.value;case e.ARRAY_BUFFER:return this.value instanceof ArrayBuffer;case e.BIG_NUMBER:if(A.a.isBigNumber(this.value))return!0;if(Object.keys(this.value).sort().equals(["c","e","s"])){var d=new A.a;return d.c=this.value.c,d.e=this.value.e,d.s=this.value.s,this.value=d,!0}return!1;case e.JSON:return!0;case e.FILE:return this.value instanceof File;case e.LIST_FILE:return this.value instanceof Array&&this.value.reduce(function(e,u){return e&&u instanceof File},!0);default:return!1}}},{key:"clone",value:function(){var u=new e;switch(this.type){case e.STRING:case e.HTML:case e.NUMBER:case e.BIG_NUMBER:u.set(this.value,this.type);break;case e.BYTE_ARRAY:case e.JSON:u.set(JSON.parse(JSON.stringify(this.value)),this.type);break;case e.ARRAY_BUFFER:u.set(this.value.slice(0),this.type);break;case e.FILE:u.set(new File([this.value],this.value.name,{type:this.value.type,lastModified:this.value.lastModified}),this.type);break;case e.LIST_FILE:u.set(this.value.map(function(e){return new File([e],e.name,{type:e.type,lastModified:e.lastModified})}),this.type);break;default:throw new x("Cannot clone Dish, unknown type")}return u}},{key:"_translate",value:function(u){var d=this,t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(F.a.debug(`Translating Dish from ${e.enumLookup(this.type)} to ${e.enumLookup(u)}`),!Object(s.c)())return new Promise(function(t,n){d._toArrayBuffer().then(function(){return d.type=e.ARRAY_BUFFER}).then(function(){d._fromArrayBuffer(u),t()}).catch(n)});this._toArrayBuffer(),this.type=e.ARRAY_BUFFER,this._fromArrayBuffer(u,t)}},{key:"_toArrayBuffer",value:function(){var u=this,d={browser:{[e.STRING]:function(){return Promise.resolve(k.toArrayBuffer.bind(u)())},[e.NUMBER]:function(){return Promise.resolve(L.toArrayBuffer.bind(u)())},[e.HTML]:function(){return Promise.resolve(R.toArrayBuffer.bind(u)())},[e.ARRAY_BUFFER]:function(){return Promise.resolve()},[e.BIG_NUMBER]:function(){return Promise.resolve(S.toArrayBuffer.bind(u)())},[e.JSON]:function(){return Promise.resolve(T.toArrayBuffer.bind(u)())},[e.FILE]:function(){return C.toArrayBuffer.bind(u)()},[e.LIST_FILE]:function(){return Promise.resolve(O.toArrayBuffer.bind(u)())},[e.BYTE_ARRAY]:function(){return Promise.resolve(B.toArrayBuffer.bind(u)())}},node:{[e.STRING]:function(){return k.toArrayBuffer.bind(u)()},[e.NUMBER]:function(){return L.toArrayBuffer.bind(u)()},[e.HTML]:function(){return R.toArrayBuffer.bind(u)()},[e.ARRAY_BUFFER]:function(){},[e.BIG_NUMBER]:function(){return S.toArrayBuffer.bind(u)()},[e.JSON]:function(){return T.toArrayBuffer.bind(u)()},[e.FILE]:function(){return C.toArrayBuffer.bind(u)()},[e.LIST_FILE]:function(){return O.toArrayBuffer.bind(u)()},[e.BYTE_ARRAY]:function(){return B.toArrayBuffer.bind(u)()}}};try{return d[Object(s.c)()?"node":"browser"][this.type]()}catch(u){throw new x(`Error translating from ${e.enumLookup(this.type)} to ArrayBuffer: ${u}`)}}},{key:"_fromArrayBuffer",value:function(u,d){var t=this,n={[e.STRING]:function(){return k.fromArrayBuffer.bind(t)(d)},[e.NUMBER]:function(){return L.fromArrayBuffer.bind(t)(d)},[e.HTML]:function(){return R.fromArrayBuffer.bind(t)(d)},[e.ARRAY_BUFFER]:function(){},[e.BIG_NUMBER]:function(){return S.fromArrayBuffer.bind(t)(d)},[e.JSON]:function(){return T.fromArrayBuffer.bind(t)(d)},[e.FILE]:function(){return C.fromArrayBuffer.bind(t)()},[e.LIST_FILE]:function(){return O.fromArrayBuffer.bind(t)()},[e.BYTE_ARRAY]:function(){return B.fromArrayBuffer.bind(t)()}};try{n[u](),this.type=u}catch(d){throw new x(`Error translating from ArrayBuffer to ${e.enumLookup(u)}: ${d}`)}}},{key:"size",get:function(){switch(this.type){case e.BYTE_ARRAY:case e.STRING:case e.HTML:return this.value.length;case e.NUMBER:case e.BIG_NUMBER:return this.value.toString().length;case e.ARRAY_BUFFER:return this.value.byteLength;case e.JSON:return JSON.stringify(this.value).length;case e.FILE:return this.value.size;case e.LIST_FILE:return this.value.reduce(function(e,u){return e+u.size},0);default:return-1}}}],[{key:"typeEnum",value:function(u){switch(u.toLowerCase()){case"bytearray":case"byte array":return e.BYTE_ARRAY;case"string":return e.STRING;case"number":return e.NUMBER;case"html":return e.HTML;case"arraybuffer":case"array buffer":return e.ARRAY_BUFFER;case"bignumber":case"big number":return e.BIG_NUMBER;case"json":case"object":return e.JSON;case"file":return e.FILE;case"list<file>":return e.LIST_FILE;default:throw new x("Invalid data type string. No matching enum.")}}},{key:"enumLookup",value:function(u){switch(u){case e.BYTE_ARRAY:return"byteArray";case e.STRING:return"string";case e.NUMBER:return"number";case e.HTML:return"html";case e.ARRAY_BUFFER:return"ArrayBuffer";case e.BIG_NUMBER:return"BigNumber";case e.JSON:return"JSON";case e.FILE:return"File";case e.LIST_FILE:return"List<File>";default:throw new x("Invalid data type enum. No matching type.")}}}]),e}();P.BYTE_ARRAY=0,P.STRING=1,P.NUMBER=2,P.HTML=3,P.ARRAY_BUFFER=4,P.BIG_NUMBER=5,P.JSON=6,P.FILE=7,P.LIST_FILE=8;u.a=P},16:function(e,u,d){var t;!function(n){"use strict";var r,a=/^-?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?$/i,i=Math.ceil,c=Math.floor,o="[BigNumber Error] ",f=o+"Number primitive has more than 15 significant digits: ",s=1e14,l=14,p=9007199254740991,m=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9,1e10,1e11,1e12,1e13],h=1e7,b=1e9;function g(e){var u=0|e;return e>0||e===u?u:u-1}function y(e){for(var u,d,t=1,n=e.length,r=e[0]+"";t<n;){for(u=e[t++]+"",d=l-u.length;d--;u="0"+u);r+=u}for(n=r.length;48===r.charCodeAt(--n););return r.slice(0,n+1||1)}function v(e,u){var d,t,n=e.c,r=u.c,a=e.s,i=u.s,c=e.e,o=u.e;if(!a||!i)return null;if(d=n&&!n[0],t=r&&!r[0],d||t)return d?t?0:-i:a;if(a!=i)return a;if(d=a<0,t=c==o,!n||!r)return t?0:!n^d?1:-1;if(!t)return c>o^d?1:-1;for(i=(c=n.length)<(o=r.length)?c:o,a=0;a<i;a++)if(n[a]!=r[a])return n[a]>r[a]^d?1:-1;return c==o?0:c>o^d?1:-1}function x(e,u,d,t){if(e<u||e>d||e!==c(e))throw Error(o+(t||"Argument")+("number"==typeof e?e<u||e>d?" out of range: ":" not an integer: ":" not a primitive number: ")+String(e))}function w(e){var u=e.c.length-1;return g(e.e/l)==u&&e.c[u]%2!=0}function A(e,u){return(e.length>1?e.charAt(0)+"."+e.slice(1):e)+(u<0?"e":"e+")+u}function _(e,u,d){var t,n;if(u<0){for(n=d+".";++u;n+=d);e=n+e}else if(++u>(t=e.length)){for(n=d,u-=t;--u;n+=d);e+=n}else u<t&&(e=e.slice(0,u)+"."+e.slice(u));return e}(r=function e(u){var d,t,n,r,E,F,I,B,S,C=$.prototype={constructor:$,toString:null,valueOf:null},k=new $(1),R=20,T=4,O=-7,L=21,P=-1e7,D=1e7,N=!1,U=1,M=0,j={prefix:"",groupSize:3,secondaryGroupSize:0,groupSeparator:",",decimalSeparator:".",fractionGroupSize:0,fractionGroupSeparator:" ",suffix:""},G="0123456789abcdefghijklmnopqrstuvwxyz";function $(e,u){var d,r,i,o,s,m,h,b,g=this;if(!(g instanceof $))return new $(e,u);if(null==u){if(e&&!0===e._isBigNumber)return g.s=e.s,void(!e.c||e.e>D?g.c=g.e=null:e.e<P?g.c=[g.e=0]:(g.e=e.e,g.c=e.c.slice()));if((m="number"==typeof e)&&0*e==0){if(g.s=1/e<0?(e=-e,-1):1,e===~~e){for(o=0,s=e;s>=10;s/=10,o++);return void(o>D?g.c=g.e=null:(g.e=o,g.c=[e]))}b=String(e)}else{if(!a.test(b=String(e)))return n(g,b,m);g.s=45==b.charCodeAt(0)?(b=b.slice(1),-1):1}(o=b.indexOf("."))>-1&&(b=b.replace(".","")),(s=b.search(/e/i))>0?(o<0&&(o=s),o+=+b.slice(s+1),b=b.substring(0,s)):o<0&&(o=b.length)}else{if(x(u,2,G.length,"Base"),10==u)return V(g=new $(e),R+g.e+1,T);if(b=String(e),m="number"==typeof e){if(0*e!=0)return n(g,b,m,u);if(g.s=1/e<0?(b=b.slice(1),-1):1,$.DEBUG&&b.replace(/^0\.0*|\./,"").length>15)throw Error(f+e)}else g.s=45===b.charCodeAt(0)?(b=b.slice(1),-1):1;for(d=G.slice(0,u),o=s=0,h=b.length;s<h;s++)if(d.indexOf(r=b.charAt(s))<0){if("."==r){if(s>o){o=h;continue}}else if(!i&&(b==b.toUpperCase()&&(b=b.toLowerCase())||b==b.toLowerCase()&&(b=b.toUpperCase()))){i=!0,s=-1,o=0;continue}return n(g,String(e),m,u)}m=!1,(o=(b=t(b,u,10,g.s)).indexOf("."))>-1?b=b.replace(".",""):o=b.length}for(s=0;48===b.charCodeAt(s);s++);for(h=b.length;48===b.charCodeAt(--h););if(b=b.slice(s,++h)){if(h-=s,m&&$.DEBUG&&h>15&&(e>p||e!==c(e)))throw Error(f+g.s*e);if((o=o-s-1)>D)g.c=g.e=null;else if(o<P)g.c=[g.e=0];else{if(g.e=o,g.c=[],s=(o+1)%l,o<0&&(s+=l),s<h){for(s&&g.c.push(+b.slice(0,s)),h-=l;s<h;)g.c.push(+b.slice(s,s+=l));s=l-(b=b.slice(s)).length}else s-=h;for(;s--;b+="0");g.c.push(+b)}}else g.c=[g.e=0]}function Y(e,u,d,t){var n,r,a,i,c;if(null==d?d=T:x(d,0,8),!e.c)return e.toString();if(n=e.c[0],a=e.e,null==u)c=y(e.c),c=1==t||2==t&&(a<=O||a>=L)?A(c,a):_(c,a,"0");else if(r=(e=V(new $(e),u,d)).e,i=(c=y(e.c)).length,1==t||2==t&&(u<=r||r<=O)){for(;i<u;c+="0",i++);c=A(c,r)}else if(u-=a,c=_(c,r,"0"),r+1>i){if(--u>0)for(c+=".";u--;c+="0");}else if((u+=r-i)>0)for(r+1==i&&(c+=".");u--;c+="0");return e.s<0&&n?"-"+c:c}function z(e,u){for(var d,t=1,n=new $(e[0]);t<e.length;t++){if(!(d=new $(e[t])).s){n=d;break}u.call(n,d)&&(n=d)}return n}function H(e,u,d){for(var t=1,n=u.length;!u[--n];u.pop());for(n=u[0];n>=10;n/=10,t++);return(d=t+d*l-1)>D?e.c=e.e=null:d<P?e.c=[e.e=0]:(e.e=d,e.c=u),e}function V(e,u,d,t){var n,r,a,o,f,p,h,b=e.c,g=m;if(b){e:{for(n=1,o=b[0];o>=10;o/=10,n++);if((r=u-n)<0)r+=l,a=u,h=(f=b[p=0])/g[n-a-1]%10|0;else if((p=i((r+1)/l))>=b.length){if(!t)break e;for(;b.length<=p;b.push(0));f=h=0,n=1,a=(r%=l)-l+1}else{for(f=o=b[p],n=1;o>=10;o/=10,n++);h=(a=(r%=l)-l+n)<0?0:f/g[n-a-1]%10|0}if(t=t||u<0||null!=b[p+1]||(a<0?f:f%g[n-a-1]),t=d<4?(h||t)&&(0==d||d==(e.s<0?3:2)):h>5||5==h&&(4==d||t||6==d&&(r>0?a>0?f/g[n-a]:0:b[p-1])%10&1||d==(e.s<0?8:7)),u<1||!b[0])return b.length=0,t?(u-=e.e+1,b[0]=g[(l-u%l)%l],e.e=-u||0):b[0]=e.e=0,e;if(0==r?(b.length=p,o=1,p--):(b.length=p+1,o=g[l-r],b[p]=a>0?c(f/g[n-a]%g[a])*o:0),t)for(;;){if(0==p){for(r=1,a=b[0];a>=10;a/=10,r++);for(a=b[0]+=o,o=1;a>=10;a/=10,o++);r!=o&&(e.e++,b[0]==s&&(b[0]=1));break}if(b[p]+=o,b[p]!=s)break;b[p--]=0,o=1}for(r=b.length;0===b[--r];b.pop());}e.e>D?e.c=e.e=null:e.e<P&&(e.c=[e.e=0])}return e}function J(e){var u,d=e.e;return null===d?e.toString():(u=y(e.c),u=d<=O||d>=L?A(u,d):_(u,d,"0"),e.s<0?"-"+u:u)}return $.clone=e,$.ROUND_UP=0,$.ROUND_DOWN=1,$.ROUND_CEIL=2,$.ROUND_FLOOR=3,$.ROUND_HALF_UP=4,$.ROUND_HALF_DOWN=5,$.ROUND_HALF_EVEN=6,$.ROUND_HALF_CEIL=7,$.ROUND_HALF_FLOOR=8,$.EUCLID=9,$.config=$.set=function(e){var u,d;if(null!=e){if("object"!=typeof e)throw Error(o+"Object expected: "+e);if(e.hasOwnProperty(u="DECIMAL_PLACES")&&(x(d=e[u],0,b,u),R=d),e.hasOwnProperty(u="ROUNDING_MODE")&&(x(d=e[u],0,8,u),T=d),e.hasOwnProperty(u="EXPONENTIAL_AT")&&((d=e[u])&&d.pop?(x(d[0],-b,0,u),x(d[1],0,b,u),O=d[0],L=d[1]):(x(d,-b,b,u),O=-(L=d<0?-d:d))),e.hasOwnProperty(u="RANGE"))if((d=e[u])&&d.pop)x(d[0],-b,-1,u),x(d[1],1,b,u),P=d[0],D=d[1];else{if(x(d,-b,b,u),!d)throw Error(o+u+" cannot be zero: "+d);P=-(D=d<0?-d:d)}if(e.hasOwnProperty(u="CRYPTO")){if((d=e[u])!==!!d)throw Error(o+u+" not true or false: "+d);if(d){if("undefined"==typeof crypto||!crypto||!crypto.getRandomValues&&!crypto.randomBytes)throw N=!d,Error(o+"crypto unavailable");N=d}else N=d}if(e.hasOwnProperty(u="MODULO_MODE")&&(x(d=e[u],0,9,u),U=d),e.hasOwnProperty(u="POW_PRECISION")&&(x(d=e[u],0,b,u),M=d),e.hasOwnProperty(u="FORMAT")){if("object"!=typeof(d=e[u]))throw Error(o+u+" not an object: "+d);j=d}if(e.hasOwnProperty(u="ALPHABET")){if("string"!=typeof(d=e[u])||/^.$|[+-.\s]|(.).*\1/.test(d))throw Error(o+u+" invalid: "+d);G=d}}return{DECIMAL_PLACES:R,ROUNDING_MODE:T,EXPONENTIAL_AT:[O,L],RANGE:[P,D],CRYPTO:N,MODULO_MODE:U,POW_PRECISION:M,FORMAT:j,ALPHABET:G}},$.isBigNumber=function(e){if(!e||!0!==e._isBigNumber)return!1;if(!$.DEBUG)return!0;var u,d,t=e.c,n=e.e,r=e.s;e:if("[object Array]"=={}.toString.call(t)){if((1===r||-1===r)&&n>=-b&&n<=b&&n===c(n)){if(0===t[0]){if(0===n&&1===t.length)return!0;break e}if((u=(n+1)%l)<1&&(u+=l),String(t[0]).length==u){for(u=0;u<t.length;u++)if((d=t[u])<0||d>=s||d!==c(d))break e;if(0!==d)return!0}}}else if(null===t&&null===n&&(null===r||1===r||-1===r))return!0;throw Error(o+"Invalid BigNumber: "+e)},$.maximum=$.max=function(){return z(arguments,C.lt)},$.minimum=$.min=function(){return z(arguments,C.gt)},$.random=(r=9007199254740992*Math.random()&2097151?function(){return c(9007199254740992*Math.random())}:function(){return 8388608*(1073741824*Math.random()|0)+(8388608*Math.random()|0)},function(e){var u,d,t,n,a,f=0,s=[],p=new $(k);if(null==e?e=R:x(e,0,b),n=i(e/l),N)if(crypto.getRandomValues){for(u=crypto.getRandomValues(new Uint32Array(n*=2));f<n;)(a=131072*u[f]+(u[f+1]>>>11))>=9e15?(d=crypto.getRandomValues(new Uint32Array(2)),u[f]=d[0],u[f+1]=d[1]):(s.push(a%1e14),f+=2);f=n/2}else{if(!crypto.randomBytes)throw N=!1,Error(o+"crypto unavailable");for(u=crypto.randomBytes(n*=7);f<n;)(a=281474976710656*(31&u[f])+1099511627776*u[f+1]+4294967296*u[f+2]+16777216*u[f+3]+(u[f+4]<<16)+(u[f+5]<<8)+u[f+6])>=9e15?crypto.randomBytes(7).copy(u,f):(s.push(a%1e14),f+=7);f=n/7}if(!N)for(;f<n;)(a=r())<9e15&&(s[f++]=a%1e14);for(n=s[--f],e%=l,n&&e&&(a=m[l-e],s[f]=c(n/a)*a);0===s[f];s.pop(),f--);if(f<0)s=[t=0];else{for(t=-1;0===s[0];s.splice(0,1),t-=l);for(f=1,a=s[0];a>=10;a/=10,f++);f<l&&(t-=l-f)}return p.e=t,p.c=s,p}),$.sum=function(){for(var e=1,u=arguments,d=new $(u[0]);e<u.length;)d=d.plus(u[e++]);return d},t=function(){function e(e,u,d,t){for(var n,r,a=[0],i=0,c=e.length;i<c;){for(r=a.length;r--;a[r]*=u);for(a[0]+=t.indexOf(e.charAt(i++)),n=0;n<a.length;n++)a[n]>d-1&&(null==a[n+1]&&(a[n+1]=0),a[n+1]+=a[n]/d|0,a[n]%=d)}return a.reverse()}return function(u,t,n,r,a){var i,c,o,f,s,l,p,m,h=u.indexOf("."),b=R,g=T;for(h>=0&&(f=M,M=0,u=u.replace(".",""),l=(m=new $(t)).pow(u.length-h),M=f,m.c=e(_(y(l.c),l.e,"0"),10,n,"0123456789"),m.e=m.c.length),o=f=(p=e(u,t,n,a?(i=G,"0123456789"):(i="0123456789",G))).length;0==p[--f];p.pop());if(!p[0])return i.charAt(0);if(h<0?--o:(l.c=p,l.e=o,l.s=r,p=(l=d(l,m,b,g,n)).c,s=l.r,o=l.e),h=p[c=o+b+1],f=n/2,s=s||c<0||null!=p[c+1],s=g<4?(null!=h||s)&&(0==g||g==(l.s<0?3:2)):h>f||h==f&&(4==g||s||6==g&&1&p[c-1]||g==(l.s<0?8:7)),c<1||!p[0])u=s?_(i.charAt(1),-b,i.charAt(0)):i.charAt(0);else{if(p.length=c,s)for(--n;++p[--c]>n;)p[c]=0,c||(++o,p=[1].concat(p));for(f=p.length;!p[--f];);for(h=0,u="";h<=f;u+=i.charAt(p[h++]));u=_(u,o,i.charAt(0))}return u}}(),d=function(){function e(e,u,d){var t,n,r,a,i=0,c=e.length,o=u%h,f=u/h|0;for(e=e.slice();c--;)i=((n=o*(r=e[c]%h)+(t=f*r+(a=e[c]/h|0)*o)%h*h+i)/d|0)+(t/h|0)+f*a,e[c]=n%d;return i&&(e=[i].concat(e)),e}function u(e,u,d,t){var n,r;if(d!=t)r=d>t?1:-1;else for(n=r=0;n<d;n++)if(e[n]!=u[n]){r=e[n]>u[n]?1:-1;break}return r}function d(e,u,d,t){for(var n=0;d--;)e[d]-=n,n=e[d]<u[d]?1:0,e[d]=n*t+e[d]-u[d];for(;!e[0]&&e.length>1;e.splice(0,1));}return function(t,n,r,a,i){var o,f,p,m,h,b,y,v,x,w,A,_,E,F,I,B,S,C=t.s==n.s?1:-1,k=t.c,R=n.c;if(!(k&&k[0]&&R&&R[0]))return new $(t.s&&n.s&&(k?!R||k[0]!=R[0]:R)?k&&0==k[0]||!R?0*C:C/0:NaN);for(x=(v=new $(C)).c=[],C=r+(f=t.e-n.e)+1,i||(i=s,f=g(t.e/l)-g(n.e/l),C=C/l|0),p=0;R[p]==(k[p]||0);p++);if(R[p]>(k[p]||0)&&f--,C<0)x.push(1),m=!0;else{for(F=k.length,B=R.length,p=0,C+=2,(h=c(i/(R[0]+1)))>1&&(R=e(R,h,i),k=e(k,h,i),B=R.length,F=k.length),E=B,A=(w=k.slice(0,B)).length;A<B;w[A++]=0);S=R.slice(),S=[0].concat(S),I=R[0],R[1]>=i/2&&I++;do{if(h=0,(o=u(R,w,B,A))<0){if(_=w[0],B!=A&&(_=_*i+(w[1]||0)),(h=c(_/I))>1)for(h>=i&&(h=i-1),y=(b=e(R,h,i)).length,A=w.length;1==u(b,w,y,A);)h--,d(b,B<y?S:R,y,i),y=b.length,o=1;else 0==h&&(o=h=1),y=(b=R.slice()).length;if(y<A&&(b=[0].concat(b)),d(w,b,A,i),A=w.length,-1==o)for(;u(R,w,B,A)<1;)h++,d(w,B<A?S:R,A,i),A=w.length}else 0===o&&(h++,w=[0]);x[p++]=h,w[0]?w[A++]=k[E]||0:(w=[k[E]],A=1)}while((E++<F||null!=w[0])&&C--);m=null!=w[0],x[0]||x.splice(0,1)}if(i==s){for(p=1,C=x[0];C>=10;C/=10,p++);V(v,r+(v.e=p+f*l-1)+1,a,m)}else v.e=f,v.r=+m;return v}}(),E=/^(-?)0([xbo])(?=\w[\w.]*$)/i,F=/^([^.]+)\.$/,I=/^\.([^.]+)$/,B=/^-?(Infinity|NaN)$/,S=/^\s*\+(?=[\w.])|^\s+|\s+$/g,n=function(e,u,d,t){var n,r=d?u:u.replace(S,"");if(B.test(r))e.s=isNaN(r)?null:r<0?-1:1;else{if(!d&&(r=r.replace(E,function(e,u,d){return n="x"==(d=d.toLowerCase())?16:"b"==d?2:8,t&&t!=n?e:u}),t&&(n=t,r=r.replace(F,"$1").replace(I,"0.$1")),u!=r))return new $(r,n);if($.DEBUG)throw Error(o+"Not a"+(t?" base "+t:"")+" number: "+u);e.s=null}e.c=e.e=null},C.absoluteValue=C.abs=function(){var e=new $(this);return e.s<0&&(e.s=1),e},C.comparedTo=function(e,u){return v(this,new $(e,u))},C.decimalPlaces=C.dp=function(e,u){var d,t,n,r=this;if(null!=e)return x(e,0,b),null==u?u=T:x(u,0,8),V(new $(r),e+r.e+1,u);if(!(d=r.c))return null;if(t=((n=d.length-1)-g(this.e/l))*l,n=d[n])for(;n%10==0;n/=10,t--);return t<0&&(t=0),t},C.dividedBy=C.div=function(e,u){return d(this,new $(e,u),R,T)},C.dividedToIntegerBy=C.idiv=function(e,u){return d(this,new $(e,u),0,1)},C.exponentiatedBy=C.pow=function(e,u){var d,t,n,r,a,f,s,p,m=this;if((e=new $(e)).c&&!e.isInteger())throw Error(o+"Exponent not an integer: "+J(e));if(null!=u&&(u=new $(u)),a=e.e>14,!m.c||!m.c[0]||1==m.c[0]&&!m.e&&1==m.c.length||!e.c||!e.c[0])return p=new $(Math.pow(+J(m),a?2-w(e):+J(e))),u?p.mod(u):p;if(f=e.s<0,u){if(u.c?!u.c[0]:!u.s)return new $(NaN);(t=!f&&m.isInteger()&&u.isInteger())&&(m=m.mod(u))}else{if(e.e>9&&(m.e>0||m.e<-1||(0==m.e?m.c[0]>1||a&&m.c[1]>=24e7:m.c[0]<8e13||a&&m.c[0]<=9999975e7)))return r=m.s<0&&w(e)?-0:0,m.e>-1&&(r=1/r),new $(f?1/r:r);M&&(r=i(M/l+2))}for(a?(d=new $(.5),f&&(e.s=1),s=w(e)):s=(n=Math.abs(+J(e)))%2,p=new $(k);;){if(s){if(!(p=p.times(m)).c)break;r?p.c.length>r&&(p.c.length=r):t&&(p=p.mod(u))}if(n){if(0===(n=c(n/2)))break;s=n%2}else if(V(e=e.times(d),e.e+1,1),e.e>14)s=w(e);else{if(0===(n=+J(e)))break;s=n%2}m=m.times(m),r?m.c&&m.c.length>r&&(m.c.length=r):t&&(m=m.mod(u))}return t?p:(f&&(p=k.div(p)),u?p.mod(u):r?V(p,M,T,void 0):p)},C.integerValue=function(e){var u=new $(this);return null==e?e=T:x(e,0,8),V(u,u.e+1,e)},C.isEqualTo=C.eq=function(e,u){return 0===v(this,new $(e,u))},C.isFinite=function(){return!!this.c},C.isGreaterThan=C.gt=function(e,u){return v(this,new $(e,u))>0},C.isGreaterThanOrEqualTo=C.gte=function(e,u){return 1===(u=v(this,new $(e,u)))||0===u},C.isInteger=function(){return!!this.c&&g(this.e/l)>this.c.length-2},C.isLessThan=C.lt=function(e,u){return v(this,new $(e,u))<0},C.isLessThanOrEqualTo=C.lte=function(e,u){return-1===(u=v(this,new $(e,u)))||0===u},C.isNaN=function(){return!this.s},C.isNegative=function(){return this.s<0},C.isPositive=function(){return this.s>0},C.isZero=function(){return!!this.c&&0==this.c[0]},C.minus=function(e,u){var d,t,n,r,a=this,i=a.s;if(u=(e=new $(e,u)).s,!i||!u)return new $(NaN);if(i!=u)return e.s=-u,a.plus(e);var c=a.e/l,o=e.e/l,f=a.c,p=e.c;if(!c||!o){if(!f||!p)return f?(e.s=-u,e):new $(p?a:NaN);if(!f[0]||!p[0])return p[0]?(e.s=-u,e):new $(f[0]?a:3==T?-0:0)}if(c=g(c),o=g(o),f=f.slice(),i=c-o){for((r=i<0)?(i=-i,n=f):(o=c,n=p),n.reverse(),u=i;u--;n.push(0));n.reverse()}else for(t=(r=(i=f.length)<(u=p.length))?i:u,i=u=0;u<t;u++)if(f[u]!=p[u]){r=f[u]<p[u];break}if(r&&(n=f,f=p,p=n,e.s=-e.s),(u=(t=p.length)-(d=f.length))>0)for(;u--;f[d++]=0);for(u=s-1;t>i;){if(f[--t]<p[t]){for(d=t;d&&!f[--d];f[d]=u);--f[d],f[t]+=s}f[t]-=p[t]}for(;0==f[0];f.splice(0,1),--o);return f[0]?H(e,f,o):(e.s=3==T?-1:1,e.c=[e.e=0],e)},C.modulo=C.mod=function(e,u){var t,n,r=this;return e=new $(e,u),!r.c||!e.s||e.c&&!e.c[0]?new $(NaN):!e.c||r.c&&!r.c[0]?new $(r):(9==U?(n=e.s,e.s=1,t=d(r,e,0,3),e.s=n,t.s*=n):t=d(r,e,0,U),(e=r.minus(t.times(e))).c[0]||1!=U||(e.s=r.s),e)},C.multipliedBy=C.times=function(e,u){var d,t,n,r,a,i,c,o,f,p,m,b,y,v,x,w=this,A=w.c,_=(e=new $(e,u)).c;if(!(A&&_&&A[0]&&_[0]))return!w.s||!e.s||A&&!A[0]&&!_||_&&!_[0]&&!A?e.c=e.e=e.s=null:(e.s*=w.s,A&&_?(e.c=[0],e.e=0):e.c=e.e=null),e;for(t=g(w.e/l)+g(e.e/l),e.s*=w.s,(c=A.length)<(p=_.length)&&(y=A,A=_,_=y,n=c,c=p,p=n),n=c+p,y=[];n--;y.push(0));for(v=s,x=h,n=p;--n>=0;){for(d=0,m=_[n]%x,b=_[n]/x|0,r=n+(a=c);r>n;)d=((o=m*(o=A[--a]%x)+(i=b*o+(f=A[a]/x|0)*m)%x*x+y[r]+d)/v|0)+(i/x|0)+b*f,y[r--]=o%v;y[r]=d}return d?++t:y.splice(0,1),H(e,y,t)},C.negated=function(){var e=new $(this);return e.s=-e.s||null,e},C.plus=function(e,u){var d,t=this,n=t.s;if(u=(e=new $(e,u)).s,!n||!u)return new $(NaN);if(n!=u)return e.s=-u,t.minus(e);var r=t.e/l,a=e.e/l,i=t.c,c=e.c;if(!r||!a){if(!i||!c)return new $(n/0);if(!i[0]||!c[0])return c[0]?e:new $(i[0]?t:0*n)}if(r=g(r),a=g(a),i=i.slice(),n=r-a){for(n>0?(a=r,d=c):(n=-n,d=i),d.reverse();n--;d.push(0));d.reverse()}for((n=i.length)-(u=c.length)<0&&(d=c,c=i,i=d,u=n),n=0;u;)n=(i[--u]=i[u]+c[u]+n)/s|0,i[u]=s===i[u]?0:i[u]%s;return n&&(i=[n].concat(i),++a),H(e,i,a)},C.precision=C.sd=function(e,u){var d,t,n,r=this;if(null!=e&&e!==!!e)return x(e,1,b),null==u?u=T:x(u,0,8),V(new $(r),e,u);if(!(d=r.c))return null;if(t=(n=d.length-1)*l+1,n=d[n]){for(;n%10==0;n/=10,t--);for(n=d[0];n>=10;n/=10,t++);}return e&&r.e+1>t&&(t=r.e+1),t},C.shiftedBy=function(e){return x(e,-p,p),this.times("1e"+e)},C.squareRoot=C.sqrt=function(){var e,u,t,n,r,a=this,i=a.c,c=a.s,o=a.e,f=R+4,s=new $("0.5");if(1!==c||!i||!i[0])return new $(!c||c<0&&(!i||i[0])?NaN:i?a:1/0);if(0==(c=Math.sqrt(+J(a)))||c==1/0?(((u=y(i)).length+o)%2==0&&(u+="0"),c=Math.sqrt(+u),o=g((o+1)/2)-(o<0||o%2),t=new $(u=c==1/0?"1e"+o:(u=c.toExponential()).slice(0,u.indexOf("e")+1)+o)):t=new $(c+""),t.c[0])for((c=(o=t.e)+f)<3&&(c=0);;)if(r=t,t=s.times(r.plus(d(a,r,f,1))),y(r.c).slice(0,c)===(u=y(t.c)).slice(0,c)){if(t.e<o&&--c,"9999"!=(u=u.slice(c-3,c+1))&&(n||"4999"!=u)){+u&&(+u.slice(1)||"5"!=u.charAt(0))||(V(t,t.e+R+2,1),e=!t.times(t).eq(a));break}if(!n&&(V(r,r.e+R+2,0),r.times(r).eq(a))){t=r;break}f+=4,c+=4,n=1}return V(t,t.e+R+1,T,e)},C.toExponential=function(e,u){return null!=e&&(x(e,0,b),e++),Y(this,e,u,1)},C.toFixed=function(e,u){return null!=e&&(x(e,0,b),e=e+this.e+1),Y(this,e,u)},C.toFormat=function(e,u,d){var t,n=this;if(null==d)null!=e&&u&&"object"==typeof u?(d=u,u=null):e&&"object"==typeof e?(d=e,e=u=null):d=j;else if("object"!=typeof d)throw Error(o+"Argument not an object: "+d);if(t=n.toFixed(e,u),n.c){var r,a=t.split("."),i=+d.groupSize,c=+d.secondaryGroupSize,f=d.groupSeparator||"",s=a[0],l=a[1],p=n.s<0,m=p?s.slice(1):s,h=m.length;if(c&&(r=i,i=c,c=r,h-=r),i>0&&h>0){for(r=h%i||i,s=m.substr(0,r);r<h;r+=i)s+=f+m.substr(r,i);c>0&&(s+=f+m.slice(r)),p&&(s="-"+s)}t=l?s+(d.decimalSeparator||"")+((c=+d.fractionGroupSize)?l.replace(new RegExp("\\d{"+c+"}\\B","g"),"$&"+(d.fractionGroupSeparator||"")):l):s}return(d.prefix||"")+t+(d.suffix||"")},C.toFraction=function(e){var u,t,n,r,a,i,c,f,s,p,h,b,g=this,v=g.c;if(null!=e&&(!(c=new $(e)).isInteger()&&(c.c||1!==c.s)||c.lt(k)))throw Error(o+"Argument "+(c.isInteger()?"out of range: ":"not an integer: ")+J(c));if(!v)return new $(g);for(u=new $(k),s=t=new $(k),n=f=new $(k),b=y(v),a=u.e=b.length-g.e-1,u.c[0]=m[(i=a%l)<0?l+i:i],e=!e||c.comparedTo(u)>0?a>0?u:s:c,i=D,D=1/0,c=new $(b),f.c[0]=0;p=d(c,u,0,1),1!=(r=t.plus(p.times(n))).comparedTo(e);)t=n,n=r,s=f.plus(p.times(r=s)),f=r,u=c.minus(p.times(r=u)),c=r;return r=d(e.minus(t),n,0,1),f=f.plus(r.times(s)),t=t.plus(r.times(n)),f.s=s.s=g.s,h=d(s,n,a*=2,T).minus(g).abs().comparedTo(d(f,t,a,T).minus(g).abs())<1?[s,n]:[f,t],D=i,h},C.toNumber=function(){return+J(this)},C.toPrecision=function(e,u){return null!=e&&x(e,1,b),Y(this,e,u,2)},C.toString=function(e){var u,d=this,n=d.s,r=d.e;return null===r?n?(u="Infinity",n<0&&(u="-"+u)):u="NaN":(null==e?u=r<=O||r>=L?A(y(d.c),r):_(y(d.c),r,"0"):10===e?u=_(y((d=V(new $(d),R+r+1,T)).c),d.e,"0"):(x(e,2,G.length,"Base"),u=t(_(y(d.c),r,"0"),10,e,n,!0)),n<0&&d.c[0]&&(u="-"+u)),u},C.valueOf=C.toJSON=function(){return J(this)},C._isBigNumber=!0,null!=u&&$.set(u),$}()).default=r.BigNumber=r,void 0===(t=function(){return r}.call(u,d,u,e))||(e.exports=t)}()},17:function(e,u,d){"use strict";d.d(u,"b",function(){return n}),d.d(u,"c",function(){return r}),d.d(u,"a",function(){return a});var t=d(0);
/**
 * Hexadecimal functions.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2016
 * @license Apache-2.0
 */function n(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:" ",d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2;if(!e)return"";e instanceof ArrayBuffer&&(e=new Uint8Array(e));for(var t="",n=0;n<e.length;n++)t+=e[n].toString(16).padStart(d,"0")+u;return"0x"===u&&(t="0x"+t),"\\x"===u&&(t="\\x"+t),u.length?t.slice(0,-u.length):t}function r(e){if(!e)return"";e instanceof ArrayBuffer&&(e=new Uint8Array(e));for(var u=[],d=0;d<e.length;d++)u.push((e[d]>>>4).toString(16)),u.push((15&e[d]).toString(16));return u.join("")}function a(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Auto",d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2;if("None"!==u){var n="Auto"===u?/[^a-f\d]/gi:t.b.regexRep(u);e=e.replace(n,"")}for(var r=[],a=0;a<e.length;a+=d)r.push(parseInt(e.substr(a,d),16));return r}["Auto"].concat(["Space","Percent","Comma","Semi-colon","Colon","Line feed","CRLF","0x","\\x","None"])},18:function(e,u,d){"use strict";var t=d(13),n=d.n(t),r=d(10),a={Images:[{name:"Joint Photographic Experts Group image",extension:"jpg,jpeg,jpe,thm,mpo",mime:"image/jpeg",description:"",signature:{0:255,1:216,2:255,3:[192,196,219,221,224,225,226,227,228,229,231,232,234,235,236,237,238,254]},extractor:function(e,u){var d=new r.a(e.slice(u));for(;d.hasMore();){var t=d.getBytes(2);if(255!==t[0])throw new Error(`Invalid marker while parsing JPEG at pos ${d.position}: ${t}`);var n=0;switch(t[1]){case 216:case 1:break;case 217:return d.carve();case 192:case 193:case 194:case 195:case 196:case 197:case 198:case 199:case 200:case 201:case 202:case 203:case 204:case 205:case 206:case 207:case 219:case 222:case 224:case 225:case 226:case 227:case 228:case 229:case 230:case 231:case 232:case 233:case 234:case 235:case 236:case 237:case 238:case 239:case 254:n=d.readInt(2,"be"),d.position+=n-2;break;case 223:d.position++;break;case 220:case 221:d.position+=2;break;case 218:n=d.readInt(2,"be"),d.position+=n-2,d.continueUntil(255);break;case 0:case 208:case 209:case 210:case 211:case 212:case 213:case 214:case 215:default:d.continueUntil(255)}}throw new Error("Unable to parse JPEG successfully")}},{name:"Graphics Interchange Format image",extension:"gif",mime:"image/gif",description:"",signature:{0:71,1:73,2:70,3:56,4:[55,57],5:97},extractor:null},{name:"Portable Network Graphics image",extension:"png",mime:"image/png",description:"",signature:{0:137,1:80,2:78,3:71,4:13,5:10,6:26,7:10},extractor:function(e,u){var d=new r.a(e.slice(u));d.moveForwardsBy(8);var t=0,n="";for(;"IEND"!==n;)t=d.readInt(4,"be"),n=d.readString(4),d.moveForwardsBy(t+4);return d.carve()}},{name:"WEBP Image",extension:"webp",mime:"image/webp",description:"",signature:{8:87,9:69,10:66,11:80},extractor:null},{name:"Camera Image File Format",extension:"crw",mime:"image/x-canon-crw",description:"",signature:{6:72,7:69,8:65,9:80,10:67,11:67,12:68,13:82},extractor:null},{name:"Canon CR2 raw image",extension:"cr2",mime:"image/x-canon-cr2",description:"",signature:[{0:73,1:73,2:42,3:0,8:67,9:82},{0:77,1:77,2:0,3:42,8:67,9:82}],extractor:null},{name:"Tagged Image File Format image",extension:"tif",mime:"image/tiff",description:"",signature:[{0:73,1:73,2:42,3:0},{0:77,1:77,2:0,3:42}],extractor:null},{name:"Bitmap image",extension:"bmp",mime:"image/bmp",description:"",signature:{0:66,1:77,7:0,9:0,14:[12,40,56,64,108,124],15:0,16:0,17:0},extractor:function(e,u){var d=new r.a(e.slice(u));d.moveForwardsBy(2);var t=d.readInt(4,"le");return d.moveForwardsBy(t-6),d.carve()}},{name:"JPEG Extended Range image",extension:"jxr",mime:"image/vnd.ms-photo",description:"",signature:{0:73,1:73,2:188},extractor:null},{name:"Photoshop image",extension:"psd",mime:"image/vnd.adobe.photoshop",description:"",signature:{0:56,1:66,2:80,3:83,4:0,5:1,6:0,7:0,8:0,9:0,10:0,11:0},extractor:null},{name:"Photoshop Large Document",extension:"psb",mime:"application/x-photoshop",description:"",signature:{0:56,1:66,2:80,3:83,4:0,5:2,6:0,7:0,8:0,9:0,10:0,11:0,12:0},extractor:null},{name:"Paint Shop Pro image",extension:"psp",mime:"image/psp",description:"",signature:[{0:80,1:97,2:105,3:110,4:116,5:32,6:83,7:104,8:111,9:112,10:32,11:80,12:114,13:111,14:32,15:73,16:109},{0:126,1:66,2:75,3:0}],extractor:null},{name:"Icon image",extension:"ico",mime:"image/x-icon",description:"",signature:{0:0,1:0,2:1,3:0,4:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],5:0,6:[16,32,48,64,128],7:[16,32,48,64,128],9:0,10:[0,1]},extractor:null},{name:"Radiance High Dynamic Range image",extension:"hdr",mime:"image/vnd.radiance",description:"",signature:{0:35,1:63,2:82,3:65,4:68,5:73,6:65,7:78,8:67,9:69,10:10},extractor:null},{name:"Sony ARW image",extension:"arw",mime:"image/x-raw",description:"",signature:{0:5,1:0,2:0,3:0,4:65,5:87,6:49,7:46},extractor:null},{name:"Fujifilm Raw Image",extension:"raf",mime:"image/x-raw",description:"",signature:{0:70,1:85,2:74,3:73,4:70,5:73,6:76,7:77,8:67,9:67,10:68,11:45,12:82,13:65,14:87},extractor:null},{name:"Minolta RAW image",extension:"mrw",mime:"image/x-raw",description:"",signature:{0:0,1:77,2:82,3:77},extractor:null},{name:"Adobe Bridge Thumbnail Cache",extension:"bct",mime:"application/octet-stream",description:"",signature:{0:108,1:110,2:98,3:116,4:2,5:0,6:0,7:0},extractor:null},{name:"Microsoft Document Imaging",extension:"mdi",mime:"image/vnd.ms-modi",description:"",signature:{0:69,1:80,2:42,3:0},extractor:null}],Video:[{name:"Matroska Multimedia Container",extension:"mkv",mime:"video/x-matroska",description:"",signature:{31:109,32:97,33:116,34:114,35:111,36:115,37:107,38:97},extractor:null},{name:"WEBM video",extension:"webm",mime:"video/webm",description:"",signature:{0:26,1:69,2:223,3:163},extractor:null},{name:"MPEG-4 video",extension:"mp4",mime:"video/mp4",description:"",signature:[{0:0,1:0,2:0,3:[24,32],4:102,5:116,6:121,7:112},{0:51,1:103,2:112,3:53},{0:0,1:0,2:0,3:28,4:102,5:116,6:121,7:112,8:109,9:112,10:52,11:50,16:109,17:112,18:52,19:49,20:109,21:112,22:52,23:50,24:105,25:115,26:111,27:109}],extractor:null},{name:"M4V video",extension:"m4v",mime:"video/x-m4v",description:"",signature:{0:0,1:0,2:0,3:28,4:102,5:116,6:121,7:112,8:77,9:52,10:86},extractor:null},{name:"Quicktime video",extension:"mov",mime:"video/quicktime",description:"",signature:{0:0,1:0,2:0,3:20,4:102,5:116,6:121,7:112},extractor:null},{name:"Audio Video Interleave",extension:"avi",mime:"video/x-msvideo",description:"",signature:{0:82,1:73,2:70,3:70,8:65,9:86,10:73},extractor:null},{name:"Windows Media Video",extension:"wmv",mime:"video/x-ms-wmv",description:"",signature:{0:48,1:38,2:178,3:117,4:142,5:102,6:207,7:17,8:166,9:217},extractor:null},{name:"MPEG video",extension:"mpg",mime:"video/mpeg",description:"",signature:{0:0,1:0,2:1,3:186},extractor:null},{name:"Flash Video",extension:"flv",mime:"video/x-flv",description:"",signature:{0:70,1:76,2:86,3:1},extractor:function(e,u){var d=new r.a(e.slice(u));d.moveForwardsBy(5);var t=d.readInt(4,"be");d.moveForwardsBy(t-9);var n=-11;for(;d.hasMore();){var a=d.readInt(4,"be"),i=d.readInt(1);if([8,9,18].indexOf(i)<0){d.moveBackwardsBy(1);break}if(a!==n+11){d.moveBackwardsBy(n+11+5);break}n=d.readInt(3,"be"),d.moveForwardsBy(7+n)}return d.carve()}}],Audio:[{name:"Waveform Audio",extension:"wav",mime:"audio/x-wav",description:"",signature:{0:82,1:73,2:70,3:70,8:87,9:65,10:86,11:69},extractor:null},{name:"OGG audio",extension:"ogg",mime:"audio/ogg",description:"",signature:{0:79,1:103,2:103,3:83},extractor:null},{name:"Musical Instrument Digital Interface audio",extension:"midi",mime:"audio/midi",description:"",signature:{0:77,1:84,2:104,3:100},extractor:null},{name:"MPEG-3 audio",extension:"mp3",mime:"audio/mpeg",description:"",signature:[{0:73,1:68,2:51},{0:255,1:251}],extractor:null},{name:"MPEG-4 Part 14 audio",extension:"m4a",mime:"audio/m4a",description:"",signature:[{4:102,5:116,6:121,7:112,8:77,9:52,10:65},{0:77,1:52,2:65,3:32}],extractor:null},{name:"Free Lossless Audio Codec",extension:"flac",mime:"audio/x-flac",description:"",signature:{0:102,1:76,2:97,3:67},extractor:null},{name:"Adaptive Multi-Rate audio codec",extension:"amr",mime:"audio/amr",description:"",signature:{0:35,1:33,2:65,3:77,4:82,5:10},extractor:null},{name:"Audacity",extension:"au",mime:"audio/x-au",description:"",signature:{0:100,1:110,2:115,3:46,24:65,25:117,26:100,27:97,28:99,29:105,30:116,31:121,32:66,33:108,34:111,35:99,36:107,37:70,38:105,39:108,40:101},extractor:null},{name:"Audacity Block",extension:"auf",mime:"application/octet-stream",description:"",signature:{0:65,1:117,2:100,3:97,4:99,5:105,6:116,7:121,8:66,9:108,10:111,11:99,12:107,13:70,14:105,15:108,16:101},extractor:null},{name:"Audio Interchange File",extension:"aif",mime:"audio/x-aiff",description:"",signature:{0:70,1:79,2:82,3:77,8:65,9:73,10:70,11:70},extractor:null},{name:"Audio Interchange File (compressed)",extension:"aifc",mime:"audio/x-aifc",description:"",signature:{0:70,1:79,2:82,3:77,8:65,9:73,10:70,11:67},extractor:null}],Documents:[{name:"Portable Document Format",extension:"pdf",mime:"application/pdf",description:"",signature:{0:37,1:80,2:68,3:70},extractor:function(e,u){var d=new r.a(e.slice(u));return d.continueUntil([37,37,69,79,70]),d.moveForwardsBy(5),d.consumeIf(13),d.consumeIf(10),d.carve()}},{name:"PostScript",extension:"ps",mime:"application/postscript",description:"",signature:{0:37,1:33},extractor:null},{name:"Rich Text Format",extension:"rtf",mime:"application/rtf",description:"",signature:{0:123,1:92,2:114,3:116,4:102},extractor:function(e,u){var d=new r.a(e.slice(u)),t=0;if(123!==d.readInt(1))throw new Error("Not a valid RTF file");t++;for(;t>0&&d.hasMore();)switch(d.readInt(1)){case 123:t++;break;case 125:t--;break;case 92:d.consumeIf(92),d.position++}return d.carve()}},{name:"Microsoft Office documents/OLE2",extension:"ole2,doc,xls,dot,ppt,xla,ppa,pps,pot,msi,sdw,db,vsd,msg",mime:"application/msword,application/vnd.ms-excel,application/vnd.ms-powerpoint",description:"Microsoft Office documents",signature:{0:208,1:207,2:17,3:224,4:161,5:177,6:26,7:225},extractor:null},{name:"Microsoft Office 2007+ documents",extension:"docx,xlsx,pptx",mime:"application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.openxmlformats-officedocument.presentationml.presentation",description:"",signature:{38:95,39:84,40:121,41:112,42:101,43:115,44:93,45:46,46:120,47:109,48:108},extractor:i},{name:"EPUB e-book",extension:"epub",mime:"application/epub+zip",description:"",signature:{0:80,1:75,2:3,3:4,30:109,31:105,32:109,33:101,34:116,35:121,36:112,37:101,38:97,39:112,40:112,41:108,42:105,43:99,44:97,45:116,46:105,47:111,48:110,49:47,50:101,51:112,52:117,53:98,54:43,55:122,56:105,57:112},extractor:i}],Applications:[{name:"Windows Portable Executable",extension:"exe,dll,drv,vxd,sys,ocx,vbx,com,fon,scr",mime:"application/x-msdownload",description:"",signature:{0:77,1:90,3:[0,1,2],5:[0,1,2]},extractor:function(e,u){var d=new r.a(e.slice(u));d.moveTo(60);var t=d.readInt(4,"le");d.moveTo(t),d.moveForwardsBy(6);var n=d.readInt(2,"le");d.moveForwardsBy(12);var a=d.readInt(2,"le");d.moveForwardsBy(2+a),d.moveForwardsBy(40*(n-1)),d.moveForwardsBy(16);var i=d.readInt(4,"le"),c=d.readInt(4,"le");return d.moveTo(c+i),d.carve()}},{name:"Executable and Linkable Format file",extension:"elf,bin,axf,o,prx,so",mime:"application/x-executable",description:"Executable and Linkable Format file. No standard file extension.",signature:{0:127,1:69,2:76,3:70},extractor:function(e,u){var d=new r.a(e.slice(u));d.moveForwardsBy(4);var t=1===d.readInt(1),n=1===d.readInt(1)?"le":"be";d.moveForwardsBy(t?26:34);var a=t?d.readInt(4,n):d.readInt(8,n);d.moveForwardsBy(10);var i=d.readInt(2,n),c=d.readInt(2,n);return d.moveTo(a),d.moveForwardsBy(i*c),d.carve()}},{name:"Adobe Flash",extension:"swf",mime:"application/x-shockwave-flash",description:"",signature:{0:[67,70],1:87,2:83},extractor:null},{name:"Java Class",extension:"class",mime:"application/java-vm",description:"",signature:{0:202,1:254,2:186,3:190},extractor:null},{name:"Dalvik Executable",extension:"dex",mime:"application/octet-stream",description:"Dalvik Executable as used by Android",signature:{0:100,1:101,2:120,3:10,4:48,5:51,6:53,7:0},extractor:null},{name:"Google Chrome Extension",extension:"crx",mime:"application/crx",description:"Google Chrome extension or packaged app",signature:{0:67,1:114,2:50,3:52},extractor:null}],Archives:[{name:"PKZIP archive",extension:"zip",mime:"application/zip",description:"",signature:{0:80,1:75,2:[3,5,7],3:[4,6,8]},extractor:i},{name:"TAR archive",extension:"tar",mime:"application/x-tar",description:"",signature:{257:117,258:115,259:116,260:97,261:114},extractor:null},{name:"Roshal Archive",extension:"rar",mime:"application/x-rar-compressed",description:"",signature:{0:82,1:97,2:114,3:33,4:26,5:7,6:[0,1]},extractor:null},{name:"Gzip",extension:"gz",mime:"application/gzip",description:"",signature:{0:31,1:139,2:8},extractor:function(e,u){var d=new r.a(e.slice(u));d.moveForwardsBy(3);var t=d.readInt(1);if(d.moveForwardsBy(4),d.readInt(1),d.moveForwardsBy(1),4&t){var n=d.readInt(2,"le");d.moveForwardsby(n)}8&t&&(d.continueUntil(0),d.moveForwardsBy(1));16&t&&(d.continueUntil(0),d.moveForwardsBy(1));2&t&&d.moveForwardsBy(2);return p(d),d.moveForwardsBy(8),d.carve()}},{name:"Bzip2",extension:"bz2",mime:"application/x-bzip2",description:"",signature:{0:66,1:90,2:104},extractor:null},{name:"7zip",extension:"7z",mime:"application/x-7z-compressed",description:"",signature:{0:55,1:122,2:188,3:175,4:39,5:28},extractor:null},{name:"Zlib Deflate",extension:"zlib",mime:"application/x-deflate",description:"",signature:{0:120,1:[1,156,218,94]},extractor:function(e,u){var d=new r.a(e.slice(u));d.moveForwardsBy(1),32&d.readInt(1)&&d.moveForwardsBy(4);return p(d),d.moveForwardsBy(4),d.carve()}},{name:"xz compression",extension:"xz",mime:"application/x-xz",description:"",signature:{0:253,1:55,2:122,3:88,4:90,5:0},extractor:null},{name:"Tarball",extension:"tar.z",mime:"application/x-gtar",description:"",signature:{0:31,1:[157,160]},extractor:null},{name:"ISO disk image",extension:"iso",mime:"application/octet-stream",description:"ISO 9660 CD/DVD image file",signature:[{32769:67,32770:68,32771:48,32772:48,32773:49},{34817:67,34818:68,34819:48,34820:48,34821:49},{36865:67,36866:68,36867:48,36868:48,36869:49}],extractor:null},{name:"Virtual Machine Disk",extension:"vmdk",mime:"application/vmdk,application/x-virtualbox-vmdk",description:"",signature:{0:75,1:68,2:77},extractor:null},{name:"ARJ Archive",extension:"arj",mime:"application/x-arj-compressed",description:"",signature:{0:96,1:234,8:[0,16,20],9:0,10:2},extractor:null},{name:"WinAce Archive",extension:"ace",mime:"application/x-ace-compressed",description:"",signature:{7:42,8:42,9:65,10:67,11:69,12:42,13:42},extractor:null},{name:"Macintosh BinHex Encoded File",extension:"hqx",mime:"application/mac-binhex",description:"",signature:{11:109,12:117,13:115,14:116,15:32,16:98,17:101,18:32,19:99,20:111,21:110,22:118,23:101,24:114,25:116,26:101,27:100,28:32,29:119,30:105,31:116,32:104,33:32,34:66,35:105,36:110,37:72,38:101,39:120},extractor:null},{name:"ALZip Archive",extension:"alz",mime:"application/octet-stream",description:"",signature:{0:65,1:76,2:90,3:1,4:10,5:0,6:0,7:0},extractor:null},{name:"KGB Compressed Archive",extension:"kgb",mime:"application/x-kgb-compressed",description:"",signature:{0:75,1:71,2:66,3:95,4:97,5:114,6:99,7:104,8:32,9:45},extractor:null}],Miscellaneous:[{name:"UTF-8 text file",extension:"txt",mime:"text/plain",description:"UTF-8 encoded Unicode byte order mark, commonly but not exclusively seen in text files.",signature:{0:239,1:187,2:191},extractor:null},{name:"UTF-32 LE file",extension:"utf32le",mime:"charset/utf32le",description:"Little-endian UTF-32 encoded Unicode byte order mark.",signature:{0:255,1:254,2:0,3:0},extractor:null},{name:"UTF-16 LE file",extension:"utf16le",mime:"charset/utf16le",description:"Little-endian UTF-16 encoded Unicode byte order mark.",signature:{0:255,1:254},extractor:null},{name:"Web Open Font Format",extension:"woff",mime:"application/font-woff",description:"",signature:{0:119,1:79,2:70,3:70,4:0,5:1,6:0,7:0},extractor:null},{name:"Web Open Font Format 2",extension:"woff2",mime:"application/font-woff",description:"",signature:{0:119,1:79,2:70,3:50,4:0,5:1,6:0,7:0},extractor:null},{name:"Embedded OpenType font",extension:"eot",mime:"application/octet-stream",description:"",signature:[{8:2,9:0,10:1,34:76,35:80},{8:1,9:0,10:0,34:76,35:80},{8:2,9:0,10:2,34:76,35:80}],extractor:null},{name:"TrueType Font",extension:"ttf",mime:"application/font-sfnt",description:"",signature:{0:0,1:1,2:0,3:0,4:0},extractor:null},{name:"OpenType Font",extension:"otf",mime:"application/font-sfnt",description:"",signature:{0:79,1:84,2:84,3:79,4:0},extractor:null},{name:"SQLite",extension:"sqlite",mime:"application/x-sqlite3",description:"",signature:{0:83,1:81,2:76,3:105},extractor:null},{name:"BitTorrent link",extension:"torrent",mime:"application/x-bittorrent",description:"",signature:[{0:100,1:56,2:58,3:97,4:110,5:110,6:111,7:117,8:110,9:99,10:101,11:35,12:35,13:58},{0:100,1:52,2:58,3:105,4:110,5:102,6:111,7:100,8:[52,53,54],9:58}],extractor:null}]};function i(e,u){var d=new r.a(e.slice(u));d.continueUntil([80,75,5,6]),d.moveForwardsBy(20);var t=d.readInt(2,"le");return d.moveForwardsBy(t),d.carve()}for(var c=new Array(288),o=0;o<c.length;o++)c[o]=o<=143?8:o<=255?9:o<=279?7:8;var f=g(c),s=g(new Array(30).fill(5)),l=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];function p(e){for(var u=0;!u;){u=e.readBits(1);var d=e.readBits(2);if(0===d){e.moveForwardsBy(1);var t=e.readInt(2,"le");e.moveForwardsBy(2+t)}else if(1===d)b(e,f,s);else{if(2!==d)throw new Error(`Invalid block type while parsing DEFLATE stream at pos ${e.position}`);for(var n=e.readBits(5)+257,r=e.readBits(5)+1,a=e.readBits(4)+4,i=new Uint8Array(l.length),c=0;c<a;c++)i[l[c]]=e.readBits(3);for(var o=g(i),p=new Uint8Array(n+r),m=void 0,h=void 0,v=void 0,x=0;x<n+r;)switch(m=y(e,o)){case 16:for(h=3+e.readBits(2);h--;)p[x++]=v;break;case 17:for(h=3+e.readBits(3);h--;)p[x++]=0;v=0;break;case 18:for(h=11+e.readBits(7);h--;)p[x++]=0;v=0;break;default:p[x++]=m,v=m}b(e,g(p.subarray(0,n)),g(p.subarray(n)))}}e.bitPos>0&&e.moveForwardsBy(1)}var m=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0],h=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13];function b(e,u,d){for(var t,n=0;(t=y(e,u))&&256!==t;){if(++n>1e4)throw new Error("Caught in probable infinite loop while parsing Huffman Block");t<256||(e.readBits(m[t-257]),t=y(e,d),e.readBits(h[t]))}}function g(e){for(var u=Math.max.apply(Math,e),d=Math.min.apply(Math,e),t=1<<u,n=new Uint32Array(t),r=1,a=0,i=2;r<=u;){for(var c=0;c<e.length;c++)if(e[c]===r){var o=void 0,f=void 0,s=void 0;for(o=0,f=a,s=0;s<r;s++)o=o<<1|1&f,f>>=1;for(var l=r<<16|c,p=o;p<t;p+=i)n[p]=l;a++}r++,a<<=1,i<<=1}return[n,u,d]}function y(e,u){var d=n()(u,2),t=d[0],r=d[1],a=t[e.readBits(r)&(1<<r)-1],i=a>>>16;if(i>r)throw new Error(`Invalid Huffman Code length while parsing DEFLATE block at pos ${e.position}: ${i}`);return e.moveBackwardsByBits(r-i),65535&a}d(0);
/**
 * File type functions
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 *
 */
function v(e,u){var d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(e.length){for(var t=0;t<e.length;t++)if(x(e[t],u,d))return!0;return!1}return x(e,u,d)}function x(e,u){var d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;for(var t in e){var n=parseInt(t,10)+d;switch(typeof e[t]){case"number":if(u[n]!==e[t])return!1;break;case"object":if(e[t].indexOf(u[n])<0)return!1;break;case"function":if(!e[t](u[n]))return!1;break;default:throw new Error(`Unrecognised signature type at offset ${t}`)}}return!0}function w(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Object.keys(a);if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),!(e&&e.length>1))return[];var d=[],t={};for(var n in a)u.includes(n)&&(t[n]=a[n]);for(var r in t){t[r].forEach(function(u){v(u.signature,e)&&d.push(u)})}return d}function A(e){return function(e,u){var d=w(u);if(!d||!d.length)return!1;if("string"==typeof e)return d.reduce(function(u,d){var t=!!d.mime.startsWith(e)&&d.mime;return u||t},!1);if(e instanceof RegExp)return d.reduce(function(u,d){var t=!!e.test(d.mime)&&d.mime;return u||t},!1);throw new Error("Invalid type input.")}("image",e)}d.d(u,"a",function(){return w}),d.d(u,"b",function(){return A})},197:function(e,u){e.exports=function(e){return e&&e.__esModule?e:{default:e}}},198:function(e,u){var d=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=d)},199:function(e,u){var d=e.exports={version:"2.6.9"};"number"==typeof __e&&(__e=d)},2:function(e,u){function d(e,u){for(var d=0;d<u.length;d++){var t=u[d];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,t.key,t)}}e.exports=function(e,u,t){return u&&d(e.prototype,u),t&&d(e,t),e}},217:function(e,u,d){var t=d(669)("wks"),n=d(671),r=d(198).Symbol,a="function"==typeof r;(e.exports=function(e){return t[e]||(t[e]=a&&r[e]||(a?r:n)("Symbol."+e))}).store=t},24:function(e,u,d){!function(e){var u,d,t,n=String.fromCharCode;function r(e){for(var u,d,t=[],n=0,r=e.length;n<r;)(u=e.charCodeAt(n++))>=55296&&u<=56319&&n<r?56320==(64512&(d=e.charCodeAt(n++)))?t.push(((1023&u)<<10)+(1023&d)+65536):(t.push(u),n--):t.push(u);return t}function a(e){if(e>=55296&&e<=57343)throw Error("Lone surrogate U+"+e.toString(16).toUpperCase()+" is not a scalar value")}function i(e,u){return n(e>>u&63|128)}function c(e){if(0==(4294967168&e))return n(e);var u="";return 0==(4294965248&e)?u=n(e>>6&31|192):0==(4294901760&e)?(a(e),u=n(e>>12&15|224),u+=i(e,6)):0==(4292870144&e)&&(u=n(e>>18&7|240),u+=i(e,12),u+=i(e,6)),u+=n(63&e|128)}function o(){if(t>=d)throw Error("Invalid byte index");var e=255&u[t];if(t++,128==(192&e))return 63&e;throw Error("Invalid continuation byte")}function f(){var e,n;if(t>d)throw Error("Invalid byte index");if(t==d)return!1;if(e=255&u[t],t++,0==(128&e))return e;if(192==(224&e)){if((n=(31&e)<<6|o())>=128)return n;throw Error("Invalid continuation byte")}if(224==(240&e)){if((n=(15&e)<<12|o()<<6|o())>=2048)return a(n),n;throw Error("Invalid continuation byte")}if(240==(248&e)&&(n=(7&e)<<18|o()<<12|o()<<6|o())>=65536&&n<=1114111)return n;throw Error("Invalid UTF-8 detected")}e.version="3.0.0",e.encode=function(e){for(var u=r(e),d=u.length,t=-1,n="";++t<d;)n+=c(u[t]);return n},e.decode=function(e){u=r(e),d=u.length,t=0;for(var a,i=[];!1!==(a=f());)i.push(a);return function(e){for(var u,d=e.length,t=-1,r="";++t<d;)(u=e[t])>65535&&(r+=n((u-=65536)>>>10&1023|55296),u=56320|1023&u),r+=n(u);return r}(i)}}(u)},25:function(e,u){e.exports=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}},26:function(e,u,d){var t,n;!function(r,a){"use strict";void 0===(n="function"==typeof(t=function(){var e=function(){},u="undefined",d=["trace","debug","info","warn","error"];function t(e,u){var d=e[u];if("function"==typeof d.bind)return d.bind(e);try{return Function.prototype.bind.call(d,e)}catch(u){return function(){return Function.prototype.apply.apply(d,[e,arguments])}}}function n(u,t){for(var n=0;n<d.length;n++){var r=d[n];this[r]=n<u?e:this.methodFactory(r,u,t)}this.log=this.debug}function r(e,d,t){return function(){typeof console!==u&&(n.call(this,d,t),this[e].apply(this,arguments))}}function a(d,n,a){return function(d){return"debug"===d&&(d="log"),typeof console!==u&&(void 0!==console[d]?t(console,d):void 0!==console.log?t(console,"log"):e)}(d)||r.apply(this,arguments)}function i(e,t,r){var i,c=this,o="loglevel";function f(){var e;if(typeof window!==u){try{e=window.localStorage[o]}catch(e){}if(typeof e===u)try{var d=window.document.cookie,t=d.indexOf(encodeURIComponent(o)+"=");-1!==t&&(e=/^([^;]+)/.exec(d.slice(t))[1])}catch(e){}return void 0===c.levels[e]&&(e=void 0),e}}e&&(o+=":"+e),c.name=e,c.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},c.methodFactory=r||a,c.getLevel=function(){return i},c.setLevel=function(t,r){if("string"==typeof t&&void 0!==c.levels[t.toUpperCase()]&&(t=c.levels[t.toUpperCase()]),!("number"==typeof t&&t>=0&&t<=c.levels.SILENT))throw"log.setLevel() called with invalid level: "+t;if(i=t,!1!==r&&function(e){var t=(d[e]||"silent").toUpperCase();if(typeof window!==u){try{return void(window.localStorage[o]=t)}catch(e){}try{window.document.cookie=encodeURIComponent(o)+"="+t+";"}catch(e){}}}(t),n.call(c,t,e),typeof console===u&&t<c.levels.SILENT)return"No console available for logging"},c.setDefaultLevel=function(e){f()||c.setLevel(e,!1)},c.enableAll=function(e){c.setLevel(c.levels.TRACE,e)},c.disableAll=function(e){c.setLevel(c.levels.SILENT,e)};var s=f();null==s&&(s=null==t?"WARN":t),c.setLevel(s,!1)}var c=new i,o={};c.getLogger=function(e){if("string"!=typeof e||""===e)throw new TypeError("You must supply a name when creating a logger.");var u=o[e];return u||(u=o[e]=new i(e,c.getLevel(),c.methodFactory)),u};var f=typeof window!==u?window.log:void 0;return c.noConflict=function(){return typeof window!==u&&window.log===c&&(window.log=f),c},c.getLoggers=function(){return o},c})?t.call(u,d,u,e):t)||(e.exports=n)}()},27:function(e,u){var d,t,n=e.exports={};function r(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function i(e){if(d===setTimeout)return setTimeout(e,0);if((d===r||!d)&&setTimeout)return d=setTimeout,setTimeout(e,0);try{return d(e,0)}catch(u){try{return d.call(null,e,0)}catch(u){return d.call(this,e,0)}}}!function(){try{d="function"==typeof setTimeout?setTimeout:r}catch(e){d=r}try{t="function"==typeof clearTimeout?clearTimeout:a}catch(e){t=a}}();var c,o=[],f=!1,s=-1;function l(){f&&c&&(f=!1,c.length?o=c.concat(o):s=-1,o.length&&p())}function p(){if(!f){var e=i(l);f=!0;for(var u=o.length;u;){for(c=o,o=[];++s<u;)c&&c[s].run();s=-1,u=o.length}c=null,f=!1,function(e){if(t===clearTimeout)return clearTimeout(e);if((t===a||!t)&&clearTimeout)return t=clearTimeout,clearTimeout(e);try{t(e)}catch(u){try{return t.call(null,e)}catch(u){return t.call(this,e)}}}(e)}}function m(e,u){this.fun=e,this.array=u}function h(){}n.nextTick=function(e){var u=new Array(arguments.length-1);if(arguments.length>1)for(var d=1;d<arguments.length;d++)u[d-1]=arguments[d];o.push(new m(e,u)),1!==o.length||f||i(p)},m.prototype.run=function(){this.fun.apply(null,this.array)},n.title="browser",n.browser=!0,n.env={},n.argv=[],n.version="",n.versions={},n.on=h,n.addListener=h,n.once=h,n.off=h,n.removeListener=h,n.removeAllListeners=h,n.emit=h,n.prependListener=h,n.prependOnceListener=h,n.listeners=function(e){return[]},n.binding=function(e){throw new Error("process.binding is not supported")},n.cwd=function(){return"/"},n.chdir=function(e){throw new Error("process.chdir is not supported")},n.umask=function(){return 0}},28:function(e,u){var d;d=function(){return this}();try{d=d||new Function("return this")()}catch(e){"object"==typeof window&&(d=window)}e.exports=d},29:function(e,u,d){"use strict";d.d(u,"a",function(){return n});var t=d(0);
/**
 * Binary functions.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 */function n(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Space",d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:8,n=t.b.regexRep(u);e=e.replace(n,"");for(var r=[],a=0;a<e.length;a+=d)r.push(parseInt(e.substr(a,d),2));return r}},3:function(e,u){function d(u){return e.exports=d=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},d(u)}e.exports=d},30:function(e,u,d){"use strict";d.d(u,"a",function(){return n});var t=d(0);
/**
 * Decimal functions.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 */function n(e){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Auto";u=t.b.charRep(u);var d=[],n=e.split(u);""===n[n.length-1]&&(n=n.slice(0,n.length-1));for(var r=0;r<n.length;r++)d[r]=parseInt(n[r],10);return d}},311:function(e,u,d){var t=d(198),n=d(199),r=d(1259),a=d(312),i=d(341),c=function(e,u,d){var o,f,s,l=e&c.F,p=e&c.G,m=e&c.S,h=e&c.P,b=e&c.B,g=e&c.W,y=p?n:n[u]||(n[u]={}),v=y.prototype,x=p?t:m?t[u]:(t[u]||{}).prototype;for(o in p&&(d=u),d)(f=!l&&x&&void 0!==x[o])&&i(y,o)||(s=f?x[o]:d[o],y[o]=p&&"function"!=typeof x[o]?d[o]:b&&f?r(s,t):g&&x[o]==s?function(e){var u=function(u,d,t){if(this instanceof e){switch(arguments.length){case 0:return new e;case 1:return new e(u);case 2:return new e(u,d)}return new e(u,d,t)}return e.apply(this,arguments)};return u.prototype=e.prototype,u}(s):h&&"function"==typeof s?r(Function.call,s):s,h&&((y.virtual||(y.virtual={}))[o]=s,e&c.R&&v&&!v[o]&&a(v,o,s)))};c.F=1,c.G=2,c.S=4,c.P=8,c.B=16,c.W=32,c.U=64,c.R=128,e.exports=c},312:function(e,u,d){var t=d(417),n=d(667);e.exports=d(340)?function(e,u,d){return t.f(e,u,n(1,d))}:function(e,u,d){return e[u]=d,e}},339:function(e,u,d){var t=d(418);e.exports=function(e){if(!t(e))throw TypeError(e+" is not an object!");return e}},340:function(e,u,d){e.exports=!d(419)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},341:function(e,u){var d={}.hasOwnProperty;e.exports=function(e,u){return d.call(e,u)}},342:function(e,u){e.exports=function(e){if(null==e)throw TypeError("Can't call method on  "+e);return e}},343:function(e,u){e.exports={}},4:function(e,u,d){var t=d(43),n=d(25);e.exports=function(e,u){return!u||"object"!==t(u)&&"function"!=typeof u?n(e):u}},417:function(e,u,d){var t=d(339),n=d(1261),r=d(1262),a=Object.defineProperty;u.f=d(340)?Object.defineProperty:function(e,u,d){if(t(e),u=r(u,!0),t(d),n)try{return a(e,u,d)}catch(e){}if("get"in d||"set"in d)throw TypeError("Accessors not supported!");return"value"in d&&(e[u]=d.value),e}},418:function(e,u){e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},419:function(e,u){e.exports=function(e){try{return!!e()}catch(e){return!0}}},420:function(e,u,d){var t=d(1266),n=d(342);e.exports=function(e){return t(n(e))}},421:function(e,u){var d={}.toString;e.exports=function(e){return d.call(e).slice(8,-1)}},422:function(e,u){var d=Math.ceil,t=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?t:d)(e)}},423:function(e,u,d){var t=d(669)("keys"),n=d(671);e.exports=function(e){return t[e]||(t[e]=n(e))}},424:function(e,u,d){e.exports=d(1278)},43:function(e,u){function d(e){return(d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function t(u){return"function"==typeof Symbol&&"symbol"===d(Symbol.iterator)?e.exports=t=function(e){return d(e)}:e.exports=t=function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":d(e)},t(u)}e.exports=t},44:function(e,u){function d(u,t){return e.exports=d=Object.setPrototypeOf||function(e,u){return e.__proto__=u,e},d(u,t)}e.exports=d},45:function(e,u,d){"use strict";u.byteLength=function(e){var u=o(e),d=u[0],t=u[1];return 3*(d+t)/4-t},u.toByteArray=function(e){for(var u,d=o(e),t=d[0],a=d[1],i=new r(function(e,u,d){return 3*(u+d)/4-d}(0,t,a)),c=0,f=a>0?t-4:t,s=0;s<f;s+=4)u=n[e.charCodeAt(s)]<<18|n[e.charCodeAt(s+1)]<<12|n[e.charCodeAt(s+2)]<<6|n[e.charCodeAt(s+3)],i[c++]=u>>16&255,i[c++]=u>>8&255,i[c++]=255&u;2===a&&(u=n[e.charCodeAt(s)]<<2|n[e.charCodeAt(s+1)]>>4,i[c++]=255&u);1===a&&(u=n[e.charCodeAt(s)]<<10|n[e.charCodeAt(s+1)]<<4|n[e.charCodeAt(s+2)]>>2,i[c++]=u>>8&255,i[c++]=255&u);return i},u.fromByteArray=function(e){for(var u,d=e.length,n=d%3,r=[],a=0,i=d-n;a<i;a+=16383)r.push(f(e,a,a+16383>i?i:a+16383));1===n?(u=e[d-1],r.push(t[u>>2]+t[u<<4&63]+"==")):2===n&&(u=(e[d-2]<<8)+e[d-1],r.push(t[u>>10]+t[u>>4&63]+t[u<<2&63]+"="));return r.join("")};for(var t=[],n=[],r="undefined"!=typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",i=0,c=a.length;i<c;++i)t[i]=a[i],n[a.charCodeAt(i)]=i;function o(e){var u=e.length;if(u%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var d=e.indexOf("=");return-1===d&&(d=u),[d,d===u?0:4-d%4]}function f(e,u,d){for(var n,r,a=[],i=u;i<d;i+=3)n=(e[i]<<16&16711680)+(e[i+1]<<8&65280)+(255&e[i+2]),a.push(t[(r=n)>>18&63]+t[r>>12&63]+t[r>>6&63]+t[63&r]);return a.join("")}n["-".charCodeAt(0)]=62,n["_".charCodeAt(0)]=63},46:function(e,u){u.read=function(e,u,d,t,n){var r,a,i=8*n-t-1,c=(1<<i)-1,o=c>>1,f=-7,s=d?n-1:0,l=d?-1:1,p=e[u+s];for(s+=l,r=p&(1<<-f)-1,p>>=-f,f+=i;f>0;r=256*r+e[u+s],s+=l,f-=8);for(a=r&(1<<-f)-1,r>>=-f,f+=t;f>0;a=256*a+e[u+s],s+=l,f-=8);if(0===r)r=1-o;else{if(r===c)return a?NaN:1/0*(p?-1:1);a+=Math.pow(2,t),r-=o}return(p?-1:1)*a*Math.pow(2,r-t)},u.write=function(e,u,d,t,n,r){var a,i,c,o=8*r-n-1,f=(1<<o)-1,s=f>>1,l=23===n?Math.pow(2,-24)-Math.pow(2,-77):0,p=t?0:r-1,m=t?1:-1,h=u<0||0===u&&1/u<0?1:0;for(u=Math.abs(u),isNaN(u)||u===1/0?(i=isNaN(u)?1:0,a=f):(a=Math.floor(Math.log(u)/Math.LN2),u*(c=Math.pow(2,-a))<1&&(a--,c*=2),(u+=a+s>=1?l/c:l*Math.pow(2,1-s))*c>=2&&(a++,c/=2),a+s>=f?(i=0,a=f):a+s>=1?(i=(u*c-1)*Math.pow(2,n),a+=s):(i=u*Math.pow(2,s-1)*Math.pow(2,n),a=0));n>=8;e[d+p]=255&i,p+=m,i/=256,n-=8);for(a=a<<n|i,o+=n;o>0;e[d+p]=255&a,p+=m,a/=256,o-=8);e[d+p-m]|=128*h}},47:function(e,u){var d={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==d.call(e)}},48:function(e,u,d){var t=function(e){"use strict";var u,d=Object.prototype,t=d.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",a=n.asyncIterator||"@@asyncIterator",i=n.toStringTag||"@@toStringTag";function c(e,u,d,t){var n=u&&u.prototype instanceof h?u:h,r=Object.create(n.prototype),a=new B(t||[]);return r._invoke=function(e,u,d){var t=f;return function(n,r){if(t===l)throw new Error("Generator is already running");if(t===p){if("throw"===n)throw r;return C()}for(d.method=n,d.arg=r;;){var a=d.delegate;if(a){var i=E(a,d);if(i){if(i===m)continue;return i}}if("next"===d.method)d.sent=d._sent=d.arg;else if("throw"===d.method){if(t===f)throw t=p,d.arg;d.dispatchException(d.arg)}else"return"===d.method&&d.abrupt("return",d.arg);t=l;var c=o(e,u,d);if("normal"===c.type){if(t=d.done?p:s,c.arg===m)continue;return{value:c.arg,done:d.done}}"throw"===c.type&&(t=p,d.method="throw",d.arg=c.arg)}}}(e,d,a),r}function o(e,u,d){try{return{type:"normal",arg:e.call(u,d)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var f="suspendedStart",s="suspendedYield",l="executing",p="completed",m={};function h(){}function b(){}function g(){}var y={};y[r]=function(){return this};var v=Object.getPrototypeOf,x=v&&v(v(S([])));x&&x!==d&&t.call(x,r)&&(y=x);var w=g.prototype=h.prototype=Object.create(y);function A(e){["next","throw","return"].forEach(function(u){e[u]=function(e){return this._invoke(u,e)}})}function _(e){var u;this._invoke=function(d,n){function r(){return new Promise(function(u,r){!function u(d,n,r,a){var i=o(e[d],e,n);if("throw"!==i.type){var c=i.arg,f=c.value;return f&&"object"==typeof f&&t.call(f,"__await")?Promise.resolve(f.__await).then(function(e){u("next",e,r,a)},function(e){u("throw",e,r,a)}):Promise.resolve(f).then(function(e){c.value=e,r(c)},function(e){return u("throw",e,r,a)})}a(i.arg)}(d,n,u,r)})}return u=u?u.then(r,r):r()}}function E(e,d){var t=e.iterator[d.method];if(t===u){if(d.delegate=null,"throw"===d.method){if(e.iterator.return&&(d.method="return",d.arg=u,E(e,d),"throw"===d.method))return m;d.method="throw",d.arg=new TypeError("The iterator does not provide a 'throw' method")}return m}var n=o(t,e.iterator,d.arg);if("throw"===n.type)return d.method="throw",d.arg=n.arg,d.delegate=null,m;var r=n.arg;return r?r.done?(d[e.resultName]=r.value,d.next=e.nextLoc,"return"!==d.method&&(d.method="next",d.arg=u),d.delegate=null,m):r:(d.method="throw",d.arg=new TypeError("iterator result is not an object"),d.delegate=null,m)}function F(e){var u={tryLoc:e[0]};1 in e&&(u.catchLoc=e[1]),2 in e&&(u.finallyLoc=e[2],u.afterLoc=e[3]),this.tryEntries.push(u)}function I(e){var u=e.completion||{};u.type="normal",delete u.arg,e.completion=u}function B(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(F,this),this.reset(!0)}function S(e){if(e){var d=e[r];if(d)return d.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,a=function d(){for(;++n<e.length;)if(t.call(e,n))return d.value=e[n],d.done=!1,d;return d.value=u,d.done=!0,d};return a.next=a}}return{next:C}}function C(){return{value:u,done:!0}}return b.prototype=w.constructor=g,g.constructor=b,g[i]=b.displayName="GeneratorFunction",e.isGeneratorFunction=function(e){var u="function"==typeof e&&e.constructor;return!!u&&(u===b||"GeneratorFunction"===(u.displayName||u.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,i in e||(e[i]="GeneratorFunction")),e.prototype=Object.create(w),e},e.awrap=function(e){return{__await:e}},A(_.prototype),_.prototype[a]=function(){return this},e.AsyncIterator=_,e.async=function(u,d,t,n){var r=new _(c(u,d,t,n));return e.isGeneratorFunction(d)?r:r.next().then(function(e){return e.done?e.value:r.next()})},A(w),w[i]="Generator",w[r]=function(){return this},w.toString=function(){return"[object Generator]"},e.keys=function(e){var u=[];for(var d in e)u.push(d);return u.reverse(),function d(){for(;u.length;){var t=u.pop();if(t in e)return d.value=t,d.done=!1,d}return d.done=!0,d}},e.values=S,B.prototype={constructor:B,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=u,this.done=!1,this.delegate=null,this.method="next",this.arg=u,this.tryEntries.forEach(I),!e)for(var d in this)"t"===d.charAt(0)&&t.call(this,d)&&!isNaN(+d.slice(1))&&(this[d]=u)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var d=this;function n(t,n){return i.type="throw",i.arg=e,d.next=t,n&&(d.method="next",d.arg=u),!!n}for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r],i=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var c=t.call(a,"catchLoc"),o=t.call(a,"finallyLoc");if(c&&o){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!o)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,u){for(var d=this.tryEntries.length-1;d>=0;--d){var n=this.tryEntries[d];if(n.tryLoc<=this.prev&&t.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var r=n;break}}r&&("break"===e||"continue"===e)&&r.tryLoc<=u&&u<=r.finallyLoc&&(r=null);var a=r?r.completion:{};return a.type=e,a.arg=u,r?(this.method="next",this.next=r.finallyLoc,m):this.complete(a)},complete:function(e,u){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&u&&(this.next=u),m},finish:function(e){for(var u=this.tryEntries.length-1;u>=0;--u){var d=this.tryEntries[u];if(d.finallyLoc===e)return this.complete(d.completion,d.afterLoc),I(d),m}},catch:function(e){for(var u=this.tryEntries.length-1;u>=0;--u){var d=this.tryEntries[u];if(d.tryLoc===e){var t=d.completion;if("throw"===t.type){var n=t.arg;I(d)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(e,d,t){return this.delegate={iterator:S(e),resultName:d,nextLoc:t},"next"===this.method&&(this.arg=u),m}},e}(e.exports);try{regeneratorRuntime=t}catch(e){Function("r","regeneratorRuntime = r")(t)}},49:function(e,u){e.exports=function(e){if(Array.isArray(e))return e}},5:function(e,u,d){var t=d(44);e.exports=function(e,u){if("function"!=typeof u&&null!==u)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(u&&u.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),u&&t(e,u)}},50:function(e,u){e.exports=function(e,u){var d=[],t=!0,n=!1,r=void 0;try{for(var a,i=e[Symbol.iterator]();!(t=(a=i.next()).done)&&(d.push(a.value),!u||d.length!==u);t=!0);}catch(e){n=!0,r=e}finally{try{t||null==i.return||i.return()}finally{if(n)throw r}}return d}},51:function(e,u){e.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}},6:function(e,u,d){"use strict";var t=d(1),n=d.n(t),r=d(2),a=d.n(r),i=d(15),c=d(0),o=d(17),f=function(){function e(u){n()(this,e),this.name="",this.type="",this._value=null,this.disabled=!1,this.hint="",this.rows=0,this.toggleValues=[],this.target=null,this.defaultIndex=0,this.min=null,this.max=null,this.step=1,u&&this._parseConfig(u)}return a()(e,[{key:"_parseConfig",value:function(e){this.name=e.name,this.type=e.type,this.defaultValue=e.value,this.disabled=!!e.disabled,this.hint=e.hint||!1,this.rows=e.rows||!1,this.toggleValues=e.toggleValues,this.target=void 0!==e.target?e.target:null,this.defaultIndex=void 0!==e.defaultIndex?e.defaultIndex:0,this.min=e.min,this.max=e.max,this.step=e.step}},{key:"config",get:function(){return this._value}},{key:"value",set:function(u){this._value=e.prepare(u,this.type)},get:function(){return this._value}}],[{key:"prepare",value:function(e,u){var d;switch(u){case"binaryString":case"binaryShortString":case"editableOption":case"editableOptionShort":return c.b.parseEscapedChars(e);case"byteArray":return"string"==typeof e?(e=e.replace(/\s+/g,""),Object(o.a)(e)):e;case"number":if(d=parseFloat(e),isNaN(d))throw"Invalid ingredient value. Not a number: "+c.b.truncate(e.toString(),10);return d;default:return e}}}]),e}(),s=function(){function e(){n()(this,e),this._inputType=-1,this._outputType=-1,this._presentType=-1,this._breakpoint=!1,this._disabled=!1,this._flowControl=!1,this._manualBake=!1,this._ingList=[],this.name="",this.module="",this.description="",this.infoURL=null}return a()(e,[{key:"run",value:function(e,u){return e}},{key:"highlight",value:function(e,u){return!1}},{key:"highlightReverse",value:function(e,u){return!1}},{key:"present",value:function(e,u){return e}},{key:"addIngredient",value:function(e){this._ingList.push(e)}},{key:"inputType",set:function(e){this._inputType=i.a.typeEnum(e)},get:function(){return i.a.enumLookup(this._inputType)}},{key:"outputType",set:function(e){this._outputType=i.a.typeEnum(e),this._presentType<0&&(this._presentType=this._outputType)},get:function(){return i.a.enumLookup(this._outputType)}},{key:"presentType",set:function(e){this._presentType=i.a.typeEnum(e)},get:function(){return i.a.enumLookup(this._presentType)}},{key:"args",set:function(e){var u=this;e.forEach(function(e){var d=new f(e);u.addIngredient(d)})},get:function(){return this._ingList.map(function(e){var u={name:e.name,type:e.type,value:e.defaultValue};return e.toggleValues&&(u.toggleValues=e.toggleValues),e.hint&&(u.hint=e.hint),e.rows&&(u.rows=e.rows),e.disabled&&(u.disabled=e.disabled),e.target&&(u.target=e.target),e.defaultIndex&&(u.defaultIndex=e.defaultIndex),"number"==typeof e.min&&(u.min=e.min),"number"==typeof e.max&&(u.max=e.max),e.step&&(u.step=e.step),u})}},{key:"config",get:function(){return{op:this.name,args:this._ingList.map(function(e){return e.config})}}},{key:"ingValues",set:function(e){var u=this;e.forEach(function(e,d){u._ingList[d].value=e})},get:function(){return this._ingList.map(function(e){return e.value})}},{key:"breakpoint",set:function(e){this._breakpoint=!!e},get:function(){return this._breakpoint}},{key:"disabled",set:function(e){this._disabled=!!e},get:function(){return this._disabled}},{key:"flowControl",get:function(){return this._flowControl},set:function(e){this._flowControl=!!e}},{key:"manualBake",get:function(){return this._manualBake},set:function(e){this._manualBake=!!e}}]),e}();u.a=s},666:function(e,u,d){var t=d(418),n=d(198).document,r=t(n)&&t(n.createElement);e.exports=function(e){return r?n.createElement(e):{}}},667:function(e,u){e.exports=function(e,u){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:u}}},668:function(e,u,d){var t=d(339),n=d(1263),r=d(672),a=d(423)("IE_PROTO"),i=function(){},c=function(){var e,u=d(666)("iframe"),t=r.length;for(u.style.display="none",d(1270).appendChild(u),u.src="javascript:",(e=u.contentWindow.document).open(),e.write("<script>document.F=Object<\/script>"),e.close(),c=e.F;t--;)delete c.prototype[r[t]];return c()};e.exports=Object.create||function(e,u){var d;return null!==e?(i.prototype=t(e),d=new i,i.prototype=null,d[a]=e):d=c(),void 0===u?d:n(d,u)}},669:function(e,u,d){var t=d(199),n=d(198),r=n["__core-js_shared__"]||(n["__core-js_shared__"]={});(e.exports=function(e,u){return r[e]||(r[e]=void 0!==u?u:{})})("versions",[]).push({version:t.version,mode:d(670)?"pure":"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})},670:function(e,u){e.exports=!0},671:function(e,u){var d=0,t=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++d+t).toString(36))}},672:function(e,u){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},673:function(e,u,d){"use strict";var t=d(670),n=d(311),r=d(1283),a=d(312),i=d(343),c=d(1284),o=d(674),f=d(1285),s=d(217)("iterator"),l=!([].keys&&"next"in[].keys()),p=function(){return this};e.exports=function(e,u,d,m,h,b,g){c(d,u,m);var y,v,x,w=function(e){if(!l&&e in F)return F[e];switch(e){case"keys":case"values":return function(){return new d(this,e)}}return function(){return new d(this,e)}},A=u+" Iterator",_="values"==h,E=!1,F=e.prototype,I=F[s]||F["@@iterator"]||h&&F[h],B=I||w(h),S=h?_?w("entries"):B:void 0,C="Array"==u&&F.entries||I;if(C&&(x=f(C.call(new e)))!==Object.prototype&&x.next&&(o(x,A,!0),t||"function"==typeof x[s]||a(x,s,p)),_&&I&&"values"!==I.name&&(E=!0,B=function(){return I.call(this)}),t&&!g||!l&&!E&&F[s]||a(F,s,B),i[u]=B,i[A]=p,h)if(y={values:_?B:w("values"),keys:b?B:w("keys"),entries:S},g)for(v in y)v in F||r(F,v,y[v]);else n(n.P+n.F*(l||E),u,y);return y}},674:function(e,u,d){var t=d(417).f,n=d(341),r=d(217)("toStringTag");e.exports=function(e,u,d){e&&!n(e=d?e:e.prototype,r)&&t(e,r,{configurable:!0,value:u})}},675:function(e,u){e.exports="\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff"},7:function(e,u,d){e.exports=d(48)},9:function(e,u,d){"use strict";var t=d(1),n=d.n(t),r=d(4),a=d.n(r),i=d(3),c=d.n(i),o=d(25),f=d.n(o),s=d(5),l=d.n(s);
/**
 * Custom error type for handling operation input errors.
 * i.e. where the operation can handle the error and print a message to the screen.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 */
var p=function(e){function u(){var e;n()(this,u);for(var d=arguments.length,t=new Array(d),r=0;r<d;r++)t[r]=arguments[r];return(e=a()(this,c()(u).call(this,...t))).type="OperationError",Error.captureStackTrace&&Error.captureStackTrace(f()(e),u),e}return l()(u,e),u}(function(e){function u(){var u=Reflect.construct(e,Array.from(arguments));return Object.setPrototypeOf(u,Object.getPrototypeOf(this)),u}return u.prototype=Object.create(e.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),Object.setPrototypeOf?Object.setPrototypeOf(u,e):u.__proto__=e,u}(Error));u.a=p},94:function(e,u,d){"use strict";d.d(u,"a",function(){return t}),d.d(u,"c",function(){return n}),d.d(u,"b",function(){return r}),d.d(u,"d",function(){return a});
/**
 * Various delimiters
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 */
var t=["Space","Comma","Semi-colon","Colon","Line feed","CRLF"],n=["Line feed","CRLF","Space","Comma","Semi-colon","Colon","Nothing (separate chars)"],r=["Line feed","CRLF","Space","Comma"],a=[{name:"Comma",value:","},{name:"Space",value:" "},{name:"CRLF",value:"\\r\\n"},{name:"Line Feed",value:"\n"}]}});