/**
 * CyberChef - The Cyber Swiss Army Knife
 *
 * @copyright Crown Copyright 2016
 * @license Apache-2.0
 *
 *   Copyright 2016 Crown Copyright
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */!function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)r.d(n,i,function(t){return e[t]}.bind(null,i));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=1345)}({0:function(e,t,r){"use strict";(function(e,n){r.d(t,"c",function(){return w}),r.d(t,"d",function(){return A}),r.d(t,"e",function(){return F}),r.d(t,"a",function(){return x});var i=r(7),o=r.n(i),a=r(11),u=r.n(a),s=r(1),c=r.n(s),l=r(2),f=r.n(l),h=r(24),p=r.n(h),d=r(14),m=r(17),g=r(30),v=r(29),y=function(){function t(){c()(this,t)}var r;return f()(t,null,[{key:"chr",value:function(e){if(e>65535){e-=65536;var t=String.fromCharCode(e>>>10&1023|55296);return e=56320|1023&e,t+String.fromCharCode(e)}return String.fromCharCode(e)}},{key:"ord",value:function(e){if(2===e.length){var t=e.charCodeAt(0),r=e.charCodeAt(1);if(t>=55296&&t<56320&&r>=56320&&r<57344)return 1024*(t-55296)+r-56320+65536}return e.charCodeAt(0)}},{key:"padBytesRight",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=new Array(t);return n.fill(r),[...e].forEach(function(e,t){n[t]=e}),n}},{key:"truncate",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"...";return e.length>t&&(e=e.slice(0,t-r.length)+r),e}},{key:"hex",value:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return(e="string"==typeof e?t.ord(e):e).toString(16).padStart(r,"0")}},{key:"bin",value:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:8;return(e="string"==typeof e?t.ord(e):e).toString(2).padStart(r,"0")}},{key:"printable",value:function(e){var r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];b()&&window.app&&!window.app.options.treatAsUtf8&&(e=t.byteArrayToChars(t.strToByteArray(e)));var n=/[\0-\x08\x0B-\x0C\x0E-\x1F\x7F-\x9F\xAD\u0378\u0379\u037F-\u0383\u038B\u038D\u03A2\u0528-\u0530\u0557\u0558\u0560\u0588\u058B-\u058E\u0590\u05C8-\u05CF\u05EB-\u05EF\u05F5-\u0605\u061C\u061D\u06DD\u070E\u070F\u074B\u074C\u07B2-\u07BF\u07FB-\u07FF\u082E\u082F\u083F\u085C\u085D\u085F-\u089F\u08A1\u08AD-\u08E3\u08FF\u0978\u0980\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09FC-\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF2-\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B55\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B78-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BFB-\u0C00\u0C04\u0C0D\u0C11\u0C29\u0C34\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5A-\u0C5F\u0C64\u0C65\u0C70-\u0C77\u0C80\u0C81\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0D01\u0D04\u0D0D\u0D11\u0D3B\u0D3C\u0D45\u0D49\u0D4F-\u0D56\u0D58-\u0D5F\u0D64\u0D65\u0D76-\u0D78\u0D80\u0D81\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DF1\u0DF5-\u0E00\u0E3B-\u0E3E\u0E5C-\u0E80\u0E83\u0E85\u0E86\u0E89\u0E8B\u0E8C\u0E8E-\u0E93\u0E98\u0EA0\u0EA4\u0EA6\u0EA8\u0EA9\u0EAC\u0EBA\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F48\u0F6D-\u0F70\u0F98\u0FBD\u0FCD\u0FDB-\u0FFF\u10C6\u10C8-\u10CC\u10CE\u10CF\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u137D-\u137F\u139A-\u139F\u13F5-\u13FF\u169D-\u169F\u16F1-\u16FF\u170D\u1715-\u171F\u1737-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17DE\u17DF\u17EA-\u17EF\u17FA-\u17FF\u180F\u181A-\u181F\u1878-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191D-\u191F\u192C-\u192F\u193C-\u193F\u1941-\u1943\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DB-\u19DD\u1A1C\u1A1D\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1A9F\u1AAE-\u1AFF\u1B4C-\u1B4F\u1B7D-\u1B7F\u1BF4-\u1BFB\u1C38-\u1C3A\u1C4A-\u1C4C\u1C80-\u1CBF\u1CC8-\u1CCF\u1CF7-\u1CFF\u1DE7-\u1DFB\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FC5\u1FD4\u1FD5\u1FDC\u1FF0\u1FF1\u1FF5\u1FFF\u200B-\u200F\u202A-\u202E\u2060-\u206F\u2072\u2073\u208F\u209D-\u209F\u20BB-\u20CF\u20F1-\u20FF\u218A-\u218F\u23F4-\u23FF\u2427-\u243F\u244B-\u245F\u2700\u2B4D-\u2B4F\u2B5A-\u2BFF\u2C2F\u2C5F\u2CF4-\u2CF8\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D71-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E3C-\u2E7F\u2E9A\u2EF4-\u2EFF\u2FD6-\u2FEF\u2FFC-\u2FFF\u3040\u3097\u3098\u3100-\u3104\u312E-\u3130\u318F\u31BB-\u31BF\u31E4-\u31EF\u321F\u32FF\u4DB6-\u4DBF\u9FCD-\u9FFF\uA48D-\uA48F\uA4C7-\uA4CF\uA62C-\uA63F\uA698-\uA69E\uA6F8-\uA6FF\uA78F\uA794-\uA79F\uA7AB-\uA7F7\uA82C-\uA82F\uA83A-\uA83F\uA878-\uA87F\uA8C5-\uA8CD\uA8DA-\uA8DF\uA8FC-\uA8FF\uA954-\uA95E\uA97D-\uA97F\uA9CE\uA9DA-\uA9DD\uA9E0-\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A\uAA5B\uAA7C-\uAA7F\uAAC3-\uAADA\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F-\uABBF\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBC2-\uFBD2\uFD40-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFE\uFDFF\uFE1A-\uFE1F\uFE27-\uFE2F\uFE53\uFE67\uFE6C-\uFE6F\uFE75\uFEFD-\uFF00\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFDF\uFFE7\uFFEF-\uFFFB\uFFFE\uFFFF]/g,i=/[\x09-\x10\x0D\u2028\u2029]/g;return e=e.replace(n,"."),r||(e=e.replace(i,".")),e}},{key:"parseEscapedChars",value:function(e){return e.replace(/(\\)?\\([bfnrtv'"]|[0-3][0-7]{2}|[0-7]{1,2}|x[\da-fA-F]{2}|u[\da-fA-F]{4}|u\{[\da-fA-F]{1,6}\}|\\)/g,function(e,t,r){if("\\"===t)return"\\"+r;switch(r[0]){case"\\":return"\\";case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":return String.fromCharCode(parseInt(r,8));case"b":return"\b";case"t":return"\t";case"n":return"\n";case"v":return"\v";case"f":return"\f";case"r":return"\r";case'"':return'"';case"'":return"'";case"x":return String.fromCharCode(parseInt(r.substr(1),16));case"u":return"{"===r[1]?String.fromCodePoint(parseInt(r.slice(2,-1),16)):String.fromCharCode(parseInt(r.substr(1),16))}})}},{key:"escapeRegex",value:function(e){return e.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}},{key:"expandAlphRange",value:function(e){for(var r=[],n=0;n<e.length;n++)if(n<e.length-2&&"-"===e[n+1]&&"\\"!==e[n]){for(var i=t.ord(e[n]),o=t.ord(e[n+2]),a=i;a<=o;a++)r.push(t.chr(a));n+=2}else n<e.length-2&&"\\"===e[n]&&"-"===e[n+1]?(r.push("-"),n++):r.push(e[n]);return r}},{key:"convertToByteArray",value:function(e,r){switch(r.toLowerCase()){case"binary":return Object(v.a)(e);case"hex":return Object(m.a)(e);case"decimal":return Object(g.a)(e);case"base64":return Object(d.a)(e,null,"byteArray");case"utf8":return t.strToUtf8ByteArray(e);case"latin1":default:return t.strToByteArray(e)}}},{key:"convertToByteString",value:function(e,r){switch(r.toLowerCase()){case"binary":return t.byteArrayToChars(Object(v.a)(e));case"hex":return t.byteArrayToChars(Object(m.a)(e));case"decimal":return t.byteArrayToChars(Object(g.a)(e));case"base64":return t.byteArrayToChars(Object(d.a)(e,null,"byteArray"));case"utf8":return p.a.encode(e);case"latin1":default:return e}}},{key:"strToArrayBuffer",value:function(e){for(var r,n=new Uint8Array(e.length),i=e.length;i--;)if(r=e.charCodeAt(i),n[i]=r,r>255)return t.strToUtf8ArrayBuffer(e);return n.buffer}},{key:"strToUtf8ArrayBuffer",value:function(e){var r=p.a.encode(e);return e.length!==r.length&&(A()?self.setOption("attemptHighlight",!1):b()&&(window.app.options.attemptHighlight=!1)),t.strToArrayBuffer(r)}},{key:"strToByteArray",value:function(e){for(var r,n=new Array(e.length),i=e.length;i--;)if(r=e.charCodeAt(i),n[i]=r,r>255)return t.strToUtf8ByteArray(e);return n}},{key:"strToUtf8ByteArray",value:function(e){var r=p.a.encode(e);return e.length!==r.length&&(A()?self.setOption("attemptHighlight",!1):b()&&(window.app.options.attemptHighlight=!1)),t.strToByteArray(r)}},{key:"strToCharcode",value:function(e){for(var r=[],n=0;n<e.length;n++){var i=e.charCodeAt(n);if(n<e.length-1&&i>=55296&&i<56320){var o=e[n+1].charCodeAt(0);o>=56320&&o<57344&&(i=t.ord(e[n]+e[++n]))}r.push(i)}return r}},{key:"byteArrayToUtf8",value:function(e){var r=t.byteArrayToChars(e);try{var n=p.a.decode(r);return r.length!==n.length&&(A()?self.setOption("attemptHighlight",!1):b()&&(window.app.options.attemptHighlight=!1)),n}catch(e){return r}}},{key:"byteArrayToChars",value:function(e){if(!e)return"";for(var t="",r=0;r<e.length;)t+=String.fromCharCode(e[r++]);return t}},{key:"arrayBufferToStr",value:function(e){var r=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=new Uint8Array(e);return r?t.byteArrayToUtf8(n):t.byteArrayToChars(n)}},{key:"parseCSV",value:function(e){var t,r,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[","],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:["\n","\r"],o=!1,a=!1,u="",s=[],c=[];e.length&&"\ufeff"===e[0]&&(e=e.substr(1));for(var l=0;l<e.length;l++)t=e[l],r=e[l+1]||"",o?(u+=t,o=!1):'"'!==t||a?'"'===t&&a?'"'===r?o=!0:a=!1:!a&&n.indexOf(t)>=0?(s.push(u),u=""):!a&&i.indexOf(t)>=0?(s.push(u),u="",c.push(s),s=[],i.indexOf(r)>=0&&r!==t&&l++):u+=t:a=!0;return s.length&&(s.push(u),c.push(s)),c}},{key:"stripHtmlTags",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return t&&(e=e.replace(/<(script|style)[^>]*>.*<\/(script|style)>/gim,"")),e.replace(/<[^>]+>/g,"")}},{key:"escapeHtml",value:function(e){var t={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","/":"&#x2F;","`":"&#x60;"};return e.replace(/[&<>"'\/`]/g,function(e){return t[e]})}},{key:"unescapeHtml",value:function(e){var t={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#x27;":"'","&#x2F;":"/","&#x60;":"`"};return e.replace(/&#?x?[a-z0-9]{2,4};/gi,function(e){return t[e]||e})}},{key:"encodeURIFragment",value:function(e){var t={"%2D":"-","%2E":".","%5F":"_","%7E":"~","%21":"!","%24":"$","%27":"'","%28":"(","%29":")","%2A":"*","%2C":",","%3B":";","%3A":":","%40":"@","%2F":"/","%3F":"?"};return(e=encodeURIComponent(e)).replace(/%[0-9A-F]{2}/g,function(e){return t[e]||e})}},{key:"generatePrettyRecipe",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r="",n="",i="",o="",a="";return e.forEach(function(e){n=e.op.replace(/ /g,"_"),i=JSON.stringify(e.args).slice(1,-1).replace(/'/g,"\\'").replace(/"((?:[^"\\]|\\.)*)"/g,"'$1'").replace(/\\"/g,'"'),o=e.disabled?"/disabled":"",a=e.breakpoint?"/breakpoint":"",r+=`${n}(${i}${o}${a})`,t&&(r+="\n")}),r}},{key:"parseRecipeConfig",value:function(e){if(0===(e=e.trim()).length)return[];if("["===e[0])return JSON.parse(e);var t,r;e=e.replace(/\n/g,"");for(var n=/([^(]+)\(((?:'[^'\\]*(?:\\.[^'\\]*)*'|[^)\/'])*)(\/[^)]+)?\)/g,i=[];t=n.exec(e);){r="["+(r=t[2].replace(/"/g,'\\"').replace(/(^|,|{|:)'/g,'$1"').replace(/([^\\]|(?:\\\\)+)'(,|:|}|$)/g,'$1"$2').replace(/\\'/g,"'"))+"]";var o={op:t[1].replace(/_/g," "),args:JSON.parse(r)};t[3]&&t[3].indexOf("disabled")>0&&(o.disabled=!0),t[3]&&t[3].indexOf("breakpoint")>0&&(o.breakpoint=!0),i.push(o)}return i}},{key:"displayFilesAsHTML",value:(r=u()(o.a.mark(function e(r){var n,i,a,s,c;return o.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:n=function(e){return`<div class='card' style='white-space: normal;'>\n                    <div class='card-header'>\n                        <h6 class="mb-0">\n                            ${t.escapeHtml(e.name)}\n                        </h6>\n                    </div>\n                </div>`},i=function(e,r){if(r.startsWith("image")){var n="data:";return n+=r+";","<img style='max-width: 100%;' src='"+(n+="base64,"+Object(d.b)(e))+"'>"}return`<pre>${t.escapeHtml(t.arrayBufferToStr(e.buffer))}</pre>`},a=function(){var e=u()(o.a.mark(function e(r,n){var a,u,s,c;return o.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.readFile(r);case 2:return a=e.sent,u=new Blob([a],{type:r.type||"octet/stream"}),s=URL.createObjectURL(u),c=`<div class='card' style='white-space: normal;'>\n                    <div class='card-header' id='heading${n}'>\n                        <h6 class='mb-0'>\n                            <a class='collapsed'\n                                data-toggle='collapse'\n                                href='#collapse${n}'\n                                aria-expanded='false'\n                                aria-controls='collapse${n}'\n                                title="Show/hide contents of '${t.escapeHtml(r.name)}'">\n                                ${t.escapeHtml(r.name)}</a>\n                            <span class='float-right' style="margin-top: -3px">\n                                ${r.size.toLocaleString()} bytes\n                                <a title="Download ${t.escapeHtml(r.name)}"\n                                    href="${s}"\n                                    download="${t.escapeHtml(r.name)}"\n                                    data-toggle="tooltip">\n                                    <i class="material-icons" style="vertical-align: bottom">save</i>\n                                </a>\n                                <a title="Move to input"\n                                    href="#"\n                                    blob-url="${s}"\n                                    file-name="${t.escapeHtml(r.name)}"\n                                    class="extract-file"\n                                    data-toggle="tooltip">\n                                    <i class="material-icons" style="vertical-align: bottom">open_in_browser</i>\n                                </a>\n                            </span>\n                        </h6>\n                    </div>\n                    <div id='collapse${n}' class='collapse' aria-labelledby='heading${n}' data-parent="#files">\n                        <div class='card-body'>\n                            ${i(a,r.type)}\n                        </div>\n                    </div>\n                </div>`,e.abrupt("return",c);case 7:case"end":return e.stop()}},e)}));return function(t,r){return e.apply(this,arguments)}}(),s=`<div style='padding: 5px; white-space: normal;'>\n                ${r.length} file(s) found\n            </div><div id="files" style="padding: 20px">`,c=0;case 5:if(!(c<r.length)){e.next=17;break}if(!r[c].name.endsWith("/")){e.next=10;break}s+=n(r[c]),e.next=14;break;case 10:return e.t0=s,e.next=13,a(r[c],c);case 13:s=e.t0+=e.sent;case 14:c++,e.next=5;break;case 17:return e.abrupt("return",s+="</div>");case 18:case"end":return e.stop()}},e)})),function(e){return r.apply(this,arguments)})},{key:"parseURIParams",value:function(e){if(""===e)return{};"?"!==e[0]&&"#"!==e[0]||(e=e.substr(1));for(var t=e.split("&"),r={},n=0;n<t.length;n++){var i=t[n].split("=");2!==i.length?r[t[n]]=!0:r[i[0]]=decodeURIComponent(i[1].replace(/\+/g," "))}return r}},{key:"readFile",value:function(t){return w()?e.from(t).buffer:new Promise(function(e,r){var n=new FileReader,i=new Uint8Array(t.size),o=0,a=function(){if(o>=t.size)e(i);else{var r=t.slice(o,o+10485760);n.readAsArrayBuffer(r)}};n.onload=function(e){i.set(new Uint8Array(n.result),o),o+=10485760,a()},n.onerror=function(e){r(n.error.message)},a()})}},{key:"readFileSync",value:function(e){if(!w())throw new TypeError("Browser environment cannot support readFileSync");return Uint8Array.from(e.data).buffer}},{key:"mod",value:function(e,t){return(e%t+t)%t}},{key:"gcd",value:function(e,r){return r?t.gcd(r,e%r):e}},{key:"modInv",value:function(e,t){e%=t;for(var r=1;r<t;r++)if(e*r%26==1)return r}},{key:"charRep",value:function(e){return{Space:" ",Percent:"%",Comma:",","Semi-colon":";",Colon:":",Tab:"\t","Line feed":"\n",CRLF:"\r\n","Forward slash":"/",Backslash:"\\","0x":"0x","\\x":"\\x","Nothing (separate chars)":"",None:""}[e]}},{key:"regexRep",value:function(e){return{Space:/\s+/g,Percent:/%/g,Comma:/,/g,"Semi-colon":/;/g,Colon:/:/g,"Line feed":/\n/g,CRLF:/\r\n/g,"Forward slash":/\//g,Backslash:/\\/g,"0x":/0x/g,"\\x":/\\x/g,None:/\s+/g}[e]}}]),t}();function w(){return void 0!==n&&null!=n.versions&&null!=n.versions.node}function b(){return"object"==typeof window}function A(){return"function"==typeof importScripts}function F(e){A()?self.sendStatusMessage(e):b()?app.alert(e,1e4):w()&&console.debug(e)}t.b=y,Array.prototype.unique=function(){for(var e={},t=[],r=0,n=this.length;r<n;r++)Object.prototype.hasOwnProperty.call(e,this[r])||(t.push(this[r]),e[this[r]]=1);return t},Array.prototype.max=function(){return Math.max.apply(null,this)},Array.prototype.min=function(){return Math.min.apply(null,this)},Array.prototype.sum=function(){return this.reduce(function(e,t){return e+t},0)},Array.prototype.equals=function(e){if(!e)return!1;var t=this.length;if(t!==e.length)return!1;for(;t--;)if(this[t]!==e[t])return!1;return!0},String.prototype.count=function(e){return this.split(e).length-1};var B={};function x(e,t,r,n,i){return function(){clearTimeout(B[r]),B[r]=setTimeout(function(){e.apply(n,i)},t)}}String.prototype.padStart||(String.prototype.padStart=function(e,t){return e>>=0,t=String(void 0!==t?t:" "),this.length>e?String(this):((e-=this.length)>t.length&&(t+=t.repeat(e/t.length)),t.slice(0,e)+String(this))}),String.prototype.padEnd||(String.prototype.padEnd=function(e,t){return e>>=0,t=String(void 0!==t?t:" "),this.length>e?String(this):((e-=this.length)>t.length&&(t+=t.repeat(e/t.length)),String(this)+t.slice(0,e))})}).call(this,r(12).Buffer,r(27))},1:function(e,t){e.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}},10:function(e,t,r){"use strict";r.d(t,"a",function(){return u});var n=r(1),i=r.n(n),o=r(2),a=r.n(o),u=function(){function e(t){i()(this,e),this.bytes=t,this.length=this.bytes.length,this.position=0,this.bitPos=0}return a()(e,[{key:"getBytes",value:function(e){if(!(this.position>this.length)){var t=this.position+e,r=this.bytes.slice(this.position,t);return this.position=t,this.bitPos=0,r}}},{key:"readString",value:function(e){if(!(this.position>this.length)){for(var t="",r=this.position;r<this.position+e;r++){var n=this.bytes[r];if(0===n)break;t+=String.fromCharCode(n)}return this.position+=e,this.bitPos=0,t}}},{key:"readInt",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"be";if(!(this.position>this.length)){var r=0;if("be"===t)for(var n=this.position;n<this.position+e;n++)r<<=8,r|=this.bytes[n];else for(var i=this.position+e-1;i>=this.position;i--)r<<=8,r|=this.bytes[i];return this.position+=e,this.bitPos=0,r}}},{key:"readBits",value:function(e){if(!(this.position>this.length)){var t=0,r=0;for(t=(this.bytes[this.position++]&256-(1<<this.bitPos))>>>this.bitPos,r=8-this.bitPos,this.bitPos=0;r<e;)t|=this.bytes[this.position++]<<r,r+=8;if(r>e){var n=r-e;t&=(1<<e)-1,r-=n,this.position--,this.bitPos=8-n}return t}}},{key:"continueUntil",value:function(e){if(!(this.position>this.length))if(this.bitPos=0,"number"!=typeof e)for(var t=!1;!t&&this.position<this.length;){for(;++this.position<this.length&&this.bytes[this.position]!==e[0];);t=!0;for(var r=1;r<e.length;r++)(this.position+r>this.length||this.bytes[this.position+r]!==e[r])&&(t=!1)}else for(;++this.position<this.length&&this.bytes[this.position]!==e;);}},{key:"consumeIf",value:function(e){this.bytes[this.position]===e&&(this.position++,this.bitPos=0)}},{key:"moveForwardsBy",value:function(e){var t=this.position+e;if(t<0||t>this.length)throw new Error("Cannot move to position "+t+" in stream. Out of bounds.");this.position=t,this.bitPos=0}},{key:"moveBackwardsBy",value:function(e){var t=this.position-e;if(t<0||t>this.length)throw new Error("Cannot move to position "+t+" in stream. Out of bounds.");this.position=t,this.bitPos=0}},{key:"moveBackwardsByBits",value:function(e){if(e<=this.bitPos)this.bitPos-=e;else for(this.bitPos>0&&(e-=this.bitPos,this.bitPos=0);e>0;)this.moveBackwardsBy(1),this.bitPos=8,this.moveBackwardsByBits(e),e-=8}},{key:"moveTo",value:function(e){if(e<0||e>this.length)throw new Error("Cannot move to position "+e+" in stream. Out of bounds.");this.position=e,this.bitPos=0}},{key:"hasMore",value:function(){return this.position<this.length}},{key:"carve",value:function(){return this.bitPos>0&&this.position++,this.bytes.slice(0,this.position)}}]),e}()},11:function(e,t){function r(e,t,r,n,i,o,a){try{var u=e[o](a),s=u.value}catch(e){return void r(e)}u.done?t(s):Promise.resolve(s).then(n,i)}e.exports=function(e){return function(){var t=this,n=arguments;return new Promise(function(i,o){var a=e.apply(t,n);function u(e){r(a,i,o,u,s,"next",e)}function s(e){r(a,i,o,u,s,"throw",e)}u(void 0)})}}},12:function(e,t,r){"use strict";(function(e){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <<EMAIL>> <http://feross.org>
 * @license  MIT
 */
var n=r(45),i=r(46),o=r(47);function a(){return s.TYPED_ARRAY_SUPPORT?**********:**********}function u(e,t){if(a()<t)throw new RangeError("Invalid typed array length");return s.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=s.prototype:(null===e&&(e=new s(t)),e.length=t),e}function s(e,t,r){if(!(s.TYPED_ARRAY_SUPPORT||this instanceof s))return new s(e,t,r);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return f(this,e)}return c(this,e,t,r)}function c(e,t,r,n){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?function(e,t,r,n){if(t.byteLength,r<0||t.byteLength<r)throw new RangeError("'offset' is out of bounds");if(t.byteLength<r+(n||0))throw new RangeError("'length' is out of bounds");t=void 0===r&&void 0===n?new Uint8Array(t):void 0===n?new Uint8Array(t,r):new Uint8Array(t,r,n);s.TYPED_ARRAY_SUPPORT?(e=t).__proto__=s.prototype:e=h(e,t);return e}(e,t,r,n):"string"==typeof t?function(e,t,r){"string"==typeof r&&""!==r||(r="utf8");if(!s.isEncoding(r))throw new TypeError('"encoding" must be a valid string encoding');var n=0|d(t,r),i=(e=u(e,n)).write(t,r);i!==n&&(e=e.slice(0,i));return e}(e,t,r):function(e,t){if(s.isBuffer(t)){var r=0|p(t.length);return 0===(e=u(e,r)).length?e:(t.copy(e,0,0,r),e)}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||(n=t.length)!=n?u(e,0):h(e,t);if("Buffer"===t.type&&o(t.data))return h(e,t.data)}var n;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,t)}function l(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function f(e,t){if(l(t),e=u(e,t<0?0:0|p(t)),!s.TYPED_ARRAY_SUPPORT)for(var r=0;r<t;++r)e[r]=0;return e}function h(e,t){var r=t.length<0?0:0|p(t.length);e=u(e,r);for(var n=0;n<r;n+=1)e[n]=255&t[n];return e}function p(e){if(e>=a())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+a().toString(16)+" bytes");return 0|e}function d(e,t){if(s.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var r=e.length;if(0===r)return 0;for(var n=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return Y(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return z(e).length;default:if(n)return Y(e).length;t=(""+t).toLowerCase(),n=!0}}function m(e,t,r){var n=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return _(this,t,r);case"utf8":case"utf-8":return k(this,t,r);case"ascii":return T(this,t,r);case"latin1":case"binary":return R(this,t,r);case"base64":return E(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return S(this,t,r);default:if(n)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),n=!0}}function g(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function v(e,t,r,n,i){if(0===e.length)return-1;if("string"==typeof r?(n=r,r=0):r>**********?r=**********:r<-2147483648&&(r=-2147483648),r=+r,isNaN(r)&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return-1;r=e.length-1}else if(r<0){if(!i)return-1;r=0}if("string"==typeof t&&(t=s.from(t,n)),s.isBuffer(t))return 0===t.length?-1:y(e,t,r,n,i);if("number"==typeof t)return t&=255,s.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):y(e,[t],r,n,i);throw new TypeError("val must be string, number or Buffer")}function y(e,t,r,n,i){var o,a=1,u=e.length,s=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return-1;a=2,u/=2,s/=2,r/=2}function c(e,t){return 1===a?e[t]:e.readUInt16BE(t*a)}if(i){var l=-1;for(o=r;o<u;o++)if(c(e,o)===c(t,-1===l?0:o-l)){if(-1===l&&(l=o),o-l+1===s)return l*a}else-1!==l&&(o-=o-l),l=-1}else for(r+s>u&&(r=u-s),o=r;o>=0;o--){for(var f=!0,h=0;h<s;h++)if(c(e,o+h)!==c(t,h)){f=!1;break}if(f)return o}return-1}function w(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=t.length;if(o%2!=0)throw new TypeError("Invalid hex string");n>o/2&&(n=o/2);for(var a=0;a<n;++a){var u=parseInt(t.substr(2*a,2),16);if(isNaN(u))return a;e[r+a]=u}return a}function b(e,t,r,n){return G(Y(t,e.length-r),e,r,n)}function A(e,t,r,n){return G(function(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(t),e,r,n)}function F(e,t,r,n){return A(e,t,r,n)}function B(e,t,r,n){return G(z(t),e,r,n)}function x(e,t,r,n){return G(function(e,t){for(var r,n,i,o=[],a=0;a<e.length&&!((t-=2)<0);++a)r=e.charCodeAt(a),n=r>>8,i=r%256,o.push(i),o.push(n);return o}(t,e.length-r),e,r,n)}function E(e,t,r){return 0===t&&r===e.length?n.fromByteArray(e):n.fromByteArray(e.slice(t,r))}function k(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var o,a,u,s,c=e[i],l=null,f=c>239?4:c>223?3:c>191?2:1;if(i+f<=r)switch(f){case 1:c<128&&(l=c);break;case 2:128==(192&(o=e[i+1]))&&(s=(31&c)<<6|63&o)>127&&(l=s);break;case 3:o=e[i+1],a=e[i+2],128==(192&o)&&128==(192&a)&&(s=(15&c)<<12|(63&o)<<6|63&a)>2047&&(s<55296||s>57343)&&(l=s);break;case 4:o=e[i+1],a=e[i+2],u=e[i+3],128==(192&o)&&128==(192&a)&&128==(192&u)&&(s=(15&c)<<18|(63&o)<<12|(63&a)<<6|63&u)>65535&&s<1114112&&(l=s)}null===l?(l=65533,f=1):l>65535&&(l-=65536,n.push(l>>>10&1023|55296),l=56320|1023&l),n.push(l),i+=f}return function(e){var t=e.length;if(t<=C)return String.fromCharCode.apply(String,e);var r="",n=0;for(;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=C));return r}(n)}t.Buffer=s,t.SlowBuffer=function(e){+e!=e&&(e=0);return s.alloc(+e)},t.INSPECT_MAX_BYTES=50,s.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}(),t.kMaxLength=a(),s.poolSize=8192,s._augment=function(e){return e.__proto__=s.prototype,e},s.from=function(e,t,r){return c(null,e,t,r)},s.TYPED_ARRAY_SUPPORT&&(s.prototype.__proto__=Uint8Array.prototype,s.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&s[Symbol.species]===s&&Object.defineProperty(s,Symbol.species,{value:null,configurable:!0})),s.alloc=function(e,t,r){return function(e,t,r,n){return l(t),t<=0?u(e,t):void 0!==r?"string"==typeof n?u(e,t).fill(r,n):u(e,t).fill(r):u(e,t)}(null,e,t,r)},s.allocUnsafe=function(e){return f(null,e)},s.allocUnsafeSlow=function(e){return f(null,e)},s.isBuffer=function(e){return!(null==e||!e._isBuffer)},s.compare=function(e,t){if(!s.isBuffer(e)||!s.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var r=e.length,n=t.length,i=0,o=Math.min(r,n);i<o;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:n<r?1:0},s.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},s.concat=function(e,t){if(!o(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return s.alloc(0);var r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;var n=s.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var a=e[r];if(!s.isBuffer(a))throw new TypeError('"list" argument must be an Array of Buffers');a.copy(n,i),i+=a.length}return n},s.byteLength=d,s.prototype._isBuffer=!0,s.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)g(this,t,t+1);return this},s.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)g(this,t,t+3),g(this,t+1,t+2);return this},s.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)g(this,t,t+7),g(this,t+1,t+6),g(this,t+2,t+5),g(this,t+3,t+4);return this},s.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?k(this,0,e):m.apply(this,arguments)},s.prototype.equals=function(e){if(!s.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===s.compare(this,e)},s.prototype.inspect=function(){var e="",r=t.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,r).match(/.{2}/g).join(" "),this.length>r&&(e+=" ... ")),"<Buffer "+e+">"},s.prototype.compare=function(e,t,r,n,i){if(!s.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return-1;if(t>=r)return 1;if(this===e)return 0;for(var o=(i>>>=0)-(n>>>=0),a=(r>>>=0)-(t>>>=0),u=Math.min(o,a),c=this.slice(n,i),l=e.slice(t,r),f=0;f<u;++f)if(c[f]!==l[f]){o=c[f],a=l[f];break}return o<a?-1:a<o?1:0},s.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},s.prototype.indexOf=function(e,t,r){return v(this,e,t,r,!0)},s.prototype.lastIndexOf=function(e,t,r){return v(this,e,t,r,!1)},s.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(r)?(r|=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var i=this.length-t;if((void 0===r||r>i)&&(r=i),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var o=!1;;)switch(n){case"hex":return w(this,e,t,r);case"utf8":case"utf-8":return b(this,e,t,r);case"ascii":return A(this,e,t,r);case"latin1":case"binary":return F(this,e,t,r);case"base64":return B(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return x(this,e,t,r);default:if(o)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),o=!0}},s.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var C=4096;function T(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}function R(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}function _(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=t;o<r;++o)i+=j(e[o]);return i}function S(e,t,r){for(var n=e.slice(t,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}function D(e,t,r){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function O(e,t,r,n,i,o){if(!s.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<o)throw new RangeError('"value" argument is out of bounds');if(r+n>e.length)throw new RangeError("Index out of range")}function I(e,t,r,n){t<0&&(t=65535+t+1);for(var i=0,o=Math.min(e.length-r,2);i<o;++i)e[r+i]=(t&255<<8*(n?i:1-i))>>>8*(n?i:1-i)}function L(e,t,r,n){t<0&&(t=4294967295+t+1);for(var i=0,o=Math.min(e.length-r,4);i<o;++i)e[r+i]=t>>>8*(n?i:3-i)&255}function N(e,t,r,n,i,o){if(r+n>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function P(e,t,r,n,o){return o||N(e,0,r,4),i.write(e,t,r,n,23,4),r+4}function U(e,t,r,n,o){return o||N(e,0,r,8),i.write(e,t,r,n,52,8),r+8}s.prototype.slice=function(e,t){var r,n=this.length;if((e=~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),(t=void 0===t?n:~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),t<e&&(t=e),s.TYPED_ARRAY_SUPPORT)(r=this.subarray(e,t)).__proto__=s.prototype;else{var i=t-e;r=new s(i,void 0);for(var o=0;o<i;++o)r[o]=this[o+e]}return r},s.prototype.readUIntLE=function(e,t,r){e|=0,t|=0,r||D(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n},s.prototype.readUIntBE=function(e,t,r){e|=0,t|=0,r||D(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},s.prototype.readUInt8=function(e,t){return t||D(e,1,this.length),this[e]},s.prototype.readUInt16LE=function(e,t){return t||D(e,2,this.length),this[e]|this[e+1]<<8},s.prototype.readUInt16BE=function(e,t){return t||D(e,2,this.length),this[e]<<8|this[e+1]},s.prototype.readUInt32LE=function(e,t){return t||D(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},s.prototype.readUInt32BE=function(e,t){return t||D(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},s.prototype.readIntLE=function(e,t,r){e|=0,t|=0,r||D(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},s.prototype.readIntBE=function(e,t,r){e|=0,t|=0,r||D(e,t,this.length);for(var n=t,i=1,o=this[e+--n];n>0&&(i*=256);)o+=this[e+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*t)),o},s.prototype.readInt8=function(e,t){return t||D(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},s.prototype.readInt16LE=function(e,t){t||D(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},s.prototype.readInt16BE=function(e,t){t||D(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},s.prototype.readInt32LE=function(e,t){return t||D(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},s.prototype.readInt32BE=function(e,t){return t||D(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},s.prototype.readFloatLE=function(e,t){return t||D(e,4,this.length),i.read(this,e,!0,23,4)},s.prototype.readFloatBE=function(e,t){return t||D(e,4,this.length),i.read(this,e,!1,23,4)},s.prototype.readDoubleLE=function(e,t){return t||D(e,8,this.length),i.read(this,e,!0,52,8)},s.prototype.readDoubleBE=function(e,t){return t||D(e,8,this.length),i.read(this,e,!1,52,8)},s.prototype.writeUIntLE=function(e,t,r,n){(e=+e,t|=0,r|=0,n)||O(this,e,t,r,Math.pow(2,8*r)-1,0);var i=1,o=0;for(this[t]=255&e;++o<r&&(i*=256);)this[t+o]=e/i&255;return t+r},s.prototype.writeUIntBE=function(e,t,r,n){(e=+e,t|=0,r|=0,n)||O(this,e,t,r,Math.pow(2,8*r)-1,0);var i=r-1,o=1;for(this[t+i]=255&e;--i>=0&&(o*=256);)this[t+i]=e/o&255;return t+r},s.prototype.writeUInt8=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,1,255,0),s.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},s.prototype.writeUInt16LE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,2,65535,0),s.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):I(this,e,t,!0),t+2},s.prototype.writeUInt16BE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,2,65535,0),s.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):I(this,e,t,!1),t+2},s.prototype.writeUInt32LE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,4,4294967295,0),s.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):L(this,e,t,!0),t+4},s.prototype.writeUInt32BE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,4,4294967295,0),s.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):L(this,e,t,!1),t+4},s.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);O(this,e,t,r,i-1,-i)}var o=0,a=1,u=0;for(this[t]=255&e;++o<r&&(a*=256);)e<0&&0===u&&0!==this[t+o-1]&&(u=1),this[t+o]=(e/a>>0)-u&255;return t+r},s.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);O(this,e,t,r,i-1,-i)}var o=r-1,a=1,u=0;for(this[t+o]=255&e;--o>=0&&(a*=256);)e<0&&0===u&&0!==this[t+o+1]&&(u=1),this[t+o]=(e/a>>0)-u&255;return t+r},s.prototype.writeInt8=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,1,127,-128),s.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},s.prototype.writeInt16LE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,2,32767,-32768),s.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):I(this,e,t,!0),t+2},s.prototype.writeInt16BE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,2,32767,-32768),s.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):I(this,e,t,!1),t+2},s.prototype.writeInt32LE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,4,**********,-2147483648),s.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):L(this,e,t,!0),t+4},s.prototype.writeInt32BE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,4,**********,-2147483648),e<0&&(e=4294967295+e+1),s.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):L(this,e,t,!1),t+4},s.prototype.writeFloatLE=function(e,t,r){return P(this,e,t,!0,r)},s.prototype.writeFloatBE=function(e,t,r){return P(this,e,t,!1,r)},s.prototype.writeDoubleLE=function(e,t,r){return U(this,e,t,!0,r)},s.prototype.writeDoubleBE=function(e,t,r){return U(this,e,t,!1,r)},s.prototype.copy=function(e,t,r,n){if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i,o=n-r;if(this===e&&r<t&&t<n)for(i=o-1;i>=0;--i)e[i+t]=this[i+r];else if(o<1e3||!s.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)e[i+t]=this[i+r];else Uint8Array.prototype.set.call(e,this.subarray(r,r+o),t);return o},s.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),1===e.length){var i=e.charCodeAt(0);i<256&&(e=i)}if(void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!s.isEncoding(n))throw new TypeError("Unknown encoding: "+n)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw new RangeError("Out of range index");if(r<=t)return this;var o;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(o=t;o<r;++o)this[o]=e;else{var a=s.isBuffer(e)?e:Y(new s(e,n).toString()),u=a.length;for(o=0;o<r-t;++o)this[o+t]=a[o%u]}return this};var M=/[^+\/0-9A-Za-z-_]/g;function j(e){return e<16?"0"+e.toString(16):e.toString(16)}function Y(e,t){var r;t=t||1/0;for(var n=e.length,i=null,o=[],a=0;a<n;++a){if((r=e.charCodeAt(a))>55295&&r<57344){if(!i){if(r>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(a+1===n){(t-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&o.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;o.push(r)}else if(r<2048){if((t-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function z(e){return n.toByteArray(function(e){if((e=function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}(e).replace(M,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function G(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length||i>=e.length);++i)t[i+r]=e[i];return i}}).call(this,r(28))},13:function(e,t,r){var n=r(49),i=r(50),o=r(51);e.exports=function(e,t){return n(e)||i(e,t)||o()}},1345:function(e,t,r){"use strict";r.r(t);var n=r(1),i=r.n(n),o=r(2),a=r.n(o),u=r(4),s=r.n(u),c=r(3),l=r.n(c),f=r(5),h=r.n(f),p=r(6),d=r(713),m=r.n(d),g=function(e){function t(){var e;return i()(this,t),(e=s()(this,l()(t).call(this))).name="Parse User Agent",e.module="UserAgent",e.description="Attempts to identify and categorise information contained in a user-agent string.",e.infoURL="https://wikipedia.org/wiki/User_agent",e.inputType="string",e.outputType="string",e.args=[],e}return h()(t,e),a()(t,[{key:"run",value:function(e,t){var r=m()(e);return`Browser\n    Name: ${r.browser.name||"unknown"}\n    Version: ${r.browser.version||"unknown"}\nDevice\n    Model: ${r.device.model||"unknown"}\n    Type: ${r.device.type||"unknown"}\n    Vendor: ${r.device.vendor||"unknown"}\nEngine\n    Name: ${r.engine.name||"unknown"}\n    Version: ${r.engine.version||"unknown"}\nOS\n    Name: ${r.os.name||"unknown"}\n    Version: ${r.os.version||"unknown"}\nCPU\n    Architecture: ${r.cpu.architecture||"unknown"}`}}]),t}(p.a),v="undefined"==typeof self?{}:self.OpModules||{};v.UserAgent={"Parse User Agent":g};t.default=v},14:function(e,t,r){"use strict";r.d(t,"b",function(){return i}),r.d(t,"a",function(){return o});var n=r(0);
/**
 * Base64 functions.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2016
 * @license Apache-2.0
 */function i(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"A-Za-z0-9+/=";if(!e)return"";e instanceof ArrayBuffer&&(e=new Uint8Array(e)),"string"==typeof e&&(e=n.b.strToByteArray(e)),t=n.b.expandAlphRange(t).join("");for(var r,i,o,a,u,s,c,l="",f=0;f<e.length;)a=(r=e[f++])>>2,u=(3&r)<<4|(i=e[f++])>>4,s=(15&i)<<2|(o=e[f++])>>6,c=63&o,isNaN(i)?s=c=64:isNaN(o)&&(c=64),l+=t.charAt(a)+t.charAt(u)+t.charAt(s)+t.charAt(c);return l}function o(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"A-Za-z0-9+/=",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"string",i=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];if(!e)return"string"===r?"":[];t=t||"A-Za-z0-9+/=",t=n.b.expandAlphRange(t).join("");var o,a,u,s,c,l,f=[],h=0;if(i){var p=new RegExp("[^"+t.replace(/[[\]\\\-^$]/g,"\\$&")+"]","g");e=e.replace(p,"")}for(;h<e.length;)o=t.indexOf(e.charAt(h++))<<2|(s=-1===(s=t.indexOf(e.charAt(h++)||"="))?64:s)>>4,a=(15&s)<<4|(c=-1===(c=t.indexOf(e.charAt(h++)||"="))?64:c)>>2,u=(3&c)<<6|(l=-1===(l=t.indexOf(e.charAt(h++)||"="))?64:l),f.push(o),64!==c&&f.push(a),64!==l&&f.push(u);return"string"===r?n.b.byteArrayToUtf8(f):f}},15:function(e,t,r){"use strict";var n=r(7),i=r.n(n),o=r(11),a=r.n(o),u=r(1),s=r.n(u),c=r(2),l=r.n(c),f=r(0),h=r(4),p=r.n(h),d=r(3),m=r.n(d),g=r(25),v=r.n(g),y=r(5),w=r.n(y);
/**
 * Custom error type for handling Dish type errors.
 * i.e. where the Dish cannot be successfully translated between types
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 */
var b=function(e){function t(){var e;s()(this,t);for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return(e=p()(this,m()(t).call(this,...n))).type="DishError",Error.captureStackTrace&&Error.captureStackTrace(v()(e),t),e}return w()(t,e),t}(function(e){function t(){var t=Reflect.construct(e,Array.from(arguments));return Object.setPrototypeOf(t,Object.getPrototypeOf(this)),t}return t.prototype=Object.create(e.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e,t}(Error)),A=r(16),F=r.n(A),B=r(18),x=r(26),E=r.n(x),k=function(){function e(){s()(this,e)}return l()(e,null,[{key:"checkForValue",value:function(e){if(void 0===e)throw new Error("only use translation methods with .bind")}},{key:"toArrayBuffer",value:function(){throw new Error("toArrayBuffer has not been implemented")}},{key:"fromArrayBuffer",value:function(){arguments.length>0&&void 0!==arguments[0]&&arguments[0];throw new Error("fromArrayBuffer has not been implemented")}}]),e}(),C=function(e){function t(){return s()(this,t),p()(this,m()(t).apply(this,arguments))}return w()(t,e),l()(t,null,[{key:"toArrayBuffer",value:function(){t.checkForValue(this.value),this.value=new Uint8Array(this.value).buffer}},{key:"fromArrayBuffer",value:function(){t.checkForValue(this.value),this.value=Array.prototype.slice.call(new Uint8Array(this.value))}}]),t}(k),T=function(e){function t(){return s()(this,t),p()(this,m()(t).apply(this,arguments))}return w()(t,e),l()(t,null,[{key:"toArrayBuffer",value:function(){t.checkForValue(this.value),this.value=F.a.isBigNumber(this.value)?f.b.strToArrayBuffer(this.value.toFixed()):new ArrayBuffer}},{key:"fromArrayBuffer",value:function(e){t.checkForValue(this.value);try{this.value=new F.a(f.b.arrayBufferToStr(this.value,!e))}catch(e){this.value=new F.a(NaN)}}}]),t}(k),R=function(e){function t(){return s()(this,t),p()(this,m()(t).apply(this,arguments))}return w()(t,e),l()(t,null,[{key:"toArrayBuffer",value:function(){var e=this;if(t.checkForValue(this.value),!Object(f.c)())return new Promise(function(t,r){f.b.readFile(e.value).then(function(t){return e.value=t.buffer}).then(t).catch(r)});this.value=f.b.readFileSync(this.value)}},{key:"fromArrayBuffer",value:function(){t.checkForValue(this.value),this.value=new File(this.value,"unknown")}}]),t}(k),_=function(e){function t(){return s()(this,t),p()(this,m()(t).apply(this,arguments))}return w()(t,e),l()(t,null,[{key:"toArrayBuffer",value:function(){t.checkForValue(this.value),this.value=this.value?f.b.strToArrayBuffer(this.value):new ArrayBuffer}},{key:"fromArrayBuffer",value:function(e){t.checkForValue(this.value),this.value=this.value?f.b.arrayBufferToStr(this.value,!e):""}}]),t}(k),S=function(e){function t(){return s()(this,t),p()(this,m()(t).apply(this,arguments))}return w()(t,e),l()(t,null,[{key:"toArrayBuffer",value:function(){t.checkForValue(this.value),this.value=this.value?f.b.strToArrayBuffer(f.b.unescapeHtml(f.b.stripHtmlTags(this.value,!0))):new ArrayBuffer}}]),t}(_),D=function(e){function t(){return s()(this,t),p()(this,m()(t).apply(this,arguments))}return w()(t,e),l()(t,null,[{key:"toArrayBuffer",value:function(){t.checkForValue(this.value),this.value=this.value?f.b.strToArrayBuffer(JSON.stringify(this.value,null,4)):new ArrayBuffer}},{key:"fromArrayBuffer",value:function(e){t.checkForValue(this.value),this.value=JSON.parse(f.b.arrayBufferToStr(this.value,!e))}}]),t}(k),O=function(e){function t(){return s()(this,t),p()(this,m()(t).apply(this,arguments))}return w()(t,e),l()(t,null,[{key:"toArrayBuffer",value:function(){t.checkForValue(this.value),Object(f.c)()&&(this.value=this.value.map(function(e){return Uint8Array.from(e.data)})),this.value=t.concatenateTypedArrays(...this.value).buffer}},{key:"fromArrayBuffer",value:function(){t.checkForValue(this.value),this.value=[new File(this.value,"unknown")]}},{key:"concatenateTypedArrays",value:function(){for(var e=0,t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];for(var i=0,o=r;i<o.length;i++){var a=o[i];e+=a.length}for(var u=new Uint8Array(e),s=0,c=0,l=r;c<l.length;c++){var f=l[c];u.set(f,s),s+=f.length}return u}}]),t}(k),I=function(e){function t(){return s()(this,t),p()(this,m()(t).apply(this,arguments))}return w()(t,e),l()(t,null,[{key:"toArrayBuffer",value:function(){t.checkForValue(this.value),this.value="number"==typeof this.value?f.b.strToArrayBuffer(this.value.toString()):new ArrayBuffer}},{key:"fromArrayBuffer",value:function(e){t.checkForValue(this.value),this.value=this.value?parseFloat(f.b.arrayBufferToStr(this.value,!e)):0}}]),t}(k),L=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(s()(this,e),this.value=new ArrayBuffer(0),this.type=e.ARRAY_BUFFER,t&&Object.prototype.hasOwnProperty.call(t,"value")&&Object.prototype.hasOwnProperty.call(t,"type"))this.set(t.value,t.type);else if(t&&null!==r)this.set(t,r);else if(t){var n=e.typeEnum(t.constructor.name);this.set(t,n)}}var t;return l()(e,[{key:"get",value:function(t){var r=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return"string"==typeof t&&(t=e.typeEnum(t)),this.type!==t?Object(f.c)()?(this._translate(t,n),this.value):new Promise(function(e,i){r._translate(t,n).then(function(){e(r.value)}).catch(i)}):this.value}},{key:"set",value:function(t,r){if("string"==typeof r&&(r=e.typeEnum(r)),E.a.debug("Dish type: "+e.enumLookup(r)),this.value=t,this.type=r,!this.valid()){var n=f.b.truncate(JSON.stringify(this.value),25);throw new b(`Data is not a valid ${e.enumLookup(r)}: ${n}`)}}},{key:"presentAs",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.clone();return r.get(e,t)}},{key:"detectDishType",value:function(){var e=new Uint8Array(this.value.slice(0,2048)),t=Object(B.a)(e);return t.length&&t[0].mime&&"text/plain"!==!t[0].mime?t[0].mime:null}},{key:"getTitle",value:(t=a()(i.a.mark(function t(r){var n,o;return i.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:n="",t.t0=this.type,t.next=t.t0===e.FILE?4:t.t0===e.LIST_FILE?6:t.t0===e.JSON?8:t.t0===e.NUMBER?10:t.t0===e.BIG_NUMBER?10:t.t0===e.ARRAY_BUFFER?12:t.t0===e.BYTE_ARRAY?12:15;break;case 4:return n=this.value.name,t.abrupt("break",26);case 6:return n=`${this.value.length} file(s)`,t.abrupt("break",26);case 8:return n="application/json",t.abrupt("break",26);case 10:return n=this.value.toString(),t.abrupt("break",26);case 12:if(null===(n=this.detectDishType())){t.next=15;break}return t.abrupt("break",26);case 15:return t.prev=15,(o=this.clone()).value=o.value.slice(0,256),t.next=20,o.get(e.STRING);case 20:n=t.sent,t.next=26;break;case 23:t.prev=23,t.t1=t.catch(15),E.a.error(`${e.enumLookup(this.type)} cannot be sliced. ${t.t1}`);case 26:return t.abrupt("return",n.slice(0,r));case 27:case"end":return t.stop()}},t,this,[[15,23]])})),function(e){return t.apply(this,arguments)})},{key:"valid",value:function(){switch(this.type){case e.BYTE_ARRAY:if(!(this.value instanceof Uint8Array||this.value instanceof Array))return!1;for(var t=0;t<this.value.length;t++)if("number"!=typeof this.value[t]||this.value[t]<0||this.value[t]>255)return!1;return!0;case e.STRING:case e.HTML:return"string"==typeof this.value;case e.NUMBER:return"number"==typeof this.value;case e.ARRAY_BUFFER:return this.value instanceof ArrayBuffer;case e.BIG_NUMBER:if(F.a.isBigNumber(this.value))return!0;if(Object.keys(this.value).sort().equals(["c","e","s"])){var r=new F.a;return r.c=this.value.c,r.e=this.value.e,r.s=this.value.s,this.value=r,!0}return!1;case e.JSON:return!0;case e.FILE:return this.value instanceof File;case e.LIST_FILE:return this.value instanceof Array&&this.value.reduce(function(e,t){return e&&t instanceof File},!0);default:return!1}}},{key:"clone",value:function(){var t=new e;switch(this.type){case e.STRING:case e.HTML:case e.NUMBER:case e.BIG_NUMBER:t.set(this.value,this.type);break;case e.BYTE_ARRAY:case e.JSON:t.set(JSON.parse(JSON.stringify(this.value)),this.type);break;case e.ARRAY_BUFFER:t.set(this.value.slice(0),this.type);break;case e.FILE:t.set(new File([this.value],this.value.name,{type:this.value.type,lastModified:this.value.lastModified}),this.type);break;case e.LIST_FILE:t.set(this.value.map(function(e){return new File([e],e.name,{type:e.type,lastModified:e.lastModified})}),this.type);break;default:throw new b("Cannot clone Dish, unknown type")}return t}},{key:"_translate",value:function(t){var r=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(E.a.debug(`Translating Dish from ${e.enumLookup(this.type)} to ${e.enumLookup(t)}`),!Object(f.c)())return new Promise(function(n,i){r._toArrayBuffer().then(function(){return r.type=e.ARRAY_BUFFER}).then(function(){r._fromArrayBuffer(t),n()}).catch(i)});this._toArrayBuffer(),this.type=e.ARRAY_BUFFER,this._fromArrayBuffer(t,n)}},{key:"_toArrayBuffer",value:function(){var t=this,r={browser:{[e.STRING]:function(){return Promise.resolve(_.toArrayBuffer.bind(t)())},[e.NUMBER]:function(){return Promise.resolve(I.toArrayBuffer.bind(t)())},[e.HTML]:function(){return Promise.resolve(S.toArrayBuffer.bind(t)())},[e.ARRAY_BUFFER]:function(){return Promise.resolve()},[e.BIG_NUMBER]:function(){return Promise.resolve(T.toArrayBuffer.bind(t)())},[e.JSON]:function(){return Promise.resolve(D.toArrayBuffer.bind(t)())},[e.FILE]:function(){return R.toArrayBuffer.bind(t)()},[e.LIST_FILE]:function(){return Promise.resolve(O.toArrayBuffer.bind(t)())},[e.BYTE_ARRAY]:function(){return Promise.resolve(C.toArrayBuffer.bind(t)())}},node:{[e.STRING]:function(){return _.toArrayBuffer.bind(t)()},[e.NUMBER]:function(){return I.toArrayBuffer.bind(t)()},[e.HTML]:function(){return S.toArrayBuffer.bind(t)()},[e.ARRAY_BUFFER]:function(){},[e.BIG_NUMBER]:function(){return T.toArrayBuffer.bind(t)()},[e.JSON]:function(){return D.toArrayBuffer.bind(t)()},[e.FILE]:function(){return R.toArrayBuffer.bind(t)()},[e.LIST_FILE]:function(){return O.toArrayBuffer.bind(t)()},[e.BYTE_ARRAY]:function(){return C.toArrayBuffer.bind(t)()}}};try{return r[Object(f.c)()?"node":"browser"][this.type]()}catch(t){throw new b(`Error translating from ${e.enumLookup(this.type)} to ArrayBuffer: ${t}`)}}},{key:"_fromArrayBuffer",value:function(t,r){var n=this,i={[e.STRING]:function(){return _.fromArrayBuffer.bind(n)(r)},[e.NUMBER]:function(){return I.fromArrayBuffer.bind(n)(r)},[e.HTML]:function(){return S.fromArrayBuffer.bind(n)(r)},[e.ARRAY_BUFFER]:function(){},[e.BIG_NUMBER]:function(){return T.fromArrayBuffer.bind(n)(r)},[e.JSON]:function(){return D.fromArrayBuffer.bind(n)(r)},[e.FILE]:function(){return R.fromArrayBuffer.bind(n)()},[e.LIST_FILE]:function(){return O.fromArrayBuffer.bind(n)()},[e.BYTE_ARRAY]:function(){return C.fromArrayBuffer.bind(n)()}};try{i[t](),this.type=t}catch(r){throw new b(`Error translating from ArrayBuffer to ${e.enumLookup(t)}: ${r}`)}}},{key:"size",get:function(){switch(this.type){case e.BYTE_ARRAY:case e.STRING:case e.HTML:return this.value.length;case e.NUMBER:case e.BIG_NUMBER:return this.value.toString().length;case e.ARRAY_BUFFER:return this.value.byteLength;case e.JSON:return JSON.stringify(this.value).length;case e.FILE:return this.value.size;case e.LIST_FILE:return this.value.reduce(function(e,t){return e+t.size},0);default:return-1}}}],[{key:"typeEnum",value:function(t){switch(t.toLowerCase()){case"bytearray":case"byte array":return e.BYTE_ARRAY;case"string":return e.STRING;case"number":return e.NUMBER;case"html":return e.HTML;case"arraybuffer":case"array buffer":return e.ARRAY_BUFFER;case"bignumber":case"big number":return e.BIG_NUMBER;case"json":case"object":return e.JSON;case"file":return e.FILE;case"list<file>":return e.LIST_FILE;default:throw new b("Invalid data type string. No matching enum.")}}},{key:"enumLookup",value:function(t){switch(t){case e.BYTE_ARRAY:return"byteArray";case e.STRING:return"string";case e.NUMBER:return"number";case e.HTML:return"html";case e.ARRAY_BUFFER:return"ArrayBuffer";case e.BIG_NUMBER:return"BigNumber";case e.JSON:return"JSON";case e.FILE:return"File";case e.LIST_FILE:return"List<File>";default:throw new b("Invalid data type enum. No matching type.")}}}]),e}();L.BYTE_ARRAY=0,L.STRING=1,L.NUMBER=2,L.HTML=3,L.ARRAY_BUFFER=4,L.BIG_NUMBER=5,L.JSON=6,L.FILE=7,L.LIST_FILE=8;t.a=L},16:function(e,t,r){var n;!function(i){"use strict";var o,a=/^-?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?$/i,u=Math.ceil,s=Math.floor,c="[BigNumber Error] ",l=c+"Number primitive has more than 15 significant digits: ",f=1e14,h=14,p=9007199254740991,d=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9,1e10,1e11,1e12,1e13],m=1e7,g=1e9;function v(e){var t=0|e;return e>0||e===t?t:t-1}function y(e){for(var t,r,n=1,i=e.length,o=e[0]+"";n<i;){for(t=e[n++]+"",r=h-t.length;r--;t="0"+t);o+=t}for(i=o.length;48===o.charCodeAt(--i););return o.slice(0,i+1||1)}function w(e,t){var r,n,i=e.c,o=t.c,a=e.s,u=t.s,s=e.e,c=t.e;if(!a||!u)return null;if(r=i&&!i[0],n=o&&!o[0],r||n)return r?n?0:-u:a;if(a!=u)return a;if(r=a<0,n=s==c,!i||!o)return n?0:!i^r?1:-1;if(!n)return s>c^r?1:-1;for(u=(s=i.length)<(c=o.length)?s:c,a=0;a<u;a++)if(i[a]!=o[a])return i[a]>o[a]^r?1:-1;return s==c?0:s>c^r?1:-1}function b(e,t,r,n){if(e<t||e>r||e!==s(e))throw Error(c+(n||"Argument")+("number"==typeof e?e<t||e>r?" out of range: ":" not an integer: ":" not a primitive number: ")+String(e))}function A(e){var t=e.c.length-1;return v(e.e/h)==t&&e.c[t]%2!=0}function F(e,t){return(e.length>1?e.charAt(0)+"."+e.slice(1):e)+(t<0?"e":"e+")+t}function B(e,t,r){var n,i;if(t<0){for(i=r+".";++t;i+=r);e=i+e}else if(++t>(n=e.length)){for(i=r,t-=n;--t;i+=r);e+=i}else t<n&&(e=e.slice(0,t)+"."+e.slice(t));return e}(o=function e(t){var r,n,i,o,x,E,k,C,T,R=z.prototype={constructor:z,toString:null,valueOf:null},_=new z(1),S=20,D=4,O=-7,I=21,L=-1e7,N=1e7,P=!1,U=1,M=0,j={prefix:"",groupSize:3,secondaryGroupSize:0,groupSeparator:",",decimalSeparator:".",fractionGroupSize:0,fractionGroupSeparator:" ",suffix:""},Y="0123456789abcdefghijklmnopqrstuvwxyz";function z(e,t){var r,o,u,c,f,d,m,g,v=this;if(!(v instanceof z))return new z(e,t);if(null==t){if(e&&!0===e._isBigNumber)return v.s=e.s,void(!e.c||e.e>N?v.c=v.e=null:e.e<L?v.c=[v.e=0]:(v.e=e.e,v.c=e.c.slice()));if((d="number"==typeof e)&&0*e==0){if(v.s=1/e<0?(e=-e,-1):1,e===~~e){for(c=0,f=e;f>=10;f/=10,c++);return void(c>N?v.c=v.e=null:(v.e=c,v.c=[e]))}g=String(e)}else{if(!a.test(g=String(e)))return i(v,g,d);v.s=45==g.charCodeAt(0)?(g=g.slice(1),-1):1}(c=g.indexOf("."))>-1&&(g=g.replace(".","")),(f=g.search(/e/i))>0?(c<0&&(c=f),c+=+g.slice(f+1),g=g.substring(0,f)):c<0&&(c=g.length)}else{if(b(t,2,Y.length,"Base"),10==t)return H(v=new z(e),S+v.e+1,D);if(g=String(e),d="number"==typeof e){if(0*e!=0)return i(v,g,d,t);if(v.s=1/e<0?(g=g.slice(1),-1):1,z.DEBUG&&g.replace(/^0\.0*|\./,"").length>15)throw Error(l+e)}else v.s=45===g.charCodeAt(0)?(g=g.slice(1),-1):1;for(r=Y.slice(0,t),c=f=0,m=g.length;f<m;f++)if(r.indexOf(o=g.charAt(f))<0){if("."==o){if(f>c){c=m;continue}}else if(!u&&(g==g.toUpperCase()&&(g=g.toLowerCase())||g==g.toLowerCase()&&(g=g.toUpperCase()))){u=!0,f=-1,c=0;continue}return i(v,String(e),d,t)}d=!1,(c=(g=n(g,t,10,v.s)).indexOf("."))>-1?g=g.replace(".",""):c=g.length}for(f=0;48===g.charCodeAt(f);f++);for(m=g.length;48===g.charCodeAt(--m););if(g=g.slice(f,++m)){if(m-=f,d&&z.DEBUG&&m>15&&(e>p||e!==s(e)))throw Error(l+v.s*e);if((c=c-f-1)>N)v.c=v.e=null;else if(c<L)v.c=[v.e=0];else{if(v.e=c,v.c=[],f=(c+1)%h,c<0&&(f+=h),f<m){for(f&&v.c.push(+g.slice(0,f)),m-=h;f<m;)v.c.push(+g.slice(f,f+=h));f=h-(g=g.slice(f)).length}else f-=m;for(;f--;g+="0");v.c.push(+g)}}else v.c=[v.e=0]}function G(e,t,r,n){var i,o,a,u,s;if(null==r?r=D:b(r,0,8),!e.c)return e.toString();if(i=e.c[0],a=e.e,null==t)s=y(e.c),s=1==n||2==n&&(a<=O||a>=I)?F(s,a):B(s,a,"0");else if(o=(e=H(new z(e),t,r)).e,u=(s=y(e.c)).length,1==n||2==n&&(t<=o||o<=O)){for(;u<t;s+="0",u++);s=F(s,o)}else if(t-=a,s=B(s,o,"0"),o+1>u){if(--t>0)for(s+=".";t--;s+="0");}else if((t+=o-u)>0)for(o+1==u&&(s+=".");t--;s+="0");return e.s<0&&i?"-"+s:s}function $(e,t){for(var r,n=1,i=new z(e[0]);n<e.length;n++){if(!(r=new z(e[n])).s){i=r;break}t.call(i,r)&&(i=r)}return i}function V(e,t,r){for(var n=1,i=t.length;!t[--i];t.pop());for(i=t[0];i>=10;i/=10,n++);return(r=n+r*h-1)>N?e.c=e.e=null:r<L?e.c=[e.e=0]:(e.e=r,e.c=t),e}function H(e,t,r,n){var i,o,a,c,l,p,m,g=e.c,v=d;if(g){e:{for(i=1,c=g[0];c>=10;c/=10,i++);if((o=t-i)<0)o+=h,a=t,m=(l=g[p=0])/v[i-a-1]%10|0;else if((p=u((o+1)/h))>=g.length){if(!n)break e;for(;g.length<=p;g.push(0));l=m=0,i=1,a=(o%=h)-h+1}else{for(l=c=g[p],i=1;c>=10;c/=10,i++);m=(a=(o%=h)-h+i)<0?0:l/v[i-a-1]%10|0}if(n=n||t<0||null!=g[p+1]||(a<0?l:l%v[i-a-1]),n=r<4?(m||n)&&(0==r||r==(e.s<0?3:2)):m>5||5==m&&(4==r||n||6==r&&(o>0?a>0?l/v[i-a]:0:g[p-1])%10&1||r==(e.s<0?8:7)),t<1||!g[0])return g.length=0,n?(t-=e.e+1,g[0]=v[(h-t%h)%h],e.e=-t||0):g[0]=e.e=0,e;if(0==o?(g.length=p,c=1,p--):(g.length=p+1,c=v[h-o],g[p]=a>0?s(l/v[i-a]%v[a])*c:0),n)for(;;){if(0==p){for(o=1,a=g[0];a>=10;a/=10,o++);for(a=g[0]+=c,c=1;a>=10;a/=10,c++);o!=c&&(e.e++,g[0]==f&&(g[0]=1));break}if(g[p]+=c,g[p]!=f)break;g[p--]=0,c=1}for(o=g.length;0===g[--o];g.pop());}e.e>N?e.c=e.e=null:e.e<L&&(e.c=[e.e=0])}return e}function q(e){var t,r=e.e;return null===r?e.toString():(t=y(e.c),t=r<=O||r>=I?F(t,r):B(t,r,"0"),e.s<0?"-"+t:t)}return z.clone=e,z.ROUND_UP=0,z.ROUND_DOWN=1,z.ROUND_CEIL=2,z.ROUND_FLOOR=3,z.ROUND_HALF_UP=4,z.ROUND_HALF_DOWN=5,z.ROUND_HALF_EVEN=6,z.ROUND_HALF_CEIL=7,z.ROUND_HALF_FLOOR=8,z.EUCLID=9,z.config=z.set=function(e){var t,r;if(null!=e){if("object"!=typeof e)throw Error(c+"Object expected: "+e);if(e.hasOwnProperty(t="DECIMAL_PLACES")&&(b(r=e[t],0,g,t),S=r),e.hasOwnProperty(t="ROUNDING_MODE")&&(b(r=e[t],0,8,t),D=r),e.hasOwnProperty(t="EXPONENTIAL_AT")&&((r=e[t])&&r.pop?(b(r[0],-g,0,t),b(r[1],0,g,t),O=r[0],I=r[1]):(b(r,-g,g,t),O=-(I=r<0?-r:r))),e.hasOwnProperty(t="RANGE"))if((r=e[t])&&r.pop)b(r[0],-g,-1,t),b(r[1],1,g,t),L=r[0],N=r[1];else{if(b(r,-g,g,t),!r)throw Error(c+t+" cannot be zero: "+r);L=-(N=r<0?-r:r)}if(e.hasOwnProperty(t="CRYPTO")){if((r=e[t])!==!!r)throw Error(c+t+" not true or false: "+r);if(r){if("undefined"==typeof crypto||!crypto||!crypto.getRandomValues&&!crypto.randomBytes)throw P=!r,Error(c+"crypto unavailable");P=r}else P=r}if(e.hasOwnProperty(t="MODULO_MODE")&&(b(r=e[t],0,9,t),U=r),e.hasOwnProperty(t="POW_PRECISION")&&(b(r=e[t],0,g,t),M=r),e.hasOwnProperty(t="FORMAT")){if("object"!=typeof(r=e[t]))throw Error(c+t+" not an object: "+r);j=r}if(e.hasOwnProperty(t="ALPHABET")){if("string"!=typeof(r=e[t])||/^.$|[+-.\s]|(.).*\1/.test(r))throw Error(c+t+" invalid: "+r);Y=r}}return{DECIMAL_PLACES:S,ROUNDING_MODE:D,EXPONENTIAL_AT:[O,I],RANGE:[L,N],CRYPTO:P,MODULO_MODE:U,POW_PRECISION:M,FORMAT:j,ALPHABET:Y}},z.isBigNumber=function(e){if(!e||!0!==e._isBigNumber)return!1;if(!z.DEBUG)return!0;var t,r,n=e.c,i=e.e,o=e.s;e:if("[object Array]"=={}.toString.call(n)){if((1===o||-1===o)&&i>=-g&&i<=g&&i===s(i)){if(0===n[0]){if(0===i&&1===n.length)return!0;break e}if((t=(i+1)%h)<1&&(t+=h),String(n[0]).length==t){for(t=0;t<n.length;t++)if((r=n[t])<0||r>=f||r!==s(r))break e;if(0!==r)return!0}}}else if(null===n&&null===i&&(null===o||1===o||-1===o))return!0;throw Error(c+"Invalid BigNumber: "+e)},z.maximum=z.max=function(){return $(arguments,R.lt)},z.minimum=z.min=function(){return $(arguments,R.gt)},z.random=(o=9007199254740992*Math.random()&2097151?function(){return s(9007199254740992*Math.random())}:function(){return 8388608*(1073741824*Math.random()|0)+(8388608*Math.random()|0)},function(e){var t,r,n,i,a,l=0,f=[],p=new z(_);if(null==e?e=S:b(e,0,g),i=u(e/h),P)if(crypto.getRandomValues){for(t=crypto.getRandomValues(new Uint32Array(i*=2));l<i;)(a=131072*t[l]+(t[l+1]>>>11))>=9e15?(r=crypto.getRandomValues(new Uint32Array(2)),t[l]=r[0],t[l+1]=r[1]):(f.push(a%1e14),l+=2);l=i/2}else{if(!crypto.randomBytes)throw P=!1,Error(c+"crypto unavailable");for(t=crypto.randomBytes(i*=7);l<i;)(a=281474976710656*(31&t[l])+1099511627776*t[l+1]+4294967296*t[l+2]+16777216*t[l+3]+(t[l+4]<<16)+(t[l+5]<<8)+t[l+6])>=9e15?crypto.randomBytes(7).copy(t,l):(f.push(a%1e14),l+=7);l=i/7}if(!P)for(;l<i;)(a=o())<9e15&&(f[l++]=a%1e14);for(i=f[--l],e%=h,i&&e&&(a=d[h-e],f[l]=s(i/a)*a);0===f[l];f.pop(),l--);if(l<0)f=[n=0];else{for(n=-1;0===f[0];f.splice(0,1),n-=h);for(l=1,a=f[0];a>=10;a/=10,l++);l<h&&(n-=h-l)}return p.e=n,p.c=f,p}),z.sum=function(){for(var e=1,t=arguments,r=new z(t[0]);e<t.length;)r=r.plus(t[e++]);return r},n=function(){function e(e,t,r,n){for(var i,o,a=[0],u=0,s=e.length;u<s;){for(o=a.length;o--;a[o]*=t);for(a[0]+=n.indexOf(e.charAt(u++)),i=0;i<a.length;i++)a[i]>r-1&&(null==a[i+1]&&(a[i+1]=0),a[i+1]+=a[i]/r|0,a[i]%=r)}return a.reverse()}return function(t,n,i,o,a){var u,s,c,l,f,h,p,d,m=t.indexOf("."),g=S,v=D;for(m>=0&&(l=M,M=0,t=t.replace(".",""),h=(d=new z(n)).pow(t.length-m),M=l,d.c=e(B(y(h.c),h.e,"0"),10,i,"0123456789"),d.e=d.c.length),c=l=(p=e(t,n,i,a?(u=Y,"0123456789"):(u="0123456789",Y))).length;0==p[--l];p.pop());if(!p[0])return u.charAt(0);if(m<0?--c:(h.c=p,h.e=c,h.s=o,p=(h=r(h,d,g,v,i)).c,f=h.r,c=h.e),m=p[s=c+g+1],l=i/2,f=f||s<0||null!=p[s+1],f=v<4?(null!=m||f)&&(0==v||v==(h.s<0?3:2)):m>l||m==l&&(4==v||f||6==v&&1&p[s-1]||v==(h.s<0?8:7)),s<1||!p[0])t=f?B(u.charAt(1),-g,u.charAt(0)):u.charAt(0);else{if(p.length=s,f)for(--i;++p[--s]>i;)p[s]=0,s||(++c,p=[1].concat(p));for(l=p.length;!p[--l];);for(m=0,t="";m<=l;t+=u.charAt(p[m++]));t=B(t,c,u.charAt(0))}return t}}(),r=function(){function e(e,t,r){var n,i,o,a,u=0,s=e.length,c=t%m,l=t/m|0;for(e=e.slice();s--;)u=((i=c*(o=e[s]%m)+(n=l*o+(a=e[s]/m|0)*c)%m*m+u)/r|0)+(n/m|0)+l*a,e[s]=i%r;return u&&(e=[u].concat(e)),e}function t(e,t,r,n){var i,o;if(r!=n)o=r>n?1:-1;else for(i=o=0;i<r;i++)if(e[i]!=t[i]){o=e[i]>t[i]?1:-1;break}return o}function r(e,t,r,n){for(var i=0;r--;)e[r]-=i,i=e[r]<t[r]?1:0,e[r]=i*n+e[r]-t[r];for(;!e[0]&&e.length>1;e.splice(0,1));}return function(n,i,o,a,u){var c,l,p,d,m,g,y,w,b,A,F,B,x,E,k,C,T,R=n.s==i.s?1:-1,_=n.c,S=i.c;if(!(_&&_[0]&&S&&S[0]))return new z(n.s&&i.s&&(_?!S||_[0]!=S[0]:S)?_&&0==_[0]||!S?0*R:R/0:NaN);for(b=(w=new z(R)).c=[],R=o+(l=n.e-i.e)+1,u||(u=f,l=v(n.e/h)-v(i.e/h),R=R/h|0),p=0;S[p]==(_[p]||0);p++);if(S[p]>(_[p]||0)&&l--,R<0)b.push(1),d=!0;else{for(E=_.length,C=S.length,p=0,R+=2,(m=s(u/(S[0]+1)))>1&&(S=e(S,m,u),_=e(_,m,u),C=S.length,E=_.length),x=C,F=(A=_.slice(0,C)).length;F<C;A[F++]=0);T=S.slice(),T=[0].concat(T),k=S[0],S[1]>=u/2&&k++;do{if(m=0,(c=t(S,A,C,F))<0){if(B=A[0],C!=F&&(B=B*u+(A[1]||0)),(m=s(B/k))>1)for(m>=u&&(m=u-1),y=(g=e(S,m,u)).length,F=A.length;1==t(g,A,y,F);)m--,r(g,C<y?T:S,y,u),y=g.length,c=1;else 0==m&&(c=m=1),y=(g=S.slice()).length;if(y<F&&(g=[0].concat(g)),r(A,g,F,u),F=A.length,-1==c)for(;t(S,A,C,F)<1;)m++,r(A,C<F?T:S,F,u),F=A.length}else 0===c&&(m++,A=[0]);b[p++]=m,A[0]?A[F++]=_[x]||0:(A=[_[x]],F=1)}while((x++<E||null!=A[0])&&R--);d=null!=A[0],b[0]||b.splice(0,1)}if(u==f){for(p=1,R=b[0];R>=10;R/=10,p++);H(w,o+(w.e=p+l*h-1)+1,a,d)}else w.e=l,w.r=+d;return w}}(),x=/^(-?)0([xbo])(?=\w[\w.]*$)/i,E=/^([^.]+)\.$/,k=/^\.([^.]+)$/,C=/^-?(Infinity|NaN)$/,T=/^\s*\+(?=[\w.])|^\s+|\s+$/g,i=function(e,t,r,n){var i,o=r?t:t.replace(T,"");if(C.test(o))e.s=isNaN(o)?null:o<0?-1:1;else{if(!r&&(o=o.replace(x,function(e,t,r){return i="x"==(r=r.toLowerCase())?16:"b"==r?2:8,n&&n!=i?e:t}),n&&(i=n,o=o.replace(E,"$1").replace(k,"0.$1")),t!=o))return new z(o,i);if(z.DEBUG)throw Error(c+"Not a"+(n?" base "+n:"")+" number: "+t);e.s=null}e.c=e.e=null},R.absoluteValue=R.abs=function(){var e=new z(this);return e.s<0&&(e.s=1),e},R.comparedTo=function(e,t){return w(this,new z(e,t))},R.decimalPlaces=R.dp=function(e,t){var r,n,i,o=this;if(null!=e)return b(e,0,g),null==t?t=D:b(t,0,8),H(new z(o),e+o.e+1,t);if(!(r=o.c))return null;if(n=((i=r.length-1)-v(this.e/h))*h,i=r[i])for(;i%10==0;i/=10,n--);return n<0&&(n=0),n},R.dividedBy=R.div=function(e,t){return r(this,new z(e,t),S,D)},R.dividedToIntegerBy=R.idiv=function(e,t){return r(this,new z(e,t),0,1)},R.exponentiatedBy=R.pow=function(e,t){var r,n,i,o,a,l,f,p,d=this;if((e=new z(e)).c&&!e.isInteger())throw Error(c+"Exponent not an integer: "+q(e));if(null!=t&&(t=new z(t)),a=e.e>14,!d.c||!d.c[0]||1==d.c[0]&&!d.e&&1==d.c.length||!e.c||!e.c[0])return p=new z(Math.pow(+q(d),a?2-A(e):+q(e))),t?p.mod(t):p;if(l=e.s<0,t){if(t.c?!t.c[0]:!t.s)return new z(NaN);(n=!l&&d.isInteger()&&t.isInteger())&&(d=d.mod(t))}else{if(e.e>9&&(d.e>0||d.e<-1||(0==d.e?d.c[0]>1||a&&d.c[1]>=24e7:d.c[0]<8e13||a&&d.c[0]<=9999975e7)))return o=d.s<0&&A(e)?-0:0,d.e>-1&&(o=1/o),new z(l?1/o:o);M&&(o=u(M/h+2))}for(a?(r=new z(.5),l&&(e.s=1),f=A(e)):f=(i=Math.abs(+q(e)))%2,p=new z(_);;){if(f){if(!(p=p.times(d)).c)break;o?p.c.length>o&&(p.c.length=o):n&&(p=p.mod(t))}if(i){if(0===(i=s(i/2)))break;f=i%2}else if(H(e=e.times(r),e.e+1,1),e.e>14)f=A(e);else{if(0===(i=+q(e)))break;f=i%2}d=d.times(d),o?d.c&&d.c.length>o&&(d.c.length=o):n&&(d=d.mod(t))}return n?p:(l&&(p=_.div(p)),t?p.mod(t):o?H(p,M,D,void 0):p)},R.integerValue=function(e){var t=new z(this);return null==e?e=D:b(e,0,8),H(t,t.e+1,e)},R.isEqualTo=R.eq=function(e,t){return 0===w(this,new z(e,t))},R.isFinite=function(){return!!this.c},R.isGreaterThan=R.gt=function(e,t){return w(this,new z(e,t))>0},R.isGreaterThanOrEqualTo=R.gte=function(e,t){return 1===(t=w(this,new z(e,t)))||0===t},R.isInteger=function(){return!!this.c&&v(this.e/h)>this.c.length-2},R.isLessThan=R.lt=function(e,t){return w(this,new z(e,t))<0},R.isLessThanOrEqualTo=R.lte=function(e,t){return-1===(t=w(this,new z(e,t)))||0===t},R.isNaN=function(){return!this.s},R.isNegative=function(){return this.s<0},R.isPositive=function(){return this.s>0},R.isZero=function(){return!!this.c&&0==this.c[0]},R.minus=function(e,t){var r,n,i,o,a=this,u=a.s;if(t=(e=new z(e,t)).s,!u||!t)return new z(NaN);if(u!=t)return e.s=-t,a.plus(e);var s=a.e/h,c=e.e/h,l=a.c,p=e.c;if(!s||!c){if(!l||!p)return l?(e.s=-t,e):new z(p?a:NaN);if(!l[0]||!p[0])return p[0]?(e.s=-t,e):new z(l[0]?a:3==D?-0:0)}if(s=v(s),c=v(c),l=l.slice(),u=s-c){for((o=u<0)?(u=-u,i=l):(c=s,i=p),i.reverse(),t=u;t--;i.push(0));i.reverse()}else for(n=(o=(u=l.length)<(t=p.length))?u:t,u=t=0;t<n;t++)if(l[t]!=p[t]){o=l[t]<p[t];break}if(o&&(i=l,l=p,p=i,e.s=-e.s),(t=(n=p.length)-(r=l.length))>0)for(;t--;l[r++]=0);for(t=f-1;n>u;){if(l[--n]<p[n]){for(r=n;r&&!l[--r];l[r]=t);--l[r],l[n]+=f}l[n]-=p[n]}for(;0==l[0];l.splice(0,1),--c);return l[0]?V(e,l,c):(e.s=3==D?-1:1,e.c=[e.e=0],e)},R.modulo=R.mod=function(e,t){var n,i,o=this;return e=new z(e,t),!o.c||!e.s||e.c&&!e.c[0]?new z(NaN):!e.c||o.c&&!o.c[0]?new z(o):(9==U?(i=e.s,e.s=1,n=r(o,e,0,3),e.s=i,n.s*=i):n=r(o,e,0,U),(e=o.minus(n.times(e))).c[0]||1!=U||(e.s=o.s),e)},R.multipliedBy=R.times=function(e,t){var r,n,i,o,a,u,s,c,l,p,d,g,y,w,b,A=this,F=A.c,B=(e=new z(e,t)).c;if(!(F&&B&&F[0]&&B[0]))return!A.s||!e.s||F&&!F[0]&&!B||B&&!B[0]&&!F?e.c=e.e=e.s=null:(e.s*=A.s,F&&B?(e.c=[0],e.e=0):e.c=e.e=null),e;for(n=v(A.e/h)+v(e.e/h),e.s*=A.s,(s=F.length)<(p=B.length)&&(y=F,F=B,B=y,i=s,s=p,p=i),i=s+p,y=[];i--;y.push(0));for(w=f,b=m,i=p;--i>=0;){for(r=0,d=B[i]%b,g=B[i]/b|0,o=i+(a=s);o>i;)r=((c=d*(c=F[--a]%b)+(u=g*c+(l=F[a]/b|0)*d)%b*b+y[o]+r)/w|0)+(u/b|0)+g*l,y[o--]=c%w;y[o]=r}return r?++n:y.splice(0,1),V(e,y,n)},R.negated=function(){var e=new z(this);return e.s=-e.s||null,e},R.plus=function(e,t){var r,n=this,i=n.s;if(t=(e=new z(e,t)).s,!i||!t)return new z(NaN);if(i!=t)return e.s=-t,n.minus(e);var o=n.e/h,a=e.e/h,u=n.c,s=e.c;if(!o||!a){if(!u||!s)return new z(i/0);if(!u[0]||!s[0])return s[0]?e:new z(u[0]?n:0*i)}if(o=v(o),a=v(a),u=u.slice(),i=o-a){for(i>0?(a=o,r=s):(i=-i,r=u),r.reverse();i--;r.push(0));r.reverse()}for((i=u.length)-(t=s.length)<0&&(r=s,s=u,u=r,t=i),i=0;t;)i=(u[--t]=u[t]+s[t]+i)/f|0,u[t]=f===u[t]?0:u[t]%f;return i&&(u=[i].concat(u),++a),V(e,u,a)},R.precision=R.sd=function(e,t){var r,n,i,o=this;if(null!=e&&e!==!!e)return b(e,1,g),null==t?t=D:b(t,0,8),H(new z(o),e,t);if(!(r=o.c))return null;if(n=(i=r.length-1)*h+1,i=r[i]){for(;i%10==0;i/=10,n--);for(i=r[0];i>=10;i/=10,n++);}return e&&o.e+1>n&&(n=o.e+1),n},R.shiftedBy=function(e){return b(e,-p,p),this.times("1e"+e)},R.squareRoot=R.sqrt=function(){var e,t,n,i,o,a=this,u=a.c,s=a.s,c=a.e,l=S+4,f=new z("0.5");if(1!==s||!u||!u[0])return new z(!s||s<0&&(!u||u[0])?NaN:u?a:1/0);if(0==(s=Math.sqrt(+q(a)))||s==1/0?(((t=y(u)).length+c)%2==0&&(t+="0"),s=Math.sqrt(+t),c=v((c+1)/2)-(c<0||c%2),n=new z(t=s==1/0?"1e"+c:(t=s.toExponential()).slice(0,t.indexOf("e")+1)+c)):n=new z(s+""),n.c[0])for((s=(c=n.e)+l)<3&&(s=0);;)if(o=n,n=f.times(o.plus(r(a,o,l,1))),y(o.c).slice(0,s)===(t=y(n.c)).slice(0,s)){if(n.e<c&&--s,"9999"!=(t=t.slice(s-3,s+1))&&(i||"4999"!=t)){+t&&(+t.slice(1)||"5"!=t.charAt(0))||(H(n,n.e+S+2,1),e=!n.times(n).eq(a));break}if(!i&&(H(o,o.e+S+2,0),o.times(o).eq(a))){n=o;break}l+=4,s+=4,i=1}return H(n,n.e+S+1,D,e)},R.toExponential=function(e,t){return null!=e&&(b(e,0,g),e++),G(this,e,t,1)},R.toFixed=function(e,t){return null!=e&&(b(e,0,g),e=e+this.e+1),G(this,e,t)},R.toFormat=function(e,t,r){var n,i=this;if(null==r)null!=e&&t&&"object"==typeof t?(r=t,t=null):e&&"object"==typeof e?(r=e,e=t=null):r=j;else if("object"!=typeof r)throw Error(c+"Argument not an object: "+r);if(n=i.toFixed(e,t),i.c){var o,a=n.split("."),u=+r.groupSize,s=+r.secondaryGroupSize,l=r.groupSeparator||"",f=a[0],h=a[1],p=i.s<0,d=p?f.slice(1):f,m=d.length;if(s&&(o=u,u=s,s=o,m-=o),u>0&&m>0){for(o=m%u||u,f=d.substr(0,o);o<m;o+=u)f+=l+d.substr(o,u);s>0&&(f+=l+d.slice(o)),p&&(f="-"+f)}n=h?f+(r.decimalSeparator||"")+((s=+r.fractionGroupSize)?h.replace(new RegExp("\\d{"+s+"}\\B","g"),"$&"+(r.fractionGroupSeparator||"")):h):f}return(r.prefix||"")+n+(r.suffix||"")},R.toFraction=function(e){var t,n,i,o,a,u,s,l,f,p,m,g,v=this,w=v.c;if(null!=e&&(!(s=new z(e)).isInteger()&&(s.c||1!==s.s)||s.lt(_)))throw Error(c+"Argument "+(s.isInteger()?"out of range: ":"not an integer: ")+q(s));if(!w)return new z(v);for(t=new z(_),f=n=new z(_),i=l=new z(_),g=y(w),a=t.e=g.length-v.e-1,t.c[0]=d[(u=a%h)<0?h+u:u],e=!e||s.comparedTo(t)>0?a>0?t:f:s,u=N,N=1/0,s=new z(g),l.c[0]=0;p=r(s,t,0,1),1!=(o=n.plus(p.times(i))).comparedTo(e);)n=i,i=o,f=l.plus(p.times(o=f)),l=o,t=s.minus(p.times(o=t)),s=o;return o=r(e.minus(n),i,0,1),l=l.plus(o.times(f)),n=n.plus(o.times(i)),l.s=f.s=v.s,m=r(f,i,a*=2,D).minus(v).abs().comparedTo(r(l,n,a,D).minus(v).abs())<1?[f,i]:[l,n],N=u,m},R.toNumber=function(){return+q(this)},R.toPrecision=function(e,t){return null!=e&&b(e,1,g),G(this,e,t,2)},R.toString=function(e){var t,r=this,i=r.s,o=r.e;return null===o?i?(t="Infinity",i<0&&(t="-"+t)):t="NaN":(null==e?t=o<=O||o>=I?F(y(r.c),o):B(y(r.c),o,"0"):10===e?t=B(y((r=H(new z(r),S+o+1,D)).c),r.e,"0"):(b(e,2,Y.length,"Base"),t=n(B(y(r.c),o,"0"),10,e,i,!0)),i<0&&r.c[0]&&(t="-"+t)),t},R.valueOf=R.toJSON=function(){return q(this)},R._isBigNumber=!0,null!=t&&z.set(t),z}()).default=o.BigNumber=o,void 0===(n=function(){return o}.call(t,r,t,e))||(e.exports=n)}()},17:function(e,t,r){"use strict";r.d(t,"b",function(){return i}),r.d(t,"c",function(){return o}),r.d(t,"a",function(){return a});var n=r(0);
/**
 * Hexadecimal functions.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2016
 * @license Apache-2.0
 */function i(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:" ",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2;if(!e)return"";e instanceof ArrayBuffer&&(e=new Uint8Array(e));for(var n="",i=0;i<e.length;i++)n+=e[i].toString(16).padStart(r,"0")+t;return"0x"===t&&(n="0x"+n),"\\x"===t&&(n="\\x"+n),t.length?n.slice(0,-t.length):n}function o(e){if(!e)return"";e instanceof ArrayBuffer&&(e=new Uint8Array(e));for(var t=[],r=0;r<e.length;r++)t.push((e[r]>>>4).toString(16)),t.push((15&e[r]).toString(16));return t.join("")}function a(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Auto",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2;if("None"!==t){var i="Auto"===t?/[^a-f\d]/gi:n.b.regexRep(t);e=e.replace(i,"")}for(var o=[],a=0;a<e.length;a+=r)o.push(parseInt(e.substr(a,r),16));return o}["Auto"].concat(["Space","Percent","Comma","Semi-colon","Colon","Line feed","CRLF","0x","\\x","None"])},18:function(e,t,r){"use strict";var n=r(13),i=r.n(n),o=r(10),a={Images:[{name:"Joint Photographic Experts Group image",extension:"jpg,jpeg,jpe,thm,mpo",mime:"image/jpeg",description:"",signature:{0:255,1:216,2:255,3:[192,196,219,221,224,225,226,227,228,229,231,232,234,235,236,237,238,254]},extractor:function(e,t){var r=new o.a(e.slice(t));for(;r.hasMore();){var n=r.getBytes(2);if(255!==n[0])throw new Error(`Invalid marker while parsing JPEG at pos ${r.position}: ${n}`);var i=0;switch(n[1]){case 216:case 1:break;case 217:return r.carve();case 192:case 193:case 194:case 195:case 196:case 197:case 198:case 199:case 200:case 201:case 202:case 203:case 204:case 205:case 206:case 207:case 219:case 222:case 224:case 225:case 226:case 227:case 228:case 229:case 230:case 231:case 232:case 233:case 234:case 235:case 236:case 237:case 238:case 239:case 254:i=r.readInt(2,"be"),r.position+=i-2;break;case 223:r.position++;break;case 220:case 221:r.position+=2;break;case 218:i=r.readInt(2,"be"),r.position+=i-2,r.continueUntil(255);break;case 0:case 208:case 209:case 210:case 211:case 212:case 213:case 214:case 215:default:r.continueUntil(255)}}throw new Error("Unable to parse JPEG successfully")}},{name:"Graphics Interchange Format image",extension:"gif",mime:"image/gif",description:"",signature:{0:71,1:73,2:70,3:56,4:[55,57],5:97},extractor:null},{name:"Portable Network Graphics image",extension:"png",mime:"image/png",description:"",signature:{0:137,1:80,2:78,3:71,4:13,5:10,6:26,7:10},extractor:function(e,t){var r=new o.a(e.slice(t));r.moveForwardsBy(8);var n=0,i="";for(;"IEND"!==i;)n=r.readInt(4,"be"),i=r.readString(4),r.moveForwardsBy(n+4);return r.carve()}},{name:"WEBP Image",extension:"webp",mime:"image/webp",description:"",signature:{8:87,9:69,10:66,11:80},extractor:null},{name:"Camera Image File Format",extension:"crw",mime:"image/x-canon-crw",description:"",signature:{6:72,7:69,8:65,9:80,10:67,11:67,12:68,13:82},extractor:null},{name:"Canon CR2 raw image",extension:"cr2",mime:"image/x-canon-cr2",description:"",signature:[{0:73,1:73,2:42,3:0,8:67,9:82},{0:77,1:77,2:0,3:42,8:67,9:82}],extractor:null},{name:"Tagged Image File Format image",extension:"tif",mime:"image/tiff",description:"",signature:[{0:73,1:73,2:42,3:0},{0:77,1:77,2:0,3:42}],extractor:null},{name:"Bitmap image",extension:"bmp",mime:"image/bmp",description:"",signature:{0:66,1:77,7:0,9:0,14:[12,40,56,64,108,124],15:0,16:0,17:0},extractor:function(e,t){var r=new o.a(e.slice(t));r.moveForwardsBy(2);var n=r.readInt(4,"le");return r.moveForwardsBy(n-6),r.carve()}},{name:"JPEG Extended Range image",extension:"jxr",mime:"image/vnd.ms-photo",description:"",signature:{0:73,1:73,2:188},extractor:null},{name:"Photoshop image",extension:"psd",mime:"image/vnd.adobe.photoshop",description:"",signature:{0:56,1:66,2:80,3:83,4:0,5:1,6:0,7:0,8:0,9:0,10:0,11:0},extractor:null},{name:"Photoshop Large Document",extension:"psb",mime:"application/x-photoshop",description:"",signature:{0:56,1:66,2:80,3:83,4:0,5:2,6:0,7:0,8:0,9:0,10:0,11:0,12:0},extractor:null},{name:"Paint Shop Pro image",extension:"psp",mime:"image/psp",description:"",signature:[{0:80,1:97,2:105,3:110,4:116,5:32,6:83,7:104,8:111,9:112,10:32,11:80,12:114,13:111,14:32,15:73,16:109},{0:126,1:66,2:75,3:0}],extractor:null},{name:"Icon image",extension:"ico",mime:"image/x-icon",description:"",signature:{0:0,1:0,2:1,3:0,4:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],5:0,6:[16,32,48,64,128],7:[16,32,48,64,128],9:0,10:[0,1]},extractor:null},{name:"Radiance High Dynamic Range image",extension:"hdr",mime:"image/vnd.radiance",description:"",signature:{0:35,1:63,2:82,3:65,4:68,5:73,6:65,7:78,8:67,9:69,10:10},extractor:null},{name:"Sony ARW image",extension:"arw",mime:"image/x-raw",description:"",signature:{0:5,1:0,2:0,3:0,4:65,5:87,6:49,7:46},extractor:null},{name:"Fujifilm Raw Image",extension:"raf",mime:"image/x-raw",description:"",signature:{0:70,1:85,2:74,3:73,4:70,5:73,6:76,7:77,8:67,9:67,10:68,11:45,12:82,13:65,14:87},extractor:null},{name:"Minolta RAW image",extension:"mrw",mime:"image/x-raw",description:"",signature:{0:0,1:77,2:82,3:77},extractor:null},{name:"Adobe Bridge Thumbnail Cache",extension:"bct",mime:"application/octet-stream",description:"",signature:{0:108,1:110,2:98,3:116,4:2,5:0,6:0,7:0},extractor:null},{name:"Microsoft Document Imaging",extension:"mdi",mime:"image/vnd.ms-modi",description:"",signature:{0:69,1:80,2:42,3:0},extractor:null}],Video:[{name:"Matroska Multimedia Container",extension:"mkv",mime:"video/x-matroska",description:"",signature:{31:109,32:97,33:116,34:114,35:111,36:115,37:107,38:97},extractor:null},{name:"WEBM video",extension:"webm",mime:"video/webm",description:"",signature:{0:26,1:69,2:223,3:163},extractor:null},{name:"MPEG-4 video",extension:"mp4",mime:"video/mp4",description:"",signature:[{0:0,1:0,2:0,3:[24,32],4:102,5:116,6:121,7:112},{0:51,1:103,2:112,3:53},{0:0,1:0,2:0,3:28,4:102,5:116,6:121,7:112,8:109,9:112,10:52,11:50,16:109,17:112,18:52,19:49,20:109,21:112,22:52,23:50,24:105,25:115,26:111,27:109}],extractor:null},{name:"M4V video",extension:"m4v",mime:"video/x-m4v",description:"",signature:{0:0,1:0,2:0,3:28,4:102,5:116,6:121,7:112,8:77,9:52,10:86},extractor:null},{name:"Quicktime video",extension:"mov",mime:"video/quicktime",description:"",signature:{0:0,1:0,2:0,3:20,4:102,5:116,6:121,7:112},extractor:null},{name:"Audio Video Interleave",extension:"avi",mime:"video/x-msvideo",description:"",signature:{0:82,1:73,2:70,3:70,8:65,9:86,10:73},extractor:null},{name:"Windows Media Video",extension:"wmv",mime:"video/x-ms-wmv",description:"",signature:{0:48,1:38,2:178,3:117,4:142,5:102,6:207,7:17,8:166,9:217},extractor:null},{name:"MPEG video",extension:"mpg",mime:"video/mpeg",description:"",signature:{0:0,1:0,2:1,3:186},extractor:null},{name:"Flash Video",extension:"flv",mime:"video/x-flv",description:"",signature:{0:70,1:76,2:86,3:1},extractor:function(e,t){var r=new o.a(e.slice(t));r.moveForwardsBy(5);var n=r.readInt(4,"be");r.moveForwardsBy(n-9);var i=-11;for(;r.hasMore();){var a=r.readInt(4,"be"),u=r.readInt(1);if([8,9,18].indexOf(u)<0){r.moveBackwardsBy(1);break}if(a!==i+11){r.moveBackwardsBy(i+11+5);break}i=r.readInt(3,"be"),r.moveForwardsBy(7+i)}return r.carve()}}],Audio:[{name:"Waveform Audio",extension:"wav",mime:"audio/x-wav",description:"",signature:{0:82,1:73,2:70,3:70,8:87,9:65,10:86,11:69},extractor:null},{name:"OGG audio",extension:"ogg",mime:"audio/ogg",description:"",signature:{0:79,1:103,2:103,3:83},extractor:null},{name:"Musical Instrument Digital Interface audio",extension:"midi",mime:"audio/midi",description:"",signature:{0:77,1:84,2:104,3:100},extractor:null},{name:"MPEG-3 audio",extension:"mp3",mime:"audio/mpeg",description:"",signature:[{0:73,1:68,2:51},{0:255,1:251}],extractor:null},{name:"MPEG-4 Part 14 audio",extension:"m4a",mime:"audio/m4a",description:"",signature:[{4:102,5:116,6:121,7:112,8:77,9:52,10:65},{0:77,1:52,2:65,3:32}],extractor:null},{name:"Free Lossless Audio Codec",extension:"flac",mime:"audio/x-flac",description:"",signature:{0:102,1:76,2:97,3:67},extractor:null},{name:"Adaptive Multi-Rate audio codec",extension:"amr",mime:"audio/amr",description:"",signature:{0:35,1:33,2:65,3:77,4:82,5:10},extractor:null},{name:"Audacity",extension:"au",mime:"audio/x-au",description:"",signature:{0:100,1:110,2:115,3:46,24:65,25:117,26:100,27:97,28:99,29:105,30:116,31:121,32:66,33:108,34:111,35:99,36:107,37:70,38:105,39:108,40:101},extractor:null},{name:"Audacity Block",extension:"auf",mime:"application/octet-stream",description:"",signature:{0:65,1:117,2:100,3:97,4:99,5:105,6:116,7:121,8:66,9:108,10:111,11:99,12:107,13:70,14:105,15:108,16:101},extractor:null},{name:"Audio Interchange File",extension:"aif",mime:"audio/x-aiff",description:"",signature:{0:70,1:79,2:82,3:77,8:65,9:73,10:70,11:70},extractor:null},{name:"Audio Interchange File (compressed)",extension:"aifc",mime:"audio/x-aifc",description:"",signature:{0:70,1:79,2:82,3:77,8:65,9:73,10:70,11:67},extractor:null}],Documents:[{name:"Portable Document Format",extension:"pdf",mime:"application/pdf",description:"",signature:{0:37,1:80,2:68,3:70},extractor:function(e,t){var r=new o.a(e.slice(t));return r.continueUntil([37,37,69,79,70]),r.moveForwardsBy(5),r.consumeIf(13),r.consumeIf(10),r.carve()}},{name:"PostScript",extension:"ps",mime:"application/postscript",description:"",signature:{0:37,1:33},extractor:null},{name:"Rich Text Format",extension:"rtf",mime:"application/rtf",description:"",signature:{0:123,1:92,2:114,3:116,4:102},extractor:function(e,t){var r=new o.a(e.slice(t)),n=0;if(123!==r.readInt(1))throw new Error("Not a valid RTF file");n++;for(;n>0&&r.hasMore();)switch(r.readInt(1)){case 123:n++;break;case 125:n--;break;case 92:r.consumeIf(92),r.position++}return r.carve()}},{name:"Microsoft Office documents/OLE2",extension:"ole2,doc,xls,dot,ppt,xla,ppa,pps,pot,msi,sdw,db,vsd,msg",mime:"application/msword,application/vnd.ms-excel,application/vnd.ms-powerpoint",description:"Microsoft Office documents",signature:{0:208,1:207,2:17,3:224,4:161,5:177,6:26,7:225},extractor:null},{name:"Microsoft Office 2007+ documents",extension:"docx,xlsx,pptx",mime:"application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.openxmlformats-officedocument.presentationml.presentation",description:"",signature:{38:95,39:84,40:121,41:112,42:101,43:115,44:93,45:46,46:120,47:109,48:108},extractor:u},{name:"EPUB e-book",extension:"epub",mime:"application/epub+zip",description:"",signature:{0:80,1:75,2:3,3:4,30:109,31:105,32:109,33:101,34:116,35:121,36:112,37:101,38:97,39:112,40:112,41:108,42:105,43:99,44:97,45:116,46:105,47:111,48:110,49:47,50:101,51:112,52:117,53:98,54:43,55:122,56:105,57:112},extractor:u}],Applications:[{name:"Windows Portable Executable",extension:"exe,dll,drv,vxd,sys,ocx,vbx,com,fon,scr",mime:"application/x-msdownload",description:"",signature:{0:77,1:90,3:[0,1,2],5:[0,1,2]},extractor:function(e,t){var r=new o.a(e.slice(t));r.moveTo(60);var n=r.readInt(4,"le");r.moveTo(n),r.moveForwardsBy(6);var i=r.readInt(2,"le");r.moveForwardsBy(12);var a=r.readInt(2,"le");r.moveForwardsBy(2+a),r.moveForwardsBy(40*(i-1)),r.moveForwardsBy(16);var u=r.readInt(4,"le"),s=r.readInt(4,"le");return r.moveTo(s+u),r.carve()}},{name:"Executable and Linkable Format file",extension:"elf,bin,axf,o,prx,so",mime:"application/x-executable",description:"Executable and Linkable Format file. No standard file extension.",signature:{0:127,1:69,2:76,3:70},extractor:function(e,t){var r=new o.a(e.slice(t));r.moveForwardsBy(4);var n=1===r.readInt(1),i=1===r.readInt(1)?"le":"be";r.moveForwardsBy(n?26:34);var a=n?r.readInt(4,i):r.readInt(8,i);r.moveForwardsBy(10);var u=r.readInt(2,i),s=r.readInt(2,i);return r.moveTo(a),r.moveForwardsBy(u*s),r.carve()}},{name:"Adobe Flash",extension:"swf",mime:"application/x-shockwave-flash",description:"",signature:{0:[67,70],1:87,2:83},extractor:null},{name:"Java Class",extension:"class",mime:"application/java-vm",description:"",signature:{0:202,1:254,2:186,3:190},extractor:null},{name:"Dalvik Executable",extension:"dex",mime:"application/octet-stream",description:"Dalvik Executable as used by Android",signature:{0:100,1:101,2:120,3:10,4:48,5:51,6:53,7:0},extractor:null},{name:"Google Chrome Extension",extension:"crx",mime:"application/crx",description:"Google Chrome extension or packaged app",signature:{0:67,1:114,2:50,3:52},extractor:null}],Archives:[{name:"PKZIP archive",extension:"zip",mime:"application/zip",description:"",signature:{0:80,1:75,2:[3,5,7],3:[4,6,8]},extractor:u},{name:"TAR archive",extension:"tar",mime:"application/x-tar",description:"",signature:{257:117,258:115,259:116,260:97,261:114},extractor:null},{name:"Roshal Archive",extension:"rar",mime:"application/x-rar-compressed",description:"",signature:{0:82,1:97,2:114,3:33,4:26,5:7,6:[0,1]},extractor:null},{name:"Gzip",extension:"gz",mime:"application/gzip",description:"",signature:{0:31,1:139,2:8},extractor:function(e,t){var r=new o.a(e.slice(t));r.moveForwardsBy(3);var n=r.readInt(1);if(r.moveForwardsBy(4),r.readInt(1),r.moveForwardsBy(1),4&n){var i=r.readInt(2,"le");r.moveForwardsby(i)}8&n&&(r.continueUntil(0),r.moveForwardsBy(1));16&n&&(r.continueUntil(0),r.moveForwardsBy(1));2&n&&r.moveForwardsBy(2);return p(r),r.moveForwardsBy(8),r.carve()}},{name:"Bzip2",extension:"bz2",mime:"application/x-bzip2",description:"",signature:{0:66,1:90,2:104},extractor:null},{name:"7zip",extension:"7z",mime:"application/x-7z-compressed",description:"",signature:{0:55,1:122,2:188,3:175,4:39,5:28},extractor:null},{name:"Zlib Deflate",extension:"zlib",mime:"application/x-deflate",description:"",signature:{0:120,1:[1,156,218,94]},extractor:function(e,t){var r=new o.a(e.slice(t));r.moveForwardsBy(1),32&r.readInt(1)&&r.moveForwardsBy(4);return p(r),r.moveForwardsBy(4),r.carve()}},{name:"xz compression",extension:"xz",mime:"application/x-xz",description:"",signature:{0:253,1:55,2:122,3:88,4:90,5:0},extractor:null},{name:"Tarball",extension:"tar.z",mime:"application/x-gtar",description:"",signature:{0:31,1:[157,160]},extractor:null},{name:"ISO disk image",extension:"iso",mime:"application/octet-stream",description:"ISO 9660 CD/DVD image file",signature:[{32769:67,32770:68,32771:48,32772:48,32773:49},{34817:67,34818:68,34819:48,34820:48,34821:49},{36865:67,36866:68,36867:48,36868:48,36869:49}],extractor:null},{name:"Virtual Machine Disk",extension:"vmdk",mime:"application/vmdk,application/x-virtualbox-vmdk",description:"",signature:{0:75,1:68,2:77},extractor:null},{name:"ARJ Archive",extension:"arj",mime:"application/x-arj-compressed",description:"",signature:{0:96,1:234,8:[0,16,20],9:0,10:2},extractor:null},{name:"WinAce Archive",extension:"ace",mime:"application/x-ace-compressed",description:"",signature:{7:42,8:42,9:65,10:67,11:69,12:42,13:42},extractor:null},{name:"Macintosh BinHex Encoded File",extension:"hqx",mime:"application/mac-binhex",description:"",signature:{11:109,12:117,13:115,14:116,15:32,16:98,17:101,18:32,19:99,20:111,21:110,22:118,23:101,24:114,25:116,26:101,27:100,28:32,29:119,30:105,31:116,32:104,33:32,34:66,35:105,36:110,37:72,38:101,39:120},extractor:null},{name:"ALZip Archive",extension:"alz",mime:"application/octet-stream",description:"",signature:{0:65,1:76,2:90,3:1,4:10,5:0,6:0,7:0},extractor:null},{name:"KGB Compressed Archive",extension:"kgb",mime:"application/x-kgb-compressed",description:"",signature:{0:75,1:71,2:66,3:95,4:97,5:114,6:99,7:104,8:32,9:45},extractor:null}],Miscellaneous:[{name:"UTF-8 text file",extension:"txt",mime:"text/plain",description:"UTF-8 encoded Unicode byte order mark, commonly but not exclusively seen in text files.",signature:{0:239,1:187,2:191},extractor:null},{name:"UTF-32 LE file",extension:"utf32le",mime:"charset/utf32le",description:"Little-endian UTF-32 encoded Unicode byte order mark.",signature:{0:255,1:254,2:0,3:0},extractor:null},{name:"UTF-16 LE file",extension:"utf16le",mime:"charset/utf16le",description:"Little-endian UTF-16 encoded Unicode byte order mark.",signature:{0:255,1:254},extractor:null},{name:"Web Open Font Format",extension:"woff",mime:"application/font-woff",description:"",signature:{0:119,1:79,2:70,3:70,4:0,5:1,6:0,7:0},extractor:null},{name:"Web Open Font Format 2",extension:"woff2",mime:"application/font-woff",description:"",signature:{0:119,1:79,2:70,3:50,4:0,5:1,6:0,7:0},extractor:null},{name:"Embedded OpenType font",extension:"eot",mime:"application/octet-stream",description:"",signature:[{8:2,9:0,10:1,34:76,35:80},{8:1,9:0,10:0,34:76,35:80},{8:2,9:0,10:2,34:76,35:80}],extractor:null},{name:"TrueType Font",extension:"ttf",mime:"application/font-sfnt",description:"",signature:{0:0,1:1,2:0,3:0,4:0},extractor:null},{name:"OpenType Font",extension:"otf",mime:"application/font-sfnt",description:"",signature:{0:79,1:84,2:84,3:79,4:0},extractor:null},{name:"SQLite",extension:"sqlite",mime:"application/x-sqlite3",description:"",signature:{0:83,1:81,2:76,3:105},extractor:null},{name:"BitTorrent link",extension:"torrent",mime:"application/x-bittorrent",description:"",signature:[{0:100,1:56,2:58,3:97,4:110,5:110,6:111,7:117,8:110,9:99,10:101,11:35,12:35,13:58},{0:100,1:52,2:58,3:105,4:110,5:102,6:111,7:100,8:[52,53,54],9:58}],extractor:null}]};function u(e,t){var r=new o.a(e.slice(t));r.continueUntil([80,75,5,6]),r.moveForwardsBy(20);var n=r.readInt(2,"le");return r.moveForwardsBy(n),r.carve()}for(var s=new Array(288),c=0;c<s.length;c++)s[c]=c<=143?8:c<=255?9:c<=279?7:8;var l=v(s),f=v(new Array(30).fill(5)),h=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];function p(e){for(var t=0;!t;){t=e.readBits(1);var r=e.readBits(2);if(0===r){e.moveForwardsBy(1);var n=e.readInt(2,"le");e.moveForwardsBy(2+n)}else if(1===r)g(e,l,f);else{if(2!==r)throw new Error(`Invalid block type while parsing DEFLATE stream at pos ${e.position}`);for(var i=e.readBits(5)+257,o=e.readBits(5)+1,a=e.readBits(4)+4,u=new Uint8Array(h.length),s=0;s<a;s++)u[h[s]]=e.readBits(3);for(var c=v(u),p=new Uint8Array(i+o),d=void 0,m=void 0,w=void 0,b=0;b<i+o;)switch(d=y(e,c)){case 16:for(m=3+e.readBits(2);m--;)p[b++]=w;break;case 17:for(m=3+e.readBits(3);m--;)p[b++]=0;w=0;break;case 18:for(m=11+e.readBits(7);m--;)p[b++]=0;w=0;break;default:p[b++]=d,w=d}g(e,v(p.subarray(0,i)),v(p.subarray(i)))}}e.bitPos>0&&e.moveForwardsBy(1)}var d=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0],m=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13];function g(e,t,r){for(var n,i=0;(n=y(e,t))&&256!==n;){if(++i>1e4)throw new Error("Caught in probable infinite loop while parsing Huffman Block");n<256||(e.readBits(d[n-257]),n=y(e,r),e.readBits(m[n]))}}function v(e){for(var t=Math.max.apply(Math,e),r=Math.min.apply(Math,e),n=1<<t,i=new Uint32Array(n),o=1,a=0,u=2;o<=t;){for(var s=0;s<e.length;s++)if(e[s]===o){var c=void 0,l=void 0,f=void 0;for(c=0,l=a,f=0;f<o;f++)c=c<<1|1&l,l>>=1;for(var h=o<<16|s,p=c;p<n;p+=u)i[p]=h;a++}o++,a<<=1,u<<=1}return[i,t,r]}function y(e,t){var r=i()(t,2),n=r[0],o=r[1],a=n[e.readBits(o)&(1<<o)-1],u=a>>>16;if(u>o)throw new Error(`Invalid Huffman Code length while parsing DEFLATE block at pos ${e.position}: ${u}`);return e.moveBackwardsByBits(o-u),65535&a}r(0);
/**
 * File type functions
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 *
 */
function w(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(e.length){for(var n=0;n<e.length;n++)if(b(e[n],t,r))return!0;return!1}return b(e,t,r)}function b(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;for(var n in e){var i=parseInt(n,10)+r;switch(typeof e[n]){case"number":if(t[i]!==e[n])return!1;break;case"object":if(e[n].indexOf(t[i])<0)return!1;break;case"function":if(!e[n](t[i]))return!1;break;default:throw new Error(`Unrecognised signature type at offset ${n}`)}}return!0}function A(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Object.keys(a);if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),!(e&&e.length>1))return[];var r=[],n={};for(var i in a)t.includes(i)&&(n[i]=a[i]);for(var o in n){n[o].forEach(function(t){w(t.signature,e)&&r.push(t)})}return r}function F(e){return function(e,t){var r=A(t);if(!r||!r.length)return!1;if("string"==typeof e)return r.reduce(function(t,r){var n=!!r.mime.startsWith(e)&&r.mime;return t||n},!1);if(e instanceof RegExp)return r.reduce(function(t,r){var n=!!e.test(r.mime)&&r.mime;return t||n},!1);throw new Error("Invalid type input.")}("image",e)}r.d(t,"a",function(){return A}),r.d(t,"b",function(){return F})},2:function(e,t){function r(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}e.exports=function(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),e}},24:function(e,t,r){!function(e){var t,r,n,i=String.fromCharCode;function o(e){for(var t,r,n=[],i=0,o=e.length;i<o;)(t=e.charCodeAt(i++))>=55296&&t<=56319&&i<o?56320==(64512&(r=e.charCodeAt(i++)))?n.push(((1023&t)<<10)+(1023&r)+65536):(n.push(t),i--):n.push(t);return n}function a(e){if(e>=55296&&e<=57343)throw Error("Lone surrogate U+"+e.toString(16).toUpperCase()+" is not a scalar value")}function u(e,t){return i(e>>t&63|128)}function s(e){if(0==(4294967168&e))return i(e);var t="";return 0==(4294965248&e)?t=i(e>>6&31|192):0==(4294901760&e)?(a(e),t=i(e>>12&15|224),t+=u(e,6)):0==(4292870144&e)&&(t=i(e>>18&7|240),t+=u(e,12),t+=u(e,6)),t+=i(63&e|128)}function c(){if(n>=r)throw Error("Invalid byte index");var e=255&t[n];if(n++,128==(192&e))return 63&e;throw Error("Invalid continuation byte")}function l(){var e,i;if(n>r)throw Error("Invalid byte index");if(n==r)return!1;if(e=255&t[n],n++,0==(128&e))return e;if(192==(224&e)){if((i=(31&e)<<6|c())>=128)return i;throw Error("Invalid continuation byte")}if(224==(240&e)){if((i=(15&e)<<12|c()<<6|c())>=2048)return a(i),i;throw Error("Invalid continuation byte")}if(240==(248&e)&&(i=(7&e)<<18|c()<<12|c()<<6|c())>=65536&&i<=1114111)return i;throw Error("Invalid UTF-8 detected")}e.version="3.0.0",e.encode=function(e){for(var t=o(e),r=t.length,n=-1,i="";++n<r;)i+=s(t[n]);return i},e.decode=function(e){t=o(e),r=t.length,n=0;for(var a,u=[];!1!==(a=l());)u.push(a);return function(e){for(var t,r=e.length,n=-1,o="";++n<r;)(t=e[n])>65535&&(o+=i((t-=65536)>>>10&1023|55296),t=56320|1023&t),o+=i(t);return o}(u)}}(t)},25:function(e,t){e.exports=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}},26:function(e,t,r){var n,i;!function(o,a){"use strict";void 0===(i="function"==typeof(n=function(){var e=function(){},t="undefined",r=["trace","debug","info","warn","error"];function n(e,t){var r=e[t];if("function"==typeof r.bind)return r.bind(e);try{return Function.prototype.bind.call(r,e)}catch(t){return function(){return Function.prototype.apply.apply(r,[e,arguments])}}}function i(t,n){for(var i=0;i<r.length;i++){var o=r[i];this[o]=i<t?e:this.methodFactory(o,t,n)}this.log=this.debug}function o(e,r,n){return function(){typeof console!==t&&(i.call(this,r,n),this[e].apply(this,arguments))}}function a(r,i,a){return function(r){return"debug"===r&&(r="log"),typeof console!==t&&(void 0!==console[r]?n(console,r):void 0!==console.log?n(console,"log"):e)}(r)||o.apply(this,arguments)}function u(e,n,o){var u,s=this,c="loglevel";function l(){var e;if(typeof window!==t){try{e=window.localStorage[c]}catch(e){}if(typeof e===t)try{var r=window.document.cookie,n=r.indexOf(encodeURIComponent(c)+"=");-1!==n&&(e=/^([^;]+)/.exec(r.slice(n))[1])}catch(e){}return void 0===s.levels[e]&&(e=void 0),e}}e&&(c+=":"+e),s.name=e,s.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},s.methodFactory=o||a,s.getLevel=function(){return u},s.setLevel=function(n,o){if("string"==typeof n&&void 0!==s.levels[n.toUpperCase()]&&(n=s.levels[n.toUpperCase()]),!("number"==typeof n&&n>=0&&n<=s.levels.SILENT))throw"log.setLevel() called with invalid level: "+n;if(u=n,!1!==o&&function(e){var n=(r[e]||"silent").toUpperCase();if(typeof window!==t){try{return void(window.localStorage[c]=n)}catch(e){}try{window.document.cookie=encodeURIComponent(c)+"="+n+";"}catch(e){}}}(n),i.call(s,n,e),typeof console===t&&n<s.levels.SILENT)return"No console available for logging"},s.setDefaultLevel=function(e){l()||s.setLevel(e,!1)},s.enableAll=function(e){s.setLevel(s.levels.TRACE,e)},s.disableAll=function(e){s.setLevel(s.levels.SILENT,e)};var f=l();null==f&&(f=null==n?"WARN":n),s.setLevel(f,!1)}var s=new u,c={};s.getLogger=function(e){if("string"!=typeof e||""===e)throw new TypeError("You must supply a name when creating a logger.");var t=c[e];return t||(t=c[e]=new u(e,s.getLevel(),s.methodFactory)),t};var l=typeof window!==t?window.log:void 0;return s.noConflict=function(){return typeof window!==t&&window.log===s&&(window.log=l),s},s.getLoggers=function(){return c},s})?n.call(t,r,t,e):n)||(e.exports=i)}()},27:function(e,t){var r,n,i=e.exports={};function o(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function u(e){if(r===setTimeout)return setTimeout(e,0);if((r===o||!r)&&setTimeout)return r=setTimeout,setTimeout(e,0);try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:o}catch(e){r=o}try{n="function"==typeof clearTimeout?clearTimeout:a}catch(e){n=a}}();var s,c=[],l=!1,f=-1;function h(){l&&s&&(l=!1,s.length?c=s.concat(c):f=-1,c.length&&p())}function p(){if(!l){var e=u(h);l=!0;for(var t=c.length;t;){for(s=c,c=[];++f<t;)s&&s[f].run();f=-1,t=c.length}s=null,l=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===a||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(e)}}function d(e,t){this.fun=e,this.array=t}function m(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];c.push(new d(e,t)),1!==c.length||l||u(p)},d.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=m,i.addListener=m,i.once=m,i.off=m,i.removeListener=m,i.removeAllListeners=m,i.emit=m,i.prependListener=m,i.prependOnceListener=m,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},28:function(e,t){var r;r=function(){return this}();try{r=r||new Function("return this")()}catch(e){"object"==typeof window&&(r=window)}e.exports=r},29:function(e,t,r){"use strict";r.d(t,"a",function(){return i});var n=r(0);
/**
 * Binary functions.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 */function i(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Space",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:8,i=n.b.regexRep(t);e=e.replace(i,"");for(var o=[],a=0;a<e.length;a+=r)o.push(parseInt(e.substr(a,r),2));return o}},3:function(e,t){function r(t){return e.exports=r=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},r(t)}e.exports=r},30:function(e,t,r){"use strict";r.d(t,"a",function(){return i});var n=r(0);
/**
 * Decimal functions.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 */function i(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Auto";t=n.b.charRep(t);var r=[],i=e.split(t);""===i[i.length-1]&&(i=i.slice(0,i.length-1));for(var o=0;o<i.length;o++)r[o]=parseInt(i[o],10);return r}},4:function(e,t,r){var n=r(43),i=r(25);e.exports=function(e,t){return!t||"object"!==n(t)&&"function"!=typeof t?i(e):t}},43:function(e,t){function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function n(t){return"function"==typeof Symbol&&"symbol"===r(Symbol.iterator)?e.exports=n=function(e){return r(e)}:e.exports=n=function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":r(e)},n(t)}e.exports=n},44:function(e,t){function r(t,n){return e.exports=r=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},r(t,n)}e.exports=r},45:function(e,t,r){"use strict";t.byteLength=function(e){var t=c(e),r=t[0],n=t[1];return 3*(r+n)/4-n},t.toByteArray=function(e){for(var t,r=c(e),n=r[0],a=r[1],u=new o(function(e,t,r){return 3*(t+r)/4-r}(0,n,a)),s=0,l=a>0?n-4:n,f=0;f<l;f+=4)t=i[e.charCodeAt(f)]<<18|i[e.charCodeAt(f+1)]<<12|i[e.charCodeAt(f+2)]<<6|i[e.charCodeAt(f+3)],u[s++]=t>>16&255,u[s++]=t>>8&255,u[s++]=255&t;2===a&&(t=i[e.charCodeAt(f)]<<2|i[e.charCodeAt(f+1)]>>4,u[s++]=255&t);1===a&&(t=i[e.charCodeAt(f)]<<10|i[e.charCodeAt(f+1)]<<4|i[e.charCodeAt(f+2)]>>2,u[s++]=t>>8&255,u[s++]=255&t);return u},t.fromByteArray=function(e){for(var t,r=e.length,i=r%3,o=[],a=0,u=r-i;a<u;a+=16383)o.push(l(e,a,a+16383>u?u:a+16383));1===i?(t=e[r-1],o.push(n[t>>2]+n[t<<4&63]+"==")):2===i&&(t=(e[r-2]<<8)+e[r-1],o.push(n[t>>10]+n[t>>4&63]+n[t<<2&63]+"="));return o.join("")};for(var n=[],i=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",u=0,s=a.length;u<s;++u)n[u]=a[u],i[a.charCodeAt(u)]=u;function c(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");return-1===r&&(r=t),[r,r===t?0:4-r%4]}function l(e,t,r){for(var i,o,a=[],u=t;u<r;u+=3)i=(e[u]<<16&16711680)+(e[u+1]<<8&65280)+(255&e[u+2]),a.push(n[(o=i)>>18&63]+n[o>>12&63]+n[o>>6&63]+n[63&o]);return a.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},46:function(e,t){t.read=function(e,t,r,n,i){var o,a,u=8*i-n-1,s=(1<<u)-1,c=s>>1,l=-7,f=r?i-1:0,h=r?-1:1,p=e[t+f];for(f+=h,o=p&(1<<-l)-1,p>>=-l,l+=u;l>0;o=256*o+e[t+f],f+=h,l-=8);for(a=o&(1<<-l)-1,o>>=-l,l+=n;l>0;a=256*a+e[t+f],f+=h,l-=8);if(0===o)o=1-c;else{if(o===s)return a?NaN:1/0*(p?-1:1);a+=Math.pow(2,n),o-=c}return(p?-1:1)*a*Math.pow(2,o-n)},t.write=function(e,t,r,n,i,o){var a,u,s,c=8*o-i-1,l=(1<<c)-1,f=l>>1,h=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,p=n?0:o-1,d=n?1:-1,m=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(u=isNaN(t)?1:0,a=l):(a=Math.floor(Math.log(t)/Math.LN2),t*(s=Math.pow(2,-a))<1&&(a--,s*=2),(t+=a+f>=1?h/s:h*Math.pow(2,1-f))*s>=2&&(a++,s/=2),a+f>=l?(u=0,a=l):a+f>=1?(u=(t*s-1)*Math.pow(2,i),a+=f):(u=t*Math.pow(2,f-1)*Math.pow(2,i),a=0));i>=8;e[r+p]=255&u,p+=d,u/=256,i-=8);for(a=a<<i|u,c+=i;c>0;e[r+p]=255&a,p+=d,a/=256,c-=8);e[r+p-d]|=128*m}},47:function(e,t){var r={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==r.call(e)}},48:function(e,t,r){var n=function(e){"use strict";var t,r=Object.prototype,n=r.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(e,t,r,n){var i=t&&t.prototype instanceof m?t:m,o=Object.create(i.prototype),a=new C(n||[]);return o._invoke=function(e,t,r){var n=l;return function(i,o){if(n===h)throw new Error("Generator is already running");if(n===p){if("throw"===i)throw o;return R()}for(r.method=i,r.arg=o;;){var a=r.delegate;if(a){var u=x(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===l)throw n=p,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=h;var s=c(e,t,r);if("normal"===s.type){if(n=r.done?p:f,s.arg===d)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n=p,r.method="throw",r.arg=s.arg)}}}(e,r,a),o}function c(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=s;var l="suspendedStart",f="suspendedYield",h="executing",p="completed",d={};function m(){}function g(){}function v(){}var y={};y[o]=function(){return this};var w=Object.getPrototypeOf,b=w&&w(w(T([])));b&&b!==r&&n.call(b,o)&&(y=b);var A=v.prototype=m.prototype=Object.create(y);function F(e){["next","throw","return"].forEach(function(t){e[t]=function(e){return this._invoke(t,e)}})}function B(e){var t;this._invoke=function(r,i){function o(){return new Promise(function(t,o){!function t(r,i,o,a){var u=c(e[r],e,i);if("throw"!==u.type){var s=u.arg,l=s.value;return l&&"object"==typeof l&&n.call(l,"__await")?Promise.resolve(l.__await).then(function(e){t("next",e,o,a)},function(e){t("throw",e,o,a)}):Promise.resolve(l).then(function(e){s.value=e,o(s)},function(e){return t("throw",e,o,a)})}a(u.arg)}(r,i,t,o)})}return t=t?t.then(o,o):o()}}function x(e,r){var n=e.iterator[r.method];if(n===t){if(r.delegate=null,"throw"===r.method){if(e.iterator.return&&(r.method="return",r.arg=t,x(e,r),"throw"===r.method))return d;r.method="throw",r.arg=new TypeError("The iterator does not provide a 'throw' method")}return d}var i=c(n,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,d;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,d):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,d)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function k(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function T(e){if(e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,a=function r(){for(;++i<e.length;)if(n.call(e,i))return r.value=e[i],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}return{next:R}}function R(){return{value:t,done:!0}}return g.prototype=A.constructor=v,v.constructor=g,v[u]=g.displayName="GeneratorFunction",e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===g||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,v):(e.__proto__=v,u in e||(e[u]="GeneratorFunction")),e.prototype=Object.create(A),e},e.awrap=function(e){return{__await:e}},F(B.prototype),B.prototype[a]=function(){return this},e.AsyncIterator=B,e.async=function(t,r,n,i){var o=new B(s(t,r,n,i));return e.isGeneratorFunction(r)?o:o.next().then(function(e){return e.done?e.value:o.next()})},F(A),A[u]="Generator",A[o]=function(){return this},A.toString=function(){return"[object Generator]"},e.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},e.values=T,C.prototype={constructor:C,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function i(n,i){return u.type="throw",u.arg=e,r.next=n,i&&(r.method="next",r.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],u=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(s&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),k(r),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;k(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:T(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),d}},e}(e.exports);try{regeneratorRuntime=n}catch(e){Function("r","regeneratorRuntime = r")(n)}},49:function(e,t){e.exports=function(e){if(Array.isArray(e))return e}},5:function(e,t,r){var n=r(44);e.exports=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&n(e,t)}},50:function(e,t){e.exports=function(e,t){var r=[],n=!0,i=!1,o=void 0;try{for(var a,u=e[Symbol.iterator]();!(n=(a=u.next()).done)&&(r.push(a.value),!t||r.length!==t);n=!0);}catch(e){i=!0,o=e}finally{try{n||null==u.return||u.return()}finally{if(i)throw o}}return r}},51:function(e,t){e.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}},6:function(e,t,r){"use strict";var n=r(1),i=r.n(n),o=r(2),a=r.n(o),u=r(15),s=r(0),c=r(17),l=function(){function e(t){i()(this,e),this.name="",this.type="",this._value=null,this.disabled=!1,this.hint="",this.rows=0,this.toggleValues=[],this.target=null,this.defaultIndex=0,this.min=null,this.max=null,this.step=1,t&&this._parseConfig(t)}return a()(e,[{key:"_parseConfig",value:function(e){this.name=e.name,this.type=e.type,this.defaultValue=e.value,this.disabled=!!e.disabled,this.hint=e.hint||!1,this.rows=e.rows||!1,this.toggleValues=e.toggleValues,this.target=void 0!==e.target?e.target:null,this.defaultIndex=void 0!==e.defaultIndex?e.defaultIndex:0,this.min=e.min,this.max=e.max,this.step=e.step}},{key:"config",get:function(){return this._value}},{key:"value",set:function(t){this._value=e.prepare(t,this.type)},get:function(){return this._value}}],[{key:"prepare",value:function(e,t){var r;switch(t){case"binaryString":case"binaryShortString":case"editableOption":case"editableOptionShort":return s.b.parseEscapedChars(e);case"byteArray":return"string"==typeof e?(e=e.replace(/\s+/g,""),Object(c.a)(e)):e;case"number":if(r=parseFloat(e),isNaN(r))throw"Invalid ingredient value. Not a number: "+s.b.truncate(e.toString(),10);return r;default:return e}}}]),e}(),f=function(){function e(){i()(this,e),this._inputType=-1,this._outputType=-1,this._presentType=-1,this._breakpoint=!1,this._disabled=!1,this._flowControl=!1,this._manualBake=!1,this._ingList=[],this.name="",this.module="",this.description="",this.infoURL=null}return a()(e,[{key:"run",value:function(e,t){return e}},{key:"highlight",value:function(e,t){return!1}},{key:"highlightReverse",value:function(e,t){return!1}},{key:"present",value:function(e,t){return e}},{key:"addIngredient",value:function(e){this._ingList.push(e)}},{key:"inputType",set:function(e){this._inputType=u.a.typeEnum(e)},get:function(){return u.a.enumLookup(this._inputType)}},{key:"outputType",set:function(e){this._outputType=u.a.typeEnum(e),this._presentType<0&&(this._presentType=this._outputType)},get:function(){return u.a.enumLookup(this._outputType)}},{key:"presentType",set:function(e){this._presentType=u.a.typeEnum(e)},get:function(){return u.a.enumLookup(this._presentType)}},{key:"args",set:function(e){var t=this;e.forEach(function(e){var r=new l(e);t.addIngredient(r)})},get:function(){return this._ingList.map(function(e){var t={name:e.name,type:e.type,value:e.defaultValue};return e.toggleValues&&(t.toggleValues=e.toggleValues),e.hint&&(t.hint=e.hint),e.rows&&(t.rows=e.rows),e.disabled&&(t.disabled=e.disabled),e.target&&(t.target=e.target),e.defaultIndex&&(t.defaultIndex=e.defaultIndex),"number"==typeof e.min&&(t.min=e.min),"number"==typeof e.max&&(t.max=e.max),e.step&&(t.step=e.step),t})}},{key:"config",get:function(){return{op:this.name,args:this._ingList.map(function(e){return e.config})}}},{key:"ingValues",set:function(e){var t=this;e.forEach(function(e,r){t._ingList[r].value=e})},get:function(){return this._ingList.map(function(e){return e.value})}},{key:"breakpoint",set:function(e){this._breakpoint=!!e},get:function(){return this._breakpoint}},{key:"disabled",set:function(e){this._disabled=!!e},get:function(){return this._disabled}},{key:"flowControl",get:function(){return this._flowControl},set:function(e){this._flowControl=!!e}},{key:"manualBake",get:function(){return this._manualBake},set:function(e){this._manualBake=!!e}}]),e}();t.a=f},7:function(e,t,r){e.exports=r(48)},713:function(e,t,r){var n;
/*!
 * UAParser.js v0.7.20
 * Lightweight JavaScript-based User-Agent string parser
 * https://github.com/faisalman/ua-parser-js
 *
 * Copyright © 2012-2019 Faisal Salman <<EMAIL>>
 * Licensed under MIT License
 */!function(i,o){"use strict";var a="model",u="name",s="type",c="vendor",l="version",f="mobile",h="tablet",p={extend:function(e,t){var r={};for(var n in e)t[n]&&t[n].length%2==0?r[n]=t[n].concat(e[n]):r[n]=e[n];return r},has:function(e,t){return"string"==typeof e&&-1!==t.toLowerCase().indexOf(e.toLowerCase())},lowerize:function(e){return e.toLowerCase()},major:function(e){return"string"==typeof e?e.replace(/[^\d\.]/g,"").split(".")[0]:void 0},trim:function(e){return e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}},d={rgx:function(e,t){for(var r,n,i,o,a,u,s=0;s<t.length&&!a;){var c=t[s],l=t[s+1];for(r=n=0;r<c.length&&!a;)if(a=c[r++].exec(e))for(i=0;i<l.length;i++)u=a[++n],"object"==typeof(o=l[i])&&o.length>0?2==o.length?"function"==typeof o[1]?this[o[0]]=o[1].call(this,u):this[o[0]]=o[1]:3==o.length?"function"!=typeof o[1]||o[1].exec&&o[1].test?this[o[0]]=u?u.replace(o[1],o[2]):void 0:this[o[0]]=u?o[1].call(this,u,o[2]):void 0:4==o.length&&(this[o[0]]=u?o[3].call(this,u.replace(o[1],o[2])):void 0):this[o]=u||void 0;s+=2}},str:function(e,t){for(var r in t)if("object"==typeof t[r]&&t[r].length>0){for(var n=0;n<t[r].length;n++)if(p.has(t[r][n],e))return"?"===r?void 0:r}else if(p.has(t[r],e))return"?"===r?void 0:r;return e}},m={browser:{oldsafari:{version:{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}}},device:{amazon:{model:{"Fire Phone":["SD","KF"]}},sprint:{model:{"Evo Shift 4G":"7373KT"},vendor:{HTC:"APA",Sprint:"Sprint"}}},os:{windows:{version:{ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"}}}},g={browser:[[/(opera\smini)\/([\w\.-]+)/i,/(opera\s[mobiletab]+).+version\/([\w\.-]+)/i,/(opera).+version\/([\w\.]+)/i,/(opera)[\/\s]+([\w\.]+)/i],[u,l],[/(opios)[\/\s]+([\w\.]+)/i],[[u,"Opera Mini"],l],[/\s(opr)\/([\w\.]+)/i],[[u,"Opera"],l],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/\s]?([\w\.]*)/i,/(avant\s|iemobile|slim|baidu)(?:browser)?[\/\s]?([\w\.]*)/i,/(?:ms|\()(ie)\s([\w\.]+)/i,/(rekonq)\/([\w\.]*)/i,/(chromium|flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon)\/([\w\.-]+)/i],[u,l],[/(konqueror)\/([\w\.]+)/i],[[u,"Konqueror"],l],[/(trident).+rv[:\s]([\w\.]+).+like\sgecko/i],[[u,"IE"],l],[/(edge|edgios|edga|edg)\/((\d+)?[\w\.]+)/i],[[u,"Edge"],l],[/(yabrowser)\/([\w\.]+)/i],[[u,"Yandex"],l],[/(puffin)\/([\w\.]+)/i],[[u,"Puffin"],l],[/(focus)\/([\w\.]+)/i],[[u,"Firefox Focus"],l],[/(opt)\/([\w\.]+)/i],[[u,"Opera Touch"],l],[/((?:[\s\/])uc?\s?browser|(?:juc.+)ucweb)[\/\s]?([\w\.]+)/i],[[u,"UCBrowser"],l],[/(comodo_dragon)\/([\w\.]+)/i],[[u,/_/g," "],l],[/(windowswechat qbcore)\/([\w\.]+)/i],[[u,"WeChat(Win) Desktop"],l],[/(micromessenger)\/([\w\.]+)/i],[[u,"WeChat"],l],[/(brave)\/([\w\.]+)/i],[[u,"Brave"],l],[/(qqbrowserlite)\/([\w\.]+)/i],[u,l],[/(QQ)\/([\d\.]+)/i],[u,l],[/m?(qqbrowser)[\/\s]?([\w\.]+)/i],[u,l],[/(BIDUBrowser)[\/\s]?([\w\.]+)/i],[u,l],[/(2345Explorer)[\/\s]?([\w\.]+)/i],[u,l],[/(MetaSr)[\/\s]?([\w\.]+)/i],[u],[/(LBBROWSER)/i],[u],[/xiaomi\/miuibrowser\/([\w\.]+)/i],[l,[u,"MIUI Browser"]],[/;fbav\/([\w\.]+);/i],[l,[u,"Facebook"]],[/safari\s(line)\/([\w\.]+)/i,/android.+(line)\/([\w\.]+)\/iab/i],[u,l],[/headlesschrome(?:\/([\w\.]+)|\s)/i],[l,[u,"Chrome Headless"]],[/\swv\).+(chrome)\/([\w\.]+)/i],[[u,/(.+)/,"$1 WebView"],l],[/((?:oculus|samsung)browser)\/([\w\.]+)/i],[[u,/(.+(?:g|us))(.+)/,"$1 $2"],l],[/android.+version\/([\w\.]+)\s+(?:mobile\s?safari|safari)*/i],[l,[u,"Android Browser"]],[/(sailfishbrowser)\/([\w\.]+)/i],[[u,"Sailfish Browser"],l],[/(chrome|omniweb|arora|[tizenoka]{5}\s?browser)\/v?([\w\.]+)/i],[u,l],[/(dolfin)\/([\w\.]+)/i],[[u,"Dolphin"],l],[/((?:android.+)crmo|crios)\/([\w\.]+)/i],[[u,"Chrome"],l],[/(coast)\/([\w\.]+)/i],[[u,"Opera Coast"],l],[/fxios\/([\w\.-]+)/i],[l,[u,"Firefox"]],[/version\/([\w\.]+).+?mobile\/\w+\s(safari)/i],[l,[u,"Mobile Safari"]],[/version\/([\w\.]+).+?(mobile\s?safari|safari)/i],[l,u],[/webkit.+?(gsa)\/([\w\.]+).+?(mobile\s?safari|safari)(\/[\w\.]+)/i],[[u,"GSA"],l],[/webkit.+?(mobile\s?safari|safari)(\/[\w\.]+)/i],[u,[l,d.str,m.browser.oldsafari.version]],[/(webkit|khtml)\/([\w\.]+)/i],[u,l],[/(navigator|netscape)\/([\w\.-]+)/i],[[u,"Netscape"],l],[/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo\sbrowser|minimo|conkeror)[\/\s]?([\w\.\+]+)/i,/(firefox|seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([\w\.-]+)$/i,/(mozilla)\/([\w\.]+).+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir)[\/\s]?([\w\.]+)/i,/(links)\s\(([\w\.]+)/i,/(gobrowser)\/?([\w\.]*)/i,/(ice\s?browser)\/v?([\w\._]+)/i,/(mosaic)[\/\s]([\w\.]+)/i],[u,l]],cpu:[[/(?:(amd|x(?:(?:86|64)[_-])?|wow|win)64)[;\)]/i],[["architecture","amd64"]],[/(ia32(?=;))/i],[["architecture",p.lowerize]],[/((?:i[346]|x)86)[;\)]/i],[["architecture","ia32"]],[/windows\s(ce|mobile);\sppc;/i],[["architecture","arm"]],[/((?:ppc|powerpc)(?:64)?)(?:\smac|;|\))/i],[["architecture",/ower/,"",p.lowerize]],[/(sun4\w)[;\)]/i],[["architecture","sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|arm(?:64|(?=v\d+[;l]))|(?=atmel\s)avr|(?:irix|mips|sparc)(?:64)?(?=;)|pa-risc)/i],[["architecture",p.lowerize]]],device:[[/\((ipad|playbook);[\w\s\),;-]+(rim|apple)/i],[a,c,[s,h]],[/applecoremedia\/[\w\.]+ \((ipad)/],[a,[c,"Apple"],[s,h]],[/(apple\s{0,1}tv)/i],[[a,"Apple TV"],[c,"Apple"]],[/(archos)\s(gamepad2?)/i,/(hp).+(touchpad)/i,/(hp).+(tablet)/i,/(kindle)\/([\w\.]+)/i,/\s(nook)[\w\s]+build\/(\w+)/i,/(dell)\s(strea[kpr\s\d]*[\dko])/i],[c,a,[s,h]],[/(kf[A-z]+)\sbuild\/.+silk\//i],[a,[c,"Amazon"],[s,h]],[/(sd|kf)[0349hijorstuw]+\sbuild\/.+silk\//i],[[a,d.str,m.device.amazon.model],[c,"Amazon"],[s,f]],[/android.+aft([bms])\sbuild/i],[a,[c,"Amazon"],[s,"smarttv"]],[/\((ip[honed|\s\w*]+);.+(apple)/i],[a,c,[s,f]],[/\((ip[honed|\s\w*]+);/i],[a,[c,"Apple"],[s,f]],[/(blackberry)[\s-]?(\w+)/i,/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[\s_-]?([\w-]*)/i,/(hp)\s([\w\s]+\w)/i,/(asus)-?(\w+)/i],[c,a,[s,f]],[/\(bb10;\s(\w+)/i],[a,[c,"BlackBerry"],[s,f]],[/android.+(transfo[prime\s]{4,10}\s\w+|eeepc|slider\s\w+|nexus 7|padfone|p00c)/i],[a,[c,"Asus"],[s,h]],[/(sony)\s(tablet\s[ps])\sbuild\//i,/(sony)?(?:sgp.+)\sbuild\//i],[[c,"Sony"],[a,"Xperia Tablet"],[s,h]],[/android.+\s([c-g]\d{4}|so[-l]\w+)(?=\sbuild\/|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[a,[c,"Sony"],[s,f]],[/\s(ouya)\s/i,/(nintendo)\s([wids3u]+)/i],[c,a,[s,"console"]],[/android.+;\s(shield)\sbuild/i],[a,[c,"Nvidia"],[s,"console"]],[/(playstation\s[34portablevi]+)/i],[a,[c,"Sony"],[s,"console"]],[/(sprint\s(\w+))/i],[[c,d.str,m.device.sprint.vendor],[a,d.str,m.device.sprint.model],[s,f]],[/(htc)[;_\s-]+([\w\s]+(?=\)|\sbuild)|\w+)/i,/(zte)-(\w*)/i,/(alcatel|geeksphone|nexian|panasonic|(?=;\s)sony)[_\s-]?([\w-]*)/i],[c,[a,/_/g," "],[s,f]],[/(nexus\s9)/i],[a,[c,"HTC"],[s,h]],[/d\/huawei([\w\s-]+)[;\)]/i,/(nexus\s6p)/i],[a,[c,"Huawei"],[s,f]],[/(microsoft);\s(lumia[\s\w]+)/i],[c,a,[s,f]],[/[\s\(;](xbox(?:\sone)?)[\s\);]/i],[a,[c,"Microsoft"],[s,"console"]],[/(kin\.[onetw]{3})/i],[[a,/\./g," "],[c,"Microsoft"],[s,f]],[/\s(milestone|droid(?:[2-4x]|\s(?:bionic|x2|pro|razr))?:?(\s4g)?)[\w\s]+build\//i,/mot[\s-]?(\w*)/i,/(XT\d{3,4}) build\//i,/(nexus\s6)/i],[a,[c,"Motorola"],[s,f]],[/android.+\s(mz60\d|xoom[\s2]{0,2})\sbuild\//i],[a,[c,"Motorola"],[s,h]],[/hbbtv\/\d+\.\d+\.\d+\s+\([\w\s]*;\s*(\w[^;]*);([^;]*)/i],[[c,p.trim],[a,p.trim],[s,"smarttv"]],[/hbbtv.+maple;(\d+)/i],[[a,/^/,"SmartTV"],[c,"Samsung"],[s,"smarttv"]],[/\(dtv[\);].+(aquos)/i],[a,[c,"Sharp"],[s,"smarttv"]],[/android.+((sch-i[89]0\d|shw-m380s|gt-p\d{4}|gt-n\d+|sgh-t8[56]9|nexus 10))/i,/((SM-T\w+))/i],[[c,"Samsung"],a,[s,h]],[/smart-tv.+(samsung)/i],[c,[s,"smarttv"],a],[/((s[cgp]h-\w+|gt-\w+|galaxy\snexus|sm-\w[\w\d]+))/i,/(sam[sung]*)[\s-]*(\w+-?[\w-]*)/i,/sec-((sgh\w+))/i],[[c,"Samsung"],a,[s,f]],[/sie-(\w*)/i],[a,[c,"Siemens"],[s,f]],[/(maemo|nokia).*(n900|lumia\s\d+)/i,/(nokia)[\s_-]?([\w-]*)/i],[[c,"Nokia"],a,[s,f]],[/android[x\d\.\s;]+\s([ab][1-7]\-?[0178a]\d\d?)/i],[a,[c,"Acer"],[s,h]],[/android.+([vl]k\-?\d{3})\s+build/i],[a,[c,"LG"],[s,h]],[/android\s3\.[\s\w;-]{10}(lg?)-([06cv9]{3,4})/i],[[c,"LG"],a,[s,h]],[/(lg) netcast\.tv/i],[c,a,[s,"smarttv"]],[/(nexus\s[45])/i,/lg[e;\s\/-]+(\w*)/i,/android.+lg(\-?[\d\w]+)\s+build/i],[a,[c,"LG"],[s,f]],[/(lenovo)\s?(s(?:5000|6000)(?:[\w-]+)|tab(?:[\s\w]+))/i],[c,a,[s,h]],[/android.+(ideatab[a-z0-9\-\s]+)/i],[a,[c,"Lenovo"],[s,h]],[/(lenovo)[_\s-]?([\w-]+)/i],[c,a,[s,f]],[/linux;.+((jolla));/i],[c,a,[s,f]],[/((pebble))app\/[\d\.]+\s/i],[c,a,[s,"wearable"]],[/android.+;\s(oppo)\s?([\w\s]+)\sbuild/i],[c,a,[s,f]],[/crkey/i],[[a,"Chromecast"],[c,"Google"]],[/android.+;\s(glass)\s\d/i],[a,[c,"Google"],[s,"wearable"]],[/android.+;\s(pixel c)[\s)]/i],[a,[c,"Google"],[s,h]],[/android.+;\s(pixel( [23])?( xl)?)[\s)]/i],[a,[c,"Google"],[s,f]],[/android.+;\s(\w+)\s+build\/hm\1/i,/android.+(hm[\s\-_]*note?[\s_]*(?:\d\w)?)\s+build/i,/android.+(mi[\s\-_]*(?:a\d|one|one[\s_]plus|note lte)?[\s_]*(?:\d?\w?)[\s_]*(?:plus)?)\s+build/i,/android.+(redmi[\s\-_]*(?:note)?(?:[\s_]*[\w\s]+))\s+build/i],[[a,/_/g," "],[c,"Xiaomi"],[s,f]],[/android.+(mi[\s\-_]*(?:pad)(?:[\s_]*[\w\s]+))\s+build/i],[[a,/_/g," "],[c,"Xiaomi"],[s,h]],[/android.+;\s(m[1-5]\snote)\sbuild/i],[a,[c,"Meizu"],[s,f]],[/(mz)-([\w-]{2,})/i],[[c,"Meizu"],a,[s,f]],[/android.+a000(1)\s+build/i,/android.+oneplus\s(a\d{4})\s+build/i],[a,[c,"OnePlus"],[s,f]],[/android.+[;\/]\s*(RCT[\d\w]+)\s+build/i],[a,[c,"RCA"],[s,h]],[/android.+[;\/\s]+(Venue[\d\s]{2,7})\s+build/i],[a,[c,"Dell"],[s,h]],[/android.+[;\/]\s*(Q[T|M][\d\w]+)\s+build/i],[a,[c,"Verizon"],[s,h]],[/android.+[;\/]\s+(Barnes[&\s]+Noble\s+|BN[RT])(V?.*)\s+build/i],[[c,"Barnes & Noble"],a,[s,h]],[/android.+[;\/]\s+(TM\d{3}.*\b)\s+build/i],[a,[c,"NuVision"],[s,h]],[/android.+;\s(k88)\sbuild/i],[a,[c,"ZTE"],[s,h]],[/android.+[;\/]\s*(gen\d{3})\s+build.*49h/i],[a,[c,"Swiss"],[s,f]],[/android.+[;\/]\s*(zur\d{3})\s+build/i],[a,[c,"Swiss"],[s,h]],[/android.+[;\/]\s*((Zeki)?TB.*\b)\s+build/i],[a,[c,"Zeki"],[s,h]],[/(android).+[;\/]\s+([YR]\d{2})\s+build/i,/android.+[;\/]\s+(Dragon[\-\s]+Touch\s+|DT)(\w{5})\sbuild/i],[[c,"Dragon Touch"],a,[s,h]],[/android.+[;\/]\s*(NS-?\w{0,9})\sbuild/i],[a,[c,"Insignia"],[s,h]],[/android.+[;\/]\s*((NX|Next)-?\w{0,9})\s+build/i],[a,[c,"NextBook"],[s,h]],[/android.+[;\/]\s*(Xtreme\_)?(V(1[045]|2[015]|30|40|60|7[05]|90))\s+build/i],[[c,"Voice"],a,[s,f]],[/android.+[;\/]\s*(LVTEL\-)?(V1[12])\s+build/i],[[c,"LvTel"],a,[s,f]],[/android.+;\s(PH-1)\s/i],[a,[c,"Essential"],[s,f]],[/android.+[;\/]\s*(V(100MD|700NA|7011|917G).*\b)\s+build/i],[a,[c,"Envizen"],[s,h]],[/android.+[;\/]\s*(Le[\s\-]+Pan)[\s\-]+(\w{1,9})\s+build/i],[c,a,[s,h]],[/android.+[;\/]\s*(Trio[\s\-]*.*)\s+build/i],[a,[c,"MachSpeed"],[s,h]],[/android.+[;\/]\s*(Trinity)[\-\s]*(T\d{3})\s+build/i],[c,a,[s,h]],[/android.+[;\/]\s*TU_(1491)\s+build/i],[a,[c,"Rotor"],[s,h]],[/android.+(KS(.+))\s+build/i],[a,[c,"Amazon"],[s,h]],[/android.+(Gigaset)[\s\-]+(Q\w{1,9})\s+build/i],[c,a,[s,h]],[/\s(tablet|tab)[;\/]/i,/\s(mobile)(?:[;\/]|\ssafari)/i],[[s,p.lowerize],c,a],[/[\s\/\(](smart-?tv)[;\)]/i],[[s,"smarttv"]],[/(android[\w\.\s\-]{0,9});.+build/i],[a,[c,"Generic"]]],engine:[[/windows.+\sedge\/([\w\.]+)/i],[l,[u,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)/i],[[u,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/(khtml|tasman|links)[\/\s]\(?([\w\.]+)/i,/(icab)[\/\s]([23]\.[\d\.]+)/i],[u,l],[/rv\:([\w\.]{1,9}).+(gecko)/i],[l,u]],os:[[/microsoft\s(windows)\s(vista|xp)/i],[u,l],[/(windows)\snt\s6\.2;\s(arm)/i,/(windows\sphone(?:\sos)*)[\s\/]?([\d\.\s\w]*)/i,/(windows\smobile|windows)[\s\/]?([ntce\d\.\s]+\w)/i],[u,[l,d.str,m.os.windows.version]],[/(win(?=3|9|n)|win\s9x\s)([nt\d\.]+)/i],[[u,"Windows"],[l,d.str,m.os.windows.version]],[/\((bb)(10);/i],[[u,"BlackBerry"],l],[/(blackberry)\w*\/?([\w\.]*)/i,/(tizen)[\/\s]([\w\.]+)/i,/(android|webos|palm\sos|qnx|bada|rim\stablet\sos|meego|sailfish|contiki)[\/\s-]?([\w\.]*)/i],[u,l],[/(symbian\s?os|symbos|s60(?=;))[\/\s-]?([\w\.]*)/i],[[u,"Symbian"],l],[/\((series40);/i],[u],[/mozilla.+\(mobile;.+gecko.+firefox/i],[[u,"Firefox OS"],l],[/(nintendo|playstation)\s([wids34portablevu]+)/i,/(mint)[\/\s\(]?(\w*)/i,/(mageia|vectorlinux)[;\s]/i,/(joli|[kxln]?ubuntu|debian|suse|opensuse|gentoo|(?=\s)arch|slackware|fedora|mandriva|centos|pclinuxos|redhat|zenwalk|linpus)[\/\s-]?(?!chrom)([\w\.-]*)/i,/(hurd|linux)\s?([\w\.]*)/i,/(gnu)\s?([\w\.]*)/i],[u,l],[/(cros)\s[\w]+\s([\w\.]+\w)/i],[[u,"Chromium OS"],l],[/(sunos)\s?([\w\.\d]*)/i],[[u,"Solaris"],l],[/\s([frentopc-]{0,4}bsd|dragonfly)\s?([\w\.]*)/i],[u,l],[/(haiku)\s(\w+)/i],[u,l],[/cfnetwork\/.+darwin/i,/ip[honead]{2,4}(?:.*os\s([\w]+)\slike\smac|;\sopera)/i],[[l,/_/g,"."],[u,"iOS"]],[/(mac\sos\sx)\s?([\w\s\.]*)/i,/(macintosh|mac(?=_powerpc)\s)/i],[[u,"Mac OS"],[l,/_/g,"."]],[/((?:open)?solaris)[\/\s-]?([\w\.]*)/i,/(aix)\s((\d)(?=\.|\)|\s)[\w\.])*/i,/(plan\s9|minix|beos|os\/2|amigaos|morphos|risc\sos|openvms|fuchsia)/i,/(unix)\s?([\w\.]*)/i],[u,l]]},v=function(e,t){if("object"==typeof e&&(t=e,e=void 0),!(this instanceof v))return new v(e,t).getResult();var r=e||(i&&i.navigator&&i.navigator.userAgent?i.navigator.userAgent:""),n=t?p.extend(g,t):g;return this.getBrowser=function(){var e={name:void 0,version:void 0};return d.rgx.call(e,r,n.browser),e.major=p.major(e.version),e},this.getCPU=function(){var e={architecture:void 0};return d.rgx.call(e,r,n.cpu),e},this.getDevice=function(){var e={vendor:void 0,model:void 0,type:void 0};return d.rgx.call(e,r,n.device),e},this.getEngine=function(){var e={name:void 0,version:void 0};return d.rgx.call(e,r,n.engine),e},this.getOS=function(){var e={name:void 0,version:void 0};return d.rgx.call(e,r,n.os),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return r},this.setUA=function(e){return r=e,this},this};v.VERSION="0.7.20",v.BROWSER={NAME:u,MAJOR:"major",VERSION:l},v.CPU={ARCHITECTURE:"architecture"},v.DEVICE={MODEL:a,VENDOR:c,TYPE:s,CONSOLE:"console",MOBILE:f,SMARTTV:"smarttv",TABLET:h,WEARABLE:"wearable",EMBEDDED:"embedded"},v.ENGINE={NAME:u,VERSION:l},v.OS={NAME:u,VERSION:l},void 0!==t?(void 0!==e&&e.exports&&(t=e.exports=v),t.UAParser=v):void 0===(n=function(){return v}.call(t,r,t,e))||(e.exports=n);var y=i&&(i.jQuery||i.Zepto);if(void 0!==y&&!y.ua){var w=new v;y.ua=w.getResult(),y.ua.get=function(){return w.getUA()},y.ua.set=function(e){w.setUA(e);var t=w.getResult();for(var r in t)y.ua[r]=t[r]}}}("object"==typeof window?window:this)}});