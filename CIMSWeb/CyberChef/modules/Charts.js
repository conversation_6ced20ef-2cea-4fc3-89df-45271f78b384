/**
 * CyberChef - The Cyber Swiss Army Knife
 *
 * @copyright Crown Copyright 2016
 * @license Apache-2.0
 *
 *   Copyright 2016 Crown Copyright
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */!function(t){var n={};function e(r){if(n[r])return n[r].exports;var i=n[r]={i:r,l:!1,exports:{}};return t[r].call(i.exports,i,i.exports,e),i.l=!0,i.exports}e.m=t,e.c=n,e.d=function(t,n,r){e.o(t,n)||Object.defineProperty(t,n,{enumerable:!0,get:r})},e.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},e.t=function(t,n){if(1&n&&(t=e(t)),8&n)return t;if(4&n&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(e.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&n&&"string"!=typeof t)for(var i in t)e.d(r,i,function(n){return t[n]}.bind(null,i));return r},e.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,"a",n),n},e.o=function(t,n){return Object.prototype.hasOwnProperty.call(t,n)},e.p="",e(e.s=1333)}({0:function(t,n,e){"use strict";(function(t,r){e.d(n,"c",function(){return b}),e.d(n,"d",function(){return x}),e.d(n,"e",function(){return w}),e.d(n,"a",function(){return E});var i=e(7),o=e.n(i),u=e(11),a=e.n(u),c=e(1),f=e.n(c),s=e(2),l=e.n(s),h=e(24),d=e.n(h),p=e(14),v=e(17),g=e(30),y=e(29),m=function(){function n(){f()(this,n)}var e;return l()(n,null,[{key:"chr",value:function(t){if(t>65535){t-=65536;var n=String.fromCharCode(t>>>10&1023|55296);return t=56320|1023&t,n+String.fromCharCode(t)}return String.fromCharCode(t)}},{key:"ord",value:function(t){if(2===t.length){var n=t.charCodeAt(0),e=t.charCodeAt(1);if(n>=55296&&n<56320&&e>=56320&&e<57344)return 1024*(n-55296)+e-56320+65536}return t.charCodeAt(0)}},{key:"padBytesRight",value:function(t,n){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=new Array(n);return r.fill(e),[...t].forEach(function(t,n){r[n]=t}),r}},{key:"truncate",value:function(t,n){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"...";return t.length>n&&(t=t.slice(0,n-e.length)+e),t}},{key:"hex",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return(t="string"==typeof t?n.ord(t):t).toString(16).padStart(e,"0")}},{key:"bin",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:8;return(t="string"==typeof t?n.ord(t):t).toString(2).padStart(e,"0")}},{key:"printable",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];_()&&window.app&&!window.app.options.treatAsUtf8&&(t=n.byteArrayToChars(n.strToByteArray(t)));var r=/[\0-\x08\x0B-\x0C\x0E-\x1F\x7F-\x9F\xAD\u0378\u0379\u037F-\u0383\u038B\u038D\u03A2\u0528-\u0530\u0557\u0558\u0560\u0588\u058B-\u058E\u0590\u05C8-\u05CF\u05EB-\u05EF\u05F5-\u0605\u061C\u061D\u06DD\u070E\u070F\u074B\u074C\u07B2-\u07BF\u07FB-\u07FF\u082E\u082F\u083F\u085C\u085D\u085F-\u089F\u08A1\u08AD-\u08E3\u08FF\u0978\u0980\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09FC-\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF2-\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B55\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B78-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BFB-\u0C00\u0C04\u0C0D\u0C11\u0C29\u0C34\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5A-\u0C5F\u0C64\u0C65\u0C70-\u0C77\u0C80\u0C81\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0D01\u0D04\u0D0D\u0D11\u0D3B\u0D3C\u0D45\u0D49\u0D4F-\u0D56\u0D58-\u0D5F\u0D64\u0D65\u0D76-\u0D78\u0D80\u0D81\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DF1\u0DF5-\u0E00\u0E3B-\u0E3E\u0E5C-\u0E80\u0E83\u0E85\u0E86\u0E89\u0E8B\u0E8C\u0E8E-\u0E93\u0E98\u0EA0\u0EA4\u0EA6\u0EA8\u0EA9\u0EAC\u0EBA\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F48\u0F6D-\u0F70\u0F98\u0FBD\u0FCD\u0FDB-\u0FFF\u10C6\u10C8-\u10CC\u10CE\u10CF\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u137D-\u137F\u139A-\u139F\u13F5-\u13FF\u169D-\u169F\u16F1-\u16FF\u170D\u1715-\u171F\u1737-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17DE\u17DF\u17EA-\u17EF\u17FA-\u17FF\u180F\u181A-\u181F\u1878-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191D-\u191F\u192C-\u192F\u193C-\u193F\u1941-\u1943\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DB-\u19DD\u1A1C\u1A1D\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1A9F\u1AAE-\u1AFF\u1B4C-\u1B4F\u1B7D-\u1B7F\u1BF4-\u1BFB\u1C38-\u1C3A\u1C4A-\u1C4C\u1C80-\u1CBF\u1CC8-\u1CCF\u1CF7-\u1CFF\u1DE7-\u1DFB\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FC5\u1FD4\u1FD5\u1FDC\u1FF0\u1FF1\u1FF5\u1FFF\u200B-\u200F\u202A-\u202E\u2060-\u206F\u2072\u2073\u208F\u209D-\u209F\u20BB-\u20CF\u20F1-\u20FF\u218A-\u218F\u23F4-\u23FF\u2427-\u243F\u244B-\u245F\u2700\u2B4D-\u2B4F\u2B5A-\u2BFF\u2C2F\u2C5F\u2CF4-\u2CF8\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D71-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E3C-\u2E7F\u2E9A\u2EF4-\u2EFF\u2FD6-\u2FEF\u2FFC-\u2FFF\u3040\u3097\u3098\u3100-\u3104\u312E-\u3130\u318F\u31BB-\u31BF\u31E4-\u31EF\u321F\u32FF\u4DB6-\u4DBF\u9FCD-\u9FFF\uA48D-\uA48F\uA4C7-\uA4CF\uA62C-\uA63F\uA698-\uA69E\uA6F8-\uA6FF\uA78F\uA794-\uA79F\uA7AB-\uA7F7\uA82C-\uA82F\uA83A-\uA83F\uA878-\uA87F\uA8C5-\uA8CD\uA8DA-\uA8DF\uA8FC-\uA8FF\uA954-\uA95E\uA97D-\uA97F\uA9CE\uA9DA-\uA9DD\uA9E0-\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A\uAA5B\uAA7C-\uAA7F\uAAC3-\uAADA\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F-\uABBF\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBC2-\uFBD2\uFD40-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFE\uFDFF\uFE1A-\uFE1F\uFE27-\uFE2F\uFE53\uFE67\uFE6C-\uFE6F\uFE75\uFEFD-\uFF00\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFDF\uFFE7\uFFEF-\uFFFB\uFFFE\uFFFF]/g,i=/[\x09-\x10\x0D\u2028\u2029]/g;return t=t.replace(r,"."),e||(t=t.replace(i,".")),t}},{key:"parseEscapedChars",value:function(t){return t.replace(/(\\)?\\([bfnrtv'"]|[0-3][0-7]{2}|[0-7]{1,2}|x[\da-fA-F]{2}|u[\da-fA-F]{4}|u\{[\da-fA-F]{1,6}\}|\\)/g,function(t,n,e){if("\\"===n)return"\\"+e;switch(e[0]){case"\\":return"\\";case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":return String.fromCharCode(parseInt(e,8));case"b":return"\b";case"t":return"\t";case"n":return"\n";case"v":return"\v";case"f":return"\f";case"r":return"\r";case'"':return'"';case"'":return"'";case"x":return String.fromCharCode(parseInt(e.substr(1),16));case"u":return"{"===e[1]?String.fromCodePoint(parseInt(e.slice(2,-1),16)):String.fromCharCode(parseInt(e.substr(1),16))}})}},{key:"escapeRegex",value:function(t){return t.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}},{key:"expandAlphRange",value:function(t){for(var e=[],r=0;r<t.length;r++)if(r<t.length-2&&"-"===t[r+1]&&"\\"!==t[r]){for(var i=n.ord(t[r]),o=n.ord(t[r+2]),u=i;u<=o;u++)e.push(n.chr(u));r+=2}else r<t.length-2&&"\\"===t[r]&&"-"===t[r+1]?(e.push("-"),r++):e.push(t[r]);return e}},{key:"convertToByteArray",value:function(t,e){switch(e.toLowerCase()){case"binary":return Object(y.a)(t);case"hex":return Object(v.a)(t);case"decimal":return Object(g.a)(t);case"base64":return Object(p.a)(t,null,"byteArray");case"utf8":return n.strToUtf8ByteArray(t);case"latin1":default:return n.strToByteArray(t)}}},{key:"convertToByteString",value:function(t,e){switch(e.toLowerCase()){case"binary":return n.byteArrayToChars(Object(y.a)(t));case"hex":return n.byteArrayToChars(Object(v.a)(t));case"decimal":return n.byteArrayToChars(Object(g.a)(t));case"base64":return n.byteArrayToChars(Object(p.a)(t,null,"byteArray"));case"utf8":return d.a.encode(t);case"latin1":default:return t}}},{key:"strToArrayBuffer",value:function(t){for(var e,r=new Uint8Array(t.length),i=t.length;i--;)if(e=t.charCodeAt(i),r[i]=e,e>255)return n.strToUtf8ArrayBuffer(t);return r.buffer}},{key:"strToUtf8ArrayBuffer",value:function(t){var e=d.a.encode(t);return t.length!==e.length&&(x()?self.setOption("attemptHighlight",!1):_()&&(window.app.options.attemptHighlight=!1)),n.strToArrayBuffer(e)}},{key:"strToByteArray",value:function(t){for(var e,r=new Array(t.length),i=t.length;i--;)if(e=t.charCodeAt(i),r[i]=e,e>255)return n.strToUtf8ByteArray(t);return r}},{key:"strToUtf8ByteArray",value:function(t){var e=d.a.encode(t);return t.length!==e.length&&(x()?self.setOption("attemptHighlight",!1):_()&&(window.app.options.attemptHighlight=!1)),n.strToByteArray(e)}},{key:"strToCharcode",value:function(t){for(var e=[],r=0;r<t.length;r++){var i=t.charCodeAt(r);if(r<t.length-1&&i>=55296&&i<56320){var o=t[r+1].charCodeAt(0);o>=56320&&o<57344&&(i=n.ord(t[r]+t[++r]))}e.push(i)}return e}},{key:"byteArrayToUtf8",value:function(t){var e=n.byteArrayToChars(t);try{var r=d.a.decode(e);return e.length!==r.length&&(x()?self.setOption("attemptHighlight",!1):_()&&(window.app.options.attemptHighlight=!1)),r}catch(t){return e}}},{key:"byteArrayToChars",value:function(t){if(!t)return"";for(var n="",e=0;e<t.length;)n+=String.fromCharCode(t[e++]);return n}},{key:"arrayBufferToStr",value:function(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=new Uint8Array(t);return e?n.byteArrayToUtf8(r):n.byteArrayToChars(r)}},{key:"parseCSV",value:function(t){var n,e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[","],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:["\n","\r"],o=!1,u=!1,a="",c=[],f=[];t.length&&"\ufeff"===t[0]&&(t=t.substr(1));for(var s=0;s<t.length;s++)n=t[s],e=t[s+1]||"",o?(a+=n,o=!1):'"'!==n||u?'"'===n&&u?'"'===e?o=!0:u=!1:!u&&r.indexOf(n)>=0?(c.push(a),a=""):!u&&i.indexOf(n)>=0?(c.push(a),a="",f.push(c),c=[],i.indexOf(e)>=0&&e!==n&&s++):a+=n:u=!0;return c.length&&(c.push(a),f.push(c)),f}},{key:"stripHtmlTags",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return n&&(t=t.replace(/<(script|style)[^>]*>.*<\/(script|style)>/gim,"")),t.replace(/<[^>]+>/g,"")}},{key:"escapeHtml",value:function(t){var n={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","/":"&#x2F;","`":"&#x60;"};return t.replace(/[&<>"'\/`]/g,function(t){return n[t]})}},{key:"unescapeHtml",value:function(t){var n={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#x27;":"'","&#x2F;":"/","&#x60;":"`"};return t.replace(/&#?x?[a-z0-9]{2,4};/gi,function(t){return n[t]||t})}},{key:"encodeURIFragment",value:function(t){var n={"%2D":"-","%2E":".","%5F":"_","%7E":"~","%21":"!","%24":"$","%27":"'","%28":"(","%29":")","%2A":"*","%2C":",","%3B":";","%3A":":","%40":"@","%2F":"/","%3F":"?"};return(t=encodeURIComponent(t)).replace(/%[0-9A-F]{2}/g,function(t){return n[t]||t})}},{key:"generatePrettyRecipe",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],e="",r="",i="",o="",u="";return t.forEach(function(t){r=t.op.replace(/ /g,"_"),i=JSON.stringify(t.args).slice(1,-1).replace(/'/g,"\\'").replace(/"((?:[^"\\]|\\.)*)"/g,"'$1'").replace(/\\"/g,'"'),o=t.disabled?"/disabled":"",u=t.breakpoint?"/breakpoint":"",e+=`${r}(${i}${o}${u})`,n&&(e+="\n")}),e}},{key:"parseRecipeConfig",value:function(t){if(0===(t=t.trim()).length)return[];if("["===t[0])return JSON.parse(t);var n,e;t=t.replace(/\n/g,"");for(var r=/([^(]+)\(((?:'[^'\\]*(?:\\.[^'\\]*)*'|[^)\/'])*)(\/[^)]+)?\)/g,i=[];n=r.exec(t);){e="["+(e=n[2].replace(/"/g,'\\"').replace(/(^|,|{|:)'/g,'$1"').replace(/([^\\]|(?:\\\\)+)'(,|:|}|$)/g,'$1"$2').replace(/\\'/g,"'"))+"]";var o={op:n[1].replace(/_/g," "),args:JSON.parse(e)};n[3]&&n[3].indexOf("disabled")>0&&(o.disabled=!0),n[3]&&n[3].indexOf("breakpoint")>0&&(o.breakpoint=!0),i.push(o)}return i}},{key:"displayFilesAsHTML",value:(e=a()(o.a.mark(function t(e){var r,i,u,c,f;return o.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:r=function(t){return`<div class='card' style='white-space: normal;'>\n                    <div class='card-header'>\n                        <h6 class="mb-0">\n                            ${n.escapeHtml(t.name)}\n                        </h6>\n                    </div>\n                </div>`},i=function(t,e){if(e.startsWith("image")){var r="data:";return r+=e+";","<img style='max-width: 100%;' src='"+(r+="base64,"+Object(p.b)(t))+"'>"}return`<pre>${n.escapeHtml(n.arrayBufferToStr(t.buffer))}</pre>`},u=function(){var t=a()(o.a.mark(function t(e,r){var u,a,c,f;return o.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,n.readFile(e);case 2:return u=t.sent,a=new Blob([u],{type:e.type||"octet/stream"}),c=URL.createObjectURL(a),f=`<div class='card' style='white-space: normal;'>\n                    <div class='card-header' id='heading${r}'>\n                        <h6 class='mb-0'>\n                            <a class='collapsed'\n                                data-toggle='collapse'\n                                href='#collapse${r}'\n                                aria-expanded='false'\n                                aria-controls='collapse${r}'\n                                title="Show/hide contents of '${n.escapeHtml(e.name)}'">\n                                ${n.escapeHtml(e.name)}</a>\n                            <span class='float-right' style="margin-top: -3px">\n                                ${e.size.toLocaleString()} bytes\n                                <a title="Download ${n.escapeHtml(e.name)}"\n                                    href="${c}"\n                                    download="${n.escapeHtml(e.name)}"\n                                    data-toggle="tooltip">\n                                    <i class="material-icons" style="vertical-align: bottom">save</i>\n                                </a>\n                                <a title="Move to input"\n                                    href="#"\n                                    blob-url="${c}"\n                                    file-name="${n.escapeHtml(e.name)}"\n                                    class="extract-file"\n                                    data-toggle="tooltip">\n                                    <i class="material-icons" style="vertical-align: bottom">open_in_browser</i>\n                                </a>\n                            </span>\n                        </h6>\n                    </div>\n                    <div id='collapse${r}' class='collapse' aria-labelledby='heading${r}' data-parent="#files">\n                        <div class='card-body'>\n                            ${i(u,e.type)}\n                        </div>\n                    </div>\n                </div>`,t.abrupt("return",f);case 7:case"end":return t.stop()}},t)}));return function(n,e){return t.apply(this,arguments)}}(),c=`<div style='padding: 5px; white-space: normal;'>\n                ${e.length} file(s) found\n            </div><div id="files" style="padding: 20px">`,f=0;case 5:if(!(f<e.length)){t.next=17;break}if(!e[f].name.endsWith("/")){t.next=10;break}c+=r(e[f]),t.next=14;break;case 10:return t.t0=c,t.next=13,u(e[f],f);case 13:c=t.t0+=t.sent;case 14:f++,t.next=5;break;case 17:return t.abrupt("return",c+="</div>");case 18:case"end":return t.stop()}},t)})),function(t){return e.apply(this,arguments)})},{key:"parseURIParams",value:function(t){if(""===t)return{};"?"!==t[0]&&"#"!==t[0]||(t=t.substr(1));for(var n=t.split("&"),e={},r=0;r<n.length;r++){var i=n[r].split("=");2!==i.length?e[n[r]]=!0:e[i[0]]=decodeURIComponent(i[1].replace(/\+/g," "))}return e}},{key:"readFile",value:function(n){return b()?t.from(n).buffer:new Promise(function(t,e){var r=new FileReader,i=new Uint8Array(n.size),o=0,u=function(){if(o>=n.size)t(i);else{var e=n.slice(o,o+10485760);r.readAsArrayBuffer(e)}};r.onload=function(t){i.set(new Uint8Array(r.result),o),o+=10485760,u()},r.onerror=function(t){e(r.error.message)},u()})}},{key:"readFileSync",value:function(t){if(!b())throw new TypeError("Browser environment cannot support readFileSync");return Uint8Array.from(t.data).buffer}},{key:"mod",value:function(t,n){return(t%n+n)%n}},{key:"gcd",value:function(t,e){return e?n.gcd(e,t%e):t}},{key:"modInv",value:function(t,n){t%=n;for(var e=1;e<n;e++)if(t*e%26==1)return e}},{key:"charRep",value:function(t){return{Space:" ",Percent:"%",Comma:",","Semi-colon":";",Colon:":",Tab:"\t","Line feed":"\n",CRLF:"\r\n","Forward slash":"/",Backslash:"\\","0x":"0x","\\x":"\\x","Nothing (separate chars)":"",None:""}[t]}},{key:"regexRep",value:function(t){return{Space:/\s+/g,Percent:/%/g,Comma:/,/g,"Semi-colon":/;/g,Colon:/:/g,"Line feed":/\n/g,CRLF:/\r\n/g,"Forward slash":/\//g,Backslash:/\\/g,"0x":/0x/g,"\\x":/\\x/g,None:/\s+/g}[t]}}]),n}();function b(){return void 0!==r&&null!=r.versions&&null!=r.versions.node}function _(){return"object"==typeof window}function x(){return"function"==typeof importScripts}function w(t){x()?self.sendStatusMessage(t):_()?app.alert(t,1e4):b()&&console.debug(t)}n.b=m,Array.prototype.unique=function(){for(var t={},n=[],e=0,r=this.length;e<r;e++)Object.prototype.hasOwnProperty.call(t,this[e])||(n.push(this[e]),t[this[e]]=1);return n},Array.prototype.max=function(){return Math.max.apply(null,this)},Array.prototype.min=function(){return Math.min.apply(null,this)},Array.prototype.sum=function(){return this.reduce(function(t,n){return t+n},0)},Array.prototype.equals=function(t){if(!t)return!1;var n=this.length;if(n!==t.length)return!1;for(;n--;)if(this[n]!==t[n])return!1;return!0},String.prototype.count=function(t){return this.split(t).length-1};var A={};function E(t,n,e,r,i){return function(){clearTimeout(A[e]),A[e]=setTimeout(function(){t.apply(r,i)},n)}}String.prototype.padStart||(String.prototype.padStart=function(t,n){return t>>=0,n=String(void 0!==n?n:" "),this.length>t?String(this):((t-=this.length)>n.length&&(n+=n.repeat(t/n.length)),n.slice(0,t)+String(this))}),String.prototype.padEnd||(String.prototype.padEnd=function(t,n){return t>>=0,n=String(void 0!==n?n:" "),this.length>t?String(this):((t-=this.length)>n.length&&(n+=n.repeat(t/n.length)),String(this)+n.slice(0,t))})}).call(this,e(12).Buffer,e(27))},1:function(t,n){t.exports=function(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}},10:function(t,n,e){"use strict";e.d(n,"a",function(){return a});var r=e(1),i=e.n(r),o=e(2),u=e.n(o),a=function(){function t(n){i()(this,t),this.bytes=n,this.length=this.bytes.length,this.position=0,this.bitPos=0}return u()(t,[{key:"getBytes",value:function(t){if(!(this.position>this.length)){var n=this.position+t,e=this.bytes.slice(this.position,n);return this.position=n,this.bitPos=0,e}}},{key:"readString",value:function(t){if(!(this.position>this.length)){for(var n="",e=this.position;e<this.position+t;e++){var r=this.bytes[e];if(0===r)break;n+=String.fromCharCode(r)}return this.position+=t,this.bitPos=0,n}}},{key:"readInt",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"be";if(!(this.position>this.length)){var e=0;if("be"===n)for(var r=this.position;r<this.position+t;r++)e<<=8,e|=this.bytes[r];else for(var i=this.position+t-1;i>=this.position;i--)e<<=8,e|=this.bytes[i];return this.position+=t,this.bitPos=0,e}}},{key:"readBits",value:function(t){if(!(this.position>this.length)){var n=0,e=0;for(n=(this.bytes[this.position++]&256-(1<<this.bitPos))>>>this.bitPos,e=8-this.bitPos,this.bitPos=0;e<t;)n|=this.bytes[this.position++]<<e,e+=8;if(e>t){var r=e-t;n&=(1<<t)-1,e-=r,this.position--,this.bitPos=8-r}return n}}},{key:"continueUntil",value:function(t){if(!(this.position>this.length))if(this.bitPos=0,"number"!=typeof t)for(var n=!1;!n&&this.position<this.length;){for(;++this.position<this.length&&this.bytes[this.position]!==t[0];);n=!0;for(var e=1;e<t.length;e++)(this.position+e>this.length||this.bytes[this.position+e]!==t[e])&&(n=!1)}else for(;++this.position<this.length&&this.bytes[this.position]!==t;);}},{key:"consumeIf",value:function(t){this.bytes[this.position]===t&&(this.position++,this.bitPos=0)}},{key:"moveForwardsBy",value:function(t){var n=this.position+t;if(n<0||n>this.length)throw new Error("Cannot move to position "+n+" in stream. Out of bounds.");this.position=n,this.bitPos=0}},{key:"moveBackwardsBy",value:function(t){var n=this.position-t;if(n<0||n>this.length)throw new Error("Cannot move to position "+n+" in stream. Out of bounds.");this.position=n,this.bitPos=0}},{key:"moveBackwardsByBits",value:function(t){if(t<=this.bitPos)this.bitPos-=t;else for(this.bitPos>0&&(t-=this.bitPos,this.bitPos=0);t>0;)this.moveBackwardsBy(1),this.bitPos=8,this.moveBackwardsByBits(t),t-=8}},{key:"moveTo",value:function(t){if(t<0||t>this.length)throw new Error("Cannot move to position "+t+" in stream. Out of bounds.");this.position=t,this.bitPos=0}},{key:"hasMore",value:function(){return this.position<this.length}},{key:"carve",value:function(){return this.bitPos>0&&this.position++,this.bytes.slice(0,this.position)}}]),t}()},102:function(t,n,e){"use strict";e.r(n);var r=Math.PI,i=2*r,o=i-1e-6;function u(){this._x0=this._y0=this._x1=this._y1=null,this._=""}function a(){return new u}u.prototype=a.prototype={constructor:u,moveTo:function(t,n){this._+="M"+(this._x0=this._x1=+t)+","+(this._y0=this._y1=+n)},closePath:function(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._+="Z")},lineTo:function(t,n){this._+="L"+(this._x1=+t)+","+(this._y1=+n)},quadraticCurveTo:function(t,n,e,r){this._+="Q"+ +t+","+ +n+","+(this._x1=+e)+","+(this._y1=+r)},bezierCurveTo:function(t,n,e,r,i,o){this._+="C"+ +t+","+ +n+","+ +e+","+ +r+","+(this._x1=+i)+","+(this._y1=+o)},arcTo:function(t,n,e,i,o){t=+t,n=+n,e=+e,i=+i,o=+o;var u=this._x1,a=this._y1,c=e-t,f=i-n,s=u-t,l=a-n,h=s*s+l*l;if(o<0)throw new Error("negative radius: "+o);if(null===this._x1)this._+="M"+(this._x1=t)+","+(this._y1=n);else if(h>1e-6)if(Math.abs(l*c-f*s)>1e-6&&o){var d=e-u,p=i-a,v=c*c+f*f,g=d*d+p*p,y=Math.sqrt(v),m=Math.sqrt(h),b=o*Math.tan((r-Math.acos((v+h-g)/(2*y*m)))/2),_=b/m,x=b/y;Math.abs(_-1)>1e-6&&(this._+="L"+(t+_*s)+","+(n+_*l)),this._+="A"+o+","+o+",0,0,"+ +(l*d>s*p)+","+(this._x1=t+x*c)+","+(this._y1=n+x*f)}else this._+="L"+(this._x1=t)+","+(this._y1=n);else;},arc:function(t,n,e,u,a,c){t=+t,n=+n,c=!!c;var f=(e=+e)*Math.cos(u),s=e*Math.sin(u),l=t+f,h=n+s,d=1^c,p=c?u-a:a-u;if(e<0)throw new Error("negative radius: "+e);null===this._x1?this._+="M"+l+","+h:(Math.abs(this._x1-l)>1e-6||Math.abs(this._y1-h)>1e-6)&&(this._+="L"+l+","+h),e&&(p<0&&(p=p%i+i),p>o?this._+="A"+e+","+e+",0,1,"+d+","+(t-f)+","+(n-s)+"A"+e+","+e+",0,1,"+d+","+(this._x1=l)+","+(this._y1=h):p>1e-6&&(this._+="A"+e+","+e+",0,"+ +(p>=r)+","+d+","+(this._x1=t+e*Math.cos(a))+","+(this._y1=n+e*Math.sin(a))))},rect:function(t,n,e,r){this._+="M"+(this._x0=this._x1=+t)+","+(this._y0=this._y1=+n)+"h"+ +e+"v"+ +r+"h"+-e+"Z"},toString:function(){return this._}};var c=a;e.d(n,"path",function(){return c})},11:function(t,n){function e(t,n,e,r,i,o,u){try{var a=t[o](u),c=a.value}catch(t){return void e(t)}a.done?n(c):Promise.resolve(c).then(r,i)}t.exports=function(t){return function(){var n=this,r=arguments;return new Promise(function(i,o){var u=t.apply(n,r);function a(t){e(u,i,o,a,c,"next",t)}function c(t){e(u,i,o,a,c,"throw",t)}a(void 0)})}}},12:function(t,n,e){"use strict";(function(t){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <<EMAIL>> <http://feross.org>
 * @license  MIT
 */
var r=e(45),i=e(46),o=e(47);function u(){return c.TYPED_ARRAY_SUPPORT?**********:**********}function a(t,n){if(u()<n)throw new RangeError("Invalid typed array length");return c.TYPED_ARRAY_SUPPORT?(t=new Uint8Array(n)).__proto__=c.prototype:(null===t&&(t=new c(n)),t.length=n),t}function c(t,n,e){if(!(c.TYPED_ARRAY_SUPPORT||this instanceof c))return new c(t,n,e);if("number"==typeof t){if("string"==typeof n)throw new Error("If encoding is specified then the first argument must be a string");return l(this,t)}return f(this,t,n,e)}function f(t,n,e,r){if("number"==typeof n)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&n instanceof ArrayBuffer?function(t,n,e,r){if(n.byteLength,e<0||n.byteLength<e)throw new RangeError("'offset' is out of bounds");if(n.byteLength<e+(r||0))throw new RangeError("'length' is out of bounds");n=void 0===e&&void 0===r?new Uint8Array(n):void 0===r?new Uint8Array(n,e):new Uint8Array(n,e,r);c.TYPED_ARRAY_SUPPORT?(t=n).__proto__=c.prototype:t=h(t,n);return t}(t,n,e,r):"string"==typeof n?function(t,n,e){"string"==typeof e&&""!==e||(e="utf8");if(!c.isEncoding(e))throw new TypeError('"encoding" must be a valid string encoding');var r=0|p(n,e),i=(t=a(t,r)).write(n,e);i!==r&&(t=t.slice(0,i));return t}(t,n,e):function(t,n){if(c.isBuffer(n)){var e=0|d(n.length);return 0===(t=a(t,e)).length?t:(n.copy(t,0,0,e),t)}if(n){if("undefined"!=typeof ArrayBuffer&&n.buffer instanceof ArrayBuffer||"length"in n)return"number"!=typeof n.length||(r=n.length)!=r?a(t,0):h(t,n);if("Buffer"===n.type&&o(n.data))return h(t,n.data)}var r;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(t,n)}function s(t){if("number"!=typeof t)throw new TypeError('"size" argument must be a number');if(t<0)throw new RangeError('"size" argument must not be negative')}function l(t,n){if(s(n),t=a(t,n<0?0:0|d(n)),!c.TYPED_ARRAY_SUPPORT)for(var e=0;e<n;++e)t[e]=0;return t}function h(t,n){var e=n.length<0?0:0|d(n.length);t=a(t,e);for(var r=0;r<e;r+=1)t[r]=255&n[r];return t}function d(t){if(t>=u())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+u().toString(16)+" bytes");return 0|t}function p(t,n){if(c.isBuffer(t))return t.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;"string"!=typeof t&&(t=""+t);var e=t.length;if(0===e)return 0;for(var r=!1;;)switch(n){case"ascii":case"latin1":case"binary":return e;case"utf8":case"utf-8":case void 0:return z(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*e;case"hex":return e>>>1;case"base64":return Y(t).length;default:if(r)return z(t).length;n=(""+n).toLowerCase(),r=!0}}function v(t,n,e){var r=!1;if((void 0===n||n<0)&&(n=0),n>this.length)return"";if((void 0===e||e>this.length)&&(e=this.length),e<=0)return"";if((e>>>=0)<=(n>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return N(this,n,e);case"utf8":case"utf-8":return k(this,n,e);case"ascii":return F(this,n,e);case"latin1":case"binary":return T(this,n,e);case"base64":return M(this,n,e);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return C(this,n,e);default:if(r)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),r=!0}}function g(t,n,e){var r=t[n];t[n]=t[e],t[e]=r}function y(t,n,e,r,i){if(0===t.length)return-1;if("string"==typeof e?(r=e,e=0):e>**********?e=**********:e<-2147483648&&(e=-2147483648),e=+e,isNaN(e)&&(e=i?0:t.length-1),e<0&&(e=t.length+e),e>=t.length){if(i)return-1;e=t.length-1}else if(e<0){if(!i)return-1;e=0}if("string"==typeof n&&(n=c.from(n,r)),c.isBuffer(n))return 0===n.length?-1:m(t,n,e,r,i);if("number"==typeof n)return n&=255,c.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(t,n,e):Uint8Array.prototype.lastIndexOf.call(t,n,e):m(t,[n],e,r,i);throw new TypeError("val must be string, number or Buffer")}function m(t,n,e,r,i){var o,u=1,a=t.length,c=n.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(t.length<2||n.length<2)return-1;u=2,a/=2,c/=2,e/=2}function f(t,n){return 1===u?t[n]:t.readUInt16BE(n*u)}if(i){var s=-1;for(o=e;o<a;o++)if(f(t,o)===f(n,-1===s?0:o-s)){if(-1===s&&(s=o),o-s+1===c)return s*u}else-1!==s&&(o-=o-s),s=-1}else for(e+c>a&&(e=a-c),o=e;o>=0;o--){for(var l=!0,h=0;h<c;h++)if(f(t,o+h)!==f(n,h)){l=!1;break}if(l)return o}return-1}function b(t,n,e,r){e=Number(e)||0;var i=t.length-e;r?(r=Number(r))>i&&(r=i):r=i;var o=n.length;if(o%2!=0)throw new TypeError("Invalid hex string");r>o/2&&(r=o/2);for(var u=0;u<r;++u){var a=parseInt(n.substr(2*u,2),16);if(isNaN(a))return u;t[e+u]=a}return u}function _(t,n,e,r){return q(z(n,t.length-e),t,e,r)}function x(t,n,e,r){return q(function(t){for(var n=[],e=0;e<t.length;++e)n.push(255&t.charCodeAt(e));return n}(n),t,e,r)}function w(t,n,e,r){return x(t,n,e,r)}function A(t,n,e,r){return q(Y(n),t,e,r)}function E(t,n,e,r){return q(function(t,n){for(var e,r,i,o=[],u=0;u<t.length&&!((n-=2)<0);++u)e=t.charCodeAt(u),r=e>>8,i=e%256,o.push(i),o.push(r);return o}(n,t.length-e),t,e,r)}function M(t,n,e){return 0===n&&e===t.length?r.fromByteArray(t):r.fromByteArray(t.slice(n,e))}function k(t,n,e){e=Math.min(t.length,e);for(var r=[],i=n;i<e;){var o,u,a,c,f=t[i],s=null,l=f>239?4:f>223?3:f>191?2:1;if(i+l<=e)switch(l){case 1:f<128&&(s=f);break;case 2:128==(192&(o=t[i+1]))&&(c=(31&f)<<6|63&o)>127&&(s=c);break;case 3:o=t[i+1],u=t[i+2],128==(192&o)&&128==(192&u)&&(c=(15&f)<<12|(63&o)<<6|63&u)>2047&&(c<55296||c>57343)&&(s=c);break;case 4:o=t[i+1],u=t[i+2],a=t[i+3],128==(192&o)&&128==(192&u)&&128==(192&a)&&(c=(15&f)<<18|(63&o)<<12|(63&u)<<6|63&a)>65535&&c<1114112&&(s=c)}null===s?(s=65533,l=1):s>65535&&(s-=65536,r.push(s>>>10&1023|55296),s=56320|1023&s),r.push(s),i+=l}return function(t){var n=t.length;if(n<=B)return String.fromCharCode.apply(String,t);var e="",r=0;for(;r<n;)e+=String.fromCharCode.apply(String,t.slice(r,r+=B));return e}(r)}n.Buffer=c,n.SlowBuffer=function(t){+t!=t&&(t=0);return c.alloc(+t)},n.INSPECT_MAX_BYTES=50,c.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:function(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()&&"function"==typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(t){return!1}}(),n.kMaxLength=u(),c.poolSize=8192,c._augment=function(t){return t.__proto__=c.prototype,t},c.from=function(t,n,e){return f(null,t,n,e)},c.TYPED_ARRAY_SUPPORT&&(c.prototype.__proto__=Uint8Array.prototype,c.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&c[Symbol.species]===c&&Object.defineProperty(c,Symbol.species,{value:null,configurable:!0})),c.alloc=function(t,n,e){return function(t,n,e,r){return s(n),n<=0?a(t,n):void 0!==e?"string"==typeof r?a(t,n).fill(e,r):a(t,n).fill(e):a(t,n)}(null,t,n,e)},c.allocUnsafe=function(t){return l(null,t)},c.allocUnsafeSlow=function(t){return l(null,t)},c.isBuffer=function(t){return!(null==t||!t._isBuffer)},c.compare=function(t,n){if(!c.isBuffer(t)||!c.isBuffer(n))throw new TypeError("Arguments must be Buffers");if(t===n)return 0;for(var e=t.length,r=n.length,i=0,o=Math.min(e,r);i<o;++i)if(t[i]!==n[i]){e=t[i],r=n[i];break}return e<r?-1:r<e?1:0},c.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},c.concat=function(t,n){if(!o(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return c.alloc(0);var e;if(void 0===n)for(n=0,e=0;e<t.length;++e)n+=t[e].length;var r=c.allocUnsafe(n),i=0;for(e=0;e<t.length;++e){var u=t[e];if(!c.isBuffer(u))throw new TypeError('"list" argument must be an Array of Buffers');u.copy(r,i),i+=u.length}return r},c.byteLength=p,c.prototype._isBuffer=!0,c.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var n=0;n<t;n+=2)g(this,n,n+1);return this},c.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var n=0;n<t;n+=4)g(this,n,n+3),g(this,n+1,n+2);return this},c.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var n=0;n<t;n+=8)g(this,n,n+7),g(this,n+1,n+6),g(this,n+2,n+5),g(this,n+3,n+4);return this},c.prototype.toString=function(){var t=0|this.length;return 0===t?"":0===arguments.length?k(this,0,t):v.apply(this,arguments)},c.prototype.equals=function(t){if(!c.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===c.compare(this,t)},c.prototype.inspect=function(){var t="",e=n.INSPECT_MAX_BYTES;return this.length>0&&(t=this.toString("hex",0,e).match(/.{2}/g).join(" "),this.length>e&&(t+=" ... ")),"<Buffer "+t+">"},c.prototype.compare=function(t,n,e,r,i){if(!c.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(void 0===n&&(n=0),void 0===e&&(e=t?t.length:0),void 0===r&&(r=0),void 0===i&&(i=this.length),n<0||e>t.length||r<0||i>this.length)throw new RangeError("out of range index");if(r>=i&&n>=e)return 0;if(r>=i)return-1;if(n>=e)return 1;if(this===t)return 0;for(var o=(i>>>=0)-(r>>>=0),u=(e>>>=0)-(n>>>=0),a=Math.min(o,u),f=this.slice(r,i),s=t.slice(n,e),l=0;l<a;++l)if(f[l]!==s[l]){o=f[l],u=s[l];break}return o<u?-1:u<o?1:0},c.prototype.includes=function(t,n,e){return-1!==this.indexOf(t,n,e)},c.prototype.indexOf=function(t,n,e){return y(this,t,n,e,!0)},c.prototype.lastIndexOf=function(t,n,e){return y(this,t,n,e,!1)},c.prototype.write=function(t,n,e,r){if(void 0===n)r="utf8",e=this.length,n=0;else if(void 0===e&&"string"==typeof n)r=n,e=this.length,n=0;else{if(!isFinite(n))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");n|=0,isFinite(e)?(e|=0,void 0===r&&(r="utf8")):(r=e,e=void 0)}var i=this.length-n;if((void 0===e||e>i)&&(e=i),t.length>0&&(e<0||n<0)||n>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var o=!1;;)switch(r){case"hex":return b(this,t,n,e);case"utf8":case"utf-8":return _(this,t,n,e);case"ascii":return x(this,t,n,e);case"latin1":case"binary":return w(this,t,n,e);case"base64":return A(this,t,n,e);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return E(this,t,n,e);default:if(o)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),o=!0}},c.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var B=4096;function F(t,n,e){var r="";e=Math.min(t.length,e);for(var i=n;i<e;++i)r+=String.fromCharCode(127&t[i]);return r}function T(t,n,e){var r="";e=Math.min(t.length,e);for(var i=n;i<e;++i)r+=String.fromCharCode(t[i]);return r}function N(t,n,e){var r=t.length;(!n||n<0)&&(n=0),(!e||e<0||e>r)&&(e=r);for(var i="",o=n;o<e;++o)i+=U(t[o]);return i}function C(t,n,e){for(var r=t.slice(n,e),i="",o=0;o<r.length;o+=2)i+=String.fromCharCode(r[o]+256*r[o+1]);return i}function S(t,n,e){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+n>e)throw new RangeError("Trying to access beyond buffer length")}function O(t,n,e,r,i,o){if(!c.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(n>i||n<o)throw new RangeError('"value" argument is out of bounds');if(e+r>t.length)throw new RangeError("Index out of range")}function R(t,n,e,r){n<0&&(n=65535+n+1);for(var i=0,o=Math.min(t.length-e,2);i<o;++i)t[e+i]=(n&255<<8*(r?i:1-i))>>>8*(r?i:1-i)}function P(t,n,e,r){n<0&&(n=4294967295+n+1);for(var i=0,o=Math.min(t.length-e,4);i<o;++i)t[e+i]=n>>>8*(r?i:3-i)&255}function j(t,n,e,r,i,o){if(e+r>t.length)throw new RangeError("Index out of range");if(e<0)throw new RangeError("Index out of range")}function D(t,n,e,r,o){return o||j(t,0,e,4),i.write(t,n,e,r,23,4),e+4}function L(t,n,e,r,o){return o||j(t,0,e,8),i.write(t,n,e,r,52,8),e+8}c.prototype.slice=function(t,n){var e,r=this.length;if((t=~~t)<0?(t+=r)<0&&(t=0):t>r&&(t=r),(n=void 0===n?r:~~n)<0?(n+=r)<0&&(n=0):n>r&&(n=r),n<t&&(n=t),c.TYPED_ARRAY_SUPPORT)(e=this.subarray(t,n)).__proto__=c.prototype;else{var i=n-t;e=new c(i,void 0);for(var o=0;o<i;++o)e[o]=this[o+t]}return e},c.prototype.readUIntLE=function(t,n,e){t|=0,n|=0,e||S(t,n,this.length);for(var r=this[t],i=1,o=0;++o<n&&(i*=256);)r+=this[t+o]*i;return r},c.prototype.readUIntBE=function(t,n,e){t|=0,n|=0,e||S(t,n,this.length);for(var r=this[t+--n],i=1;n>0&&(i*=256);)r+=this[t+--n]*i;return r},c.prototype.readUInt8=function(t,n){return n||S(t,1,this.length),this[t]},c.prototype.readUInt16LE=function(t,n){return n||S(t,2,this.length),this[t]|this[t+1]<<8},c.prototype.readUInt16BE=function(t,n){return n||S(t,2,this.length),this[t]<<8|this[t+1]},c.prototype.readUInt32LE=function(t,n){return n||S(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},c.prototype.readUInt32BE=function(t,n){return n||S(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},c.prototype.readIntLE=function(t,n,e){t|=0,n|=0,e||S(t,n,this.length);for(var r=this[t],i=1,o=0;++o<n&&(i*=256);)r+=this[t+o]*i;return r>=(i*=128)&&(r-=Math.pow(2,8*n)),r},c.prototype.readIntBE=function(t,n,e){t|=0,n|=0,e||S(t,n,this.length);for(var r=n,i=1,o=this[t+--r];r>0&&(i*=256);)o+=this[t+--r]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*n)),o},c.prototype.readInt8=function(t,n){return n||S(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},c.prototype.readInt16LE=function(t,n){n||S(t,2,this.length);var e=this[t]|this[t+1]<<8;return 32768&e?4294901760|e:e},c.prototype.readInt16BE=function(t,n){n||S(t,2,this.length);var e=this[t+1]|this[t]<<8;return 32768&e?4294901760|e:e},c.prototype.readInt32LE=function(t,n){return n||S(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},c.prototype.readInt32BE=function(t,n){return n||S(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},c.prototype.readFloatLE=function(t,n){return n||S(t,4,this.length),i.read(this,t,!0,23,4)},c.prototype.readFloatBE=function(t,n){return n||S(t,4,this.length),i.read(this,t,!1,23,4)},c.prototype.readDoubleLE=function(t,n){return n||S(t,8,this.length),i.read(this,t,!0,52,8)},c.prototype.readDoubleBE=function(t,n){return n||S(t,8,this.length),i.read(this,t,!1,52,8)},c.prototype.writeUIntLE=function(t,n,e,r){(t=+t,n|=0,e|=0,r)||O(this,t,n,e,Math.pow(2,8*e)-1,0);var i=1,o=0;for(this[n]=255&t;++o<e&&(i*=256);)this[n+o]=t/i&255;return n+e},c.prototype.writeUIntBE=function(t,n,e,r){(t=+t,n|=0,e|=0,r)||O(this,t,n,e,Math.pow(2,8*e)-1,0);var i=e-1,o=1;for(this[n+i]=255&t;--i>=0&&(o*=256);)this[n+i]=t/o&255;return n+e},c.prototype.writeUInt8=function(t,n,e){return t=+t,n|=0,e||O(this,t,n,1,255,0),c.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[n]=255&t,n+1},c.prototype.writeUInt16LE=function(t,n,e){return t=+t,n|=0,e||O(this,t,n,2,65535,0),c.TYPED_ARRAY_SUPPORT?(this[n]=255&t,this[n+1]=t>>>8):R(this,t,n,!0),n+2},c.prototype.writeUInt16BE=function(t,n,e){return t=+t,n|=0,e||O(this,t,n,2,65535,0),c.TYPED_ARRAY_SUPPORT?(this[n]=t>>>8,this[n+1]=255&t):R(this,t,n,!1),n+2},c.prototype.writeUInt32LE=function(t,n,e){return t=+t,n|=0,e||O(this,t,n,4,4294967295,0),c.TYPED_ARRAY_SUPPORT?(this[n+3]=t>>>24,this[n+2]=t>>>16,this[n+1]=t>>>8,this[n]=255&t):P(this,t,n,!0),n+4},c.prototype.writeUInt32BE=function(t,n,e){return t=+t,n|=0,e||O(this,t,n,4,4294967295,0),c.TYPED_ARRAY_SUPPORT?(this[n]=t>>>24,this[n+1]=t>>>16,this[n+2]=t>>>8,this[n+3]=255&t):P(this,t,n,!1),n+4},c.prototype.writeIntLE=function(t,n,e,r){if(t=+t,n|=0,!r){var i=Math.pow(2,8*e-1);O(this,t,n,e,i-1,-i)}var o=0,u=1,a=0;for(this[n]=255&t;++o<e&&(u*=256);)t<0&&0===a&&0!==this[n+o-1]&&(a=1),this[n+o]=(t/u>>0)-a&255;return n+e},c.prototype.writeIntBE=function(t,n,e,r){if(t=+t,n|=0,!r){var i=Math.pow(2,8*e-1);O(this,t,n,e,i-1,-i)}var o=e-1,u=1,a=0;for(this[n+o]=255&t;--o>=0&&(u*=256);)t<0&&0===a&&0!==this[n+o+1]&&(a=1),this[n+o]=(t/u>>0)-a&255;return n+e},c.prototype.writeInt8=function(t,n,e){return t=+t,n|=0,e||O(this,t,n,1,127,-128),c.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),t<0&&(t=255+t+1),this[n]=255&t,n+1},c.prototype.writeInt16LE=function(t,n,e){return t=+t,n|=0,e||O(this,t,n,2,32767,-32768),c.TYPED_ARRAY_SUPPORT?(this[n]=255&t,this[n+1]=t>>>8):R(this,t,n,!0),n+2},c.prototype.writeInt16BE=function(t,n,e){return t=+t,n|=0,e||O(this,t,n,2,32767,-32768),c.TYPED_ARRAY_SUPPORT?(this[n]=t>>>8,this[n+1]=255&t):R(this,t,n,!1),n+2},c.prototype.writeInt32LE=function(t,n,e){return t=+t,n|=0,e||O(this,t,n,4,**********,-2147483648),c.TYPED_ARRAY_SUPPORT?(this[n]=255&t,this[n+1]=t>>>8,this[n+2]=t>>>16,this[n+3]=t>>>24):P(this,t,n,!0),n+4},c.prototype.writeInt32BE=function(t,n,e){return t=+t,n|=0,e||O(this,t,n,4,**********,-2147483648),t<0&&(t=4294967295+t+1),c.TYPED_ARRAY_SUPPORT?(this[n]=t>>>24,this[n+1]=t>>>16,this[n+2]=t>>>8,this[n+3]=255&t):P(this,t,n,!1),n+4},c.prototype.writeFloatLE=function(t,n,e){return D(this,t,n,!0,e)},c.prototype.writeFloatBE=function(t,n,e){return D(this,t,n,!1,e)},c.prototype.writeDoubleLE=function(t,n,e){return L(this,t,n,!0,e)},c.prototype.writeDoubleBE=function(t,n,e){return L(this,t,n,!1,e)},c.prototype.copy=function(t,n,e,r){if(e||(e=0),r||0===r||(r=this.length),n>=t.length&&(n=t.length),n||(n=0),r>0&&r<e&&(r=e),r===e)return 0;if(0===t.length||0===this.length)return 0;if(n<0)throw new RangeError("targetStart out of bounds");if(e<0||e>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-n<r-e&&(r=t.length-n+e);var i,o=r-e;if(this===t&&e<n&&n<r)for(i=o-1;i>=0;--i)t[i+n]=this[i+e];else if(o<1e3||!c.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)t[i+n]=this[i+e];else Uint8Array.prototype.set.call(t,this.subarray(e,e+o),n);return o},c.prototype.fill=function(t,n,e,r){if("string"==typeof t){if("string"==typeof n?(r=n,n=0,e=this.length):"string"==typeof e&&(r=e,e=this.length),1===t.length){var i=t.charCodeAt(0);i<256&&(t=i)}if(void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!c.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"==typeof t&&(t&=255);if(n<0||this.length<n||this.length<e)throw new RangeError("Out of range index");if(e<=n)return this;var o;if(n>>>=0,e=void 0===e?this.length:e>>>0,t||(t=0),"number"==typeof t)for(o=n;o<e;++o)this[o]=t;else{var u=c.isBuffer(t)?t:z(new c(t,r).toString()),a=u.length;for(o=0;o<e-n;++o)this[o+n]=u[o%a]}return this};var I=/[^+\/0-9A-Za-z-_]/g;function U(t){return t<16?"0"+t.toString(16):t.toString(16)}function z(t,n){var e;n=n||1/0;for(var r=t.length,i=null,o=[],u=0;u<r;++u){if((e=t.charCodeAt(u))>55295&&e<57344){if(!i){if(e>56319){(n-=3)>-1&&o.push(239,191,189);continue}if(u+1===r){(n-=3)>-1&&o.push(239,191,189);continue}i=e;continue}if(e<56320){(n-=3)>-1&&o.push(239,191,189),i=e;continue}e=65536+(i-55296<<10|e-56320)}else i&&(n-=3)>-1&&o.push(239,191,189);if(i=null,e<128){if((n-=1)<0)break;o.push(e)}else if(e<2048){if((n-=2)<0)break;o.push(e>>6|192,63&e|128)}else if(e<65536){if((n-=3)<0)break;o.push(e>>12|224,e>>6&63|128,63&e|128)}else{if(!(e<1114112))throw new Error("Invalid code point");if((n-=4)<0)break;o.push(e>>18|240,e>>12&63|128,e>>6&63|128,63&e|128)}}return o}function Y(t){return r.toByteArray(function(t){if((t=function(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}(t).replace(I,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function q(t,n,e,r){for(var i=0;i<r&&!(i+e>=n.length||i>=t.length);++i)n[i+e]=t[i];return i}}).call(this,e(28))},126:function(t,n,e){"use strict";e.r(n);var r={value:function(){}};function i(){for(var t,n=0,e=arguments.length,r={};n<e;++n){if(!(t=arguments[n]+"")||t in r)throw new Error("illegal type: "+t);r[t]=[]}return new o(r)}function o(t){this._=t}function u(t,n){return t.trim().split(/^|\s+/).map(function(t){var e="",r=t.indexOf(".");if(r>=0&&(e=t.slice(r+1),t=t.slice(0,r)),t&&!n.hasOwnProperty(t))throw new Error("unknown type: "+t);return{type:t,name:e}})}function a(t,n){for(var e,r=0,i=t.length;r<i;++r)if((e=t[r]).name===n)return e.value}function c(t,n,e){for(var i=0,o=t.length;i<o;++i)if(t[i].name===n){t[i]=r,t=t.slice(0,i).concat(t.slice(i+1));break}return null!=e&&t.push({name:n,value:e}),t}o.prototype=i.prototype={constructor:o,on:function(t,n){var e,r=this._,i=u(t+"",r),o=-1,f=i.length;if(!(arguments.length<2)){if(null!=n&&"function"!=typeof n)throw new Error("invalid callback: "+n);for(;++o<f;)if(e=(t=i[o]).type)r[e]=c(r[e],t.name,n);else if(null==n)for(e in r)r[e]=c(r[e],t.name,null);return this}for(;++o<f;)if((e=(t=i[o]).type)&&(e=a(r[e],t.name)))return e},copy:function(){var t={},n=this._;for(var e in n)t[e]=n[e].slice();return new o(t)},call:function(t,n){if((e=arguments.length-2)>0)for(var e,r,i=new Array(e),o=0;o<e;++o)i[o]=arguments[o+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(o=0,e=(r=this._[t]).length;o<e;++o)r[o].value.apply(n,i)},apply:function(t,n,e){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var r=this._[t],i=0,o=r.length;i<o;++i)r[i].value.apply(n,e)}};var f=i;e.d(n,"dispatch",function(){return f})},13:function(t,n,e){var r=e(49),i=e(50),o=e(51);t.exports=function(t,n){return r(t)||i(t,n)||o()}},1313:function(t,n,e){"use strict";e.r(n);var r=function(){return new i};function i(){this.reset()}i.prototype={constructor:i,reset:function(){this.s=this.t=0},add:function(t){u(o,t,this.t),u(this,o.s,this.s),this.s?this.t+=o.t:this.s=o.t},valueOf:function(){return this.s}};var o=new i;function u(t,n,e){var r=t.s=n+e,i=r-n,o=r-i;t.t=n-o+(e-i)}var a=1e-6,c=1e-12,f=Math.PI,s=f/2,l=f/4,h=2*f,d=180/f,p=f/180,v=Math.abs,g=Math.atan,y=Math.atan2,m=Math.cos,b=Math.ceil,_=Math.exp,x=(Math.floor,Math.log),w=Math.pow,A=Math.sin,E=Math.sign||function(t){return t>0?1:t<0?-1:0},M=Math.sqrt,k=Math.tan;function B(t){return t>1?0:t<-1?f:Math.acos(t)}function F(t){return t>1?s:t<-1?-s:Math.asin(t)}function T(t){return(t=A(t/2))*t}function N(){}function C(t,n){t&&O.hasOwnProperty(t.type)&&O[t.type](t,n)}var S={Feature:function(t,n){C(t.geometry,n)},FeatureCollection:function(t,n){for(var e=t.features,r=-1,i=e.length;++r<i;)C(e[r].geometry,n)}},O={Sphere:function(t,n){n.sphere()},Point:function(t,n){t=t.coordinates,n.point(t[0],t[1],t[2])},MultiPoint:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)t=e[r],n.point(t[0],t[1],t[2])},LineString:function(t,n){R(t.coordinates,n,0)},MultiLineString:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)R(e[r],n,0)},Polygon:function(t,n){P(t.coordinates,n)},MultiPolygon:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)P(e[r],n)},GeometryCollection:function(t,n){for(var e=t.geometries,r=-1,i=e.length;++r<i;)C(e[r],n)}};function R(t,n,e){var r,i=-1,o=t.length-e;for(n.lineStart();++i<o;)r=t[i],n.point(r[0],r[1],r[2]);n.lineEnd()}function P(t,n){var e=-1,r=t.length;for(n.polygonStart();++e<r;)R(t[e],n,1);n.polygonEnd()}var j,D,L,I,U,z=function(t,n){t&&S.hasOwnProperty(t.type)?S[t.type](t,n):C(t,n)},Y=r(),q=r(),H={point:N,lineStart:N,lineEnd:N,polygonStart:function(){Y.reset(),H.lineStart=$,H.lineEnd=V},polygonEnd:function(){var t=+Y;q.add(t<0?h+t:t),this.lineStart=this.lineEnd=this.point=N},sphere:function(){q.add(h)}};function $(){H.point=G}function V(){X(j,D)}function G(t,n){H.point=X,j=t,D=n,L=t*=p,I=m(n=(n*=p)/2+l),U=A(n)}function X(t,n){var e=(t*=p)-L,r=e>=0?1:-1,i=r*e,o=m(n=(n*=p)/2+l),u=A(n),a=U*u,c=I*o+a*m(i),f=a*r*A(i);Y.add(y(f,c)),L=t,I=o,U=u}var W=function(t){return q.reset(),z(t,H),2*q};function J(t){return[y(t[1],t[0]),F(t[2])]}function Z(t){var n=t[0],e=t[1],r=m(e);return[r*m(n),r*A(n),A(e)]}function Q(t,n){return t[0]*n[0]+t[1]*n[1]+t[2]*n[2]}function K(t,n){return[t[1]*n[2]-t[2]*n[1],t[2]*n[0]-t[0]*n[2],t[0]*n[1]-t[1]*n[0]]}function tt(t,n){t[0]+=n[0],t[1]+=n[1],t[2]+=n[2]}function nt(t,n){return[t[0]*n,t[1]*n,t[2]*n]}function et(t){var n=M(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]);t[0]/=n,t[1]/=n,t[2]/=n}var rt,it,ot,ut,at,ct,ft,st,lt,ht,dt=r(),pt={point:vt,lineStart:yt,lineEnd:mt,polygonStart:function(){pt.point=bt,pt.lineStart=_t,pt.lineEnd=xt,dt.reset(),H.polygonStart()},polygonEnd:function(){H.polygonEnd(),pt.point=vt,pt.lineStart=yt,pt.lineEnd=mt,Y<0?(rt=-(ot=180),it=-(ut=90)):dt>a?ut=90:dt<-a&&(it=-90),ht[0]=rt,ht[1]=ot},sphere:function(){rt=-(ot=180),it=-(ut=90)}};function vt(t,n){lt.push(ht=[rt=t,ot=t]),n<it&&(it=n),n>ut&&(ut=n)}function gt(t,n){var e=Z([t*p,n*p]);if(st){var r=K(st,e),i=K([r[1],-r[0],0],r);et(i),i=J(i);var o,u=t-at,a=u>0?1:-1,c=i[0]*d*a,f=v(u)>180;f^(a*at<c&&c<a*t)?(o=i[1]*d)>ut&&(ut=o):f^(a*at<(c=(c+360)%360-180)&&c<a*t)?(o=-i[1]*d)<it&&(it=o):(n<it&&(it=n),n>ut&&(ut=n)),f?t<at?wt(rt,t)>wt(rt,ot)&&(ot=t):wt(t,ot)>wt(rt,ot)&&(rt=t):ot>=rt?(t<rt&&(rt=t),t>ot&&(ot=t)):t>at?wt(rt,t)>wt(rt,ot)&&(ot=t):wt(t,ot)>wt(rt,ot)&&(rt=t)}else lt.push(ht=[rt=t,ot=t]);n<it&&(it=n),n>ut&&(ut=n),st=e,at=t}function yt(){pt.point=gt}function mt(){ht[0]=rt,ht[1]=ot,pt.point=vt,st=null}function bt(t,n){if(st){var e=t-at;dt.add(v(e)>180?e+(e>0?360:-360):e)}else ct=t,ft=n;H.point(t,n),gt(t,n)}function _t(){H.lineStart()}function xt(){bt(ct,ft),H.lineEnd(),v(dt)>a&&(rt=-(ot=180)),ht[0]=rt,ht[1]=ot,st=null}function wt(t,n){return(n-=t)<0?n+360:n}function At(t,n){return t[0]-n[0]}function Et(t,n){return t[0]<=t[1]?t[0]<=n&&n<=t[1]:n<t[0]||t[1]<n}var Mt,kt,Bt,Ft,Tt,Nt,Ct,St,Ot,Rt,Pt,jt,Dt,Lt,It,Ut,zt=function(t){var n,e,r,i,o,u,a;if(ut=ot=-(rt=it=1/0),lt=[],z(t,pt),e=lt.length){for(lt.sort(At),n=1,o=[r=lt[0]];n<e;++n)Et(r,(i=lt[n])[0])||Et(r,i[1])?(wt(r[0],i[1])>wt(r[0],r[1])&&(r[1]=i[1]),wt(i[0],r[1])>wt(r[0],r[1])&&(r[0]=i[0])):o.push(r=i);for(u=-1/0,n=0,r=o[e=o.length-1];n<=e;r=i,++n)i=o[n],(a=wt(r[1],i[0]))>u&&(u=a,rt=i[0],ot=r[1])}return lt=ht=null,rt===1/0||it===1/0?[[NaN,NaN],[NaN,NaN]]:[[rt,it],[ot,ut]]},Yt={sphere:N,point:qt,lineStart:$t,lineEnd:Xt,polygonStart:function(){Yt.lineStart=Wt,Yt.lineEnd=Jt},polygonEnd:function(){Yt.lineStart=$t,Yt.lineEnd=Xt}};function qt(t,n){t*=p;var e=m(n*=p);Ht(e*m(t),e*A(t),A(n))}function Ht(t,n,e){Bt+=(t-Bt)/++Mt,Ft+=(n-Ft)/Mt,Tt+=(e-Tt)/Mt}function $t(){Yt.point=Vt}function Vt(t,n){t*=p;var e=m(n*=p);Lt=e*m(t),It=e*A(t),Ut=A(n),Yt.point=Gt,Ht(Lt,It,Ut)}function Gt(t,n){t*=p;var e=m(n*=p),r=e*m(t),i=e*A(t),o=A(n),u=y(M((u=It*o-Ut*i)*u+(u=Ut*r-Lt*o)*u+(u=Lt*i-It*r)*u),Lt*r+It*i+Ut*o);kt+=u,Nt+=u*(Lt+(Lt=r)),Ct+=u*(It+(It=i)),St+=u*(Ut+(Ut=o)),Ht(Lt,It,Ut)}function Xt(){Yt.point=qt}function Wt(){Yt.point=Zt}function Jt(){Qt(jt,Dt),Yt.point=qt}function Zt(t,n){jt=t,Dt=n,t*=p,n*=p,Yt.point=Qt;var e=m(n);Lt=e*m(t),It=e*A(t),Ut=A(n),Ht(Lt,It,Ut)}function Qt(t,n){t*=p;var e=m(n*=p),r=e*m(t),i=e*A(t),o=A(n),u=It*o-Ut*i,a=Ut*r-Lt*o,c=Lt*i-It*r,f=M(u*u+a*a+c*c),s=F(f),l=f&&-s/f;Ot+=l*u,Rt+=l*a,Pt+=l*c,kt+=s,Nt+=s*(Lt+(Lt=r)),Ct+=s*(It+(It=i)),St+=s*(Ut+(Ut=o)),Ht(Lt,It,Ut)}var Kt=function(t){Mt=kt=Bt=Ft=Tt=Nt=Ct=St=Ot=Rt=Pt=0,z(t,Yt);var n=Ot,e=Rt,r=Pt,i=n*n+e*e+r*r;return i<c&&(n=Nt,e=Ct,r=St,kt<a&&(n=Bt,e=Ft,r=Tt),(i=n*n+e*e+r*r)<c)?[NaN,NaN]:[y(e,n)*d,F(r/M(i))*d]},tn=function(t){return function(){return t}},nn=function(t,n){function e(e,r){return e=t(e,r),n(e[0],e[1])}return t.invert&&n.invert&&(e.invert=function(e,r){return(e=n.invert(e,r))&&t.invert(e[0],e[1])}),e};function en(t,n){return[v(t)>f?t+Math.round(-t/h)*h:t,n]}function rn(t,n,e){return(t%=h)?n||e?nn(un(t),an(n,e)):un(t):n||e?an(n,e):en}function on(t){return function(n,e){return[(n+=t)>f?n-h:n<-f?n+h:n,e]}}function un(t){var n=on(t);return n.invert=on(-t),n}function an(t,n){var e=m(t),r=A(t),i=m(n),o=A(n);function u(t,n){var u=m(n),a=m(t)*u,c=A(t)*u,f=A(n),s=f*e+a*r;return[y(c*i-s*o,a*e-f*r),F(s*i+c*o)]}return u.invert=function(t,n){var u=m(n),a=m(t)*u,c=A(t)*u,f=A(n),s=f*i-c*o;return[y(c*i+f*o,a*e+s*r),F(s*e-a*r)]},u}en.invert=en;var cn=function(t){function n(n){return(n=t(n[0]*p,n[1]*p))[0]*=d,n[1]*=d,n}return t=rn(t[0]*p,t[1]*p,t.length>2?t[2]*p:0),n.invert=function(n){return(n=t.invert(n[0]*p,n[1]*p))[0]*=d,n[1]*=d,n},n};function fn(t,n,e,r,i,o){if(e){var u=m(n),a=A(n),c=r*e;null==i?(i=n+r*h,o=n-c/2):(i=sn(u,i),o=sn(u,o),(r>0?i<o:i>o)&&(i+=r*h));for(var f,s=i;r>0?s>o:s<o;s-=c)f=J([u,-a*m(s),-a*A(s)]),t.point(f[0],f[1])}}function sn(t,n){(n=Z(n))[0]-=t,et(n);var e=B(-n[1]);return((-n[2]<0?-e:e)+h-a)%h}var ln=function(){var t,n,e=tn([0,0]),r=tn(90),i=tn(6),o={point:function(e,r){t.push(e=n(e,r)),e[0]*=d,e[1]*=d}};function u(){var u=e.apply(this,arguments),a=r.apply(this,arguments)*p,c=i.apply(this,arguments)*p;return t=[],n=rn(-u[0]*p,-u[1]*p,0).invert,fn(o,a,c,1),u={type:"Polygon",coordinates:[t]},t=n=null,u}return u.center=function(t){return arguments.length?(e="function"==typeof t?t:tn([+t[0],+t[1]]),u):e},u.radius=function(t){return arguments.length?(r="function"==typeof t?t:tn(+t),u):r},u.precision=function(t){return arguments.length?(i="function"==typeof t?t:tn(+t),u):i},u},hn=function(){var t,n=[];return{point:function(n,e){t.push([n,e])},lineStart:function(){n.push(t=[])},lineEnd:N,rejoin:function(){n.length>1&&n.push(n.pop().concat(n.shift()))},result:function(){var e=n;return n=[],t=null,e}}},dn=function(t,n){return v(t[0]-n[0])<a&&v(t[1]-n[1])<a};function pn(t,n,e,r){this.x=t,this.z=n,this.o=e,this.e=r,this.v=!1,this.n=this.p=null}var vn=function(t,n,e,r,i){var o,u,a=[],c=[];if(t.forEach(function(t){if(!((n=t.length-1)<=0)){var n,e,r=t[0],u=t[n];if(dn(r,u)){for(i.lineStart(),o=0;o<n;++o)i.point((r=t[o])[0],r[1]);i.lineEnd()}else a.push(e=new pn(r,t,null,!0)),c.push(e.o=new pn(r,null,e,!1)),a.push(e=new pn(u,t,null,!1)),c.push(e.o=new pn(u,null,e,!0))}}),a.length){for(c.sort(n),gn(a),gn(c),o=0,u=c.length;o<u;++o)c[o].e=e=!e;for(var f,s,l=a[0];;){for(var h=l,d=!0;h.v;)if((h=h.n)===l)return;f=h.z,i.lineStart();do{if(h.v=h.o.v=!0,h.e){if(d)for(o=0,u=f.length;o<u;++o)i.point((s=f[o])[0],s[1]);else r(h.x,h.n.x,1,i);h=h.n}else{if(d)for(f=h.p.z,o=f.length-1;o>=0;--o)i.point((s=f[o])[0],s[1]);else r(h.x,h.p.x,-1,i);h=h.p}f=(h=h.o).z,d=!d}while(!h.v);i.lineEnd()}}};function gn(t){if(n=t.length){for(var n,e,r=0,i=t[0];++r<n;)i.n=e=t[r],e.p=i,i=e;i.n=e=t[0],e.p=i}}var yn=r();function mn(t){return v(t[0])<=f?t[0]:E(t[0])*((v(t[0])+f)%h-f)}var bn=function(t,n){var e=mn(n),r=n[1],i=A(r),o=[A(e),-m(e),0],u=0,c=0;yn.reset(),1===i?r=s+a:-1===i&&(r=-s-a);for(var d=0,p=t.length;d<p;++d)if(g=(v=t[d]).length)for(var v,g,b=v[g-1],_=mn(b),x=b[1]/2+l,w=A(x),E=m(x),M=0;M<g;++M,_=B,w=N,E=C,b=k){var k=v[M],B=mn(k),T=k[1]/2+l,N=A(T),C=m(T),S=B-_,O=S>=0?1:-1,R=O*S,P=R>f,j=w*N;if(yn.add(y(j*O*A(R),E*C+j*m(R))),u+=P?S+O*h:S,P^_>=e^B>=e){var D=K(Z(b),Z(k));et(D);var L=K(o,D);et(L);var I=(P^S>=0?-1:1)*F(L[2]);(r>I||r===I&&(D[0]||D[1]))&&(c+=P^S>=0?1:-1)}}return(u<-a||u<a&&yn<-a)^1&c},_n=e(35),xn=function(t,n,e,r){return function(i){var o,u,a,c=n(i),f=hn(),s=n(f),l=!1,h={point:d,lineStart:v,lineEnd:g,polygonStart:function(){h.point=y,h.lineStart=m,h.lineEnd=b,u=[],o=[]},polygonEnd:function(){h.point=d,h.lineStart=v,h.lineEnd=g,u=Object(_n.merge)(u);var t=bn(o,r);u.length?(l||(i.polygonStart(),l=!0),vn(u,An,t,e,i)):t&&(l||(i.polygonStart(),l=!0),i.lineStart(),e(null,null,1,i),i.lineEnd()),l&&(i.polygonEnd(),l=!1),u=o=null},sphere:function(){i.polygonStart(),i.lineStart(),e(null,null,1,i),i.lineEnd(),i.polygonEnd()}};function d(n,e){t(n,e)&&i.point(n,e)}function p(t,n){c.point(t,n)}function v(){h.point=p,c.lineStart()}function g(){h.point=d,c.lineEnd()}function y(t,n){a.push([t,n]),s.point(t,n)}function m(){s.lineStart(),a=[]}function b(){y(a[0][0],a[0][1]),s.lineEnd();var t,n,e,r,c=s.clean(),h=f.result(),d=h.length;if(a.pop(),o.push(a),a=null,d)if(1&c){if((n=(e=h[0]).length-1)>0){for(l||(i.polygonStart(),l=!0),i.lineStart(),t=0;t<n;++t)i.point((r=e[t])[0],r[1]);i.lineEnd()}}else d>1&&2&c&&h.push(h.pop().concat(h.shift())),u.push(h.filter(wn))}return h}};function wn(t){return t.length>1}function An(t,n){return((t=t.x)[0]<0?t[1]-s-a:s-t[1])-((n=n.x)[0]<0?n[1]-s-a:s-n[1])}var En=xn(function(){return!0},function(t){var n,e=NaN,r=NaN,i=NaN;return{lineStart:function(){t.lineStart(),n=1},point:function(o,u){var c=o>0?f:-f,l=v(o-e);v(l-f)<a?(t.point(e,r=(r+u)/2>0?s:-s),t.point(i,r),t.lineEnd(),t.lineStart(),t.point(c,r),t.point(o,r),n=0):i!==c&&l>=f&&(v(e-i)<a&&(e-=i*a),v(o-c)<a&&(o-=c*a),r=function(t,n,e,r){var i,o,u=A(t-e);return v(u)>a?g((A(n)*(o=m(r))*A(e)-A(r)*(i=m(n))*A(t))/(i*o*u)):(n+r)/2}(e,r,o,u),t.point(i,r),t.lineEnd(),t.lineStart(),t.point(c,r),n=0),t.point(e=o,r=u),i=c},lineEnd:function(){t.lineEnd(),e=r=NaN},clean:function(){return 2-n}}},function(t,n,e,r){var i;if(null==t)i=e*s,r.point(-f,i),r.point(0,i),r.point(f,i),r.point(f,0),r.point(f,-i),r.point(0,-i),r.point(-f,-i),r.point(-f,0),r.point(-f,i);else if(v(t[0]-n[0])>a){var o=t[0]<n[0]?f:-f;i=e*o/2,r.point(-o,i),r.point(0,i),r.point(o,i)}else r.point(n[0],n[1])},[-f,-s]);var Mn=function(t){var n=m(t),e=6*p,r=n>0,i=v(n)>a;function o(t,e){return m(t)*m(e)>n}function u(t,e,r){var i=[1,0,0],o=K(Z(t),Z(e)),u=Q(o,o),c=o[0],s=u-c*c;if(!s)return!r&&t;var l=n*u/s,h=-n*c/s,d=K(i,o),p=nt(i,l);tt(p,nt(o,h));var g=d,y=Q(p,g),m=Q(g,g),b=y*y-m*(Q(p,p)-1);if(!(b<0)){var _=M(b),x=nt(g,(-y-_)/m);if(tt(x,p),x=J(x),!r)return x;var w,A=t[0],E=e[0],k=t[1],B=e[1];E<A&&(w=A,A=E,E=w);var F=E-A,T=v(F-f)<a;if(!T&&B<k&&(w=k,k=B,B=w),T||F<a?T?k+B>0^x[1]<(v(x[0]-A)<a?k:B):k<=x[1]&&x[1]<=B:F>f^(A<=x[0]&&x[0]<=E)){var N=nt(g,(-y+_)/m);return tt(N,p),[x,J(N)]}}}function c(n,e){var i=r?t:f-t,o=0;return n<-i?o|=1:n>i&&(o|=2),e<-i?o|=4:e>i&&(o|=8),o}return xn(o,function(t){var n,e,s,l,h;return{lineStart:function(){l=s=!1,h=1},point:function(d,p){var v,g=[d,p],y=o(d,p),m=r?y?0:c(d,p):y?c(d+(d<0?f:-f),p):0;if(!n&&(l=s=y)&&t.lineStart(),y!==s&&(!(v=u(n,g))||dn(n,v)||dn(g,v))&&(g[0]+=a,g[1]+=a,y=o(g[0],g[1])),y!==s)h=0,y?(t.lineStart(),v=u(g,n),t.point(v[0],v[1])):(v=u(n,g),t.point(v[0],v[1]),t.lineEnd()),n=v;else if(i&&n&&r^y){var b;m&e||!(b=u(g,n,!0))||(h=0,r?(t.lineStart(),t.point(b[0][0],b[0][1]),t.point(b[1][0],b[1][1]),t.lineEnd()):(t.point(b[1][0],b[1][1]),t.lineEnd(),t.lineStart(),t.point(b[0][0],b[0][1])))}!y||n&&dn(n,g)||t.point(g[0],g[1]),n=g,s=y,e=m},lineEnd:function(){s&&t.lineEnd(),n=null},clean:function(){return h|(l&&s)<<1}}},function(n,r,i,o){fn(o,t,e,i,n,r)},r?[0,-t]:[-f,t-f])},kn=function(t,n,e,r,i,o){var u,a=t[0],c=t[1],f=0,s=1,l=n[0]-a,h=n[1]-c;if(u=e-a,l||!(u>0)){if(u/=l,l<0){if(u<f)return;u<s&&(s=u)}else if(l>0){if(u>s)return;u>f&&(f=u)}if(u=i-a,l||!(u<0)){if(u/=l,l<0){if(u>s)return;u>f&&(f=u)}else if(l>0){if(u<f)return;u<s&&(s=u)}if(u=r-c,h||!(u>0)){if(u/=h,h<0){if(u<f)return;u<s&&(s=u)}else if(h>0){if(u>s)return;u>f&&(f=u)}if(u=o-c,h||!(u<0)){if(u/=h,h<0){if(u>s)return;u>f&&(f=u)}else if(h>0){if(u<f)return;u<s&&(s=u)}return f>0&&(t[0]=a+f*l,t[1]=c+f*h),s<1&&(n[0]=a+s*l,n[1]=c+s*h),!0}}}}},Bn=1e9,Fn=-Bn;function Tn(t,n,e,r){function i(i,o){return t<=i&&i<=e&&n<=o&&o<=r}function o(i,o,a,c){var s=0,l=0;if(null==i||(s=u(i,a))!==(l=u(o,a))||f(i,o)<0^a>0)do{c.point(0===s||3===s?t:e,s>1?r:n)}while((s=(s+a+4)%4)!==l);else c.point(o[0],o[1])}function u(r,i){return v(r[0]-t)<a?i>0?0:3:v(r[0]-e)<a?i>0?2:1:v(r[1]-n)<a?i>0?1:0:i>0?3:2}function c(t,n){return f(t.x,n.x)}function f(t,n){var e=u(t,1),r=u(n,1);return e!==r?e-r:0===e?n[1]-t[1]:1===e?t[0]-n[0]:2===e?t[1]-n[1]:n[0]-t[0]}return function(u){var a,f,s,l,h,d,p,v,g,y,m,b=u,_=hn(),x={point:w,lineStart:function(){x.point=A,f&&f.push(s=[]);y=!0,g=!1,p=v=NaN},lineEnd:function(){a&&(A(l,h),d&&g&&_.rejoin(),a.push(_.result()));x.point=w,g&&b.lineEnd()},polygonStart:function(){b=_,a=[],f=[],m=!0},polygonEnd:function(){var n=function(){for(var n=0,e=0,i=f.length;e<i;++e)for(var o,u,a=f[e],c=1,s=a.length,l=a[0],h=l[0],d=l[1];c<s;++c)o=h,u=d,l=a[c],h=l[0],d=l[1],u<=r?d>r&&(h-o)*(r-u)>(d-u)*(t-o)&&++n:d<=r&&(h-o)*(r-u)<(d-u)*(t-o)&&--n;return n}(),e=m&&n,i=(a=Object(_n.merge)(a)).length;(e||i)&&(u.polygonStart(),e&&(u.lineStart(),o(null,null,1,u),u.lineEnd()),i&&vn(a,c,n,o,u),u.polygonEnd());b=u,a=f=s=null}};function w(t,n){i(t,n)&&b.point(t,n)}function A(o,u){var a=i(o,u);if(f&&s.push([o,u]),y)l=o,h=u,d=a,y=!1,a&&(b.lineStart(),b.point(o,u));else if(a&&g)b.point(o,u);else{var c=[p=Math.max(Fn,Math.min(Bn,p)),v=Math.max(Fn,Math.min(Bn,v))],_=[o=Math.max(Fn,Math.min(Bn,o)),u=Math.max(Fn,Math.min(Bn,u))];kn(c,_,t,n,e,r)?(g||(b.lineStart(),b.point(c[0],c[1])),b.point(_[0],_[1]),a||b.lineEnd(),m=!1):a&&(b.lineStart(),b.point(o,u),m=!1)}p=o,v=u,g=a}return x}}var Nn,Cn,Sn,On=function(){var t,n,e,r=0,i=0,o=960,u=500;return e={stream:function(e){return t&&n===e?t:t=Tn(r,i,o,u)(n=e)},extent:function(a){return arguments.length?(r=+a[0][0],i=+a[0][1],o=+a[1][0],u=+a[1][1],t=n=null,e):[[r,i],[o,u]]}}},Rn=r(),Pn={sphere:N,point:N,lineStart:function(){Pn.point=Dn,Pn.lineEnd=jn},lineEnd:N,polygonStart:N,polygonEnd:N};function jn(){Pn.point=Pn.lineEnd=N}function Dn(t,n){Nn=t*=p,Cn=A(n*=p),Sn=m(n),Pn.point=Ln}function Ln(t,n){t*=p;var e=A(n*=p),r=m(n),i=v(t-Nn),o=m(i),u=r*A(i),a=Sn*e-Cn*r*o,c=Cn*e+Sn*r*o;Rn.add(y(M(u*u+a*a),c)),Nn=t,Cn=e,Sn=r}var In=function(t){return Rn.reset(),z(t,Pn),+Rn},Un=[null,null],zn={type:"LineString",coordinates:Un},Yn=function(t,n){return Un[0]=t,Un[1]=n,In(zn)},qn={Feature:function(t,n){return $n(t.geometry,n)},FeatureCollection:function(t,n){for(var e=t.features,r=-1,i=e.length;++r<i;)if($n(e[r].geometry,n))return!0;return!1}},Hn={Sphere:function(){return!0},Point:function(t,n){return Vn(t.coordinates,n)},MultiPoint:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(Vn(e[r],n))return!0;return!1},LineString:function(t,n){return Gn(t.coordinates,n)},MultiLineString:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(Gn(e[r],n))return!0;return!1},Polygon:function(t,n){return Xn(t.coordinates,n)},MultiPolygon:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(Xn(e[r],n))return!0;return!1},GeometryCollection:function(t,n){for(var e=t.geometries,r=-1,i=e.length;++r<i;)if($n(e[r],n))return!0;return!1}};function $n(t,n){return!(!t||!Hn.hasOwnProperty(t.type))&&Hn[t.type](t,n)}function Vn(t,n){return 0===Yn(t,n)}function Gn(t,n){for(var e,r,i,o=0,u=t.length;o<u;o++){if(0===(r=Yn(t[o],n)))return!0;if(o>0&&(i=Yn(t[o],t[o-1]))>0&&e<=i&&r<=i&&(e+r-i)*(1-Math.pow((e-r)/i,2))<c*i)return!0;e=r}return!1}function Xn(t,n){return!!bn(t.map(Wn),Jn(n))}function Wn(t){return(t=t.map(Jn)).pop(),t}function Jn(t){return[t[0]*p,t[1]*p]}var Zn=function(t,n){return(t&&qn.hasOwnProperty(t.type)?qn[t.type]:$n)(t,n)};function Qn(t,n,e){var r=Object(_n.range)(t,n-a,e).concat(n);return function(t){return r.map(function(n){return[t,n]})}}function Kn(t,n,e){var r=Object(_n.range)(t,n-a,e).concat(n);return function(t){return r.map(function(n){return[n,t]})}}function te(){var t,n,e,r,i,o,u,c,f,s,l,h,d=10,p=d,g=90,y=360,m=2.5;function _(){return{type:"MultiLineString",coordinates:x()}}function x(){return Object(_n.range)(b(r/g)*g,e,g).map(l).concat(Object(_n.range)(b(c/y)*y,u,y).map(h)).concat(Object(_n.range)(b(n/d)*d,t,d).filter(function(t){return v(t%g)>a}).map(f)).concat(Object(_n.range)(b(o/p)*p,i,p).filter(function(t){return v(t%y)>a}).map(s))}return _.lines=function(){return x().map(function(t){return{type:"LineString",coordinates:t}})},_.outline=function(){return{type:"Polygon",coordinates:[l(r).concat(h(u).slice(1),l(e).reverse().slice(1),h(c).reverse().slice(1))]}},_.extent=function(t){return arguments.length?_.extentMajor(t).extentMinor(t):_.extentMinor()},_.extentMajor=function(t){return arguments.length?(r=+t[0][0],e=+t[1][0],c=+t[0][1],u=+t[1][1],r>e&&(t=r,r=e,e=t),c>u&&(t=c,c=u,u=t),_.precision(m)):[[r,c],[e,u]]},_.extentMinor=function(e){return arguments.length?(n=+e[0][0],t=+e[1][0],o=+e[0][1],i=+e[1][1],n>t&&(e=n,n=t,t=e),o>i&&(e=o,o=i,i=e),_.precision(m)):[[n,o],[t,i]]},_.step=function(t){return arguments.length?_.stepMajor(t).stepMinor(t):_.stepMinor()},_.stepMajor=function(t){return arguments.length?(g=+t[0],y=+t[1],_):[g,y]},_.stepMinor=function(t){return arguments.length?(d=+t[0],p=+t[1],_):[d,p]},_.precision=function(a){return arguments.length?(m=+a,f=Qn(o,i,90),s=Kn(n,t,m),l=Qn(c,u,90),h=Kn(r,e,m),_):m},_.extentMajor([[-180,-90+a],[180,90-a]]).extentMinor([[-180,-80-a],[180,80+a]])}function ne(){return te()()}var ee,re,ie,oe,ue=function(t,n){var e=t[0]*p,r=t[1]*p,i=n[0]*p,o=n[1]*p,u=m(r),a=A(r),c=m(o),f=A(o),s=u*m(e),l=u*A(e),h=c*m(i),v=c*A(i),g=2*F(M(T(o-r)+u*c*T(i-e))),b=A(g),_=g?function(t){var n=A(t*=g)/b,e=A(g-t)/b,r=e*s+n*h,i=e*l+n*v,o=e*a+n*f;return[y(i,r)*d,y(o,M(r*r+i*i))*d]}:function(){return[e*d,r*d]};return _.distance=g,_},ae=function(t){return t},ce=r(),fe=r(),se={point:N,lineStart:N,lineEnd:N,polygonStart:function(){se.lineStart=le,se.lineEnd=pe},polygonEnd:function(){se.lineStart=se.lineEnd=se.point=N,ce.add(v(fe)),fe.reset()},result:function(){var t=ce/2;return ce.reset(),t}};function le(){se.point=he}function he(t,n){se.point=de,ee=ie=t,re=oe=n}function de(t,n){fe.add(oe*t-ie*n),ie=t,oe=n}function pe(){de(ee,re)}var ve=se,ge=1/0,ye=ge,me=-ge,be=me;var _e,xe,we,Ae,Ee={point:function(t,n){t<ge&&(ge=t);t>me&&(me=t);n<ye&&(ye=n);n>be&&(be=n)},lineStart:N,lineEnd:N,polygonStart:N,polygonEnd:N,result:function(){var t=[[ge,ye],[me,be]];return me=be=-(ye=ge=1/0),t}},Me=0,ke=0,Be=0,Fe=0,Te=0,Ne=0,Ce=0,Se=0,Oe=0,Re={point:Pe,lineStart:je,lineEnd:Ie,polygonStart:function(){Re.lineStart=Ue,Re.lineEnd=ze},polygonEnd:function(){Re.point=Pe,Re.lineStart=je,Re.lineEnd=Ie},result:function(){var t=Oe?[Ce/Oe,Se/Oe]:Ne?[Fe/Ne,Te/Ne]:Be?[Me/Be,ke/Be]:[NaN,NaN];return Me=ke=Be=Fe=Te=Ne=Ce=Se=Oe=0,t}};function Pe(t,n){Me+=t,ke+=n,++Be}function je(){Re.point=De}function De(t,n){Re.point=Le,Pe(we=t,Ae=n)}function Le(t,n){var e=t-we,r=n-Ae,i=M(e*e+r*r);Fe+=i*(we+t)/2,Te+=i*(Ae+n)/2,Ne+=i,Pe(we=t,Ae=n)}function Ie(){Re.point=Pe}function Ue(){Re.point=Ye}function ze(){qe(_e,xe)}function Ye(t,n){Re.point=qe,Pe(_e=we=t,xe=Ae=n)}function qe(t,n){var e=t-we,r=n-Ae,i=M(e*e+r*r);Fe+=i*(we+t)/2,Te+=i*(Ae+n)/2,Ne+=i,Ce+=(i=Ae*t-we*n)*(we+t),Se+=i*(Ae+n),Oe+=3*i,Pe(we=t,Ae=n)}var He=Re;function $e(t){this._context=t}$e.prototype={_radius:4.5,pointRadius:function(t){return this._radius=t,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){0===this._line&&this._context.closePath(),this._point=NaN},point:function(t,n){switch(this._point){case 0:this._context.moveTo(t,n),this._point=1;break;case 1:this._context.lineTo(t,n);break;default:this._context.moveTo(t+this._radius,n),this._context.arc(t,n,this._radius,0,h)}},result:N};var Ve,Ge,Xe,We,Je,Ze=r(),Qe={point:N,lineStart:function(){Qe.point=Ke},lineEnd:function(){Ve&&tr(Ge,Xe),Qe.point=N},polygonStart:function(){Ve=!0},polygonEnd:function(){Ve=null},result:function(){var t=+Ze;return Ze.reset(),t}};function Ke(t,n){Qe.point=tr,Ge=We=t,Xe=Je=n}function tr(t,n){We-=t,Je-=n,Ze.add(M(We*We+Je*Je)),We=t,Je=n}var nr=Qe;function er(){this._string=[]}function rr(t){return"m0,"+t+"a"+t+","+t+" 0 1,1 0,"+-2*t+"a"+t+","+t+" 0 1,1 0,"+2*t+"z"}er.prototype={_radius:4.5,_circle:rr(4.5),pointRadius:function(t){return(t=+t)!==this._radius&&(this._radius=t,this._circle=null),this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){0===this._line&&this._string.push("Z"),this._point=NaN},point:function(t,n){switch(this._point){case 0:this._string.push("M",t,",",n),this._point=1;break;case 1:this._string.push("L",t,",",n);break;default:null==this._circle&&(this._circle=rr(this._radius)),this._string.push("M",t,",",n,this._circle)}},result:function(){if(this._string.length){var t=this._string.join("");return this._string=[],t}return null}};var ir=function(t,n){var e,r,i=4.5;function o(t){return t&&("function"==typeof i&&r.pointRadius(+i.apply(this,arguments)),z(t,e(r))),r.result()}return o.area=function(t){return z(t,e(ve)),ve.result()},o.measure=function(t){return z(t,e(nr)),nr.result()},o.bounds=function(t){return z(t,e(Ee)),Ee.result()},o.centroid=function(t){return z(t,e(He)),He.result()},o.projection=function(n){return arguments.length?(e=null==n?(t=null,ae):(t=n).stream,o):t},o.context=function(t){return arguments.length?(r=null==t?(n=null,new er):new $e(n=t),"function"!=typeof i&&r.pointRadius(i),o):n},o.pointRadius=function(t){return arguments.length?(i="function"==typeof t?t:(r.pointRadius(+t),+t),o):i},o.projection(t).context(n)},or=function(t){return{stream:ur(t)}};function ur(t){return function(n){var e=new ar;for(var r in t)e[r]=t[r];return e.stream=n,e}}function ar(){}function cr(t,n,e){var r=t.clipExtent&&t.clipExtent();return t.scale(150).translate([0,0]),null!=r&&t.clipExtent(null),z(e,t.stream(Ee)),n(Ee.result()),null!=r&&t.clipExtent(r),t}function fr(t,n,e){return cr(t,function(e){var r=n[1][0]-n[0][0],i=n[1][1]-n[0][1],o=Math.min(r/(e[1][0]-e[0][0]),i/(e[1][1]-e[0][1])),u=+n[0][0]+(r-o*(e[1][0]+e[0][0]))/2,a=+n[0][1]+(i-o*(e[1][1]+e[0][1]))/2;t.scale(150*o).translate([u,a])},e)}function sr(t,n,e){return fr(t,[[0,0],n],e)}function lr(t,n,e){return cr(t,function(e){var r=+n,i=r/(e[1][0]-e[0][0]),o=(r-i*(e[1][0]+e[0][0]))/2,u=-i*e[0][1];t.scale(150*i).translate([o,u])},e)}function hr(t,n,e){return cr(t,function(e){var r=+n,i=r/(e[1][1]-e[0][1]),o=-i*e[0][0],u=(r-i*(e[1][1]+e[0][1]))/2;t.scale(150*i).translate([o,u])},e)}ar.prototype={constructor:ar,point:function(t,n){this.stream.point(t,n)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};var dr=16,pr=m(30*p),vr=function(t,n){return+n?function(t,n){function e(r,i,o,u,c,f,s,l,h,d,p,g,m,b){var _=s-r,x=l-i,w=_*_+x*x;if(w>4*n&&m--){var A=u+d,E=c+p,k=f+g,B=M(A*A+E*E+k*k),T=F(k/=B),N=v(v(k)-1)<a||v(o-h)<a?(o+h)/2:y(E,A),C=t(N,T),S=C[0],O=C[1],R=S-r,P=O-i,j=x*R-_*P;(j*j/w>n||v((_*R+x*P)/w-.5)>.3||u*d+c*p+f*g<pr)&&(e(r,i,o,u,c,f,S,O,N,A/=B,E/=B,k,m,b),b.point(S,O),e(S,O,N,A,E,k,s,l,h,d,p,g,m,b))}}return function(n){var r,i,o,u,a,c,f,s,l,h,d,p,v={point:g,lineStart:y,lineEnd:b,polygonStart:function(){n.polygonStart(),v.lineStart=_},polygonEnd:function(){n.polygonEnd(),v.lineStart=y}};function g(e,r){e=t(e,r),n.point(e[0],e[1])}function y(){s=NaN,v.point=m,n.lineStart()}function m(r,i){var o=Z([r,i]),u=t(r,i);e(s,l,f,h,d,p,s=u[0],l=u[1],f=r,h=o[0],d=o[1],p=o[2],dr,n),n.point(s,l)}function b(){v.point=g,n.lineEnd()}function _(){y(),v.point=x,v.lineEnd=w}function x(t,n){m(r=t,n),i=s,o=l,u=h,a=d,c=p,v.point=m}function w(){e(s,l,f,h,d,p,i,o,r,u,a,c,dr,n),v.lineEnd=b,b()}return v}}(t,n):function(t){return ur({point:function(n,e){n=t(n,e),this.stream.point(n[0],n[1])}})}(t)};var gr=ur({point:function(t,n){this.stream.point(t*p,n*p)}});function yr(t,n,e){function r(r,i){return[n+t*r,e-t*i]}return r.invert=function(r,i){return[(r-n)/t,(e-i)/t]},r}function mr(t,n,e,r){var i=m(r),o=A(r),u=i*t,a=o*t,c=i/t,f=o/t,s=(o*e-i*n)/t,l=(o*n+i*e)/t;function h(t,r){return[u*t-a*r+n,e-a*t-u*r]}return h.invert=function(t,n){return[c*t-f*n+s,l-f*t-c*n]},h}function br(t){return _r(function(){return t})()}function _r(t){var n,e,r,i,o,u,a,c,f,s,l=150,h=480,v=250,g=0,y=0,m=0,b=0,_=0,x=0,w=null,A=En,E=null,k=ae,B=.5;function F(t){return c(t[0]*p,t[1]*p)}function T(t){return(t=c.invert(t[0],t[1]))&&[t[0]*d,t[1]*d]}function N(){var t=mr(l,0,0,x).apply(null,n(g,y)),r=(x?mr:yr)(l,h-t[0],v-t[1],x);return e=rn(m,b,_),a=nn(n,r),c=nn(e,a),u=vr(a,B),C()}function C(){return f=s=null,F}return F.stream=function(t){return f&&s===t?f:f=gr(function(t){return ur({point:function(n,e){var r=t(n,e);return this.stream.point(r[0],r[1])}})}(e)(A(u(k(s=t)))))},F.preclip=function(t){return arguments.length?(A=t,w=void 0,C()):A},F.postclip=function(t){return arguments.length?(k=t,E=r=i=o=null,C()):k},F.clipAngle=function(t){return arguments.length?(A=+t?Mn(w=t*p):(w=null,En),C()):w*d},F.clipExtent=function(t){return arguments.length?(k=null==t?(E=r=i=o=null,ae):Tn(E=+t[0][0],r=+t[0][1],i=+t[1][0],o=+t[1][1]),C()):null==E?null:[[E,r],[i,o]]},F.scale=function(t){return arguments.length?(l=+t,N()):l},F.translate=function(t){return arguments.length?(h=+t[0],v=+t[1],N()):[h,v]},F.center=function(t){return arguments.length?(g=t[0]%360*p,y=t[1]%360*p,N()):[g*d,y*d]},F.rotate=function(t){return arguments.length?(m=t[0]%360*p,b=t[1]%360*p,_=t.length>2?t[2]%360*p:0,N()):[m*d,b*d,_*d]},F.angle=function(t){return arguments.length?(x=t%360*p,N()):x*d},F.precision=function(t){return arguments.length?(u=vr(a,B=t*t),C()):M(B)},F.fitExtent=function(t,n){return fr(F,t,n)},F.fitSize=function(t,n){return sr(F,t,n)},F.fitWidth=function(t,n){return lr(F,t,n)},F.fitHeight=function(t,n){return hr(F,t,n)},function(){return n=t.apply(this,arguments),F.invert=n.invert&&T,N()}}function xr(t){var n=0,e=f/3,r=_r(t),i=r(n,e);return i.parallels=function(t){return arguments.length?r(n=t[0]*p,e=t[1]*p):[n*d,e*d]},i}function wr(t,n){var e=A(t),r=(e+A(n))/2;if(v(r)<a)return function(t){var n=m(t);function e(t,e){return[t*n,A(e)/n]}return e.invert=function(t,e){return[t/n,F(e*n)]},e}(t);var i=1+e*(2*r-e),o=M(i)/r;function u(t,n){var e=M(i-2*r*A(n))/r;return[e*A(t*=r),o-e*m(t)]}return u.invert=function(t,n){var e=o-n;return[y(t,v(e))/r*E(e),F((i-(t*t+e*e)*r*r)/(2*r))]},u}var Ar=function(){return xr(wr).scale(155.424).center([0,33.6442])},Er=function(){return Ar().parallels([29.5,45.5]).scale(1070).translate([480,250]).rotate([96,0]).center([-.6,38.7])};var Mr=function(){var t,n,e,r,i,o,u=Er(),c=Ar().rotate([154,0]).center([-2,58.5]).parallels([55,65]),f=Ar().rotate([157,0]).center([-3,19.9]).parallels([8,18]),s={point:function(t,n){o=[t,n]}};function l(t){var n=t[0],u=t[1];return o=null,e.point(n,u),o||(r.point(n,u),o)||(i.point(n,u),o)}function h(){return t=n=null,l}return l.invert=function(t){var n=u.scale(),e=u.translate(),r=(t[0]-e[0])/n,i=(t[1]-e[1])/n;return(i>=.12&&i<.234&&r>=-.425&&r<-.214?c:i>=.166&&i<.234&&r>=-.214&&r<-.115?f:u).invert(t)},l.stream=function(e){return t&&n===e?t:(r=[u.stream(n=e),c.stream(e),f.stream(e)],i=r.length,t={point:function(t,n){for(var e=-1;++e<i;)r[e].point(t,n)},sphere:function(){for(var t=-1;++t<i;)r[t].sphere()},lineStart:function(){for(var t=-1;++t<i;)r[t].lineStart()},lineEnd:function(){for(var t=-1;++t<i;)r[t].lineEnd()},polygonStart:function(){for(var t=-1;++t<i;)r[t].polygonStart()},polygonEnd:function(){for(var t=-1;++t<i;)r[t].polygonEnd()}});var r,i},l.precision=function(t){return arguments.length?(u.precision(t),c.precision(t),f.precision(t),h()):u.precision()},l.scale=function(t){return arguments.length?(u.scale(t),c.scale(.35*t),f.scale(t),l.translate(u.translate())):u.scale()},l.translate=function(t){if(!arguments.length)return u.translate();var n=u.scale(),o=+t[0],l=+t[1];return e=u.translate(t).clipExtent([[o-.455*n,l-.238*n],[o+.455*n,l+.238*n]]).stream(s),r=c.translate([o-.307*n,l+.201*n]).clipExtent([[o-.425*n+a,l+.12*n+a],[o-.214*n-a,l+.234*n-a]]).stream(s),i=f.translate([o-.205*n,l+.212*n]).clipExtent([[o-.214*n+a,l+.166*n+a],[o-.115*n-a,l+.234*n-a]]).stream(s),h()},l.fitExtent=function(t,n){return fr(l,t,n)},l.fitSize=function(t,n){return sr(l,t,n)},l.fitWidth=function(t,n){return lr(l,t,n)},l.fitHeight=function(t,n){return hr(l,t,n)},l.scale(1070)};function kr(t){return function(n,e){var r=m(n),i=m(e),o=t(r*i);return[o*i*A(n),o*A(e)]}}function Br(t){return function(n,e){var r=M(n*n+e*e),i=t(r),o=A(i),u=m(i);return[y(n*o,r*u),F(r&&e*o/r)]}}var Fr=kr(function(t){return M(2/(1+t))});Fr.invert=Br(function(t){return 2*F(t/2)});var Tr=function(){return br(Fr).scale(124.75).clipAngle(179.999)},Nr=kr(function(t){return(t=B(t))&&t/A(t)});Nr.invert=Br(function(t){return t});var Cr=function(){return br(Nr).scale(79.4188).clipAngle(179.999)};function Sr(t,n){return[t,x(k((s+n)/2))]}Sr.invert=function(t,n){return[t,2*g(_(n))-s]};var Or=function(){return Rr(Sr).scale(961/h)};function Rr(t){var n,e,r,i=br(t),o=i.center,u=i.scale,a=i.translate,c=i.clipExtent,s=null;function l(){var o=f*u(),a=i(cn(i.rotate()).invert([0,0]));return c(null==s?[[a[0]-o,a[1]-o],[a[0]+o,a[1]+o]]:t===Sr?[[Math.max(a[0]-o,s),n],[Math.min(a[0]+o,e),r]]:[[s,Math.max(a[1]-o,n)],[e,Math.min(a[1]+o,r)]])}return i.scale=function(t){return arguments.length?(u(t),l()):u()},i.translate=function(t){return arguments.length?(a(t),l()):a()},i.center=function(t){return arguments.length?(o(t),l()):o()},i.clipExtent=function(t){return arguments.length?(null==t?s=n=e=r=null:(s=+t[0][0],n=+t[0][1],e=+t[1][0],r=+t[1][1]),l()):null==s?null:[[s,n],[e,r]]},l()}function Pr(t){return k((s+t)/2)}function jr(t,n){var e=m(t),r=t===n?A(t):x(e/m(n))/x(Pr(n)/Pr(t)),i=e*w(Pr(t),r)/r;if(!r)return Sr;function o(t,n){i>0?n<-s+a&&(n=-s+a):n>s-a&&(n=s-a);var e=i/w(Pr(n),r);return[e*A(r*t),i-e*m(r*t)]}return o.invert=function(t,n){var e=i-n,o=E(r)*M(t*t+e*e);return[y(t,v(e))/r*E(e),2*g(w(i/o,1/r))-s]},o}var Dr=function(){return xr(jr).scale(109.5).parallels([30,30])};function Lr(t,n){return[t,n]}Lr.invert=Lr;var Ir=function(){return br(Lr).scale(152.63)};function Ur(t,n){var e=m(t),r=t===n?A(t):(e-m(n))/(n-t),i=e/r+t;if(v(r)<a)return Lr;function o(t,n){var e=i-n,o=r*t;return[e*A(o),i-e*m(o)]}return o.invert=function(t,n){var e=i-n;return[y(t,v(e))/r*E(e),i-E(r)*M(t*t+e*e)]},o}var zr=function(){return xr(Ur).scale(131.154).center([0,13.9389])},Yr=1.340264,qr=-.081106,Hr=893e-6,$r=.003796,Vr=M(3)/2;function Gr(t,n){var e=F(Vr*A(n)),r=e*e,i=r*r*r;return[t*m(e)/(Vr*(Yr+3*qr*r+i*(7*Hr+9*$r*r))),e*(Yr+qr*r+i*(Hr+$r*r))]}Gr.invert=function(t,n){for(var e,r=n,i=r*r,o=i*i*i,u=0;u<12&&(o=(i=(r-=e=(r*(Yr+qr*i+o*(Hr+$r*i))-n)/(Yr+3*qr*i+o*(7*Hr+9*$r*i)))*r)*i*i,!(v(e)<c));++u);return[Vr*t*(Yr+3*qr*i+o*(7*Hr+9*$r*i))/m(r),F(A(r)/Vr)]};var Xr=function(){return br(Gr).scale(177.158)};function Wr(t,n){var e=m(n),r=m(t)*e;return[e*A(t)/r,A(n)/r]}Wr.invert=Br(g);var Jr=function(){return br(Wr).scale(144.049).clipAngle(60)};function Zr(t,n,e,r){return 1===t&&1===n&&0===e&&0===r?ae:ur({point:function(i,o){this.stream.point(i*t+e,o*n+r)}})}var Qr=function(){var t,n,e,r,i,o,u=1,a=0,c=0,f=1,s=1,l=ae,h=null,d=ae;function p(){return r=i=null,o}return o={stream:function(t){return r&&i===t?r:r=l(d(i=t))},postclip:function(r){return arguments.length?(d=r,h=t=n=e=null,p()):d},clipExtent:function(r){return arguments.length?(d=null==r?(h=t=n=e=null,ae):Tn(h=+r[0][0],t=+r[0][1],n=+r[1][0],e=+r[1][1]),p()):null==h?null:[[h,t],[n,e]]},scale:function(t){return arguments.length?(l=Zr((u=+t)*f,u*s,a,c),p()):u},translate:function(t){return arguments.length?(l=Zr(u*f,u*s,a=+t[0],c=+t[1]),p()):[a,c]},reflectX:function(t){return arguments.length?(l=Zr(u*(f=t?-1:1),u*s,a,c),p()):f<0},reflectY:function(t){return arguments.length?(l=Zr(u*f,u*(s=t?-1:1),a,c),p()):s<0},fitExtent:function(t,n){return fr(o,t,n)},fitSize:function(t,n){return sr(o,t,n)},fitWidth:function(t,n){return lr(o,t,n)},fitHeight:function(t,n){return hr(o,t,n)}}};function Kr(t,n){var e=n*n,r=e*e;return[t*(.8707-.131979*e+r*(r*(.003971*e-.001529*r)-.013791)),n*(1.007226+e*(.015085+r*(.028874*e-.044475-.005916*r)))]}Kr.invert=function(t,n){var e,r=n,i=25;do{var o=r*r,u=o*o;r-=e=(r*(1.007226+o*(.015085+u*(.028874*o-.044475-.005916*u)))-n)/(1.007226+o*(.045255+u*(.259866*o-.311325-.005916*11*u)))}while(v(e)>a&&--i>0);return[t/(.8707+(o=r*r)*(o*(o*o*o*(.003971-.001529*o)-.013791)-.131979)),r]};var ti=function(){return br(Kr).scale(175.295)};function ni(t,n){return[m(n)*A(t),A(n)]}ni.invert=Br(F);var ei=function(){return br(ni).scale(249.5).clipAngle(90+a)};function ri(t,n){var e=m(n),r=1+m(t)*e;return[e*A(t)/r,A(n)/r]}ri.invert=Br(function(t){return 2*g(t)});var ii=function(){return br(ri).scale(250).clipAngle(142)};function oi(t,n){return[x(k((s+n)/2)),-t]}oi.invert=function(t,n){return[-n,2*g(_(t))-s]};var ui=function(){var t=Rr(oi),n=t.center,e=t.rotate;return t.center=function(t){return arguments.length?n([-t[1],t[0]]):[(t=n())[1],-t[0]]},t.rotate=function(t){return arguments.length?e([t[0],t[1],t.length>2?t[2]+90:90]):[(t=e())[0],t[1],t[2]-90]},e([0,0,90]).scale(159.155)};e.d(n,"geoArea",function(){return W}),e.d(n,"geoBounds",function(){return zt}),e.d(n,"geoCentroid",function(){return Kt}),e.d(n,"geoCircle",function(){return ln}),e.d(n,"geoClipAntimeridian",function(){return En}),e.d(n,"geoClipCircle",function(){return Mn}),e.d(n,"geoClipExtent",function(){return On}),e.d(n,"geoClipRectangle",function(){return Tn}),e.d(n,"geoContains",function(){return Zn}),e.d(n,"geoDistance",function(){return Yn}),e.d(n,"geoGraticule",function(){return te}),e.d(n,"geoGraticule10",function(){return ne}),e.d(n,"geoInterpolate",function(){return ue}),e.d(n,"geoLength",function(){return In}),e.d(n,"geoPath",function(){return ir}),e.d(n,"geoAlbers",function(){return Er}),e.d(n,"geoAlbersUsa",function(){return Mr}),e.d(n,"geoAzimuthalEqualArea",function(){return Tr}),e.d(n,"geoAzimuthalEqualAreaRaw",function(){return Fr}),e.d(n,"geoAzimuthalEquidistant",function(){return Cr}),e.d(n,"geoAzimuthalEquidistantRaw",function(){return Nr}),e.d(n,"geoConicConformal",function(){return Dr}),e.d(n,"geoConicConformalRaw",function(){return jr}),e.d(n,"geoConicEqualArea",function(){return Ar}),e.d(n,"geoConicEqualAreaRaw",function(){return wr}),e.d(n,"geoConicEquidistant",function(){return zr}),e.d(n,"geoConicEquidistantRaw",function(){return Ur}),e.d(n,"geoEqualEarth",function(){return Xr}),e.d(n,"geoEqualEarthRaw",function(){return Gr}),e.d(n,"geoEquirectangular",function(){return Ir}),e.d(n,"geoEquirectangularRaw",function(){return Lr}),e.d(n,"geoGnomonic",function(){return Jr}),e.d(n,"geoGnomonicRaw",function(){return Wr}),e.d(n,"geoIdentity",function(){return Qr}),e.d(n,"geoProjection",function(){return br}),e.d(n,"geoProjectionMutator",function(){return _r}),e.d(n,"geoMercator",function(){return Or}),e.d(n,"geoMercatorRaw",function(){return Sr}),e.d(n,"geoNaturalEarth1",function(){return ti}),e.d(n,"geoNaturalEarth1Raw",function(){return Kr}),e.d(n,"geoOrthographic",function(){return ei}),e.d(n,"geoOrthographicRaw",function(){return ni}),e.d(n,"geoStereographic",function(){return ii}),e.d(n,"geoStereographicRaw",function(){return ri}),e.d(n,"geoTransverseMercator",function(){return ui}),e.d(n,"geoTransverseMercatorRaw",function(){return oi}),e.d(n,"geoRotation",function(){return cn}),e.d(n,"geoStream",function(){return z}),e.d(n,"geoTransform",function(){return or})},1314:function(t,n,e){"use strict";e.r(n);var r=e(102),i=function(t){return function(){return t}},o=Math.abs,u=Math.atan2,a=Math.cos,c=Math.max,f=Math.min,s=Math.sin,l=Math.sqrt,h=1e-12,d=Math.PI,p=d/2,v=2*d;function g(t){return t>=1?p:t<=-1?-p:Math.asin(t)}function y(t){return t.innerRadius}function m(t){return t.outerRadius}function b(t){return t.startAngle}function _(t){return t.endAngle}function x(t){return t&&t.padAngle}function w(t,n,e,r,i,o,u){var a=t-e,f=n-r,s=(u?o:-o)/l(a*a+f*f),h=s*f,d=-s*a,p=t+h,v=n+d,g=e+h,y=r+d,m=(p+g)/2,b=(v+y)/2,_=g-p,x=y-v,w=_*_+x*x,A=i-o,E=p*y-g*v,M=(x<0?-1:1)*l(c(0,A*A*w-E*E)),k=(E*x-_*M)/w,B=(-E*_-x*M)/w,F=(E*x+_*M)/w,T=(-E*_+x*M)/w,N=k-m,C=B-b,S=F-m,O=T-b;return N*N+C*C>S*S+O*O&&(k=F,B=T),{cx:k,cy:B,x01:-h,y01:-d,x11:k*(i/A-1),y11:B*(i/A-1)}}var A=function(){var t=y,n=m,e=i(0),c=null,A=b,E=_,M=x,k=null;function B(){var i,y,m,b=+t.apply(this,arguments),_=+n.apply(this,arguments),x=A.apply(this,arguments)-p,B=E.apply(this,arguments)-p,F=o(B-x),T=B>x;if(k||(k=i=Object(r.path)()),_<b&&(y=_,_=b,b=y),_>h)if(F>v-h)k.moveTo(_*a(x),_*s(x)),k.arc(0,0,_,x,B,!T),b>h&&(k.moveTo(b*a(B),b*s(B)),k.arc(0,0,b,B,x,T));else{var N,C,S=x,O=B,R=x,P=B,j=F,D=F,L=M.apply(this,arguments)/2,I=L>h&&(c?+c.apply(this,arguments):l(b*b+_*_)),U=f(o(_-b)/2,+e.apply(this,arguments)),z=U,Y=U;if(I>h){var q=g(I/b*s(L)),H=g(I/_*s(L));(j-=2*q)>h?(R+=q*=T?1:-1,P-=q):(j=0,R=P=(x+B)/2),(D-=2*H)>h?(S+=H*=T?1:-1,O-=H):(D=0,S=O=(x+B)/2)}var $=_*a(S),V=_*s(S),G=b*a(P),X=b*s(P);if(U>h){var W,J=_*a(O),Z=_*s(O),Q=b*a(R),K=b*s(R);if(F<d&&(W=function(t,n,e,r,i,o,u,a){var c=e-t,f=r-n,s=u-i,l=a-o,d=l*c-s*f;if(!(d*d<h))return[t+(d=(s*(n-o)-l*(t-i))/d)*c,n+d*f]}($,V,Q,K,J,Z,G,X))){var tt=$-W[0],nt=V-W[1],et=J-W[0],rt=Z-W[1],it=1/s(((m=(tt*et+nt*rt)/(l(tt*tt+nt*nt)*l(et*et+rt*rt)))>1?0:m<-1?d:Math.acos(m))/2),ot=l(W[0]*W[0]+W[1]*W[1]);z=f(U,(b-ot)/(it-1)),Y=f(U,(_-ot)/(it+1))}}D>h?Y>h?(N=w(Q,K,$,V,_,Y,T),C=w(J,Z,G,X,_,Y,T),k.moveTo(N.cx+N.x01,N.cy+N.y01),Y<U?k.arc(N.cx,N.cy,Y,u(N.y01,N.x01),u(C.y01,C.x01),!T):(k.arc(N.cx,N.cy,Y,u(N.y01,N.x01),u(N.y11,N.x11),!T),k.arc(0,0,_,u(N.cy+N.y11,N.cx+N.x11),u(C.cy+C.y11,C.cx+C.x11),!T),k.arc(C.cx,C.cy,Y,u(C.y11,C.x11),u(C.y01,C.x01),!T))):(k.moveTo($,V),k.arc(0,0,_,S,O,!T)):k.moveTo($,V),b>h&&j>h?z>h?(N=w(G,X,J,Z,b,-z,T),C=w($,V,Q,K,b,-z,T),k.lineTo(N.cx+N.x01,N.cy+N.y01),z<U?k.arc(N.cx,N.cy,z,u(N.y01,N.x01),u(C.y01,C.x01),!T):(k.arc(N.cx,N.cy,z,u(N.y01,N.x01),u(N.y11,N.x11),!T),k.arc(0,0,b,u(N.cy+N.y11,N.cx+N.x11),u(C.cy+C.y11,C.cx+C.x11),T),k.arc(C.cx,C.cy,z,u(C.y11,C.x11),u(C.y01,C.x01),!T))):k.arc(0,0,b,P,R,T):k.lineTo(G,X)}else k.moveTo(0,0);if(k.closePath(),i)return k=null,i+""||null}return B.centroid=function(){var e=(+t.apply(this,arguments)+ +n.apply(this,arguments))/2,r=(+A.apply(this,arguments)+ +E.apply(this,arguments))/2-d/2;return[a(r)*e,s(r)*e]},B.innerRadius=function(n){return arguments.length?(t="function"==typeof n?n:i(+n),B):t},B.outerRadius=function(t){return arguments.length?(n="function"==typeof t?t:i(+t),B):n},B.cornerRadius=function(t){return arguments.length?(e="function"==typeof t?t:i(+t),B):e},B.padRadius=function(t){return arguments.length?(c=null==t?null:"function"==typeof t?t:i(+t),B):c},B.startAngle=function(t){return arguments.length?(A="function"==typeof t?t:i(+t),B):A},B.endAngle=function(t){return arguments.length?(E="function"==typeof t?t:i(+t),B):E},B.padAngle=function(t){return arguments.length?(M="function"==typeof t?t:i(+t),B):M},B.context=function(t){return arguments.length?(k=null==t?null:t,B):k},B};function E(t){this._context=t}E.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:this._context.lineTo(t,n)}}};var M=function(t){return new E(t)};function k(t){return t[0]}function B(t){return t[1]}var F=function(){var t=k,n=B,e=i(!0),o=null,u=M,a=null;function c(i){var c,f,s,l=i.length,h=!1;for(null==o&&(a=u(s=Object(r.path)())),c=0;c<=l;++c)!(c<l&&e(f=i[c],c,i))===h&&((h=!h)?a.lineStart():a.lineEnd()),h&&a.point(+t(f,c,i),+n(f,c,i));if(s)return a=null,s+""||null}return c.x=function(n){return arguments.length?(t="function"==typeof n?n:i(+n),c):t},c.y=function(t){return arguments.length?(n="function"==typeof t?t:i(+t),c):n},c.defined=function(t){return arguments.length?(e="function"==typeof t?t:i(!!t),c):e},c.curve=function(t){return arguments.length?(u=t,null!=o&&(a=u(o)),c):u},c.context=function(t){return arguments.length?(null==t?o=a=null:a=u(o=t),c):o},c},T=function(){var t=k,n=null,e=i(0),o=B,u=i(!0),a=null,c=M,f=null;function s(i){var s,l,h,d,p,v=i.length,g=!1,y=new Array(v),m=new Array(v);for(null==a&&(f=c(p=Object(r.path)())),s=0;s<=v;++s){if(!(s<v&&u(d=i[s],s,i))===g)if(g=!g)l=s,f.areaStart(),f.lineStart();else{for(f.lineEnd(),f.lineStart(),h=s-1;h>=l;--h)f.point(y[h],m[h]);f.lineEnd(),f.areaEnd()}g&&(y[s]=+t(d,s,i),m[s]=+e(d,s,i),f.point(n?+n(d,s,i):y[s],o?+o(d,s,i):m[s]))}if(p)return f=null,p+""||null}function l(){return F().defined(u).curve(c).context(a)}return s.x=function(e){return arguments.length?(t="function"==typeof e?e:i(+e),n=null,s):t},s.x0=function(n){return arguments.length?(t="function"==typeof n?n:i(+n),s):t},s.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:i(+t),s):n},s.y=function(t){return arguments.length?(e="function"==typeof t?t:i(+t),o=null,s):e},s.y0=function(t){return arguments.length?(e="function"==typeof t?t:i(+t),s):e},s.y1=function(t){return arguments.length?(o=null==t?null:"function"==typeof t?t:i(+t),s):o},s.lineX0=s.lineY0=function(){return l().x(t).y(e)},s.lineY1=function(){return l().x(t).y(o)},s.lineX1=function(){return l().x(n).y(e)},s.defined=function(t){return arguments.length?(u="function"==typeof t?t:i(!!t),s):u},s.curve=function(t){return arguments.length?(c=t,null!=a&&(f=c(a)),s):c},s.context=function(t){return arguments.length?(null==t?a=f=null:f=c(a=t),s):a},s},N=function(t,n){return n<t?-1:n>t?1:n>=t?0:NaN},C=function(t){return t},S=function(){var t=C,n=N,e=null,r=i(0),o=i(v),u=i(0);function a(i){var a,c,f,s,l,h=i.length,d=0,p=new Array(h),g=new Array(h),y=+r.apply(this,arguments),m=Math.min(v,Math.max(-v,o.apply(this,arguments)-y)),b=Math.min(Math.abs(m)/h,u.apply(this,arguments)),_=b*(m<0?-1:1);for(a=0;a<h;++a)(l=g[p[a]=a]=+t(i[a],a,i))>0&&(d+=l);for(null!=n?p.sort(function(t,e){return n(g[t],g[e])}):null!=e&&p.sort(function(t,n){return e(i[t],i[n])}),a=0,f=d?(m-h*_)/d:0;a<h;++a,y=s)c=p[a],s=y+((l=g[c])>0?l*f:0)+_,g[c]={data:i[c],index:a,value:l,startAngle:y,endAngle:s,padAngle:b};return g}return a.value=function(n){return arguments.length?(t="function"==typeof n?n:i(+n),a):t},a.sortValues=function(t){return arguments.length?(n=t,e=null,a):n},a.sort=function(t){return arguments.length?(e=t,n=null,a):e},a.startAngle=function(t){return arguments.length?(r="function"==typeof t?t:i(+t),a):r},a.endAngle=function(t){return arguments.length?(o="function"==typeof t?t:i(+t),a):o},a.padAngle=function(t){return arguments.length?(u="function"==typeof t?t:i(+t),a):u},a},O=P(M);function R(t){this._curve=t}function P(t){function n(n){return new R(t(n))}return n._curve=t,n}function j(t){var n=t.curve;return t.angle=t.x,delete t.x,t.radius=t.y,delete t.y,t.curve=function(t){return arguments.length?n(P(t)):n()._curve},t}R.prototype={areaStart:function(){this._curve.areaStart()},areaEnd:function(){this._curve.areaEnd()},lineStart:function(){this._curve.lineStart()},lineEnd:function(){this._curve.lineEnd()},point:function(t,n){this._curve.point(n*Math.sin(t),n*-Math.cos(t))}};var D=function(){return j(F().curve(O))},L=function(){var t=T().curve(O),n=t.curve,e=t.lineX0,r=t.lineX1,i=t.lineY0,o=t.lineY1;return t.angle=t.x,delete t.x,t.startAngle=t.x0,delete t.x0,t.endAngle=t.x1,delete t.x1,t.radius=t.y,delete t.y,t.innerRadius=t.y0,delete t.y0,t.outerRadius=t.y1,delete t.y1,t.lineStartAngle=function(){return j(e())},delete t.lineX0,t.lineEndAngle=function(){return j(r())},delete t.lineX1,t.lineInnerRadius=function(){return j(i())},delete t.lineY0,t.lineOuterRadius=function(){return j(o())},delete t.lineY1,t.curve=function(t){return arguments.length?n(P(t)):n()._curve},t},I=function(t,n){return[(n=+n)*Math.cos(t-=Math.PI/2),n*Math.sin(t)]},U=Array.prototype.slice;function z(t){return t.source}function Y(t){return t.target}function q(t){var n=z,e=Y,o=k,u=B,a=null;function c(){var i,c=U.call(arguments),f=n.apply(this,c),s=e.apply(this,c);if(a||(a=i=Object(r.path)()),t(a,+o.apply(this,(c[0]=f,c)),+u.apply(this,c),+o.apply(this,(c[0]=s,c)),+u.apply(this,c)),i)return a=null,i+""||null}return c.source=function(t){return arguments.length?(n=t,c):n},c.target=function(t){return arguments.length?(e=t,c):e},c.x=function(t){return arguments.length?(o="function"==typeof t?t:i(+t),c):o},c.y=function(t){return arguments.length?(u="function"==typeof t?t:i(+t),c):u},c.context=function(t){return arguments.length?(a=null==t?null:t,c):a},c}function H(t,n,e,r,i){t.moveTo(n,e),t.bezierCurveTo(n=(n+r)/2,e,n,i,r,i)}function $(t,n,e,r,i){t.moveTo(n,e),t.bezierCurveTo(n,e=(e+i)/2,r,e,r,i)}function V(t,n,e,r,i){var o=I(n,e),u=I(n,e=(e+i)/2),a=I(r,e),c=I(r,i);t.moveTo(o[0],o[1]),t.bezierCurveTo(u[0],u[1],a[0],a[1],c[0],c[1])}function G(){return q(H)}function X(){return q($)}function W(){var t=q(V);return t.angle=t.x,delete t.x,t.radius=t.y,delete t.y,t}var J={draw:function(t,n){var e=Math.sqrt(n/d);t.moveTo(e,0),t.arc(0,0,e,0,v)}},Z={draw:function(t,n){var e=Math.sqrt(n/5)/2;t.moveTo(-3*e,-e),t.lineTo(-e,-e),t.lineTo(-e,-3*e),t.lineTo(e,-3*e),t.lineTo(e,-e),t.lineTo(3*e,-e),t.lineTo(3*e,e),t.lineTo(e,e),t.lineTo(e,3*e),t.lineTo(-e,3*e),t.lineTo(-e,e),t.lineTo(-3*e,e),t.closePath()}},Q=Math.sqrt(1/3),K=2*Q,tt={draw:function(t,n){var e=Math.sqrt(n/K),r=e*Q;t.moveTo(0,-e),t.lineTo(r,0),t.lineTo(0,e),t.lineTo(-r,0),t.closePath()}},nt=Math.sin(d/10)/Math.sin(7*d/10),et=Math.sin(v/10)*nt,rt=-Math.cos(v/10)*nt,it={draw:function(t,n){var e=Math.sqrt(.8908130915292852*n),r=et*e,i=rt*e;t.moveTo(0,-e),t.lineTo(r,i);for(var o=1;o<5;++o){var u=v*o/5,a=Math.cos(u),c=Math.sin(u);t.lineTo(c*e,-a*e),t.lineTo(a*r-c*i,c*r+a*i)}t.closePath()}},ot={draw:function(t,n){var e=Math.sqrt(n),r=-e/2;t.rect(r,r,e,e)}},ut=Math.sqrt(3),at={draw:function(t,n){var e=-Math.sqrt(n/(3*ut));t.moveTo(0,2*e),t.lineTo(-ut*e,-e),t.lineTo(ut*e,-e),t.closePath()}},ct=Math.sqrt(3)/2,ft=1/Math.sqrt(12),st=3*(ft/2+1),lt={draw:function(t,n){var e=Math.sqrt(n/st),r=e/2,i=e*ft,o=r,u=e*ft+e,a=-o,c=u;t.moveTo(r,i),t.lineTo(o,u),t.lineTo(a,c),t.lineTo(-.5*r-ct*i,ct*r+-.5*i),t.lineTo(-.5*o-ct*u,ct*o+-.5*u),t.lineTo(-.5*a-ct*c,ct*a+-.5*c),t.lineTo(-.5*r+ct*i,-.5*i-ct*r),t.lineTo(-.5*o+ct*u,-.5*u-ct*o),t.lineTo(-.5*a+ct*c,-.5*c-ct*a),t.closePath()}},ht=[J,Z,tt,ot,it,at,lt],dt=function(){var t=i(J),n=i(64),e=null;function o(){var i;if(e||(e=i=Object(r.path)()),t.apply(this,arguments).draw(e,+n.apply(this,arguments)),i)return e=null,i+""||null}return o.type=function(n){return arguments.length?(t="function"==typeof n?n:i(n),o):t},o.size=function(t){return arguments.length?(n="function"==typeof t?t:i(+t),o):n},o.context=function(t){return arguments.length?(e=null==t?null:t,o):e},o},pt=function(){};function vt(t,n,e){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+n)/6,(t._y0+4*t._y1+e)/6)}function gt(t){this._context=t}gt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:vt(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:vt(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}};var yt=function(t){return new gt(t)};function mt(t){this._context=t}mt.prototype={areaStart:pt,areaEnd:pt,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._x2=t,this._y2=n;break;case 1:this._point=2,this._x3=t,this._y3=n;break;case 2:this._point=3,this._x4=t,this._y4=n,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+n)/6);break;default:vt(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}};var bt=function(t){return new mt(t)};function _t(t){this._context=t}_t.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var e=(this._x0+4*this._x1+t)/6,r=(this._y0+4*this._y1+n)/6;this._line?this._context.lineTo(e,r):this._context.moveTo(e,r);break;case 3:this._point=4;default:vt(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}};var xt=function(t){return new _t(t)};function wt(t,n){this._basis=new gt(t),this._beta=n}wt.prototype={lineStart:function(){this._x=[],this._y=[],this._basis.lineStart()},lineEnd:function(){var t=this._x,n=this._y,e=t.length-1;if(e>0)for(var r,i=t[0],o=n[0],u=t[e]-i,a=n[e]-o,c=-1;++c<=e;)r=c/e,this._basis.point(this._beta*t[c]+(1-this._beta)*(i+r*u),this._beta*n[c]+(1-this._beta)*(o+r*a));this._x=this._y=null,this._basis.lineEnd()},point:function(t,n){this._x.push(+t),this._y.push(+n)}};var At=function t(n){function e(t){return 1===n?new gt(t):new wt(t,n)}return e.beta=function(n){return t(+n)},e}(.85);function Et(t,n,e){t._context.bezierCurveTo(t._x1+t._k*(t._x2-t._x0),t._y1+t._k*(t._y2-t._y0),t._x2+t._k*(t._x1-n),t._y2+t._k*(t._y1-e),t._x2,t._y2)}function Mt(t,n){this._context=t,this._k=(1-n)/6}Mt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:Et(this,this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2,this._x1=t,this._y1=n;break;case 2:this._point=3;default:Et(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var kt=function t(n){function e(t){return new Mt(t,n)}return e.tension=function(n){return t(+n)},e}(0);function Bt(t,n){this._context=t,this._k=(1-n)/6}Bt.prototype={areaStart:pt,areaEnd:pt,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._x3=t,this._y3=n;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3,this._x5=t,this._y5=n;break;default:Et(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var Ft=function t(n){function e(t){return new Bt(t,n)}return e.tension=function(n){return t(+n)},e}(0);function Tt(t,n){this._context=t,this._k=(1-n)/6}Tt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:Et(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var Nt=function t(n){function e(t){return new Tt(t,n)}return e.tension=function(n){return t(+n)},e}(0);function Ct(t,n,e){var r=t._x1,i=t._y1,o=t._x2,u=t._y2;if(t._l01_a>h){var a=2*t._l01_2a+3*t._l01_a*t._l12_a+t._l12_2a,c=3*t._l01_a*(t._l01_a+t._l12_a);r=(r*a-t._x0*t._l12_2a+t._x2*t._l01_2a)/c,i=(i*a-t._y0*t._l12_2a+t._y2*t._l01_2a)/c}if(t._l23_a>h){var f=2*t._l23_2a+3*t._l23_a*t._l12_a+t._l12_2a,s=3*t._l23_a*(t._l23_a+t._l12_a);o=(o*f+t._x1*t._l23_2a-n*t._l12_2a)/s,u=(u*f+t._y1*t._l23_2a-e*t._l12_2a)/s}t._context.bezierCurveTo(r,i,o,u,t._x2,t._y2)}function St(t,n){this._context=t,this._alpha=n}St.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:this.point(this._x2,this._y2)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){if(t=+t,n=+n,this._point){var e=this._x2-t,r=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))}switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3;default:Ct(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var Ot=function t(n){function e(t){return n?new St(t,n):new Mt(t,0)}return e.alpha=function(n){return t(+n)},e}(.5);function Rt(t,n){this._context=t,this._alpha=n}Rt.prototype={areaStart:pt,areaEnd:pt,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,n){if(t=+t,n=+n,this._point){var e=this._x2-t,r=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))}switch(this._point){case 0:this._point=1,this._x3=t,this._y3=n;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3,this._x5=t,this._y5=n;break;default:Ct(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var Pt=function t(n){function e(t){return n?new Rt(t,n):new Bt(t,0)}return e.alpha=function(n){return t(+n)},e}(.5);function jt(t,n){this._context=t,this._alpha=n}jt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){if(t=+t,n=+n,this._point){var e=this._x2-t,r=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))}switch(this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:Ct(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var Dt=function t(n){function e(t){return n?new jt(t,n):new Tt(t,0)}return e.alpha=function(n){return t(+n)},e}(.5);function Lt(t){this._context=t}Lt.prototype={areaStart:pt,areaEnd:pt,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,n){t=+t,n=+n,this._point?this._context.lineTo(t,n):(this._point=1,this._context.moveTo(t,n))}};var It=function(t){return new Lt(t)};function Ut(t){return t<0?-1:1}function zt(t,n,e){var r=t._x1-t._x0,i=n-t._x1,o=(t._y1-t._y0)/(r||i<0&&-0),u=(e-t._y1)/(i||r<0&&-0),a=(o*i+u*r)/(r+i);return(Ut(o)+Ut(u))*Math.min(Math.abs(o),Math.abs(u),.5*Math.abs(a))||0}function Yt(t,n){var e=t._x1-t._x0;return e?(3*(t._y1-t._y0)/e-n)/2:n}function qt(t,n,e){var r=t._x0,i=t._y0,o=t._x1,u=t._y1,a=(o-r)/3;t._context.bezierCurveTo(r+a,i+a*n,o-a,u-a*e,o,u)}function Ht(t){this._context=t}function $t(t){this._context=new Vt(t)}function Vt(t){this._context=t}function Gt(t){return new Ht(t)}function Xt(t){return new $t(t)}function Wt(t){this._context=t}function Jt(t){var n,e,r=t.length-1,i=new Array(r),o=new Array(r),u=new Array(r);for(i[0]=0,o[0]=2,u[0]=t[0]+2*t[1],n=1;n<r-1;++n)i[n]=1,o[n]=4,u[n]=4*t[n]+2*t[n+1];for(i[r-1]=2,o[r-1]=7,u[r-1]=8*t[r-1]+t[r],n=1;n<r;++n)e=i[n]/o[n-1],o[n]-=e,u[n]-=e*u[n-1];for(i[r-1]=u[r-1]/o[r-1],n=r-2;n>=0;--n)i[n]=(u[n]-i[n+1])/o[n];for(o[r-1]=(t[r]+i[r-1])/2,n=0;n<r-1;++n)o[n]=2*t[n+1]-i[n+1];return[i,o]}Ht.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:qt(this,this._t0,Yt(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){var e=NaN;if(n=+n,(t=+t)!==this._x1||n!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3,qt(this,Yt(this,e=zt(this,t,n)),e);break;default:qt(this,this._t0,e=zt(this,t,n))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n,this._t0=e}}},($t.prototype=Object.create(Ht.prototype)).point=function(t,n){Ht.prototype.point.call(this,n,t)},Vt.prototype={moveTo:function(t,n){this._context.moveTo(n,t)},closePath:function(){this._context.closePath()},lineTo:function(t,n){this._context.lineTo(n,t)},bezierCurveTo:function(t,n,e,r,i,o){this._context.bezierCurveTo(n,t,r,e,o,i)}},Wt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,n=this._y,e=t.length;if(e)if(this._line?this._context.lineTo(t[0],n[0]):this._context.moveTo(t[0],n[0]),2===e)this._context.lineTo(t[1],n[1]);else for(var r=Jt(t),i=Jt(n),o=0,u=1;u<e;++o,++u)this._context.bezierCurveTo(r[0][o],i[0][o],r[1][o],i[1][o],t[u],n[u]);(this._line||0!==this._line&&1===e)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,n){this._x.push(+t),this._y.push(+n)}};var Zt=function(t){return new Wt(t)};function Qt(t,n){this._context=t,this._t=n}Qt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,n),this._context.lineTo(t,n);else{var e=this._x*(1-this._t)+t*this._t;this._context.lineTo(e,this._y),this._context.lineTo(e,n)}}this._x=t,this._y=n}};var Kt=function(t){return new Qt(t,.5)};function tn(t){return new Qt(t,0)}function nn(t){return new Qt(t,1)}var en=function(t,n){if((i=t.length)>1)for(var e,r,i,o=1,u=t[n[0]],a=u.length;o<i;++o)for(r=u,u=t[n[o]],e=0;e<a;++e)u[e][1]+=u[e][0]=isNaN(r[e][1])?r[e][0]:r[e][1]},rn=function(t){for(var n=t.length,e=new Array(n);--n>=0;)e[n]=n;return e};function on(t,n){return t[n]}var un=function(){var t=i([]),n=rn,e=en,r=on;function o(i){var o,u,a=t.apply(this,arguments),c=i.length,f=a.length,s=new Array(f);for(o=0;o<f;++o){for(var l,h=a[o],d=s[o]=new Array(c),p=0;p<c;++p)d[p]=l=[0,+r(i[p],h,p,i)],l.data=i[p];d.key=h}for(o=0,u=n(s);o<f;++o)s[u[o]].index=o;return e(s,u),s}return o.keys=function(n){return arguments.length?(t="function"==typeof n?n:i(U.call(n)),o):t},o.value=function(t){return arguments.length?(r="function"==typeof t?t:i(+t),o):r},o.order=function(t){return arguments.length?(n=null==t?rn:"function"==typeof t?t:i(U.call(t)),o):n},o.offset=function(t){return arguments.length?(e=null==t?en:t,o):e},o},an=function(t,n){if((r=t.length)>0){for(var e,r,i,o=0,u=t[0].length;o<u;++o){for(i=e=0;e<r;++e)i+=t[e][o][1]||0;if(i)for(e=0;e<r;++e)t[e][o][1]/=i}en(t,n)}},cn=function(t,n){if((a=t.length)>0)for(var e,r,i,o,u,a,c=0,f=t[n[0]].length;c<f;++c)for(o=u=0,e=0;e<a;++e)(i=(r=t[n[e]][c])[1]-r[0])>=0?(r[0]=o,r[1]=o+=i):i<0?(r[1]=u,r[0]=u+=i):r[0]=o},fn=function(t,n){if((e=t.length)>0){for(var e,r=0,i=t[n[0]],o=i.length;r<o;++r){for(var u=0,a=0;u<e;++u)a+=t[u][r][1]||0;i[r][1]+=i[r][0]=-a/2}en(t,n)}},sn=function(t,n){if((i=t.length)>0&&(r=(e=t[n[0]]).length)>0){for(var e,r,i,o=0,u=1;u<r;++u){for(var a=0,c=0,f=0;a<i;++a){for(var s=t[n[a]],l=s[u][1]||0,h=(l-(s[u-1][1]||0))/2,d=0;d<a;++d){var p=t[n[d]];h+=(p[u][1]||0)-(p[u-1][1]||0)}c+=l,f+=h*l}e[u-1][1]+=e[u-1][0]=o,c&&(o-=f/c)}e[u-1][1]+=e[u-1][0]=o,en(t,n)}},ln=function(t){var n=t.map(hn);return rn(t).sort(function(t,e){return n[t]-n[e]})};function hn(t){for(var n,e=-1,r=0,i=t.length,o=-1/0;++e<i;)(n=+t[e][1])>o&&(o=n,r=e);return r}var dn=function(t){var n=t.map(pn);return rn(t).sort(function(t,e){return n[t]-n[e]})};function pn(t){for(var n,e=0,r=-1,i=t.length;++r<i;)(n=+t[r][1])&&(e+=n);return e}var vn=function(t){return dn(t).reverse()},gn=function(t){var n,e,r=t.length,i=t.map(pn),o=ln(t),u=0,a=0,c=[],f=[];for(n=0;n<r;++n)e=o[n],u<a?(u+=i[e],c.push(e)):(a+=i[e],f.push(e));return f.reverse().concat(c)},yn=function(t){return rn(t).reverse()};e.d(n,"arc",function(){return A}),e.d(n,"area",function(){return T}),e.d(n,"line",function(){return F}),e.d(n,"pie",function(){return S}),e.d(n,"areaRadial",function(){return L}),e.d(n,"radialArea",function(){return L}),e.d(n,"lineRadial",function(){return D}),e.d(n,"radialLine",function(){return D}),e.d(n,"pointRadial",function(){return I}),e.d(n,"linkHorizontal",function(){return G}),e.d(n,"linkVertical",function(){return X}),e.d(n,"linkRadial",function(){return W}),e.d(n,"symbol",function(){return dt}),e.d(n,"symbols",function(){return ht}),e.d(n,"symbolCircle",function(){return J}),e.d(n,"symbolCross",function(){return Z}),e.d(n,"symbolDiamond",function(){return tt}),e.d(n,"symbolSquare",function(){return ot}),e.d(n,"symbolStar",function(){return it}),e.d(n,"symbolTriangle",function(){return at}),e.d(n,"symbolWye",function(){return lt}),e.d(n,"curveBasisClosed",function(){return bt}),e.d(n,"curveBasisOpen",function(){return xt}),e.d(n,"curveBasis",function(){return yt}),e.d(n,"curveBundle",function(){return At}),e.d(n,"curveCardinalClosed",function(){return Ft}),e.d(n,"curveCardinalOpen",function(){return Nt}),e.d(n,"curveCardinal",function(){return kt}),e.d(n,"curveCatmullRomClosed",function(){return Pt}),e.d(n,"curveCatmullRomOpen",function(){return Dt}),e.d(n,"curveCatmullRom",function(){return Ot}),e.d(n,"curveLinearClosed",function(){return It}),e.d(n,"curveLinear",function(){return M}),e.d(n,"curveMonotoneX",function(){return Gt}),e.d(n,"curveMonotoneY",function(){return Xt}),e.d(n,"curveNatural",function(){return Zt}),e.d(n,"curveStep",function(){return Kt}),e.d(n,"curveStepAfter",function(){return nn}),e.d(n,"curveStepBefore",function(){return tn}),e.d(n,"stack",function(){return un}),e.d(n,"stackOffsetExpand",function(){return an}),e.d(n,"stackOffsetDiverging",function(){return cn}),e.d(n,"stackOffsetNone",function(){return en}),e.d(n,"stackOffsetSilhouette",function(){return fn}),e.d(n,"stackOffsetWiggle",function(){return sn}),e.d(n,"stackOrderAppearance",function(){return ln}),e.d(n,"stackOrderAscending",function(){return dn}),e.d(n,"stackOrderDescending",function(){return vn}),e.d(n,"stackOrderInsideOut",function(){return gn}),e.d(n,"stackOrderNone",function(){return rn}),e.d(n,"stackOrderReverse",function(){return yn})},1315:function(t,n,e){"use strict";e.r(n);var r=function(t){for(var n=t.length/6|0,e=new Array(n),r=0;r<n;)e[r]="#"+t.slice(6*r,6*++r);return e},i=r("1f77b4ff7f0e2ca02cd627289467bd8c564be377c27f7f7fbcbd2217becf"),o=r("7fc97fbeaed4fdc086ffff99386cb0f0027fbf5b17666666"),u=r("1b9e77d95f027570b3e7298a66a61ee6ab02a6761d666666"),a=r("a6cee31f78b4b2df8a33a02cfb9a99e31a1cfdbf6fff7f00cab2d66a3d9affff99b15928"),c=r("fbb4aeb3cde3ccebc5decbe4fed9a6ffffcce5d8bdfddaecf2f2f2"),f=r("b3e2cdfdcdaccbd5e8f4cae4e6f5c9fff2aef1e2cccccccc"),s=r("e41a1c377eb84daf4a984ea3ff7f00ffff33a65628f781bf999999"),l=r("66c2a5fc8d628da0cbe78ac3a6d854ffd92fe5c494b3b3b3"),h=r("8dd3c7ffffb3bebadafb807280b1d3fdb462b3de69fccde5d9d9d9bc80bdccebc5ffed6f"),d=r("4e79a7f28e2ce1575976b7b259a14fedc949af7aa1ff9da79c755fbab0ab"),p=e(63),v=function(t){return Object(p.interpolateRgbBasis)(t[t.length-1])},g=new Array(3).concat("d8b365f5f5f55ab4ac","a6611adfc27d80cdc1018571","a6611adfc27df5f5f580cdc1018571","8c510ad8b365f6e8c3c7eae55ab4ac01665e","8c510ad8b365f6e8c3f5f5f5c7eae55ab4ac01665e","8c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e","8c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e","5430058c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e003c30","5430058c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e003c30").map(r),y=v(g),m=new Array(3).concat("af8dc3f7f7f77fbf7b","7b3294c2a5cfa6dba0008837","7b3294c2a5cff7f7f7a6dba0008837","762a83af8dc3e7d4e8d9f0d37fbf7b1b7837","762a83af8dc3e7d4e8f7f7f7d9f0d37fbf7b1b7837","762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b7837","762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b7837","40004b762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b783700441b","40004b762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b783700441b").map(r),b=v(m),_=new Array(3).concat("e9a3c9f7f7f7a1d76a","d01c8bf1b6dab8e1864dac26","d01c8bf1b6daf7f7f7b8e1864dac26","c51b7de9a3c9fde0efe6f5d0a1d76a4d9221","c51b7de9a3c9fde0eff7f7f7e6f5d0a1d76a4d9221","c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221","c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221","8e0152c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221276419","8e0152c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221276419").map(r),x=v(_),w=new Array(3).concat("998ec3f7f7f7f1a340","5e3c99b2abd2fdb863e66101","5e3c99b2abd2f7f7f7fdb863e66101","542788998ec3d8daebfee0b6f1a340b35806","542788998ec3d8daebf7f7f7fee0b6f1a340b35806","5427888073acb2abd2d8daebfee0b6fdb863e08214b35806","5427888073acb2abd2d8daebf7f7f7fee0b6fdb863e08214b35806","2d004b5427888073acb2abd2d8daebfee0b6fdb863e08214b358067f3b08","2d004b5427888073acb2abd2d8daebf7f7f7fee0b6fdb863e08214b358067f3b08").map(r),A=v(w),E=new Array(3).concat("ef8a62f7f7f767a9cf","ca0020f4a58292c5de0571b0","ca0020f4a582f7f7f792c5de0571b0","b2182bef8a62fddbc7d1e5f067a9cf2166ac","b2182bef8a62fddbc7f7f7f7d1e5f067a9cf2166ac","b2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac","b2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac","67001fb2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac053061","67001fb2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac053061").map(r),M=v(E),k=new Array(3).concat("ef8a62ffffff999999","ca0020f4a582bababa404040","ca0020f4a582ffffffbababa404040","b2182bef8a62fddbc7e0e0e09999994d4d4d","b2182bef8a62fddbc7ffffffe0e0e09999994d4d4d","b2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d","b2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d","67001fb2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d1a1a1a","67001fb2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d1a1a1a").map(r),B=v(k),F=new Array(3).concat("fc8d59ffffbf91bfdb","d7191cfdae61abd9e92c7bb6","d7191cfdae61ffffbfabd9e92c7bb6","d73027fc8d59fee090e0f3f891bfdb4575b4","d73027fc8d59fee090ffffbfe0f3f891bfdb4575b4","d73027f46d43fdae61fee090e0f3f8abd9e974add14575b4","d73027f46d43fdae61fee090ffffbfe0f3f8abd9e974add14575b4","a50026d73027f46d43fdae61fee090e0f3f8abd9e974add14575b4313695","a50026d73027f46d43fdae61fee090ffffbfe0f3f8abd9e974add14575b4313695").map(r),T=v(F),N=new Array(3).concat("fc8d59ffffbf91cf60","d7191cfdae61a6d96a1a9641","d7191cfdae61ffffbfa6d96a1a9641","d73027fc8d59fee08bd9ef8b91cf601a9850","d73027fc8d59fee08bffffbfd9ef8b91cf601a9850","d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850","d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850","a50026d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850006837","a50026d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850006837").map(r),C=v(N),S=new Array(3).concat("fc8d59ffffbf99d594","d7191cfdae61abdda42b83ba","d7191cfdae61ffffbfabdda42b83ba","d53e4ffc8d59fee08be6f59899d5943288bd","d53e4ffc8d59fee08bffffbfe6f59899d5943288bd","d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd","d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd","9e0142d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd5e4fa2","9e0142d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd5e4fa2").map(r),O=v(S),R=new Array(3).concat("e5f5f999d8c92ca25f","edf8fbb2e2e266c2a4238b45","edf8fbb2e2e266c2a42ca25f006d2c","edf8fbccece699d8c966c2a42ca25f006d2c","edf8fbccece699d8c966c2a441ae76238b45005824","f7fcfde5f5f9ccece699d8c966c2a441ae76238b45005824","f7fcfde5f5f9ccece699d8c966c2a441ae76238b45006d2c00441b").map(r),P=v(R),j=new Array(3).concat("e0ecf49ebcda8856a7","edf8fbb3cde38c96c688419d","edf8fbb3cde38c96c68856a7810f7c","edf8fbbfd3e69ebcda8c96c68856a7810f7c","edf8fbbfd3e69ebcda8c96c68c6bb188419d6e016b","f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d6e016b","f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d810f7c4d004b").map(r),D=v(j),L=new Array(3).concat("e0f3dba8ddb543a2ca","f0f9e8bae4bc7bccc42b8cbe","f0f9e8bae4bc7bccc443a2ca0868ac","f0f9e8ccebc5a8ddb57bccc443a2ca0868ac","f0f9e8ccebc5a8ddb57bccc44eb3d32b8cbe08589e","f7fcf0e0f3dbccebc5a8ddb57bccc44eb3d32b8cbe08589e","f7fcf0e0f3dbccebc5a8ddb57bccc44eb3d32b8cbe0868ac084081").map(r),I=v(L),U=new Array(3).concat("fee8c8fdbb84e34a33","fef0d9fdcc8afc8d59d7301f","fef0d9fdcc8afc8d59e34a33b30000","fef0d9fdd49efdbb84fc8d59e34a33b30000","fef0d9fdd49efdbb84fc8d59ef6548d7301f990000","fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301f990000","fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301fb300007f0000").map(r),z=v(U),Y=new Array(3).concat("ece2f0a6bddb1c9099","f6eff7bdc9e167a9cf02818a","f6eff7bdc9e167a9cf1c9099016c59","f6eff7d0d1e6a6bddb67a9cf1c9099016c59","f6eff7d0d1e6a6bddb67a9cf3690c002818a016450","fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016450","fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016c59014636").map(r),q=v(Y),H=new Array(3).concat("ece7f2a6bddb2b8cbe","f1eef6bdc9e174a9cf0570b0","f1eef6bdc9e174a9cf2b8cbe045a8d","f1eef6d0d1e6a6bddb74a9cf2b8cbe045a8d","f1eef6d0d1e6a6bddb74a9cf3690c00570b0034e7b","fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0034e7b","fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0045a8d023858").map(r),$=v(H),V=new Array(3).concat("e7e1efc994c7dd1c77","f1eef6d7b5d8df65b0ce1256","f1eef6d7b5d8df65b0dd1c77980043","f1eef6d4b9dac994c7df65b0dd1c77980043","f1eef6d4b9dac994c7df65b0e7298ace125691003f","f7f4f9e7e1efd4b9dac994c7df65b0e7298ace125691003f","f7f4f9e7e1efd4b9dac994c7df65b0e7298ace125698004367001f").map(r),G=v(V),X=new Array(3).concat("fde0ddfa9fb5c51b8a","feebe2fbb4b9f768a1ae017e","feebe2fbb4b9f768a1c51b8a7a0177","feebe2fcc5c0fa9fb5f768a1c51b8a7a0177","feebe2fcc5c0fa9fb5f768a1dd3497ae017e7a0177","fff7f3fde0ddfcc5c0fa9fb5f768a1dd3497ae017e7a0177","fff7f3fde0ddfcc5c0fa9fb5f768a1dd3497ae017e7a017749006a").map(r),W=v(X),J=new Array(3).concat("edf8b17fcdbb2c7fb8","ffffcca1dab441b6c4225ea8","ffffcca1dab441b6c42c7fb8253494","ffffccc7e9b47fcdbb41b6c42c7fb8253494","ffffccc7e9b47fcdbb41b6c41d91c0225ea80c2c84","ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea80c2c84","ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea8253494081d58").map(r),Z=v(J),Q=new Array(3).concat("f7fcb9addd8e31a354","ffffccc2e69978c679238443","ffffccc2e69978c67931a354006837","ffffccd9f0a3addd8e78c67931a354006837","ffffccd9f0a3addd8e78c67941ab5d238443005a32","ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443005a32","ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443006837004529").map(r),K=v(Q),tt=new Array(3).concat("fff7bcfec44fd95f0e","ffffd4fed98efe9929cc4c02","ffffd4fed98efe9929d95f0e993404","ffffd4fee391fec44ffe9929d95f0e993404","ffffd4fee391fec44ffe9929ec7014cc4c028c2d04","ffffe5fff7bcfee391fec44ffe9929ec7014cc4c028c2d04","ffffe5fff7bcfee391fec44ffe9929ec7014cc4c02993404662506").map(r),nt=v(tt),et=new Array(3).concat("ffeda0feb24cf03b20","ffffb2fecc5cfd8d3ce31a1c","ffffb2fecc5cfd8d3cf03b20bd0026","ffffb2fed976feb24cfd8d3cf03b20bd0026","ffffb2fed976feb24cfd8d3cfc4e2ae31a1cb10026","ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cb10026","ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cbd0026800026").map(r),rt=v(et),it=new Array(3).concat("deebf79ecae13182bd","eff3ffbdd7e76baed62171b5","eff3ffbdd7e76baed63182bd08519c","eff3ffc6dbef9ecae16baed63182bd08519c","eff3ffc6dbef9ecae16baed64292c62171b5084594","f7fbffdeebf7c6dbef9ecae16baed64292c62171b5084594","f7fbffdeebf7c6dbef9ecae16baed64292c62171b508519c08306b").map(r),ot=v(it),ut=new Array(3).concat("e5f5e0a1d99b31a354","edf8e9bae4b374c476238b45","edf8e9bae4b374c47631a354006d2c","edf8e9c7e9c0a1d99b74c47631a354006d2c","edf8e9c7e9c0a1d99b74c47641ab5d238b45005a32","f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45005a32","f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45006d2c00441b").map(r),at=v(ut),ct=new Array(3).concat("f0f0f0bdbdbd636363","f7f7f7cccccc969696525252","f7f7f7cccccc969696636363252525","f7f7f7d9d9d9bdbdbd969696636363252525","f7f7f7d9d9d9bdbdbd969696737373525252252525","fffffff0f0f0d9d9d9bdbdbd969696737373525252252525","fffffff0f0f0d9d9d9bdbdbd969696737373525252252525000000").map(r),ft=v(ct),st=new Array(3).concat("efedf5bcbddc756bb1","f2f0f7cbc9e29e9ac86a51a3","f2f0f7cbc9e29e9ac8756bb154278f","f2f0f7dadaebbcbddc9e9ac8756bb154278f","f2f0f7dadaebbcbddc9e9ac8807dba6a51a34a1486","fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a34a1486","fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a354278f3f007d").map(r),lt=v(st),ht=new Array(3).concat("fee0d2fc9272de2d26","fee5d9fcae91fb6a4acb181d","fee5d9fcae91fb6a4ade2d26a50f15","fee5d9fcbba1fc9272fb6a4ade2d26a50f15","fee5d9fcbba1fc9272fb6a4aef3b2ccb181d99000d","fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181d99000d","fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181da50f1567000d").map(r),dt=v(ht),pt=new Array(3).concat("fee6cefdae6be6550d","feeddefdbe85fd8d3cd94701","feeddefdbe85fd8d3ce6550da63603","feeddefdd0a2fdae6bfd8d3ce6550da63603","feeddefdd0a2fdae6bfd8d3cf16913d948018c2d04","fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d948018c2d04","fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d94801a636037f2704").map(r),vt=v(pt),gt=function(t){return t=Math.max(0,Math.min(1,t)),"rgb("+Math.max(0,Math.min(255,Math.round(-4.54-t*(35.34-t*(2381.73-t*(6402.7-t*(7024.72-2710.57*t)))))))+", "+Math.max(0,Math.min(255,Math.round(32.49+t*(170.73+t*(52.82-t*(131.46-t*(176.58-67.37*t)))))))+", "+Math.max(0,Math.min(255,Math.round(81.24+t*(442.36-t*(2482.43-t*(6167.24-t*(6614.94-2475.67*t)))))))+")"},yt=e(53),mt=Object(p.interpolateCubehelixLong)(Object(yt.cubehelix)(300,.5,0),Object(yt.cubehelix)(-240,.5,1)),bt=Object(p.interpolateCubehelixLong)(Object(yt.cubehelix)(-100,.75,.35),Object(yt.cubehelix)(80,1.5,.8)),_t=Object(p.interpolateCubehelixLong)(Object(yt.cubehelix)(260,.75,.35),Object(yt.cubehelix)(80,1.5,.8)),xt=Object(yt.cubehelix)(),wt=function(t){(t<0||t>1)&&(t-=Math.floor(t));var n=Math.abs(t-.5);return xt.h=360*t-100,xt.s=1.5-1.5*n,xt.l=.8-.9*n,xt+""},At=Object(yt.rgb)(),Et=Math.PI/3,Mt=2*Math.PI/3,kt=function(t){var n;return t=(.5-t)*Math.PI,At.r=255*(n=Math.sin(t))*n,At.g=255*(n=Math.sin(t+Et))*n,At.b=255*(n=Math.sin(t+Mt))*n,At+""},Bt=function(t){return t=Math.max(0,Math.min(1,t)),"rgb("+Math.max(0,Math.min(255,Math.round(34.61+t*(1172.33-t*(10793.56-t*(33300.12-t*(38394.49-14825.05*t)))))))+", "+Math.max(0,Math.min(255,Math.round(23.31+t*(557.33+t*(1225.33-t*(3574.96-t*(1073.77+707.56*t)))))))+", "+Math.max(0,Math.min(255,Math.round(27.2+t*(3211.1-t*(15327.97-t*(27814-t*(22569.18-6838.66*t)))))))+")"};function Ft(t){var n=t.length;return function(e){return t[Math.max(0,Math.min(n-1,Math.floor(e*n)))]}}var Tt=Ft(r("44015444025645045745055946075a46085c460a5d460b5e470d60470e6147106347116447136548146748166848176948186a481a6c481b6d481c6e481d6f481f70482071482173482374482475482576482677482878482979472a7a472c7a472d7b472e7c472f7d46307e46327e46337f463480453581453781453882443983443a83443b84433d84433e85423f854240864241864142874144874045884046883f47883f48893e49893e4a893e4c8a3d4d8a3d4e8a3c4f8a3c508b3b518b3b528b3a538b3a548c39558c39568c38588c38598c375a8c375b8d365c8d365d8d355e8d355f8d34608d34618d33628d33638d32648e32658e31668e31678e31688e30698e306a8e2f6b8e2f6c8e2e6d8e2e6e8e2e6f8e2d708e2d718e2c718e2c728e2c738e2b748e2b758e2a768e2a778e2a788e29798e297a8e297b8e287c8e287d8e277e8e277f8e27808e26818e26828e26828e25838e25848e25858e24868e24878e23888e23898e238a8d228b8d228c8d228d8d218e8d218f8d21908d21918c20928c20928c20938c1f948c1f958b1f968b1f978b1f988b1f998a1f9a8a1e9b8a1e9c891e9d891f9e891f9f881fa0881fa1881fa1871fa28720a38620a48621a58521a68522a78522a88423a98324aa8325ab8225ac8226ad8127ad8128ae8029af7f2ab07f2cb17e2db27d2eb37c2fb47c31b57b32b67a34b67935b77937b87838b9773aba763bbb753dbc743fbc7340bd7242be7144bf7046c06f48c16e4ac16d4cc26c4ec36b50c46a52c56954c56856c66758c7655ac8645cc8635ec96260ca6063cb5f65cb5e67cc5c69cd5b6ccd5a6ece5870cf5773d05675d05477d1537ad1517cd2507fd34e81d34d84d44b86d54989d5488bd6468ed64590d74393d74195d84098d83e9bd93c9dd93ba0da39a2da37a5db36a8db34aadc32addc30b0dd2fb2dd2db5de2bb8de29bade28bddf26c0df25c2df23c5e021c8e020cae11fcde11dd0e11cd2e21bd5e21ad8e219dae319dde318dfe318e2e418e5e419e7e419eae51aece51befe51cf1e51df4e61ef6e620f8e621fbe723fde725")),Nt=Ft(r("00000401000501010601010802010902020b02020d03030f03031204041405041606051806051a07061c08071e0907200a08220b09240c09260d0a290e0b2b100b2d110c2f120d31130d34140e36150e38160f3b180f3d19103f1a10421c10441d11471e114920114b21114e22115024125325125527125829115a2a115c2c115f2d11612f116331116533106734106936106b38106c390f6e3b0f703d0f713f0f72400f74420f75440f764510774710784910784a10794c117a4e117b4f127b51127c52137c54137d56147d57157e59157e5a167e5c167f5d177f5f187f601880621980641a80651a80671b80681c816a1c816b1d816d1d816e1e81701f81721f817320817521817621817822817922827b23827c23827e24828025828125818326818426818627818827818928818b29818c29818e2a81902a81912b81932b80942c80962c80982d80992d809b2e7f9c2e7f9e2f7fa02f7fa1307ea3307ea5317ea6317da8327daa337dab337cad347cae347bb0357bb2357bb3367ab5367ab73779b83779ba3878bc3978bd3977bf3a77c03a76c23b75c43c75c53c74c73d73c83e73ca3e72cc3f71cd4071cf4070d0416fd2426fd3436ed5446dd6456cd8456cd9466bdb476adc4869de4968df4a68e04c67e24d66e34e65e44f64e55064e75263e85362e95462ea5661eb5760ec5860ed5a5fee5b5eef5d5ef05f5ef1605df2625df2645cf3655cf4675cf4695cf56b5cf66c5cf66e5cf7705cf7725cf8745cf8765cf9785df9795df97b5dfa7d5efa7f5efa815ffb835ffb8560fb8761fc8961fc8a62fc8c63fc8e64fc9065fd9266fd9467fd9668fd9869fd9a6afd9b6bfe9d6cfe9f6dfea16efea36ffea571fea772fea973feaa74feac76feae77feb078feb27afeb47bfeb67cfeb77efeb97ffebb81febd82febf84fec185fec287fec488fec68afec88cfeca8dfecc8ffecd90fecf92fed194fed395fed597fed799fed89afdda9cfddc9efddea0fde0a1fde2a3fde3a5fde5a7fde7a9fde9aafdebacfcecaefceeb0fcf0b2fcf2b4fcf4b6fcf6b8fcf7b9fcf9bbfcfbbdfcfdbf")),Ct=Ft(r("00000401000501010601010802010a02020c02020e03021004031204031405041706041907051b08051d09061f0a07220b07240c08260d08290e092b10092d110a30120a32140b34150b37160b39180c3c190c3e1b0c411c0c431e0c451f0c48210c4a230c4c240c4f260c51280b53290b552b0b572d0b592f0a5b310a5c320a5e340a5f3609613809623909633b09643d09653e0966400a67420a68440a68450a69470b6a490b6a4a0c6b4c0c6b4d0d6c4f0d6c510e6c520e6d540f6d550f6d57106e59106e5a116e5c126e5d126e5f136e61136e62146e64156e65156e67166e69166e6a176e6c186e6d186e6f196e71196e721a6e741a6e751b6e771c6d781c6d7a1d6d7c1d6d7d1e6d7f1e6c801f6c82206c84206b85216b87216b88226a8a226a8c23698d23698f24699025689225689326679526679727669827669a28659b29649d29649f2a63a02a63a22b62a32c61a52c60a62d60a82e5fa92e5eab2f5ead305dae305cb0315bb1325ab3325ab43359b63458b73557b93556ba3655bc3754bd3853bf3952c03a51c13a50c33b4fc43c4ec63d4dc73e4cc83f4bca404acb4149cc4248ce4347cf4446d04545d24644d34743d44842d54a41d74b3fd84c3ed94d3dda4e3cdb503bdd513ade5238df5337e05536e15635e25734e35933e45a31e55c30e65d2fe75e2ee8602de9612bea632aeb6429eb6628ec6726ed6925ee6a24ef6c23ef6e21f06f20f1711ff1731df2741cf3761bf37819f47918f57b17f57d15f67e14f68013f78212f78410f8850ff8870ef8890cf98b0bf98c0af98e09fa9008fa9207fa9407fb9606fb9706fb9906fb9b06fb9d07fc9f07fca108fca309fca50afca60cfca80dfcaa0ffcac11fcae12fcb014fcb216fcb418fbb61afbb81dfbba1ffbbc21fbbe23fac026fac228fac42afac62df9c72ff9c932f9cb35f8cd37f8cf3af7d13df7d340f6d543f6d746f5d949f5db4cf4dd4ff4df53f4e156f3e35af3e55df2e661f2e865f2ea69f1ec6df1ed71f1ef75f1f179f2f27df2f482f3f586f3f68af4f88ef5f992f6fa96f8fb9af9fc9dfafda1fcffa4")),St=Ft(r("0d088710078813078916078a19068c1b068d1d068e20068f2206902406912605912805922a05932c05942e05952f059631059733059735049837049938049a3a049a3c049b3e049c3f049c41049d43039e44039e46039f48039f4903a04b03a14c02a14e02a25002a25102a35302a35502a45601a45801a45901a55b01a55c01a65e01a66001a66100a76300a76400a76600a76700a86900a86a00a86c00a86e00a86f00a87100a87201a87401a87501a87701a87801a87a02a87b02a87d03a87e03a88004a88104a78305a78405a78606a68707a68808a68a09a58b0aa58d0ba58e0ca48f0da4910ea3920fa39410a29511a19613a19814a099159f9a169f9c179e9d189d9e199da01a9ca11b9ba21d9aa31e9aa51f99a62098a72197a82296aa2395ab2494ac2694ad2793ae2892b02991b12a90b22b8fb32c8eb42e8db52f8cb6308bb7318ab83289ba3388bb3488bc3587bd3786be3885bf3984c03a83c13b82c23c81c33d80c43e7fc5407ec6417dc7427cc8437bc9447aca457acb4679cc4778cc4977cd4a76ce4b75cf4c74d04d73d14e72d24f71d35171d45270d5536fd5546ed6556dd7566cd8576bd9586ada5a6ada5b69db5c68dc5d67dd5e66de5f65de6164df6263e06363e16462e26561e26660e3685fe4695ee56a5de56b5de66c5ce76e5be76f5ae87059e97158e97257ea7457eb7556eb7655ec7754ed7953ed7a52ee7b51ef7c51ef7e50f07f4ff0804ef1814df1834cf2844bf3854bf3874af48849f48948f58b47f58c46f68d45f68f44f79044f79143f79342f89441f89540f9973ff9983ef99a3efa9b3dfa9c3cfa9e3bfb9f3afba139fba238fca338fca537fca636fca835fca934fdab33fdac33fdae32fdaf31fdb130fdb22ffdb42ffdb52efeb72dfeb82cfeba2cfebb2bfebd2afebe2afec029fdc229fdc328fdc527fdc627fdc827fdca26fdcb26fccd25fcce25fcd025fcd225fbd324fbd524fbd724fad824fada24f9dc24f9dd25f8df25f8e125f7e225f7e425f6e626f6e826f5e926f5eb27f4ed27f3ee27f3f027f2f227f1f426f1f525f0f724f0f921"));e.d(n,"schemeCategory10",function(){return i}),e.d(n,"schemeAccent",function(){return o}),e.d(n,"schemeDark2",function(){return u}),e.d(n,"schemePaired",function(){return a}),e.d(n,"schemePastel1",function(){return c}),e.d(n,"schemePastel2",function(){return f}),e.d(n,"schemeSet1",function(){return s}),e.d(n,"schemeSet2",function(){return l}),e.d(n,"schemeSet3",function(){return h}),e.d(n,"schemeTableau10",function(){return d}),e.d(n,"interpolateBrBG",function(){return y}),e.d(n,"schemeBrBG",function(){return g}),e.d(n,"interpolatePRGn",function(){return b}),e.d(n,"schemePRGn",function(){return m}),e.d(n,"interpolatePiYG",function(){return x}),e.d(n,"schemePiYG",function(){return _}),e.d(n,"interpolatePuOr",function(){return A}),e.d(n,"schemePuOr",function(){return w}),e.d(n,"interpolateRdBu",function(){return M}),e.d(n,"schemeRdBu",function(){return E}),e.d(n,"interpolateRdGy",function(){return B}),e.d(n,"schemeRdGy",function(){return k}),e.d(n,"interpolateRdYlBu",function(){return T}),e.d(n,"schemeRdYlBu",function(){return F}),e.d(n,"interpolateRdYlGn",function(){return C}),e.d(n,"schemeRdYlGn",function(){return N}),e.d(n,"interpolateSpectral",function(){return O}),e.d(n,"schemeSpectral",function(){return S}),e.d(n,"interpolateBuGn",function(){return P}),e.d(n,"schemeBuGn",function(){return R}),e.d(n,"interpolateBuPu",function(){return D}),e.d(n,"schemeBuPu",function(){return j}),e.d(n,"interpolateGnBu",function(){return I}),e.d(n,"schemeGnBu",function(){return L}),e.d(n,"interpolateOrRd",function(){return z}),e.d(n,"schemeOrRd",function(){return U}),e.d(n,"interpolatePuBuGn",function(){return q}),e.d(n,"schemePuBuGn",function(){return Y}),e.d(n,"interpolatePuBu",function(){return $}),e.d(n,"schemePuBu",function(){return H}),e.d(n,"interpolatePuRd",function(){return G}),e.d(n,"schemePuRd",function(){return V}),e.d(n,"interpolateRdPu",function(){return W}),e.d(n,"schemeRdPu",function(){return X}),e.d(n,"interpolateYlGnBu",function(){return Z}),e.d(n,"schemeYlGnBu",function(){return J}),e.d(n,"interpolateYlGn",function(){return K}),e.d(n,"schemeYlGn",function(){return Q}),e.d(n,"interpolateYlOrBr",function(){return nt}),e.d(n,"schemeYlOrBr",function(){return tt}),e.d(n,"interpolateYlOrRd",function(){return rt}),e.d(n,"schemeYlOrRd",function(){return et}),e.d(n,"interpolateBlues",function(){return ot}),e.d(n,"schemeBlues",function(){return it}),e.d(n,"interpolateGreens",function(){return at}),e.d(n,"schemeGreens",function(){return ut}),e.d(n,"interpolateGreys",function(){return ft}),e.d(n,"schemeGreys",function(){return ct}),e.d(n,"interpolatePurples",function(){return lt}),e.d(n,"schemePurples",function(){return st}),e.d(n,"interpolateReds",function(){return dt}),e.d(n,"schemeReds",function(){return ht}),e.d(n,"interpolateOranges",function(){return vt}),e.d(n,"schemeOranges",function(){return pt}),e.d(n,"interpolateCividis",function(){return gt}),e.d(n,"interpolateCubehelixDefault",function(){return mt}),e.d(n,"interpolateRainbow",function(){return wt}),e.d(n,"interpolateWarm",function(){return bt}),e.d(n,"interpolateCool",function(){return _t}),e.d(n,"interpolateSinebow",function(){return kt}),e.d(n,"interpolateTurbo",function(){return Bt}),e.d(n,"interpolateViridis",function(){return Tt}),e.d(n,"interpolateMagma",function(){return Nt}),e.d(n,"interpolateInferno",function(){return Ct}),e.d(n,"interpolatePlasma",function(){return St})},1317:function(t,n,e){"use strict";function r(t,n){return t.parent===n.parent?1:2}function i(t,n){return t+n.x}function o(t,n){return Math.max(t,n.y)}e.r(n);var u=function(){var t=r,n=1,e=1,u=!1;function a(r){var a,c=0;r.eachAfter(function(n){var e=n.children;e?(n.x=function(t){return t.reduce(i,0)/t.length}(e),n.y=function(t){return 1+t.reduce(o,0)}(e)):(n.x=a?c+=t(n,a):0,n.y=0,a=n)});var f=function(t){for(var n;n=t.children;)t=n[0];return t}(r),s=function(t){for(var n;n=t.children;)t=n[n.length-1];return t}(r),l=f.x-t(f,s)/2,h=s.x+t(s,f)/2;return r.eachAfter(u?function(t){t.x=(t.x-r.x)*n,t.y=(r.y-t.y)*e}:function(t){t.x=(t.x-l)/(h-l)*n,t.y=(1-(r.y?t.y/r.y:1))*e})}return a.separation=function(n){return arguments.length?(t=n,a):t},a.size=function(t){return arguments.length?(u=!1,n=+t[0],e=+t[1],a):u?null:[n,e]},a.nodeSize=function(t){return arguments.length?(u=!0,n=+t[0],e=+t[1],a):u?[n,e]:null},a};function a(t){var n=0,e=t.children,r=e&&e.length;if(r)for(;--r>=0;)n+=e[r].value;else n=1;t.value=n}function c(t,n){var e,r,i,o,u,a=new h(t),c=+t.value&&(a.value=t.value),s=[a];for(null==n&&(n=f);e=s.pop();)if(c&&(e.value=+e.data.value),(i=n(e.data))&&(u=i.length))for(e.children=new Array(u),o=u-1;o>=0;--o)s.push(r=e.children[o]=new h(i[o])),r.parent=e,r.depth=e.depth+1;return a.eachBefore(l)}function f(t){return t.children}function s(t){t.data=t.data.data}function l(t){var n=0;do{t.height=n}while((t=t.parent)&&t.height<++n)}function h(t){this.data=t,this.depth=this.height=0,this.parent=null}h.prototype=c.prototype={constructor:h,count:function(){return this.eachAfter(a)},each:function(t){var n,e,r,i,o=this,u=[o];do{for(n=u.reverse(),u=[];o=n.pop();)if(t(o),e=o.children)for(r=0,i=e.length;r<i;++r)u.push(e[r])}while(u.length);return this},eachAfter:function(t){for(var n,e,r,i=this,o=[i],u=[];i=o.pop();)if(u.push(i),n=i.children)for(e=0,r=n.length;e<r;++e)o.push(n[e]);for(;i=u.pop();)t(i);return this},eachBefore:function(t){for(var n,e,r=this,i=[r];r=i.pop();)if(t(r),n=r.children)for(e=n.length-1;e>=0;--e)i.push(n[e]);return this},sum:function(t){return this.eachAfter(function(n){for(var e=+t(n.data)||0,r=n.children,i=r&&r.length;--i>=0;)e+=r[i].value;n.value=e})},sort:function(t){return this.eachBefore(function(n){n.children&&n.children.sort(t)})},path:function(t){for(var n=this,e=function(t,n){if(t===n)return t;var e=t.ancestors(),r=n.ancestors(),i=null;t=e.pop(),n=r.pop();for(;t===n;)i=t,t=e.pop(),n=r.pop();return i}(n,t),r=[n];n!==e;)n=n.parent,r.push(n);for(var i=r.length;t!==e;)r.splice(i,0,t),t=t.parent;return r},ancestors:function(){for(var t=this,n=[t];t=t.parent;)n.push(t);return n},descendants:function(){var t=[];return this.each(function(n){t.push(n)}),t},leaves:function(){var t=[];return this.eachBefore(function(n){n.children||t.push(n)}),t},links:function(){var t=this,n=[];return t.each(function(e){e!==t&&n.push({source:e.parent,target:e})}),n},copy:function(){return c(this).eachBefore(s)}};var d=Array.prototype.slice;var p=function(t){for(var n,e,r=0,i=(t=function(t){for(var n,e,r=t.length;r;)e=Math.random()*r--|0,n=t[r],t[r]=t[e],t[e]=n;return t}(d.call(t))).length,o=[];r<i;)n=t[r],e&&y(e,n)?++r:(e=b(o=v(o,n)),r=0);return e};function v(t,n){var e,r;if(m(n,t))return[n];for(e=0;e<t.length;++e)if(g(n,t[e])&&m(_(t[e],n),t))return[t[e],n];for(e=0;e<t.length-1;++e)for(r=e+1;r<t.length;++r)if(g(_(t[e],t[r]),n)&&g(_(t[e],n),t[r])&&g(_(t[r],n),t[e])&&m(x(t[e],t[r],n),t))return[t[e],t[r],n];throw new Error}function g(t,n){var e=t.r-n.r,r=n.x-t.x,i=n.y-t.y;return e<0||e*e<r*r+i*i}function y(t,n){var e=t.r-n.r+1e-6,r=n.x-t.x,i=n.y-t.y;return e>0&&e*e>r*r+i*i}function m(t,n){for(var e=0;e<n.length;++e)if(!y(t,n[e]))return!1;return!0}function b(t){switch(t.length){case 1:return{x:(n=t[0]).x,y:n.y,r:n.r};case 2:return _(t[0],t[1]);case 3:return x(t[0],t[1],t[2])}var n}function _(t,n){var e=t.x,r=t.y,i=t.r,o=n.x,u=n.y,a=n.r,c=o-e,f=u-r,s=a-i,l=Math.sqrt(c*c+f*f);return{x:(e+o+c/l*s)/2,y:(r+u+f/l*s)/2,r:(l+i+a)/2}}function x(t,n,e){var r=t.x,i=t.y,o=t.r,u=n.x,a=n.y,c=n.r,f=e.x,s=e.y,l=e.r,h=r-u,d=r-f,p=i-a,v=i-s,g=c-o,y=l-o,m=r*r+i*i-o*o,b=m-u*u-a*a+c*c,_=m-f*f-s*s+l*l,x=d*p-h*v,w=(p*_-v*b)/(2*x)-r,A=(v*g-p*y)/x,E=(d*b-h*_)/(2*x)-i,M=(h*y-d*g)/x,k=A*A+M*M-1,B=2*(o+w*A+E*M),F=w*w+E*E-o*o,T=-(k?(B+Math.sqrt(B*B-4*k*F))/(2*k):F/B);return{x:r+w+A*T,y:i+E+M*T,r:T}}function w(t,n,e){var r,i,o,u,a=t.x-n.x,c=t.y-n.y,f=a*a+c*c;f?(i=n.r+e.r,i*=i,u=t.r+e.r,i>(u*=u)?(r=(f+u-i)/(2*f),o=Math.sqrt(Math.max(0,u/f-r*r)),e.x=t.x-r*a-o*c,e.y=t.y-r*c+o*a):(r=(f+i-u)/(2*f),o=Math.sqrt(Math.max(0,i/f-r*r)),e.x=n.x+r*a-o*c,e.y=n.y+r*c+o*a)):(e.x=n.x+e.r,e.y=n.y)}function A(t,n){var e=t.r+n.r-1e-6,r=n.x-t.x,i=n.y-t.y;return e>0&&e*e>r*r+i*i}function E(t){var n=t._,e=t.next._,r=n.r+e.r,i=(n.x*e.r+e.x*n.r)/r,o=(n.y*e.r+e.y*n.r)/r;return i*i+o*o}function M(t){this._=t,this.next=null,this.previous=null}function k(t){if(!(i=t.length))return 0;var n,e,r,i,o,u,a,c,f,s,l;if((n=t[0]).x=0,n.y=0,!(i>1))return n.r;if(e=t[1],n.x=-e.r,e.x=n.r,e.y=0,!(i>2))return n.r+e.r;w(e,n,r=t[2]),n=new M(n),e=new M(e),r=new M(r),n.next=r.previous=e,e.next=n.previous=r,r.next=e.previous=n;t:for(a=3;a<i;++a){w(n._,e._,r=t[a]),r=new M(r),c=e.next,f=n.previous,s=e._.r,l=n._.r;do{if(s<=l){if(A(c._,r._)){e=c,n.next=e,e.previous=n,--a;continue t}s+=c._.r,c=c.next}else{if(A(f._,r._)){(n=f).next=e,e.previous=n,--a;continue t}l+=f._.r,f=f.previous}}while(c!==f.next);for(r.previous=n,r.next=e,n.next=e.previous=e=r,o=E(n);(r=r.next)!==e;)(u=E(r))<o&&(n=r,o=u);e=n.next}for(n=[e._],r=e;(r=r.next)!==e;)n.push(r._);for(r=p(n),a=0;a<i;++a)(n=t[a]).x-=r.x,n.y-=r.y;return r.r}var B=function(t){return k(t),t};function F(t){return null==t?null:T(t)}function T(t){if("function"!=typeof t)throw new Error;return t}function N(){return 0}var C=function(t){return function(){return t}};function S(t){return Math.sqrt(t.value)}var O=function(){var t=null,n=1,e=1,r=N;function i(i){return i.x=n/2,i.y=e/2,t?i.eachBefore(R(t)).eachAfter(P(r,.5)).eachBefore(j(1)):i.eachBefore(R(S)).eachAfter(P(N,1)).eachAfter(P(r,i.r/Math.min(n,e))).eachBefore(j(Math.min(n,e)/(2*i.r))),i}return i.radius=function(n){return arguments.length?(t=F(n),i):t},i.size=function(t){return arguments.length?(n=+t[0],e=+t[1],i):[n,e]},i.padding=function(t){return arguments.length?(r="function"==typeof t?t:C(+t),i):r},i};function R(t){return function(n){n.children||(n.r=Math.max(0,+t(n)||0))}}function P(t,n){return function(e){if(r=e.children){var r,i,o,u=r.length,a=t(e)*n||0;if(a)for(i=0;i<u;++i)r[i].r+=a;if(o=k(r),a)for(i=0;i<u;++i)r[i].r-=a;e.r=o+a}}}function j(t){return function(n){var e=n.parent;n.r*=t,e&&(n.x=e.x+t*n.x,n.y=e.y+t*n.y)}}var D=function(t){t.x0=Math.round(t.x0),t.y0=Math.round(t.y0),t.x1=Math.round(t.x1),t.y1=Math.round(t.y1)},L=function(t,n,e,r,i){for(var o,u=t.children,a=-1,c=u.length,f=t.value&&(r-n)/t.value;++a<c;)(o=u[a]).y0=e,o.y1=i,o.x0=n,o.x1=n+=o.value*f},I=function(){var t=1,n=1,e=0,r=!1;function i(i){var o=i.height+1;return i.x0=i.y0=e,i.x1=t,i.y1=n/o,i.eachBefore(function(t,n){return function(r){r.children&&L(r,r.x0,t*(r.depth+1)/n,r.x1,t*(r.depth+2)/n);var i=r.x0,o=r.y0,u=r.x1-e,a=r.y1-e;u<i&&(i=u=(i+u)/2),a<o&&(o=a=(o+a)/2),r.x0=i,r.y0=o,r.x1=u,r.y1=a}}(n,o)),r&&i.eachBefore(D),i}return i.round=function(t){return arguments.length?(r=!!t,i):r},i.size=function(e){return arguments.length?(t=+e[0],n=+e[1],i):[t,n]},i.padding=function(t){return arguments.length?(e=+t,i):e},i},U="$",z={depth:-1},Y={};function q(t){return t.id}function H(t){return t.parentId}var $=function(){var t=q,n=H;function e(e){var r,i,o,u,a,c,f,s=e.length,d=new Array(s),p={};for(i=0;i<s;++i)r=e[i],a=d[i]=new h(r),null!=(c=t(r,i,e))&&(c+="")&&(p[f=U+(a.id=c)]=f in p?Y:a);for(i=0;i<s;++i)if(a=d[i],null!=(c=n(e[i],i,e))&&(c+="")){if(!(u=p[U+c]))throw new Error("missing: "+c);if(u===Y)throw new Error("ambiguous: "+c);u.children?u.children.push(a):u.children=[a],a.parent=u}else{if(o)throw new Error("multiple roots");o=a}if(!o)throw new Error("no root");if(o.parent=z,o.eachBefore(function(t){t.depth=t.parent.depth+1,--s}).eachBefore(l),o.parent=null,s>0)throw new Error("cycle");return o}return e.id=function(n){return arguments.length?(t=T(n),e):t},e.parentId=function(t){return arguments.length?(n=T(t),e):n},e};function V(t,n){return t.parent===n.parent?1:2}function G(t){var n=t.children;return n?n[0]:t.t}function X(t){var n=t.children;return n?n[n.length-1]:t.t}function W(t,n,e){var r=e/(n.i-t.i);n.c-=r,n.s+=e,t.c+=r,n.z+=e,n.m+=e}function J(t,n,e){return t.a.parent===n.parent?t.a:e}function Z(t,n){this._=t,this.parent=null,this.children=null,this.A=null,this.a=this,this.z=0,this.m=0,this.c=0,this.s=0,this.t=null,this.i=n}Z.prototype=Object.create(h.prototype);var Q=function(){var t=V,n=1,e=1,r=null;function i(i){var c=function(t){for(var n,e,r,i,o,u=new Z(t,0),a=[u];n=a.pop();)if(r=n._.children)for(n.children=new Array(o=r.length),i=o-1;i>=0;--i)a.push(e=n.children[i]=new Z(r[i],i)),e.parent=n;return(u.parent=new Z(null,0)).children=[u],u}(i);if(c.eachAfter(o),c.parent.m=-c.z,c.eachBefore(u),r)i.eachBefore(a);else{var f=i,s=i,l=i;i.eachBefore(function(t){t.x<f.x&&(f=t),t.x>s.x&&(s=t),t.depth>l.depth&&(l=t)});var h=f===s?1:t(f,s)/2,d=h-f.x,p=n/(s.x+h+d),v=e/(l.depth||1);i.eachBefore(function(t){t.x=(t.x+d)*p,t.y=t.depth*v})}return i}function o(n){var e=n.children,r=n.parent.children,i=n.i?r[n.i-1]:null;if(e){!function(t){for(var n,e=0,r=0,i=t.children,o=i.length;--o>=0;)(n=i[o]).z+=e,n.m+=e,e+=n.s+(r+=n.c)}(n);var o=(e[0].z+e[e.length-1].z)/2;i?(n.z=i.z+t(n._,i._),n.m=n.z-o):n.z=o}else i&&(n.z=i.z+t(n._,i._));n.parent.A=function(n,e,r){if(e){for(var i,o=n,u=n,a=e,c=o.parent.children[0],f=o.m,s=u.m,l=a.m,h=c.m;a=X(a),o=G(o),a&&o;)c=G(c),(u=X(u)).a=n,(i=a.z+l-o.z-f+t(a._,o._))>0&&(W(J(a,n,r),n,i),f+=i,s+=i),l+=a.m,f+=o.m,h+=c.m,s+=u.m;a&&!X(u)&&(u.t=a,u.m+=l-s),o&&!G(c)&&(c.t=o,c.m+=f-h,r=n)}return r}(n,i,n.parent.A||r[0])}function u(t){t._.x=t.z+t.parent.m,t.m+=t.parent.m}function a(t){t.x*=n,t.y=t.depth*e}return i.separation=function(n){return arguments.length?(t=n,i):t},i.size=function(t){return arguments.length?(r=!1,n=+t[0],e=+t[1],i):r?null:[n,e]},i.nodeSize=function(t){return arguments.length?(r=!0,n=+t[0],e=+t[1],i):r?[n,e]:null},i},K=function(t,n,e,r,i){for(var o,u=t.children,a=-1,c=u.length,f=t.value&&(i-e)/t.value;++a<c;)(o=u[a]).x0=n,o.x1=r,o.y0=e,o.y1=e+=o.value*f},tt=(1+Math.sqrt(5))/2;function nt(t,n,e,r,i,o){for(var u,a,c,f,s,l,h,d,p,v,g,y=[],m=n.children,b=0,_=0,x=m.length,w=n.value;b<x;){c=i-e,f=o-r;do{s=m[_++].value}while(!s&&_<x);for(l=h=s,g=s*s*(v=Math.max(f/c,c/f)/(w*t)),p=Math.max(h/g,g/l);_<x;++_){if(s+=a=m[_].value,a<l&&(l=a),a>h&&(h=a),g=s*s*v,(d=Math.max(h/g,g/l))>p){s-=a;break}p=d}y.push(u={value:s,dice:c<f,children:m.slice(b,_)}),u.dice?L(u,e,r,i,w?r+=f*s/w:o):K(u,e,r,w?e+=c*s/w:i,o),w-=s,b=_}return y}var et=function t(n){function e(t,e,r,i,o){nt(n,t,e,r,i,o)}return e.ratio=function(n){return t((n=+n)>1?n:1)},e}(tt),rt=function(){var t=et,n=!1,e=1,r=1,i=[0],o=N,u=N,a=N,c=N,f=N;function s(t){return t.x0=t.y0=0,t.x1=e,t.y1=r,t.eachBefore(l),i=[0],n&&t.eachBefore(D),t}function l(n){var e=i[n.depth],r=n.x0+e,s=n.y0+e,l=n.x1-e,h=n.y1-e;l<r&&(r=l=(r+l)/2),h<s&&(s=h=(s+h)/2),n.x0=r,n.y0=s,n.x1=l,n.y1=h,n.children&&(e=i[n.depth+1]=o(n)/2,r+=f(n)-e,s+=u(n)-e,(l-=a(n)-e)<r&&(r=l=(r+l)/2),(h-=c(n)-e)<s&&(s=h=(s+h)/2),t(n,r,s,l,h))}return s.round=function(t){return arguments.length?(n=!!t,s):n},s.size=function(t){return arguments.length?(e=+t[0],r=+t[1],s):[e,r]},s.tile=function(n){return arguments.length?(t=T(n),s):t},s.padding=function(t){return arguments.length?s.paddingInner(t).paddingOuter(t):s.paddingInner()},s.paddingInner=function(t){return arguments.length?(o="function"==typeof t?t:C(+t),s):o},s.paddingOuter=function(t){return arguments.length?s.paddingTop(t).paddingRight(t).paddingBottom(t).paddingLeft(t):s.paddingTop()},s.paddingTop=function(t){return arguments.length?(u="function"==typeof t?t:C(+t),s):u},s.paddingRight=function(t){return arguments.length?(a="function"==typeof t?t:C(+t),s):a},s.paddingBottom=function(t){return arguments.length?(c="function"==typeof t?t:C(+t),s):c},s.paddingLeft=function(t){return arguments.length?(f="function"==typeof t?t:C(+t),s):f},s},it=function(t,n,e,r,i){var o,u,a=t.children,c=a.length,f=new Array(c+1);for(f[0]=u=o=0;o<c;++o)f[o+1]=u+=a[o].value;!function t(n,e,r,i,o,u,c){if(n>=e-1){var s=a[n];return s.x0=i,s.y0=o,s.x1=u,void(s.y1=c)}var l=f[n],h=r/2+l,d=n+1,p=e-1;for(;d<p;){var v=d+p>>>1;f[v]<h?d=v+1:p=v}h-f[d-1]<f[d]-h&&n+1<d&&--d;var g=f[d]-l,y=r-g;if(u-i>c-o){var m=(i*y+u*g)/r;t(n,d,g,i,o,m,c),t(d,e,y,m,o,u,c)}else{var b=(o*y+c*g)/r;t(n,d,g,i,o,u,b),t(d,e,y,i,b,u,c)}}(0,c,t.value,n,e,r,i)},ot=function(t,n,e,r,i){(1&t.depth?K:L)(t,n,e,r,i)},ut=function t(n){function e(t,e,r,i,o){if((u=t._squarify)&&u.ratio===n)for(var u,a,c,f,s,l=-1,h=u.length,d=t.value;++l<h;){for(c=(a=u[l]).children,f=a.value=0,s=c.length;f<s;++f)a.value+=c[f].value;a.dice?L(a,e,r,i,r+=(o-r)*a.value/d):K(a,e,r,e+=(i-e)*a.value/d,o),d-=a.value}else t._squarify=u=nt(n,t,e,r,i,o),u.ratio=n}return e.ratio=function(n){return t((n=+n)>1?n:1)},e}(tt);e.d(n,"cluster",function(){return u}),e.d(n,"hierarchy",function(){return c}),e.d(n,"pack",function(){return O}),e.d(n,"packSiblings",function(){return B}),e.d(n,"packEnclose",function(){return p}),e.d(n,"partition",function(){return I}),e.d(n,"stratify",function(){return $}),e.d(n,"tree",function(){return Q}),e.d(n,"treemap",function(){return rt}),e.d(n,"treemapBinary",function(){return it}),e.d(n,"treemapDice",function(){return L}),e.d(n,"treemapSlice",function(){return K}),e.d(n,"treemapSliceDice",function(){return ot}),e.d(n,"treemapSquarify",function(){return et}),e.d(n,"treemapResquarify",function(){return ut})},1321:function(t,n,e){"use strict";e.r(n);var r=e(35);function i(t,n){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(n).domain(t)}return this}function o(t,n){switch(arguments.length){case 0:break;case 1:this.interpolator(t);break;default:this.interpolator(n).domain(t)}return this}var u=e(179),a=Array.prototype,c=a.map,f=a.slice,s={name:"implicit"};function l(){var t=Object(u.map)(),n=[],e=[],r=s;function o(i){var o=i+"",u=t.get(o);if(!u){if(r!==s)return r;t.set(o,u=n.push(i))}return e[(u-1)%e.length]}return o.domain=function(e){if(!arguments.length)return n.slice();n=[],t=Object(u.map)();for(var r,i,a=-1,c=e.length;++a<c;)t.has(i=(r=e[a])+"")||t.set(i,n.push(r));return o},o.range=function(t){return arguments.length?(e=f.call(t),o):e.slice()},o.unknown=function(t){return arguments.length?(r=t,o):r},o.copy=function(){return l(n,e).unknown(r)},i.apply(o,arguments),o}function h(){var t,n,e=l().unknown(void 0),o=e.domain,u=e.range,a=[0,1],c=!1,f=0,s=0,d=.5;function p(){var e=o().length,i=a[1]<a[0],l=a[i-0],h=a[1-i];t=(h-l)/Math.max(1,e-f+2*s),c&&(t=Math.floor(t)),l+=(h-l-t*(e-f))*d,n=t*(1-f),c&&(l=Math.round(l),n=Math.round(n));var p=Object(r.range)(e).map(function(n){return l+t*n});return u(i?p.reverse():p)}return delete e.unknown,e.domain=function(t){return arguments.length?(o(t),p()):o()},e.range=function(t){return arguments.length?(a=[+t[0],+t[1]],p()):a.slice()},e.rangeRound=function(t){return a=[+t[0],+t[1]],c=!0,p()},e.bandwidth=function(){return n},e.step=function(){return t},e.round=function(t){return arguments.length?(c=!!t,p()):c},e.padding=function(t){return arguments.length?(f=Math.min(1,s=+t),p()):f},e.paddingInner=function(t){return arguments.length?(f=Math.min(1,t),p()):f},e.paddingOuter=function(t){return arguments.length?(s=+t,p()):s},e.align=function(t){return arguments.length?(d=Math.max(0,Math.min(1,t)),p()):d},e.copy=function(){return h(o(),a).round(c).paddingInner(f).paddingOuter(s).align(d)},i.apply(p(),arguments)}function d(){return function t(n){var e=n.copy;return n.padding=n.paddingOuter,delete n.paddingInner,delete n.paddingOuter,n.copy=function(){return t(e())},n}(h.apply(null,arguments).paddingInner(1))}var p=e(63),v=function(t){return function(){return t}},g=function(t){return+t},y=[0,1];function m(t){return t}function b(t,n){return(n-=t=+t)?function(e){return(e-t)/n}:v(isNaN(n)?NaN:.5)}function _(t){var n,e=t[0],r=t[t.length-1];return e>r&&(n=e,e=r,r=n),function(t){return Math.max(e,Math.min(r,t))}}function x(t,n,e){var r=t[0],i=t[1],o=n[0],u=n[1];return i<r?(r=b(i,r),o=e(u,o)):(r=b(r,i),o=e(o,u)),function(t){return o(r(t))}}function w(t,n,e){var i=Math.min(t.length,n.length)-1,o=new Array(i),u=new Array(i),a=-1;for(t[i]<t[0]&&(t=t.slice().reverse(),n=n.slice().reverse());++a<i;)o[a]=b(t[a],t[a+1]),u[a]=e(n[a],n[a+1]);return function(n){var e=Object(r.bisect)(t,n,1,i)-1;return u[e](o[e](n))}}function A(t,n){return n.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function E(){var t,n,e,r,i,o,u=y,a=y,s=p.interpolate,l=m;function h(){return r=Math.min(u.length,a.length)>2?w:x,i=o=null,d}function d(n){return isNaN(n=+n)?e:(i||(i=r(u.map(t),a,s)))(t(l(n)))}return d.invert=function(e){return l(n((o||(o=r(a,u.map(t),p.interpolateNumber)))(e)))},d.domain=function(t){return arguments.length?(u=c.call(t,g),l===m||(l=_(u)),h()):u.slice()},d.range=function(t){return arguments.length?(a=f.call(t),h()):a.slice()},d.rangeRound=function(t){return a=f.call(t),s=p.interpolateRound,h()},d.clamp=function(t){return arguments.length?(l=t?_(u):m,d):l!==m},d.interpolate=function(t){return arguments.length?(s=t,h()):s},d.unknown=function(t){return arguments.length?(e=t,d):e},function(e,r){return t=e,n=r,h()}}function M(t,n){return E()(t,n)}var k=e(135),B=function(t,n,e,i){var o,u=Object(r.tickStep)(t,n,e);switch((i=Object(k.formatSpecifier)(null==i?",f":i)).type){case"s":var a=Math.max(Math.abs(t),Math.abs(n));return null!=i.precision||isNaN(o=Object(k.precisionPrefix)(u,a))||(i.precision=o),Object(k.formatPrefix)(i,a);case"":case"e":case"g":case"p":case"r":null!=i.precision||isNaN(o=Object(k.precisionRound)(u,Math.max(Math.abs(t),Math.abs(n))))||(i.precision=o-("e"===i.type));break;case"f":case"%":null!=i.precision||isNaN(o=Object(k.precisionFixed)(u))||(i.precision=o-2*("%"===i.type))}return Object(k.format)(i)};function F(t){var n=t.domain;return t.ticks=function(t){var e=n();return Object(r.ticks)(e[0],e[e.length-1],null==t?10:t)},t.tickFormat=function(t,e){var r=n();return B(r[0],r[r.length-1],null==t?10:t,e)},t.nice=function(e){null==e&&(e=10);var i,o=n(),u=0,a=o.length-1,c=o[u],f=o[a];return f<c&&(i=c,c=f,f=i,i=u,u=a,a=i),(i=Object(r.tickIncrement)(c,f,e))>0?(c=Math.floor(c/i)*i,f=Math.ceil(f/i)*i,i=Object(r.tickIncrement)(c,f,e)):i<0&&(c=Math.ceil(c*i)/i,f=Math.floor(f*i)/i,i=Object(r.tickIncrement)(c,f,e)),i>0?(o[u]=Math.floor(c/i)*i,o[a]=Math.ceil(f/i)*i,n(o)):i<0&&(o[u]=Math.ceil(c*i)/i,o[a]=Math.floor(f*i)/i,n(o)),t},t}function T(){var t=M(m,m);return t.copy=function(){return A(t,T())},i.apply(t,arguments),F(t)}function N(t){var n;function e(t){return isNaN(t=+t)?n:t}return e.invert=e,e.domain=e.range=function(n){return arguments.length?(t=c.call(n,g),e):t.slice()},e.unknown=function(t){return arguments.length?(n=t,e):n},e.copy=function(){return N(t).unknown(n)},t=arguments.length?c.call(t,g):[0,1],F(e)}var C=function(t,n){var e,r=0,i=(t=t.slice()).length-1,o=t[r],u=t[i];return u<o&&(e=r,r=i,i=e,e=o,o=u,u=e),t[r]=n.floor(o),t[i]=n.ceil(u),t};function S(t){return Math.log(t)}function O(t){return Math.exp(t)}function R(t){return-Math.log(-t)}function P(t){return-Math.exp(-t)}function j(t){return isFinite(t)?+("1e"+t):t<0?0:t}function D(t){return function(n){return-t(-n)}}function L(t){var n,e,i=t(S,O),o=i.domain,u=10;function a(){return n=function(t){return t===Math.E?Math.log:10===t&&Math.log10||2===t&&Math.log2||(t=Math.log(t),function(n){return Math.log(n)/t})}(u),e=function(t){return 10===t?j:t===Math.E?Math.exp:function(n){return Math.pow(t,n)}}(u),o()[0]<0?(n=D(n),e=D(e),t(R,P)):t(S,O),i}return i.base=function(t){return arguments.length?(u=+t,a()):u},i.domain=function(t){return arguments.length?(o(t),a()):o()},i.ticks=function(t){var i,a=o(),c=a[0],f=a[a.length-1];(i=f<c)&&(d=c,c=f,f=d);var s,l,h,d=n(c),p=n(f),v=null==t?10:+t,g=[];if(!(u%1)&&p-d<v){if(d=Math.round(d)-1,p=Math.round(p)+1,c>0){for(;d<p;++d)for(l=1,s=e(d);l<u;++l)if(!((h=s*l)<c)){if(h>f)break;g.push(h)}}else for(;d<p;++d)for(l=u-1,s=e(d);l>=1;--l)if(!((h=s*l)<c)){if(h>f)break;g.push(h)}}else g=Object(r.ticks)(d,p,Math.min(p-d,v)).map(e);return i?g.reverse():g},i.tickFormat=function(t,r){if(null==r&&(r=10===u?".0e":","),"function"!=typeof r&&(r=Object(k.format)(r)),t===1/0)return r;null==t&&(t=10);var o=Math.max(1,u*t/i.ticks().length);return function(t){var i=t/e(Math.round(n(t)));return i*u<u-.5&&(i*=u),i<=o?r(t):""}},i.nice=function(){return o(C(o(),{floor:function(t){return e(Math.floor(n(t)))},ceil:function(t){return e(Math.ceil(n(t)))}}))},i}function I(){var t=L(E()).domain([1,10]);return t.copy=function(){return A(t,I()).base(t.base())},i.apply(t,arguments),t}function U(t){return function(n){return Math.sign(n)*Math.log1p(Math.abs(n/t))}}function z(t){return function(n){return Math.sign(n)*Math.expm1(Math.abs(n))*t}}function Y(t){var n=1,e=t(U(n),z(n));return e.constant=function(e){return arguments.length?t(U(n=+e),z(n)):n},F(e)}function q(){var t=Y(E());return t.copy=function(){return A(t,q()).constant(t.constant())},i.apply(t,arguments)}function H(t){return function(n){return n<0?-Math.pow(-n,t):Math.pow(n,t)}}function $(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function V(t){return t<0?-t*t:t*t}function G(t){var n=t(m,m),e=1;function r(){return 1===e?t(m,m):.5===e?t($,V):t(H(e),H(1/e))}return n.exponent=function(t){return arguments.length?(e=+t,r()):e},F(n)}function X(){var t=G(E());return t.copy=function(){return A(t,X()).exponent(t.exponent())},i.apply(t,arguments),t}function W(){return X.apply(null,arguments).exponent(.5)}function J(){var t,n=[],e=[],o=[];function u(){var t=0,i=Math.max(1,e.length);for(o=new Array(i-1);++t<i;)o[t-1]=Object(r.quantile)(n,t/i);return a}function a(n){return isNaN(n=+n)?t:e[Object(r.bisect)(o,n)]}return a.invertExtent=function(t){var r=e.indexOf(t);return r<0?[NaN,NaN]:[r>0?o[r-1]:n[0],r<o.length?o[r]:n[n.length-1]]},a.domain=function(t){if(!arguments.length)return n.slice();n=[];for(var e,i=0,o=t.length;i<o;++i)null==(e=t[i])||isNaN(e=+e)||n.push(e);return n.sort(r.ascending),u()},a.range=function(t){return arguments.length?(e=f.call(t),u()):e.slice()},a.unknown=function(n){return arguments.length?(t=n,a):t},a.quantiles=function(){return o.slice()},a.copy=function(){return J().domain(n).range(e).unknown(t)},i.apply(a,arguments)}function Z(){var t,n=0,e=1,o=1,u=[.5],a=[0,1];function c(n){return n<=n?a[Object(r.bisect)(u,n,0,o)]:t}function s(){var t=-1;for(u=new Array(o);++t<o;)u[t]=((t+1)*e-(t-o)*n)/(o+1);return c}return c.domain=function(t){return arguments.length?(n=+t[0],e=+t[1],s()):[n,e]},c.range=function(t){return arguments.length?(o=(a=f.call(t)).length-1,s()):a.slice()},c.invertExtent=function(t){var r=a.indexOf(t);return r<0?[NaN,NaN]:r<1?[n,u[0]]:r>=o?[u[o-1],e]:[u[r-1],u[r]]},c.unknown=function(n){return arguments.length?(t=n,c):c},c.thresholds=function(){return u.slice()},c.copy=function(){return Z().domain([n,e]).range(a).unknown(t)},i.apply(F(c),arguments)}function Q(){var t,n=[.5],e=[0,1],o=1;function u(i){return i<=i?e[Object(r.bisect)(n,i,0,o)]:t}return u.domain=function(t){return arguments.length?(n=f.call(t),o=Math.min(n.length,e.length-1),u):n.slice()},u.range=function(t){return arguments.length?(e=f.call(t),o=Math.min(n.length,e.length-1),u):e.slice()},u.invertExtent=function(t){var r=e.indexOf(t);return[n[r-1],n[r]]},u.unknown=function(n){return arguments.length?(t=n,u):t},u.copy=function(){return Q().domain(n).range(e).unknown(t)},i.apply(u,arguments)}var K=e(38),tt=e(316),nt=1e3,et=60*nt,rt=60*et,it=24*rt,ot=7*it,ut=30*it,at=365*it;function ct(t){return new Date(t)}function ft(t){return t instanceof Date?+t:+new Date(+t)}function st(t,n,e,i,o,u,a,f,s){var l=M(m,m),h=l.invert,d=l.domain,p=s(".%L"),v=s(":%S"),g=s("%I:%M"),y=s("%I %p"),b=s("%a %d"),_=s("%b %d"),x=s("%B"),w=s("%Y"),E=[[a,1,nt],[a,5,5*nt],[a,15,15*nt],[a,30,30*nt],[u,1,et],[u,5,5*et],[u,15,15*et],[u,30,30*et],[o,1,rt],[o,3,3*rt],[o,6,6*rt],[o,12,12*rt],[i,1,it],[i,2,2*it],[e,1,ot],[n,1,ut],[n,3,3*ut],[t,1,at]];function k(r){return(a(r)<r?p:u(r)<r?v:o(r)<r?g:i(r)<r?y:n(r)<r?e(r)<r?b:_:t(r)<r?x:w)(r)}function B(n,e,i,o){if(null==n&&(n=10),"number"==typeof n){var u=Math.abs(i-e)/n,a=Object(r.bisector)(function(t){return t[2]}).right(E,u);a===E.length?(o=Object(r.tickStep)(e/at,i/at,n),n=t):a?(o=(a=E[u/E[a-1][2]<E[a][2]/u?a-1:a])[1],n=a[0]):(o=Math.max(Object(r.tickStep)(e,i,n),1),n=f)}return null==o?n:n.every(o)}return l.invert=function(t){return new Date(h(t))},l.domain=function(t){return arguments.length?d(c.call(t,ft)):d().map(ct)},l.ticks=function(t,n){var e,r=d(),i=r[0],o=r[r.length-1],u=o<i;return u&&(e=i,i=o,o=e),e=(e=B(t,i,o,n))?e.range(i,o+1):[],u?e.reverse():e},l.tickFormat=function(t,n){return null==n?k:s(n)},l.nice=function(t,n){var e=d();return(t=B(t,e[0],e[e.length-1],n))?d(C(e,t)):l},l.copy=function(){return A(l,st(t,n,e,i,o,u,a,f,s))},l}var lt=function(){return i.apply(st(K.timeYear,K.timeMonth,K.timeWeek,K.timeDay,K.timeHour,K.timeMinute,K.timeSecond,K.timeMillisecond,tt.timeFormat).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)},ht=function(){return i.apply(st(K.utcYear,K.utcMonth,K.utcWeek,K.utcDay,K.utcHour,K.utcMinute,K.utcSecond,K.utcMillisecond,tt.utcFormat).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)};function dt(){var t,n,e,r,i,o=0,u=1,a=m,c=!1;function f(n){return isNaN(n=+n)?i:a(0===e?.5:(n=(r(n)-t)*e,c?Math.max(0,Math.min(1,n)):n))}return f.domain=function(i){return arguments.length?(t=r(o=+i[0]),n=r(u=+i[1]),e=t===n?0:1/(n-t),f):[o,u]},f.clamp=function(t){return arguments.length?(c=!!t,f):c},f.interpolator=function(t){return arguments.length?(a=t,f):a},f.unknown=function(t){return arguments.length?(i=t,f):i},function(i){return r=i,t=i(o),n=i(u),e=t===n?0:1/(n-t),f}}function pt(t,n){return n.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function vt(){var t=F(dt()(m));return t.copy=function(){return pt(t,vt())},o.apply(t,arguments)}function gt(){var t=L(dt()).domain([1,10]);return t.copy=function(){return pt(t,gt()).base(t.base())},o.apply(t,arguments)}function yt(){var t=Y(dt());return t.copy=function(){return pt(t,yt()).constant(t.constant())},o.apply(t,arguments)}function mt(){var t=G(dt());return t.copy=function(){return pt(t,mt()).exponent(t.exponent())},o.apply(t,arguments)}function bt(){return mt.apply(null,arguments).exponent(.5)}function _t(){var t=[],n=m;function e(e){if(!isNaN(e=+e))return n((Object(r.bisect)(t,e)-1)/(t.length-1))}return e.domain=function(n){if(!arguments.length)return t.slice();t=[];for(var i,o=0,u=n.length;o<u;++o)null==(i=n[o])||isNaN(i=+i)||t.push(i);return t.sort(r.ascending),e},e.interpolator=function(t){return arguments.length?(n=t,e):n},e.copy=function(){return _t(n).domain(t)},o.apply(e,arguments)}function xt(){var t,n,e,r,i,o,u,a=0,c=.5,f=1,s=m,l=!1;function h(t){return isNaN(t=+t)?u:(t=.5+((t=+o(t))-n)*(t<n?r:i),s(l?Math.max(0,Math.min(1,t)):t))}return h.domain=function(u){return arguments.length?(t=o(a=+u[0]),n=o(c=+u[1]),e=o(f=+u[2]),r=t===n?0:.5/(n-t),i=n===e?0:.5/(e-n),h):[a,c,f]},h.clamp=function(t){return arguments.length?(l=!!t,h):l},h.interpolator=function(t){return arguments.length?(s=t,h):s},h.unknown=function(t){return arguments.length?(u=t,h):u},function(u){return o=u,t=u(a),n=u(c),e=u(f),r=t===n?0:.5/(n-t),i=n===e?0:.5/(e-n),h}}function wt(){var t=F(xt()(m));return t.copy=function(){return pt(t,wt())},o.apply(t,arguments)}function At(){var t=L(xt()).domain([.1,1,10]);return t.copy=function(){return pt(t,At()).base(t.base())},o.apply(t,arguments)}function Et(){var t=Y(xt());return t.copy=function(){return pt(t,Et()).constant(t.constant())},o.apply(t,arguments)}function Mt(){var t=G(xt());return t.copy=function(){return pt(t,Mt()).exponent(t.exponent())},o.apply(t,arguments)}function kt(){return Mt.apply(null,arguments).exponent(.5)}e.d(n,"scaleBand",function(){return h}),e.d(n,"scalePoint",function(){return d}),e.d(n,"scaleIdentity",function(){return N}),e.d(n,"scaleLinear",function(){return T}),e.d(n,"scaleLog",function(){return I}),e.d(n,"scaleSymlog",function(){return q}),e.d(n,"scaleOrdinal",function(){return l}),e.d(n,"scaleImplicit",function(){return s}),e.d(n,"scalePow",function(){return X}),e.d(n,"scaleSqrt",function(){return W}),e.d(n,"scaleQuantile",function(){return J}),e.d(n,"scaleQuantize",function(){return Z}),e.d(n,"scaleThreshold",function(){return Q}),e.d(n,"scaleTime",function(){return lt}),e.d(n,"scaleUtc",function(){return ht}),e.d(n,"scaleSequential",function(){return vt}),e.d(n,"scaleSequentialLog",function(){return gt}),e.d(n,"scaleSequentialPow",function(){return mt}),e.d(n,"scaleSequentialSqrt",function(){return bt}),e.d(n,"scaleSequentialSymlog",function(){return yt}),e.d(n,"scaleSequentialQuantile",function(){return _t}),e.d(n,"scaleDiverging",function(){return wt}),e.d(n,"scaleDivergingLog",function(){return At}),e.d(n,"scaleDivergingPow",function(){return Mt}),e.d(n,"scaleDivergingSqrt",function(){return kt}),e.d(n,"scaleDivergingSymlog",function(){return Et}),e.d(n,"tickFormat",function(){return B})},1326:function(t,n,e){"use strict";e.r(n);var r=function(t,n){var e;function r(){var r,i,o=e.length,u=0,a=0;for(r=0;r<o;++r)u+=(i=e[r]).x,a+=i.y;for(u=u/o-t,a=a/o-n,r=0;r<o;++r)(i=e[r]).x-=u,i.y-=a}return null==t&&(t=0),null==n&&(n=0),r.initialize=function(t){e=t},r.x=function(n){return arguments.length?(t=+n,r):t},r.y=function(t){return arguments.length?(n=+t,r):n},r},i=function(t){return function(){return t}},o=function(){return 1e-6*(Math.random()-.5)},u=e(315);function a(t){return t.x+t.vx}function c(t){return t.y+t.vy}var f=function(t){var n,e,r=1,f=1;function s(){for(var t,i,s,h,d,p,v,g=n.length,y=0;y<f;++y)for(i=Object(u.quadtree)(n,a,c).visitAfter(l),t=0;t<g;++t)s=n[t],p=e[s.index],v=p*p,h=s.x+s.vx,d=s.y+s.vy,i.visit(m);function m(t,n,e,i,u){var a=t.data,c=t.r,f=p+c;if(!a)return n>h+f||i<h-f||e>d+f||u<d-f;if(a.index>s.index){var l=h-a.x-a.vx,g=d-a.y-a.vy,y=l*l+g*g;y<f*f&&(0===l&&(y+=(l=o())*l),0===g&&(y+=(g=o())*g),y=(f-(y=Math.sqrt(y)))/y*r,s.vx+=(l*=y)*(f=(c*=c)/(v+c)),s.vy+=(g*=y)*f,a.vx-=l*(f=1-f),a.vy-=g*f)}}}function l(t){if(t.data)return t.r=e[t.data.index];for(var n=t.r=0;n<4;++n)t[n]&&t[n].r>t.r&&(t.r=t[n].r)}function h(){if(n){var r,i,o=n.length;for(e=new Array(o),r=0;r<o;++r)i=n[r],e[i.index]=+t(i,r,n)}}return"function"!=typeof t&&(t=i(null==t?1:+t)),s.initialize=function(t){n=t,h()},s.iterations=function(t){return arguments.length?(f=+t,s):f},s.strength=function(t){return arguments.length?(r=+t,s):r},s.radius=function(n){return arguments.length?(t="function"==typeof n?n:i(+n),h(),s):t},s},s=e(179);function l(t){return t.index}function h(t,n){var e=t.get(n);if(!e)throw new Error("missing: "+n);return e}var d=function(t){var n,e,r,u,a,c=l,f=function(t){return 1/Math.min(u[t.source.index],u[t.target.index])},d=i(30),p=1;function v(r){for(var i=0,u=t.length;i<p;++i)for(var c,f,s,l,h,d,v,g=0;g<u;++g)f=(c=t[g]).source,l=(s=c.target).x+s.vx-f.x-f.vx||o(),h=s.y+s.vy-f.y-f.vy||o(),l*=d=((d=Math.sqrt(l*l+h*h))-e[g])/d*r*n[g],h*=d,s.vx-=l*(v=a[g]),s.vy-=h*v,f.vx+=l*(v=1-v),f.vy+=h*v}function g(){if(r){var i,o,f=r.length,l=t.length,d=Object(s.map)(r,c);for(i=0,u=new Array(f);i<l;++i)(o=t[i]).index=i,"object"!=typeof o.source&&(o.source=h(d,o.source)),"object"!=typeof o.target&&(o.target=h(d,o.target)),u[o.source.index]=(u[o.source.index]||0)+1,u[o.target.index]=(u[o.target.index]||0)+1;for(i=0,a=new Array(l);i<l;++i)o=t[i],a[i]=u[o.source.index]/(u[o.source.index]+u[o.target.index]);n=new Array(l),y(),e=new Array(l),m()}}function y(){if(r)for(var e=0,i=t.length;e<i;++e)n[e]=+f(t[e],e,t)}function m(){if(r)for(var n=0,i=t.length;n<i;++n)e[n]=+d(t[n],n,t)}return null==t&&(t=[]),v.initialize=function(t){r=t,g()},v.links=function(n){return arguments.length?(t=n,g(),v):t},v.id=function(t){return arguments.length?(c=t,v):c},v.iterations=function(t){return arguments.length?(p=+t,v):p},v.strength=function(t){return arguments.length?(f="function"==typeof t?t:i(+t),y(),v):f},v.distance=function(t){return arguments.length?(d="function"==typeof t?t:i(+t),m(),v):d},v},p=e(126),v=e(134);function g(t){return t.x}function y(t){return t.y}var m=10,b=Math.PI*(3-Math.sqrt(5)),_=function(t){var n,e=1,r=.001,i=1-Math.pow(r,1/300),o=0,u=.6,a=Object(s.map)(),c=Object(v.timer)(l),f=Object(p.dispatch)("tick","end");function l(){h(),f.call("tick",n),e<r&&(c.stop(),f.call("end",n))}function h(r){var c,f,s=t.length;void 0===r&&(r=1);for(var l=0;l<r;++l)for(e+=(o-e)*i,a.each(function(t){t(e)}),c=0;c<s;++c)null==(f=t[c]).fx?f.x+=f.vx*=u:(f.x=f.fx,f.vx=0),null==f.fy?f.y+=f.vy*=u:(f.y=f.fy,f.vy=0);return n}function d(){for(var n,e=0,r=t.length;e<r;++e){if((n=t[e]).index=e,null!=n.fx&&(n.x=n.fx),null!=n.fy&&(n.y=n.fy),isNaN(n.x)||isNaN(n.y)){var i=m*Math.sqrt(e),o=e*b;n.x=i*Math.cos(o),n.y=i*Math.sin(o)}(isNaN(n.vx)||isNaN(n.vy))&&(n.vx=n.vy=0)}}function g(n){return n.initialize&&n.initialize(t),n}return null==t&&(t=[]),d(),n={tick:h,restart:function(){return c.restart(l),n},stop:function(){return c.stop(),n},nodes:function(e){return arguments.length?(t=e,d(),a.each(g),n):t},alpha:function(t){return arguments.length?(e=+t,n):e},alphaMin:function(t){return arguments.length?(r=+t,n):r},alphaDecay:function(t){return arguments.length?(i=+t,n):+i},alphaTarget:function(t){return arguments.length?(o=+t,n):o},velocityDecay:function(t){return arguments.length?(u=1-t,n):1-u},force:function(t,e){return arguments.length>1?(null==e?a.remove(t):a.set(t,g(e)),n):a.get(t)},find:function(n,e,r){var i,o,u,a,c,f=0,s=t.length;for(null==r?r=1/0:r*=r,f=0;f<s;++f)(u=(i=n-(a=t[f]).x)*i+(o=e-a.y)*o)<r&&(c=a,r=u);return c},on:function(t,e){return arguments.length>1?(f.on(t,e),n):f.on(t)}}},x=function(){var t,n,e,r,a=i(-30),c=1,f=1/0,s=.81;function l(r){var i,o=t.length,a=Object(u.quadtree)(t,g,y).visitAfter(d);for(e=r,i=0;i<o;++i)n=t[i],a.visit(p)}function h(){if(t){var n,e,i=t.length;for(r=new Array(i),n=0;n<i;++n)e=t[n],r[e.index]=+a(e,n,t)}}function d(t){var n,e,i,o,u,a=0,c=0;if(t.length){for(i=o=u=0;u<4;++u)(n=t[u])&&(e=Math.abs(n.value))&&(a+=n.value,c+=e,i+=e*n.x,o+=e*n.y);t.x=i/c,t.y=o/c}else{(n=t).x=n.data.x,n.y=n.data.y;do{a+=r[n.data.index]}while(n=n.next)}t.value=a}function p(t,i,u,a){if(!t.value)return!0;var l=t.x-n.x,h=t.y-n.y,d=a-i,p=l*l+h*h;if(d*d/s<p)return p<f&&(0===l&&(p+=(l=o())*l),0===h&&(p+=(h=o())*h),p<c&&(p=Math.sqrt(c*p)),n.vx+=l*t.value*e/p,n.vy+=h*t.value*e/p),!0;if(!(t.length||p>=f)){(t.data!==n||t.next)&&(0===l&&(p+=(l=o())*l),0===h&&(p+=(h=o())*h),p<c&&(p=Math.sqrt(c*p)));do{t.data!==n&&(d=r[t.data.index]*e/p,n.vx+=l*d,n.vy+=h*d)}while(t=t.next)}}return l.initialize=function(n){t=n,h()},l.strength=function(t){return arguments.length?(a="function"==typeof t?t:i(+t),h(),l):a},l.distanceMin=function(t){return arguments.length?(c=t*t,l):Math.sqrt(c)},l.distanceMax=function(t){return arguments.length?(f=t*t,l):Math.sqrt(f)},l.theta=function(t){return arguments.length?(s=t*t,l):Math.sqrt(s)},l},w=function(t,n,e){var r,o,u,a=i(.1);function c(t){for(var i=0,a=r.length;i<a;++i){var c=r[i],f=c.x-n||1e-6,s=c.y-e||1e-6,l=Math.sqrt(f*f+s*s),h=(u[i]-l)*o[i]*t/l;c.vx+=f*h,c.vy+=s*h}}function f(){if(r){var n,e=r.length;for(o=new Array(e),u=new Array(e),n=0;n<e;++n)u[n]=+t(r[n],n,r),o[n]=isNaN(u[n])?0:+a(r[n],n,r)}}return"function"!=typeof t&&(t=i(+t)),null==n&&(n=0),null==e&&(e=0),c.initialize=function(t){r=t,f()},c.strength=function(t){return arguments.length?(a="function"==typeof t?t:i(+t),f(),c):a},c.radius=function(n){return arguments.length?(t="function"==typeof n?n:i(+n),f(),c):t},c.x=function(t){return arguments.length?(n=+t,c):n},c.y=function(t){return arguments.length?(e=+t,c):e},c},A=function(t){var n,e,r,o=i(.1);function u(t){for(var i,o=0,u=n.length;o<u;++o)(i=n[o]).vx+=(r[o]-i.x)*e[o]*t}function a(){if(n){var i,u=n.length;for(e=new Array(u),r=new Array(u),i=0;i<u;++i)e[i]=isNaN(r[i]=+t(n[i],i,n))?0:+o(n[i],i,n)}}return"function"!=typeof t&&(t=i(null==t?0:+t)),u.initialize=function(t){n=t,a()},u.strength=function(t){return arguments.length?(o="function"==typeof t?t:i(+t),a(),u):o},u.x=function(n){return arguments.length?(t="function"==typeof n?n:i(+n),a(),u):t},u},E=function(t){var n,e,r,o=i(.1);function u(t){for(var i,o=0,u=n.length;o<u;++o)(i=n[o]).vy+=(r[o]-i.y)*e[o]*t}function a(){if(n){var i,u=n.length;for(e=new Array(u),r=new Array(u),i=0;i<u;++i)e[i]=isNaN(r[i]=+t(n[i],i,n))?0:+o(n[i],i,n)}}return"function"!=typeof t&&(t=i(null==t?0:+t)),u.initialize=function(t){n=t,a()},u.strength=function(t){return arguments.length?(o="function"==typeof t?t:i(+t),a(),u):o},u.y=function(n){return arguments.length?(t="function"==typeof n?n:i(+n),a(),u):t},u};e.d(n,"forceCenter",function(){return r}),e.d(n,"forceCollide",function(){return f}),e.d(n,"forceLink",function(){return d}),e.d(n,"forceManyBody",function(){return x}),e.d(n,"forceRadial",function(){return w}),e.d(n,"forceSimulation",function(){return _}),e.d(n,"forceX",function(){return A}),e.d(n,"forceY",function(){return E})},1327:function(t,n,e){"use strict";e.r(n);var r=function(t){return function(){return t}};function i(t){return t[0]}function o(t){return t[1]}function u(){this._=null}function a(t){t.U=t.C=t.L=t.R=t.P=t.N=null}function c(t,n){var e=n,r=n.R,i=e.U;i?i.L===e?i.L=r:i.R=r:t._=r,r.U=i,e.U=r,e.R=r.L,e.R&&(e.R.U=e),r.L=e}function f(t,n){var e=n,r=n.L,i=e.U;i?i.L===e?i.L=r:i.R=r:t._=r,r.U=i,e.U=r,e.L=r.R,e.L&&(e.L.U=e),r.R=e}function s(t){for(;t.L;)t=t.L;return t}u.prototype={constructor:u,insert:function(t,n){var e,r,i;if(t){if(n.P=t,n.N=t.N,t.N&&(t.N.P=n),t.N=n,t.R){for(t=t.R;t.L;)t=t.L;t.L=n}else t.R=n;e=t}else this._?(t=s(this._),n.P=null,n.N=t,t.P=t.L=n,e=t):(n.P=n.N=null,this._=n,e=null);for(n.L=n.R=null,n.U=e,n.C=!0,t=n;e&&e.C;)e===(r=e.U).L?(i=r.R)&&i.C?(e.C=i.C=!1,r.C=!0,t=r):(t===e.R&&(c(this,e),e=(t=e).U),e.C=!1,r.C=!0,f(this,r)):(i=r.L)&&i.C?(e.C=i.C=!1,r.C=!0,t=r):(t===e.L&&(f(this,e),e=(t=e).U),e.C=!1,r.C=!0,c(this,r)),e=t.U;this._.C=!1},remove:function(t){t.N&&(t.N.P=t.P),t.P&&(t.P.N=t.N),t.N=t.P=null;var n,e,r,i=t.U,o=t.L,u=t.R;if(e=o?u?s(u):o:u,i?i.L===t?i.L=e:i.R=e:this._=e,o&&u?(r=e.C,e.C=t.C,e.L=o,o.U=e,e!==u?(i=e.U,e.U=t.U,t=e.R,i.L=t,e.R=u,u.U=e):(e.U=i,i=e,t=e.R)):(r=t.C,t=e),t&&(t.U=i),!r)if(t&&t.C)t.C=!1;else{do{if(t===this._)break;if(t===i.L){if((n=i.R).C&&(n.C=!1,i.C=!0,c(this,i),n=i.R),n.L&&n.L.C||n.R&&n.R.C){n.R&&n.R.C||(n.L.C=!1,n.C=!0,f(this,n),n=i.R),n.C=i.C,i.C=n.R.C=!1,c(this,i),t=this._;break}}else if((n=i.L).C&&(n.C=!1,i.C=!0,f(this,i),n=i.L),n.L&&n.L.C||n.R&&n.R.C){n.L&&n.L.C||(n.R.C=!1,n.C=!0,c(this,n),n=i.L),n.C=i.C,i.C=n.L.C=!1,f(this,i),t=this._;break}n.C=!0,t=i,i=i.U}while(!t.C);t&&(t.C=!1)}}};var l=u;function h(t,n,e,r){var i=[null,null],o=j.push(i)-1;return i.left=t,i.right=n,e&&p(i,t,n,e),r&&p(i,n,t,r),R[t.index].halfedges.push(o),R[n.index].halfedges.push(o),i}function d(t,n,e){var r=[n,e];return r.left=t,r}function p(t,n,e,r){t[0]||t[1]?t.left===e?t[1]=r:t[0]=r:(t[0]=r,t.left=n,t.right=e)}function v(t,n,e,r,i){var o,u=t[0],a=t[1],c=u[0],f=u[1],s=0,l=1,h=a[0]-c,d=a[1]-f;if(o=n-c,h||!(o>0)){if(o/=h,h<0){if(o<s)return;o<l&&(l=o)}else if(h>0){if(o>l)return;o>s&&(s=o)}if(o=r-c,h||!(o<0)){if(o/=h,h<0){if(o>l)return;o>s&&(s=o)}else if(h>0){if(o<s)return;o<l&&(l=o)}if(o=e-f,d||!(o>0)){if(o/=d,d<0){if(o<s)return;o<l&&(l=o)}else if(d>0){if(o>l)return;o>s&&(s=o)}if(o=i-f,d||!(o<0)){if(o/=d,d<0){if(o>l)return;o>s&&(s=o)}else if(d>0){if(o<s)return;o<l&&(l=o)}return!(s>0||l<1)||(s>0&&(t[0]=[c+s*h,f+s*d]),l<1&&(t[1]=[c+l*h,f+l*d]),!0)}}}}}function g(t,n,e,r,i){var o=t[1];if(o)return!0;var u,a,c=t[0],f=t.left,s=t.right,l=f[0],h=f[1],d=s[0],p=s[1],v=(l+d)/2,g=(h+p)/2;if(p===h){if(v<n||v>=r)return;if(l>d){if(c){if(c[1]>=i)return}else c=[v,e];o=[v,i]}else{if(c){if(c[1]<e)return}else c=[v,i];o=[v,e]}}else if(a=g-(u=(l-d)/(p-h))*v,u<-1||u>1)if(l>d){if(c){if(c[1]>=i)return}else c=[(e-a)/u,e];o=[(i-a)/u,i]}else{if(c){if(c[1]<e)return}else c=[(i-a)/u,i];o=[(e-a)/u,e]}else if(h<p){if(c){if(c[0]>=r)return}else c=[n,u*n+a];o=[r,u*r+a]}else{if(c){if(c[0]<n)return}else c=[r,u*r+a];o=[n,u*n+a]}return t[0]=c,t[1]=o,!0}function y(t,n){var e=t.site,r=n.left,i=n.right;return e===i&&(i=r,r=e),i?Math.atan2(i[1]-r[1],i[0]-r[0]):(e===r?(r=n[1],i=n[0]):(r=n[0],i=n[1]),Math.atan2(r[0]-i[0],i[1]-r[1]))}function m(t,n){return n[+(n.left!==t.site)]}function b(t,n){return n[+(n.left===t.site)]}var _,x=[];function w(){a(this),this.x=this.y=this.arc=this.site=this.cy=null}function A(t){var n=t.P,e=t.N;if(n&&e){var r=n.site,i=t.site,o=e.site;if(r!==o){var u=i[0],a=i[1],c=r[0]-u,f=r[1]-a,s=o[0]-u,l=o[1]-a,h=2*(c*l-f*s);if(!(h>=-L)){var d=c*c+f*f,p=s*s+l*l,v=(l*d-f*p)/h,g=(c*p-s*d)/h,y=x.pop()||new w;y.arc=t,y.site=i,y.x=v+u,y.y=(y.cy=g+a)+Math.sqrt(v*v+g*g),t.circle=y;for(var m=null,b=P._;b;)if(y.y<b.y||y.y===b.y&&y.x<=b.x){if(!b.L){m=b.P;break}b=b.L}else{if(!b.R){m=b;break}b=b.R}P.insert(m,y),m||(_=y)}}}}function E(t){var n=t.circle;n&&(n.P||(_=n.N),P.remove(n),x.push(n),a(n),t.circle=null)}var M=[];function k(){a(this),this.edge=this.site=this.circle=null}function B(t){var n=M.pop()||new k;return n.site=t,n}function F(t){E(t),O.remove(t),M.push(t),a(t)}function T(t){var n=t.circle,e=n.x,r=n.cy,i=[e,r],o=t.P,u=t.N,a=[t];F(t);for(var c=o;c.circle&&Math.abs(e-c.circle.x)<D&&Math.abs(r-c.circle.cy)<D;)o=c.P,a.unshift(c),F(c),c=o;a.unshift(c),E(c);for(var f=u;f.circle&&Math.abs(e-f.circle.x)<D&&Math.abs(r-f.circle.cy)<D;)u=f.N,a.push(f),F(f),f=u;a.push(f),E(f);var s,l=a.length;for(s=1;s<l;++s)f=a[s],c=a[s-1],p(f.edge,c.site,f.site,i);c=a[0],(f=a[l-1]).edge=h(c.site,f.site,null,i),A(c),A(f)}function N(t){for(var n,e,r,i,o=t[0],u=t[1],a=O._;a;)if((r=C(a,u)-o)>D)a=a.L;else{if(!((i=o-S(a,u))>D)){r>-D?(n=a.P,e=a):i>-D?(n=a,e=a.N):n=e=a;break}if(!a.R){n=a;break}a=a.R}!function(t){R[t.index]={site:t,halfedges:[]}}(t);var c=B(t);if(O.insert(n,c),n||e){if(n===e)return E(n),e=B(n.site),O.insert(c,e),c.edge=e.edge=h(n.site,c.site),A(n),void A(e);if(e){E(n),E(e);var f=n.site,s=f[0],l=f[1],d=t[0]-s,v=t[1]-l,g=e.site,y=g[0]-s,m=g[1]-l,b=2*(d*m-v*y),_=d*d+v*v,x=y*y+m*m,w=[(m*_-v*x)/b+s,(d*x-y*_)/b+l];p(e.edge,f,g,w),c.edge=h(f,t,null,w),e.edge=h(t,g,null,w),A(n),A(e)}else c.edge=h(n.site,c.site)}}function C(t,n){var e=t.site,r=e[0],i=e[1],o=i-n;if(!o)return r;var u=t.P;if(!u)return-1/0;var a=(e=u.site)[0],c=e[1],f=c-n;if(!f)return a;var s=a-r,l=1/o-1/f,h=s/f;return l?(-h+Math.sqrt(h*h-2*l*(s*s/(-2*f)-c+f/2+i-o/2)))/l+r:(r+a)/2}function S(t,n){var e=t.N;if(e)return C(e,n);var r=t.site;return r[1]===n?r[0]:1/0}var O,R,P,j,D=1e-6,L=1e-12;function I(t,n){return n[1]-t[1]||n[0]-t[0]}function U(t,n){var e,r,i,o=t.sort(I).pop();for(j=[],R=new Array(t.length),O=new l,P=new l;;)if(i=_,o&&(!i||o[1]<i.y||o[1]===i.y&&o[0]<i.x))o[0]===e&&o[1]===r||(N(o),e=o[0],r=o[1]),o=t.pop();else{if(!i)break;T(i.arc)}if(function(){for(var t,n,e,r,i=0,o=R.length;i<o;++i)if((t=R[i])&&(r=(n=t.halfedges).length)){var u=new Array(r),a=new Array(r);for(e=0;e<r;++e)u[e]=e,a[e]=y(t,j[n[e]]);for(u.sort(function(t,n){return a[n]-a[t]}),e=0;e<r;++e)a[e]=n[u[e]];for(e=0;e<r;++e)n[e]=a[e]}}(),n){var u=+n[0][0],a=+n[0][1],c=+n[1][0],f=+n[1][1];!function(t,n,e,r){for(var i,o=j.length;o--;)g(i=j[o],t,n,e,r)&&v(i,t,n,e,r)&&(Math.abs(i[0][0]-i[1][0])>D||Math.abs(i[0][1]-i[1][1])>D)||delete j[o]}(u,a,c,f),function(t,n,e,r){var i,o,u,a,c,f,s,l,h,p,v,g,y=R.length,_=!0;for(i=0;i<y;++i)if(o=R[i]){for(u=o.site,a=(c=o.halfedges).length;a--;)j[c[a]]||c.splice(a,1);for(a=0,f=c.length;a<f;)v=(p=b(o,j[c[a]]))[0],g=p[1],l=(s=m(o,j[c[++a%f]]))[0],h=s[1],(Math.abs(v-l)>D||Math.abs(g-h)>D)&&(c.splice(a,0,j.push(d(u,p,Math.abs(v-t)<D&&r-g>D?[t,Math.abs(l-t)<D?h:r]:Math.abs(g-r)<D&&e-v>D?[Math.abs(h-r)<D?l:e,r]:Math.abs(v-e)<D&&g-n>D?[e,Math.abs(l-e)<D?h:n]:Math.abs(g-n)<D&&v-t>D?[Math.abs(h-n)<D?l:t,n]:null))-1),++f);f&&(_=!1)}if(_){var x,w,A,E=1/0;for(i=0,_=null;i<y;++i)(o=R[i])&&(A=(x=(u=o.site)[0]-t)*x+(w=u[1]-n)*w)<E&&(E=A,_=o);if(_){var M=[t,n],k=[t,r],B=[e,r],F=[e,n];_.halfedges.push(j.push(d(u=_.site,M,k))-1,j.push(d(u,k,B))-1,j.push(d(u,B,F))-1,j.push(d(u,F,M))-1)}}for(i=0;i<y;++i)(o=R[i])&&(o.halfedges.length||delete R[i])}(u,a,c,f)}this.edges=j,this.cells=R,O=P=j=R=null}U.prototype={constructor:U,polygons:function(){var t=this.edges;return this.cells.map(function(n){var e=n.halfedges.map(function(e){return m(n,t[e])});return e.data=n.site.data,e})},triangles:function(){var t=[],n=this.edges;return this.cells.forEach(function(e,r){if(o=(i=e.halfedges).length)for(var i,o,u,a,c,f,s=e.site,l=-1,h=n[i[o-1]],d=h.left===s?h.right:h.left;++l<o;)u=d,d=(h=n[i[l]]).left===s?h.right:h.left,u&&d&&r<u.index&&r<d.index&&(c=u,f=d,((a=s)[0]-f[0])*(c[1]-a[1])-(a[0]-c[0])*(f[1]-a[1])<0)&&t.push([s.data,u.data,d.data])}),t},links:function(){return this.edges.filter(function(t){return t.right}).map(function(t){return{source:t.left.data,target:t.right.data}})},find:function(t,n,e){for(var r,i,o=this,u=o._found||0,a=o.cells.length;!(i=o.cells[u]);)if(++u>=a)return null;var c=t-i.site[0],f=n-i.site[1],s=c*c+f*f;do{i=o.cells[r=u],u=null,i.halfedges.forEach(function(e){var r=o.edges[e],a=r.left;if(a!==i.site&&a||(a=r.right)){var c=t-a[0],f=n-a[1],l=c*c+f*f;l<s&&(s=l,u=a.index)}})}while(null!==u);return o._found=r,null==e||s<=e*e?i.site:null}};var z=function(){var t=i,n=o,e=null;function u(r){return new U(r.map(function(e,i){var o=[Math.round(t(e,i,r)/D)*D,Math.round(n(e,i,r)/D)*D];return o.index=i,o.data=e,o}),e)}return u.polygons=function(t){return u(t).polygons()},u.links=function(t){return u(t).links()},u.triangles=function(t){return u(t).triangles()},u.x=function(n){return arguments.length?(t="function"==typeof n?n:r(+n),u):t},u.y=function(t){return arguments.length?(n="function"==typeof t?t:r(+t),u):n},u.extent=function(t){return arguments.length?(e=null==t?null:[[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]],u):e&&[[e[0][0],e[0][1]],[e[1][0],e[1][1]]]},u.size=function(t){return arguments.length?(e=null==t?null:[[0,0],[+t[0],+t[1]]],u):e&&[e[1][0]-e[0][0],e[1][1]-e[0][1]]},u};e.d(n,"voronoi",function(){return z})},1328:function(t,n,e){"use strict";e.r(n);var r=e(35),i=Array.prototype.slice,o=function(t,n){return t-n},u=function(t){for(var n=0,e=t.length,r=t[e-1][1]*t[0][0]-t[e-1][0]*t[0][1];++n<e;)r+=t[n-1][1]*t[n][0]-t[n-1][0]*t[n][1];return r},a=function(t){return function(){return t}},c=function(t,n){for(var e,r=-1,i=n.length;++r<i;)if(e=f(t,n[r]))return e;return 0};function f(t,n){for(var e=n[0],r=n[1],i=-1,o=0,u=t.length,a=u-1;o<u;a=o++){var c=t[o],f=c[0],l=c[1],h=t[a],d=h[0],p=h[1];if(s(c,h,n))return 0;l>r!=p>r&&e<(d-f)*(r-l)/(p-l)+f&&(i=-i)}return i}function s(t,n,e){var r,i,o,u;return function(t,n,e){return(n[0]-t[0])*(e[1]-t[1])==(e[0]-t[0])*(n[1]-t[1])}(t,n,e)&&(i=t[r=+(t[0]===n[0])],o=e[r],u=n[r],i<=o&&o<=u||u<=o&&o<=i)}var l=function(){},h=[[],[[[1,1.5],[.5,1]]],[[[1.5,1],[1,1.5]]],[[[1.5,1],[.5,1]]],[[[1,.5],[1.5,1]]],[[[1,1.5],[.5,1]],[[1,.5],[1.5,1]]],[[[1,.5],[1,1.5]]],[[[1,.5],[.5,1]]],[[[.5,1],[1,.5]]],[[[1,1.5],[1,.5]]],[[[.5,1],[1,.5]],[[1.5,1],[1,1.5]]],[[[1.5,1],[1,.5]]],[[[.5,1],[1.5,1]]],[[[1,1.5],[1.5,1]]],[[[.5,1],[1,1.5]]],[]],d=function(){var t=1,n=1,e=r.thresholdSturges,f=v;function s(t){var n=e(t);if(Array.isArray(n))n=n.slice().sort(o);else{var i=Object(r.extent)(t),u=i[0],a=i[1];n=Object(r.tickStep)(u,a,n),n=Object(r.range)(Math.floor(u/n)*n,Math.floor(a/n)*n,n)}return n.map(function(n){return d(t,n)})}function d(e,r){var i=[],o=[];return function(e,r,i){var o,u,a,c,f,s,l=new Array,d=new Array;o=u=-1,c=e[0]>=r,h[c<<1].forEach(v);for(;++o<t-1;)a=c,c=e[o+1]>=r,h[a|c<<1].forEach(v);h[c<<0].forEach(v);for(;++u<n-1;){for(o=-1,c=e[u*t+t]>=r,f=e[u*t]>=r,h[c<<1|f<<2].forEach(v);++o<t-1;)a=c,c=e[u*t+t+o+1]>=r,s=f,f=e[u*t+o+1]>=r,h[a|c<<1|f<<2|s<<3].forEach(v);h[c|f<<3].forEach(v)}o=-1,f=e[u*t]>=r,h[f<<2].forEach(v);for(;++o<t-1;)s=f,f=e[u*t+o+1]>=r,h[f<<2|s<<3].forEach(v);function v(t){var n,e,r=[t[0][0]+o,t[0][1]+u],a=[t[1][0]+o,t[1][1]+u],c=p(r),f=p(a);(n=d[c])?(e=l[f])?(delete d[n.end],delete l[e.start],n===e?(n.ring.push(a),i(n.ring)):l[n.start]=d[e.end]={start:n.start,end:e.end,ring:n.ring.concat(e.ring)}):(delete d[n.end],n.ring.push(a),d[n.end=f]=n):(n=l[f])?(e=d[c])?(delete l[n.start],delete d[e.end],n===e?(n.ring.push(a),i(n.ring)):l[e.start]=d[n.end]={start:e.start,end:n.end,ring:e.ring.concat(n.ring)}):(delete l[n.start],n.ring.unshift(r),l[n.start=c]=n):l[c]=d[f]={start:c,end:f,ring:[r,a]}}h[f<<3].forEach(v)}(e,r,function(t){f(t,e,r),u(t)>0?i.push([t]):o.push(t)}),o.forEach(function(t){for(var n,e=0,r=i.length;e<r;++e)if(-1!==c((n=i[e])[0],t))return void n.push(t)}),{type:"MultiPolygon",value:r,coordinates:i}}function p(n){return 2*n[0]+n[1]*(t+1)*4}function v(e,r,i){e.forEach(function(e){var o,u=e[0],a=e[1],c=0|u,f=0|a,s=r[f*t+c];u>0&&u<t&&c===u&&(o=r[f*t+c-1],e[0]=u+(i-o)/(s-o)-.5),a>0&&a<n&&f===a&&(o=r[(f-1)*t+c],e[1]=a+(i-o)/(s-o)-.5)})}return s.contour=d,s.size=function(e){if(!arguments.length)return[t,n];var r=Math.ceil(e[0]),i=Math.ceil(e[1]);if(!(r>0&&i>0))throw new Error("invalid size");return t=r,n=i,s},s.thresholds=function(t){return arguments.length?(e="function"==typeof t?t:Array.isArray(t)?a(i.call(t)):a(t),s):e},s.smooth=function(t){return arguments.length?(f=t?v:l,s):f===v},s};function p(t,n,e){for(var r=t.width,i=t.height,o=1+(e<<1),u=0;u<i;++u)for(var a=0,c=0;a<r+e;++a)a<r&&(c+=t.data[a+u*r]),a>=e&&(a>=o&&(c-=t.data[a-o+u*r]),n.data[a-e+u*r]=c/Math.min(a+1,r-1+o-a,o))}function v(t,n,e){for(var r=t.width,i=t.height,o=1+(e<<1),u=0;u<r;++u)for(var a=0,c=0;a<i+e;++a)a<i&&(c+=t.data[u+a*r]),a>=e&&(a>=o&&(c-=t.data[u+(a-o)*r]),n.data[u+(a-e)*r]=c/Math.min(a+1,i-1+o-a,o))}function g(t){return t[0]}function y(t){return t[1]}function m(){return 1}var b=function(){var t=g,n=y,e=m,o=960,u=500,c=20,f=2,s=3*c,l=o+2*s>>f,h=u+2*s>>f,b=a(20);function _(i){var o=new Float32Array(l*h),u=new Float32Array(l*h);i.forEach(function(r,i,u){var a=+t(r,i,u)+s>>f,c=+n(r,i,u)+s>>f,d=+e(r,i,u);a>=0&&a<l&&c>=0&&c<h&&(o[a+c*l]+=d)}),p({width:l,height:h,data:o},{width:l,height:h,data:u},c>>f),v({width:l,height:h,data:u},{width:l,height:h,data:o},c>>f),p({width:l,height:h,data:o},{width:l,height:h,data:u},c>>f),v({width:l,height:h,data:u},{width:l,height:h,data:o},c>>f),p({width:l,height:h,data:o},{width:l,height:h,data:u},c>>f),v({width:l,height:h,data:u},{width:l,height:h,data:o},c>>f);var a=b(o);if(!Array.isArray(a)){var g=Object(r.max)(o);a=Object(r.tickStep)(0,g,a),(a=Object(r.range)(0,Math.floor(g/a)*a,a)).shift()}return d().thresholds(a).size([l,h])(o).map(x)}function x(t){return t.value*=Math.pow(2,-2*f),t.coordinates.forEach(w),t}function w(t){t.forEach(A)}function A(t){t.forEach(E)}function E(t){t[0]=t[0]*Math.pow(2,f)-s,t[1]=t[1]*Math.pow(2,f)-s}function M(){return l=o+2*(s=3*c)>>f,h=u+2*s>>f,_}return _.x=function(n){return arguments.length?(t="function"==typeof n?n:a(+n),_):t},_.y=function(t){return arguments.length?(n="function"==typeof t?t:a(+t),_):n},_.weight=function(t){return arguments.length?(e="function"==typeof t?t:a(+t),_):e},_.size=function(t){if(!arguments.length)return[o,u];var n=Math.ceil(t[0]),e=Math.ceil(t[1]);if(!(n>=0||n>=0))throw new Error("invalid size");return o=n,u=e,M()},_.cellSize=function(t){if(!arguments.length)return 1<<f;if(!((t=+t)>=1))throw new Error("invalid cell size");return f=Math.floor(Math.log(t)/Math.LN2),M()},_.thresholds=function(t){return arguments.length?(b="function"==typeof t?t:Array.isArray(t)?a(i.call(t)):a(t),_):b},_.bandwidth=function(t){if(!arguments.length)return Math.sqrt(c*(c+1));if(!((t=+t)>=0))throw new Error("invalid bandwidth");return c=Math.round((Math.sqrt(4*t*t+1)-1)/2),M()},_};e.d(n,"contours",function(){return d}),e.d(n,"contourDensity",function(){return b})},1329:function(t,n,e){"use strict";function r(t){if(!t.ok)throw new Error(t.status+" "+t.statusText);return t.blob()}e.r(n);var i=function(t,n){return fetch(t,n).then(r)};function o(t){if(!t.ok)throw new Error(t.status+" "+t.statusText);return t.arrayBuffer()}var u=function(t,n){return fetch(t,n).then(o)},a=e(319);function c(t){if(!t.ok)throw new Error(t.status+" "+t.statusText);return t.text()}var f=function(t,n){return fetch(t,n).then(c)};function s(t){return function(n,e,r){return 2===arguments.length&&"function"==typeof e&&(r=e,e=void 0),f(n,e).then(function(n){return t(n,r)})}}function l(t,n,e,r){3===arguments.length&&"function"==typeof e&&(r=e,e=void 0);var i=Object(a.dsvFormat)(t);return f(n,e).then(function(t){return i.parse(t,r)})}var h=s(a.csvParse),d=s(a.tsvParse),p=function(t,n){return new Promise(function(e,r){var i=new Image;for(var o in n)i[o]=n[o];i.onerror=r,i.onload=function(){e(i)},i.src=t})};function v(t){if(!t.ok)throw new Error(t.status+" "+t.statusText);return t.json()}var g=function(t,n){return fetch(t,n).then(v)};function y(t){return function(n,e){return f(n,e).then(function(n){return(new DOMParser).parseFromString(n,t)})}}var m=y("application/xml"),b=y("text/html"),_=y("image/svg+xml");e.d(n,"blob",function(){return i}),e.d(n,"buffer",function(){return u}),e.d(n,"dsv",function(){return l}),e.d(n,"csv",function(){return h}),e.d(n,"tsv",function(){return d}),e.d(n,"image",function(){return p}),e.d(n,"json",function(){return g}),e.d(n,"text",function(){return f}),e.d(n,"xml",function(){return m}),e.d(n,"html",function(){return b}),e.d(n,"svg",function(){return _})},1332:function(t,n,e){"use strict";e.r(n);var r=function(){return Math.random()},i=function t(n){function e(t,e){return t=null==t?0:+t,e=null==e?1:+e,1===arguments.length?(e=t,t=0):e-=t,function(){return n()*e+t}}return e.source=t,e}(r),o=function t(n){function e(t,e){var r,i;return t=null==t?0:+t,e=null==e?1:+e,function(){var o;if(null!=r)o=r,r=null;else do{r=2*n()-1,o=2*n()-1,i=r*r+o*o}while(!i||i>1);return t+e*o*Math.sqrt(-2*Math.log(i)/i)}}return e.source=t,e}(r),u=function t(n){function e(){var t=o.source(n).apply(this,arguments);return function(){return Math.exp(t())}}return e.source=t,e}(r),a=function t(n){function e(t){return function(){for(var e=0,r=0;r<t;++r)e+=n();return e}}return e.source=t,e}(r),c=function t(n){function e(t){var e=a.source(n)(t);return function(){return e()/t}}return e.source=t,e}(r),f=function t(n){function e(t){return function(){return-Math.log(1-n())/t}}return e.source=t,e}(r);e.d(n,"randomUniform",function(){return i}),e.d(n,"randomNormal",function(){return o}),e.d(n,"randomLogNormal",function(){return u}),e.d(n,"randomBates",function(){return c}),e.d(n,"randomIrwinHall",function(){return a}),e.d(n,"randomExponential",function(){return f})},1333:function(t,n,e){"use strict";e.r(n);var r=e(1),i=e.n(r),o=e(2),u=e.n(o),a=e(4),c=e.n(a),f=e(3),s=e.n(f),l=e(5),h=e.n(l),d=e(72),p=e.n(d),v=e(73),g=e.n(v),y=e(6),m=p.a?p.a:d,b=g.a?g.a:v,_=function(t){function n(){var t;return i()(this,n),(t=c()(this,s()(n).call(this))).name="Entropy",t.module="Charts",t.description="Shannon Entropy, in the context of information theory, is a measure of the rate at which information is produced by a source of data. It can be used, in a broad sense, to detect whether data is likely to be structured or unstructured. 8 is the maximum, representing highly unstructured, 'random' data. English language text usually falls somewhere between 3.5 and 5. Properly encrypted or compressed data should have an entropy of over 7.5.",t.infoURL="https://wikipedia.org/wiki/Entropy_(information_theory)",t.inputType="ArrayBuffer",t.outputType="json",t.presentType="html",t.args=[{name:"Visualisation",type:"option",value:["Shannon scale","Histogram (Bar)","Histogram (Line)","Curve","Image"]}],t}return h()(n,t),u()(n,[{key:"calculateShannonEntropy",value:function(t){var n,e=[],r=new Array(256).fill(0);for(n=0;n<t.length;n++)r[t[n]]++;for(n=0;n<r.length;n++)r[n]>0&&e.push(r[n]/t.length);var i,o=0;for(n=0;n<e.length;n++)o+=(i=e[n])*Math.log(i)/Math.log(2);return-o}},{key:"calculateScanningEntropy",value:function(t){for(var n=[],e=t.length<256?8:256,r=0;r<t.length;r+=e){var i=t.slice(r,r+e);n.push(this.calculateShannonEntropy(i))}return{entropyData:n,binWidth:e}}},{key:"createAxes",value:function(t,n,e,r,i,o,u,a,c){var f=m.axisLeft().scale(e),s=m.axisBottom().scale(n);t.append("g").attr("transform",`translate(0, ${r-o.bottom})`).call(s),t.append("g").attr("transform",`translate(${o.left},0)`).call(f),t.append("text").attr("transform","rotate(-90)").attr("y",0-o.left).attr("x",0-r/2).attr("dy","1em").style("text-anchor","middle").text(c),t.append("text").attr("transform",`translate(${i/2}, ${r-o.bottom+40})`).style("text-anchor","middle").text(a),t.append("text").attr("transform",`translate(${i/2}, ${o.top-10})`).style("text-anchor","middle").text(u)}},{key:"calculateByteFrequency",value:function(t){var n,e=new Array(256).fill(0);if(0===t.length)return e;for(n=0;n<t.length;n++)e[t[n]]++;for(n=0;n<e.length;n++)e[n]=e[n]/t.length;return e}},{key:"createByteFrequencyLineHistogram",value:function(t){var n={top:30,right:20,bottom:50,left:30},e=(new b.Document).createElement("svg");e=m.select(e).attr("width","100%").attr("height","100%").attr("viewBox","0 0 500 500");var r=m.scaleLinear().domain([0,m.max(t,function(t){return t})]).range([500-n.bottom,n.top]),i=m.scaleLinear().domain([0,t.length-1]).range([n.left,500-n.right]),o=m.line().x(function(t,n){return i(n)}).y(function(t){return r(t)}).curve(m.curveMonotoneX);return e.append("path").datum(t).attr("fill","none").attr("stroke","steelblue").attr("d",o),this.createAxes(e,i,r,500,500,n,"","Byte","Byte Frequency"),e._groups[0][0].outerHTML}},{key:"createByteFrequencyBarHistogram",value:function(t){var n={top:30,right:20,bottom:50,left:30},e=(new b.Document).createElement("svg");e=m.select(e).attr("width","100%").attr("height","100%").attr("viewBox","0 0 500 500");var r=m.extent(t,function(t){return t}),i=m.scaleLinear().domain(r).range([500-n.bottom,n.top]),o=m.scaleLinear().domain([0,t.length-1]).range([n.left-1,500-n.right]);return e.selectAll("rect").data(t).enter().append("rect").attr("x",function(t,n){return o(n)+1}).attr("y",function(t){return i(t)}).attr("width",1).attr("height",function(t){return i(r[0])-i(t)}).attr("fill","blue"),this.createAxes(e,o,i,500,500,n,"","Byte","Byte Frequency"),e._groups[0][0].outerHTML}},{key:"createEntropyCurve",value:function(t){var n={top:30,right:20,bottom:50,left:30},e=(new b.Document).createElement("svg");e=m.select(e).attr("width","100%").attr("height","100%").attr("viewBox","0 0 500 500");var r=m.scaleLinear().domain([0,m.max(t,function(t){return t})]).range([500-n.bottom,n.top]),i=m.scaleLinear().domain([0,t.length]).range([n.left,500-n.right]),o=m.line().x(function(t,n){return i(n)}).y(function(t){return r(t)}).curve(m.curveMonotoneX);return t.length>0&&(e.append("path").datum(t).attr("d",o),e.selectAll("path").attr("fill","none").attr("stroke","steelblue")),this.createAxes(e,i,r,500,500,n,"Scanning Entropy","Block","Entropy"),e._groups[0][0].outerHTML}},{key:"createEntropyImage",value:function(t){for(var n=[],e=0;e<t.length;e++)n.push({x:e%100,y:Math.floor(e/100),entropy:t[e]});var r=(new b.Document).createElement("svg");r=m.select(r).attr("width","100%").attr("height","100%").attr("viewBox","0 0 100 100");var i=m.scaleLinear().domain([0,m.max(t,function(t){return t})]).range(["#000000","#FFFFFF"]).interpolate(m.interpolateRgb);return r.selectAll("rect").data(n).enter().append("rect").attr("x",function(t){return 1*t.x}).attr("y",function(t){return 1*t.y}).attr("width",1).attr("height",1).style("fill",function(t){return i(t.entropy)}),r._groups[0][0].outerHTML}},{key:"createShannonEntropyVisualization",value:function(t){return`Shannon entropy: ${t}\n        <br><canvas id='chart-area'></canvas><br>\n        - 0 represents no randomness (i.e. all the bytes in the data have the same value) whereas 8, the maximum, represents a completely random string.\n        - Standard English text usually falls somewhere between 3.5 and 5.\n        - Properly encrypted or compressed data of a reasonable length should have an entropy of over 7.5.\n\n        The following results show the entropy of chunks of the input data. Chunks with particularly high entropy could suggest encrypted or compressed sections.\n\n        <br><script>\n            var canvas = document.getElementById("chart-area"),\n                parentRect = canvas.parentNode.getBoundingClientRect(),\n                entropy = ${t},\n                height = parentRect.height * 0.25;\n\n            canvas.width = parentRect.width * 0.95;\n            canvas.height = height > 150 ? 150 : height;\n\n            CanvasComponents.drawScaleBar(canvas, entropy, 8, [\n                {\n                    label: "English text",\n                    min: 3.5,\n                    max: 5\n                },{\n                    label: "Encrypted/compressed",\n                    min: 7.5,\n                    max: 8\n                }\n            ]);\n        <\/script>`}},{key:"run",value:function(t,n){var e=n[0];switch(t=new Uint8Array(t),e){case"Histogram (Bar)":case"Histogram (Line)":return this.calculateByteFrequency(t);case"Curve":case"Image":return this.calculateScanningEntropy(t).entropyData;case"Shannon scale":default:return this.calculateShannonEntropy(t)}}},{key:"present",value:function(t,n){switch(n[0]){case"Histogram (Bar)":return this.createByteFrequencyBarHistogram(t);case"Histogram (Line)":return this.createByteFrequencyLineHistogram(t);case"Curve":return this.createEntropyCurve(t);case"Image":return this.createEntropyImage(t);case"Shannon scale":default:return this.createShannonEntropyVisualization(t)}}}]),n}(y.a),x=e(9),w=["Line feed","CRLF"],A=["Space","Comma","Semi-colon","Colon","Tab"],E={min:"white",max:"black"};function M(t,n,e,r,i){var o,u=[];return t.split(n).forEach(function(t,n){var a=t.split(e);if(a.length!==i)throw new x.a(`Each row must have length ${i}.`);r&&0===n?o=a:u.push(a)}),{headings:o,values:u}}function k(t,n,e,r){var i=M(t,n,e,r,2),o=i.headings,u=i.values;return o&&(o={x:o[0],y:o[1]}),{headings:o,values:u=u.map(function(t){var n=parseFloat(t[0],10),e=parseFloat(t[1],10);if(Number.isNaN(n))throw new x.a("Values must be numbers in base 10.");if(Number.isNaN(e))throw new x.a("Values must be numbers in base 10.");return[n,e]})}}function B(t,n,e,r){var i=M(t,n,e,r,3),o=i.headings,u=i.values;return o&&(o={x:o[0],y:o[1]}),{headings:o,values:u=u.map(function(t){var n=parseFloat(t[0],10),e=parseFloat(t[1],10),r=t[2];if(Number.isNaN(n))throw new x.a("Values must be numbers in base 10.");if(Number.isNaN(e))throw new x.a("Values must be numbers in base 10.");return[n,e,r]})}}var F=e(0),T=p.a?p.a:d,N=g.a?g.a:v,C=function(t){function n(){var t;return i()(this,n),(t=c()(this,s()(n).call(this))).name="Heatmap chart",t.module="Charts",t.description="A heatmap is a graphical representation of data where the individual values contained in a matrix are represented as colors.",t.infoURL="https://wikipedia.org/wiki/Heat_map",t.inputType="string",t.outputType="html",t.args=[{name:"Record delimiter",type:"option",value:w},{name:"Field delimiter",type:"option",value:A},{name:"Number of vertical bins",type:"number",value:25},{name:"Number of horizontal bins",type:"number",value:25},{name:"Use column headers as labels",type:"boolean",value:!0},{name:"X label",type:"string",value:""},{name:"Y label",type:"string",value:""},{name:"Draw bin edges",type:"boolean",value:!1},{name:"Min colour value",type:"string",value:E.min},{name:"Max colour value",type:"string",value:E.max}],t}return h()(n,t),u()(n,[{key:"run",value:function(t,n){var e=F.b.charRep(n[0]),r=F.b.charRep(n[1]),i=n[2],o=n[3],u=n[4],a=n[7],c=n[8],f=n[9];if(i<=0)throw new x.a("Number of vertical bins must be greater than 0");if(o<=0)throw new x.a("Number of horizontal bins must be greater than 0");var s=n[5],l=n[6],h=k(t,e,r,u),d=h.headings,p=h.values;d&&(s=d.x,l=d.y);var v=(new N.Document).createElement("svg");v=T.select(v).attr("width","100%").attr("height","100%").attr("viewBox","0 0 500 500");var g=10,y=30,m=500-y-0,b=500-g-40,_=m/o,w=b/i,A=v.append("g").attr("transform","translate("+y+","+g+")"),E=this.getHeatmapPacking(p,i,o),M=Math.max(...E.map(function(t){var n=t.map(function(t){return t.length});return Math.max(...n)})),B=T.extent(p,function(t){return t[0]}),C=T.extent(p,function(t){return t[1]}),S=T.scaleLinear().domain(B).range([0,m]),O=T.scaleLinear().domain(C).range([b,0]),R=T.scaleSequential(T.interpolateLab(c,f)).domain([0,M]);return A.append("clipPath").attr("id","clip").append("rect").attr("width",m).attr("height",b),A.append("g").attr("class","bins").attr("clip-path","url(#clip)").selectAll("g").data(E).enter().append("g").selectAll("rect").data(function(t){return t}).enter().append("rect").attr("x",function(t){return _*t.x}).attr("y",function(t){return b-w*(t.y+1)}).attr("width",_).attr("height",w).attr("fill",function(t){return R(t.length)}).attr("stroke",a?"rgba(0, 0, 0, 0.5)":"none").attr("stroke-width",a?"0.5":"none").append("title").text(function(t){return`Count: ${t.length}\n\n                               Percentage: ${(100*t.length/p.length).toFixed(2)}%\n\n                    `.replace(/\s{2,}/g,"\n")}),A.append("g").attr("class","axis axis--y").call(T.axisLeft(O).tickSizeOuter(-m)),v.append("text").attr("transform","rotate(-90)").attr("y",-y).attr("x",-b/2).attr("dy","1em").style("text-anchor","middle").text(l),A.append("g").attr("class","axis axis--x").attr("transform","translate(0,"+b+")").call(T.axisBottom(S).tickSizeOuter(-b)),v.append("text").attr("x",m/2).attr("y",500).style("text-anchor","middle").text(s),v._groups[0][0].outerHTML}},{key:"getHeatmapPacking",value:function(t,n,e){var r=T.extent(t,function(t){return t[0]}),i=T.extent(t,function(t){return t[1]}),o=[];if(r[0]===r[1])throw"Cannot pack points. There is no difference between the minimum and maximum X coordinate.";if(i[0]===i[1])throw"Cannot pack points. There is no difference between the minimum and maximum Y coordinate.";for(var u=0;u<n;u++){o.push([]);for(var a=0;a<e;a++){var c=[];c.y=u,c.x=a,o[u].push(c)}}return t.forEach(function(t){var u=(t[1]-i[0])/(i[1]+1e-9-i[0]),a=(t[0]-r[0])/(r[1]+1e-9-r[0]),c=Math.floor(n*u),f=Math.floor(e*a);o[c][f].push({x:t[0],y:t[1]})}),o}}]),n}(y.a),S=e(353),O=e.n(S),R=p.a?p.a:d,P=O.a?O.a:S,j=g.a?g.a:v,D=function(t){function n(){var t;return i()(this,n),(t=c()(this,s()(n).call(this))).name="Hex Density chart",t.module="Charts",t.description="Hex density charts are used in a similar way to scatter charts, however rather than rendering tens of thousands of points, it groups the points into a few hundred hexagons to show the distribution.",t.inputType="string",t.outputType="html",t.args=[{name:"Record delimiter",type:"option",value:w},{name:"Field delimiter",type:"option",value:A},{name:"Pack radius",type:"number",value:25},{name:"Draw radius",type:"number",value:15},{name:"Use column headers as labels",type:"boolean",value:!0},{name:"X label",type:"string",value:""},{name:"Y label",type:"string",value:""},{name:"Draw hexagon edges",type:"boolean",value:!1},{name:"Min colour value",type:"string",value:E.min},{name:"Max colour value",type:"string",value:E.max},{name:"Draw empty hexagons within data boundaries",type:"boolean",value:!1}],t}return h()(n,t),u()(n,[{key:"run",value:function(t,n){var e=F.b.charRep(n[0]),r=F.b.charRep(n[1]),i=n[2],o=n[3],u=n[4],a=n[7],c=n[8],f=n[9],s=n[10],l=n[5],h=n[6],d=k(t,e,r,u),p=d.headings,v=d.values;p&&(l=p.x,h=p.y);var g=(new j.Document).createElement("svg"),y=10,m=30,b=500-m-0,_=500-y-40,x=(g=R.select(g).attr("width","100%").attr("height","100%").attr("viewBox","0 0 500 500")).append("g").attr("transform","translate("+m+","+y+")"),w=P.hexbin().radius(i).extent([0,0],[b,_]),A=w(v),E=Math.max(...A.map(function(t){return t.length})),M=R.extent(A,function(t){return t.x}),B=R.extent(A,function(t){return t.y});M[0]-=2*i,M[1]+=3*i,B[0]-=2*i,B[1]+=2*i;var T=R.scaleLinear().domain(M).range([0,b]),N=R.scaleLinear().domain(B).range([_,0]),C=R.scaleSequential(R.interpolateLab(c,f)).domain([0,E]);return x.append("clipPath").attr("id","clip").append("rect").attr("width",b).attr("height",_),s&&x.append("g").attr("class","empty-hexagon").selectAll("path").data(this.getEmptyHexagons(A,i)).enter().append("path").attr("d",function(t){return`M${T(t.x)},${N(t.y)} ${w.hexagon(o)}`}).attr("fill",function(t){return C(0)}).attr("stroke",a?"black":"none").attr("stroke-width",a?"0.5":"none").append("title").text(function(t){return`Count: 0\n\n                                Percentage: ${(0).toFixed(2)}%\n\n                                Center: ${t.x.toFixed(2)}, ${t.y.toFixed(2)}\n\n                        `.replace(/\s{2,}/g,"\n")}),x.append("g").attr("class","hexagon").attr("clip-path","url(#clip)").selectAll("path").data(A).enter().append("path").attr("d",function(t){return`M${T(t.x)},${N(t.y)} ${w.hexagon(o)}`}).attr("fill",function(t){return C(t.length)}).attr("stroke",a?"black":"none").attr("stroke-width",a?"0.5":"none").append("title").text(function(t){var n=t.length,e=100*t.length/v.length,r=t.x,i=t.y,o=Math.min(...t.map(function(t){return t[0]})),u=Math.max(...t.map(function(t){return t[0]})),a=Math.min(...t.map(function(t){return t[1]})),c=Math.max(...t.map(function(t){return t[1]}));return`Count: ${n}\n\n                               Percentage: ${e.toFixed(2)}%\n\n                               Center: ${r.toFixed(2)}, ${i.toFixed(2)}\n\n                               Min X: ${o.toFixed(2)}\n\n                               Max X: ${u.toFixed(2)}\n\n                               Min Y: ${a.toFixed(2)}\n\n                               Max Y: ${c.toFixed(2)}\n                    `.replace(/\s{2,}/g,"\n")}),x.append("g").attr("class","axis axis--y").call(R.axisLeft(N).tickSizeOuter(-b)),g.append("text").attr("transform","rotate(-90)").attr("y",-m).attr("x",-_/2).attr("dy","1em").style("text-anchor","middle").text(h),x.append("g").attr("class","axis axis--x").attr("transform","translate(0,"+_+")").call(R.axisBottom(T).tickSizeOuter(-_)),g.append("text").attr("x",b/2).attr("y",500).style("text-anchor","middle").text(l),g._groups[0][0].outerHTML}},{key:"getEmptyHexagons",value:function(t,n){for(var e=[],r=[R.extent(t,function(t){return t.x}),R.extent(t,function(t){return t.y})],i=Math.cos(2*Math.PI/12)*n,o=Math.sin(2*Math.PI/12)*n,u=!1,a=r[1][0];a<=r[1][1]+n;a+=o+n){for(var c=r[0][0];c<=r[0][1]+n;c+=2*i){var f=c,s=a;if(u&&c>=r[0][1])break;u&&(f+=i),e.push({x:f,y:s})}u=!u}return e}}]),n}(y.a),L=p.a?p.a:d,I=g.a?g.a:v,U=function(t){function n(){var t;return i()(this,n),(t=c()(this,s()(n).call(this))).name="Scatter chart",t.module="Charts",t.description="Plots two-variable data as single points on a graph.",t.infoURL="https://wikipedia.org/wiki/Scatter_plot",t.inputType="string",t.outputType="html",t.args=[{name:"Record delimiter",type:"option",value:w},{name:"Field delimiter",type:"option",value:A},{name:"Use column headers as labels",type:"boolean",value:!0},{name:"X label",type:"string",value:""},{name:"Y label",type:"string",value:""},{name:"Colour",type:"string",value:E.max},{name:"Point radius",type:"number",value:10},{name:"Use colour from third column",type:"boolean",value:!1}],t}return h()(n,t),u()(n,[{key:"run",value:function(t,n){var e=F.b.charRep(n[0]),r=F.b.charRep(n[1]),i=n[2],o=n[5],u=n[6],a=n[7],c=n[3],f=n[4],s=(a?B:k)(t,e,r,i),l=s.headings,h=s.values;l&&(c=l.x,f=l.y);var d=(new I.Document).createElement("svg"),p=10,v=30,g=500-v-0,y=500-p-40,m=(d=L.select(d).attr("width","100%").attr("height","100%").attr("viewBox","0 0 500 500")).append("g").attr("transform","translate("+v+","+p+")"),b=L.extent(h,function(t){return t[0]}),_=b[1]-b[0],x=L.extent(h,function(t){return t[1]}),w=x[1]-x[0],A=L.scaleLinear().domain([b[0]-.1*_,b[1]+.1*_]).range([0,g]),E=L.scaleLinear().domain([x[0]-.1*w,x[1]+.1*w]).range([y,0]);return m.append("clipPath").attr("id","clip").append("rect").attr("width",g).attr("height",y),m.append("g").attr("class","points").attr("clip-path","url(#clip)").selectAll("circle").data(h).enter().append("circle").attr("cx",function(t){return A(t[0])}).attr("cy",function(t){return E(t[1])}).attr("r",function(t){return u}).attr("fill",function(t){return a?t[2]:o}).attr("stroke","rgba(0, 0, 0, 0.5)").attr("stroke-width","0.5").append("title").text(function(t){return`X: ${t[0]}\n\n                               Y: ${t[1]}\n\n                    `.replace(/\s{2,}/g,"\n")}),m.append("g").attr("class","axis axis--y").call(L.axisLeft(E).tickSizeOuter(-g)),d.append("text").attr("transform","rotate(-90)").attr("y",-v).attr("x",-y/2).attr("dy","1em").style("text-anchor","middle").text(f),m.append("g").attr("class","axis axis--x").attr("transform","translate(0,"+y+")").call(L.axisBottom(A).tickSizeOuter(-y)),d.append("text").attr("x",g/2).attr("y",500).style("text-anchor","middle").text(c),d._groups[0][0].outerHTML}}]),n}(y.a),z=p.a?p.a:d,Y=g.a?g.a:v,q=function(t){function n(){var t;return i()(this,n),(t=c()(this,s()(n).call(this))).name="Series chart",t.module="Charts",t.description="A time series graph is a line graph of repeated measurements taken over regular time intervals.",t.inputType="string",t.outputType="html",t.args=[{name:"Record delimiter",type:"option",value:w},{name:"Field delimiter",type:"option",value:A},{name:"X label",type:"string",value:""},{name:"Point radius",type:"number",value:1},{name:"Series colours",type:"string",value:"mediumseagreen, dodgerblue, tomato"}],t}return h()(n,t),u()(n,[{key:"run",value:function(t,n){var e=F.b.charRep(n[0]),r=F.b.charRep(n[1]),i=n[2],o=n[3],u=n[4].split(","),a=function(t,n,e,r){var i=M(t,n,e,!1,3).values,o=new Set,u={};i.forEach(function(t){var n=t[0],e=t[1],r=parseFloat(t[2],10);if(Number.isNaN(r))throw new x.a("Values must be numbers in base 10.");o.add(e),void 0===u[n]&&(u[n]={}),u[n][e]=r}),o=new Array(...o);var a=[];for(var c in u){var f=u[c];a.push({name:c,data:f})}return{xValues:o,series:a}}(t,e,r),c=a.xValues,f=a.series,s=120*Object.keys(f).length,l=s+50+20,h=(new Y.Document).createElement("svg");h=z.select(h).attr("width","100%").attr("height","100%").attr("viewBox",`0 0 500 ${l}`);var d=z.scalePoint().domain(c).range([0,430]);h.append("g").attr("class","axis axis--x").attr("transform","translate(50, 50)").call(z.axisTop(d).tickValues(c.filter(function(t,n){return[0,Math.round(c.length/2),c.length-1].indexOf(n)>=0}))),h.append("text").attr("x",250).attr("y",25).style("text-anchor","middle").text(i);var p={},v=430/c.length;c.forEach(function(t){var n=[];f.forEach(function(e){var r=e.data[t];void 0!==r&&n.push(`${e.name}: ${r}`)}),p[t]=n.join("\n")});var g=h.append("g").attr("transform","translate(50, 50)");g.append("g").selectAll("rect").data(c).enter().append("rect").attr("x",function(t){return d(t)-v/2}).attr("y",0).attr("width",v).attr("height",s).attr("stroke","none").attr("fill","transparent").append("title").text(function(t){return`${t}\n\n                    --\n\n                    ${p[t]}\n\n                `.replace(/\s{2,}/g,"\n")});var y=h.append("g").attr("transform","translate(0, 50)");return f.forEach(function(t,n){var e=z.extent(Object.values(t.data)),r=z.scaleLinear().domain(e).range([100,0]),i=g.append("g").attr("transform",`translate(0, ${100*n+20*(n+1)})`),a="";c.forEach(function(n,e){var i=c[e+1],o=t.data[n],u=t.data[i];void 0!==o&&void 0!==u&&(n=d(n),i=d(i),o=r(o),u=r(u),a+=`M ${n} ${o} L ${i} ${u} z `)}),i.append("path").attr("d",a).attr("fill","none").attr("stroke",u[n%u.length]).attr("stroke-width","1"),c.forEach(function(e){var a=t.data[e];void 0!==a&&i.append("circle").attr("cx",d(e)).attr("cy",r(a)).attr("r",o).attr("fill",u[n%u.length]).append("title").text(function(t){return`${e}\n\n                            --\n\n                            ${p[e]}\n\n                        `.replace(/\s{2,}/g,"\n")})}),y.append("g").attr("transform",`translate(30, ${100*n+20*(n+1)})`).attr("class","axis axis--y").call(z.axisLeft(r).ticks(5)),y.append("g").attr("transform",`translate(0, ${50+100*n+20*(n+1)})`).append("text").style("text-anchor","middle").attr("transform","rotate(-90)").text(t.name)}),h._groups[0][0].outerHTML}}]),n}(y.a),H="undefined"==typeof self?{}:self.OpModules||{};
/**
 * <AUTHOR> [<EMAIL>]
 * <AUTHOR> C [<EMAIL>]
 * @copyright Crown Copyright 2019
 * @license Apache-2.0
 */H.Charts={Entropy:_,"Heatmap chart":C,"Hex Density chart":D,"Scatter chart":U,"Series chart":q};n.default=H},1334:function(t,n,e){"use strict";e.r(n);var r=function(t){for(var n,e=-1,r=t.length,i=t[r-1],o=0;++e<r;)n=i,i=t[e],o+=n[1]*i[0]-n[0]*i[1];return o/2},i=function(t){for(var n,e,r=-1,i=t.length,o=0,u=0,a=t[i-1],c=0;++r<i;)n=a,a=t[r],c+=e=n[0]*a[1]-a[0]*n[1],o+=(n[0]+a[0])*e,u+=(n[1]+a[1])*e;return[o/(c*=3),u/c]},o=function(t,n,e){return(n[0]-t[0])*(e[1]-t[1])-(n[1]-t[1])*(e[0]-t[0])};function u(t,n){return t[0]-n[0]||t[1]-n[1]}function a(t){for(var n=t.length,e=[0,1],r=2,i=2;i<n;++i){for(;r>1&&o(t[e[r-2]],t[e[r-1]],t[i])<=0;)--r;e[r++]=i}return e.slice(0,r)}var c=function(t){if((e=t.length)<3)return null;var n,e,r=new Array(e),i=new Array(e);for(n=0;n<e;++n)r[n]=[+t[n][0],+t[n][1],n];for(r.sort(u),n=0;n<e;++n)i[n]=[r[n][0],-r[n][1]];var o=a(r),c=a(i),f=c[0]===o[0],s=c[c.length-1]===o[o.length-1],l=[];for(n=o.length-1;n>=0;--n)l.push(t[r[o[n]][2]]);for(n=+f;n<c.length-s;++n)l.push(t[r[c[n]][2]]);return l},f=function(t,n){for(var e,r,i=t.length,o=t[i-1],u=n[0],a=n[1],c=o[0],f=o[1],s=!1,l=0;l<i;++l)e=(o=t[l])[0],(r=o[1])>a!=f>a&&u<(c-e)*(a-r)/(f-r)+e&&(s=!s),c=e,f=r;return s},s=function(t){for(var n,e,r=-1,i=t.length,o=t[i-1],u=o[0],a=o[1],c=0;++r<i;)n=u,e=a,n-=u=(o=t[r])[0],e-=a=o[1],c+=Math.sqrt(n*n+e*e);return c};e.d(n,"polygonArea",function(){return r}),e.d(n,"polygonCentroid",function(){return i}),e.d(n,"polygonHull",function(){return c}),e.d(n,"polygonContains",function(){return f}),e.d(n,"polygonLength",function(){return s})},1335:function(t,n,e){"use strict";e.r(n);var r=e(35),i=Math.cos,o=Math.sin,u=Math.PI,a=u/2,c=2*u,f=Math.max;function s(t){return function(n,e){return t(n.source.value+n.target.value,e.source.value+e.target.value)}}var l=function(){var t=0,n=null,e=null,i=null;function o(o){var u,a,s,l,h,d,p=o.length,v=[],g=Object(r.range)(p),y=[],m=[],b=m.groups=new Array(p),_=new Array(p*p);for(u=0,h=-1;++h<p;){for(a=0,d=-1;++d<p;)a+=o[h][d];v.push(a),y.push(Object(r.range)(p)),u+=a}for(n&&g.sort(function(t,e){return n(v[t],v[e])}),e&&y.forEach(function(t,n){t.sort(function(t,r){return e(o[n][t],o[n][r])})}),l=(u=f(0,c-t*p)/u)?t:c/p,a=0,h=-1;++h<p;){for(s=a,d=-1;++d<p;){var x=g[h],w=y[x][d],A=o[x][w],E=a,M=a+=A*u;_[w*p+x]={index:x,subindex:w,startAngle:E,endAngle:M,value:A}}b[x]={index:x,startAngle:s,endAngle:a,value:v[x]},a+=l}for(h=-1;++h<p;)for(d=h-1;++d<p;){var k=_[d*p+h],B=_[h*p+d];(k.value||B.value)&&m.push(k.value<B.value?{source:B,target:k}:{source:k,target:B})}return i?m.sort(i):m}return o.padAngle=function(n){return arguments.length?(t=f(0,n),o):t},o.sortGroups=function(t){return arguments.length?(n=t,o):n},o.sortSubgroups=function(t){return arguments.length?(e=t,o):e},o.sortChords=function(t){return arguments.length?(null==t?i=null:(i=s(t))._=t,o):i&&i._},o},h=Array.prototype.slice,d=function(t){return function(){return t}},p=e(102);function v(t){return t.source}function g(t){return t.target}function y(t){return t.radius}function m(t){return t.startAngle}function b(t){return t.endAngle}var _=function(){var t=v,n=g,e=y,r=m,u=b,c=null;function f(){var f,s=h.call(arguments),l=t.apply(this,s),d=n.apply(this,s),v=+e.apply(this,(s[0]=l,s)),g=r.apply(this,s)-a,y=u.apply(this,s)-a,m=v*i(g),b=v*o(g),_=+e.apply(this,(s[0]=d,s)),x=r.apply(this,s)-a,w=u.apply(this,s)-a;if(c||(c=f=Object(p.path)()),c.moveTo(m,b),c.arc(0,0,v,g,y),g===x&&y===w||(c.quadraticCurveTo(0,0,_*i(x),_*o(x)),c.arc(0,0,_,x,w)),c.quadraticCurveTo(0,0,m,b),c.closePath(),f)return c=null,f+""||null}return f.radius=function(t){return arguments.length?(e="function"==typeof t?t:d(+t),f):e},f.startAngle=function(t){return arguments.length?(r="function"==typeof t?t:d(+t),f):r},f.endAngle=function(t){return arguments.length?(u="function"==typeof t?t:d(+t),f):u},f.source=function(n){return arguments.length?(t=n,f):t},f.target=function(t){return arguments.length?(n=t,f):n},f.context=function(t){return arguments.length?(c=null==t?null:t,f):c},f};e.d(n,"chord",function(){return l}),e.d(n,"ribbon",function(){return _})},1336:function(t,n,e){"use strict";e.r(n);var r=e(126),i=e(201),o=e(63),u=e(23),a=e(180),c=function(t){return function(){return t}};function f(t,n,e){this.target=t,this.type=n,this.transform=e}function s(t,n,e){this.k=t,this.x=n,this.y=e}s.prototype={constructor:s,scale:function(t){return 1===t?this:new s(this.k*t,this.x,this.y)},translate:function(t,n){return 0===t&0===n?this:new s(this.k,this.x+this.k*t,this.y+this.k*n)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var l=new s(1,0,0);function h(t){for(;!t.__zoom;)if(!(t=t.parentNode))return l;return t.__zoom}function d(){u.event.stopImmediatePropagation()}h.prototype=s.prototype;var p=function(){u.event.preventDefault(),u.event.stopImmediatePropagation()};function v(){return!u.event.ctrlKey&&!u.event.button}function g(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t).hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]:[[0,0],[t.clientWidth,t.clientHeight]]}function y(){return this.__zoom||l}function m(){return-u.event.deltaY*(1===u.event.deltaMode?.05:u.event.deltaMode?1:.002)}function b(){return navigator.maxTouchPoints||"ontouchstart"in this}function _(t,n,e){var r=t.invertX(n[0][0])-e[0][0],i=t.invertX(n[1][0])-e[1][0],o=t.invertY(n[0][1])-e[0][1],u=t.invertY(n[1][1])-e[1][1];return t.translate(i>r?(r+i)/2:Math.min(0,r)||Math.max(0,i),u>o?(o+u)/2:Math.min(0,o)||Math.max(0,u))}var x=function(){var t,n,e=v,h=g,x=_,w=m,A=b,E=[0,1/0],M=[[-1/0,-1/0],[1/0,1/0]],k=250,B=o.interpolateZoom,F=Object(r.dispatch)("start","zoom","end"),T=500,N=150,C=0;function S(t){t.property("__zoom",y).on("wheel.zoom",I).on("mousedown.zoom",U).on("dblclick.zoom",z).filter(A).on("touchstart.zoom",Y).on("touchmove.zoom",q).on("touchend.zoom touchcancel.zoom",H).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function O(t,n){return(n=Math.max(E[0],Math.min(E[1],n)))===t.k?t:new s(n,t.x,t.y)}function R(t,n,e){var r=n[0]-e[0]*t.k,i=n[1]-e[1]*t.k;return r===t.x&&i===t.y?t:new s(t.k,r,i)}function P(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function j(t,n,e){t.on("start.zoom",function(){D(this,arguments).start()}).on("interrupt.zoom end.zoom",function(){D(this,arguments).end()}).tween("zoom",function(){var t=this,r=arguments,i=D(t,r),o=h.apply(t,r),u=null==e?P(o):"function"==typeof e?e.apply(t,r):e,a=Math.max(o[1][0]-o[0][0],o[1][1]-o[0][1]),c=t.__zoom,f="function"==typeof n?n.apply(t,r):n,l=B(c.invert(u).concat(a/c.k),f.invert(u).concat(a/f.k));return function(t){if(1===t)t=f;else{var n=l(t),e=a/n[2];t=new s(e,u[0]-n[0]*e,u[1]-n[1]*e)}i.zoom(null,t)}})}function D(t,n,e){return!e&&t.__zooming||new L(t,n)}function L(t,n){this.that=t,this.args=n,this.active=0,this.extent=h.apply(t,n),this.taps=0}function I(){if(e.apply(this,arguments)){var t=D(this,arguments),n=this.__zoom,r=Math.max(E[0],Math.min(E[1],n.k*Math.pow(2,w.apply(this,arguments)))),i=Object(u.mouse)(this);if(t.wheel)t.mouse[0][0]===i[0]&&t.mouse[0][1]===i[1]||(t.mouse[1]=n.invert(t.mouse[0]=i)),clearTimeout(t.wheel);else{if(n.k===r)return;t.mouse=[i,n.invert(i)],Object(a.interrupt)(this),t.start()}p(),t.wheel=setTimeout(function(){t.wheel=null,t.end()},N),t.zoom("mouse",x(R(O(n,r),t.mouse[0],t.mouse[1]),t.extent,M))}}function U(){if(!n&&e.apply(this,arguments)){var t=D(this,arguments,!0),r=Object(u.select)(u.event.view).on("mousemove.zoom",function(){if(p(),!t.moved){var n=u.event.clientX-c,e=u.event.clientY-f;t.moved=n*n+e*e>C}t.zoom("mouse",x(R(t.that.__zoom,t.mouse[0]=Object(u.mouse)(t.that),t.mouse[1]),t.extent,M))},!0).on("mouseup.zoom",function(){r.on("mousemove.zoom mouseup.zoom",null),Object(i.dragEnable)(u.event.view,t.moved),p(),t.end()},!0),o=Object(u.mouse)(this),c=u.event.clientX,f=u.event.clientY;Object(i.dragDisable)(u.event.view),d(),t.mouse=[o,this.__zoom.invert(o)],Object(a.interrupt)(this),t.start()}}function z(){if(e.apply(this,arguments)){var t=this.__zoom,n=Object(u.mouse)(this),r=t.invert(n),i=t.k*(u.event.shiftKey?.5:2),o=x(R(O(t,i),n,r),h.apply(this,arguments),M);p(),k>0?Object(u.select)(this).transition().duration(k).call(j,o,n):Object(u.select)(this).call(S.transform,o)}}function Y(){if(e.apply(this,arguments)){var n,r,i,o,c=u.event.touches,f=c.length,s=D(this,arguments,u.event.changedTouches.length===f);for(d(),r=0;r<f;++r)i=c[r],o=[o=Object(u.touch)(this,c,i.identifier),this.__zoom.invert(o),i.identifier],s.touch0?s.touch1||s.touch0[2]===o[2]||(s.touch1=o,s.taps=0):(s.touch0=o,n=!0,s.taps=1+!!t);t&&(t=clearTimeout(t)),n&&(s.taps<2&&(t=setTimeout(function(){t=null},T)),Object(a.interrupt)(this),s.start())}}function q(){if(this.__zooming){var n,e,r,i,o=D(this,arguments),a=u.event.changedTouches,c=a.length;for(p(),t&&(t=clearTimeout(t)),o.taps=0,n=0;n<c;++n)e=a[n],r=Object(u.touch)(this,a,e.identifier),o.touch0&&o.touch0[2]===e.identifier?o.touch0[0]=r:o.touch1&&o.touch1[2]===e.identifier&&(o.touch1[0]=r);if(e=o.that.__zoom,o.touch1){var f=o.touch0[0],s=o.touch0[1],l=o.touch1[0],h=o.touch1[1],d=(d=l[0]-f[0])*d+(d=l[1]-f[1])*d,v=(v=h[0]-s[0])*v+(v=h[1]-s[1])*v;e=O(e,Math.sqrt(d/v)),r=[(f[0]+l[0])/2,(f[1]+l[1])/2],i=[(s[0]+h[0])/2,(s[1]+h[1])/2]}else{if(!o.touch0)return;r=o.touch0[0],i=o.touch0[1]}o.zoom("touch",x(R(e,r,i),o.extent,M))}}function H(){if(this.__zooming){var t,e,r=D(this,arguments),i=u.event.changedTouches,o=i.length;for(d(),n&&clearTimeout(n),n=setTimeout(function(){n=null},T),t=0;t<o;++t)e=i[t],r.touch0&&r.touch0[2]===e.identifier?delete r.touch0:r.touch1&&r.touch1[2]===e.identifier&&delete r.touch1;if(r.touch1&&!r.touch0&&(r.touch0=r.touch1,delete r.touch1),r.touch0)r.touch0[1]=this.__zoom.invert(r.touch0[0]);else if(r.end(),2===r.taps){var a=Object(u.select)(this).on("dblclick.zoom");a&&a.apply(this,arguments)}}}return S.transform=function(t,n,e){var r=t.selection?t.selection():t;r.property("__zoom",y),t!==r?j(t,n,e):r.interrupt().each(function(){D(this,arguments).start().zoom(null,"function"==typeof n?n.apply(this,arguments):n).end()})},S.scaleBy=function(t,n,e){S.scaleTo(t,function(){var t=this.__zoom.k,e="function"==typeof n?n.apply(this,arguments):n;return t*e},e)},S.scaleTo=function(t,n,e){S.transform(t,function(){var t=h.apply(this,arguments),r=this.__zoom,i=null==e?P(t):"function"==typeof e?e.apply(this,arguments):e,o=r.invert(i),u="function"==typeof n?n.apply(this,arguments):n;return x(R(O(r,u),i,o),t,M)},e)},S.translateBy=function(t,n,e){S.transform(t,function(){return x(this.__zoom.translate("function"==typeof n?n.apply(this,arguments):n,"function"==typeof e?e.apply(this,arguments):e),h.apply(this,arguments),M)})},S.translateTo=function(t,n,e,r){S.transform(t,function(){var t=h.apply(this,arguments),i=this.__zoom,o=null==r?P(t):"function"==typeof r?r.apply(this,arguments):r;return x(l.translate(o[0],o[1]).scale(i.k).translate("function"==typeof n?-n.apply(this,arguments):-n,"function"==typeof e?-e.apply(this,arguments):-e),t,M)},r)},L.prototype={start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(t,n){return this.mouse&&"mouse"!==t&&(this.mouse[1]=n.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=n.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=n.invert(this.touch1[0])),this.that.__zoom=n,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(t){Object(u.customEvent)(new f(S,t,this.that.__zoom),F.apply,F,[t,this.that,this.args])}},S.wheelDelta=function(t){return arguments.length?(w="function"==typeof t?t:c(+t),S):w},S.filter=function(t){return arguments.length?(e="function"==typeof t?t:c(!!t),S):e},S.touchable=function(t){return arguments.length?(A="function"==typeof t?t:c(!!t),S):A},S.extent=function(t){return arguments.length?(h="function"==typeof t?t:c([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),S):h},S.scaleExtent=function(t){return arguments.length?(E[0]=+t[0],E[1]=+t[1],S):[E[0],E[1]]},S.translateExtent=function(t){return arguments.length?(M[0][0]=+t[0][0],M[1][0]=+t[1][0],M[0][1]=+t[0][1],M[1][1]=+t[1][1],S):[[M[0][0],M[0][1]],[M[1][0],M[1][1]]]},S.constrain=function(t){return arguments.length?(x=t,S):x},S.duration=function(t){return arguments.length?(k=+t,S):k},S.interpolate=function(t){return arguments.length?(B=t,S):B},S.on=function(){var t=F.on.apply(F,arguments);return t===F?S:t},S.clickDistance=function(t){return arguments.length?(C=(t=+t)*t,S):Math.sqrt(C)},S};e.d(n,"zoom",function(){return x}),e.d(n,"zoomTransform",function(){return h}),e.d(n,"zoomIdentity",function(){return l})},1337:function(t,n,e){"use strict";e.r(n);var r=e(126),i=e(201),o=e(63),u=e(23),a=e(180),c=function(t){return function(){return t}},f=function(t,n,e){this.target=t,this.type=n,this.selection=e};function s(){u.event.stopImmediatePropagation()}var l=function(){u.event.preventDefault(),u.event.stopImmediatePropagation()},h={name:"drag"},d={name:"space"},p={name:"handle"},v={name:"center"};function g(t){return[+t[0],+t[1]]}function y(t){return[g(t[0]),g(t[1])]}var m={name:"x",handles:["w","e"].map(k),input:function(t,n){return null==t?null:[[+t[0],n[0][1]],[+t[1],n[1][1]]]},output:function(t){return t&&[t[0][0],t[1][0]]}},b={name:"y",handles:["n","s"].map(k),input:function(t,n){return null==t?null:[[n[0][0],+t[0]],[n[1][0],+t[1]]]},output:function(t){return t&&[t[0][1],t[1][1]]}},_={name:"xy",handles:["n","w","e","s","nw","ne","sw","se"].map(k),input:function(t){return null==t?null:y(t)},output:function(t){return t}},x={overlay:"crosshair",selection:"move",n:"ns-resize",e:"ew-resize",s:"ns-resize",w:"ew-resize",nw:"nwse-resize",ne:"nesw-resize",se:"nwse-resize",sw:"nesw-resize"},w={e:"w",w:"e",nw:"ne",ne:"nw",se:"sw",sw:"se"},A={n:"s",s:"n",nw:"sw",ne:"se",se:"ne",sw:"nw"},E={overlay:1,selection:1,n:null,e:1,s:null,w:-1,nw:-1,ne:1,se:1,sw:-1},M={overlay:1,selection:1,n:-1,e:null,s:1,w:null,nw:-1,ne:-1,se:1,sw:1};function k(t){return{type:t}}function B(){return!u.event.ctrlKey&&!u.event.button}function F(){var t=this.ownerSVGElement||this;return t.hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]}function T(){return navigator.maxTouchPoints||"ontouchstart"in this}function N(t){for(;!t.__brush;)if(!(t=t.parentNode))return;return t.__brush}function C(t){var n=t.__brush;return n?n.dim.output(n.selection):null}function S(){return P(m)}function O(){return P(b)}var R=function(){return P(_)};function P(t){var n,e=F,g=B,_=T,C=!0,S=Object(r.dispatch)(R,"start","brush","end"),O=6;function R(n){var e=n.property("__brush",z).selectAll(".overlay").data([k("overlay")]);e.enter().append("rect").attr("class","overlay").attr("pointer-events","all").attr("cursor",x.overlay).merge(e).each(function(){var t=N(this).extent;Object(u.select)(this).attr("x",t[0][0]).attr("y",t[0][1]).attr("width",t[1][0]-t[0][0]).attr("height",t[1][1]-t[0][1])}),n.selectAll(".selection").data([k("selection")]).enter().append("rect").attr("class","selection").attr("cursor",x.selection).attr("fill","#777").attr("fill-opacity",.3).attr("stroke","#fff").attr("shape-rendering","crispEdges");var r=n.selectAll(".handle").data(t.handles,function(t){return t.type});r.exit().remove(),r.enter().append("rect").attr("class",function(t){return"handle handle--"+t.type}).attr("cursor",function(t){return x[t.type]}),n.each(P).attr("fill","none").attr("pointer-events","all").on("mousedown.brush",L).filter(_).on("touchstart.brush",L).on("touchmove.brush",I).on("touchend.brush touchcancel.brush",U).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function P(){var t=Object(u.select)(this),n=N(this).selection;n?(t.selectAll(".selection").style("display",null).attr("x",n[0][0]).attr("y",n[0][1]).attr("width",n[1][0]-n[0][0]).attr("height",n[1][1]-n[0][1]),t.selectAll(".handle").style("display",null).attr("x",function(t){return"e"===t.type[t.type.length-1]?n[1][0]-O/2:n[0][0]-O/2}).attr("y",function(t){return"s"===t.type[0]?n[1][1]-O/2:n[0][1]-O/2}).attr("width",function(t){return"n"===t.type||"s"===t.type?n[1][0]-n[0][0]+O:O}).attr("height",function(t){return"e"===t.type||"w"===t.type?n[1][1]-n[0][1]+O:O})):t.selectAll(".selection,.handle").style("display","none").attr("x",null).attr("y",null).attr("width",null).attr("height",null)}function j(t,n,e){return!e&&t.__brush.emitter||new D(t,n)}function D(t,n){this.that=t,this.args=n,this.state=t.__brush,this.active=0}function L(){if((!n||u.event.touches)&&g.apply(this,arguments)){var e,r,o,c,f,y,_,k,B,F,T,S,O=this,R=u.event.target.__data__.type,D="selection"===(C&&u.event.metaKey?R="overlay":R)?h:C&&u.event.altKey?v:p,L=t===b?null:E[R],I=t===m?null:M[R],U=N(O),z=U.extent,Y=U.selection,q=z[0][0],H=z[0][1],$=z[1][0],V=z[1][1],G=0,X=0,W=L&&I&&C&&u.event.shiftKey,J=u.event.touches?(S=u.event.changedTouches[0].identifier,function(t){return Object(u.touch)(t,u.event.touches,S)}):u.mouse,Z=J(O),Q=Z,K=j(O,arguments,!0).beforestart();"overlay"===R?(Y&&(B=!0),U.selection=Y=[[e=t===b?q:Z[0],o=t===m?H:Z[1]],[f=t===b?$:e,_=t===m?V:o]]):(e=Y[0][0],o=Y[0][1],f=Y[1][0],_=Y[1][1]),r=e,c=o,y=f,k=_;var tt=Object(u.select)(O).attr("pointer-events","none"),nt=tt.selectAll(".overlay").attr("cursor",x[R]);if(u.event.touches)K.moved=rt,K.ended=ot;else{var et=Object(u.select)(u.event.view).on("mousemove.brush",rt,!0).on("mouseup.brush",ot,!0);C&&et.on("keydown.brush",function(){switch(u.event.keyCode){case 16:W=L&&I;break;case 18:D===p&&(L&&(f=y-G*L,e=r+G*L),I&&(_=k-X*I,o=c+X*I),D=v,it());break;case 32:D!==p&&D!==v||(L<0?f=y-G:L>0&&(e=r-G),I<0?_=k-X:I>0&&(o=c-X),D=d,nt.attr("cursor",x.selection),it());break;default:return}l()},!0).on("keyup.brush",function(){switch(u.event.keyCode){case 16:W&&(F=T=W=!1,it());break;case 18:D===v&&(L<0?f=y:L>0&&(e=r),I<0?_=k:I>0&&(o=c),D=p,it());break;case 32:D===d&&(u.event.altKey?(L&&(f=y-G*L,e=r+G*L),I&&(_=k-X*I,o=c+X*I),D=v):(L<0?f=y:L>0&&(e=r),I<0?_=k:I>0&&(o=c),D=p),nt.attr("cursor",x[R]),it());break;default:return}l()},!0),Object(i.dragDisable)(u.event.view)}s(),Object(a.interrupt)(O),P.call(O),K.start()}function rt(){var t=J(O);!W||F||T||(Math.abs(t[0]-Q[0])>Math.abs(t[1]-Q[1])?T=!0:F=!0),Q=t,B=!0,l(),it()}function it(){var t;switch(G=Q[0]-Z[0],X=Q[1]-Z[1],D){case d:case h:L&&(G=Math.max(q-e,Math.min($-f,G)),r=e+G,y=f+G),I&&(X=Math.max(H-o,Math.min(V-_,X)),c=o+X,k=_+X);break;case p:L<0?(G=Math.max(q-e,Math.min($-e,G)),r=e+G,y=f):L>0&&(G=Math.max(q-f,Math.min($-f,G)),r=e,y=f+G),I<0?(X=Math.max(H-o,Math.min(V-o,X)),c=o+X,k=_):I>0&&(X=Math.max(H-_,Math.min(V-_,X)),c=o,k=_+X);break;case v:L&&(r=Math.max(q,Math.min($,e-G*L)),y=Math.max(q,Math.min($,f+G*L))),I&&(c=Math.max(H,Math.min(V,o-X*I)),k=Math.max(H,Math.min(V,_+X*I)))}y<r&&(L*=-1,t=e,e=f,f=t,t=r,r=y,y=t,R in w&&nt.attr("cursor",x[R=w[R]])),k<c&&(I*=-1,t=o,o=_,_=t,t=c,c=k,k=t,R in A&&nt.attr("cursor",x[R=A[R]])),U.selection&&(Y=U.selection),F&&(r=Y[0][0],y=Y[1][0]),T&&(c=Y[0][1],k=Y[1][1]),Y[0][0]===r&&Y[0][1]===c&&Y[1][0]===y&&Y[1][1]===k||(U.selection=[[r,c],[y,k]],P.call(O),K.brush())}function ot(){if(s(),u.event.touches){if(u.event.touches.length)return;n&&clearTimeout(n),n=setTimeout(function(){n=null},500)}else Object(i.dragEnable)(u.event.view,B),et.on("keydown.brush keyup.brush mousemove.brush mouseup.brush",null);tt.attr("pointer-events","all"),nt.attr("cursor",x.overlay),U.selection&&(Y=U.selection),function(t){return t[0][0]===t[1][0]||t[0][1]===t[1][1]}(Y)&&(U.selection=null,P.call(O)),K.end()}}function I(){j(this,arguments).moved()}function U(){j(this,arguments).ended()}function z(){var n=this.__brush||{selection:null};return n.extent=y(e.apply(this,arguments)),n.dim=t,n}return R.move=function(n,e){n.selection?n.on("start.brush",function(){j(this,arguments).beforestart().start()}).on("interrupt.brush end.brush",function(){j(this,arguments).end()}).tween("brush",function(){var n=this,r=n.__brush,i=j(n,arguments),u=r.selection,a=t.input("function"==typeof e?e.apply(this,arguments):e,r.extent),c=Object(o.interpolate)(u,a);function f(t){r.selection=1===t&&null===a?null:c(t),P.call(n),i.brush()}return null!==u&&null!==a?f:f(1)}):n.each(function(){var n=this,r=arguments,i=n.__brush,o=t.input("function"==typeof e?e.apply(n,r):e,i.extent),u=j(n,r).beforestart();Object(a.interrupt)(n),i.selection=null===o?null:o,P.call(n),u.start().brush().end()})},R.clear=function(t){R.move(t,null)},D.prototype={beforestart:function(){return 1==++this.active&&(this.state.emitter=this,this.starting=!0),this},start:function(){return this.starting?(this.starting=!1,this.emit("start")):this.emit("brush"),this},brush:function(){return this.emit("brush"),this},end:function(){return 0==--this.active&&(delete this.state.emitter,this.emit("end")),this},emit:function(n){Object(u.customEvent)(new f(R,n,t.output(this.state.selection)),S.apply,S,[n,this.that,this.args])}},R.extent=function(t){return arguments.length?(e="function"==typeof t?t:c(y(t)),R):e},R.filter=function(t){return arguments.length?(g="function"==typeof t?t:c(!!t),R):g},R.handleSize=function(t){return arguments.length?(O=+t,R):O},R.keyModifiers=function(t){return arguments.length?(C=!!t,R):C},R.on=function(){var t=S.on.apply(S,arguments);return t===S?R:t},R}e.d(n,"brush",function(){return R}),e.d(n,"brushX",function(){return S}),e.d(n,"brushY",function(){return O}),e.d(n,"brushSelection",function(){return C})},1338:function(t,n,e){"use strict";e.r(n);var r=Array.prototype.slice,i=function(t){return t},o=1,u=2,a=3,c=4,f=1e-6;function s(t){return"translate("+(t+.5)+",0)"}function l(t){return"translate(0,"+(t+.5)+")"}function h(t){return function(n){return+t(n)}}function d(t){var n=Math.max(0,t.bandwidth()-1)/2;return t.round()&&(n=Math.round(n)),function(e){return+t(e)+n}}function p(){return!this.__axis}function v(t,n){var e=[],v=null,g=null,y=6,m=6,b=3,_=t===o||t===c?-1:1,x=t===c||t===u?"x":"y",w=t===o||t===a?s:l;function A(r){var s=null==v?n.ticks?n.ticks.apply(n,e):n.domain():v,l=null==g?n.tickFormat?n.tickFormat.apply(n,e):i:g,A=Math.max(y,0)+b,E=n.range(),M=+E[0]+.5,k=+E[E.length-1]+.5,B=(n.bandwidth?d:h)(n.copy()),F=r.selection?r.selection():r,T=F.selectAll(".domain").data([null]),N=F.selectAll(".tick").data(s,n).order(),C=N.exit(),S=N.enter().append("g").attr("class","tick"),O=N.select("line"),R=N.select("text");T=T.merge(T.enter().insert("path",".tick").attr("class","domain").attr("stroke","currentColor")),N=N.merge(S),O=O.merge(S.append("line").attr("stroke","currentColor").attr(x+"2",_*y)),R=R.merge(S.append("text").attr("fill","currentColor").attr(x,_*A).attr("dy",t===o?"0em":t===a?"0.71em":"0.32em")),r!==F&&(T=T.transition(r),N=N.transition(r),O=O.transition(r),R=R.transition(r),C=C.transition(r).attr("opacity",f).attr("transform",function(t){return isFinite(t=B(t))?w(t):this.getAttribute("transform")}),S.attr("opacity",f).attr("transform",function(t){var n=this.parentNode.__axis;return w(n&&isFinite(n=n(t))?n:B(t))})),C.remove(),T.attr("d",t===c||t==u?m?"M"+_*m+","+M+"H0.5V"+k+"H"+_*m:"M0.5,"+M+"V"+k:m?"M"+M+","+_*m+"V0.5H"+k+"V"+_*m:"M"+M+",0.5H"+k),N.attr("opacity",1).attr("transform",function(t){return w(B(t))}),O.attr(x+"2",_*y),R.attr(x,_*A).text(l),F.filter(p).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",t===u?"start":t===c?"end":"middle"),F.each(function(){this.__axis=B})}return A.scale=function(t){return arguments.length?(n=t,A):n},A.ticks=function(){return e=r.call(arguments),A},A.tickArguments=function(t){return arguments.length?(e=null==t?[]:r.call(t),A):e.slice()},A.tickValues=function(t){return arguments.length?(v=null==t?null:r.call(t),A):v&&v.slice()},A.tickFormat=function(t){return arguments.length?(g=t,A):g},A.tickSize=function(t){return arguments.length?(y=m=+t,A):y},A.tickSizeInner=function(t){return arguments.length?(y=+t,A):y},A.tickSizeOuter=function(t){return arguments.length?(m=+t,A):m},A.tickPadding=function(t){return arguments.length?(b=+t,A):b},A}function g(t){return v(o,t)}function y(t){return v(u,t)}function m(t){return v(a,t)}function b(t){return v(c,t)}e.d(n,"axisTop",function(){return g}),e.d(n,"axisRight",function(){return y}),e.d(n,"axisBottom",function(){return m}),e.d(n,"axisLeft",function(){return b})},134:function(t,n,e){"use strict";e.r(n);var r,i,o=0,u=0,a=0,c=1e3,f=0,s=0,l=0,h="object"==typeof performance&&performance.now?performance:Date,d="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function p(){return s||(d(v),s=h.now()+l)}function v(){s=0}function g(){this._call=this._time=this._next=null}function y(t,n,e){var r=new g;return r.restart(t,n,e),r}function m(){p(),++o;for(var t,n=r;n;)(t=s-n._time)>=0&&n._call.call(null,t),n=n._next;--o}function b(){s=(f=h.now())+l,o=u=0;try{m()}finally{o=0,function(){var t,n,e=r,o=1/0;for(;e;)e._call?(o>e._time&&(o=e._time),t=e,e=e._next):(n=e._next,e._next=null,e=t?t._next=n:r=n);i=t,x(o)}(),s=0}}function _(){var t=h.now(),n=t-f;n>c&&(l-=n,f=t)}function x(t){o||(u&&(u=clearTimeout(u)),t-s>24?(t<1/0&&(u=setTimeout(b,t-h.now()-l)),a&&(a=clearInterval(a))):(a||(f=h.now(),a=setInterval(_,c)),o=1,d(b)))}g.prototype=y.prototype={constructor:g,restart:function(t,n,e){if("function"!=typeof t)throw new TypeError("callback is not a function");e=(null==e?p():+e)+(null==n?0:+n),this._next||i===this||(i?i._next=this:r=this,i=this),this._call=t,this._time=e,x()},stop:function(){this._call&&(this._call=null,this._time=1/0,x())}};var w=function(t,n,e){var r=new g;return n=null==n?0:+n,r.restart(function(e){r.stop(),t(e+n)},n,e),r},A=function(t,n,e){var r=new g,i=n;return null==n?(r.restart(t,n,e),r):(n=+n,e=null==e?p():+e,r.restart(function o(u){u+=i,r.restart(o,i+=n,e),t(u)},n,e),r)};e.d(n,"now",function(){return p}),e.d(n,"timer",function(){return y}),e.d(n,"timerFlush",function(){return m}),e.d(n,"timeout",function(){return w}),e.d(n,"interval",function(){return A})},135:function(t,n,e){"use strict";e.r(n);var r=function(t,n){if((e=(t=n?t.toExponential(n-1):t.toExponential()).indexOf("e"))<0)return null;var e,r=t.slice(0,e);return[r.length>1?r[0]+r.slice(2):r,+t.slice(e+1)]},i=function(t){return(t=r(Math.abs(t)))?t[1]:NaN},o=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function u(t){return new a(t)}function a(t){if(!(n=o.exec(t)))throw new Error("invalid format: "+t);var n;this.fill=n[1]||" ",this.align=n[2]||">",this.sign=n[3]||"-",this.symbol=n[4]||"",this.zero=!!n[5],this.width=n[6]&&+n[6],this.comma=!!n[7],this.precision=n[8]&&+n[8].slice(1),this.trim=!!n[9],this.type=n[10]||""}u.prototype=a.prototype,a.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(null==this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(null==this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};var c,f,s,l,h=function(t){t:for(var n,e=t.length,r=1,i=-1;r<e;++r)switch(t[r]){case".":i=n=r;break;case"0":0===i&&(i=r),n=r;break;default:if(i>0){if(!+t[r])break t;i=0}}return i>0?t.slice(0,i)+t.slice(n+1):t},d=function(t,n){var e=r(t,n);if(!e)return t+"";var i=e[0],o=e[1];return o<0?"0."+new Array(-o).join("0")+i:i.length>o+1?i.slice(0,o+1)+"."+i.slice(o+1):i+new Array(o-i.length+2).join("0")},p={"%":function(t,n){return(100*t).toFixed(n)},b:function(t){return Math.round(t).toString(2)},c:function(t){return t+""},d:function(t){return Math.round(t).toString(10)},e:function(t,n){return t.toExponential(n)},f:function(t,n){return t.toFixed(n)},g:function(t,n){return t.toPrecision(n)},o:function(t){return Math.round(t).toString(8)},p:function(t,n){return d(100*t,n)},r:d,s:function(t,n){var e=r(t,n);if(!e)return t+"";var i=e[0],o=e[1],u=o-(c=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=i.length;return u===a?i:u>a?i+new Array(u-a+1).join("0"):u>0?i.slice(0,u)+"."+i.slice(u):"0."+new Array(1-u).join("0")+r(t,Math.max(0,n+u-1))[0]},X:function(t){return Math.round(t).toString(16).toUpperCase()},x:function(t){return Math.round(t).toString(16)}},v=function(t){return t},g=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"],y=function(t){var n,e,r=t.grouping&&t.thousands?(n=t.grouping,e=t.thousands,function(t,r){for(var i=t.length,o=[],u=0,a=n[0],c=0;i>0&&a>0&&(c+a+1>r&&(a=Math.max(1,r-c)),o.push(t.substring(i-=a,i+a)),!((c+=a+1)>r));)a=n[u=(u+1)%n.length];return o.reverse().join(e)}):v,o=t.currency,a=t.decimal,f=t.numerals?function(t){return function(n){return n.replace(/[0-9]/g,function(n){return t[+n]})}}(t.numerals):v,s=t.percent||"%";function l(t){var n=(t=u(t)).fill,e=t.align,i=t.sign,l=t.symbol,d=t.zero,v=t.width,y=t.comma,m=t.precision,b=t.trim,_=t.type;"n"===_?(y=!0,_="g"):p[_]||(null==m&&(m=12),b=!0,_="g"),(d||"0"===n&&"="===e)&&(d=!0,n="0",e="=");var x="$"===l?o[0]:"#"===l&&/[boxX]/.test(_)?"0"+_.toLowerCase():"",w="$"===l?o[1]:/[%p]/.test(_)?s:"",A=p[_],E=/[defgprs%]/.test(_);function M(t){var o,u,s,l=x,p=w;if("c"===_)p=A(t)+p,t="";else{var M=(t=+t)<0;if(t=A(Math.abs(t),m),b&&(t=h(t)),M&&0==+t&&(M=!1),l=(M?"("===i?i:"-":"-"===i||"("===i?"":i)+l,p=("s"===_?g[8+c/3]:"")+p+(M&&"("===i?")":""),E)for(o=-1,u=t.length;++o<u;)if(48>(s=t.charCodeAt(o))||s>57){p=(46===s?a+t.slice(o+1):t.slice(o))+p,t=t.slice(0,o);break}}y&&!d&&(t=r(t,1/0));var k=l.length+t.length+p.length,B=k<v?new Array(v-k+1).join(n):"";switch(y&&d&&(t=r(B+t,B.length?v-p.length:1/0),B=""),e){case"<":t=l+t+p+B;break;case"=":t=l+B+t+p;break;case"^":t=B.slice(0,k=B.length>>1)+l+t+p+B.slice(k);break;default:t=B+l+t+p}return f(t)}return m=null==m?6:/[gprs]/.test(_)?Math.max(1,Math.min(21,m)):Math.max(0,Math.min(20,m)),M.toString=function(){return t+""},M}return{format:l,formatPrefix:function(t,n){var e=l(((t=u(t)).type="f",t)),r=3*Math.max(-8,Math.min(8,Math.floor(i(n)/3))),o=Math.pow(10,-r),a=g[8+r/3];return function(t){return e(o*t)+a}}}};function m(t){return f=y(t),s=f.format,l=f.formatPrefix,f}m({decimal:".",thousands:",",grouping:[3],currency:["$",""]});var b=function(t){return Math.max(0,-i(Math.abs(t)))},_=function(t,n){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(i(n)/3)))-i(Math.abs(t)))},x=function(t,n){return t=Math.abs(t),n=Math.abs(n)-t,Math.max(0,i(n)-i(t))+1};e.d(n,"formatDefaultLocale",function(){return m}),e.d(n,"format",function(){return s}),e.d(n,"formatPrefix",function(){return l}),e.d(n,"formatLocale",function(){return y}),e.d(n,"formatSpecifier",function(){return u}),e.d(n,"precisionFixed",function(){return b}),e.d(n,"precisionPrefix",function(){return _}),e.d(n,"precisionRound",function(){return x})},14:function(t,n,e){"use strict";e.d(n,"b",function(){return i}),e.d(n,"a",function(){return o});var r=e(0);
/**
 * Base64 functions.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2016
 * @license Apache-2.0
 */function i(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"A-Za-z0-9+/=";if(!t)return"";t instanceof ArrayBuffer&&(t=new Uint8Array(t)),"string"==typeof t&&(t=r.b.strToByteArray(t)),n=r.b.expandAlphRange(n).join("");for(var e,i,o,u,a,c,f,s="",l=0;l<t.length;)u=(e=t[l++])>>2,a=(3&e)<<4|(i=t[l++])>>4,c=(15&i)<<2|(o=t[l++])>>6,f=63&o,isNaN(i)?c=f=64:isNaN(o)&&(f=64),s+=n.charAt(u)+n.charAt(a)+n.charAt(c)+n.charAt(f);return s}function o(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"A-Za-z0-9+/=",e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"string",i=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];if(!t)return"string"===e?"":[];n=n||"A-Za-z0-9+/=",n=r.b.expandAlphRange(n).join("");var o,u,a,c,f,s,l=[],h=0;if(i){var d=new RegExp("[^"+n.replace(/[[\]\\\-^$]/g,"\\$&")+"]","g");t=t.replace(d,"")}for(;h<t.length;)o=n.indexOf(t.charAt(h++))<<2|(c=-1===(c=n.indexOf(t.charAt(h++)||"="))?64:c)>>4,u=(15&c)<<4|(f=-1===(f=n.indexOf(t.charAt(h++)||"="))?64:f)>>2,a=(3&f)<<6|(s=-1===(s=n.indexOf(t.charAt(h++)||"="))?64:s),l.push(o),64!==f&&l.push(u),64!==s&&l.push(a);return"string"===e?r.b.byteArrayToUtf8(l):l}},15:function(t,n,e){"use strict";var r=e(7),i=e.n(r),o=e(11),u=e.n(o),a=e(1),c=e.n(a),f=e(2),s=e.n(f),l=e(0),h=e(4),d=e.n(h),p=e(3),v=e.n(p),g=e(25),y=e.n(g),m=e(5),b=e.n(m);
/**
 * Custom error type for handling Dish type errors.
 * i.e. where the Dish cannot be successfully translated between types
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 */
var _=function(t){function n(){var t;c()(this,n);for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];return(t=d()(this,v()(n).call(this,...r))).type="DishError",Error.captureStackTrace&&Error.captureStackTrace(y()(t),n),t}return b()(n,t),n}(function(t){function n(){var n=Reflect.construct(t,Array.from(arguments));return Object.setPrototypeOf(n,Object.getPrototypeOf(this)),n}return n.prototype=Object.create(t.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),Object.setPrototypeOf?Object.setPrototypeOf(n,t):n.__proto__=t,n}(Error)),x=e(16),w=e.n(x),A=e(18),E=e(26),M=e.n(E),k=function(){function t(){c()(this,t)}return s()(t,null,[{key:"checkForValue",value:function(t){if(void 0===t)throw new Error("only use translation methods with .bind")}},{key:"toArrayBuffer",value:function(){throw new Error("toArrayBuffer has not been implemented")}},{key:"fromArrayBuffer",value:function(){arguments.length>0&&void 0!==arguments[0]&&arguments[0];throw new Error("fromArrayBuffer has not been implemented")}}]),t}(),B=function(t){function n(){return c()(this,n),d()(this,v()(n).apply(this,arguments))}return b()(n,t),s()(n,null,[{key:"toArrayBuffer",value:function(){n.checkForValue(this.value),this.value=new Uint8Array(this.value).buffer}},{key:"fromArrayBuffer",value:function(){n.checkForValue(this.value),this.value=Array.prototype.slice.call(new Uint8Array(this.value))}}]),n}(k),F=function(t){function n(){return c()(this,n),d()(this,v()(n).apply(this,arguments))}return b()(n,t),s()(n,null,[{key:"toArrayBuffer",value:function(){n.checkForValue(this.value),this.value=w.a.isBigNumber(this.value)?l.b.strToArrayBuffer(this.value.toFixed()):new ArrayBuffer}},{key:"fromArrayBuffer",value:function(t){n.checkForValue(this.value);try{this.value=new w.a(l.b.arrayBufferToStr(this.value,!t))}catch(t){this.value=new w.a(NaN)}}}]),n}(k),T=function(t){function n(){return c()(this,n),d()(this,v()(n).apply(this,arguments))}return b()(n,t),s()(n,null,[{key:"toArrayBuffer",value:function(){var t=this;if(n.checkForValue(this.value),!Object(l.c)())return new Promise(function(n,e){l.b.readFile(t.value).then(function(n){return t.value=n.buffer}).then(n).catch(e)});this.value=l.b.readFileSync(this.value)}},{key:"fromArrayBuffer",value:function(){n.checkForValue(this.value),this.value=new File(this.value,"unknown")}}]),n}(k),N=function(t){function n(){return c()(this,n),d()(this,v()(n).apply(this,arguments))}return b()(n,t),s()(n,null,[{key:"toArrayBuffer",value:function(){n.checkForValue(this.value),this.value=this.value?l.b.strToArrayBuffer(this.value):new ArrayBuffer}},{key:"fromArrayBuffer",value:function(t){n.checkForValue(this.value),this.value=this.value?l.b.arrayBufferToStr(this.value,!t):""}}]),n}(k),C=function(t){function n(){return c()(this,n),d()(this,v()(n).apply(this,arguments))}return b()(n,t),s()(n,null,[{key:"toArrayBuffer",value:function(){n.checkForValue(this.value),this.value=this.value?l.b.strToArrayBuffer(l.b.unescapeHtml(l.b.stripHtmlTags(this.value,!0))):new ArrayBuffer}}]),n}(N),S=function(t){function n(){return c()(this,n),d()(this,v()(n).apply(this,arguments))}return b()(n,t),s()(n,null,[{key:"toArrayBuffer",value:function(){n.checkForValue(this.value),this.value=this.value?l.b.strToArrayBuffer(JSON.stringify(this.value,null,4)):new ArrayBuffer}},{key:"fromArrayBuffer",value:function(t){n.checkForValue(this.value),this.value=JSON.parse(l.b.arrayBufferToStr(this.value,!t))}}]),n}(k),O=function(t){function n(){return c()(this,n),d()(this,v()(n).apply(this,arguments))}return b()(n,t),s()(n,null,[{key:"toArrayBuffer",value:function(){n.checkForValue(this.value),Object(l.c)()&&(this.value=this.value.map(function(t){return Uint8Array.from(t.data)})),this.value=n.concatenateTypedArrays(...this.value).buffer}},{key:"fromArrayBuffer",value:function(){n.checkForValue(this.value),this.value=[new File(this.value,"unknown")]}},{key:"concatenateTypedArrays",value:function(){for(var t=0,n=arguments.length,e=new Array(n),r=0;r<n;r++)e[r]=arguments[r];for(var i=0,o=e;i<o.length;i++){var u=o[i];t+=u.length}for(var a=new Uint8Array(t),c=0,f=0,s=e;f<s.length;f++){var l=s[f];a.set(l,c),c+=l.length}return a}}]),n}(k),R=function(t){function n(){return c()(this,n),d()(this,v()(n).apply(this,arguments))}return b()(n,t),s()(n,null,[{key:"toArrayBuffer",value:function(){n.checkForValue(this.value),this.value="number"==typeof this.value?l.b.strToArrayBuffer(this.value.toString()):new ArrayBuffer}},{key:"fromArrayBuffer",value:function(t){n.checkForValue(this.value),this.value=this.value?parseFloat(l.b.arrayBufferToStr(this.value,!t)):0}}]),n}(k),P=function(){function t(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(c()(this,t),this.value=new ArrayBuffer(0),this.type=t.ARRAY_BUFFER,n&&Object.prototype.hasOwnProperty.call(n,"value")&&Object.prototype.hasOwnProperty.call(n,"type"))this.set(n.value,n.type);else if(n&&null!==e)this.set(n,e);else if(n){var r=t.typeEnum(n.constructor.name);this.set(n,r)}}var n;return s()(t,[{key:"get",value:function(n){var e=this,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return"string"==typeof n&&(n=t.typeEnum(n)),this.type!==n?Object(l.c)()?(this._translate(n,r),this.value):new Promise(function(t,i){e._translate(n,r).then(function(){t(e.value)}).catch(i)}):this.value}},{key:"set",value:function(n,e){if("string"==typeof e&&(e=t.typeEnum(e)),M.a.debug("Dish type: "+t.enumLookup(e)),this.value=n,this.type=e,!this.valid()){var r=l.b.truncate(JSON.stringify(this.value),25);throw new _(`Data is not a valid ${t.enumLookup(e)}: ${r}`)}}},{key:"presentAs",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],e=this.clone();return e.get(t,n)}},{key:"detectDishType",value:function(){var t=new Uint8Array(this.value.slice(0,2048)),n=Object(A.a)(t);return n.length&&n[0].mime&&"text/plain"!==!n[0].mime?n[0].mime:null}},{key:"getTitle",value:(n=u()(i.a.mark(function n(e){var r,o;return i.a.wrap(function(n){for(;;)switch(n.prev=n.next){case 0:r="",n.t0=this.type,n.next=n.t0===t.FILE?4:n.t0===t.LIST_FILE?6:n.t0===t.JSON?8:n.t0===t.NUMBER?10:n.t0===t.BIG_NUMBER?10:n.t0===t.ARRAY_BUFFER?12:n.t0===t.BYTE_ARRAY?12:15;break;case 4:return r=this.value.name,n.abrupt("break",26);case 6:return r=`${this.value.length} file(s)`,n.abrupt("break",26);case 8:return r="application/json",n.abrupt("break",26);case 10:return r=this.value.toString(),n.abrupt("break",26);case 12:if(null===(r=this.detectDishType())){n.next=15;break}return n.abrupt("break",26);case 15:return n.prev=15,(o=this.clone()).value=o.value.slice(0,256),n.next=20,o.get(t.STRING);case 20:r=n.sent,n.next=26;break;case 23:n.prev=23,n.t1=n.catch(15),M.a.error(`${t.enumLookup(this.type)} cannot be sliced. ${n.t1}`);case 26:return n.abrupt("return",r.slice(0,e));case 27:case"end":return n.stop()}},n,this,[[15,23]])})),function(t){return n.apply(this,arguments)})},{key:"valid",value:function(){switch(this.type){case t.BYTE_ARRAY:if(!(this.value instanceof Uint8Array||this.value instanceof Array))return!1;for(var n=0;n<this.value.length;n++)if("number"!=typeof this.value[n]||this.value[n]<0||this.value[n]>255)return!1;return!0;case t.STRING:case t.HTML:return"string"==typeof this.value;case t.NUMBER:return"number"==typeof this.value;case t.ARRAY_BUFFER:return this.value instanceof ArrayBuffer;case t.BIG_NUMBER:if(w.a.isBigNumber(this.value))return!0;if(Object.keys(this.value).sort().equals(["c","e","s"])){var e=new w.a;return e.c=this.value.c,e.e=this.value.e,e.s=this.value.s,this.value=e,!0}return!1;case t.JSON:return!0;case t.FILE:return this.value instanceof File;case t.LIST_FILE:return this.value instanceof Array&&this.value.reduce(function(t,n){return t&&n instanceof File},!0);default:return!1}}},{key:"clone",value:function(){var n=new t;switch(this.type){case t.STRING:case t.HTML:case t.NUMBER:case t.BIG_NUMBER:n.set(this.value,this.type);break;case t.BYTE_ARRAY:case t.JSON:n.set(JSON.parse(JSON.stringify(this.value)),this.type);break;case t.ARRAY_BUFFER:n.set(this.value.slice(0),this.type);break;case t.FILE:n.set(new File([this.value],this.value.name,{type:this.value.type,lastModified:this.value.lastModified}),this.type);break;case t.LIST_FILE:n.set(this.value.map(function(t){return new File([t],t.name,{type:t.type,lastModified:t.lastModified})}),this.type);break;default:throw new _("Cannot clone Dish, unknown type")}return n}},{key:"_translate",value:function(n){var e=this,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(M.a.debug(`Translating Dish from ${t.enumLookup(this.type)} to ${t.enumLookup(n)}`),!Object(l.c)())return new Promise(function(r,i){e._toArrayBuffer().then(function(){return e.type=t.ARRAY_BUFFER}).then(function(){e._fromArrayBuffer(n),r()}).catch(i)});this._toArrayBuffer(),this.type=t.ARRAY_BUFFER,this._fromArrayBuffer(n,r)}},{key:"_toArrayBuffer",value:function(){var n=this,e={browser:{[t.STRING]:function(){return Promise.resolve(N.toArrayBuffer.bind(n)())},[t.NUMBER]:function(){return Promise.resolve(R.toArrayBuffer.bind(n)())},[t.HTML]:function(){return Promise.resolve(C.toArrayBuffer.bind(n)())},[t.ARRAY_BUFFER]:function(){return Promise.resolve()},[t.BIG_NUMBER]:function(){return Promise.resolve(F.toArrayBuffer.bind(n)())},[t.JSON]:function(){return Promise.resolve(S.toArrayBuffer.bind(n)())},[t.FILE]:function(){return T.toArrayBuffer.bind(n)()},[t.LIST_FILE]:function(){return Promise.resolve(O.toArrayBuffer.bind(n)())},[t.BYTE_ARRAY]:function(){return Promise.resolve(B.toArrayBuffer.bind(n)())}},node:{[t.STRING]:function(){return N.toArrayBuffer.bind(n)()},[t.NUMBER]:function(){return R.toArrayBuffer.bind(n)()},[t.HTML]:function(){return C.toArrayBuffer.bind(n)()},[t.ARRAY_BUFFER]:function(){},[t.BIG_NUMBER]:function(){return F.toArrayBuffer.bind(n)()},[t.JSON]:function(){return S.toArrayBuffer.bind(n)()},[t.FILE]:function(){return T.toArrayBuffer.bind(n)()},[t.LIST_FILE]:function(){return O.toArrayBuffer.bind(n)()},[t.BYTE_ARRAY]:function(){return B.toArrayBuffer.bind(n)()}}};try{return e[Object(l.c)()?"node":"browser"][this.type]()}catch(n){throw new _(`Error translating from ${t.enumLookup(this.type)} to ArrayBuffer: ${n}`)}}},{key:"_fromArrayBuffer",value:function(n,e){var r=this,i={[t.STRING]:function(){return N.fromArrayBuffer.bind(r)(e)},[t.NUMBER]:function(){return R.fromArrayBuffer.bind(r)(e)},[t.HTML]:function(){return C.fromArrayBuffer.bind(r)(e)},[t.ARRAY_BUFFER]:function(){},[t.BIG_NUMBER]:function(){return F.fromArrayBuffer.bind(r)(e)},[t.JSON]:function(){return S.fromArrayBuffer.bind(r)(e)},[t.FILE]:function(){return T.fromArrayBuffer.bind(r)()},[t.LIST_FILE]:function(){return O.fromArrayBuffer.bind(r)()},[t.BYTE_ARRAY]:function(){return B.fromArrayBuffer.bind(r)()}};try{i[n](),this.type=n}catch(e){throw new _(`Error translating from ArrayBuffer to ${t.enumLookup(n)}: ${e}`)}}},{key:"size",get:function(){switch(this.type){case t.BYTE_ARRAY:case t.STRING:case t.HTML:return this.value.length;case t.NUMBER:case t.BIG_NUMBER:return this.value.toString().length;case t.ARRAY_BUFFER:return this.value.byteLength;case t.JSON:return JSON.stringify(this.value).length;case t.FILE:return this.value.size;case t.LIST_FILE:return this.value.reduce(function(t,n){return t+n.size},0);default:return-1}}}],[{key:"typeEnum",value:function(n){switch(n.toLowerCase()){case"bytearray":case"byte array":return t.BYTE_ARRAY;case"string":return t.STRING;case"number":return t.NUMBER;case"html":return t.HTML;case"arraybuffer":case"array buffer":return t.ARRAY_BUFFER;case"bignumber":case"big number":return t.BIG_NUMBER;case"json":case"object":return t.JSON;case"file":return t.FILE;case"list<file>":return t.LIST_FILE;default:throw new _("Invalid data type string. No matching enum.")}}},{key:"enumLookup",value:function(n){switch(n){case t.BYTE_ARRAY:return"byteArray";case t.STRING:return"string";case t.NUMBER:return"number";case t.HTML:return"html";case t.ARRAY_BUFFER:return"ArrayBuffer";case t.BIG_NUMBER:return"BigNumber";case t.JSON:return"JSON";case t.FILE:return"File";case t.LIST_FILE:return"List<File>";default:throw new _("Invalid data type enum. No matching type.")}}}]),t}();P.BYTE_ARRAY=0,P.STRING=1,P.NUMBER=2,P.HTML=3,P.ARRAY_BUFFER=4,P.BIG_NUMBER=5,P.JSON=6,P.FILE=7,P.LIST_FILE=8;n.a=P},16:function(t,n,e){var r;!function(i){"use strict";var o,u=/^-?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?$/i,a=Math.ceil,c=Math.floor,f="[BigNumber Error] ",s=f+"Number primitive has more than 15 significant digits: ",l=1e14,h=14,d=9007199254740991,p=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9,1e10,1e11,1e12,1e13],v=1e7,g=1e9;function y(t){var n=0|t;return t>0||t===n?n:n-1}function m(t){for(var n,e,r=1,i=t.length,o=t[0]+"";r<i;){for(n=t[r++]+"",e=h-n.length;e--;n="0"+n);o+=n}for(i=o.length;48===o.charCodeAt(--i););return o.slice(0,i+1||1)}function b(t,n){var e,r,i=t.c,o=n.c,u=t.s,a=n.s,c=t.e,f=n.e;if(!u||!a)return null;if(e=i&&!i[0],r=o&&!o[0],e||r)return e?r?0:-a:u;if(u!=a)return u;if(e=u<0,r=c==f,!i||!o)return r?0:!i^e?1:-1;if(!r)return c>f^e?1:-1;for(a=(c=i.length)<(f=o.length)?c:f,u=0;u<a;u++)if(i[u]!=o[u])return i[u]>o[u]^e?1:-1;return c==f?0:c>f^e?1:-1}function _(t,n,e,r){if(t<n||t>e||t!==c(t))throw Error(f+(r||"Argument")+("number"==typeof t?t<n||t>e?" out of range: ":" not an integer: ":" not a primitive number: ")+String(t))}function x(t){var n=t.c.length-1;return y(t.e/h)==n&&t.c[n]%2!=0}function w(t,n){return(t.length>1?t.charAt(0)+"."+t.slice(1):t)+(n<0?"e":"e+")+n}function A(t,n,e){var r,i;if(n<0){for(i=e+".";++n;i+=e);t=i+t}else if(++n>(r=t.length)){for(i=e,n-=r;--n;i+=e);t+=i}else n<r&&(t=t.slice(0,n)+"."+t.slice(n));return t}(o=function t(n){var e,r,i,o,E,M,k,B,F,T=Y.prototype={constructor:Y,toString:null,valueOf:null},N=new Y(1),C=20,S=4,O=-7,R=21,P=-1e7,j=1e7,D=!1,L=1,I=0,U={prefix:"",groupSize:3,secondaryGroupSize:0,groupSeparator:",",decimalSeparator:".",fractionGroupSize:0,fractionGroupSeparator:" ",suffix:""},z="0123456789abcdefghijklmnopqrstuvwxyz";function Y(t,n){var e,o,a,f,l,p,v,g,y=this;if(!(y instanceof Y))return new Y(t,n);if(null==n){if(t&&!0===t._isBigNumber)return y.s=t.s,void(!t.c||t.e>j?y.c=y.e=null:t.e<P?y.c=[y.e=0]:(y.e=t.e,y.c=t.c.slice()));if((p="number"==typeof t)&&0*t==0){if(y.s=1/t<0?(t=-t,-1):1,t===~~t){for(f=0,l=t;l>=10;l/=10,f++);return void(f>j?y.c=y.e=null:(y.e=f,y.c=[t]))}g=String(t)}else{if(!u.test(g=String(t)))return i(y,g,p);y.s=45==g.charCodeAt(0)?(g=g.slice(1),-1):1}(f=g.indexOf("."))>-1&&(g=g.replace(".","")),(l=g.search(/e/i))>0?(f<0&&(f=l),f+=+g.slice(l+1),g=g.substring(0,l)):f<0&&(f=g.length)}else{if(_(n,2,z.length,"Base"),10==n)return V(y=new Y(t),C+y.e+1,S);if(g=String(t),p="number"==typeof t){if(0*t!=0)return i(y,g,p,n);if(y.s=1/t<0?(g=g.slice(1),-1):1,Y.DEBUG&&g.replace(/^0\.0*|\./,"").length>15)throw Error(s+t)}else y.s=45===g.charCodeAt(0)?(g=g.slice(1),-1):1;for(e=z.slice(0,n),f=l=0,v=g.length;l<v;l++)if(e.indexOf(o=g.charAt(l))<0){if("."==o){if(l>f){f=v;continue}}else if(!a&&(g==g.toUpperCase()&&(g=g.toLowerCase())||g==g.toLowerCase()&&(g=g.toUpperCase()))){a=!0,l=-1,f=0;continue}return i(y,String(t),p,n)}p=!1,(f=(g=r(g,n,10,y.s)).indexOf("."))>-1?g=g.replace(".",""):f=g.length}for(l=0;48===g.charCodeAt(l);l++);for(v=g.length;48===g.charCodeAt(--v););if(g=g.slice(l,++v)){if(v-=l,p&&Y.DEBUG&&v>15&&(t>d||t!==c(t)))throw Error(s+y.s*t);if((f=f-l-1)>j)y.c=y.e=null;else if(f<P)y.c=[y.e=0];else{if(y.e=f,y.c=[],l=(f+1)%h,f<0&&(l+=h),l<v){for(l&&y.c.push(+g.slice(0,l)),v-=h;l<v;)y.c.push(+g.slice(l,l+=h));l=h-(g=g.slice(l)).length}else l-=v;for(;l--;g+="0");y.c.push(+g)}}else y.c=[y.e=0]}function q(t,n,e,r){var i,o,u,a,c;if(null==e?e=S:_(e,0,8),!t.c)return t.toString();if(i=t.c[0],u=t.e,null==n)c=m(t.c),c=1==r||2==r&&(u<=O||u>=R)?w(c,u):A(c,u,"0");else if(o=(t=V(new Y(t),n,e)).e,a=(c=m(t.c)).length,1==r||2==r&&(n<=o||o<=O)){for(;a<n;c+="0",a++);c=w(c,o)}else if(n-=u,c=A(c,o,"0"),o+1>a){if(--n>0)for(c+=".";n--;c+="0");}else if((n+=o-a)>0)for(o+1==a&&(c+=".");n--;c+="0");return t.s<0&&i?"-"+c:c}function H(t,n){for(var e,r=1,i=new Y(t[0]);r<t.length;r++){if(!(e=new Y(t[r])).s){i=e;break}n.call(i,e)&&(i=e)}return i}function $(t,n,e){for(var r=1,i=n.length;!n[--i];n.pop());for(i=n[0];i>=10;i/=10,r++);return(e=r+e*h-1)>j?t.c=t.e=null:e<P?t.c=[t.e=0]:(t.e=e,t.c=n),t}function V(t,n,e,r){var i,o,u,f,s,d,v,g=t.c,y=p;if(g){t:{for(i=1,f=g[0];f>=10;f/=10,i++);if((o=n-i)<0)o+=h,u=n,v=(s=g[d=0])/y[i-u-1]%10|0;else if((d=a((o+1)/h))>=g.length){if(!r)break t;for(;g.length<=d;g.push(0));s=v=0,i=1,u=(o%=h)-h+1}else{for(s=f=g[d],i=1;f>=10;f/=10,i++);v=(u=(o%=h)-h+i)<0?0:s/y[i-u-1]%10|0}if(r=r||n<0||null!=g[d+1]||(u<0?s:s%y[i-u-1]),r=e<4?(v||r)&&(0==e||e==(t.s<0?3:2)):v>5||5==v&&(4==e||r||6==e&&(o>0?u>0?s/y[i-u]:0:g[d-1])%10&1||e==(t.s<0?8:7)),n<1||!g[0])return g.length=0,r?(n-=t.e+1,g[0]=y[(h-n%h)%h],t.e=-n||0):g[0]=t.e=0,t;if(0==o?(g.length=d,f=1,d--):(g.length=d+1,f=y[h-o],g[d]=u>0?c(s/y[i-u]%y[u])*f:0),r)for(;;){if(0==d){for(o=1,u=g[0];u>=10;u/=10,o++);for(u=g[0]+=f,f=1;u>=10;u/=10,f++);o!=f&&(t.e++,g[0]==l&&(g[0]=1));break}if(g[d]+=f,g[d]!=l)break;g[d--]=0,f=1}for(o=g.length;0===g[--o];g.pop());}t.e>j?t.c=t.e=null:t.e<P&&(t.c=[t.e=0])}return t}function G(t){var n,e=t.e;return null===e?t.toString():(n=m(t.c),n=e<=O||e>=R?w(n,e):A(n,e,"0"),t.s<0?"-"+n:n)}return Y.clone=t,Y.ROUND_UP=0,Y.ROUND_DOWN=1,Y.ROUND_CEIL=2,Y.ROUND_FLOOR=3,Y.ROUND_HALF_UP=4,Y.ROUND_HALF_DOWN=5,Y.ROUND_HALF_EVEN=6,Y.ROUND_HALF_CEIL=7,Y.ROUND_HALF_FLOOR=8,Y.EUCLID=9,Y.config=Y.set=function(t){var n,e;if(null!=t){if("object"!=typeof t)throw Error(f+"Object expected: "+t);if(t.hasOwnProperty(n="DECIMAL_PLACES")&&(_(e=t[n],0,g,n),C=e),t.hasOwnProperty(n="ROUNDING_MODE")&&(_(e=t[n],0,8,n),S=e),t.hasOwnProperty(n="EXPONENTIAL_AT")&&((e=t[n])&&e.pop?(_(e[0],-g,0,n),_(e[1],0,g,n),O=e[0],R=e[1]):(_(e,-g,g,n),O=-(R=e<0?-e:e))),t.hasOwnProperty(n="RANGE"))if((e=t[n])&&e.pop)_(e[0],-g,-1,n),_(e[1],1,g,n),P=e[0],j=e[1];else{if(_(e,-g,g,n),!e)throw Error(f+n+" cannot be zero: "+e);P=-(j=e<0?-e:e)}if(t.hasOwnProperty(n="CRYPTO")){if((e=t[n])!==!!e)throw Error(f+n+" not true or false: "+e);if(e){if("undefined"==typeof crypto||!crypto||!crypto.getRandomValues&&!crypto.randomBytes)throw D=!e,Error(f+"crypto unavailable");D=e}else D=e}if(t.hasOwnProperty(n="MODULO_MODE")&&(_(e=t[n],0,9,n),L=e),t.hasOwnProperty(n="POW_PRECISION")&&(_(e=t[n],0,g,n),I=e),t.hasOwnProperty(n="FORMAT")){if("object"!=typeof(e=t[n]))throw Error(f+n+" not an object: "+e);U=e}if(t.hasOwnProperty(n="ALPHABET")){if("string"!=typeof(e=t[n])||/^.$|[+-.\s]|(.).*\1/.test(e))throw Error(f+n+" invalid: "+e);z=e}}return{DECIMAL_PLACES:C,ROUNDING_MODE:S,EXPONENTIAL_AT:[O,R],RANGE:[P,j],CRYPTO:D,MODULO_MODE:L,POW_PRECISION:I,FORMAT:U,ALPHABET:z}},Y.isBigNumber=function(t){if(!t||!0!==t._isBigNumber)return!1;if(!Y.DEBUG)return!0;var n,e,r=t.c,i=t.e,o=t.s;t:if("[object Array]"=={}.toString.call(r)){if((1===o||-1===o)&&i>=-g&&i<=g&&i===c(i)){if(0===r[0]){if(0===i&&1===r.length)return!0;break t}if((n=(i+1)%h)<1&&(n+=h),String(r[0]).length==n){for(n=0;n<r.length;n++)if((e=r[n])<0||e>=l||e!==c(e))break t;if(0!==e)return!0}}}else if(null===r&&null===i&&(null===o||1===o||-1===o))return!0;throw Error(f+"Invalid BigNumber: "+t)},Y.maximum=Y.max=function(){return H(arguments,T.lt)},Y.minimum=Y.min=function(){return H(arguments,T.gt)},Y.random=(o=9007199254740992*Math.random()&2097151?function(){return c(9007199254740992*Math.random())}:function(){return 8388608*(1073741824*Math.random()|0)+(8388608*Math.random()|0)},function(t){var n,e,r,i,u,s=0,l=[],d=new Y(N);if(null==t?t=C:_(t,0,g),i=a(t/h),D)if(crypto.getRandomValues){for(n=crypto.getRandomValues(new Uint32Array(i*=2));s<i;)(u=131072*n[s]+(n[s+1]>>>11))>=9e15?(e=crypto.getRandomValues(new Uint32Array(2)),n[s]=e[0],n[s+1]=e[1]):(l.push(u%1e14),s+=2);s=i/2}else{if(!crypto.randomBytes)throw D=!1,Error(f+"crypto unavailable");for(n=crypto.randomBytes(i*=7);s<i;)(u=281474976710656*(31&n[s])+1099511627776*n[s+1]+4294967296*n[s+2]+16777216*n[s+3]+(n[s+4]<<16)+(n[s+5]<<8)+n[s+6])>=9e15?crypto.randomBytes(7).copy(n,s):(l.push(u%1e14),s+=7);s=i/7}if(!D)for(;s<i;)(u=o())<9e15&&(l[s++]=u%1e14);for(i=l[--s],t%=h,i&&t&&(u=p[h-t],l[s]=c(i/u)*u);0===l[s];l.pop(),s--);if(s<0)l=[r=0];else{for(r=-1;0===l[0];l.splice(0,1),r-=h);for(s=1,u=l[0];u>=10;u/=10,s++);s<h&&(r-=h-s)}return d.e=r,d.c=l,d}),Y.sum=function(){for(var t=1,n=arguments,e=new Y(n[0]);t<n.length;)e=e.plus(n[t++]);return e},r=function(){function t(t,n,e,r){for(var i,o,u=[0],a=0,c=t.length;a<c;){for(o=u.length;o--;u[o]*=n);for(u[0]+=r.indexOf(t.charAt(a++)),i=0;i<u.length;i++)u[i]>e-1&&(null==u[i+1]&&(u[i+1]=0),u[i+1]+=u[i]/e|0,u[i]%=e)}return u.reverse()}return function(n,r,i,o,u){var a,c,f,s,l,h,d,p,v=n.indexOf("."),g=C,y=S;for(v>=0&&(s=I,I=0,n=n.replace(".",""),h=(p=new Y(r)).pow(n.length-v),I=s,p.c=t(A(m(h.c),h.e,"0"),10,i,"0123456789"),p.e=p.c.length),f=s=(d=t(n,r,i,u?(a=z,"0123456789"):(a="0123456789",z))).length;0==d[--s];d.pop());if(!d[0])return a.charAt(0);if(v<0?--f:(h.c=d,h.e=f,h.s=o,d=(h=e(h,p,g,y,i)).c,l=h.r,f=h.e),v=d[c=f+g+1],s=i/2,l=l||c<0||null!=d[c+1],l=y<4?(null!=v||l)&&(0==y||y==(h.s<0?3:2)):v>s||v==s&&(4==y||l||6==y&&1&d[c-1]||y==(h.s<0?8:7)),c<1||!d[0])n=l?A(a.charAt(1),-g,a.charAt(0)):a.charAt(0);else{if(d.length=c,l)for(--i;++d[--c]>i;)d[c]=0,c||(++f,d=[1].concat(d));for(s=d.length;!d[--s];);for(v=0,n="";v<=s;n+=a.charAt(d[v++]));n=A(n,f,a.charAt(0))}return n}}(),e=function(){function t(t,n,e){var r,i,o,u,a=0,c=t.length,f=n%v,s=n/v|0;for(t=t.slice();c--;)a=((i=f*(o=t[c]%v)+(r=s*o+(u=t[c]/v|0)*f)%v*v+a)/e|0)+(r/v|0)+s*u,t[c]=i%e;return a&&(t=[a].concat(t)),t}function n(t,n,e,r){var i,o;if(e!=r)o=e>r?1:-1;else for(i=o=0;i<e;i++)if(t[i]!=n[i]){o=t[i]>n[i]?1:-1;break}return o}function e(t,n,e,r){for(var i=0;e--;)t[e]-=i,i=t[e]<n[e]?1:0,t[e]=i*r+t[e]-n[e];for(;!t[0]&&t.length>1;t.splice(0,1));}return function(r,i,o,u,a){var f,s,d,p,v,g,m,b,_,x,w,A,E,M,k,B,F,T=r.s==i.s?1:-1,N=r.c,C=i.c;if(!(N&&N[0]&&C&&C[0]))return new Y(r.s&&i.s&&(N?!C||N[0]!=C[0]:C)?N&&0==N[0]||!C?0*T:T/0:NaN);for(_=(b=new Y(T)).c=[],T=o+(s=r.e-i.e)+1,a||(a=l,s=y(r.e/h)-y(i.e/h),T=T/h|0),d=0;C[d]==(N[d]||0);d++);if(C[d]>(N[d]||0)&&s--,T<0)_.push(1),p=!0;else{for(M=N.length,B=C.length,d=0,T+=2,(v=c(a/(C[0]+1)))>1&&(C=t(C,v,a),N=t(N,v,a),B=C.length,M=N.length),E=B,w=(x=N.slice(0,B)).length;w<B;x[w++]=0);F=C.slice(),F=[0].concat(F),k=C[0],C[1]>=a/2&&k++;do{if(v=0,(f=n(C,x,B,w))<0){if(A=x[0],B!=w&&(A=A*a+(x[1]||0)),(v=c(A/k))>1)for(v>=a&&(v=a-1),m=(g=t(C,v,a)).length,w=x.length;1==n(g,x,m,w);)v--,e(g,B<m?F:C,m,a),m=g.length,f=1;else 0==v&&(f=v=1),m=(g=C.slice()).length;if(m<w&&(g=[0].concat(g)),e(x,g,w,a),w=x.length,-1==f)for(;n(C,x,B,w)<1;)v++,e(x,B<w?F:C,w,a),w=x.length}else 0===f&&(v++,x=[0]);_[d++]=v,x[0]?x[w++]=N[E]||0:(x=[N[E]],w=1)}while((E++<M||null!=x[0])&&T--);p=null!=x[0],_[0]||_.splice(0,1)}if(a==l){for(d=1,T=_[0];T>=10;T/=10,d++);V(b,o+(b.e=d+s*h-1)+1,u,p)}else b.e=s,b.r=+p;return b}}(),E=/^(-?)0([xbo])(?=\w[\w.]*$)/i,M=/^([^.]+)\.$/,k=/^\.([^.]+)$/,B=/^-?(Infinity|NaN)$/,F=/^\s*\+(?=[\w.])|^\s+|\s+$/g,i=function(t,n,e,r){var i,o=e?n:n.replace(F,"");if(B.test(o))t.s=isNaN(o)?null:o<0?-1:1;else{if(!e&&(o=o.replace(E,function(t,n,e){return i="x"==(e=e.toLowerCase())?16:"b"==e?2:8,r&&r!=i?t:n}),r&&(i=r,o=o.replace(M,"$1").replace(k,"0.$1")),n!=o))return new Y(o,i);if(Y.DEBUG)throw Error(f+"Not a"+(r?" base "+r:"")+" number: "+n);t.s=null}t.c=t.e=null},T.absoluteValue=T.abs=function(){var t=new Y(this);return t.s<0&&(t.s=1),t},T.comparedTo=function(t,n){return b(this,new Y(t,n))},T.decimalPlaces=T.dp=function(t,n){var e,r,i,o=this;if(null!=t)return _(t,0,g),null==n?n=S:_(n,0,8),V(new Y(o),t+o.e+1,n);if(!(e=o.c))return null;if(r=((i=e.length-1)-y(this.e/h))*h,i=e[i])for(;i%10==0;i/=10,r--);return r<0&&(r=0),r},T.dividedBy=T.div=function(t,n){return e(this,new Y(t,n),C,S)},T.dividedToIntegerBy=T.idiv=function(t,n){return e(this,new Y(t,n),0,1)},T.exponentiatedBy=T.pow=function(t,n){var e,r,i,o,u,s,l,d,p=this;if((t=new Y(t)).c&&!t.isInteger())throw Error(f+"Exponent not an integer: "+G(t));if(null!=n&&(n=new Y(n)),u=t.e>14,!p.c||!p.c[0]||1==p.c[0]&&!p.e&&1==p.c.length||!t.c||!t.c[0])return d=new Y(Math.pow(+G(p),u?2-x(t):+G(t))),n?d.mod(n):d;if(s=t.s<0,n){if(n.c?!n.c[0]:!n.s)return new Y(NaN);(r=!s&&p.isInteger()&&n.isInteger())&&(p=p.mod(n))}else{if(t.e>9&&(p.e>0||p.e<-1||(0==p.e?p.c[0]>1||u&&p.c[1]>=24e7:p.c[0]<8e13||u&&p.c[0]<=9999975e7)))return o=p.s<0&&x(t)?-0:0,p.e>-1&&(o=1/o),new Y(s?1/o:o);I&&(o=a(I/h+2))}for(u?(e=new Y(.5),s&&(t.s=1),l=x(t)):l=(i=Math.abs(+G(t)))%2,d=new Y(N);;){if(l){if(!(d=d.times(p)).c)break;o?d.c.length>o&&(d.c.length=o):r&&(d=d.mod(n))}if(i){if(0===(i=c(i/2)))break;l=i%2}else if(V(t=t.times(e),t.e+1,1),t.e>14)l=x(t);else{if(0===(i=+G(t)))break;l=i%2}p=p.times(p),o?p.c&&p.c.length>o&&(p.c.length=o):r&&(p=p.mod(n))}return r?d:(s&&(d=N.div(d)),n?d.mod(n):o?V(d,I,S,void 0):d)},T.integerValue=function(t){var n=new Y(this);return null==t?t=S:_(t,0,8),V(n,n.e+1,t)},T.isEqualTo=T.eq=function(t,n){return 0===b(this,new Y(t,n))},T.isFinite=function(){return!!this.c},T.isGreaterThan=T.gt=function(t,n){return b(this,new Y(t,n))>0},T.isGreaterThanOrEqualTo=T.gte=function(t,n){return 1===(n=b(this,new Y(t,n)))||0===n},T.isInteger=function(){return!!this.c&&y(this.e/h)>this.c.length-2},T.isLessThan=T.lt=function(t,n){return b(this,new Y(t,n))<0},T.isLessThanOrEqualTo=T.lte=function(t,n){return-1===(n=b(this,new Y(t,n)))||0===n},T.isNaN=function(){return!this.s},T.isNegative=function(){return this.s<0},T.isPositive=function(){return this.s>0},T.isZero=function(){return!!this.c&&0==this.c[0]},T.minus=function(t,n){var e,r,i,o,u=this,a=u.s;if(n=(t=new Y(t,n)).s,!a||!n)return new Y(NaN);if(a!=n)return t.s=-n,u.plus(t);var c=u.e/h,f=t.e/h,s=u.c,d=t.c;if(!c||!f){if(!s||!d)return s?(t.s=-n,t):new Y(d?u:NaN);if(!s[0]||!d[0])return d[0]?(t.s=-n,t):new Y(s[0]?u:3==S?-0:0)}if(c=y(c),f=y(f),s=s.slice(),a=c-f){for((o=a<0)?(a=-a,i=s):(f=c,i=d),i.reverse(),n=a;n--;i.push(0));i.reverse()}else for(r=(o=(a=s.length)<(n=d.length))?a:n,a=n=0;n<r;n++)if(s[n]!=d[n]){o=s[n]<d[n];break}if(o&&(i=s,s=d,d=i,t.s=-t.s),(n=(r=d.length)-(e=s.length))>0)for(;n--;s[e++]=0);for(n=l-1;r>a;){if(s[--r]<d[r]){for(e=r;e&&!s[--e];s[e]=n);--s[e],s[r]+=l}s[r]-=d[r]}for(;0==s[0];s.splice(0,1),--f);return s[0]?$(t,s,f):(t.s=3==S?-1:1,t.c=[t.e=0],t)},T.modulo=T.mod=function(t,n){var r,i,o=this;return t=new Y(t,n),!o.c||!t.s||t.c&&!t.c[0]?new Y(NaN):!t.c||o.c&&!o.c[0]?new Y(o):(9==L?(i=t.s,t.s=1,r=e(o,t,0,3),t.s=i,r.s*=i):r=e(o,t,0,L),(t=o.minus(r.times(t))).c[0]||1!=L||(t.s=o.s),t)},T.multipliedBy=T.times=function(t,n){var e,r,i,o,u,a,c,f,s,d,p,g,m,b,_,x=this,w=x.c,A=(t=new Y(t,n)).c;if(!(w&&A&&w[0]&&A[0]))return!x.s||!t.s||w&&!w[0]&&!A||A&&!A[0]&&!w?t.c=t.e=t.s=null:(t.s*=x.s,w&&A?(t.c=[0],t.e=0):t.c=t.e=null),t;for(r=y(x.e/h)+y(t.e/h),t.s*=x.s,(c=w.length)<(d=A.length)&&(m=w,w=A,A=m,i=c,c=d,d=i),i=c+d,m=[];i--;m.push(0));for(b=l,_=v,i=d;--i>=0;){for(e=0,p=A[i]%_,g=A[i]/_|0,o=i+(u=c);o>i;)e=((f=p*(f=w[--u]%_)+(a=g*f+(s=w[u]/_|0)*p)%_*_+m[o]+e)/b|0)+(a/_|0)+g*s,m[o--]=f%b;m[o]=e}return e?++r:m.splice(0,1),$(t,m,r)},T.negated=function(){var t=new Y(this);return t.s=-t.s||null,t},T.plus=function(t,n){var e,r=this,i=r.s;if(n=(t=new Y(t,n)).s,!i||!n)return new Y(NaN);if(i!=n)return t.s=-n,r.minus(t);var o=r.e/h,u=t.e/h,a=r.c,c=t.c;if(!o||!u){if(!a||!c)return new Y(i/0);if(!a[0]||!c[0])return c[0]?t:new Y(a[0]?r:0*i)}if(o=y(o),u=y(u),a=a.slice(),i=o-u){for(i>0?(u=o,e=c):(i=-i,e=a),e.reverse();i--;e.push(0));e.reverse()}for((i=a.length)-(n=c.length)<0&&(e=c,c=a,a=e,n=i),i=0;n;)i=(a[--n]=a[n]+c[n]+i)/l|0,a[n]=l===a[n]?0:a[n]%l;return i&&(a=[i].concat(a),++u),$(t,a,u)},T.precision=T.sd=function(t,n){var e,r,i,o=this;if(null!=t&&t!==!!t)return _(t,1,g),null==n?n=S:_(n,0,8),V(new Y(o),t,n);if(!(e=o.c))return null;if(r=(i=e.length-1)*h+1,i=e[i]){for(;i%10==0;i/=10,r--);for(i=e[0];i>=10;i/=10,r++);}return t&&o.e+1>r&&(r=o.e+1),r},T.shiftedBy=function(t){return _(t,-d,d),this.times("1e"+t)},T.squareRoot=T.sqrt=function(){var t,n,r,i,o,u=this,a=u.c,c=u.s,f=u.e,s=C+4,l=new Y("0.5");if(1!==c||!a||!a[0])return new Y(!c||c<0&&(!a||a[0])?NaN:a?u:1/0);if(0==(c=Math.sqrt(+G(u)))||c==1/0?(((n=m(a)).length+f)%2==0&&(n+="0"),c=Math.sqrt(+n),f=y((f+1)/2)-(f<0||f%2),r=new Y(n=c==1/0?"1e"+f:(n=c.toExponential()).slice(0,n.indexOf("e")+1)+f)):r=new Y(c+""),r.c[0])for((c=(f=r.e)+s)<3&&(c=0);;)if(o=r,r=l.times(o.plus(e(u,o,s,1))),m(o.c).slice(0,c)===(n=m(r.c)).slice(0,c)){if(r.e<f&&--c,"9999"!=(n=n.slice(c-3,c+1))&&(i||"4999"!=n)){+n&&(+n.slice(1)||"5"!=n.charAt(0))||(V(r,r.e+C+2,1),t=!r.times(r).eq(u));break}if(!i&&(V(o,o.e+C+2,0),o.times(o).eq(u))){r=o;break}s+=4,c+=4,i=1}return V(r,r.e+C+1,S,t)},T.toExponential=function(t,n){return null!=t&&(_(t,0,g),t++),q(this,t,n,1)},T.toFixed=function(t,n){return null!=t&&(_(t,0,g),t=t+this.e+1),q(this,t,n)},T.toFormat=function(t,n,e){var r,i=this;if(null==e)null!=t&&n&&"object"==typeof n?(e=n,n=null):t&&"object"==typeof t?(e=t,t=n=null):e=U;else if("object"!=typeof e)throw Error(f+"Argument not an object: "+e);if(r=i.toFixed(t,n),i.c){var o,u=r.split("."),a=+e.groupSize,c=+e.secondaryGroupSize,s=e.groupSeparator||"",l=u[0],h=u[1],d=i.s<0,p=d?l.slice(1):l,v=p.length;if(c&&(o=a,a=c,c=o,v-=o),a>0&&v>0){for(o=v%a||a,l=p.substr(0,o);o<v;o+=a)l+=s+p.substr(o,a);c>0&&(l+=s+p.slice(o)),d&&(l="-"+l)}r=h?l+(e.decimalSeparator||"")+((c=+e.fractionGroupSize)?h.replace(new RegExp("\\d{"+c+"}\\B","g"),"$&"+(e.fractionGroupSeparator||"")):h):l}return(e.prefix||"")+r+(e.suffix||"")},T.toFraction=function(t){var n,r,i,o,u,a,c,s,l,d,v,g,y=this,b=y.c;if(null!=t&&(!(c=new Y(t)).isInteger()&&(c.c||1!==c.s)||c.lt(N)))throw Error(f+"Argument "+(c.isInteger()?"out of range: ":"not an integer: ")+G(c));if(!b)return new Y(y);for(n=new Y(N),l=r=new Y(N),i=s=new Y(N),g=m(b),u=n.e=g.length-y.e-1,n.c[0]=p[(a=u%h)<0?h+a:a],t=!t||c.comparedTo(n)>0?u>0?n:l:c,a=j,j=1/0,c=new Y(g),s.c[0]=0;d=e(c,n,0,1),1!=(o=r.plus(d.times(i))).comparedTo(t);)r=i,i=o,l=s.plus(d.times(o=l)),s=o,n=c.minus(d.times(o=n)),c=o;return o=e(t.minus(r),i,0,1),s=s.plus(o.times(l)),r=r.plus(o.times(i)),s.s=l.s=y.s,v=e(l,i,u*=2,S).minus(y).abs().comparedTo(e(s,r,u,S).minus(y).abs())<1?[l,i]:[s,r],j=a,v},T.toNumber=function(){return+G(this)},T.toPrecision=function(t,n){return null!=t&&_(t,1,g),q(this,t,n,2)},T.toString=function(t){var n,e=this,i=e.s,o=e.e;return null===o?i?(n="Infinity",i<0&&(n="-"+n)):n="NaN":(null==t?n=o<=O||o>=R?w(m(e.c),o):A(m(e.c),o,"0"):10===t?n=A(m((e=V(new Y(e),C+o+1,S)).c),e.e,"0"):(_(t,2,z.length,"Base"),n=r(A(m(e.c),o,"0"),10,t,i,!0)),i<0&&e.c[0]&&(n="-"+n)),n},T.valueOf=T.toJSON=function(){return G(this)},T._isBigNumber=!0,null!=n&&Y.set(n),Y}()).default=o.BigNumber=o,void 0===(r=function(){return o}.call(n,e,n,t))||(t.exports=r)}()},17:function(t,n,e){"use strict";e.d(n,"b",function(){return i}),e.d(n,"c",function(){return o}),e.d(n,"a",function(){return u});var r=e(0);
/**
 * Hexadecimal functions.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2016
 * @license Apache-2.0
 */function i(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:" ",e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2;if(!t)return"";t instanceof ArrayBuffer&&(t=new Uint8Array(t));for(var r="",i=0;i<t.length;i++)r+=t[i].toString(16).padStart(e,"0")+n;return"0x"===n&&(r="0x"+r),"\\x"===n&&(r="\\x"+r),n.length?r.slice(0,-n.length):r}function o(t){if(!t)return"";t instanceof ArrayBuffer&&(t=new Uint8Array(t));for(var n=[],e=0;e<t.length;e++)n.push((t[e]>>>4).toString(16)),n.push((15&t[e]).toString(16));return n.join("")}function u(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Auto",e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2;if("None"!==n){var i="Auto"===n?/[^a-f\d]/gi:r.b.regexRep(n);t=t.replace(i,"")}for(var o=[],u=0;u<t.length;u+=e)o.push(parseInt(t.substr(u,e),16));return o}["Auto"].concat(["Space","Percent","Comma","Semi-colon","Colon","Line feed","CRLF","0x","\\x","None"])},179:function(t,n,e){"use strict";e.r(n);function r(){}function i(t,n){var e=new r;if(t instanceof r)t.each(function(t,n){e.set(n,t)});else if(Array.isArray(t)){var i,o=-1,u=t.length;if(null==n)for(;++o<u;)e.set(o,t[o]);else for(;++o<u;)e.set(n(i=t[o],o,t),i)}else if(t)for(var a in t)e.set(a,t[a]);return e}r.prototype=i.prototype={constructor:r,has:function(t){return"$"+t in this},get:function(t){return this["$"+t]},set:function(t,n){return this["$"+t]=n,this},remove:function(t){var n="$"+t;return n in this&&delete this[n]},clear:function(){for(var t in this)"$"===t[0]&&delete this[t]},keys:function(){var t=[];for(var n in this)"$"===n[0]&&t.push(n.slice(1));return t},values:function(){var t=[];for(var n in this)"$"===n[0]&&t.push(this[n]);return t},entries:function(){var t=[];for(var n in this)"$"===n[0]&&t.push({key:n.slice(1),value:this[n]});return t},size:function(){var t=0;for(var n in this)"$"===n[0]&&++t;return t},empty:function(){for(var t in this)if("$"===t[0])return!1;return!0},each:function(t){for(var n in this)"$"===n[0]&&t(this[n],n.slice(1),this)}};var o=i,u=function(){var t,n,e,r=[],i=[];function u(e,i,a,c){if(i>=r.length)return null!=t&&e.sort(t),null!=n?n(e):e;for(var f,s,l,h=-1,d=e.length,p=r[i++],v=o(),g=a();++h<d;)(l=v.get(f=p(s=e[h])+""))?l.push(s):v.set(f,[s]);return v.each(function(t,n){c(g,n,u(t,i,a,c))}),g}return e={object:function(t){return u(t,0,a,c)},map:function(t){return u(t,0,f,s)},entries:function(t){return function t(e,o){if(++o>r.length)return e;var u,a=i[o-1];return null!=n&&o>=r.length?u=e.entries():(u=[],e.each(function(n,e){u.push({key:e,values:t(n,o)})})),null!=a?u.sort(function(t,n){return a(t.key,n.key)}):u}(u(t,0,f,s),0)},key:function(t){return r.push(t),e},sortKeys:function(t){return i[r.length-1]=t,e},sortValues:function(n){return t=n,e},rollup:function(t){return n=t,e}}};function a(){return{}}function c(t,n,e){t[n]=e}function f(){return o()}function s(t,n,e){t.set(n,e)}function l(){}var h=o.prototype;function d(t,n){var e=new l;if(t instanceof l)t.each(function(t){e.add(t)});else if(t){var r=-1,i=t.length;if(null==n)for(;++r<i;)e.add(t[r]);else for(;++r<i;)e.add(n(t[r],r,t))}return e}l.prototype=d.prototype={constructor:l,has:h.has,add:function(t){return this["$"+(t+="")]=t,this},remove:h.remove,clear:h.clear,values:h.keys,size:h.size,empty:h.empty,each:h.each};var p=d,v=function(t){var n=[];for(var e in t)n.push(e);return n},g=function(t){var n=[];for(var e in t)n.push(t[e]);return n},y=function(t){var n=[];for(var e in t)n.push({key:e,value:t[e]});return n};e.d(n,"nest",function(){return u}),e.d(n,"set",function(){return p}),e.d(n,"map",function(){return o}),e.d(n,"keys",function(){return v}),e.d(n,"values",function(){return g}),e.d(n,"entries",function(){return y})},18:function(t,n,e){"use strict";var r=e(13),i=e.n(r),o=e(10),u={Images:[{name:"Joint Photographic Experts Group image",extension:"jpg,jpeg,jpe,thm,mpo",mime:"image/jpeg",description:"",signature:{0:255,1:216,2:255,3:[192,196,219,221,224,225,226,227,228,229,231,232,234,235,236,237,238,254]},extractor:function(t,n){var e=new o.a(t.slice(n));for(;e.hasMore();){var r=e.getBytes(2);if(255!==r[0])throw new Error(`Invalid marker while parsing JPEG at pos ${e.position}: ${r}`);var i=0;switch(r[1]){case 216:case 1:break;case 217:return e.carve();case 192:case 193:case 194:case 195:case 196:case 197:case 198:case 199:case 200:case 201:case 202:case 203:case 204:case 205:case 206:case 207:case 219:case 222:case 224:case 225:case 226:case 227:case 228:case 229:case 230:case 231:case 232:case 233:case 234:case 235:case 236:case 237:case 238:case 239:case 254:i=e.readInt(2,"be"),e.position+=i-2;break;case 223:e.position++;break;case 220:case 221:e.position+=2;break;case 218:i=e.readInt(2,"be"),e.position+=i-2,e.continueUntil(255);break;case 0:case 208:case 209:case 210:case 211:case 212:case 213:case 214:case 215:default:e.continueUntil(255)}}throw new Error("Unable to parse JPEG successfully")}},{name:"Graphics Interchange Format image",extension:"gif",mime:"image/gif",description:"",signature:{0:71,1:73,2:70,3:56,4:[55,57],5:97},extractor:null},{name:"Portable Network Graphics image",extension:"png",mime:"image/png",description:"",signature:{0:137,1:80,2:78,3:71,4:13,5:10,6:26,7:10},extractor:function(t,n){var e=new o.a(t.slice(n));e.moveForwardsBy(8);var r=0,i="";for(;"IEND"!==i;)r=e.readInt(4,"be"),i=e.readString(4),e.moveForwardsBy(r+4);return e.carve()}},{name:"WEBP Image",extension:"webp",mime:"image/webp",description:"",signature:{8:87,9:69,10:66,11:80},extractor:null},{name:"Camera Image File Format",extension:"crw",mime:"image/x-canon-crw",description:"",signature:{6:72,7:69,8:65,9:80,10:67,11:67,12:68,13:82},extractor:null},{name:"Canon CR2 raw image",extension:"cr2",mime:"image/x-canon-cr2",description:"",signature:[{0:73,1:73,2:42,3:0,8:67,9:82},{0:77,1:77,2:0,3:42,8:67,9:82}],extractor:null},{name:"Tagged Image File Format image",extension:"tif",mime:"image/tiff",description:"",signature:[{0:73,1:73,2:42,3:0},{0:77,1:77,2:0,3:42}],extractor:null},{name:"Bitmap image",extension:"bmp",mime:"image/bmp",description:"",signature:{0:66,1:77,7:0,9:0,14:[12,40,56,64,108,124],15:0,16:0,17:0},extractor:function(t,n){var e=new o.a(t.slice(n));e.moveForwardsBy(2);var r=e.readInt(4,"le");return e.moveForwardsBy(r-6),e.carve()}},{name:"JPEG Extended Range image",extension:"jxr",mime:"image/vnd.ms-photo",description:"",signature:{0:73,1:73,2:188},extractor:null},{name:"Photoshop image",extension:"psd",mime:"image/vnd.adobe.photoshop",description:"",signature:{0:56,1:66,2:80,3:83,4:0,5:1,6:0,7:0,8:0,9:0,10:0,11:0},extractor:null},{name:"Photoshop Large Document",extension:"psb",mime:"application/x-photoshop",description:"",signature:{0:56,1:66,2:80,3:83,4:0,5:2,6:0,7:0,8:0,9:0,10:0,11:0,12:0},extractor:null},{name:"Paint Shop Pro image",extension:"psp",mime:"image/psp",description:"",signature:[{0:80,1:97,2:105,3:110,4:116,5:32,6:83,7:104,8:111,9:112,10:32,11:80,12:114,13:111,14:32,15:73,16:109},{0:126,1:66,2:75,3:0}],extractor:null},{name:"Icon image",extension:"ico",mime:"image/x-icon",description:"",signature:{0:0,1:0,2:1,3:0,4:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],5:0,6:[16,32,48,64,128],7:[16,32,48,64,128],9:0,10:[0,1]},extractor:null},{name:"Radiance High Dynamic Range image",extension:"hdr",mime:"image/vnd.radiance",description:"",signature:{0:35,1:63,2:82,3:65,4:68,5:73,6:65,7:78,8:67,9:69,10:10},extractor:null},{name:"Sony ARW image",extension:"arw",mime:"image/x-raw",description:"",signature:{0:5,1:0,2:0,3:0,4:65,5:87,6:49,7:46},extractor:null},{name:"Fujifilm Raw Image",extension:"raf",mime:"image/x-raw",description:"",signature:{0:70,1:85,2:74,3:73,4:70,5:73,6:76,7:77,8:67,9:67,10:68,11:45,12:82,13:65,14:87},extractor:null},{name:"Minolta RAW image",extension:"mrw",mime:"image/x-raw",description:"",signature:{0:0,1:77,2:82,3:77},extractor:null},{name:"Adobe Bridge Thumbnail Cache",extension:"bct",mime:"application/octet-stream",description:"",signature:{0:108,1:110,2:98,3:116,4:2,5:0,6:0,7:0},extractor:null},{name:"Microsoft Document Imaging",extension:"mdi",mime:"image/vnd.ms-modi",description:"",signature:{0:69,1:80,2:42,3:0},extractor:null}],Video:[{name:"Matroska Multimedia Container",extension:"mkv",mime:"video/x-matroska",description:"",signature:{31:109,32:97,33:116,34:114,35:111,36:115,37:107,38:97},extractor:null},{name:"WEBM video",extension:"webm",mime:"video/webm",description:"",signature:{0:26,1:69,2:223,3:163},extractor:null},{name:"MPEG-4 video",extension:"mp4",mime:"video/mp4",description:"",signature:[{0:0,1:0,2:0,3:[24,32],4:102,5:116,6:121,7:112},{0:51,1:103,2:112,3:53},{0:0,1:0,2:0,3:28,4:102,5:116,6:121,7:112,8:109,9:112,10:52,11:50,16:109,17:112,18:52,19:49,20:109,21:112,22:52,23:50,24:105,25:115,26:111,27:109}],extractor:null},{name:"M4V video",extension:"m4v",mime:"video/x-m4v",description:"",signature:{0:0,1:0,2:0,3:28,4:102,5:116,6:121,7:112,8:77,9:52,10:86},extractor:null},{name:"Quicktime video",extension:"mov",mime:"video/quicktime",description:"",signature:{0:0,1:0,2:0,3:20,4:102,5:116,6:121,7:112},extractor:null},{name:"Audio Video Interleave",extension:"avi",mime:"video/x-msvideo",description:"",signature:{0:82,1:73,2:70,3:70,8:65,9:86,10:73},extractor:null},{name:"Windows Media Video",extension:"wmv",mime:"video/x-ms-wmv",description:"",signature:{0:48,1:38,2:178,3:117,4:142,5:102,6:207,7:17,8:166,9:217},extractor:null},{name:"MPEG video",extension:"mpg",mime:"video/mpeg",description:"",signature:{0:0,1:0,2:1,3:186},extractor:null},{name:"Flash Video",extension:"flv",mime:"video/x-flv",description:"",signature:{0:70,1:76,2:86,3:1},extractor:function(t,n){var e=new o.a(t.slice(n));e.moveForwardsBy(5);var r=e.readInt(4,"be");e.moveForwardsBy(r-9);var i=-11;for(;e.hasMore();){var u=e.readInt(4,"be"),a=e.readInt(1);if([8,9,18].indexOf(a)<0){e.moveBackwardsBy(1);break}if(u!==i+11){e.moveBackwardsBy(i+11+5);break}i=e.readInt(3,"be"),e.moveForwardsBy(7+i)}return e.carve()}}],Audio:[{name:"Waveform Audio",extension:"wav",mime:"audio/x-wav",description:"",signature:{0:82,1:73,2:70,3:70,8:87,9:65,10:86,11:69},extractor:null},{name:"OGG audio",extension:"ogg",mime:"audio/ogg",description:"",signature:{0:79,1:103,2:103,3:83},extractor:null},{name:"Musical Instrument Digital Interface audio",extension:"midi",mime:"audio/midi",description:"",signature:{0:77,1:84,2:104,3:100},extractor:null},{name:"MPEG-3 audio",extension:"mp3",mime:"audio/mpeg",description:"",signature:[{0:73,1:68,2:51},{0:255,1:251}],extractor:null},{name:"MPEG-4 Part 14 audio",extension:"m4a",mime:"audio/m4a",description:"",signature:[{4:102,5:116,6:121,7:112,8:77,9:52,10:65},{0:77,1:52,2:65,3:32}],extractor:null},{name:"Free Lossless Audio Codec",extension:"flac",mime:"audio/x-flac",description:"",signature:{0:102,1:76,2:97,3:67},extractor:null},{name:"Adaptive Multi-Rate audio codec",extension:"amr",mime:"audio/amr",description:"",signature:{0:35,1:33,2:65,3:77,4:82,5:10},extractor:null},{name:"Audacity",extension:"au",mime:"audio/x-au",description:"",signature:{0:100,1:110,2:115,3:46,24:65,25:117,26:100,27:97,28:99,29:105,30:116,31:121,32:66,33:108,34:111,35:99,36:107,37:70,38:105,39:108,40:101},extractor:null},{name:"Audacity Block",extension:"auf",mime:"application/octet-stream",description:"",signature:{0:65,1:117,2:100,3:97,4:99,5:105,6:116,7:121,8:66,9:108,10:111,11:99,12:107,13:70,14:105,15:108,16:101},extractor:null},{name:"Audio Interchange File",extension:"aif",mime:"audio/x-aiff",description:"",signature:{0:70,1:79,2:82,3:77,8:65,9:73,10:70,11:70},extractor:null},{name:"Audio Interchange File (compressed)",extension:"aifc",mime:"audio/x-aifc",description:"",signature:{0:70,1:79,2:82,3:77,8:65,9:73,10:70,11:67},extractor:null}],Documents:[{name:"Portable Document Format",extension:"pdf",mime:"application/pdf",description:"",signature:{0:37,1:80,2:68,3:70},extractor:function(t,n){var e=new o.a(t.slice(n));return e.continueUntil([37,37,69,79,70]),e.moveForwardsBy(5),e.consumeIf(13),e.consumeIf(10),e.carve()}},{name:"PostScript",extension:"ps",mime:"application/postscript",description:"",signature:{0:37,1:33},extractor:null},{name:"Rich Text Format",extension:"rtf",mime:"application/rtf",description:"",signature:{0:123,1:92,2:114,3:116,4:102},extractor:function(t,n){var e=new o.a(t.slice(n)),r=0;if(123!==e.readInt(1))throw new Error("Not a valid RTF file");r++;for(;r>0&&e.hasMore();)switch(e.readInt(1)){case 123:r++;break;case 125:r--;break;case 92:e.consumeIf(92),e.position++}return e.carve()}},{name:"Microsoft Office documents/OLE2",extension:"ole2,doc,xls,dot,ppt,xla,ppa,pps,pot,msi,sdw,db,vsd,msg",mime:"application/msword,application/vnd.ms-excel,application/vnd.ms-powerpoint",description:"Microsoft Office documents",signature:{0:208,1:207,2:17,3:224,4:161,5:177,6:26,7:225},extractor:null},{name:"Microsoft Office 2007+ documents",extension:"docx,xlsx,pptx",mime:"application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.openxmlformats-officedocument.presentationml.presentation",description:"",signature:{38:95,39:84,40:121,41:112,42:101,43:115,44:93,45:46,46:120,47:109,48:108},extractor:a},{name:"EPUB e-book",extension:"epub",mime:"application/epub+zip",description:"",signature:{0:80,1:75,2:3,3:4,30:109,31:105,32:109,33:101,34:116,35:121,36:112,37:101,38:97,39:112,40:112,41:108,42:105,43:99,44:97,45:116,46:105,47:111,48:110,49:47,50:101,51:112,52:117,53:98,54:43,55:122,56:105,57:112},extractor:a}],Applications:[{name:"Windows Portable Executable",extension:"exe,dll,drv,vxd,sys,ocx,vbx,com,fon,scr",mime:"application/x-msdownload",description:"",signature:{0:77,1:90,3:[0,1,2],5:[0,1,2]},extractor:function(t,n){var e=new o.a(t.slice(n));e.moveTo(60);var r=e.readInt(4,"le");e.moveTo(r),e.moveForwardsBy(6);var i=e.readInt(2,"le");e.moveForwardsBy(12);var u=e.readInt(2,"le");e.moveForwardsBy(2+u),e.moveForwardsBy(40*(i-1)),e.moveForwardsBy(16);var a=e.readInt(4,"le"),c=e.readInt(4,"le");return e.moveTo(c+a),e.carve()}},{name:"Executable and Linkable Format file",extension:"elf,bin,axf,o,prx,so",mime:"application/x-executable",description:"Executable and Linkable Format file. No standard file extension.",signature:{0:127,1:69,2:76,3:70},extractor:function(t,n){var e=new o.a(t.slice(n));e.moveForwardsBy(4);var r=1===e.readInt(1),i=1===e.readInt(1)?"le":"be";e.moveForwardsBy(r?26:34);var u=r?e.readInt(4,i):e.readInt(8,i);e.moveForwardsBy(10);var a=e.readInt(2,i),c=e.readInt(2,i);return e.moveTo(u),e.moveForwardsBy(a*c),e.carve()}},{name:"Adobe Flash",extension:"swf",mime:"application/x-shockwave-flash",description:"",signature:{0:[67,70],1:87,2:83},extractor:null},{name:"Java Class",extension:"class",mime:"application/java-vm",description:"",signature:{0:202,1:254,2:186,3:190},extractor:null},{name:"Dalvik Executable",extension:"dex",mime:"application/octet-stream",description:"Dalvik Executable as used by Android",signature:{0:100,1:101,2:120,3:10,4:48,5:51,6:53,7:0},extractor:null},{name:"Google Chrome Extension",extension:"crx",mime:"application/crx",description:"Google Chrome extension or packaged app",signature:{0:67,1:114,2:50,3:52},extractor:null}],Archives:[{name:"PKZIP archive",extension:"zip",mime:"application/zip",description:"",signature:{0:80,1:75,2:[3,5,7],3:[4,6,8]},extractor:a},{name:"TAR archive",extension:"tar",mime:"application/x-tar",description:"",signature:{257:117,258:115,259:116,260:97,261:114},extractor:null},{name:"Roshal Archive",extension:"rar",mime:"application/x-rar-compressed",description:"",signature:{0:82,1:97,2:114,3:33,4:26,5:7,6:[0,1]},extractor:null},{name:"Gzip",extension:"gz",mime:"application/gzip",description:"",signature:{0:31,1:139,2:8},extractor:function(t,n){var e=new o.a(t.slice(n));e.moveForwardsBy(3);var r=e.readInt(1);if(e.moveForwardsBy(4),e.readInt(1),e.moveForwardsBy(1),4&r){var i=e.readInt(2,"le");e.moveForwardsby(i)}8&r&&(e.continueUntil(0),e.moveForwardsBy(1));16&r&&(e.continueUntil(0),e.moveForwardsBy(1));2&r&&e.moveForwardsBy(2);return d(e),e.moveForwardsBy(8),e.carve()}},{name:"Bzip2",extension:"bz2",mime:"application/x-bzip2",description:"",signature:{0:66,1:90,2:104},extractor:null},{name:"7zip",extension:"7z",mime:"application/x-7z-compressed",description:"",signature:{0:55,1:122,2:188,3:175,4:39,5:28},extractor:null},{name:"Zlib Deflate",extension:"zlib",mime:"application/x-deflate",description:"",signature:{0:120,1:[1,156,218,94]},extractor:function(t,n){var e=new o.a(t.slice(n));e.moveForwardsBy(1),32&e.readInt(1)&&e.moveForwardsBy(4);return d(e),e.moveForwardsBy(4),e.carve()}},{name:"xz compression",extension:"xz",mime:"application/x-xz",description:"",signature:{0:253,1:55,2:122,3:88,4:90,5:0},extractor:null},{name:"Tarball",extension:"tar.z",mime:"application/x-gtar",description:"",signature:{0:31,1:[157,160]},extractor:null},{name:"ISO disk image",extension:"iso",mime:"application/octet-stream",description:"ISO 9660 CD/DVD image file",signature:[{32769:67,32770:68,32771:48,32772:48,32773:49},{34817:67,34818:68,34819:48,34820:48,34821:49},{36865:67,36866:68,36867:48,36868:48,36869:49}],extractor:null},{name:"Virtual Machine Disk",extension:"vmdk",mime:"application/vmdk,application/x-virtualbox-vmdk",description:"",signature:{0:75,1:68,2:77},extractor:null},{name:"ARJ Archive",extension:"arj",mime:"application/x-arj-compressed",description:"",signature:{0:96,1:234,8:[0,16,20],9:0,10:2},extractor:null},{name:"WinAce Archive",extension:"ace",mime:"application/x-ace-compressed",description:"",signature:{7:42,8:42,9:65,10:67,11:69,12:42,13:42},extractor:null},{name:"Macintosh BinHex Encoded File",extension:"hqx",mime:"application/mac-binhex",description:"",signature:{11:109,12:117,13:115,14:116,15:32,16:98,17:101,18:32,19:99,20:111,21:110,22:118,23:101,24:114,25:116,26:101,27:100,28:32,29:119,30:105,31:116,32:104,33:32,34:66,35:105,36:110,37:72,38:101,39:120},extractor:null},{name:"ALZip Archive",extension:"alz",mime:"application/octet-stream",description:"",signature:{0:65,1:76,2:90,3:1,4:10,5:0,6:0,7:0},extractor:null},{name:"KGB Compressed Archive",extension:"kgb",mime:"application/x-kgb-compressed",description:"",signature:{0:75,1:71,2:66,3:95,4:97,5:114,6:99,7:104,8:32,9:45},extractor:null}],Miscellaneous:[{name:"UTF-8 text file",extension:"txt",mime:"text/plain",description:"UTF-8 encoded Unicode byte order mark, commonly but not exclusively seen in text files.",signature:{0:239,1:187,2:191},extractor:null},{name:"UTF-32 LE file",extension:"utf32le",mime:"charset/utf32le",description:"Little-endian UTF-32 encoded Unicode byte order mark.",signature:{0:255,1:254,2:0,3:0},extractor:null},{name:"UTF-16 LE file",extension:"utf16le",mime:"charset/utf16le",description:"Little-endian UTF-16 encoded Unicode byte order mark.",signature:{0:255,1:254},extractor:null},{name:"Web Open Font Format",extension:"woff",mime:"application/font-woff",description:"",signature:{0:119,1:79,2:70,3:70,4:0,5:1,6:0,7:0},extractor:null},{name:"Web Open Font Format 2",extension:"woff2",mime:"application/font-woff",description:"",signature:{0:119,1:79,2:70,3:50,4:0,5:1,6:0,7:0},extractor:null},{name:"Embedded OpenType font",extension:"eot",mime:"application/octet-stream",description:"",signature:[{8:2,9:0,10:1,34:76,35:80},{8:1,9:0,10:0,34:76,35:80},{8:2,9:0,10:2,34:76,35:80}],extractor:null},{name:"TrueType Font",extension:"ttf",mime:"application/font-sfnt",description:"",signature:{0:0,1:1,2:0,3:0,4:0},extractor:null},{name:"OpenType Font",extension:"otf",mime:"application/font-sfnt",description:"",signature:{0:79,1:84,2:84,3:79,4:0},extractor:null},{name:"SQLite",extension:"sqlite",mime:"application/x-sqlite3",description:"",signature:{0:83,1:81,2:76,3:105},extractor:null},{name:"BitTorrent link",extension:"torrent",mime:"application/x-bittorrent",description:"",signature:[{0:100,1:56,2:58,3:97,4:110,5:110,6:111,7:117,8:110,9:99,10:101,11:35,12:35,13:58},{0:100,1:52,2:58,3:105,4:110,5:102,6:111,7:100,8:[52,53,54],9:58}],extractor:null}]};function a(t,n){var e=new o.a(t.slice(n));e.continueUntil([80,75,5,6]),e.moveForwardsBy(20);var r=e.readInt(2,"le");return e.moveForwardsBy(r),e.carve()}for(var c=new Array(288),f=0;f<c.length;f++)c[f]=f<=143?8:f<=255?9:f<=279?7:8;var s=y(c),l=y(new Array(30).fill(5)),h=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];function d(t){for(var n=0;!n;){n=t.readBits(1);var e=t.readBits(2);if(0===e){t.moveForwardsBy(1);var r=t.readInt(2,"le");t.moveForwardsBy(2+r)}else if(1===e)g(t,s,l);else{if(2!==e)throw new Error(`Invalid block type while parsing DEFLATE stream at pos ${t.position}`);for(var i=t.readBits(5)+257,o=t.readBits(5)+1,u=t.readBits(4)+4,a=new Uint8Array(h.length),c=0;c<u;c++)a[h[c]]=t.readBits(3);for(var f=y(a),d=new Uint8Array(i+o),p=void 0,v=void 0,b=void 0,_=0;_<i+o;)switch(p=m(t,f)){case 16:for(v=3+t.readBits(2);v--;)d[_++]=b;break;case 17:for(v=3+t.readBits(3);v--;)d[_++]=0;b=0;break;case 18:for(v=11+t.readBits(7);v--;)d[_++]=0;b=0;break;default:d[_++]=p,b=p}g(t,y(d.subarray(0,i)),y(d.subarray(i)))}}t.bitPos>0&&t.moveForwardsBy(1)}var p=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0],v=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13];function g(t,n,e){for(var r,i=0;(r=m(t,n))&&256!==r;){if(++i>1e4)throw new Error("Caught in probable infinite loop while parsing Huffman Block");r<256||(t.readBits(p[r-257]),r=m(t,e),t.readBits(v[r]))}}function y(t){for(var n=Math.max.apply(Math,t),e=Math.min.apply(Math,t),r=1<<n,i=new Uint32Array(r),o=1,u=0,a=2;o<=n;){for(var c=0;c<t.length;c++)if(t[c]===o){var f=void 0,s=void 0,l=void 0;for(f=0,s=u,l=0;l<o;l++)f=f<<1|1&s,s>>=1;for(var h=o<<16|c,d=f;d<r;d+=a)i[d]=h;u++}o++,u<<=1,a<<=1}return[i,n,e]}function m(t,n){var e=i()(n,2),r=e[0],o=e[1],u=r[t.readBits(o)&(1<<o)-1],a=u>>>16;if(a>o)throw new Error(`Invalid Huffman Code length while parsing DEFLATE block at pos ${t.position}: ${a}`);return t.moveBackwardsByBits(o-a),65535&u}e(0);
/**
 * File type functions
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 *
 */
function b(t,n){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(t.length){for(var r=0;r<t.length;r++)if(_(t[r],n,e))return!0;return!1}return _(t,n,e)}function _(t,n){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;for(var r in t){var i=parseInt(r,10)+e;switch(typeof t[r]){case"number":if(n[i]!==t[r])return!1;break;case"object":if(t[r].indexOf(n[i])<0)return!1;break;case"function":if(!t[r](n[i]))return!1;break;default:throw new Error(`Unrecognised signature type at offset ${r}`)}}return!0}function x(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Object.keys(u);if(t instanceof ArrayBuffer&&(t=new Uint8Array(t)),!(t&&t.length>1))return[];var e=[],r={};for(var i in u)n.includes(i)&&(r[i]=u[i]);for(var o in r){r[o].forEach(function(n){b(n.signature,t)&&e.push(n)})}return e}function w(t){return function(t,n){var e=x(n);if(!e||!e.length)return!1;if("string"==typeof t)return e.reduce(function(n,e){var r=!!e.mime.startsWith(t)&&e.mime;return n||r},!1);if(t instanceof RegExp)return e.reduce(function(n,e){var r=!!t.test(e.mime)&&e.mime;return n||r},!1);throw new Error("Invalid type input.")}("image",t)}e.d(n,"a",function(){return x}),e.d(n,"b",function(){return w})},180:function(t,n,e){"use strict";e.r(n);var r=e(23),i=e(126),o=e(134),u=Object(i.dispatch)("start","end","cancel","interrupt"),a=[],c=0,f=1,s=2,l=3,h=4,d=5,p=6,v=function(t,n,e,r,i,v){var g=t.__transition;if(g){if(e in g)return}else t.__transition={};!function(t,n,e){var r,i=t.__transition;function u(d){var v,g,y,m;if(e.state!==f)return c();for(v in i)if((m=i[v]).name===e.name){if(m.state===l)return Object(o.timeout)(u);m.state===h?(m.state=p,m.timer.stop(),m.on.call("interrupt",t,t.__data__,m.index,m.group),delete i[v]):+v<n&&(m.state=p,m.timer.stop(),m.on.call("cancel",t,t.__data__,m.index,m.group),delete i[v])}if(Object(o.timeout)(function(){e.state===l&&(e.state=h,e.timer.restart(a,e.delay,e.time),a(d))}),e.state=s,e.on.call("start",t,t.__data__,e.index,e.group),e.state===s){for(e.state=l,r=new Array(y=e.tween.length),v=0,g=-1;v<y;++v)(m=e.tween[v].value.call(t,t.__data__,e.index,e.group))&&(r[++g]=m);r.length=g+1}}function a(n){for(var i=n<e.duration?e.ease.call(null,n/e.duration):(e.timer.restart(c),e.state=d,1),o=-1,u=r.length;++o<u;)r[o].call(t,i);e.state===d&&(e.on.call("end",t,t.__data__,e.index,e.group),c())}function c(){for(var r in e.state=p,e.timer.stop(),delete i[n],i)return;delete t.__transition}i[n]=e,e.timer=Object(o.timer)(function(t){e.state=f,e.timer.restart(u,e.delay,e.time),e.delay<=t&&u(t-e.delay)},0,e.time)}(t,e,{name:n,index:r,group:i,on:u,tween:a,time:v.time,delay:v.delay,duration:v.duration,ease:v.ease,timer:null,state:c})};function g(t,n){var e=m(t,n);if(e.state>c)throw new Error("too late; already scheduled");return e}function y(t,n){var e=m(t,n);if(e.state>l)throw new Error("too late; already running");return e}function m(t,n){var e=t.__transition;if(!e||!(e=e[n]))throw new Error("transition not found");return e}var b=function(t,n){var e,r,i,o=t.__transition,u=!0;if(o){for(i in n=null==n?null:n+"",o)(e=o[i]).name===n?(r=e.state>s&&e.state<d,e.state=p,e.timer.stop(),e.on.call(r?"interrupt":"cancel",t,t.__data__,e.index,e.group),delete o[i]):u=!1;u&&delete t.__transition}},_=e(63);function x(t,n){var e,r;return function(){var i=y(this,t),o=i.tween;if(o!==e)for(var u=0,a=(r=e=o).length;u<a;++u)if(r[u].name===n){(r=r.slice()).splice(u,1);break}i.tween=r}}function w(t,n,e){var r,i;if("function"!=typeof e)throw new Error;return function(){var o=y(this,t),u=o.tween;if(u!==r){i=(r=u).slice();for(var a={name:n,value:e},c=0,f=i.length;c<f;++c)if(i[c].name===n){i[c]=a;break}c===f&&i.push(a)}o.tween=i}}function A(t,n,e){var r=t._id;return t.each(function(){var t=y(this,r);(t.value||(t.value={}))[n]=e.apply(this,arguments)}),function(t){return m(t,r).value[n]}}var E=e(53),M=function(t,n){var e;return("number"==typeof n?_.interpolateNumber:n instanceof E.color?_.interpolateRgb:(e=Object(E.color)(n))?(n=e,_.interpolateRgb):_.interpolateString)(t,n)};function k(t){return function(){this.removeAttribute(t)}}function B(t){return function(){this.removeAttributeNS(t.space,t.local)}}function F(t,n,e){var r,i,o=e+"";return function(){var u=this.getAttribute(t);return u===o?null:u===r?i:i=n(r=u,e)}}function T(t,n,e){var r,i,o=e+"";return function(){var u=this.getAttributeNS(t.space,t.local);return u===o?null:u===r?i:i=n(r=u,e)}}function N(t,n,e){var r,i,o;return function(){var u,a,c=e(this);if(null!=c)return(u=this.getAttribute(t))===(a=c+"")?null:u===r&&a===i?o:(i=a,o=n(r=u,c));this.removeAttribute(t)}}function C(t,n,e){var r,i,o;return function(){var u,a,c=e(this);if(null!=c)return(u=this.getAttributeNS(t.space,t.local))===(a=c+"")?null:u===r&&a===i?o:(i=a,o=n(r=u,c));this.removeAttributeNS(t.space,t.local)}}function S(t,n){var e,r;function i(){var i=n.apply(this,arguments);return i!==r&&(e=(r=i)&&function(t,n){return function(e){this.setAttributeNS(t.space,t.local,n(e))}}(t,i)),e}return i._value=n,i}function O(t,n){var e,r;function i(){var i=n.apply(this,arguments);return i!==r&&(e=(r=i)&&function(t,n){return function(e){this.setAttribute(t,n(e))}}(t,i)),e}return i._value=n,i}function R(t,n){return function(){g(this,t).delay=+n.apply(this,arguments)}}function P(t,n){return n=+n,function(){g(this,t).delay=n}}function j(t,n){return function(){y(this,t).duration=+n.apply(this,arguments)}}function D(t,n){return n=+n,function(){y(this,t).duration=n}}function L(t,n){if("function"!=typeof n)throw new Error;return function(){y(this,t).ease=n}}function I(t,n,e){var r,i,o=function(t){return(t+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||"start"===t})}(n)?g:y;return function(){var u=o(this,t),a=u.on;a!==r&&(i=(r=a).copy()).on(n,e),u.on=i}}var U=r.selection.prototype.constructor;function z(t){return function(){this.style.removeProperty(t)}}function Y(t,n,e){var r,i;function o(){var o=n.apply(this,arguments);return o!==i&&(r=(i=o)&&function(t,n,e){return function(r){this.style.setProperty(t,n(r),e)}}(t,o,e)),r}return o._value=n,o}var q=0;function H(t,n,e,r){this._groups=t,this._parents=n,this._name=e,this._id=r}function $(t){return Object(r.selection)().transition(t)}function V(){return++q}var G=r.selection.prototype;H.prototype=$.prototype={constructor:H,select:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=Object(r.selector)(t));for(var i=this._groups,o=i.length,u=new Array(o),a=0;a<o;++a)for(var c,f,s=i[a],l=s.length,h=u[a]=new Array(l),d=0;d<l;++d)(c=s[d])&&(f=t.call(c,c.__data__,d,s))&&("__data__"in c&&(f.__data__=c.__data__),h[d]=f,v(h[d],n,e,d,h,m(c,e)));return new H(u,this._parents,n,e)},selectAll:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=Object(r.selectorAll)(t));for(var i=this._groups,o=i.length,u=[],a=[],c=0;c<o;++c)for(var f,s=i[c],l=s.length,h=0;h<l;++h)if(f=s[h]){for(var d,p=t.call(f,f.__data__,h,s),g=m(f,e),y=0,b=p.length;y<b;++y)(d=p[y])&&v(d,n,e,y,p,g);u.push(p),a.push(f)}return new H(u,a,n,e)},filter:function(t){"function"!=typeof t&&(t=Object(r.matcher)(t));for(var n=this._groups,e=n.length,i=new Array(e),o=0;o<e;++o)for(var u,a=n[o],c=a.length,f=i[o]=[],s=0;s<c;++s)(u=a[s])&&t.call(u,u.__data__,s,a)&&f.push(u);return new H(i,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw new Error;for(var n=this._groups,e=t._groups,r=n.length,i=e.length,o=Math.min(r,i),u=new Array(r),a=0;a<o;++a)for(var c,f=n[a],s=e[a],l=f.length,h=u[a]=new Array(l),d=0;d<l;++d)(c=f[d]||s[d])&&(h[d]=c);for(;a<r;++a)u[a]=n[a];return new H(u,this._parents,this._name,this._id)},selection:function(){return new U(this._groups,this._parents)},transition:function(){for(var t=this._name,n=this._id,e=V(),r=this._groups,i=r.length,o=0;o<i;++o)for(var u,a=r[o],c=a.length,f=0;f<c;++f)if(u=a[f]){var s=m(u,n);v(u,t,e,f,a,{time:s.time+s.delay+s.duration,delay:0,duration:s.duration,ease:s.ease})}return new H(r,this._parents,t,e)},call:G.call,nodes:G.nodes,node:G.node,size:G.size,empty:G.empty,each:G.each,on:function(t,n){var e=this._id;return arguments.length<2?m(this.node(),e).on.on(t):this.each(I(e,t,n))},attr:function(t,n){var e=Object(r.namespace)(t),i="transform"===e?_.interpolateTransformSvg:M;return this.attrTween(t,"function"==typeof n?(e.local?C:N)(e,i,A(this,"attr."+t,n)):null==n?(e.local?B:k)(e):(e.local?T:F)(e,i,n))},attrTween:function(t,n){var e="attr."+t;if(arguments.length<2)return(e=this.tween(e))&&e._value;if(null==n)return this.tween(e,null);if("function"!=typeof n)throw new Error;var i=Object(r.namespace)(t);return this.tween(e,(i.local?S:O)(i,n))},style:function(t,n,e){var i="transform"==(t+="")?_.interpolateTransformCss:M;return null==n?this.styleTween(t,function(t,n){var e,i,o;return function(){var u=Object(r.style)(this,t),a=(this.style.removeProperty(t),Object(r.style)(this,t));return u===a?null:u===e&&a===i?o:o=n(e=u,i=a)}}(t,i)).on("end.style."+t,z(t)):"function"==typeof n?this.styleTween(t,function(t,n,e){var i,o,u;return function(){var a=Object(r.style)(this,t),c=e(this),f=c+"";return null==c&&(this.style.removeProperty(t),f=c=Object(r.style)(this,t)),a===f?null:a===i&&f===o?u:(o=f,u=n(i=a,c))}}(t,i,A(this,"style."+t,n))).each(function(t,n){var e,r,i,o,u="style."+n,a="end."+u;return function(){var c=y(this,t),f=c.on,s=null==c.value[u]?o||(o=z(n)):void 0;f===e&&i===s||(r=(e=f).copy()).on(a,i=s),c.on=r}}(this._id,t)):this.styleTween(t,function(t,n,e){var i,o,u=e+"";return function(){var a=Object(r.style)(this,t);return a===u?null:a===i?o:o=n(i=a,e)}}(t,i,n),e).on("end.style."+t,null)},styleTween:function(t,n,e){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==n)return this.tween(r,null);if("function"!=typeof n)throw new Error;return this.tween(r,Y(t,n,null==e?"":e))},text:function(t){return this.tween("text","function"==typeof t?function(t){return function(){var n=t(this);this.textContent=null==n?"":n}}(A(this,"text",t)):function(t){return function(){this.textContent=t}}(null==t?"":t+""))},remove:function(){return this.on("end.remove",(t=this._id,function(){var n=this.parentNode;for(var e in this.__transition)if(+e!==t)return;n&&n.removeChild(this)}));var t},tween:function(t,n){var e=this._id;if(t+="",arguments.length<2){for(var r,i=m(this.node(),e).tween,o=0,u=i.length;o<u;++o)if((r=i[o]).name===t)return r.value;return null}return this.each((null==n?x:w)(e,t,n))},delay:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?R:P)(n,t)):m(this.node(),n).delay},duration:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?j:D)(n,t)):m(this.node(),n).duration},ease:function(t){var n=this._id;return arguments.length?this.each(L(n,t)):m(this.node(),n).ease},end:function(){var t,n,e=this,r=e._id,i=e.size();return new Promise(function(o,u){var a={value:u},c={value:function(){0==--i&&o()}};e.each(function(){var e=y(this,r),i=e.on;i!==t&&((n=(t=i).copy())._.cancel.push(a),n._.interrupt.push(a),n._.end.push(c)),e.on=n})})}};var X={time:null,delay:0,duration:250,ease:e(426).easeCubicInOut};function W(t,n){for(var e;!(e=t.__transition)||!(e=e[n]);)if(!(t=t.parentNode))return X.time=Object(o.now)(),X;return e}r.selection.prototype.interrupt=function(t){return this.each(function(){b(this,t)})},r.selection.prototype.transition=function(t){var n,e;t instanceof H?(n=t._id,t=t._name):(n=V(),(e=X).time=Object(o.now)(),t=null==t?null:t+"");for(var r=this._groups,i=r.length,u=0;u<i;++u)for(var a,c=r[u],f=c.length,s=0;s<f;++s)(a=c[s])&&v(a,t,n,s,c,e||W(a,n));return new H(r,this._parents,t,n)};var J=[null],Z=function(t,n){var e,r,i=t.__transition;if(i)for(r in n=null==n?null:n+"",i)if((e=i[r]).state>f&&e.name===n)return new H([[t]],J,n,+r);return null};e.d(n,"transition",function(){return $}),e.d(n,"active",function(){return Z}),e.d(n,"interrupt",function(){return b})},2:function(t,n){function e(t,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}t.exports=function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}},201:function(t,n,e){"use strict";e.r(n);var r=e(126),i=e(23);function o(){i.event.stopImmediatePropagation()}var u=function(){i.event.preventDefault(),i.event.stopImmediatePropagation()},a=function(t){var n=t.document.documentElement,e=Object(i.select)(t).on("dragstart.drag",u,!0);"onselectstart"in n?e.on("selectstart.drag",u,!0):(n.__noselect=n.style.MozUserSelect,n.style.MozUserSelect="none")};function c(t,n){var e=t.document.documentElement,r=Object(i.select)(t).on("dragstart.drag",null);n&&(r.on("click.drag",u,!0),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in e?r.on("selectstart.drag",null):(e.style.MozUserSelect=e.__noselect,delete e.__noselect)}var f=function(t){return function(){return t}};function s(t,n,e,r,i,o,u,a,c,f){this.target=t,this.type=n,this.subject=e,this.identifier=r,this.active=i,this.x=o,this.y=u,this.dx=a,this.dy=c,this._=f}function l(){return!i.event.ctrlKey&&!i.event.button}function h(){return this.parentNode}function d(t){return null==t?{x:i.event.x,y:i.event.y}:t}function p(){return navigator.maxTouchPoints||"ontouchstart"in this}s.prototype.on=function(){var t=this._.on.apply(this._,arguments);return t===this._?this:t};var v=function(){var t,n,e,v,g=l,y=h,m=d,b=p,_={},x=Object(r.dispatch)("start","drag","end"),w=0,A=0;function E(t){t.on("mousedown.drag",M).filter(b).on("touchstart.drag",F).on("touchmove.drag",T).on("touchend.drag touchcancel.drag",N).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function M(){if(!v&&g.apply(this,arguments)){var r=C("mouse",y.apply(this,arguments),i.mouse,this,arguments);r&&(Object(i.select)(i.event.view).on("mousemove.drag",k,!0).on("mouseup.drag",B,!0),a(i.event.view),o(),e=!1,t=i.event.clientX,n=i.event.clientY,r("start"))}}function k(){if(u(),!e){var r=i.event.clientX-t,o=i.event.clientY-n;e=r*r+o*o>A}_.mouse("drag")}function B(){Object(i.select)(i.event.view).on("mousemove.drag mouseup.drag",null),c(i.event.view,e),u(),_.mouse("end")}function F(){if(g.apply(this,arguments)){var t,n,e=i.event.changedTouches,r=y.apply(this,arguments),u=e.length;for(t=0;t<u;++t)(n=C(e[t].identifier,r,i.touch,this,arguments))&&(o(),n("start"))}}function T(){var t,n,e=i.event.changedTouches,r=e.length;for(t=0;t<r;++t)(n=_[e[t].identifier])&&(u(),n("drag"))}function N(){var t,n,e=i.event.changedTouches,r=e.length;for(v&&clearTimeout(v),v=setTimeout(function(){v=null},500),t=0;t<r;++t)(n=_[e[t].identifier])&&(o(),n("end"))}function C(t,n,e,r,o){var u,a,c,f=e(n,t),l=x.copy();if(Object(i.customEvent)(new s(E,"beforestart",u,t,w,f[0],f[1],0,0,l),function(){return null!=(i.event.subject=u=m.apply(r,o))&&(a=u.x-f[0]||0,c=u.y-f[1]||0,!0)}))return function h(d){var p,v=f;switch(d){case"start":_[t]=h,p=w++;break;case"end":delete _[t],--w;case"drag":f=e(n,t),p=w}Object(i.customEvent)(new s(E,d,u,t,p,f[0]+a,f[1]+c,f[0]-v[0],f[1]-v[1],l),l.apply,l,[d,r,o])}}return E.filter=function(t){return arguments.length?(g="function"==typeof t?t:f(!!t),E):g},E.container=function(t){return arguments.length?(y="function"==typeof t?t:f(t),E):y},E.subject=function(t){return arguments.length?(m="function"==typeof t?t:f(t),E):m},E.touchable=function(t){return arguments.length?(b="function"==typeof t?t:f(!!t),E):b},E.on=function(){var t=x.on.apply(x,arguments);return t===x?E:t},E.clickDistance=function(t){return arguments.length?(A=(t=+t)*t,E):Math.sqrt(A)},E};e.d(n,"drag",function(){return v}),e.d(n,"dragDisable",function(){return a}),e.d(n,"dragEnable",function(){return c})},23:function(t,n,e){"use strict";e.r(n);var r="http://www.w3.org/1999/xhtml",i={svg:"http://www.w3.org/2000/svg",xhtml:r,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"},o=function(t){var n=t+="",e=n.indexOf(":");return e>=0&&"xmlns"!==(n=t.slice(0,e))&&(t=t.slice(e+1)),i.hasOwnProperty(n)?{space:i[n],local:t}:t};function u(t){return function(){var n=this.ownerDocument,e=this.namespaceURI;return e===r&&n.documentElement.namespaceURI===r?n.createElement(t):n.createElementNS(e,t)}}function a(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}var c=function(t){var n=o(t);return(n.local?a:u)(n)};function f(){}var s=function(t){return null==t?f:function(){return this.querySelector(t)}};function l(){return[]}var h=function(t){return null==t?l:function(){return this.querySelectorAll(t)}},d=function(t){return function(){return this.matches(t)}},p=function(t){return new Array(t.length)};function v(t,n){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=n}v.prototype={constructor:v,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,n){return this._parent.insertBefore(t,n)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};var g="$";function y(t,n,e,r,i,o){for(var u,a=0,c=n.length,f=o.length;a<f;++a)(u=n[a])?(u.__data__=o[a],r[a]=u):e[a]=new v(t,o[a]);for(;a<c;++a)(u=n[a])&&(i[a]=u)}function m(t,n,e,r,i,o,u){var a,c,f,s={},l=n.length,h=o.length,d=new Array(l);for(a=0;a<l;++a)(c=n[a])&&(d[a]=f=g+u.call(c,c.__data__,a,n),f in s?i[a]=c:s[f]=c);for(a=0;a<h;++a)(c=s[f=g+u.call(t,o[a],a,o)])?(r[a]=c,c.__data__=o[a],s[f]=null):e[a]=new v(t,o[a]);for(a=0;a<l;++a)(c=n[a])&&s[d[a]]===c&&(i[a]=c)}function b(t,n){return t<n?-1:t>n?1:t>=n?0:NaN}function _(t){return function(){this.removeAttribute(t)}}function x(t){return function(){this.removeAttributeNS(t.space,t.local)}}function w(t,n){return function(){this.setAttribute(t,n)}}function A(t,n){return function(){this.setAttributeNS(t.space,t.local,n)}}function E(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttribute(t):this.setAttribute(t,e)}}function M(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,e)}}var k=function(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView};function B(t){return function(){this.style.removeProperty(t)}}function F(t,n,e){return function(){this.style.setProperty(t,n,e)}}function T(t,n,e){return function(){var r=n.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,e)}}function N(t,n){return t.style.getPropertyValue(n)||k(t).getComputedStyle(t,null).getPropertyValue(n)}function C(t){return function(){delete this[t]}}function S(t,n){return function(){this[t]=n}}function O(t,n){return function(){var e=n.apply(this,arguments);null==e?delete this[t]:this[t]=e}}function R(t){return t.trim().split(/^|\s+/)}function P(t){return t.classList||new j(t)}function j(t){this._node=t,this._names=R(t.getAttribute("class")||"")}function D(t,n){for(var e=P(t),r=-1,i=n.length;++r<i;)e.add(n[r])}function L(t,n){for(var e=P(t),r=-1,i=n.length;++r<i;)e.remove(n[r])}function I(t){return function(){D(this,t)}}function U(t){return function(){L(this,t)}}function z(t,n){return function(){(n.apply(this,arguments)?D:L)(this,t)}}j.prototype={add:function(t){this._names.indexOf(t)<0&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var n=this._names.indexOf(t);n>=0&&(this._names.splice(n,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};function Y(){this.textContent=""}function q(t){return function(){this.textContent=t}}function H(t){return function(){var n=t.apply(this,arguments);this.textContent=null==n?"":n}}function $(){this.innerHTML=""}function V(t){return function(){this.innerHTML=t}}function G(t){return function(){var n=t.apply(this,arguments);this.innerHTML=null==n?"":n}}function X(){this.nextSibling&&this.parentNode.appendChild(this)}function W(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function J(){return null}function Z(){var t=this.parentNode;t&&t.removeChild(this)}function Q(){return this.parentNode.insertBefore(this.cloneNode(!1),this.nextSibling)}function K(){return this.parentNode.insertBefore(this.cloneNode(!0),this.nextSibling)}var tt={},nt=null;"undefined"!=typeof document&&("onmouseenter"in document.documentElement||(tt={mouseenter:"mouseover",mouseleave:"mouseout"}));function et(t,n,e){return t=rt(t,n,e),function(n){var e=n.relatedTarget;e&&(e===this||8&e.compareDocumentPosition(this))||t.call(this,n)}}function rt(t,n,e){return function(r){var i=nt;nt=r;try{t.call(this,this.__data__,n,e)}finally{nt=i}}}function it(t){return t.trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");return e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),{type:t,name:n}})}function ot(t){return function(){var n=this.__on;if(n){for(var e,r=0,i=-1,o=n.length;r<o;++r)e=n[r],t.type&&e.type!==t.type||e.name!==t.name?n[++i]=e:this.removeEventListener(e.type,e.listener,e.capture);++i?n.length=i:delete this.__on}}}function ut(t,n,e){var r=tt.hasOwnProperty(t.type)?et:rt;return function(i,o,u){var a,c=this.__on,f=r(n,o,u);if(c)for(var s=0,l=c.length;s<l;++s)if((a=c[s]).type===t.type&&a.name===t.name)return this.removeEventListener(a.type,a.listener,a.capture),this.addEventListener(a.type,a.listener=f,a.capture=e),void(a.value=n);this.addEventListener(t.type,f,e),a={type:t.type,name:t.name,value:n,listener:f,capture:e},c?c.push(a):this.__on=[a]}}function at(t,n,e,r){var i=nt;t.sourceEvent=nt,nt=t;try{return n.apply(e,r)}finally{nt=i}}function ct(t,n,e){var r=k(t),i=r.CustomEvent;"function"==typeof i?i=new i(n,e):(i=r.document.createEvent("Event"),e?(i.initEvent(n,e.bubbles,e.cancelable),i.detail=e.detail):i.initEvent(n,!1,!1)),t.dispatchEvent(i)}function ft(t,n){return function(){return ct(this,t,n)}}function st(t,n){return function(){return ct(this,t,n.apply(this,arguments))}}var lt=[null];function ht(t,n){this._groups=t,this._parents=n}function dt(){return new ht([[document.documentElement]],lt)}ht.prototype=dt.prototype={constructor:ht,select:function(t){"function"!=typeof t&&(t=s(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var o,u,a=n[i],c=a.length,f=r[i]=new Array(c),l=0;l<c;++l)(o=a[l])&&(u=t.call(o,o.__data__,l,a))&&("__data__"in o&&(u.__data__=o.__data__),f[l]=u);return new ht(r,this._parents)},selectAll:function(t){"function"!=typeof t&&(t=h(t));for(var n=this._groups,e=n.length,r=[],i=[],o=0;o<e;++o)for(var u,a=n[o],c=a.length,f=0;f<c;++f)(u=a[f])&&(r.push(t.call(u,u.__data__,f,a)),i.push(u));return new ht(r,i)},filter:function(t){"function"!=typeof t&&(t=d(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var o,u=n[i],a=u.length,c=r[i]=[],f=0;f<a;++f)(o=u[f])&&t.call(o,o.__data__,f,u)&&c.push(o);return new ht(r,this._parents)},data:function(t,n){if(!t)return p=new Array(this.size()),s=-1,this.each(function(t){p[++s]=t}),p;var e,r=n?m:y,i=this._parents,o=this._groups;"function"!=typeof t&&(e=t,t=function(){return e});for(var u=o.length,a=new Array(u),c=new Array(u),f=new Array(u),s=0;s<u;++s){var l=i[s],h=o[s],d=h.length,p=t.call(l,l&&l.__data__,s,i),v=p.length,g=c[s]=new Array(v),b=a[s]=new Array(v);r(l,h,g,b,f[s]=new Array(d),p,n);for(var _,x,w=0,A=0;w<v;++w)if(_=g[w]){for(w>=A&&(A=w+1);!(x=b[A])&&++A<v;);_._next=x||null}}return(a=new ht(a,i))._enter=c,a._exit=f,a},enter:function(){return new ht(this._enter||this._groups.map(p),this._parents)},exit:function(){return new ht(this._exit||this._groups.map(p),this._parents)},join:function(t,n,e){var r=this.enter(),i=this,o=this.exit();return r="function"==typeof t?t(r):r.append(t+""),null!=n&&(i=n(i)),null==e?o.remove():e(o),r&&i?r.merge(i).order():i},merge:function(t){for(var n=this._groups,e=t._groups,r=n.length,i=e.length,o=Math.min(r,i),u=new Array(r),a=0;a<o;++a)for(var c,f=n[a],s=e[a],l=f.length,h=u[a]=new Array(l),d=0;d<l;++d)(c=f[d]||s[d])&&(h[d]=c);for(;a<r;++a)u[a]=n[a];return new ht(u,this._parents)},order:function(){for(var t=this._groups,n=-1,e=t.length;++n<e;)for(var r,i=t[n],o=i.length-1,u=i[o];--o>=0;)(r=i[o])&&(u&&4^r.compareDocumentPosition(u)&&u.parentNode.insertBefore(r,u),u=r);return this},sort:function(t){function n(n,e){return n&&e?t(n.__data__,e.__data__):!n-!e}t||(t=b);for(var e=this._groups,r=e.length,i=new Array(r),o=0;o<r;++o){for(var u,a=e[o],c=a.length,f=i[o]=new Array(c),s=0;s<c;++s)(u=a[s])&&(f[s]=u);f.sort(n)}return new ht(i,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){var t=new Array(this.size()),n=-1;return this.each(function(){t[++n]=this}),t},node:function(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r=t[n],i=0,o=r.length;i<o;++i){var u=r[i];if(u)return u}return null},size:function(){var t=0;return this.each(function(){++t}),t},empty:function(){return!this.node()},each:function(t){for(var n=this._groups,e=0,r=n.length;e<r;++e)for(var i,o=n[e],u=0,a=o.length;u<a;++u)(i=o[u])&&t.call(i,i.__data__,u,o);return this},attr:function(t,n){var e=o(t);if(arguments.length<2){var r=this.node();return e.local?r.getAttributeNS(e.space,e.local):r.getAttribute(e)}return this.each((null==n?e.local?x:_:"function"==typeof n?e.local?M:E:e.local?A:w)(e,n))},style:function(t,n,e){return arguments.length>1?this.each((null==n?B:"function"==typeof n?T:F)(t,n,null==e?"":e)):N(this.node(),t)},property:function(t,n){return arguments.length>1?this.each((null==n?C:"function"==typeof n?O:S)(t,n)):this.node()[t]},classed:function(t,n){var e=R(t+"");if(arguments.length<2){for(var r=P(this.node()),i=-1,o=e.length;++i<o;)if(!r.contains(e[i]))return!1;return!0}return this.each(("function"==typeof n?z:n?I:U)(e,n))},text:function(t){return arguments.length?this.each(null==t?Y:("function"==typeof t?H:q)(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?$:("function"==typeof t?G:V)(t)):this.node().innerHTML},raise:function(){return this.each(X)},lower:function(){return this.each(W)},append:function(t){var n="function"==typeof t?t:c(t);return this.select(function(){return this.appendChild(n.apply(this,arguments))})},insert:function(t,n){var e="function"==typeof t?t:c(t),r=null==n?J:"function"==typeof n?n:s(n);return this.select(function(){return this.insertBefore(e.apply(this,arguments),r.apply(this,arguments)||null)})},remove:function(){return this.each(Z)},clone:function(t){return this.select(t?K:Q)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,n,e){var r,i,o=it(t+""),u=o.length;if(!(arguments.length<2)){for(a=n?ut:ot,null==e&&(e=!1),r=0;r<u;++r)this.each(a(o[r],n,e));return this}var a=this.node().__on;if(a)for(var c,f=0,s=a.length;f<s;++f)for(r=0,c=a[f];r<u;++r)if((i=o[r]).type===c.type&&i.name===c.name)return c.value},dispatch:function(t,n){return this.each(("function"==typeof n?st:ft)(t,n))}};var pt=dt,vt=function(t){return"string"==typeof t?new ht([[document.querySelector(t)]],[document.documentElement]):new ht([[t]],lt)},gt=function(t){return vt(c(t).call(document.documentElement))},yt=0;function mt(){return new bt}function bt(){this._="@"+(++yt).toString(36)}bt.prototype=mt.prototype={constructor:bt,get:function(t){for(var n=this._;!(n in t);)if(!(t=t.parentNode))return;return t[n]},set:function(t,n){return t[this._]=n},remove:function(t){return this._ in t&&delete t[this._]},toString:function(){return this._}};var _t=function(){for(var t,n=nt;t=n.sourceEvent;)n=t;return n},xt=function(t,n){var e=t.ownerSVGElement||t;if(e.createSVGPoint){var r=e.createSVGPoint();return r.x=n.clientX,r.y=n.clientY,[(r=r.matrixTransform(t.getScreenCTM().inverse())).x,r.y]}var i=t.getBoundingClientRect();return[n.clientX-i.left-t.clientLeft,n.clientY-i.top-t.clientTop]},wt=function(t){var n=_t();return n.changedTouches&&(n=n.changedTouches[0]),xt(t,n)},At=function(t){return"string"==typeof t?new ht([document.querySelectorAll(t)],[document.documentElement]):new ht([null==t?[]:t],lt)},Et=function(t,n,e){arguments.length<3&&(e=n,n=_t().changedTouches);for(var r,i=0,o=n?n.length:0;i<o;++i)if((r=n[i]).identifier===e)return xt(t,r);return null},Mt=function(t,n){null==n&&(n=_t().touches);for(var e=0,r=n?n.length:0,i=new Array(r);e<r;++e)i[e]=xt(t,n[e]);return i};e.d(n,"create",function(){return gt}),e.d(n,"creator",function(){return c}),e.d(n,"local",function(){return mt}),e.d(n,"matcher",function(){return d}),e.d(n,"mouse",function(){return wt}),e.d(n,"namespace",function(){return o}),e.d(n,"namespaces",function(){return i}),e.d(n,"clientPoint",function(){return xt}),e.d(n,"select",function(){return vt}),e.d(n,"selectAll",function(){return At}),e.d(n,"selection",function(){return pt}),e.d(n,"selector",function(){return s}),e.d(n,"selectorAll",function(){return h}),e.d(n,"style",function(){return N}),e.d(n,"touch",function(){return Et}),e.d(n,"touches",function(){return Mt}),e.d(n,"window",function(){return k}),e.d(n,"event",function(){return nt}),e.d(n,"customEvent",function(){return at})},24:function(t,n,e){!function(t){var n,e,r,i=String.fromCharCode;function o(t){for(var n,e,r=[],i=0,o=t.length;i<o;)(n=t.charCodeAt(i++))>=55296&&n<=56319&&i<o?56320==(64512&(e=t.charCodeAt(i++)))?r.push(((1023&n)<<10)+(1023&e)+65536):(r.push(n),i--):r.push(n);return r}function u(t){if(t>=55296&&t<=57343)throw Error("Lone surrogate U+"+t.toString(16).toUpperCase()+" is not a scalar value")}function a(t,n){return i(t>>n&63|128)}function c(t){if(0==(4294967168&t))return i(t);var n="";return 0==(4294965248&t)?n=i(t>>6&31|192):0==(4294901760&t)?(u(t),n=i(t>>12&15|224),n+=a(t,6)):0==(4292870144&t)&&(n=i(t>>18&7|240),n+=a(t,12),n+=a(t,6)),n+=i(63&t|128)}function f(){if(r>=e)throw Error("Invalid byte index");var t=255&n[r];if(r++,128==(192&t))return 63&t;throw Error("Invalid continuation byte")}function s(){var t,i;if(r>e)throw Error("Invalid byte index");if(r==e)return!1;if(t=255&n[r],r++,0==(128&t))return t;if(192==(224&t)){if((i=(31&t)<<6|f())>=128)return i;throw Error("Invalid continuation byte")}if(224==(240&t)){if((i=(15&t)<<12|f()<<6|f())>=2048)return u(i),i;throw Error("Invalid continuation byte")}if(240==(248&t)&&(i=(7&t)<<18|f()<<12|f()<<6|f())>=65536&&i<=1114111)return i;throw Error("Invalid UTF-8 detected")}t.version="3.0.0",t.encode=function(t){for(var n=o(t),e=n.length,r=-1,i="";++r<e;)i+=c(n[r]);return i},t.decode=function(t){n=o(t),e=n.length,r=0;for(var u,a=[];!1!==(u=s());)a.push(u);return function(t){for(var n,e=t.length,r=-1,o="";++r<e;)(n=t[r])>65535&&(o+=i((n-=65536)>>>10&1023|55296),n=56320|1023&n),o+=i(n);return o}(a)}}(n)},25:function(t,n){t.exports=function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}},26:function(t,n,e){var r,i;!function(o,u){"use strict";void 0===(i="function"==typeof(r=function(){var t=function(){},n="undefined",e=["trace","debug","info","warn","error"];function r(t,n){var e=t[n];if("function"==typeof e.bind)return e.bind(t);try{return Function.prototype.bind.call(e,t)}catch(n){return function(){return Function.prototype.apply.apply(e,[t,arguments])}}}function i(n,r){for(var i=0;i<e.length;i++){var o=e[i];this[o]=i<n?t:this.methodFactory(o,n,r)}this.log=this.debug}function o(t,e,r){return function(){typeof console!==n&&(i.call(this,e,r),this[t].apply(this,arguments))}}function u(e,i,u){return function(e){return"debug"===e&&(e="log"),typeof console!==n&&(void 0!==console[e]?r(console,e):void 0!==console.log?r(console,"log"):t)}(e)||o.apply(this,arguments)}function a(t,r,o){var a,c=this,f="loglevel";function s(){var t;if(typeof window!==n){try{t=window.localStorage[f]}catch(t){}if(typeof t===n)try{var e=window.document.cookie,r=e.indexOf(encodeURIComponent(f)+"=");-1!==r&&(t=/^([^;]+)/.exec(e.slice(r))[1])}catch(t){}return void 0===c.levels[t]&&(t=void 0),t}}t&&(f+=":"+t),c.name=t,c.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},c.methodFactory=o||u,c.getLevel=function(){return a},c.setLevel=function(r,o){if("string"==typeof r&&void 0!==c.levels[r.toUpperCase()]&&(r=c.levels[r.toUpperCase()]),!("number"==typeof r&&r>=0&&r<=c.levels.SILENT))throw"log.setLevel() called with invalid level: "+r;if(a=r,!1!==o&&function(t){var r=(e[t]||"silent").toUpperCase();if(typeof window!==n){try{return void(window.localStorage[f]=r)}catch(t){}try{window.document.cookie=encodeURIComponent(f)+"="+r+";"}catch(t){}}}(r),i.call(c,r,t),typeof console===n&&r<c.levels.SILENT)return"No console available for logging"},c.setDefaultLevel=function(t){s()||c.setLevel(t,!1)},c.enableAll=function(t){c.setLevel(c.levels.TRACE,t)},c.disableAll=function(t){c.setLevel(c.levels.SILENT,t)};var l=s();null==l&&(l=null==r?"WARN":r),c.setLevel(l,!1)}var c=new a,f={};c.getLogger=function(t){if("string"!=typeof t||""===t)throw new TypeError("You must supply a name when creating a logger.");var n=f[t];return n||(n=f[t]=new a(t,c.getLevel(),c.methodFactory)),n};var s=typeof window!==n?window.log:void 0;return c.noConflict=function(){return typeof window!==n&&window.log===c&&(window.log=s),c},c.getLoggers=function(){return f},c})?r.call(n,e,n,t):r)||(t.exports=i)}()},27:function(t,n){var e,r,i=t.exports={};function o(){throw new Error("setTimeout has not been defined")}function u(){throw new Error("clearTimeout has not been defined")}function a(t){if(e===setTimeout)return setTimeout(t,0);if((e===o||!e)&&setTimeout)return e=setTimeout,setTimeout(t,0);try{return e(t,0)}catch(n){try{return e.call(null,t,0)}catch(n){return e.call(this,t,0)}}}!function(){try{e="function"==typeof setTimeout?setTimeout:o}catch(t){e=o}try{r="function"==typeof clearTimeout?clearTimeout:u}catch(t){r=u}}();var c,f=[],s=!1,l=-1;function h(){s&&c&&(s=!1,c.length?f=c.concat(f):l=-1,f.length&&d())}function d(){if(!s){var t=a(h);s=!0;for(var n=f.length;n;){for(c=f,f=[];++l<n;)c&&c[l].run();l=-1,n=f.length}c=null,s=!1,function(t){if(r===clearTimeout)return clearTimeout(t);if((r===u||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(t);try{r(t)}catch(n){try{return r.call(null,t)}catch(n){return r.call(this,t)}}}(t)}}function p(t,n){this.fun=t,this.array=n}function v(){}i.nextTick=function(t){var n=new Array(arguments.length-1);if(arguments.length>1)for(var e=1;e<arguments.length;e++)n[e-1]=arguments[e];f.push(new p(t,n)),1!==f.length||s||a(d)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=v,i.addListener=v,i.once=v,i.off=v,i.removeListener=v,i.removeAllListeners=v,i.emit=v,i.prependListener=v,i.prependOnceListener=v,i.listeners=function(t){return[]},i.binding=function(t){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(t){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},28:function(t,n){var e;e=function(){return this}();try{e=e||new Function("return this")()}catch(t){"object"==typeof window&&(e=window)}t.exports=e},29:function(t,n,e){"use strict";e.d(n,"a",function(){return i});var r=e(0);
/**
 * Binary functions.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 */function i(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Space",e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:8,i=r.b.regexRep(n);t=t.replace(i,"");for(var o=[],u=0;u<t.length;u+=e)o.push(parseInt(t.substr(u,e),2));return o}},3:function(t,n){function e(n){return t.exports=e=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},e(n)}t.exports=e},30:function(t,n,e){"use strict";e.d(n,"a",function(){return i});var r=e(0);
/**
 * Decimal functions.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 */function i(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Auto";n=r.b.charRep(n);var e=[],i=t.split(n);""===i[i.length-1]&&(i=i.slice(0,i.length-1));for(var o=0;o<i.length;o++)e[o]=parseInt(i[o],10);return e}},315:function(t,n,e){"use strict";e.r(n);function r(t,n,e,r){if(isNaN(n)||isNaN(e))return t;var i,o,u,a,c,f,s,l,h,d=t._root,p={data:r},v=t._x0,g=t._y0,y=t._x1,m=t._y1;if(!d)return t._root=p,t;for(;d.length;)if((f=n>=(o=(v+y)/2))?v=o:y=o,(s=e>=(u=(g+m)/2))?g=u:m=u,i=d,!(d=d[l=s<<1|f]))return i[l]=p,t;if(a=+t._x.call(null,d.data),c=+t._y.call(null,d.data),n===a&&e===c)return p.next=d,i?i[l]=p:t._root=p,t;do{i=i?i[l]=new Array(4):t._root=new Array(4),(f=n>=(o=(v+y)/2))?v=o:y=o,(s=e>=(u=(g+m)/2))?g=u:m=u}while((l=s<<1|f)==(h=(c>=u)<<1|a>=o));return i[h]=d,i[l]=p,t}var i=function(t,n,e,r,i){this.node=t,this.x0=n,this.y0=e,this.x1=r,this.y1=i};function o(t){return t[0]}function u(t){return t[1]}function a(t,n,e){var r=new c(null==n?o:n,null==e?u:e,NaN,NaN,NaN,NaN);return null==t?r:r.addAll(t)}function c(t,n,e,r,i,o){this._x=t,this._y=n,this._x0=e,this._y0=r,this._x1=i,this._y1=o,this._root=void 0}function f(t){for(var n={data:t.data},e=n;t=t.next;)e=e.next={data:t.data};return n}var s=a.prototype=c.prototype;s.copy=function(){var t,n,e=new c(this._x,this._y,this._x0,this._y0,this._x1,this._y1),r=this._root;if(!r)return e;if(!r.length)return e._root=f(r),e;for(t=[{source:r,target:e._root=new Array(4)}];r=t.pop();)for(var i=0;i<4;++i)(n=r.source[i])&&(n.length?t.push({source:n,target:r.target[i]=new Array(4)}):r.target[i]=f(n));return e},s.add=function(t){var n=+this._x.call(null,t),e=+this._y.call(null,t);return r(this.cover(n,e),n,e,t)},s.addAll=function(t){var n,e,i,o,u=t.length,a=new Array(u),c=new Array(u),f=1/0,s=1/0,l=-1/0,h=-1/0;for(e=0;e<u;++e)isNaN(i=+this._x.call(null,n=t[e]))||isNaN(o=+this._y.call(null,n))||(a[e]=i,c[e]=o,i<f&&(f=i),i>l&&(l=i),o<s&&(s=o),o>h&&(h=o));if(f>l||s>h)return this;for(this.cover(f,s).cover(l,h),e=0;e<u;++e)r(this,a[e],c[e],t[e]);return this},s.cover=function(t,n){if(isNaN(t=+t)||isNaN(n=+n))return this;var e=this._x0,r=this._y0,i=this._x1,o=this._y1;if(isNaN(e))i=(e=Math.floor(t))+1,o=(r=Math.floor(n))+1;else{for(var u,a,c=i-e,f=this._root;e>t||t>=i||r>n||n>=o;)switch(a=(n<r)<<1|t<e,(u=new Array(4))[a]=f,f=u,c*=2,a){case 0:i=e+c,o=r+c;break;case 1:e=i-c,o=r+c;break;case 2:i=e+c,r=o-c;break;case 3:e=i-c,r=o-c}this._root&&this._root.length&&(this._root=f)}return this._x0=e,this._y0=r,this._x1=i,this._y1=o,this},s.data=function(){var t=[];return this.visit(function(n){if(!n.length)do{t.push(n.data)}while(n=n.next)}),t},s.extent=function(t){return arguments.length?this.cover(+t[0][0],+t[0][1]).cover(+t[1][0],+t[1][1]):isNaN(this._x0)?void 0:[[this._x0,this._y0],[this._x1,this._y1]]},s.find=function(t,n,e){var r,o,u,a,c,f,s,l=this._x0,h=this._y0,d=this._x1,p=this._y1,v=[],g=this._root;for(g&&v.push(new i(g,l,h,d,p)),null==e?e=1/0:(l=t-e,h=n-e,d=t+e,p=n+e,e*=e);f=v.pop();)if(!(!(g=f.node)||(o=f.x0)>d||(u=f.y0)>p||(a=f.x1)<l||(c=f.y1)<h))if(g.length){var y=(o+a)/2,m=(u+c)/2;v.push(new i(g[3],y,m,a,c),new i(g[2],o,m,y,c),new i(g[1],y,u,a,m),new i(g[0],o,u,y,m)),(s=(n>=m)<<1|t>=y)&&(f=v[v.length-1],v[v.length-1]=v[v.length-1-s],v[v.length-1-s]=f)}else{var b=t-+this._x.call(null,g.data),_=n-+this._y.call(null,g.data),x=b*b+_*_;if(x<e){var w=Math.sqrt(e=x);l=t-w,h=n-w,d=t+w,p=n+w,r=g.data}}return r},s.remove=function(t){if(isNaN(o=+this._x.call(null,t))||isNaN(u=+this._y.call(null,t)))return this;var n,e,r,i,o,u,a,c,f,s,l,h,d=this._root,p=this._x0,v=this._y0,g=this._x1,y=this._y1;if(!d)return this;if(d.length)for(;;){if((f=o>=(a=(p+g)/2))?p=a:g=a,(s=u>=(c=(v+y)/2))?v=c:y=c,n=d,!(d=d[l=s<<1|f]))return this;if(!d.length)break;(n[l+1&3]||n[l+2&3]||n[l+3&3])&&(e=n,h=l)}for(;d.data!==t;)if(r=d,!(d=d.next))return this;return(i=d.next)&&delete d.next,r?(i?r.next=i:delete r.next,this):n?(i?n[l]=i:delete n[l],(d=n[0]||n[1]||n[2]||n[3])&&d===(n[3]||n[2]||n[1]||n[0])&&!d.length&&(e?e[h]=d:this._root=d),this):(this._root=i,this)},s.removeAll=function(t){for(var n=0,e=t.length;n<e;++n)this.remove(t[n]);return this},s.root=function(){return this._root},s.size=function(){var t=0;return this.visit(function(n){if(!n.length)do{++t}while(n=n.next)}),t},s.visit=function(t){var n,e,r,o,u,a,c=[],f=this._root;for(f&&c.push(new i(f,this._x0,this._y0,this._x1,this._y1));n=c.pop();)if(!t(f=n.node,r=n.x0,o=n.y0,u=n.x1,a=n.y1)&&f.length){var s=(r+u)/2,l=(o+a)/2;(e=f[3])&&c.push(new i(e,s,l,u,a)),(e=f[2])&&c.push(new i(e,r,l,s,a)),(e=f[1])&&c.push(new i(e,s,o,u,l)),(e=f[0])&&c.push(new i(e,r,o,s,l))}return this},s.visitAfter=function(t){var n,e=[],r=[];for(this._root&&e.push(new i(this._root,this._x0,this._y0,this._x1,this._y1));n=e.pop();){var o=n.node;if(o.length){var u,a=n.x0,c=n.y0,f=n.x1,s=n.y1,l=(a+f)/2,h=(c+s)/2;(u=o[0])&&e.push(new i(u,a,c,l,h)),(u=o[1])&&e.push(new i(u,l,c,f,h)),(u=o[2])&&e.push(new i(u,a,h,l,s)),(u=o[3])&&e.push(new i(u,l,h,f,s))}r.push(n)}for(;n=r.pop();)t(n.node,n.x0,n.y0,n.x1,n.y1);return this},s.x=function(t){return arguments.length?(this._x=t,this):this._x},s.y=function(t){return arguments.length?(this._y=t,this):this._y},e.d(n,"quadtree",function(){return a})},316:function(t,n,e){"use strict";e.r(n);var r=e(38);function i(t){if(0<=t.y&&t.y<100){var n=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return n.setFullYear(t.y),n}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function o(t){if(0<=t.y&&t.y<100){var n=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return n.setUTCFullYear(t.y),n}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function u(t){return{y:t,m:0,d:1,H:0,M:0,S:0,L:0}}function a(t){var n=t.dateTime,e=t.date,a=t.time,c=t.periods,f=t.days,s=t.shortDays,l=t.months,h=t.shortMonths,p=b(c),v=_(c),g=b(f),y=_(f),m=b(s),At=_(s),Et=b(l),Mt=_(l),kt=b(h),Bt=_(h),Ft={a:function(t){return s[t.getDay()]},A:function(t){return f[t.getDay()]},b:function(t){return h[t.getMonth()]},B:function(t){return l[t.getMonth()]},c:null,d:U,e:U,f:$,H:z,I:Y,j:q,L:H,m:V,M:G,p:function(t){return c[+(t.getHours()>=12)]},Q:xt,s:wt,S:X,u:W,U:J,V:Z,w:Q,W:K,x:null,X:null,y:tt,Y:nt,Z:et,"%":_t},Tt={a:function(t){return s[t.getUTCDay()]},A:function(t){return f[t.getUTCDay()]},b:function(t){return h[t.getUTCMonth()]},B:function(t){return l[t.getUTCMonth()]},c:null,d:rt,e:rt,f:ct,H:it,I:ot,j:ut,L:at,m:ft,M:st,p:function(t){return c[+(t.getUTCHours()>=12)]},Q:xt,s:wt,S:lt,u:ht,U:dt,V:pt,w:vt,W:gt,x:null,X:null,y:yt,Y:mt,Z:bt,"%":_t},Nt={a:function(t,n,e){var r=m.exec(n.slice(e));return r?(t.w=At[r[0].toLowerCase()],e+r[0].length):-1},A:function(t,n,e){var r=g.exec(n.slice(e));return r?(t.w=y[r[0].toLowerCase()],e+r[0].length):-1},b:function(t,n,e){var r=kt.exec(n.slice(e));return r?(t.m=Bt[r[0].toLowerCase()],e+r[0].length):-1},B:function(t,n,e){var r=Et.exec(n.slice(e));return r?(t.m=Mt[r[0].toLowerCase()],e+r[0].length):-1},c:function(t,e,r){return Ot(t,n,e,r)},d:N,e:N,f:j,H:S,I:S,j:C,L:P,m:T,M:O,p:function(t,n,e){var r=p.exec(n.slice(e));return r?(t.p=v[r[0].toLowerCase()],e+r[0].length):-1},Q:L,s:I,S:R,u:w,U:A,V:E,w:x,W:M,x:function(t,n,r){return Ot(t,e,n,r)},X:function(t,n,e){return Ot(t,a,n,e)},y:B,Y:k,Z:F,"%":D};function Ct(t,n){return function(e){var r,i,o,u=[],a=-1,c=0,f=t.length;for(e instanceof Date||(e=new Date(+e));++a<f;)37===t.charCodeAt(a)&&(u.push(t.slice(c,a)),null!=(i=d[r=t.charAt(++a)])?r=t.charAt(++a):i="e"===r?" ":"0",(o=n[r])&&(r=o(e,i)),u.push(r),c=a+1);return u.push(t.slice(c,a)),u.join("")}}function St(t,n){return function(e){var i,a,c=u(1900);if(Ot(c,t,e+="",0)!=e.length)return null;if("Q"in c)return new Date(c.Q);if("p"in c&&(c.H=c.H%12+12*c.p),"V"in c){if(c.V<1||c.V>53)return null;"w"in c||(c.w=1),"Z"in c?(a=(i=o(u(c.y))).getUTCDay(),i=a>4||0===a?r.utcMonday.ceil(i):Object(r.utcMonday)(i),i=r.utcDay.offset(i,7*(c.V-1)),c.y=i.getUTCFullYear(),c.m=i.getUTCMonth(),c.d=i.getUTCDate()+(c.w+6)%7):(a=(i=n(u(c.y))).getDay(),i=a>4||0===a?r.timeMonday.ceil(i):Object(r.timeMonday)(i),i=r.timeDay.offset(i,7*(c.V-1)),c.y=i.getFullYear(),c.m=i.getMonth(),c.d=i.getDate()+(c.w+6)%7)}else("W"in c||"U"in c)&&("w"in c||(c.w="u"in c?c.u%7:"W"in c?1:0),a="Z"in c?o(u(c.y)).getUTCDay():n(u(c.y)).getDay(),c.m=0,c.d="W"in c?(c.w+6)%7+7*c.W-(a+5)%7:c.w+7*c.U-(a+6)%7);return"Z"in c?(c.H+=c.Z/100|0,c.M+=c.Z%100,o(c)):n(c)}}function Ot(t,n,e,r){for(var i,o,u=0,a=n.length,c=e.length;u<a;){if(r>=c)return-1;if(37===(i=n.charCodeAt(u++))){if(i=n.charAt(u++),!(o=Nt[i in d?n.charAt(u++):i])||(r=o(t,e,r))<0)return-1}else if(i!=e.charCodeAt(r++))return-1}return r}return Ft.x=Ct(e,Ft),Ft.X=Ct(a,Ft),Ft.c=Ct(n,Ft),Tt.x=Ct(e,Tt),Tt.X=Ct(a,Tt),Tt.c=Ct(n,Tt),{format:function(t){var n=Ct(t+="",Ft);return n.toString=function(){return t},n},parse:function(t){var n=St(t+="",i);return n.toString=function(){return t},n},utcFormat:function(t){var n=Ct(t+="",Tt);return n.toString=function(){return t},n},utcParse:function(t){var n=St(t,o);return n.toString=function(){return t},n}}}var c,f,s,l,h,d={"-":"",_:" ",0:"0"},p=/^\s*\d+/,v=/^%/,g=/[\\^$*+?|[\]().{}]/g;function y(t,n,e){var r=t<0?"-":"",i=(r?-t:t)+"",o=i.length;return r+(o<e?new Array(e-o+1).join(n)+i:i)}function m(t){return t.replace(g,"\\$&")}function b(t){return new RegExp("^(?:"+t.map(m).join("|")+")","i")}function _(t){for(var n={},e=-1,r=t.length;++e<r;)n[t[e].toLowerCase()]=e;return n}function x(t,n,e){var r=p.exec(n.slice(e,e+1));return r?(t.w=+r[0],e+r[0].length):-1}function w(t,n,e){var r=p.exec(n.slice(e,e+1));return r?(t.u=+r[0],e+r[0].length):-1}function A(t,n,e){var r=p.exec(n.slice(e,e+2));return r?(t.U=+r[0],e+r[0].length):-1}function E(t,n,e){var r=p.exec(n.slice(e,e+2));return r?(t.V=+r[0],e+r[0].length):-1}function M(t,n,e){var r=p.exec(n.slice(e,e+2));return r?(t.W=+r[0],e+r[0].length):-1}function k(t,n,e){var r=p.exec(n.slice(e,e+4));return r?(t.y=+r[0],e+r[0].length):-1}function B(t,n,e){var r=p.exec(n.slice(e,e+2));return r?(t.y=+r[0]+(+r[0]>68?1900:2e3),e+r[0].length):-1}function F(t,n,e){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(n.slice(e,e+6));return r?(t.Z=r[1]?0:-(r[2]+(r[3]||"00")),e+r[0].length):-1}function T(t,n,e){var r=p.exec(n.slice(e,e+2));return r?(t.m=r[0]-1,e+r[0].length):-1}function N(t,n,e){var r=p.exec(n.slice(e,e+2));return r?(t.d=+r[0],e+r[0].length):-1}function C(t,n,e){var r=p.exec(n.slice(e,e+3));return r?(t.m=0,t.d=+r[0],e+r[0].length):-1}function S(t,n,e){var r=p.exec(n.slice(e,e+2));return r?(t.H=+r[0],e+r[0].length):-1}function O(t,n,e){var r=p.exec(n.slice(e,e+2));return r?(t.M=+r[0],e+r[0].length):-1}function R(t,n,e){var r=p.exec(n.slice(e,e+2));return r?(t.S=+r[0],e+r[0].length):-1}function P(t,n,e){var r=p.exec(n.slice(e,e+3));return r?(t.L=+r[0],e+r[0].length):-1}function j(t,n,e){var r=p.exec(n.slice(e,e+6));return r?(t.L=Math.floor(r[0]/1e3),e+r[0].length):-1}function D(t,n,e){var r=v.exec(n.slice(e,e+1));return r?e+r[0].length:-1}function L(t,n,e){var r=p.exec(n.slice(e));return r?(t.Q=+r[0],e+r[0].length):-1}function I(t,n,e){var r=p.exec(n.slice(e));return r?(t.Q=1e3*+r[0],e+r[0].length):-1}function U(t,n){return y(t.getDate(),n,2)}function z(t,n){return y(t.getHours(),n,2)}function Y(t,n){return y(t.getHours()%12||12,n,2)}function q(t,n){return y(1+r.timeDay.count(Object(r.timeYear)(t),t),n,3)}function H(t,n){return y(t.getMilliseconds(),n,3)}function $(t,n){return H(t,n)+"000"}function V(t,n){return y(t.getMonth()+1,n,2)}function G(t,n){return y(t.getMinutes(),n,2)}function X(t,n){return y(t.getSeconds(),n,2)}function W(t){var n=t.getDay();return 0===n?7:n}function J(t,n){return y(r.timeSunday.count(Object(r.timeYear)(t),t),n,2)}function Z(t,n){var e=t.getDay();return t=e>=4||0===e?Object(r.timeThursday)(t):r.timeThursday.ceil(t),y(r.timeThursday.count(Object(r.timeYear)(t),t)+(4===Object(r.timeYear)(t).getDay()),n,2)}function Q(t){return t.getDay()}function K(t,n){return y(r.timeMonday.count(Object(r.timeYear)(t),t),n,2)}function tt(t,n){return y(t.getFullYear()%100,n,2)}function nt(t,n){return y(t.getFullYear()%1e4,n,4)}function et(t){var n=t.getTimezoneOffset();return(n>0?"-":(n*=-1,"+"))+y(n/60|0,"0",2)+y(n%60,"0",2)}function rt(t,n){return y(t.getUTCDate(),n,2)}function it(t,n){return y(t.getUTCHours(),n,2)}function ot(t,n){return y(t.getUTCHours()%12||12,n,2)}function ut(t,n){return y(1+r.utcDay.count(Object(r.utcYear)(t),t),n,3)}function at(t,n){return y(t.getUTCMilliseconds(),n,3)}function ct(t,n){return at(t,n)+"000"}function ft(t,n){return y(t.getUTCMonth()+1,n,2)}function st(t,n){return y(t.getUTCMinutes(),n,2)}function lt(t,n){return y(t.getUTCSeconds(),n,2)}function ht(t){var n=t.getUTCDay();return 0===n?7:n}function dt(t,n){return y(r.utcSunday.count(Object(r.utcYear)(t),t),n,2)}function pt(t,n){var e=t.getUTCDay();return t=e>=4||0===e?Object(r.utcThursday)(t):r.utcThursday.ceil(t),y(r.utcThursday.count(Object(r.utcYear)(t),t)+(4===Object(r.utcYear)(t).getUTCDay()),n,2)}function vt(t){return t.getUTCDay()}function gt(t,n){return y(r.utcMonday.count(Object(r.utcYear)(t),t),n,2)}function yt(t,n){return y(t.getUTCFullYear()%100,n,2)}function mt(t,n){return y(t.getUTCFullYear()%1e4,n,4)}function bt(){return"+0000"}function _t(){return"%"}function xt(t){return+t}function wt(t){return Math.floor(+t/1e3)}function At(t){return c=a(t),f=c.format,s=c.parse,l=c.utcFormat,h=c.utcParse,c}At({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});var Et=Date.prototype.toISOString?function(t){return t.toISOString()}:l("%Y-%m-%dT%H:%M:%S.%LZ");var Mt=+new Date("2000-01-01T00:00:00.000Z")?function(t){var n=new Date(t);return isNaN(n)?null:n}:h("%Y-%m-%dT%H:%M:%S.%LZ");e.d(n,"timeFormatDefaultLocale",function(){return At}),e.d(n,"timeFormat",function(){return f}),e.d(n,"timeParse",function(){return s}),e.d(n,"utcFormat",function(){return l}),e.d(n,"utcParse",function(){return h}),e.d(n,"timeFormatLocale",function(){return a}),e.d(n,"isoFormat",function(){return Et}),e.d(n,"isoParse",function(){return Mt})},319:function(t,n,e){"use strict";e.r(n);var r={},i={},o=34,u=10,a=13;function c(t){return new Function("d","return {"+t.map(function(t,n){return JSON.stringify(t)+": d["+n+"]"}).join(",")+"}")}function f(t){var n=Object.create(null),e=[];return t.forEach(function(t){for(var r in t)r in n||e.push(n[r]=r)}),e}function s(t,n){var e=t+"",r=e.length;return r<n?new Array(n-r+1).join(0)+e:e}function l(t){var n,e=t.getUTCHours(),r=t.getUTCMinutes(),i=t.getUTCSeconds(),o=t.getUTCMilliseconds();return isNaN(t)?"Invalid Date":((n=t.getUTCFullYear())<0?"-"+s(-n,6):n>9999?"+"+s(n,6):s(n,4))+"-"+s(t.getUTCMonth()+1,2)+"-"+s(t.getUTCDate(),2)+(o?"T"+s(e,2)+":"+s(r,2)+":"+s(i,2)+"."+s(o,3)+"Z":i?"T"+s(e,2)+":"+s(r,2)+":"+s(i,2)+"Z":r||e?"T"+s(e,2)+":"+s(r,2)+"Z":"")}var h=function(t){var n=new RegExp('["'+t+"\n\r]"),e=t.charCodeAt(0);function s(t,n){var c,f=[],s=t.length,l=0,h=0,d=s<=0,p=!1;function v(){if(d)return i;if(p)return p=!1,r;var n,c,f=l;if(t.charCodeAt(f)===o){for(;l++<s&&t.charCodeAt(l)!==o||t.charCodeAt(++l)===o;);return(n=l)>=s?d=!0:(c=t.charCodeAt(l++))===u?p=!0:c===a&&(p=!0,t.charCodeAt(l)===u&&++l),t.slice(f+1,n-1).replace(/""/g,'"')}for(;l<s;){if((c=t.charCodeAt(n=l++))===u)p=!0;else if(c===a)p=!0,t.charCodeAt(l)===u&&++l;else if(c!==e)continue;return t.slice(f,n)}return d=!0,t.slice(f,s)}for(t.charCodeAt(s-1)===u&&--s,t.charCodeAt(s-1)===a&&--s;(c=v())!==i;){for(var g=[];c!==r&&c!==i;)g.push(c),c=v();n&&null==(g=n(g,h++))||f.push(g)}return f}function h(n,e){return n.map(function(n){return e.map(function(t){return p(n[t])}).join(t)})}function d(n){return n.map(p).join(t)}function p(t){return null==t?"":t instanceof Date?l(t):n.test(t+="")?'"'+t.replace(/"/g,'""')+'"':t}return{parse:function(t,n){var e,r,i=s(t,function(t,i){if(e)return e(t,i-1);r=t,e=n?function(t,n){var e=c(t);return function(r,i){return n(e(r),i,t)}}(t,n):c(t)});return i.columns=r||[],i},parseRows:s,format:function(n,e){return null==e&&(e=f(n)),[e.map(p).join(t)].concat(h(n,e)).join("\n")},formatBody:function(t,n){return null==n&&(n=f(t)),h(t,n).join("\n")},formatRows:function(t){return t.map(d).join("\n")}}},d=h(","),p=d.parse,v=d.parseRows,g=d.format,y=d.formatBody,m=d.formatRows,b=h("\t"),_=b.parse,x=b.parseRows,w=b.format,A=b.formatBody,E=b.formatRows;function M(t){for(var n in t){var e,r=t[n].trim();if(r)if("true"===r)r=!0;else if("false"===r)r=!1;else if("NaN"===r)r=NaN;else if(isNaN(e=+r)){if(!/^([-+]\d{2})?\d{4}(-\d{2}(-\d{2})?)?(T\d{2}:\d{2}(:\d{2}(\.\d{3})?)?(Z|[-+]\d{2}:\d{2})?)?$/.test(r))continue;r=new Date(r)}else r=e;else r=null;t[n]=r}return t}e.d(n,"dsvFormat",function(){return h}),e.d(n,"csvParse",function(){return p}),e.d(n,"csvParseRows",function(){return v}),e.d(n,"csvFormat",function(){return g}),e.d(n,"csvFormatBody",function(){return y}),e.d(n,"csvFormatRows",function(){return m}),e.d(n,"tsvParse",function(){return _}),e.d(n,"tsvParseRows",function(){return x}),e.d(n,"tsvFormat",function(){return w}),e.d(n,"tsvFormatBody",function(){return A}),e.d(n,"tsvFormatRows",function(){return E}),e.d(n,"autoType",function(){return M})},35:function(t,n,e){"use strict";e.r(n);var r=function(t,n){return t<n?-1:t>n?1:t>=n?0:NaN},i=function(t){var n;return 1===t.length&&(n=t,t=function(t,e){return r(n(t),e)}),{left:function(n,e,r,i){for(null==r&&(r=0),null==i&&(i=n.length);r<i;){var o=r+i>>>1;t(n[o],e)<0?r=o+1:i=o}return r},right:function(n,e,r,i){for(null==r&&(r=0),null==i&&(i=n.length);r<i;){var o=r+i>>>1;t(n[o],e)>0?i=o:r=o+1}return r}}};var o=i(r),u=o.right,a=o.left,c=u,f=function(t,n){null==n&&(n=s);for(var e=0,r=t.length-1,i=t[0],o=new Array(r<0?0:r);e<r;)o[e]=n(i,i=t[++e]);return o};function s(t,n){return[t,n]}var l=function(t,n,e){var r,i,o,u,a=t.length,c=n.length,f=new Array(a*c);for(null==e&&(e=s),r=o=0;r<a;++r)for(u=t[r],i=0;i<c;++i,++o)f[o]=e(u,n[i]);return f},h=function(t,n){return n<t?-1:n>t?1:n>=t?0:NaN},d=function(t){return null===t?NaN:+t},p=function(t,n){var e,r,i=t.length,o=0,u=-1,a=0,c=0;if(null==n)for(;++u<i;)isNaN(e=d(t[u]))||(c+=(r=e-a)*(e-(a+=r/++o)));else for(;++u<i;)isNaN(e=d(n(t[u],u,t)))||(c+=(r=e-a)*(e-(a+=r/++o)));if(o>1)return c/(o-1)},v=function(t,n){var e=p(t,n);return e?Math.sqrt(e):e},g=function(t,n){var e,r,i,o=t.length,u=-1;if(null==n){for(;++u<o;)if(null!=(e=t[u])&&e>=e)for(r=i=e;++u<o;)null!=(e=t[u])&&(r>e&&(r=e),i<e&&(i=e))}else for(;++u<o;)if(null!=(e=n(t[u],u,t))&&e>=e)for(r=i=e;++u<o;)null!=(e=n(t[u],u,t))&&(r>e&&(r=e),i<e&&(i=e));return[r,i]},y=Array.prototype,m=y.slice,b=y.map,_=function(t){return function(){return t}},x=function(t){return t},w=function(t,n,e){t=+t,n=+n,e=(i=arguments.length)<2?(n=t,t=0,1):i<3?1:+e;for(var r=-1,i=0|Math.max(0,Math.ceil((n-t)/e)),o=new Array(i);++r<i;)o[r]=t+r*e;return o},A=Math.sqrt(50),E=Math.sqrt(10),M=Math.sqrt(2),k=function(t,n,e){var r,i,o,u,a=-1;if(e=+e,(t=+t)===(n=+n)&&e>0)return[t];if((r=n<t)&&(i=t,t=n,n=i),0===(u=B(t,n,e))||!isFinite(u))return[];if(u>0)for(t=Math.ceil(t/u),n=Math.floor(n/u),o=new Array(i=Math.ceil(n-t+1));++a<i;)o[a]=(t+a)*u;else for(t=Math.floor(t*u),n=Math.ceil(n*u),o=new Array(i=Math.ceil(t-n+1));++a<i;)o[a]=(t-a)/u;return r&&o.reverse(),o};function B(t,n,e){var r=(n-t)/Math.max(0,e),i=Math.floor(Math.log(r)/Math.LN10),o=r/Math.pow(10,i);return i>=0?(o>=A?10:o>=E?5:o>=M?2:1)*Math.pow(10,i):-Math.pow(10,-i)/(o>=A?10:o>=E?5:o>=M?2:1)}function F(t,n,e){var r=Math.abs(n-t)/Math.max(0,e),i=Math.pow(10,Math.floor(Math.log(r)/Math.LN10)),o=r/i;return o>=A?i*=10:o>=E?i*=5:o>=M&&(i*=2),n<t?-i:i}var T=function(t){return Math.ceil(Math.log(t.length)/Math.LN2)+1},N=function(){var t=x,n=g,e=T;function r(r){var i,o,u=r.length,a=new Array(u);for(i=0;i<u;++i)a[i]=t(r[i],i,r);var f=n(a),s=f[0],l=f[1],h=e(a,s,l);Array.isArray(h)||(h=F(s,l,h),h=w(Math.ceil(s/h)*h,l,h));for(var d=h.length;h[0]<=s;)h.shift(),--d;for(;h[d-1]>l;)h.pop(),--d;var p,v=new Array(d+1);for(i=0;i<=d;++i)(p=v[i]=[]).x0=i>0?h[i-1]:s,p.x1=i<d?h[i]:l;for(i=0;i<u;++i)s<=(o=a[i])&&o<=l&&v[c(h,o,0,d)].push(r[i]);return v}return r.value=function(n){return arguments.length?(t="function"==typeof n?n:_(n),r):t},r.domain=function(t){return arguments.length?(n="function"==typeof t?t:_([t[0],t[1]]),r):n},r.thresholds=function(t){return arguments.length?(e="function"==typeof t?t:Array.isArray(t)?_(m.call(t)):_(t),r):e},r},C=function(t,n,e){if(null==e&&(e=d),r=t.length){if((n=+n)<=0||r<2)return+e(t[0],0,t);if(n>=1)return+e(t[r-1],r-1,t);var r,i=(r-1)*n,o=Math.floor(i),u=+e(t[o],o,t);return u+(+e(t[o+1],o+1,t)-u)*(i-o)}},S=function(t,n,e){return t=b.call(t,d).sort(r),Math.ceil((e-n)/(2*(C(t,.75)-C(t,.25))*Math.pow(t.length,-1/3)))},O=function(t,n,e){return Math.ceil((e-n)/(3.5*v(t)*Math.pow(t.length,-1/3)))},R=function(t,n){var e,r,i=t.length,o=-1;if(null==n){for(;++o<i;)if(null!=(e=t[o])&&e>=e)for(r=e;++o<i;)null!=(e=t[o])&&e>r&&(r=e)}else for(;++o<i;)if(null!=(e=n(t[o],o,t))&&e>=e)for(r=e;++o<i;)null!=(e=n(t[o],o,t))&&e>r&&(r=e);return r},P=function(t,n){var e,r=t.length,i=r,o=-1,u=0;if(null==n)for(;++o<r;)isNaN(e=d(t[o]))?--i:u+=e;else for(;++o<r;)isNaN(e=d(n(t[o],o,t)))?--i:u+=e;if(i)return u/i},j=function(t,n){var e,i=t.length,o=-1,u=[];if(null==n)for(;++o<i;)isNaN(e=d(t[o]))||u.push(e);else for(;++o<i;)isNaN(e=d(n(t[o],o,t)))||u.push(e);return C(u.sort(r),.5)},D=function(t){for(var n,e,r,i=t.length,o=-1,u=0;++o<i;)u+=t[o].length;for(e=new Array(u);--i>=0;)for(n=(r=t[i]).length;--n>=0;)e[--u]=r[n];return e},L=function(t,n){var e,r,i=t.length,o=-1;if(null==n){for(;++o<i;)if(null!=(e=t[o])&&e>=e)for(r=e;++o<i;)null!=(e=t[o])&&r>e&&(r=e)}else for(;++o<i;)if(null!=(e=n(t[o],o,t))&&e>=e)for(r=e;++o<i;)null!=(e=n(t[o],o,t))&&r>e&&(r=e);return r},I=function(t,n){for(var e=n.length,r=new Array(e);e--;)r[e]=t[n[e]];return r},U=function(t,n){if(e=t.length){var e,i,o=0,u=0,a=t[u];for(null==n&&(n=r);++o<e;)(n(i=t[o],a)<0||0!==n(a,a))&&(a=i,u=o);return 0===n(a,a)?u:void 0}},z=function(t,n,e){for(var r,i,o=(null==e?t.length:e)-(n=null==n?0:+n);o;)i=Math.random()*o--|0,r=t[o+n],t[o+n]=t[i+n],t[i+n]=r;return t},Y=function(t,n){var e,r=t.length,i=-1,o=0;if(null==n)for(;++i<r;)(e=+t[i])&&(o+=e);else for(;++i<r;)(e=+n(t[i],i,t))&&(o+=e);return o},q=function(t){if(!(i=t.length))return[];for(var n=-1,e=L(t,H),r=new Array(e);++n<e;)for(var i,o=-1,u=r[n]=new Array(i);++o<i;)u[o]=t[o][n];return r};function H(t){return t.length}var $=function(){return q(arguments)};e.d(n,"bisect",function(){return c}),e.d(n,"bisectRight",function(){return u}),e.d(n,"bisectLeft",function(){return a}),e.d(n,"ascending",function(){return r}),e.d(n,"bisector",function(){return i}),e.d(n,"cross",function(){return l}),e.d(n,"descending",function(){return h}),e.d(n,"deviation",function(){return v}),e.d(n,"extent",function(){return g}),e.d(n,"histogram",function(){return N}),e.d(n,"thresholdFreedmanDiaconis",function(){return S}),e.d(n,"thresholdScott",function(){return O}),e.d(n,"thresholdSturges",function(){return T}),e.d(n,"max",function(){return R}),e.d(n,"mean",function(){return P}),e.d(n,"median",function(){return j}),e.d(n,"merge",function(){return D}),e.d(n,"min",function(){return L}),e.d(n,"pairs",function(){return f}),e.d(n,"permute",function(){return I}),e.d(n,"quantile",function(){return C}),e.d(n,"range",function(){return w}),e.d(n,"scan",function(){return U}),e.d(n,"shuffle",function(){return z}),e.d(n,"sum",function(){return Y}),e.d(n,"ticks",function(){return k}),e.d(n,"tickIncrement",function(){return B}),e.d(n,"tickStep",function(){return F}),e.d(n,"transpose",function(){return q}),e.d(n,"variance",function(){return p}),e.d(n,"zip",function(){return $})},353:function(t,n,e){!function(t){"use strict";var n=Math.PI/3,e=[0,n,2*n,3*n,4*n,5*n];function r(t){return t[0]}function i(t){return t[1]}t.hexbin=function(){var t,o,u,a=0,c=0,f=1,s=1,l=r,h=i;function d(t){var n,e={},r=[],i=t.length;for(n=0;n<i;++n)if(!isNaN(c=+l.call(null,a=t[n],n,t))&&!isNaN(f=+h.call(null,a,n,t))){var a,c,f,s=Math.round(f/=u),d=Math.round(c=c/o-(1&s)/2),p=f-s;if(3*Math.abs(p)>1){var v=c-d,g=d+(c<d?-1:1)/2,y=s+(f<s?-1:1),m=c-g,b=f-y;v*v+p*p>m*m+b*b&&(d=g+(1&s?1:-1)/2,s=y)}var _=d+"-"+s,x=e[_];x?x.push(a):(r.push(x=e[_]=[a]),x.x=(d+(1&s)/2)*o,x.y=s*u)}return r}function p(t){var n=0,r=0;return e.map(function(e){var i=Math.sin(e)*t,o=-Math.cos(e)*t,u=i-n,a=o-r;return n=i,r=o,[u,a]})}return d.hexagon=function(n){return"m"+p(null==n?t:+n).join("l")+"z"},d.centers=function(){for(var n=[],e=Math.round(c/u),r=Math.round(a/o),i=e*u;i<s+t;i+=u,++e)for(var l=r*o+(1&e)*o/2;l<f+o/2;l+=o)n.push([l,i]);return n},d.mesh=function(){var n=p(t).slice(0,4).join("l");return d.centers().map(function(t){return"M"+t+"m"+n}).join("")},d.x=function(t){return arguments.length?(l=t,d):l},d.y=function(t){return arguments.length?(h=t,d):h},d.radius=function(e){return arguments.length?(o=2*(t=+e)*Math.sin(n),u=1.5*t,d):t},d.size=function(t){return arguments.length?(a=c=0,f=+t[0],s=+t[1],d):[f-a,s-c]},d.extent=function(t){return arguments.length?(a=+t[0][0],c=+t[0][1],f=+t[1][0],s=+t[1][1],d):[[a,c],[f,s]]},d.radius(1)},Object.defineProperty(t,"__esModule",{value:!0})}(n)},38:function(t,n,e){"use strict";e.r(n);var r=new Date,i=new Date;function o(t,n,e,u){function a(n){return t(n=new Date(+n)),n}return a.floor=a,a.ceil=function(e){return t(e=new Date(e-1)),n(e,1),t(e),e},a.round=function(t){var n=a(t),e=a.ceil(t);return t-n<e-t?n:e},a.offset=function(t,e){return n(t=new Date(+t),null==e?1:Math.floor(e)),t},a.range=function(e,r,i){var o,u=[];if(e=a.ceil(e),i=null==i?1:Math.floor(i),!(e<r&&i>0))return u;do{u.push(o=new Date(+e)),n(e,i),t(e)}while(o<e&&e<r);return u},a.filter=function(e){return o(function(n){if(n>=n)for(;t(n),!e(n);)n.setTime(n-1)},function(t,r){if(t>=t)if(r<0)for(;++r<=0;)for(;n(t,-1),!e(t););else for(;--r>=0;)for(;n(t,1),!e(t););})},e&&(a.count=function(n,o){return r.setTime(+n),i.setTime(+o),t(r),t(i),Math.floor(e(r,i))},a.every=function(t){return t=Math.floor(t),isFinite(t)&&t>0?t>1?a.filter(u?function(n){return u(n)%t==0}:function(n){return a.count(0,n)%t==0}):a:null}),a}var u=o(function(){},function(t,n){t.setTime(+t+n)},function(t,n){return n-t});u.every=function(t){return t=Math.floor(t),isFinite(t)&&t>0?t>1?o(function(n){n.setTime(Math.floor(n/t)*t)},function(n,e){n.setTime(+n+e*t)},function(n,e){return(e-n)/t}):u:null};var a=u,c=u.range,f=6e4,s=6048e5,l=o(function(t){t.setTime(t-t.getMilliseconds())},function(t,n){t.setTime(+t+1e3*n)},function(t,n){return(n-t)/1e3},function(t){return t.getUTCSeconds()}),h=l,d=l.range,p=o(function(t){t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},function(t,n){t.setTime(+t+n*f)},function(t,n){return(n-t)/f},function(t){return t.getMinutes()}),v=p,g=p.range,y=o(function(t){t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-t.getMinutes()*f)},function(t,n){t.setTime(+t+36e5*n)},function(t,n){return(n-t)/36e5},function(t){return t.getHours()}),m=y,b=y.range,_=o(function(t){t.setHours(0,0,0,0)},function(t,n){t.setDate(t.getDate()+n)},function(t,n){return(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*f)/864e5},function(t){return t.getDate()-1}),x=_,w=_.range;function A(t){return o(function(n){n.setDate(n.getDate()-(n.getDay()+7-t)%7),n.setHours(0,0,0,0)},function(t,n){t.setDate(t.getDate()+7*n)},function(t,n){return(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*f)/s})}var E=A(0),M=A(1),k=A(2),B=A(3),F=A(4),T=A(5),N=A(6),C=E.range,S=M.range,O=k.range,R=B.range,P=F.range,j=T.range,D=N.range,L=o(function(t){t.setDate(1),t.setHours(0,0,0,0)},function(t,n){t.setMonth(t.getMonth()+n)},function(t,n){return n.getMonth()-t.getMonth()+12*(n.getFullYear()-t.getFullYear())},function(t){return t.getMonth()}),I=L,U=L.range,z=o(function(t){t.setMonth(0,1),t.setHours(0,0,0,0)},function(t,n){t.setFullYear(t.getFullYear()+n)},function(t,n){return n.getFullYear()-t.getFullYear()},function(t){return t.getFullYear()});z.every=function(t){return isFinite(t=Math.floor(t))&&t>0?o(function(n){n.setFullYear(Math.floor(n.getFullYear()/t)*t),n.setMonth(0,1),n.setHours(0,0,0,0)},function(n,e){n.setFullYear(n.getFullYear()+e*t)}):null};var Y=z,q=z.range,H=o(function(t){t.setUTCSeconds(0,0)},function(t,n){t.setTime(+t+n*f)},function(t,n){return(n-t)/f},function(t){return t.getUTCMinutes()}),$=H,V=H.range,G=o(function(t){t.setUTCMinutes(0,0,0)},function(t,n){t.setTime(+t+36e5*n)},function(t,n){return(n-t)/36e5},function(t){return t.getUTCHours()}),X=G,W=G.range,J=o(function(t){t.setUTCHours(0,0,0,0)},function(t,n){t.setUTCDate(t.getUTCDate()+n)},function(t,n){return(n-t)/864e5},function(t){return t.getUTCDate()-1}),Z=J,Q=J.range;function K(t){return o(function(n){n.setUTCDate(n.getUTCDate()-(n.getUTCDay()+7-t)%7),n.setUTCHours(0,0,0,0)},function(t,n){t.setUTCDate(t.getUTCDate()+7*n)},function(t,n){return(n-t)/s})}var tt=K(0),nt=K(1),et=K(2),rt=K(3),it=K(4),ot=K(5),ut=K(6),at=tt.range,ct=nt.range,ft=et.range,st=rt.range,lt=it.range,ht=ot.range,dt=ut.range,pt=o(function(t){t.setUTCDate(1),t.setUTCHours(0,0,0,0)},function(t,n){t.setUTCMonth(t.getUTCMonth()+n)},function(t,n){return n.getUTCMonth()-t.getUTCMonth()+12*(n.getUTCFullYear()-t.getUTCFullYear())},function(t){return t.getUTCMonth()}),vt=pt,gt=pt.range,yt=o(function(t){t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},function(t,n){t.setUTCFullYear(t.getUTCFullYear()+n)},function(t,n){return n.getUTCFullYear()-t.getUTCFullYear()},function(t){return t.getUTCFullYear()});yt.every=function(t){return isFinite(t=Math.floor(t))&&t>0?o(function(n){n.setUTCFullYear(Math.floor(n.getUTCFullYear()/t)*t),n.setUTCMonth(0,1),n.setUTCHours(0,0,0,0)},function(n,e){n.setUTCFullYear(n.getUTCFullYear()+e*t)}):null};var mt=yt,bt=yt.range;e.d(n,"timeInterval",function(){return o}),e.d(n,"timeMillisecond",function(){return a}),e.d(n,"timeMilliseconds",function(){return c}),e.d(n,"utcMillisecond",function(){return a}),e.d(n,"utcMilliseconds",function(){return c}),e.d(n,"timeSecond",function(){return h}),e.d(n,"timeSeconds",function(){return d}),e.d(n,"utcSecond",function(){return h}),e.d(n,"utcSeconds",function(){return d}),e.d(n,"timeMinute",function(){return v}),e.d(n,"timeMinutes",function(){return g}),e.d(n,"timeHour",function(){return m}),e.d(n,"timeHours",function(){return b}),e.d(n,"timeDay",function(){return x}),e.d(n,"timeDays",function(){return w}),e.d(n,"timeWeek",function(){return E}),e.d(n,"timeWeeks",function(){return C}),e.d(n,"timeSunday",function(){return E}),e.d(n,"timeSundays",function(){return C}),e.d(n,"timeMonday",function(){return M}),e.d(n,"timeMondays",function(){return S}),e.d(n,"timeTuesday",function(){return k}),e.d(n,"timeTuesdays",function(){return O}),e.d(n,"timeWednesday",function(){return B}),e.d(n,"timeWednesdays",function(){return R}),e.d(n,"timeThursday",function(){return F}),e.d(n,"timeThursdays",function(){return P}),e.d(n,"timeFriday",function(){return T}),e.d(n,"timeFridays",function(){return j}),e.d(n,"timeSaturday",function(){return N}),e.d(n,"timeSaturdays",function(){return D}),e.d(n,"timeMonth",function(){return I}),e.d(n,"timeMonths",function(){return U}),e.d(n,"timeYear",function(){return Y}),e.d(n,"timeYears",function(){return q}),e.d(n,"utcMinute",function(){return $}),e.d(n,"utcMinutes",function(){return V}),e.d(n,"utcHour",function(){return X}),e.d(n,"utcHours",function(){return W}),e.d(n,"utcDay",function(){return Z}),e.d(n,"utcDays",function(){return Q}),e.d(n,"utcWeek",function(){return tt}),e.d(n,"utcWeeks",function(){return at}),e.d(n,"utcSunday",function(){return tt}),e.d(n,"utcSundays",function(){return at}),e.d(n,"utcMonday",function(){return nt}),e.d(n,"utcMondays",function(){return ct}),e.d(n,"utcTuesday",function(){return et}),e.d(n,"utcTuesdays",function(){return ft}),e.d(n,"utcWednesday",function(){return rt}),e.d(n,"utcWednesdays",function(){return st}),e.d(n,"utcThursday",function(){return it}),e.d(n,"utcThursdays",function(){return lt}),e.d(n,"utcFriday",function(){return ot}),e.d(n,"utcFridays",function(){return ht}),e.d(n,"utcSaturday",function(){return ut}),e.d(n,"utcSaturdays",function(){return dt}),e.d(n,"utcMonth",function(){return vt}),e.d(n,"utcMonths",function(){return gt}),e.d(n,"utcYear",function(){return mt}),e.d(n,"utcYears",function(){return bt})},4:function(t,n,e){var r=e(43),i=e(25);t.exports=function(t,n){return!n||"object"!==r(n)&&"function"!=typeof n?i(t):n}},426:function(t,n,e){"use strict";function r(t){return+t}function i(t){return t*t}function o(t){return t*(2-t)}function u(t){return((t*=2)<=1?t*t:--t*(2-t)+1)/2}function a(t){return t*t*t}function c(t){return--t*t*t+1}function f(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}e.r(n);var s=function t(n){function e(t){return Math.pow(t,n)}return n=+n,e.exponent=t,e}(3),l=function t(n){function e(t){return 1-Math.pow(1-t,n)}return n=+n,e.exponent=t,e}(3),h=function t(n){function e(t){return((t*=2)<=1?Math.pow(t,n):2-Math.pow(2-t,n))/2}return n=+n,e.exponent=t,e}(3),d=Math.PI,p=d/2;function v(t){return 1-Math.cos(t*p)}function g(t){return Math.sin(t*p)}function y(t){return(1-Math.cos(d*t))/2}function m(t){return Math.pow(2,10*t-10)}function b(t){return 1-Math.pow(2,-10*t)}function _(t){return((t*=2)<=1?Math.pow(2,10*t-10):2-Math.pow(2,10-10*t))/2}function x(t){return 1-Math.sqrt(1-t*t)}function w(t){return Math.sqrt(1- --t*t)}function A(t){return((t*=2)<=1?1-Math.sqrt(1-t*t):Math.sqrt(1-(t-=2)*t)+1)/2}var E=4/11,M=6/11,k=8/11,B=.75,F=9/11,T=10/11,N=.9375,C=21/22,S=63/64,O=1/E/E;function R(t){return 1-P(1-t)}function P(t){return(t=+t)<E?O*t*t:t<k?O*(t-=M)*t+B:t<T?O*(t-=F)*t+N:O*(t-=C)*t+S}function j(t){return((t*=2)<=1?1-P(1-t):P(t-1)+1)/2}var D=function t(n){function e(t){return t*t*((n+1)*t-n)}return n=+n,e.overshoot=t,e}(1.70158),L=function t(n){function e(t){return--t*t*((n+1)*t+n)+1}return n=+n,e.overshoot=t,e}(1.70158),I=function t(n){function e(t){return((t*=2)<1?t*t*((n+1)*t-n):(t-=2)*t*((n+1)*t+n)+2)/2}return n=+n,e.overshoot=t,e}(1.70158),U=2*Math.PI,z=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=U);function i(t){return n*Math.pow(2,10*--t)*Math.sin((r-t)/e)}return i.amplitude=function(n){return t(n,e*U)},i.period=function(e){return t(n,e)},i}(1,.3),Y=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=U);function i(t){return 1-n*Math.pow(2,-10*(t=+t))*Math.sin((t+r)/e)}return i.amplitude=function(n){return t(n,e*U)},i.period=function(e){return t(n,e)},i}(1,.3),q=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=U);function i(t){return((t=2*t-1)<0?n*Math.pow(2,10*t)*Math.sin((r-t)/e):2-n*Math.pow(2,-10*t)*Math.sin((r+t)/e))/2}return i.amplitude=function(n){return t(n,e*U)},i.period=function(e){return t(n,e)},i}(1,.3);e.d(n,"easeLinear",function(){return r}),e.d(n,"easeQuad",function(){return u}),e.d(n,"easeQuadIn",function(){return i}),e.d(n,"easeQuadOut",function(){return o}),e.d(n,"easeQuadInOut",function(){return u}),e.d(n,"easeCubic",function(){return f}),e.d(n,"easeCubicIn",function(){return a}),e.d(n,"easeCubicOut",function(){return c}),e.d(n,"easeCubicInOut",function(){return f}),e.d(n,"easePoly",function(){return h}),e.d(n,"easePolyIn",function(){return s}),e.d(n,"easePolyOut",function(){return l}),e.d(n,"easePolyInOut",function(){return h}),e.d(n,"easeSin",function(){return y}),e.d(n,"easeSinIn",function(){return v}),e.d(n,"easeSinOut",function(){return g}),e.d(n,"easeSinInOut",function(){return y}),e.d(n,"easeExp",function(){return _}),e.d(n,"easeExpIn",function(){return m}),e.d(n,"easeExpOut",function(){return b}),e.d(n,"easeExpInOut",function(){return _}),e.d(n,"easeCircle",function(){return A}),e.d(n,"easeCircleIn",function(){return x}),e.d(n,"easeCircleOut",function(){return w}),e.d(n,"easeCircleInOut",function(){return A}),e.d(n,"easeBounce",function(){return P}),e.d(n,"easeBounceIn",function(){return R}),e.d(n,"easeBounceOut",function(){return P}),e.d(n,"easeBounceInOut",function(){return j}),e.d(n,"easeBack",function(){return I}),e.d(n,"easeBackIn",function(){return D}),e.d(n,"easeBackOut",function(){return L}),e.d(n,"easeBackInOut",function(){return I}),e.d(n,"easeElastic",function(){return Y}),e.d(n,"easeElasticIn",function(){return z}),e.d(n,"easeElasticOut",function(){return Y}),e.d(n,"easeElasticInOut",function(){return q})},43:function(t,n){function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function r(n){return"function"==typeof Symbol&&"symbol"===e(Symbol.iterator)?t.exports=r=function(t){return e(t)}:t.exports=r=function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":e(t)},r(n)}t.exports=r},44:function(t,n){function e(n,r){return t.exports=e=Object.setPrototypeOf||function(t,n){return t.__proto__=n,t},e(n,r)}t.exports=e},45:function(t,n,e){"use strict";n.byteLength=function(t){var n=f(t),e=n[0],r=n[1];return 3*(e+r)/4-r},n.toByteArray=function(t){for(var n,e=f(t),r=e[0],u=e[1],a=new o(function(t,n,e){return 3*(n+e)/4-e}(0,r,u)),c=0,s=u>0?r-4:r,l=0;l<s;l+=4)n=i[t.charCodeAt(l)]<<18|i[t.charCodeAt(l+1)]<<12|i[t.charCodeAt(l+2)]<<6|i[t.charCodeAt(l+3)],a[c++]=n>>16&255,a[c++]=n>>8&255,a[c++]=255&n;2===u&&(n=i[t.charCodeAt(l)]<<2|i[t.charCodeAt(l+1)]>>4,a[c++]=255&n);1===u&&(n=i[t.charCodeAt(l)]<<10|i[t.charCodeAt(l+1)]<<4|i[t.charCodeAt(l+2)]>>2,a[c++]=n>>8&255,a[c++]=255&n);return a},n.fromByteArray=function(t){for(var n,e=t.length,i=e%3,o=[],u=0,a=e-i;u<a;u+=16383)o.push(s(t,u,u+16383>a?a:u+16383));1===i?(n=t[e-1],o.push(r[n>>2]+r[n<<4&63]+"==")):2===i&&(n=(t[e-2]<<8)+t[e-1],o.push(r[n>>10]+r[n>>4&63]+r[n<<2&63]+"="));return o.join("")};for(var r=[],i=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0,c=u.length;a<c;++a)r[a]=u[a],i[u.charCodeAt(a)]=a;function f(t){var n=t.length;if(n%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var e=t.indexOf("=");return-1===e&&(e=n),[e,e===n?0:4-e%4]}function s(t,n,e){for(var i,o,u=[],a=n;a<e;a+=3)i=(t[a]<<16&16711680)+(t[a+1]<<8&65280)+(255&t[a+2]),u.push(r[(o=i)>>18&63]+r[o>>12&63]+r[o>>6&63]+r[63&o]);return u.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},46:function(t,n){n.read=function(t,n,e,r,i){var o,u,a=8*i-r-1,c=(1<<a)-1,f=c>>1,s=-7,l=e?i-1:0,h=e?-1:1,d=t[n+l];for(l+=h,o=d&(1<<-s)-1,d>>=-s,s+=a;s>0;o=256*o+t[n+l],l+=h,s-=8);for(u=o&(1<<-s)-1,o>>=-s,s+=r;s>0;u=256*u+t[n+l],l+=h,s-=8);if(0===o)o=1-f;else{if(o===c)return u?NaN:1/0*(d?-1:1);u+=Math.pow(2,r),o-=f}return(d?-1:1)*u*Math.pow(2,o-r)},n.write=function(t,n,e,r,i,o){var u,a,c,f=8*o-i-1,s=(1<<f)-1,l=s>>1,h=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,d=r?0:o-1,p=r?1:-1,v=n<0||0===n&&1/n<0?1:0;for(n=Math.abs(n),isNaN(n)||n===1/0?(a=isNaN(n)?1:0,u=s):(u=Math.floor(Math.log(n)/Math.LN2),n*(c=Math.pow(2,-u))<1&&(u--,c*=2),(n+=u+l>=1?h/c:h*Math.pow(2,1-l))*c>=2&&(u++,c/=2),u+l>=s?(a=0,u=s):u+l>=1?(a=(n*c-1)*Math.pow(2,i),u+=l):(a=n*Math.pow(2,l-1)*Math.pow(2,i),u=0));i>=8;t[e+d]=255&a,d+=p,a/=256,i-=8);for(u=u<<i|a,f+=i;f>0;t[e+d]=255&u,d+=p,u/=256,f-=8);t[e+d-p]|=128*v}},47:function(t,n){var e={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==e.call(t)}},48:function(t,n,e){var r=function(t){"use strict";var n,e=Object.prototype,r=e.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",a=i.toStringTag||"@@toStringTag";function c(t,n,e,r){var i=n&&n.prototype instanceof v?n:v,o=Object.create(i.prototype),u=new B(r||[]);return o._invoke=function(t,n,e){var r=s;return function(i,o){if(r===h)throw new Error("Generator is already running");if(r===d){if("throw"===i)throw o;return T()}for(e.method=i,e.arg=o;;){var u=e.delegate;if(u){var a=E(u,e);if(a){if(a===p)continue;return a}}if("next"===e.method)e.sent=e._sent=e.arg;else if("throw"===e.method){if(r===s)throw r=d,e.arg;e.dispatchException(e.arg)}else"return"===e.method&&e.abrupt("return",e.arg);r=h;var c=f(t,n,e);if("normal"===c.type){if(r=e.done?d:l,c.arg===p)continue;return{value:c.arg,done:e.done}}"throw"===c.type&&(r=d,e.method="throw",e.arg=c.arg)}}}(t,e,u),o}function f(t,n,e){try{return{type:"normal",arg:t.call(n,e)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var s="suspendedStart",l="suspendedYield",h="executing",d="completed",p={};function v(){}function g(){}function y(){}var m={};m[o]=function(){return this};var b=Object.getPrototypeOf,_=b&&b(b(F([])));_&&_!==e&&r.call(_,o)&&(m=_);var x=y.prototype=v.prototype=Object.create(m);function w(t){["next","throw","return"].forEach(function(n){t[n]=function(t){return this._invoke(n,t)}})}function A(t){var n;this._invoke=function(e,i){function o(){return new Promise(function(n,o){!function n(e,i,o,u){var a=f(t[e],t,i);if("throw"!==a.type){var c=a.arg,s=c.value;return s&&"object"==typeof s&&r.call(s,"__await")?Promise.resolve(s.__await).then(function(t){n("next",t,o,u)},function(t){n("throw",t,o,u)}):Promise.resolve(s).then(function(t){c.value=t,o(c)},function(t){return n("throw",t,o,u)})}u(a.arg)}(e,i,n,o)})}return n=n?n.then(o,o):o()}}function E(t,e){var r=t.iterator[e.method];if(r===n){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=n,E(t,e),"throw"===e.method))return p;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return p}var i=f(r,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,p;var o=i.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=n),e.delegate=null,p):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}function M(t){var n={tryLoc:t[0]};1 in t&&(n.catchLoc=t[1]),2 in t&&(n.finallyLoc=t[2],n.afterLoc=t[3]),this.tryEntries.push(n)}function k(t){var n=t.completion||{};n.type="normal",delete n.arg,t.completion=n}function B(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(M,this),this.reset(!0)}function F(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,u=function e(){for(;++i<t.length;)if(r.call(t,i))return e.value=t[i],e.done=!1,e;return e.value=n,e.done=!0,e};return u.next=u}}return{next:T}}function T(){return{value:n,done:!0}}return g.prototype=x.constructor=y,y.constructor=g,y[a]=g.displayName="GeneratorFunction",t.isGeneratorFunction=function(t){var n="function"==typeof t&&t.constructor;return!!n&&(n===g||"GeneratorFunction"===(n.displayName||n.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,a in t||(t[a]="GeneratorFunction")),t.prototype=Object.create(x),t},t.awrap=function(t){return{__await:t}},w(A.prototype),A.prototype[u]=function(){return this},t.AsyncIterator=A,t.async=function(n,e,r,i){var o=new A(c(n,e,r,i));return t.isGeneratorFunction(e)?o:o.next().then(function(t){return t.done?t.value:o.next()})},w(x),x[a]="Generator",x[o]=function(){return this},x.toString=function(){return"[object Generator]"},t.keys=function(t){var n=[];for(var e in t)n.push(e);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=F,B.prototype={constructor:B,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=n,this.done=!1,this.delegate=null,this.method="next",this.arg=n,this.tryEntries.forEach(k),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=n)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function i(r,i){return a.type="throw",a.arg=t,e.next=r,i&&(e.method="next",e.arg=n),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var u=this.tryEntries[o],a=u.completion;if("root"===u.tryLoc)return i("end");if(u.tryLoc<=this.prev){var c=r.call(u,"catchLoc"),f=r.call(u,"finallyLoc");if(c&&f){if(this.prev<u.catchLoc)return i(u.catchLoc,!0);if(this.prev<u.finallyLoc)return i(u.finallyLoc)}else if(c){if(this.prev<u.catchLoc)return i(u.catchLoc,!0)}else{if(!f)throw new Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return i(u.finallyLoc)}}}},abrupt:function(t,n){for(var e=this.tryEntries.length-1;e>=0;--e){var i=this.tryEntries[e];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=n&&n<=o.finallyLoc&&(o=null);var u=o?o.completion:{};return u.type=t,u.arg=n,o?(this.method="next",this.next=o.finallyLoc,p):this.complete(u)},complete:function(t,n){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&n&&(this.next=n),p},finish:function(t){for(var n=this.tryEntries.length-1;n>=0;--n){var e=this.tryEntries[n];if(e.finallyLoc===t)return this.complete(e.completion,e.afterLoc),k(e),p}},catch:function(t){for(var n=this.tryEntries.length-1;n>=0;--n){var e=this.tryEntries[n];if(e.tryLoc===t){var r=e.completion;if("throw"===r.type){var i=r.arg;k(e)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:F(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=n),p}},t}(t.exports);try{regeneratorRuntime=r}catch(t){Function("r","regeneratorRuntime = r")(r)}},49:function(t,n){t.exports=function(t){if(Array.isArray(t))return t}},5:function(t,n,e){var r=e(44);t.exports=function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),n&&r(t,n)}},50:function(t,n){t.exports=function(t,n){var e=[],r=!0,i=!1,o=void 0;try{for(var u,a=t[Symbol.iterator]();!(r=(u=a.next()).done)&&(e.push(u.value),!n||e.length!==n);r=!0);}catch(t){i=!0,o=t}finally{try{r||null==a.return||a.return()}finally{if(i)throw o}}return e}},51:function(t,n){t.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}},53:function(t,n,e){"use strict";e.r(n);var r=function(t,n,e){t.prototype=n.prototype=e,e.constructor=t};function i(t,n){var e=Object.create(t.prototype);for(var r in n)e[r]=n[r];return e}function o(){}var u="\\s*([+-]?\\d+)\\s*",a="\\s*([+-]?\\d*\\.?\\d+(?:[eE][+-]?\\d+)?)\\s*",c="\\s*([+-]?\\d*\\.?\\d+(?:[eE][+-]?\\d+)?)%\\s*",f=/^#([0-9a-f]{3})$/,s=/^#([0-9a-f]{6})$/,l=new RegExp("^rgb\\("+[u,u,u]+"\\)$"),h=new RegExp("^rgb\\("+[c,c,c]+"\\)$"),d=new RegExp("^rgba\\("+[u,u,u,a]+"\\)$"),p=new RegExp("^rgba\\("+[c,c,c,a]+"\\)$"),v=new RegExp("^hsl\\("+[a,c,c]+"\\)$"),g=new RegExp("^hsla\\("+[a,c,c,a]+"\\)$"),y={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function m(){return this.rgb().formatHex()}function b(){return this.rgb().formatRgb()}function _(t){var n;return t=(t+"").trim().toLowerCase(),(n=f.exec(t))?new M((n=parseInt(n[1],16))>>8&15|n>>4&240,n>>4&15|240&n,(15&n)<<4|15&n,1):(n=s.exec(t))?x(parseInt(n[1],16)):(n=l.exec(t))?new M(n[1],n[2],n[3],1):(n=h.exec(t))?new M(255*n[1]/100,255*n[2]/100,255*n[3]/100,1):(n=d.exec(t))?w(n[1],n[2],n[3],n[4]):(n=p.exec(t))?w(255*n[1]/100,255*n[2]/100,255*n[3]/100,n[4]):(n=v.exec(t))?T(n[1],n[2]/100,n[3]/100,1):(n=g.exec(t))?T(n[1],n[2]/100,n[3]/100,n[4]):y.hasOwnProperty(t)?x(y[t]):"transparent"===t?new M(NaN,NaN,NaN,0):null}function x(t){return new M(t>>16&255,t>>8&255,255&t,1)}function w(t,n,e,r){return r<=0&&(t=n=e=NaN),new M(t,n,e,r)}function A(t){return t instanceof o||(t=_(t)),t?new M((t=t.rgb()).r,t.g,t.b,t.opacity):new M}function E(t,n,e,r){return 1===arguments.length?A(t):new M(t,n,e,null==r?1:r)}function M(t,n,e,r){this.r=+t,this.g=+n,this.b=+e,this.opacity=+r}function k(){return"#"+F(this.r)+F(this.g)+F(this.b)}function B(){var t=this.opacity;return(1===(t=isNaN(t)?1:Math.max(0,Math.min(1,t)))?"rgb(":"rgba(")+Math.max(0,Math.min(255,Math.round(this.r)||0))+", "+Math.max(0,Math.min(255,Math.round(this.g)||0))+", "+Math.max(0,Math.min(255,Math.round(this.b)||0))+(1===t?")":", "+t+")")}function F(t){return((t=Math.max(0,Math.min(255,Math.round(t)||0)))<16?"0":"")+t.toString(16)}function T(t,n,e,r){return r<=0?t=n=e=NaN:e<=0||e>=1?t=n=NaN:n<=0&&(t=NaN),new S(t,n,e,r)}function N(t){if(t instanceof S)return new S(t.h,t.s,t.l,t.opacity);if(t instanceof o||(t=_(t)),!t)return new S;if(t instanceof S)return t;var n=(t=t.rgb()).r/255,e=t.g/255,r=t.b/255,i=Math.min(n,e,r),u=Math.max(n,e,r),a=NaN,c=u-i,f=(u+i)/2;return c?(a=n===u?(e-r)/c+6*(e<r):e===u?(r-n)/c+2:(n-e)/c+4,c/=f<.5?u+i:2-u-i,a*=60):c=f>0&&f<1?0:a,new S(a,c,f,t.opacity)}function C(t,n,e,r){return 1===arguments.length?N(t):new S(t,n,e,null==r?1:r)}function S(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function O(t,n,e){return 255*(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)}r(o,_,{copy:function(t){return Object.assign(new this.constructor,this,t)},displayable:function(){return this.rgb().displayable()},hex:m,formatHex:m,formatHsl:function(){return N(this).formatHsl()},formatRgb:b,toString:b}),r(M,E,i(o,{brighter:function(t){return t=null==t?1/.7:Math.pow(1/.7,t),new M(this.r*t,this.g*t,this.b*t,this.opacity)},darker:function(t){return t=null==t?.7:Math.pow(.7,t),new M(this.r*t,this.g*t,this.b*t,this.opacity)},rgb:function(){return this},displayable:function(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:k,formatHex:k,formatRgb:B,toString:B})),r(S,C,i(o,{brighter:function(t){return t=null==t?1/.7:Math.pow(1/.7,t),new S(this.h,this.s,this.l*t,this.opacity)},darker:function(t){return t=null==t?.7:Math.pow(.7,t),new S(this.h,this.s,this.l*t,this.opacity)},rgb:function(){var t=this.h%360+360*(this.h<0),n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,r=e+(e<.5?e:1-e)*n,i=2*e-r;return new M(O(t>=240?t-240:t+120,i,r),O(t,i,r),O(t<120?t+240:t-120,i,r),this.opacity)},displayable:function(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl:function(){var t=this.opacity;return(1===(t=isNaN(t)?1:Math.max(0,Math.min(1,t)))?"hsl(":"hsla(")+(this.h||0)+", "+100*(this.s||0)+"%, "+100*(this.l||0)+"%"+(1===t?")":", "+t+")")}}));var R=Math.PI/180,P=180/Math.PI,j=.96422,D=1,L=.82521,I=4/29,U=6/29,z=3*U*U,Y=U*U*U;function q(t){if(t instanceof V)return new V(t.l,t.a,t.b,t.opacity);if(t instanceof tt)return nt(t);t instanceof M||(t=A(t));var n,e,r=J(t.r),i=J(t.g),o=J(t.b),u=G((.2225045*r+.7168786*i+.0606169*o)/D);return r===i&&i===o?n=e=u:(n=G((.4360747*r+.3850649*i+.1430804*o)/j),e=G((.0139322*r+.0971045*i+.7141733*o)/L)),new V(116*u-16,500*(n-u),200*(u-e),t.opacity)}function H(t,n){return new V(t,0,0,null==n?1:n)}function $(t,n,e,r){return 1===arguments.length?q(t):new V(t,n,e,null==r?1:r)}function V(t,n,e,r){this.l=+t,this.a=+n,this.b=+e,this.opacity=+r}function G(t){return t>Y?Math.pow(t,1/3):t/z+I}function X(t){return t>U?t*t*t:z*(t-I)}function W(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function J(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function Z(t){if(t instanceof tt)return new tt(t.h,t.c,t.l,t.opacity);if(t instanceof V||(t=q(t)),0===t.a&&0===t.b)return new tt(NaN,0<t.l&&t.l<100?0:NaN,t.l,t.opacity);var n=Math.atan2(t.b,t.a)*P;return new tt(n<0?n+360:n,Math.sqrt(t.a*t.a+t.b*t.b),t.l,t.opacity)}function Q(t,n,e,r){return 1===arguments.length?Z(t):new tt(e,n,t,null==r?1:r)}function K(t,n,e,r){return 1===arguments.length?Z(t):new tt(t,n,e,null==r?1:r)}function tt(t,n,e,r){this.h=+t,this.c=+n,this.l=+e,this.opacity=+r}function nt(t){if(isNaN(t.h))return new V(t.l,0,0,t.opacity);var n=t.h*R;return new V(t.l,Math.cos(n)*t.c,Math.sin(n)*t.c,t.opacity)}r(V,$,i(o,{brighter:function(t){return new V(this.l+18*(null==t?1:t),this.a,this.b,this.opacity)},darker:function(t){return new V(this.l-18*(null==t?1:t),this.a,this.b,this.opacity)},rgb:function(){var t=(this.l+16)/116,n=isNaN(this.a)?t:t+this.a/500,e=isNaN(this.b)?t:t-this.b/200;return new M(W(3.1338561*(n=j*X(n))-1.6168667*(t=D*X(t))-.4906146*(e=L*X(e))),W(-.9787684*n+1.9161415*t+.033454*e),W(.0719453*n-.2289914*t+1.4052427*e),this.opacity)}})),r(tt,K,i(o,{brighter:function(t){return new tt(this.h,this.c,this.l+18*(null==t?1:t),this.opacity)},darker:function(t){return new tt(this.h,this.c,this.l-18*(null==t?1:t),this.opacity)},rgb:function(){return nt(this).rgb()}}));var et=-.14861,rt=1.78277,it=-.29227,ot=-.90649,ut=1.97294,at=ut*ot,ct=ut*rt,ft=rt*it-ot*et;function st(t,n,e,r){return 1===arguments.length?function(t){if(t instanceof lt)return new lt(t.h,t.s,t.l,t.opacity);t instanceof M||(t=A(t));var n=t.r/255,e=t.g/255,r=t.b/255,i=(ft*r+at*n-ct*e)/(ft+at-ct),o=r-i,u=(ut*(e-i)-it*o)/ot,a=Math.sqrt(u*u+o*o)/(ut*i*(1-i)),c=a?Math.atan2(u,o)*P-120:NaN;return new lt(c<0?c+360:c,a,i,t.opacity)}(t):new lt(t,n,e,null==r?1:r)}function lt(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}r(lt,st,i(o,{brighter:function(t){return t=null==t?1/.7:Math.pow(1/.7,t),new lt(this.h,this.s,this.l*t,this.opacity)},darker:function(t){return t=null==t?.7:Math.pow(.7,t),new lt(this.h,this.s,this.l*t,this.opacity)},rgb:function(){var t=isNaN(this.h)?0:(this.h+120)*R,n=+this.l,e=isNaN(this.s)?0:this.s*n*(1-n),r=Math.cos(t),i=Math.sin(t);return new M(255*(n+e*(et*r+rt*i)),255*(n+e*(it*r+ot*i)),255*(n+e*(ut*r)),this.opacity)}})),e.d(n,"color",function(){return _}),e.d(n,"rgb",function(){return E}),e.d(n,"hsl",function(){return C}),e.d(n,"lab",function(){return $}),e.d(n,"hcl",function(){return K}),e.d(n,"lch",function(){return Q}),e.d(n,"gray",function(){return H}),e.d(n,"cubehelix",function(){return st})},6:function(t,n,e){"use strict";var r=e(1),i=e.n(r),o=e(2),u=e.n(o),a=e(15),c=e(0),f=e(17),s=function(){function t(n){i()(this,t),this.name="",this.type="",this._value=null,this.disabled=!1,this.hint="",this.rows=0,this.toggleValues=[],this.target=null,this.defaultIndex=0,this.min=null,this.max=null,this.step=1,n&&this._parseConfig(n)}return u()(t,[{key:"_parseConfig",value:function(t){this.name=t.name,this.type=t.type,this.defaultValue=t.value,this.disabled=!!t.disabled,this.hint=t.hint||!1,this.rows=t.rows||!1,this.toggleValues=t.toggleValues,this.target=void 0!==t.target?t.target:null,this.defaultIndex=void 0!==t.defaultIndex?t.defaultIndex:0,this.min=t.min,this.max=t.max,this.step=t.step}},{key:"config",get:function(){return this._value}},{key:"value",set:function(n){this._value=t.prepare(n,this.type)},get:function(){return this._value}}],[{key:"prepare",value:function(t,n){var e;switch(n){case"binaryString":case"binaryShortString":case"editableOption":case"editableOptionShort":return c.b.parseEscapedChars(t);case"byteArray":return"string"==typeof t?(t=t.replace(/\s+/g,""),Object(f.a)(t)):t;case"number":if(e=parseFloat(t),isNaN(e))throw"Invalid ingredient value. Not a number: "+c.b.truncate(t.toString(),10);return e;default:return t}}}]),t}(),l=function(){function t(){i()(this,t),this._inputType=-1,this._outputType=-1,this._presentType=-1,this._breakpoint=!1,this._disabled=!1,this._flowControl=!1,this._manualBake=!1,this._ingList=[],this.name="",this.module="",this.description="",this.infoURL=null}return u()(t,[{key:"run",value:function(t,n){return t}},{key:"highlight",value:function(t,n){return!1}},{key:"highlightReverse",value:function(t,n){return!1}},{key:"present",value:function(t,n){return t}},{key:"addIngredient",value:function(t){this._ingList.push(t)}},{key:"inputType",set:function(t){this._inputType=a.a.typeEnum(t)},get:function(){return a.a.enumLookup(this._inputType)}},{key:"outputType",set:function(t){this._outputType=a.a.typeEnum(t),this._presentType<0&&(this._presentType=this._outputType)},get:function(){return a.a.enumLookup(this._outputType)}},{key:"presentType",set:function(t){this._presentType=a.a.typeEnum(t)},get:function(){return a.a.enumLookup(this._presentType)}},{key:"args",set:function(t){var n=this;t.forEach(function(t){var e=new s(t);n.addIngredient(e)})},get:function(){return this._ingList.map(function(t){var n={name:t.name,type:t.type,value:t.defaultValue};return t.toggleValues&&(n.toggleValues=t.toggleValues),t.hint&&(n.hint=t.hint),t.rows&&(n.rows=t.rows),t.disabled&&(n.disabled=t.disabled),t.target&&(n.target=t.target),t.defaultIndex&&(n.defaultIndex=t.defaultIndex),"number"==typeof t.min&&(n.min=t.min),"number"==typeof t.max&&(n.max=t.max),t.step&&(n.step=t.step),n})}},{key:"config",get:function(){return{op:this.name,args:this._ingList.map(function(t){return t.config})}}},{key:"ingValues",set:function(t){var n=this;t.forEach(function(t,e){n._ingList[e].value=t})},get:function(){return this._ingList.map(function(t){return t.value})}},{key:"breakpoint",set:function(t){this._breakpoint=!!t},get:function(){return this._breakpoint}},{key:"disabled",set:function(t){this._disabled=!!t},get:function(){return this._disabled}},{key:"flowControl",get:function(){return this._flowControl},set:function(t){this._flowControl=!!t}},{key:"manualBake",get:function(){return this._manualBake},set:function(t){this._manualBake=!!t}}]),t}();n.a=l},63:function(t,n,e){"use strict";e.r(n);var r=e(53);function i(t,n,e,r,i){var o=t*t,u=o*t;return((1-3*t+3*o-u)*n+(4-6*o+3*u)*e+(1+3*t+3*o-3*u)*r+u*i)/6}var o=function(t){var n=t.length-1;return function(e){var r=e<=0?e=0:e>=1?(e=1,n-1):Math.floor(e*n),o=t[r],u=t[r+1],a=r>0?t[r-1]:2*o-u,c=r<n-1?t[r+2]:2*u-o;return i((e-r/n)*n,a,o,u,c)}},u=function(t){var n=t.length;return function(e){var r=Math.floor(((e%=1)<0?++e:e)*n),o=t[(r+n-1)%n],u=t[r%n],a=t[(r+1)%n],c=t[(r+2)%n];return i((e-r/n)*n,o,u,a,c)}},a=function(t){return function(){return t}};function c(t,n){return function(e){return t+e*n}}function f(t,n){var e=n-t;return e?c(t,e>180||e<-180?e-360*Math.round(e/360):e):a(isNaN(t)?n:t)}function s(t){return 1==(t=+t)?l:function(n,e){return e-n?function(t,n,e){return t=Math.pow(t,e),n=Math.pow(n,e)-t,e=1/e,function(r){return Math.pow(t+r*n,e)}}(n,e,t):a(isNaN(n)?e:n)}}function l(t,n){var e=n-t;return e?c(t,e):a(isNaN(t)?n:t)}var h=function t(n){var e=s(n);function i(t,n){var i=e((t=Object(r.rgb)(t)).r,(n=Object(r.rgb)(n)).r),o=e(t.g,n.g),u=e(t.b,n.b),a=l(t.opacity,n.opacity);return function(n){return t.r=i(n),t.g=o(n),t.b=u(n),t.opacity=a(n),t+""}}return i.gamma=t,i}(1);function d(t){return function(n){var e,i,o=n.length,u=new Array(o),a=new Array(o),c=new Array(o);for(e=0;e<o;++e)i=Object(r.rgb)(n[e]),u[e]=i.r||0,a[e]=i.g||0,c[e]=i.b||0;return u=t(u),a=t(a),c=t(c),i.opacity=1,function(t){return i.r=u(t),i.g=a(t),i.b=c(t),i+""}}}var p=d(o),v=d(u),g=function(t,n){var e,r=n?n.length:0,i=t?Math.min(r,t.length):0,o=new Array(i),u=new Array(r);for(e=0;e<i;++e)o[e]=B(t[e],n[e]);for(;e<r;++e)u[e]=n[e];return function(t){for(e=0;e<i;++e)u[e]=o[e](t);return u}},y=function(t,n){var e=new Date;return n-=t=+t,function(r){return e.setTime(t+n*r),e}},m=function(t,n){return n-=t=+t,function(e){return t+n*e}},b=function(t,n){var e,r={},i={};for(e in null!==t&&"object"==typeof t||(t={}),null!==n&&"object"==typeof n||(n={}),n)e in t?r[e]=B(t[e],n[e]):i[e]=n[e];return function(t){for(e in r)i[e]=r[e](t);return i}},_=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,x=new RegExp(_.source,"g");var w,A,E,M,k=function(t,n){var e,r,i,o=_.lastIndex=x.lastIndex=0,u=-1,a=[],c=[];for(t+="",n+="";(e=_.exec(t))&&(r=x.exec(n));)(i=r.index)>o&&(i=n.slice(o,i),a[u]?a[u]+=i:a[++u]=i),(e=e[0])===(r=r[0])?a[u]?a[u]+=r:a[++u]=r:(a[++u]=null,c.push({i:u,x:m(e,r)})),o=x.lastIndex;return o<n.length&&(i=n.slice(o),a[u]?a[u]+=i:a[++u]=i),a.length<2?c[0]?function(t){return function(n){return t(n)+""}}(c[0].x):function(t){return function(){return t}}(n):(n=c.length,function(t){for(var e,r=0;r<n;++r)a[(e=c[r]).i]=e.x(t);return a.join("")})},B=function(t,n){var e,i=typeof n;return null==n||"boolean"===i?a(n):("number"===i?m:"string"===i?(e=Object(r.color)(n))?(n=e,h):k:n instanceof r.color?h:n instanceof Date?y:Array.isArray(n)?g:"function"!=typeof n.valueOf&&"function"!=typeof n.toString||isNaN(n)?b:m)(t,n)},F=function(t){var n=t.length;return function(e){return t[Math.max(0,Math.min(n-1,Math.floor(e*n)))]}},T=function(t,n){var e=f(+t,+n);return function(t){var n=e(t);return n-360*Math.floor(n/360)}},N=function(t,n){return n-=t=+t,function(e){return Math.round(t+n*e)}},C=180/Math.PI,S={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1},O=function(t,n,e,r,i,o){var u,a,c;return(u=Math.sqrt(t*t+n*n))&&(t/=u,n/=u),(c=t*e+n*r)&&(e-=t*c,r-=n*c),(a=Math.sqrt(e*e+r*r))&&(e/=a,r/=a,c/=a),t*r<n*e&&(t=-t,n=-n,c=-c,u=-u),{translateX:i,translateY:o,rotate:Math.atan2(n,t)*C,skewX:Math.atan(c)*C,scaleX:u,scaleY:a}};function R(t,n,e,r){function i(t){return t.length?t.pop()+" ":""}return function(o,u){var a=[],c=[];return o=t(o),u=t(u),function(t,r,i,o,u,a){if(t!==i||r!==o){var c=u.push("translate(",null,n,null,e);a.push({i:c-4,x:m(t,i)},{i:c-2,x:m(r,o)})}else(i||o)&&u.push("translate("+i+n+o+e)}(o.translateX,o.translateY,u.translateX,u.translateY,a,c),function(t,n,e,o){t!==n?(t-n>180?n+=360:n-t>180&&(t+=360),o.push({i:e.push(i(e)+"rotate(",null,r)-2,x:m(t,n)})):n&&e.push(i(e)+"rotate("+n+r)}(o.rotate,u.rotate,a,c),function(t,n,e,o){t!==n?o.push({i:e.push(i(e)+"skewX(",null,r)-2,x:m(t,n)}):n&&e.push(i(e)+"skewX("+n+r)}(o.skewX,u.skewX,a,c),function(t,n,e,r,o,u){if(t!==e||n!==r){var a=o.push(i(o)+"scale(",null,",",null,")");u.push({i:a-4,x:m(t,e)},{i:a-2,x:m(n,r)})}else 1===e&&1===r||o.push(i(o)+"scale("+e+","+r+")")}(o.scaleX,o.scaleY,u.scaleX,u.scaleY,a,c),o=u=null,function(t){for(var n,e=-1,r=c.length;++e<r;)a[(n=c[e]).i]=n.x(t);return a.join("")}}}var P=R(function(t){return"none"===t?S:(w||(w=document.createElement("DIV"),A=document.documentElement,E=document.defaultView),w.style.transform=t,t=E.getComputedStyle(A.appendChild(w),null).getPropertyValue("transform"),A.removeChild(w),t=t.slice(7,-1).split(","),O(+t[0],+t[1],+t[2],+t[3],+t[4],+t[5]))},"px, ","px)","deg)"),j=R(function(t){return null==t?S:(M||(M=document.createElementNS("http://www.w3.org/2000/svg","g")),M.setAttribute("transform",t),(t=M.transform.baseVal.consolidate())?(t=t.matrix,O(t.a,t.b,t.c,t.d,t.e,t.f)):S)},", ",")",")"),D=Math.SQRT2;function L(t){return((t=Math.exp(t))+1/t)/2}var I=function(t,n){var e,r,i=t[0],o=t[1],u=t[2],a=n[0],c=n[1],f=n[2],s=a-i,l=c-o,h=s*s+l*l;if(h<1e-12)r=Math.log(f/u)/D,e=function(t){return[i+t*s,o+t*l,u*Math.exp(D*t*r)]};else{var d=Math.sqrt(h),p=(f*f-u*u+4*h)/(2*u*2*d),v=(f*f-u*u-4*h)/(2*f*2*d),g=Math.log(Math.sqrt(p*p+1)-p),y=Math.log(Math.sqrt(v*v+1)-v);r=(y-g)/D,e=function(t){var n,e=t*r,a=L(g),c=u/(2*d)*(a*(n=D*e+g,((n=Math.exp(2*n))-1)/(n+1))-function(t){return((t=Math.exp(t))-1/t)/2}(g));return[i+c*s,o+c*l,u*a/L(D*e+g)]}}return e.duration=1e3*r,e};function U(t){return function(n,e){var i=t((n=Object(r.hsl)(n)).h,(e=Object(r.hsl)(e)).h),o=l(n.s,e.s),u=l(n.l,e.l),a=l(n.opacity,e.opacity);return function(t){return n.h=i(t),n.s=o(t),n.l=u(t),n.opacity=a(t),n+""}}}var z=U(f),Y=U(l);function q(t,n){var e=l((t=Object(r.lab)(t)).l,(n=Object(r.lab)(n)).l),i=l(t.a,n.a),o=l(t.b,n.b),u=l(t.opacity,n.opacity);return function(n){return t.l=e(n),t.a=i(n),t.b=o(n),t.opacity=u(n),t+""}}function H(t){return function(n,e){var i=t((n=Object(r.hcl)(n)).h,(e=Object(r.hcl)(e)).h),o=l(n.c,e.c),u=l(n.l,e.l),a=l(n.opacity,e.opacity);return function(t){return n.h=i(t),n.c=o(t),n.l=u(t),n.opacity=a(t),n+""}}}var $=H(f),V=H(l);function G(t){return function n(e){function i(n,i){var o=t((n=Object(r.cubehelix)(n)).h,(i=Object(r.cubehelix)(i)).h),u=l(n.s,i.s),a=l(n.l,i.l),c=l(n.opacity,i.opacity);return function(t){return n.h=o(t),n.s=u(t),n.l=a(Math.pow(t,e)),n.opacity=c(t),n+""}}return e=+e,i.gamma=n,i}(1)}var X=G(f),W=G(l);function J(t,n){for(var e=0,r=n.length-1,i=n[0],o=new Array(r<0?0:r);e<r;)o[e]=t(i,i=n[++e]);return function(t){var n=Math.max(0,Math.min(r-1,Math.floor(t*=r)));return o[n](t-n)}}var Z=function(t,n){for(var e=new Array(n),r=0;r<n;++r)e[r]=t(r/(n-1));return e};e.d(n,"interpolate",function(){return B}),e.d(n,"interpolateArray",function(){return g}),e.d(n,"interpolateBasis",function(){return o}),e.d(n,"interpolateBasisClosed",function(){return u}),e.d(n,"interpolateDate",function(){return y}),e.d(n,"interpolateDiscrete",function(){return F}),e.d(n,"interpolateHue",function(){return T}),e.d(n,"interpolateNumber",function(){return m}),e.d(n,"interpolateObject",function(){return b}),e.d(n,"interpolateRound",function(){return N}),e.d(n,"interpolateString",function(){return k}),e.d(n,"interpolateTransformCss",function(){return P}),e.d(n,"interpolateTransformSvg",function(){return j}),e.d(n,"interpolateZoom",function(){return I}),e.d(n,"interpolateRgb",function(){return h}),e.d(n,"interpolateRgbBasis",function(){return p}),e.d(n,"interpolateRgbBasisClosed",function(){return v}),e.d(n,"interpolateHsl",function(){return z}),e.d(n,"interpolateHslLong",function(){return Y}),e.d(n,"interpolateLab",function(){return q}),e.d(n,"interpolateHcl",function(){return $}),e.d(n,"interpolateHclLong",function(){return V}),e.d(n,"interpolateCubehelix",function(){return X}),e.d(n,"interpolateCubehelixLong",function(){return W}),e.d(n,"piecewise",function(){return J}),e.d(n,"quantize",function(){return Z})},7:function(t,n,e){t.exports=e(48)},72:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e(35),i=e(1338),o=e(1337),u=e(1335),a=e(179),c=e(53),f=e(1328),s=e(126),l=e(201),h=e(319),d=e(426),p=e(1329),v=e(1326),g=e(135),y=e(1313),m=e(1317),b=e(63),_=e(102),x=e(1334),w=e(315),A=e(1332),E=e(1321),M=e(1315),k=e(23),B=e(1314),F=e(38),T=e(316),N=e(134),C=e(180),S=e(1327),O=e(1336);Object.keys(r).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return r[t]}})}),Object.keys(i).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return i[t]}})}),Object.keys(o).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return o[t]}})}),Object.keys(u).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return u[t]}})}),Object.keys(a).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return a[t]}})}),Object.keys(c).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return c[t]}})}),Object.keys(f).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return f[t]}})}),Object.keys(s).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return s[t]}})}),Object.keys(l).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return l[t]}})}),Object.keys(h).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return h[t]}})}),Object.keys(d).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return d[t]}})}),Object.keys(p).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return p[t]}})}),Object.keys(v).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return v[t]}})}),Object.keys(g).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return g[t]}})}),Object.keys(y).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return y[t]}})}),Object.keys(m).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return m[t]}})}),Object.keys(b).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return b[t]}})}),Object.keys(_).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return _[t]}})}),Object.keys(x).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return x[t]}})}),Object.keys(w).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return w[t]}})}),Object.keys(A).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return A[t]}})}),Object.keys(E).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return E[t]}})}),Object.keys(M).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return M[t]}})}),Object.keys(k).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return k[t]}})}),Object.keys(B).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return B[t]}})}),Object.keys(F).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return F[t]}})}),Object.keys(T).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return T[t]}})}),Object.keys(N).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return N[t]}})}),Object.keys(C).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return C[t]}})}),Object.keys(S).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return S[t]}})}),Object.keys(O).forEach(function(t){"default"!==t&&Object.defineProperty(n,t,{enumerable:!0,get:function(){return O[t]}})}),n.version="5.11.0"},73:function(t,n,e){!function(t){"use strict";function n(t){this.reset(t.className)}function e(){this.childNodes=[]}function r(t){e.apply(this),this.nodeType=3,this.textContent=t}n.prototype=[],n.prototype.reset=function(t){var n=(t||"").split(" ");this.length=n.length;for(var e=0;e<n.length;e++)this[e]=n[e]},n.prototype.add=function(t){this.contains(t)||this.push(t)},n.prototype.contains=function(t){for(var n=0;n<this.length;n++)if(this[n]===t)return!0;return!1},n.prototype.remove=function(t){for(var n=this.classNames,e=0;e<this.length;e++)n[e]===t&&this.splice(e,1)},n.prototype.toString=function(){return this.join(" ").trim()},e.prototype.cloneNode=function(t){if(!t||"childNodes"in this&&Array.isArray(this.childNodes)&&0===this.childNodes.length)return new(Object.getPrototypeOf(this).constructor)(this);var n=new(Object.getPrototypeOf(this).constructor)(this),e=[];return this.childNodes.map(function(t){return e.push(t.cloneNode(!0))}),n.childNodes=e,n},Object.defineProperty(e.prototype,"nodeValue",{get:function(){return null}}),Object.defineProperty(e.prototype,"children",{get:function(){return this.childNodes}}),Object.defineProperty(e.prototype,"firstChild",{get:function(){return this.childNodes[0]}}),Object.defineProperty(e.prototype,"lastChild",{get:function(){return this.childNodes[this.childNodes.length-1]}}),Object.defineProperty(e.prototype,"nodeName",{get:function(){return this.tagName}}),r.prototype=Object.create(e.prototype),r.prototype.constructor=r,r.prototype.render=function(){return this.textContent},Object.defineProperty(r.prototype,"nodeValue",{get:function(){return this.textContent}});var i,o=new RegExp("\\s*([ >+~])\\s*","g"),u=new RegExp("(^|[ >+~])([^ >+~]+)","ig"),a=new RegExp("#[^. >+~]+","g"),c=new RegExp("^(?:[ >+~])?([^#. >+~\\[\\]]+)"),f=new RegExp("\\.[^. >+~]+","g"),s=function(t){return null!=t?t[0]:null},l=function(t){return Array.isArray(t)&&t.length>1?t[1]:null},h=function(t){return t.substr(1)},d=function(t){return null!=t?h(t):null},p=(i=h,function(t){return Array.isArray(t)?t.map(i):null});function v(t){return null==t||0===t.length?null:t.replace(o,"$1").match(u).map(function(t,n){return{tagName:(l(t.match(c))||"").toLowerCase(),id:d(s(t.match(a))),classNames:p(t.match(f)),relation:n>0?t[0]:null}}).reverse()}function g(t,n){return!(null==t||n.tagName&&t.tagName!==n.tagName||n.id&&t.id!==n.id||n.classNames&&!n.classNames.every(function(n){return t.classList.contains(n)}))}function y(t,n){for(var e=t,r=function(t){switch(n[t-1].relation){case" ":do{e=e.parentNode}while(null!=e&&!g(e,n[t]));break;case">":if(!g(e.parentNode,n[t]))return{v:!1};break;case"+":if(!g(e.parentNode.childNodes.find(function(t){return t.nextSibling===e}),n[t]))return{v:!1};break;case"~":if(!e.parentNode.childNodes.slice(0,e.parentNode.childNodes.indexOf(e)).some(function(e){return g(e,n[t])}))return{v:!1}}},i=1;i<n.length;i++){var o=r(i);if(o)return o.v}return null!=e}function m(t,n,e){var r=v(t);if(null==r)return[];for(var i=n.getElementsByTagName(r[0].tagName||"*"),o=[],u=0;u<i.length&&!(g(i[u],r[0])&&y(i[u],r)&&(o.push(i[u]),null!=e&&o.length>=e));u++);return o}function b(t,n){return m(t,n,1)[0]||null}function _(){}function x(t){var n=[],e=t.split("");e.map(function(t,e){"-"===t&&n.push(e)});var r=t.split("");return n.map(function(t,n){var i=e[t+1].toUpperCase();r.splice(t-n,2,i)}),r.join("")}function w(t){var n=[],e=t.split("");e.map(function(t,e){/^[A-Z]/.test(t)&&n.push(e)});var r=t.split("");return n.map(function(t){r.splice(t,1,"-"+e[t].toLowerCase())}),r.join("")}function A(){}function E(){}_.prototype={},A.prototype=Object.create({}),A.prototype.setProperty=function(t,n){this[x(t)]=n},A.prototype.valueOf=function(){return this},A.prototype.toString=function(){var t="";for(var n in this)this.hasOwnProperty(n)&&(t+=w(n)+": "+this[n]+"; ");return t},A.prototype.setValue=function(t){var n=t.split(";");for(var e in n){var r=e.split(":");this[r[0].trim()]=r[1].trim()}},Object.defineProperty(A.prototype,"cssText",{get:function(){return this.toString()},set:function(t){this.setValue(t)},enumerable:!0}),E.prototype={};var M="area base br col command embed hr img input keygen link meta param source track wbr".split(" ").reduce(function(t,n){return t[n]=!0,t},{});function k(t){for(var n in e.apply(this),this.attributes=new _,this.style=new A,this.dataset=new E,this.nodeType=1,t)this[n]=t[n];this.tagName||(this.tagName="div"),this.tagName=this.tagName.toLowerCase(),Object.defineProperty(this,"isVoidEl",{value:M[this.tagName]})}var B=function(t){function n(){t.apply(this,arguments)}return t&&(n.__proto__=t),n.prototype=Object.create(t&&t.prototype),n.prototype.constructor=n,n}(k);k.prototype=Object.create(e.prototype),k.prototype.constructor=k;var F=function(){};"blur click focus".split(" ").forEach(function(t){return k.prototype[t]=F});var T="tagName view nodeType isVoidEl parent parentNode childNodes isMounted".split(" ").reduce(function(t,n){return t[n]=!0,t},{});function N(t){return t.render()}function C(){this.documentElement=this.createElement("html"),this.head=this.documentElement.appendChild(this.createElement("head")),this.body=this.documentElement.appendChild(this.createElement("body")),this.nodeType=9}k.prototype.render=function(t){var n=this,e=this.isVoidEl,r=[],i=!1,o="";for(var u in this)if("isMounted"!==u&&"style"!==u&&"attributes"!==u&&"dataset"!==u&&"_classList"!==u&&this.hasOwnProperty(u))if(T[u])this.childNodes.length&&(i=!0);else if("_innerHTML"===u)o=this._innerHTML;else if(!T[u]){if("function"==typeof this[u])continue;var a=void 0;switch(typeof this[u]){case"string":case"number":a='"'+this[u]+'"';break;default:a="'"+JSON.stringify(this[u])+"'"}r.push(u+"="+a)}this.className&&r.push('class="'+this.className+'"');var c=this.style.cssText;c.length>0&&r.push('style="'+c+'"');var f=Object.keys(this.attributes);if(f.length>0&&f.filter(function(t){return!(t in["style","_classList"])}).map(function(t){return r.push(t+'="'+n.attributes[t]+'"')}),t)return!e&&i?this.childNodes.map(N).join(""):!e&&o?o:"";if(!e&&i){var s=this.tagName;return"<"+[s].concat(r).join(" ")+">"+this.childNodes.map(N).join("")+"</"+s+">"}if(!e&&o){var l=this.tagName;return"<"+[this.tagName].concat(r).join(" ")+">"+o+"</"+l+">"}var h=[this.tagName].concat(r).join(" ");return e?"<"+h+">":"<"+h+"></"+this.tagName+">"},k.prototype.addEventListener=function(){},k.prototype.removeEventListener=function(){},k.prototype.setAttribute=function(t,n){var e=this;switch(t){case"class":this.classList.splice(0,this.classList.length),n.split(" ").forEach(function(t){return e.classList.add(t)})}var r,i,o=t;/^data-/.test(t)?(o=x(t),this.dataset[o]=n,Object.defineProperty(this,o,{get:(r=this,i=o,function(){return r.dataset[i]}),enumerable:!0})):this.hasOwnProperty(o)||Object.defineProperty(this,o,{get:function(t,n){return function(){return t.attributes[n]}}(this,o),enumerable:!0}),this.attributes[t]=n},k.prototype.getAttribute=function(t){return this.attributes[t]||this[t]},k.prototype.appendChild=function(t){if(!this.isVoidEl){t.parentNode=this;for(var n=0;n<this.childNodes.length;n++)this.childNodes[n]===t&&this.childNodes.splice(n,1);return this.childNodes.push(t),t}},k.prototype.insertBefore=function(t,n){if(!this.isVoidEl){if(t.parentNode=this,null==n)this.childNodes.push(t);else for(var e=0;e<this.childNodes.length;e++)this.childNodes[e]===n?this.childNodes.splice(e++,0,t):this.childNodes[e]===t&&this.childNodes.splice(e,1);return t}},k.prototype.replaceChild=function(t,n){if(!this.isVoidEl){t.parentNode=this;for(var e=0;e<this.childNodes.length;e++)this.childNodes[e]===n&&(this.childNodes[e]=t)}},k.prototype.removeChild=function(t){if(!this.isVoidEl){t.parentNode=null;for(var n=0;n<this.childNodes.length;n++)this.childNodes[n]===t&&this.childNodes.splice(n,1)}},k.prototype.getElementsByTagName=function(t){var n=t.toLowerCase();return this.isVoidEl||0===this.childNodes.length?[]:this.childNodes.reduce(function(t,e){return e.getElementsByTagName?"*"===n||e.tagName===n?t.concat(e,e.getElementsByTagName(n)):t.concat(e.getElementsByTagName(n)):t},[])},k.prototype.getElementsByClassName=function(t){return Array.isArray(t)?0===t.length?[]:this.childNodes.reduce(function(n,e){return t.every(function(t){return e.classList.contains(t)})?n.concat(e,e.getElementsByClassName(t)):n.concat(e.getElementsByClassName(t))},[]):this.getElementsByClassName(String(t).split(" ").map(function(t){return t.trim()}).filter(function(t){return t.length>0}))},k.prototype.querySelector=function(t){return b(t,this)},k.prototype.querySelectorAll=function(t){return m(t,this)},k.prototype.matches=function(t){var n=v(t);return!(null==n||n.length>1)&&g(this,n[0])},Object.defineProperties(k.prototype,{_classList:{value:null,enumerable:!1,configurable:!1,writable:!0},classList:{get:function(){return this._classList||(this._classList=new n(this)),this._classList}},className:{set:function(t){this.classList.reset(t)},get:function(){return null==this._classList?"":this._classList.toString()}},innerHTML:{get:function(){return this._innerHTML||this.render(!0)},set:function(t){this._innerHTML=t}},outerHTML:{get:function(){return this.render()}},firstChild:{get:function(){return this.childNodes[0]}},textContent:{get:function(){return this.childNodes.filter(function(t){return t instanceof r}).map(function(t){return t.textContent}).join("")},set:function(t){this.childNodes=[new r(t)]}},nextSibling:{get:function(){for(var t=this.parentNode.childNodes,n=0;n<t.length;n++)if(t[n]===this)return t[n+1]}}}),C.prototype.createElement=function(t){var n,e=new k({tagName:t});return"ownerDocument"in e||Object.defineProperty(e,"ownerDocument",{enumerable:!1,get:(n=this,function(){return n})}),e},C.prototype.createElementNS=function(t,n){var e,r;return"ownerDocument"in(e="http://www.w3.org/2000/svg"===n?new B({tagName:n}):new k({tagName:n}))||Object.defineProperty(e,"ownerDocument",{enumerable:!1,get:(r=this,function(){return r})}),e},C.prototype.createDocumentFragment=function(){return(new C).body},C.prototype.createTextNode=function(t){var n,e=new r(t);return"ownerDocument"in e||Object.defineProperty(e,"ownerDocument",{enumerable:!1,get:(n=this,function(){return n})}),e},C.prototype.getElementsByTagName=function(t){var n=t.toLowerCase();return"html"===n?[this.documentElement]:"*"===n?[this.documentElement].concat(this.documentElement.getElementsByTagName(n)):this.documentElement.getElementsByTagName(n)},C.prototype.getElementsByClassName=function(t){var n=this;if(!Array.isArray(t))return this.getElementsByClassName(String(t).split(" ").map(function(t){return t.trim()}).filter(function(t){return t.length>0}));if(0===t.length)return[];var e=t.every(function(t){return n.documentElement.classList.contains(t)});return this.documentElement.childNodes.reduce(function(n,e){return t.every(function(t){return e.classList.contains(t)})?n.concat(e,e.getElementsByClassName(t)):n.concat(e.getElementsByClassName(t))},e?[this.documentElement]:[])},C.prototype.getElementById=function(t){return this.documentElement.id===t?this.documentElement:function t(n,e){for(var r=[],i=0;i<e.length;i++){if(e[i].id===n)return e[i];r=r.concat(e[i].childNodes)}return r.length>0?t(n,r):null}(t,this.documentElement.childNodes)},C.prototype.querySelector=function(t){return b(t,this)},C.prototype.querySelectorAll=function(t){return m(t,this)},C.prototype.implementation=Object.create(null),C.prototype.implementation.hasFeature=function(t){return!1},C.prototype.implementation.createHTMLDocument=function(t){var n=new C;return n.outerHTML=t,n},t.Document=C,t.HTMLElement=k,t.SVGElement=B,t.Node=e,t.render=function(t,n){return(t.el||t).render(n)},t.TextNode=r,Object.defineProperty(t,"__esModule",{value:!0})}(n)},9:function(t,n,e){"use strict";var r=e(1),i=e.n(r),o=e(4),u=e.n(o),a=e(3),c=e.n(a),f=e(25),s=e.n(f),l=e(5),h=e.n(l);
/**
 * Custom error type for handling operation input errors.
 * i.e. where the operation can handle the error and print a message to the screen.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 */
var d=function(t){function n(){var t;i()(this,n);for(var e=arguments.length,r=new Array(e),o=0;o<e;o++)r[o]=arguments[o];return(t=u()(this,c()(n).call(this,...r))).type="OperationError",Error.captureStackTrace&&Error.captureStackTrace(s()(t),n),t}return h()(n,t),n}(function(t){function n(){var n=Reflect.construct(t,Array.from(arguments));return Object.setPrototypeOf(n,Object.getPrototypeOf(this)),n}return n.prototype=Object.create(t.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),Object.setPrototypeOf?Object.setPrototypeOf(n,t):n.__proto__=t,n}(Error));n.a=d}});