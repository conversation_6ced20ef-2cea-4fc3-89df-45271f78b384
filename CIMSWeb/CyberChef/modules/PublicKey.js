/**
 * CyberChef - The Cyber Swiss Army Knife
 *
 * @copyright Crown Copyright 2016
 * @license Apache-2.0
 *
 *   Copyright 2016 Crown Copyright
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */!function(t){var e={};function r(n){if(e[n])return e[n].exports;var i=e[n]={i:n,l:!1,exports:{}};return t[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)r.d(n,i,function(e){return t[e]}.bind(null,i));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=1330)}({0:function(t,e,r){"use strict";(function(t,n){r.d(e,"c",function(){return F}),r.d(e,"d",function(){return b}),r.d(e,"e",function(){return x}),r.d(e,"a",function(){return w});var i=r(7),s=r.n(i),a=r(11),o=r.n(a),u=r(1),h=r.n(u),c=r(2),l=r.n(c),f=r(24),d=r.n(f),g=r(14),p=r(17),y=r(30),v=r(29),m=function(){function e(){h()(this,e)}var r;return l()(e,null,[{key:"chr",value:function(t){if(t>65535){t-=65536;var e=String.fromCharCode(t>>>10&1023|55296);return t=56320|1023&t,e+String.fromCharCode(t)}return String.fromCharCode(t)}},{key:"ord",value:function(t){if(2===t.length){var e=t.charCodeAt(0),r=t.charCodeAt(1);if(e>=55296&&e<56320&&r>=56320&&r<57344)return 1024*(e-55296)+r-56320+65536}return t.charCodeAt(0)}},{key:"padBytesRight",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=new Array(e);return n.fill(r),[...t].forEach(function(t,e){n[e]=t}),n}},{key:"truncate",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"...";return t.length>e&&(t=t.slice(0,e-r.length)+r),t}},{key:"hex",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return(t="string"==typeof t?e.ord(t):t).toString(16).padStart(r,"0")}},{key:"bin",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:8;return(t="string"==typeof t?e.ord(t):t).toString(2).padStart(r,"0")}},{key:"printable",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];S()&&window.app&&!window.app.options.treatAsUtf8&&(t=e.byteArrayToChars(e.strToByteArray(t)));var n=/[\0-\x08\x0B-\x0C\x0E-\x1F\x7F-\x9F\xAD\u0378\u0379\u037F-\u0383\u038B\u038D\u03A2\u0528-\u0530\u0557\u0558\u0560\u0588\u058B-\u058E\u0590\u05C8-\u05CF\u05EB-\u05EF\u05F5-\u0605\u061C\u061D\u06DD\u070E\u070F\u074B\u074C\u07B2-\u07BF\u07FB-\u07FF\u082E\u082F\u083F\u085C\u085D\u085F-\u089F\u08A1\u08AD-\u08E3\u08FF\u0978\u0980\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09FC-\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF2-\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B55\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B78-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BFB-\u0C00\u0C04\u0C0D\u0C11\u0C29\u0C34\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5A-\u0C5F\u0C64\u0C65\u0C70-\u0C77\u0C80\u0C81\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0D01\u0D04\u0D0D\u0D11\u0D3B\u0D3C\u0D45\u0D49\u0D4F-\u0D56\u0D58-\u0D5F\u0D64\u0D65\u0D76-\u0D78\u0D80\u0D81\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DF1\u0DF5-\u0E00\u0E3B-\u0E3E\u0E5C-\u0E80\u0E83\u0E85\u0E86\u0E89\u0E8B\u0E8C\u0E8E-\u0E93\u0E98\u0EA0\u0EA4\u0EA6\u0EA8\u0EA9\u0EAC\u0EBA\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F48\u0F6D-\u0F70\u0F98\u0FBD\u0FCD\u0FDB-\u0FFF\u10C6\u10C8-\u10CC\u10CE\u10CF\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u137D-\u137F\u139A-\u139F\u13F5-\u13FF\u169D-\u169F\u16F1-\u16FF\u170D\u1715-\u171F\u1737-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17DE\u17DF\u17EA-\u17EF\u17FA-\u17FF\u180F\u181A-\u181F\u1878-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191D-\u191F\u192C-\u192F\u193C-\u193F\u1941-\u1943\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DB-\u19DD\u1A1C\u1A1D\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1A9F\u1AAE-\u1AFF\u1B4C-\u1B4F\u1B7D-\u1B7F\u1BF4-\u1BFB\u1C38-\u1C3A\u1C4A-\u1C4C\u1C80-\u1CBF\u1CC8-\u1CCF\u1CF7-\u1CFF\u1DE7-\u1DFB\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FC5\u1FD4\u1FD5\u1FDC\u1FF0\u1FF1\u1FF5\u1FFF\u200B-\u200F\u202A-\u202E\u2060-\u206F\u2072\u2073\u208F\u209D-\u209F\u20BB-\u20CF\u20F1-\u20FF\u218A-\u218F\u23F4-\u23FF\u2427-\u243F\u244B-\u245F\u2700\u2B4D-\u2B4F\u2B5A-\u2BFF\u2C2F\u2C5F\u2CF4-\u2CF8\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D71-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E3C-\u2E7F\u2E9A\u2EF4-\u2EFF\u2FD6-\u2FEF\u2FFC-\u2FFF\u3040\u3097\u3098\u3100-\u3104\u312E-\u3130\u318F\u31BB-\u31BF\u31E4-\u31EF\u321F\u32FF\u4DB6-\u4DBF\u9FCD-\u9FFF\uA48D-\uA48F\uA4C7-\uA4CF\uA62C-\uA63F\uA698-\uA69E\uA6F8-\uA6FF\uA78F\uA794-\uA79F\uA7AB-\uA7F7\uA82C-\uA82F\uA83A-\uA83F\uA878-\uA87F\uA8C5-\uA8CD\uA8DA-\uA8DF\uA8FC-\uA8FF\uA954-\uA95E\uA97D-\uA97F\uA9CE\uA9DA-\uA9DD\uA9E0-\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A\uAA5B\uAA7C-\uAA7F\uAAC3-\uAADA\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F-\uABBF\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBC2-\uFBD2\uFD40-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFE\uFDFF\uFE1A-\uFE1F\uFE27-\uFE2F\uFE53\uFE67\uFE6C-\uFE6F\uFE75\uFEFD-\uFF00\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFDF\uFFE7\uFFEF-\uFFFB\uFFFE\uFFFF]/g,i=/[\x09-\x10\x0D\u2028\u2029]/g;return t=t.replace(n,"."),r||(t=t.replace(i,".")),t}},{key:"parseEscapedChars",value:function(t){return t.replace(/(\\)?\\([bfnrtv'"]|[0-3][0-7]{2}|[0-7]{1,2}|x[\da-fA-F]{2}|u[\da-fA-F]{4}|u\{[\da-fA-F]{1,6}\}|\\)/g,function(t,e,r){if("\\"===e)return"\\"+r;switch(r[0]){case"\\":return"\\";case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":return String.fromCharCode(parseInt(r,8));case"b":return"\b";case"t":return"\t";case"n":return"\n";case"v":return"\v";case"f":return"\f";case"r":return"\r";case'"':return'"';case"'":return"'";case"x":return String.fromCharCode(parseInt(r.substr(1),16));case"u":return"{"===r[1]?String.fromCodePoint(parseInt(r.slice(2,-1),16)):String.fromCharCode(parseInt(r.substr(1),16))}})}},{key:"escapeRegex",value:function(t){return t.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}},{key:"expandAlphRange",value:function(t){for(var r=[],n=0;n<t.length;n++)if(n<t.length-2&&"-"===t[n+1]&&"\\"!==t[n]){for(var i=e.ord(t[n]),s=e.ord(t[n+2]),a=i;a<=s;a++)r.push(e.chr(a));n+=2}else n<t.length-2&&"\\"===t[n]&&"-"===t[n+1]?(r.push("-"),n++):r.push(t[n]);return r}},{key:"convertToByteArray",value:function(t,r){switch(r.toLowerCase()){case"binary":return Object(v.a)(t);case"hex":return Object(p.a)(t);case"decimal":return Object(y.a)(t);case"base64":return Object(g.a)(t,null,"byteArray");case"utf8":return e.strToUtf8ByteArray(t);case"latin1":default:return e.strToByteArray(t)}}},{key:"convertToByteString",value:function(t,r){switch(r.toLowerCase()){case"binary":return e.byteArrayToChars(Object(v.a)(t));case"hex":return e.byteArrayToChars(Object(p.a)(t));case"decimal":return e.byteArrayToChars(Object(y.a)(t));case"base64":return e.byteArrayToChars(Object(g.a)(t,null,"byteArray"));case"utf8":return d.a.encode(t);case"latin1":default:return t}}},{key:"strToArrayBuffer",value:function(t){for(var r,n=new Uint8Array(t.length),i=t.length;i--;)if(r=t.charCodeAt(i),n[i]=r,r>255)return e.strToUtf8ArrayBuffer(t);return n.buffer}},{key:"strToUtf8ArrayBuffer",value:function(t){var r=d.a.encode(t);return t.length!==r.length&&(b()?self.setOption("attemptHighlight",!1):S()&&(window.app.options.attemptHighlight=!1)),e.strToArrayBuffer(r)}},{key:"strToByteArray",value:function(t){for(var r,n=new Array(t.length),i=t.length;i--;)if(r=t.charCodeAt(i),n[i]=r,r>255)return e.strToUtf8ByteArray(t);return n}},{key:"strToUtf8ByteArray",value:function(t){var r=d.a.encode(t);return t.length!==r.length&&(b()?self.setOption("attemptHighlight",!1):S()&&(window.app.options.attemptHighlight=!1)),e.strToByteArray(r)}},{key:"strToCharcode",value:function(t){for(var r=[],n=0;n<t.length;n++){var i=t.charCodeAt(n);if(n<t.length-1&&i>=55296&&i<56320){var s=t[n+1].charCodeAt(0);s>=56320&&s<57344&&(i=e.ord(t[n]+t[++n]))}r.push(i)}return r}},{key:"byteArrayToUtf8",value:function(t){var r=e.byteArrayToChars(t);try{var n=d.a.decode(r);return r.length!==n.length&&(b()?self.setOption("attemptHighlight",!1):S()&&(window.app.options.attemptHighlight=!1)),n}catch(t){return r}}},{key:"byteArrayToChars",value:function(t){if(!t)return"";for(var e="",r=0;r<t.length;)e+=String.fromCharCode(t[r++]);return e}},{key:"arrayBufferToStr",value:function(t){var r=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=new Uint8Array(t);return r?e.byteArrayToUtf8(n):e.byteArrayToChars(n)}},{key:"parseCSV",value:function(t){var e,r,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[","],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:["\n","\r"],s=!1,a=!1,o="",u=[],h=[];t.length&&"\ufeff"===t[0]&&(t=t.substr(1));for(var c=0;c<t.length;c++)e=t[c],r=t[c+1]||"",s?(o+=e,s=!1):'"'!==e||a?'"'===e&&a?'"'===r?s=!0:a=!1:!a&&n.indexOf(e)>=0?(u.push(o),o=""):!a&&i.indexOf(e)>=0?(u.push(o),o="",h.push(u),u=[],i.indexOf(r)>=0&&r!==e&&c++):o+=e:a=!0;return u.length&&(u.push(o),h.push(u)),h}},{key:"stripHtmlTags",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return e&&(t=t.replace(/<(script|style)[^>]*>.*<\/(script|style)>/gim,"")),t.replace(/<[^>]+>/g,"")}},{key:"escapeHtml",value:function(t){var e={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","/":"&#x2F;","`":"&#x60;"};return t.replace(/[&<>"'\/`]/g,function(t){return e[t]})}},{key:"unescapeHtml",value:function(t){var e={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#x27;":"'","&#x2F;":"/","&#x60;":"`"};return t.replace(/&#?x?[a-z0-9]{2,4};/gi,function(t){return e[t]||t})}},{key:"encodeURIFragment",value:function(t){var e={"%2D":"-","%2E":".","%5F":"_","%7E":"~","%21":"!","%24":"$","%27":"'","%28":"(","%29":")","%2A":"*","%2C":",","%3B":";","%3A":":","%40":"@","%2F":"/","%3F":"?"};return(t=encodeURIComponent(t)).replace(/%[0-9A-F]{2}/g,function(t){return e[t]||t})}},{key:"generatePrettyRecipe",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r="",n="",i="",s="",a="";return t.forEach(function(t){n=t.op.replace(/ /g,"_"),i=JSON.stringify(t.args).slice(1,-1).replace(/'/g,"\\'").replace(/"((?:[^"\\]|\\.)*)"/g,"'$1'").replace(/\\"/g,'"'),s=t.disabled?"/disabled":"",a=t.breakpoint?"/breakpoint":"",r+=`${n}(${i}${s}${a})`,e&&(r+="\n")}),r}},{key:"parseRecipeConfig",value:function(t){if(0===(t=t.trim()).length)return[];if("["===t[0])return JSON.parse(t);var e,r;t=t.replace(/\n/g,"");for(var n=/([^(]+)\(((?:'[^'\\]*(?:\\.[^'\\]*)*'|[^)\/'])*)(\/[^)]+)?\)/g,i=[];e=n.exec(t);){r="["+(r=e[2].replace(/"/g,'\\"').replace(/(^|,|{|:)'/g,'$1"').replace(/([^\\]|(?:\\\\)+)'(,|:|}|$)/g,'$1"$2').replace(/\\'/g,"'"))+"]";var s={op:e[1].replace(/_/g," "),args:JSON.parse(r)};e[3]&&e[3].indexOf("disabled")>0&&(s.disabled=!0),e[3]&&e[3].indexOf("breakpoint")>0&&(s.breakpoint=!0),i.push(s)}return i}},{key:"displayFilesAsHTML",value:(r=o()(s.a.mark(function t(r){var n,i,a,u,h;return s.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:n=function(t){return`<div class='card' style='white-space: normal;'>\n                    <div class='card-header'>\n                        <h6 class="mb-0">\n                            ${e.escapeHtml(t.name)}\n                        </h6>\n                    </div>\n                </div>`},i=function(t,r){if(r.startsWith("image")){var n="data:";return n+=r+";","<img style='max-width: 100%;' src='"+(n+="base64,"+Object(g.b)(t))+"'>"}return`<pre>${e.escapeHtml(e.arrayBufferToStr(t.buffer))}</pre>`},a=function(){var t=o()(s.a.mark(function t(r,n){var a,o,u,h;return s.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.readFile(r);case 2:return a=t.sent,o=new Blob([a],{type:r.type||"octet/stream"}),u=URL.createObjectURL(o),h=`<div class='card' style='white-space: normal;'>\n                    <div class='card-header' id='heading${n}'>\n                        <h6 class='mb-0'>\n                            <a class='collapsed'\n                                data-toggle='collapse'\n                                href='#collapse${n}'\n                                aria-expanded='false'\n                                aria-controls='collapse${n}'\n                                title="Show/hide contents of '${e.escapeHtml(r.name)}'">\n                                ${e.escapeHtml(r.name)}</a>\n                            <span class='float-right' style="margin-top: -3px">\n                                ${r.size.toLocaleString()} bytes\n                                <a title="Download ${e.escapeHtml(r.name)}"\n                                    href="${u}"\n                                    download="${e.escapeHtml(r.name)}"\n                                    data-toggle="tooltip">\n                                    <i class="material-icons" style="vertical-align: bottom">save</i>\n                                </a>\n                                <a title="Move to input"\n                                    href="#"\n                                    blob-url="${u}"\n                                    file-name="${e.escapeHtml(r.name)}"\n                                    class="extract-file"\n                                    data-toggle="tooltip">\n                                    <i class="material-icons" style="vertical-align: bottom">open_in_browser</i>\n                                </a>\n                            </span>\n                        </h6>\n                    </div>\n                    <div id='collapse${n}' class='collapse' aria-labelledby='heading${n}' data-parent="#files">\n                        <div class='card-body'>\n                            ${i(a,r.type)}\n                        </div>\n                    </div>\n                </div>`,t.abrupt("return",h);case 7:case"end":return t.stop()}},t)}));return function(e,r){return t.apply(this,arguments)}}(),u=`<div style='padding: 5px; white-space: normal;'>\n                ${r.length} file(s) found\n            </div><div id="files" style="padding: 20px">`,h=0;case 5:if(!(h<r.length)){t.next=17;break}if(!r[h].name.endsWith("/")){t.next=10;break}u+=n(r[h]),t.next=14;break;case 10:return t.t0=u,t.next=13,a(r[h],h);case 13:u=t.t0+=t.sent;case 14:h++,t.next=5;break;case 17:return t.abrupt("return",u+="</div>");case 18:case"end":return t.stop()}},t)})),function(t){return r.apply(this,arguments)})},{key:"parseURIParams",value:function(t){if(""===t)return{};"?"!==t[0]&&"#"!==t[0]||(t=t.substr(1));for(var e=t.split("&"),r={},n=0;n<e.length;n++){var i=e[n].split("=");2!==i.length?r[e[n]]=!0:r[i[0]]=decodeURIComponent(i[1].replace(/\+/g," "))}return r}},{key:"readFile",value:function(e){return F()?t.from(e).buffer:new Promise(function(t,r){var n=new FileReader,i=new Uint8Array(e.size),s=0,a=function(){if(s>=e.size)t(i);else{var r=e.slice(s,s+10485760);n.readAsArrayBuffer(r)}};n.onload=function(t){i.set(new Uint8Array(n.result),s),s+=10485760,a()},n.onerror=function(t){r(n.error.message)},a()})}},{key:"readFileSync",value:function(t){if(!F())throw new TypeError("Browser environment cannot support readFileSync");return Uint8Array.from(t.data).buffer}},{key:"mod",value:function(t,e){return(t%e+e)%e}},{key:"gcd",value:function(t,r){return r?e.gcd(r,t%r):t}},{key:"modInv",value:function(t,e){t%=e;for(var r=1;r<e;r++)if(t*r%26==1)return r}},{key:"charRep",value:function(t){return{Space:" ",Percent:"%",Comma:",","Semi-colon":";",Colon:":",Tab:"\t","Line feed":"\n",CRLF:"\r\n","Forward slash":"/",Backslash:"\\","0x":"0x","\\x":"\\x","Nothing (separate chars)":"",None:""}[t]}},{key:"regexRep",value:function(t){return{Space:/\s+/g,Percent:/%/g,Comma:/,/g,"Semi-colon":/;/g,Colon:/:/g,"Line feed":/\n/g,CRLF:/\r\n/g,"Forward slash":/\//g,Backslash:/\\/g,"0x":/0x/g,"\\x":/\\x/g,None:/\s+/g}[t]}}]),e}();function F(){return void 0!==n&&null!=n.versions&&null!=n.versions.node}function S(){return"object"==typeof window}function b(){return"function"==typeof importScripts}function x(t){b()?self.sendStatusMessage(t):S()?app.alert(t,1e4):F()&&console.debug(t)}e.b=m,Array.prototype.unique=function(){for(var t={},e=[],r=0,n=this.length;r<n;r++)Object.prototype.hasOwnProperty.call(t,this[r])||(e.push(this[r]),t[this[r]]=1);return e},Array.prototype.max=function(){return Math.max.apply(null,this)},Array.prototype.min=function(){return Math.min.apply(null,this)},Array.prototype.sum=function(){return this.reduce(function(t,e){return t+e},0)},Array.prototype.equals=function(t){if(!t)return!1;var e=this.length;if(e!==t.length)return!1;for(;e--;)if(this[e]!==t[e])return!1;return!0},String.prototype.count=function(t){return this.split(t).length-1};var A={};function w(t,e,r,n,i){return function(){clearTimeout(A[r]),A[r]=setTimeout(function(){t.apply(n,i)},e)}}String.prototype.padStart||(String.prototype.padStart=function(t,e){return t>>=0,e=String(void 0!==e?e:" "),this.length>t?String(this):((t-=this.length)>e.length&&(e+=e.repeat(t/e.length)),e.slice(0,t)+String(this))}),String.prototype.padEnd||(String.prototype.padEnd=function(t,e){return t>>=0,e=String(void 0!==e?e:" "),this.length>t?String(this):((t-=this.length)>e.length&&(e+=e.repeat(t/e.length)),String(this)+e.slice(0,t))})}).call(this,r(12).Buffer,r(27))},1:function(t,e){t.exports=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}},10:function(t,e,r){"use strict";r.d(e,"a",function(){return o});var n=r(1),i=r.n(n),s=r(2),a=r.n(s),o=function(){function t(e){i()(this,t),this.bytes=e,this.length=this.bytes.length,this.position=0,this.bitPos=0}return a()(t,[{key:"getBytes",value:function(t){if(!(this.position>this.length)){var e=this.position+t,r=this.bytes.slice(this.position,e);return this.position=e,this.bitPos=0,r}}},{key:"readString",value:function(t){if(!(this.position>this.length)){for(var e="",r=this.position;r<this.position+t;r++){var n=this.bytes[r];if(0===n)break;e+=String.fromCharCode(n)}return this.position+=t,this.bitPos=0,e}}},{key:"readInt",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"be";if(!(this.position>this.length)){var r=0;if("be"===e)for(var n=this.position;n<this.position+t;n++)r<<=8,r|=this.bytes[n];else for(var i=this.position+t-1;i>=this.position;i--)r<<=8,r|=this.bytes[i];return this.position+=t,this.bitPos=0,r}}},{key:"readBits",value:function(t){if(!(this.position>this.length)){var e=0,r=0;for(e=(this.bytes[this.position++]&256-(1<<this.bitPos))>>>this.bitPos,r=8-this.bitPos,this.bitPos=0;r<t;)e|=this.bytes[this.position++]<<r,r+=8;if(r>t){var n=r-t;e&=(1<<t)-1,r-=n,this.position--,this.bitPos=8-n}return e}}},{key:"continueUntil",value:function(t){if(!(this.position>this.length))if(this.bitPos=0,"number"!=typeof t)for(var e=!1;!e&&this.position<this.length;){for(;++this.position<this.length&&this.bytes[this.position]!==t[0];);e=!0;for(var r=1;r<t.length;r++)(this.position+r>this.length||this.bytes[this.position+r]!==t[r])&&(e=!1)}else for(;++this.position<this.length&&this.bytes[this.position]!==t;);}},{key:"consumeIf",value:function(t){this.bytes[this.position]===t&&(this.position++,this.bitPos=0)}},{key:"moveForwardsBy",value:function(t){var e=this.position+t;if(e<0||e>this.length)throw new Error("Cannot move to position "+e+" in stream. Out of bounds.");this.position=e,this.bitPos=0}},{key:"moveBackwardsBy",value:function(t){var e=this.position-t;if(e<0||e>this.length)throw new Error("Cannot move to position "+e+" in stream. Out of bounds.");this.position=e,this.bitPos=0}},{key:"moveBackwardsByBits",value:function(t){if(t<=this.bitPos)this.bitPos-=t;else for(this.bitPos>0&&(t-=this.bitPos,this.bitPos=0);t>0;)this.moveBackwardsBy(1),this.bitPos=8,this.moveBackwardsByBits(t),t-=8}},{key:"moveTo",value:function(t){if(t<0||t>this.length)throw new Error("Cannot move to position "+t+" in stream. Out of bounds.");this.position=t,this.bitPos=0}},{key:"hasMore",value:function(){return this.position<this.length}},{key:"carve",value:function(){return this.bitPos>0&&this.position++,this.bytes.slice(0,this.position)}}]),t}()},11:function(t,e){function r(t,e,r,n,i,s,a){try{var o=t[s](a),u=o.value}catch(t){return void r(t)}o.done?e(u):Promise.resolve(u).then(n,i)}t.exports=function(t){return function(){var e=this,n=arguments;return new Promise(function(i,s){var a=t.apply(e,n);function o(t){r(a,i,s,o,u,"next",t)}function u(t){r(a,i,s,o,u,"throw",t)}o(void 0)})}}},12:function(t,e,r){"use strict";(function(t){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <<EMAIL>> <http://feross.org>
 * @license  MIT
 */
var n=r(45),i=r(46),s=r(47);function a(){return u.TYPED_ARRAY_SUPPORT?**********:**********}function o(t,e){if(a()<e)throw new RangeError("Invalid typed array length");return u.TYPED_ARRAY_SUPPORT?(t=new Uint8Array(e)).__proto__=u.prototype:(null===t&&(t=new u(e)),t.length=e),t}function u(t,e,r){if(!(u.TYPED_ARRAY_SUPPORT||this instanceof u))return new u(t,e,r);if("number"==typeof t){if("string"==typeof e)throw new Error("If encoding is specified then the first argument must be a string");return l(this,t)}return h(this,t,e,r)}function h(t,e,r,n){if("number"==typeof e)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer?function(t,e,r,n){if(e.byteLength,r<0||e.byteLength<r)throw new RangeError("'offset' is out of bounds");if(e.byteLength<r+(n||0))throw new RangeError("'length' is out of bounds");e=void 0===r&&void 0===n?new Uint8Array(e):void 0===n?new Uint8Array(e,r):new Uint8Array(e,r,n);u.TYPED_ARRAY_SUPPORT?(t=e).__proto__=u.prototype:t=f(t,e);return t}(t,e,r,n):"string"==typeof e?function(t,e,r){"string"==typeof r&&""!==r||(r="utf8");if(!u.isEncoding(r))throw new TypeError('"encoding" must be a valid string encoding');var n=0|g(e,r),i=(t=o(t,n)).write(e,r);i!==n&&(t=t.slice(0,i));return t}(t,e,r):function(t,e){if(u.isBuffer(e)){var r=0|d(e.length);return 0===(t=o(t,r)).length?t:(e.copy(t,0,0,r),t)}if(e){if("undefined"!=typeof ArrayBuffer&&e.buffer instanceof ArrayBuffer||"length"in e)return"number"!=typeof e.length||(n=e.length)!=n?o(t,0):f(t,e);if("Buffer"===e.type&&s(e.data))return f(t,e.data)}var n;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(t,e)}function c(t){if("number"!=typeof t)throw new TypeError('"size" argument must be a number');if(t<0)throw new RangeError('"size" argument must not be negative')}function l(t,e){if(c(e),t=o(t,e<0?0:0|d(e)),!u.TYPED_ARRAY_SUPPORT)for(var r=0;r<e;++r)t[r]=0;return t}function f(t,e){var r=e.length<0?0:0|d(e.length);t=o(t,r);for(var n=0;n<r;n+=1)t[n]=255&e[n];return t}function d(t){if(t>=a())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+a().toString(16)+" bytes");return 0|t}function g(t,e){if(u.isBuffer(t))return t.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;"string"!=typeof t&&(t=""+t);var r=t.length;if(0===r)return 0;for(var n=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return M(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return U(t).length;default:if(n)return M(t).length;e=(""+e).toLowerCase(),n=!0}}function p(t,e,r){var n=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(e>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return I(this,e,r);case"utf8":case"utf-8":return B(this,e,r);case"ascii":return D(this,e,r);case"latin1":case"binary":return T(this,e,r);case"base64":return E(this,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return P(this,e,r);default:if(n)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),n=!0}}function y(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function v(t,e,r,n,i){if(0===t.length)return-1;if("string"==typeof r?(n=r,r=0):r>**********?r=**********:r<-2147483648&&(r=-2147483648),r=+r,isNaN(r)&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length){if(i)return-1;r=t.length-1}else if(r<0){if(!i)return-1;r=0}if("string"==typeof e&&(e=u.from(e,n)),u.isBuffer(e))return 0===e.length?-1:m(t,e,r,n,i);if("number"==typeof e)return e&=255,u.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(t,e,r):Uint8Array.prototype.lastIndexOf.call(t,e,r):m(t,[e],r,n,i);throw new TypeError("val must be string, number or Buffer")}function m(t,e,r,n,i){var s,a=1,o=t.length,u=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return-1;a=2,o/=2,u/=2,r/=2}function h(t,e){return 1===a?t[e]:t.readUInt16BE(e*a)}if(i){var c=-1;for(s=r;s<o;s++)if(h(t,s)===h(e,-1===c?0:s-c)){if(-1===c&&(c=s),s-c+1===u)return c*a}else-1!==c&&(s-=s-c),c=-1}else for(r+u>o&&(r=o-u),s=r;s>=0;s--){for(var l=!0,f=0;f<u;f++)if(h(t,s+f)!==h(e,f)){l=!1;break}if(l)return s}return-1}function F(t,e,r,n){r=Number(r)||0;var i=t.length-r;n?(n=Number(n))>i&&(n=i):n=i;var s=e.length;if(s%2!=0)throw new TypeError("Invalid hex string");n>s/2&&(n=s/2);for(var a=0;a<n;++a){var o=parseInt(e.substr(2*a,2),16);if(isNaN(o))return a;t[r+a]=o}return a}function S(t,e,r,n){return K(M(e,t.length-r),t,r,n)}function b(t,e,r,n){return K(function(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}(e),t,r,n)}function x(t,e,r,n){return b(t,e,r,n)}function A(t,e,r,n){return K(U(e),t,r,n)}function w(t,e,r,n){return K(function(t,e){for(var r,n,i,s=[],a=0;a<t.length&&!((e-=2)<0);++a)r=t.charCodeAt(a),n=r>>8,i=r%256,s.push(i),s.push(n);return s}(e,t.length-r),t,r,n)}function E(t,e,r){return 0===e&&r===t.length?n.fromByteArray(t):n.fromByteArray(t.slice(e,r))}function B(t,e,r){r=Math.min(t.length,r);for(var n=[],i=e;i<r;){var s,a,o,u,h=t[i],c=null,l=h>239?4:h>223?3:h>191?2:1;if(i+l<=r)switch(l){case 1:h<128&&(c=h);break;case 2:128==(192&(s=t[i+1]))&&(u=(31&h)<<6|63&s)>127&&(c=u);break;case 3:s=t[i+1],a=t[i+2],128==(192&s)&&128==(192&a)&&(u=(15&h)<<12|(63&s)<<6|63&a)>2047&&(u<55296||u>57343)&&(c=u);break;case 4:s=t[i+1],a=t[i+2],o=t[i+3],128==(192&s)&&128==(192&a)&&128==(192&o)&&(u=(15&h)<<18|(63&s)<<12|(63&a)<<6|63&o)>65535&&u<1114112&&(c=u)}null===c?(c=65533,l=1):c>65535&&(c-=65536,n.push(c>>>10&1023|55296),c=56320|1023&c),n.push(c),i+=l}return function(t){var e=t.length;if(e<=C)return String.fromCharCode.apply(String,t);var r="",n=0;for(;n<e;)r+=String.fromCharCode.apply(String,t.slice(n,n+=C));return r}(n)}e.Buffer=u,e.SlowBuffer=function(t){+t!=t&&(t=0);return u.alloc(+t)},e.INSPECT_MAX_BYTES=50,u.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:function(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()&&"function"==typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(t){return!1}}(),e.kMaxLength=a(),u.poolSize=8192,u._augment=function(t){return t.__proto__=u.prototype,t},u.from=function(t,e,r){return h(null,t,e,r)},u.TYPED_ARRAY_SUPPORT&&(u.prototype.__proto__=Uint8Array.prototype,u.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&u[Symbol.species]===u&&Object.defineProperty(u,Symbol.species,{value:null,configurable:!0})),u.alloc=function(t,e,r){return function(t,e,r,n){return c(e),e<=0?o(t,e):void 0!==r?"string"==typeof n?o(t,e).fill(r,n):o(t,e).fill(r):o(t,e)}(null,t,e,r)},u.allocUnsafe=function(t){return l(null,t)},u.allocUnsafeSlow=function(t){return l(null,t)},u.isBuffer=function(t){return!(null==t||!t._isBuffer)},u.compare=function(t,e){if(!u.isBuffer(t)||!u.isBuffer(e))throw new TypeError("Arguments must be Buffers");if(t===e)return 0;for(var r=t.length,n=e.length,i=0,s=Math.min(r,n);i<s;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:n<r?1:0},u.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},u.concat=function(t,e){if(!s(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return u.alloc(0);var r;if(void 0===e)for(e=0,r=0;r<t.length;++r)e+=t[r].length;var n=u.allocUnsafe(e),i=0;for(r=0;r<t.length;++r){var a=t[r];if(!u.isBuffer(a))throw new TypeError('"list" argument must be an Array of Buffers');a.copy(n,i),i+=a.length}return n},u.byteLength=g,u.prototype._isBuffer=!0,u.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)y(this,e,e+1);return this},u.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)y(this,e,e+3),y(this,e+1,e+2);return this},u.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)y(this,e,e+7),y(this,e+1,e+6),y(this,e+2,e+5),y(this,e+3,e+4);return this},u.prototype.toString=function(){var t=0|this.length;return 0===t?"":0===arguments.length?B(this,0,t):p.apply(this,arguments)},u.prototype.equals=function(t){if(!u.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===u.compare(this,t)},u.prototype.inspect=function(){var t="",r=e.INSPECT_MAX_BYTES;return this.length>0&&(t=this.toString("hex",0,r).match(/.{2}/g).join(" "),this.length>r&&(t+=" ... ")),"<Buffer "+t+">"},u.prototype.compare=function(t,e,r,n,i){if(!u.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),e<0||r>t.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&e>=r)return 0;if(n>=i)return-1;if(e>=r)return 1;if(this===t)return 0;for(var s=(i>>>=0)-(n>>>=0),a=(r>>>=0)-(e>>>=0),o=Math.min(s,a),h=this.slice(n,i),c=t.slice(e,r),l=0;l<o;++l)if(h[l]!==c[l]){s=h[l],a=c[l];break}return s<a?-1:a<s?1:0},u.prototype.includes=function(t,e,r){return-1!==this.indexOf(t,e,r)},u.prototype.indexOf=function(t,e,r){return v(this,t,e,r,!0)},u.prototype.lastIndexOf=function(t,e,r){return v(this,t,e,r,!1)},u.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e|=0,isFinite(r)?(r|=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var i=this.length-e;if((void 0===r||r>i)&&(r=i),t.length>0&&(r<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var s=!1;;)switch(n){case"hex":return F(this,t,e,r);case"utf8":case"utf-8":return S(this,t,e,r);case"ascii":return b(this,t,e,r);case"latin1":case"binary":return x(this,t,e,r);case"base64":return A(this,t,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return w(this,t,e,r);default:if(s)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),s=!0}},u.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var C=4096;function D(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}function T(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}function I(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);for(var i="",s=e;s<r;++s)i+=_(t[s]);return i}function P(t,e,r){for(var n=t.slice(e,r),i="",s=0;s<n.length;s+=2)i+=String.fromCharCode(n[s]+256*n[s+1]);return i}function R(t,e,r){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+e>r)throw new RangeError("Trying to access beyond buffer length")}function H(t,e,r,n,i,s){if(!u.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<s)throw new RangeError('"value" argument is out of bounds');if(r+n>t.length)throw new RangeError("Index out of range")}function N(t,e,r,n){e<0&&(e=65535+e+1);for(var i=0,s=Math.min(t.length-r,2);i<s;++i)t[r+i]=(e&255<<8*(n?i:1-i))>>>8*(n?i:1-i)}function O(t,e,r,n){e<0&&(e=4294967295+e+1);for(var i=0,s=Math.min(t.length-r,4);i<s;++i)t[r+i]=e>>>8*(n?i:3-i)&255}function k(t,e,r,n,i,s){if(r+n>t.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function j(t,e,r,n,s){return s||k(t,0,r,4),i.write(t,e,r,n,23,4),r+4}function L(t,e,r,n,s){return s||k(t,0,r,8),i.write(t,e,r,n,52,8),r+8}u.prototype.slice=function(t,e){var r,n=this.length;if((t=~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),(e=void 0===e?n:~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),e<t&&(e=t),u.TYPED_ARRAY_SUPPORT)(r=this.subarray(t,e)).__proto__=u.prototype;else{var i=e-t;r=new u(i,void 0);for(var s=0;s<i;++s)r[s]=this[s+t]}return r},u.prototype.readUIntLE=function(t,e,r){t|=0,e|=0,r||R(t,e,this.length);for(var n=this[t],i=1,s=0;++s<e&&(i*=256);)n+=this[t+s]*i;return n},u.prototype.readUIntBE=function(t,e,r){t|=0,e|=0,r||R(t,e,this.length);for(var n=this[t+--e],i=1;e>0&&(i*=256);)n+=this[t+--e]*i;return n},u.prototype.readUInt8=function(t,e){return e||R(t,1,this.length),this[t]},u.prototype.readUInt16LE=function(t,e){return e||R(t,2,this.length),this[t]|this[t+1]<<8},u.prototype.readUInt16BE=function(t,e){return e||R(t,2,this.length),this[t]<<8|this[t+1]},u.prototype.readUInt32LE=function(t,e){return e||R(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},u.prototype.readUInt32BE=function(t,e){return e||R(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},u.prototype.readIntLE=function(t,e,r){t|=0,e|=0,r||R(t,e,this.length);for(var n=this[t],i=1,s=0;++s<e&&(i*=256);)n+=this[t+s]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*e)),n},u.prototype.readIntBE=function(t,e,r){t|=0,e|=0,r||R(t,e,this.length);for(var n=e,i=1,s=this[t+--n];n>0&&(i*=256);)s+=this[t+--n]*i;return s>=(i*=128)&&(s-=Math.pow(2,8*e)),s},u.prototype.readInt8=function(t,e){return e||R(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},u.prototype.readInt16LE=function(t,e){e||R(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt16BE=function(t,e){e||R(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt32LE=function(t,e){return e||R(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},u.prototype.readInt32BE=function(t,e){return e||R(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},u.prototype.readFloatLE=function(t,e){return e||R(t,4,this.length),i.read(this,t,!0,23,4)},u.prototype.readFloatBE=function(t,e){return e||R(t,4,this.length),i.read(this,t,!1,23,4)},u.prototype.readDoubleLE=function(t,e){return e||R(t,8,this.length),i.read(this,t,!0,52,8)},u.prototype.readDoubleBE=function(t,e){return e||R(t,8,this.length),i.read(this,t,!1,52,8)},u.prototype.writeUIntLE=function(t,e,r,n){(t=+t,e|=0,r|=0,n)||H(this,t,e,r,Math.pow(2,8*r)-1,0);var i=1,s=0;for(this[e]=255&t;++s<r&&(i*=256);)this[e+s]=t/i&255;return e+r},u.prototype.writeUIntBE=function(t,e,r,n){(t=+t,e|=0,r|=0,n)||H(this,t,e,r,Math.pow(2,8*r)-1,0);var i=r-1,s=1;for(this[e+i]=255&t;--i>=0&&(s*=256);)this[e+i]=t/s&255;return e+r},u.prototype.writeUInt8=function(t,e,r){return t=+t,e|=0,r||H(this,t,e,1,255,0),u.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[e]=255&t,e+1},u.prototype.writeUInt16LE=function(t,e,r){return t=+t,e|=0,r||H(this,t,e,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):N(this,t,e,!0),e+2},u.prototype.writeUInt16BE=function(t,e,r){return t=+t,e|=0,r||H(this,t,e,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):N(this,t,e,!1),e+2},u.prototype.writeUInt32LE=function(t,e,r){return t=+t,e|=0,r||H(this,t,e,4,4294967295,0),u.TYPED_ARRAY_SUPPORT?(this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t):O(this,t,e,!0),e+4},u.prototype.writeUInt32BE=function(t,e,r){return t=+t,e|=0,r||H(this,t,e,4,4294967295,0),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):O(this,t,e,!1),e+4},u.prototype.writeIntLE=function(t,e,r,n){if(t=+t,e|=0,!n){var i=Math.pow(2,8*r-1);H(this,t,e,r,i-1,-i)}var s=0,a=1,o=0;for(this[e]=255&t;++s<r&&(a*=256);)t<0&&0===o&&0!==this[e+s-1]&&(o=1),this[e+s]=(t/a>>0)-o&255;return e+r},u.prototype.writeIntBE=function(t,e,r,n){if(t=+t,e|=0,!n){var i=Math.pow(2,8*r-1);H(this,t,e,r,i-1,-i)}var s=r-1,a=1,o=0;for(this[e+s]=255&t;--s>=0&&(a*=256);)t<0&&0===o&&0!==this[e+s+1]&&(o=1),this[e+s]=(t/a>>0)-o&255;return e+r},u.prototype.writeInt8=function(t,e,r){return t=+t,e|=0,r||H(this,t,e,1,127,-128),u.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),t<0&&(t=255+t+1),this[e]=255&t,e+1},u.prototype.writeInt16LE=function(t,e,r){return t=+t,e|=0,r||H(this,t,e,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):N(this,t,e,!0),e+2},u.prototype.writeInt16BE=function(t,e,r){return t=+t,e|=0,r||H(this,t,e,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):N(this,t,e,!1),e+2},u.prototype.writeInt32LE=function(t,e,r){return t=+t,e|=0,r||H(this,t,e,4,**********,-2147483648),u.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24):O(this,t,e,!0),e+4},u.prototype.writeInt32BE=function(t,e,r){return t=+t,e|=0,r||H(this,t,e,4,**********,-2147483648),t<0&&(t=4294967295+t+1),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):O(this,t,e,!1),e+4},u.prototype.writeFloatLE=function(t,e,r){return j(this,t,e,!0,r)},u.prototype.writeFloatBE=function(t,e,r){return j(this,t,e,!1,r)},u.prototype.writeDoubleLE=function(t,e,r){return L(this,t,e,!0,r)},u.prototype.writeDoubleBE=function(t,e,r){return L(this,t,e,!1,r)},u.prototype.copy=function(t,e,r,n){if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var i,s=n-r;if(this===t&&r<e&&e<n)for(i=s-1;i>=0;--i)t[i+e]=this[i+r];else if(s<1e3||!u.TYPED_ARRAY_SUPPORT)for(i=0;i<s;++i)t[i+e]=this[i+r];else Uint8Array.prototype.set.call(t,this.subarray(r,r+s),e);return s},u.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),1===t.length){var i=t.charCodeAt(0);i<256&&(t=i)}if(void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!u.isEncoding(n))throw new TypeError("Unknown encoding: "+n)}else"number"==typeof t&&(t&=255);if(e<0||this.length<e||this.length<r)throw new RangeError("Out of range index");if(r<=e)return this;var s;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(s=e;s<r;++s)this[s]=t;else{var a=u.isBuffer(t)?t:M(new u(t,n).toString()),o=a.length;for(s=0;s<r-e;++s)this[s+e]=a[s%o]}return this};var V=/[^+\/0-9A-Za-z-_]/g;function _(t){return t<16?"0"+t.toString(16):t.toString(16)}function M(t,e){var r;e=e||1/0;for(var n=t.length,i=null,s=[],a=0;a<n;++a){if((r=t.charCodeAt(a))>55295&&r<57344){if(!i){if(r>56319){(e-=3)>-1&&s.push(239,191,189);continue}if(a+1===n){(e-=3)>-1&&s.push(239,191,189);continue}i=r;continue}if(r<56320){(e-=3)>-1&&s.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(e-=3)>-1&&s.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;s.push(r)}else if(r<2048){if((e-=2)<0)break;s.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;s.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;s.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return s}function U(t){return n.toByteArray(function(t){if((t=function(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}(t).replace(V,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function K(t,e,r,n){for(var i=0;i<n&&!(i+r>=e.length||i>=t.length);++i)e[i+r]=t[i];return i}}).call(this,r(28))},13:function(t,e,r){var n=r(49),i=r(50),s=r(51);t.exports=function(t,e){return n(t)||i(t,e)||s()}},1330:function(t,e,r){"use strict";r.r(e);var n=r(1),i=r.n(n),s=r(2),a=r.n(s),o=r(4),u=r.n(o),h=r(3),c=r.n(h),l=r(5),f=r.n(l),d=r(85),g=r.n(d),p=r(6),y=function(t){function e(){var t;return i()(this,e),(t=u()(this,c()(e).call(this))).name="Hex to Object Identifier",t.module="PublicKey",t.description="Converts a hexadecimal string into an object identifier (OID).",t.infoURL="https://wikipedia.org/wiki/Object_identifier",t.inputType="string",t.outputType="string",t.args=[],t}return f()(e,t),a()(e,[{key:"run",value:function(t,e){return g.a.KJUR.asn1.ASN1Util.oidHexToInt(t.replace(/\s/g,""))}}]),e}(p.a),v=function(t){function e(){var t;return i()(this,e),(t=u()(this,c()(e).call(this))).name="Hex to PEM",t.module="PublicKey",t.description="Converts a hexadecimal DER (Distinguished Encoding Rules) string into PEM (Privacy Enhanced Mail) format.",t.infoURL="https://wikipedia.org/wiki/Privacy-Enhanced_Mail",t.inputType="string",t.outputType="string",t.args=[{name:"Header string",type:"string",value:"CERTIFICATE"}],t}return f()(e,t),a()(e,[{key:"run",value:function(t,e){return g.a.KJUR.asn1.ASN1Util.getPEMStringFromHex(t.replace(/\s/g,""),e[0])}}]),e}(p.a),m=function(t){function e(){var t;return i()(this,e),(t=u()(this,c()(e).call(this))).name="Object Identifier to Hex",t.module="PublicKey",t.description="Converts an object identifier (OID) into a hexadecimal string.",t.infoURL="https://wikipedia.org/wiki/Object_identifier",t.inputType="string",t.outputType="string",t.args=[],t}return f()(e,t),a()(e,[{key:"run",value:function(t,e){return g.a.KJUR.asn1.ASN1Util.oidIntToHex(t)}}]),e}(p.a),F=function(t){function e(){var t;return i()(this,e),(t=u()(this,c()(e).call(this))).name="PEM to Hex",t.module="PublicKey",t.description="Converts PEM (Privacy Enhanced Mail) format to a hexadecimal DER (Distinguished Encoding Rules) string.",t.infoURL="https://wikipedia.org/wiki/X.690#DER_encoding",t.inputType="string",t.outputType="string",t.args=[],t}return f()(e,t),a()(e,[{key:"run",value:function(t,e){t.indexOf("-----BEGIN")<0&&(t="-----BEGIN CERTIFICATE-----"+t),t.indexOf("-----END")<0&&(t+="-----END CERTIFICATE-----");var r=new g.a.X509;return r.readCertPEM(t),r.hex}}]),e}(p.a),S=r(13),b=r.n(S),x=function(t){function e(){var t;return i()(this,e),(t=u()(this,c()(e).call(this))).name="Parse ASN.1 hex string",t.module="PublicKey",t.description="Abstract Syntax Notation One (ASN.1) is a standard and notation that describes rules and structures for representing, encoding, transmitting, and decoding data in telecommunications and computer networking.<br><br>This operation parses arbitrary ASN.1 data and presents the resulting tree.",t.infoURL="https://wikipedia.org/wiki/Abstract_Syntax_Notation_One",t.inputType="string",t.outputType="string",t.args=[{name:"Starting index",type:"number",value:0},{name:"Truncate octet strings longer than",type:"number",value:32}],t}return f()(e,t),a()(e,[{key:"run",value:function(t,e){var r=b()(e,2),n=r[0],i=r[1];return g.a.ASN1HEX.dump(t.replace(/\s/g,""),{ommitLongOctet:i},n)}}]),e}(p.a),A=r(14),w=r(17);
/**
 * Public key resources.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2016
 * @license Apache-2.0
 */
function E(t,e){var r,n,i,s,a=t.substr(1).replace(/([^\\])\//g,"$1$1/").split(/[^\\]\//),o="",u=0;for(i=0;i<a.length;i++)a[i].length&&(u=(r=a[i].split("=")[0]).length>u?r.length:u);for(i=0;i<a.length;i++)a[i].length&&(r=a[i].split("=")[0],n=a[i].split("=")[1],o+=(s=r.padEnd(u," ")+" = "+n+"\n").padStart(e+s.length," "));return o.slice(0,-1)}function B(t,e,r){t=Object(w.b)(Object(w.a)(t),":"),e*=3;for(var n="",i=0;i<t.length;i+=e){var s=t.slice(i,i+e)+"\n";n+=0===i?s:s.padStart(r+s.length," ")}return n.slice(0,n.length-1)}var C=r(0);
/**
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2016
 * @license Apache-2.0
 */function D(t){return 13===t.length&&(t=(t[0]<"5"?"20":"19")+t),t[6]+t[7]+"/"+t[4]+t[5]+"/"+t[0]+t[1]+t[2]+t[3]+" "+t[8]+t[9]+":"+t[10]+t[11]+":"+t[12]+t[13]}var T=function(t){function e(){var t;return i()(this,e),(t=u()(this,c()(e).call(this))).name="Parse X.509 certificate",t.module="PublicKey",t.description="X.509 is an ITU-T standard for a public key infrastructure (PKI) and Privilege Management Infrastructure (PMI). It is commonly involved with SSL/TLS security.<br><br>This operation displays the contents of a certificate in a human readable format, similar to the openssl command line tool.<br><br>Tags: X509, server hello, handshake",t.infoURL="https://wikipedia.org/wiki/X.509",t.inputType="string",t.outputType="string",t.args=[{name:"Input format",type:"option",value:["PEM","DER Hex","Base64","Raw"]}],t.patterns=[{match:"^-+BEGIN CERTIFICATE-+\\r?\\n[\\da-z+/\\n\\r]+-+END CERTIFICATE-+\\r?\\n?$",flags:"i",args:["PEM"]}],t}return f()(e,t),a()(e,[{key:"run",value:function(t,e){if(!t.length)return"No input";var r=new g.a.X509;switch(e[0]){case"DER Hex":t=t.replace(/\s/g,""),r.readCertHex(t);break;case"PEM":r.readCertPEM(t);break;case"Base64":r.readCertHex(Object(w.b)(Object(A.a)(t,null,"byteArray"),""));break;case"Raw":r.readCertHex(Object(w.b)(C.b.strToByteArray(t),""));break;default:throw"Undefined input format"}var n=r.getSerialNumberHex(),i=r.getIssuerString(),s=r.getSubjectString(),a=r.getPublicKey(),o=[],u=r.getSignatureValueHex(),h="",c="",l="";o.push({key:"Algorithm",value:a.type}),"EC"===a.type?(o.push({key:"Curve Name",value:a.curveName}),o.push({key:"Length",value:(new g.a.BigInteger(a.pubKeyHex,16).bitLength()-3)/2+" bits"}),o.push({key:"pub",value:B(a.pubKeyHex,16,18)})):"DSA"===a.type?(o.push({key:"pub",value:B(a.y.toString(16),16,18)}),o.push({key:"P",value:B(a.p.toString(16),16,18)}),o.push({key:"Q",value:B(a.q.toString(16),16,18)}),o.push({key:"G",value:B(a.g.toString(16),16,18)})):a.e?(o.push({key:"Length",value:a.n.bitLength()+" bits"}),o.push({key:"Modulus",value:B(a.n.toString(16),16,18)}),o.push({key:"Exponent",value:a.e+" (0x"+a.e.toString(16)+")"})):o.push({key:"Error",value:"Unknown Public Key type"});for(var f=0;f<o.length;f++)h+=`  ${o[f].key}:${(o[f].value+"\n").padStart(18-(o[f].key.length+3)+o[f].value.length+1," ")}`;var d=!1;try{d=0===g.a.ASN1HEX.dump(u).indexOf("SEQUENCE")}catch(t){}c=d?`  r:              ${B(g.a.ASN1HEX.getV(u,4),16,18)}\n  s:              ${B(g.a.ASN1HEX.getV(u,48),16,18)}`:`  Signature:      ${B(u,16,18)}`;try{l=r.getInfo().split("X509v3 Extensions:\n")[1].split("signature")[0]}catch(t){}var p=E(i,2),y=D(r.getNotBefore()),v=D(r.getNotAfter()),m=E(s,2);return`Version:          ${r.version} (0x${C.b.hex(r.version-1)})\nSerial number:    ${new g.a.BigInteger(n,16).toString()} (0x${n})\nAlgorithm ID:     ${r.getSignatureAlgorithmField()}\nValidity\n  Not Before:     ${y} (dd-mm-yyyy hh:mm:ss) (${r.getNotBefore()})\n  Not After:      ${v} (dd-mm-yyyy hh:mm:ss) (${r.getNotAfter()})\nIssuer\n${p}\nSubject\n${m}\nPublic Key\n${h.slice(0,-1)}\nCertificate Signature\n  Algorithm:      ${r.getSignatureAlgorithmName()}\n${c}\n\nExtensions\n${l}`}}]),e}(p.a),I="undefined"==typeof self?{}:self.OpModules||{};
/**
* THIS FILE IS AUTOMATICALLY GENERATED BY src/core/config/scripts/generateConfig.mjs
*
* <AUTHOR> [<EMAIL>]
* @copyright Crown Copyright 2019
* @license Apache-2.0
*/I.PublicKey={"Hex to Object Identifier":y,"Hex to PEM":v,"Object Identifier to Hex":m,"PEM to Hex":F,"Parse ASN.1 hex string":x,"Parse X.509 certificate":T};e.default=I},14:function(t,e,r){"use strict";r.d(e,"b",function(){return i}),r.d(e,"a",function(){return s});var n=r(0);
/**
 * Base64 functions.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2016
 * @license Apache-2.0
 */function i(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"A-Za-z0-9+/=";if(!t)return"";t instanceof ArrayBuffer&&(t=new Uint8Array(t)),"string"==typeof t&&(t=n.b.strToByteArray(t)),e=n.b.expandAlphRange(e).join("");for(var r,i,s,a,o,u,h,c="",l=0;l<t.length;)a=(r=t[l++])>>2,o=(3&r)<<4|(i=t[l++])>>4,u=(15&i)<<2|(s=t[l++])>>6,h=63&s,isNaN(i)?u=h=64:isNaN(s)&&(h=64),c+=e.charAt(a)+e.charAt(o)+e.charAt(u)+e.charAt(h);return c}function s(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"A-Za-z0-9+/=",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"string",i=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];if(!t)return"string"===r?"":[];e=e||"A-Za-z0-9+/=",e=n.b.expandAlphRange(e).join("");var s,a,o,u,h,c,l=[],f=0;if(i){var d=new RegExp("[^"+e.replace(/[[\]\\\-^$]/g,"\\$&")+"]","g");t=t.replace(d,"")}for(;f<t.length;)s=e.indexOf(t.charAt(f++))<<2|(u=-1===(u=e.indexOf(t.charAt(f++)||"="))?64:u)>>4,a=(15&u)<<4|(h=-1===(h=e.indexOf(t.charAt(f++)||"="))?64:h)>>2,o=(3&h)<<6|(c=-1===(c=e.indexOf(t.charAt(f++)||"="))?64:c),l.push(s),64!==h&&l.push(a),64!==c&&l.push(o);return"string"===r?n.b.byteArrayToUtf8(l):l}},15:function(t,e,r){"use strict";var n=r(7),i=r.n(n),s=r(11),a=r.n(s),o=r(1),u=r.n(o),h=r(2),c=r.n(h),l=r(0),f=r(4),d=r.n(f),g=r(3),p=r.n(g),y=r(25),v=r.n(y),m=r(5),F=r.n(m);
/**
 * Custom error type for handling Dish type errors.
 * i.e. where the Dish cannot be successfully translated between types
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 */
var S=function(t){function e(){var t;u()(this,e);for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return(t=d()(this,p()(e).call(this,...n))).type="DishError",Error.captureStackTrace&&Error.captureStackTrace(v()(t),e),t}return F()(e,t),e}(function(t){function e(){var e=Reflect.construct(t,Array.from(arguments));return Object.setPrototypeOf(e,Object.getPrototypeOf(this)),e}return e.prototype=Object.create(t.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t,e}(Error)),b=r(16),x=r.n(b),A=r(18),w=r(26),E=r.n(w),B=function(){function t(){u()(this,t)}return c()(t,null,[{key:"checkForValue",value:function(t){if(void 0===t)throw new Error("only use translation methods with .bind")}},{key:"toArrayBuffer",value:function(){throw new Error("toArrayBuffer has not been implemented")}},{key:"fromArrayBuffer",value:function(){arguments.length>0&&void 0!==arguments[0]&&arguments[0];throw new Error("fromArrayBuffer has not been implemented")}}]),t}(),C=function(t){function e(){return u()(this,e),d()(this,p()(e).apply(this,arguments))}return F()(e,t),c()(e,null,[{key:"toArrayBuffer",value:function(){e.checkForValue(this.value),this.value=new Uint8Array(this.value).buffer}},{key:"fromArrayBuffer",value:function(){e.checkForValue(this.value),this.value=Array.prototype.slice.call(new Uint8Array(this.value))}}]),e}(B),D=function(t){function e(){return u()(this,e),d()(this,p()(e).apply(this,arguments))}return F()(e,t),c()(e,null,[{key:"toArrayBuffer",value:function(){e.checkForValue(this.value),this.value=x.a.isBigNumber(this.value)?l.b.strToArrayBuffer(this.value.toFixed()):new ArrayBuffer}},{key:"fromArrayBuffer",value:function(t){e.checkForValue(this.value);try{this.value=new x.a(l.b.arrayBufferToStr(this.value,!t))}catch(t){this.value=new x.a(NaN)}}}]),e}(B),T=function(t){function e(){return u()(this,e),d()(this,p()(e).apply(this,arguments))}return F()(e,t),c()(e,null,[{key:"toArrayBuffer",value:function(){var t=this;if(e.checkForValue(this.value),!Object(l.c)())return new Promise(function(e,r){l.b.readFile(t.value).then(function(e){return t.value=e.buffer}).then(e).catch(r)});this.value=l.b.readFileSync(this.value)}},{key:"fromArrayBuffer",value:function(){e.checkForValue(this.value),this.value=new File(this.value,"unknown")}}]),e}(B),I=function(t){function e(){return u()(this,e),d()(this,p()(e).apply(this,arguments))}return F()(e,t),c()(e,null,[{key:"toArrayBuffer",value:function(){e.checkForValue(this.value),this.value=this.value?l.b.strToArrayBuffer(this.value):new ArrayBuffer}},{key:"fromArrayBuffer",value:function(t){e.checkForValue(this.value),this.value=this.value?l.b.arrayBufferToStr(this.value,!t):""}}]),e}(B),P=function(t){function e(){return u()(this,e),d()(this,p()(e).apply(this,arguments))}return F()(e,t),c()(e,null,[{key:"toArrayBuffer",value:function(){e.checkForValue(this.value),this.value=this.value?l.b.strToArrayBuffer(l.b.unescapeHtml(l.b.stripHtmlTags(this.value,!0))):new ArrayBuffer}}]),e}(I),R=function(t){function e(){return u()(this,e),d()(this,p()(e).apply(this,arguments))}return F()(e,t),c()(e,null,[{key:"toArrayBuffer",value:function(){e.checkForValue(this.value),this.value=this.value?l.b.strToArrayBuffer(JSON.stringify(this.value,null,4)):new ArrayBuffer}},{key:"fromArrayBuffer",value:function(t){e.checkForValue(this.value),this.value=JSON.parse(l.b.arrayBufferToStr(this.value,!t))}}]),e}(B),H=function(t){function e(){return u()(this,e),d()(this,p()(e).apply(this,arguments))}return F()(e,t),c()(e,null,[{key:"toArrayBuffer",value:function(){e.checkForValue(this.value),Object(l.c)()&&(this.value=this.value.map(function(t){return Uint8Array.from(t.data)})),this.value=e.concatenateTypedArrays(...this.value).buffer}},{key:"fromArrayBuffer",value:function(){e.checkForValue(this.value),this.value=[new File(this.value,"unknown")]}},{key:"concatenateTypedArrays",value:function(){for(var t=0,e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];for(var i=0,s=r;i<s.length;i++){var a=s[i];t+=a.length}for(var o=new Uint8Array(t),u=0,h=0,c=r;h<c.length;h++){var l=c[h];o.set(l,u),u+=l.length}return o}}]),e}(B),N=function(t){function e(){return u()(this,e),d()(this,p()(e).apply(this,arguments))}return F()(e,t),c()(e,null,[{key:"toArrayBuffer",value:function(){e.checkForValue(this.value),this.value="number"==typeof this.value?l.b.strToArrayBuffer(this.value.toString()):new ArrayBuffer}},{key:"fromArrayBuffer",value:function(t){e.checkForValue(this.value),this.value=this.value?parseFloat(l.b.arrayBufferToStr(this.value,!t)):0}}]),e}(B),O=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(u()(this,t),this.value=new ArrayBuffer(0),this.type=t.ARRAY_BUFFER,e&&Object.prototype.hasOwnProperty.call(e,"value")&&Object.prototype.hasOwnProperty.call(e,"type"))this.set(e.value,e.type);else if(e&&null!==r)this.set(e,r);else if(e){var n=t.typeEnum(e.constructor.name);this.set(e,n)}}var e;return c()(t,[{key:"get",value:function(e){var r=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return"string"==typeof e&&(e=t.typeEnum(e)),this.type!==e?Object(l.c)()?(this._translate(e,n),this.value):new Promise(function(t,i){r._translate(e,n).then(function(){t(r.value)}).catch(i)}):this.value}},{key:"set",value:function(e,r){if("string"==typeof r&&(r=t.typeEnum(r)),E.a.debug("Dish type: "+t.enumLookup(r)),this.value=e,this.type=r,!this.valid()){var n=l.b.truncate(JSON.stringify(this.value),25);throw new S(`Data is not a valid ${t.enumLookup(r)}: ${n}`)}}},{key:"presentAs",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.clone();return r.get(t,e)}},{key:"detectDishType",value:function(){var t=new Uint8Array(this.value.slice(0,2048)),e=Object(A.a)(t);return e.length&&e[0].mime&&"text/plain"!==!e[0].mime?e[0].mime:null}},{key:"getTitle",value:(e=a()(i.a.mark(function e(r){var n,s;return i.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:n="",e.t0=this.type,e.next=e.t0===t.FILE?4:e.t0===t.LIST_FILE?6:e.t0===t.JSON?8:e.t0===t.NUMBER?10:e.t0===t.BIG_NUMBER?10:e.t0===t.ARRAY_BUFFER?12:e.t0===t.BYTE_ARRAY?12:15;break;case 4:return n=this.value.name,e.abrupt("break",26);case 6:return n=`${this.value.length} file(s)`,e.abrupt("break",26);case 8:return n="application/json",e.abrupt("break",26);case 10:return n=this.value.toString(),e.abrupt("break",26);case 12:if(null===(n=this.detectDishType())){e.next=15;break}return e.abrupt("break",26);case 15:return e.prev=15,(s=this.clone()).value=s.value.slice(0,256),e.next=20,s.get(t.STRING);case 20:n=e.sent,e.next=26;break;case 23:e.prev=23,e.t1=e.catch(15),E.a.error(`${t.enumLookup(this.type)} cannot be sliced. ${e.t1}`);case 26:return e.abrupt("return",n.slice(0,r));case 27:case"end":return e.stop()}},e,this,[[15,23]])})),function(t){return e.apply(this,arguments)})},{key:"valid",value:function(){switch(this.type){case t.BYTE_ARRAY:if(!(this.value instanceof Uint8Array||this.value instanceof Array))return!1;for(var e=0;e<this.value.length;e++)if("number"!=typeof this.value[e]||this.value[e]<0||this.value[e]>255)return!1;return!0;case t.STRING:case t.HTML:return"string"==typeof this.value;case t.NUMBER:return"number"==typeof this.value;case t.ARRAY_BUFFER:return this.value instanceof ArrayBuffer;case t.BIG_NUMBER:if(x.a.isBigNumber(this.value))return!0;if(Object.keys(this.value).sort().equals(["c","e","s"])){var r=new x.a;return r.c=this.value.c,r.e=this.value.e,r.s=this.value.s,this.value=r,!0}return!1;case t.JSON:return!0;case t.FILE:return this.value instanceof File;case t.LIST_FILE:return this.value instanceof Array&&this.value.reduce(function(t,e){return t&&e instanceof File},!0);default:return!1}}},{key:"clone",value:function(){var e=new t;switch(this.type){case t.STRING:case t.HTML:case t.NUMBER:case t.BIG_NUMBER:e.set(this.value,this.type);break;case t.BYTE_ARRAY:case t.JSON:e.set(JSON.parse(JSON.stringify(this.value)),this.type);break;case t.ARRAY_BUFFER:e.set(this.value.slice(0),this.type);break;case t.FILE:e.set(new File([this.value],this.value.name,{type:this.value.type,lastModified:this.value.lastModified}),this.type);break;case t.LIST_FILE:e.set(this.value.map(function(t){return new File([t],t.name,{type:t.type,lastModified:t.lastModified})}),this.type);break;default:throw new S("Cannot clone Dish, unknown type")}return e}},{key:"_translate",value:function(e){var r=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(E.a.debug(`Translating Dish from ${t.enumLookup(this.type)} to ${t.enumLookup(e)}`),!Object(l.c)())return new Promise(function(n,i){r._toArrayBuffer().then(function(){return r.type=t.ARRAY_BUFFER}).then(function(){r._fromArrayBuffer(e),n()}).catch(i)});this._toArrayBuffer(),this.type=t.ARRAY_BUFFER,this._fromArrayBuffer(e,n)}},{key:"_toArrayBuffer",value:function(){var e=this,r={browser:{[t.STRING]:function(){return Promise.resolve(I.toArrayBuffer.bind(e)())},[t.NUMBER]:function(){return Promise.resolve(N.toArrayBuffer.bind(e)())},[t.HTML]:function(){return Promise.resolve(P.toArrayBuffer.bind(e)())},[t.ARRAY_BUFFER]:function(){return Promise.resolve()},[t.BIG_NUMBER]:function(){return Promise.resolve(D.toArrayBuffer.bind(e)())},[t.JSON]:function(){return Promise.resolve(R.toArrayBuffer.bind(e)())},[t.FILE]:function(){return T.toArrayBuffer.bind(e)()},[t.LIST_FILE]:function(){return Promise.resolve(H.toArrayBuffer.bind(e)())},[t.BYTE_ARRAY]:function(){return Promise.resolve(C.toArrayBuffer.bind(e)())}},node:{[t.STRING]:function(){return I.toArrayBuffer.bind(e)()},[t.NUMBER]:function(){return N.toArrayBuffer.bind(e)()},[t.HTML]:function(){return P.toArrayBuffer.bind(e)()},[t.ARRAY_BUFFER]:function(){},[t.BIG_NUMBER]:function(){return D.toArrayBuffer.bind(e)()},[t.JSON]:function(){return R.toArrayBuffer.bind(e)()},[t.FILE]:function(){return T.toArrayBuffer.bind(e)()},[t.LIST_FILE]:function(){return H.toArrayBuffer.bind(e)()},[t.BYTE_ARRAY]:function(){return C.toArrayBuffer.bind(e)()}}};try{return r[Object(l.c)()?"node":"browser"][this.type]()}catch(e){throw new S(`Error translating from ${t.enumLookup(this.type)} to ArrayBuffer: ${e}`)}}},{key:"_fromArrayBuffer",value:function(e,r){var n=this,i={[t.STRING]:function(){return I.fromArrayBuffer.bind(n)(r)},[t.NUMBER]:function(){return N.fromArrayBuffer.bind(n)(r)},[t.HTML]:function(){return P.fromArrayBuffer.bind(n)(r)},[t.ARRAY_BUFFER]:function(){},[t.BIG_NUMBER]:function(){return D.fromArrayBuffer.bind(n)(r)},[t.JSON]:function(){return R.fromArrayBuffer.bind(n)(r)},[t.FILE]:function(){return T.fromArrayBuffer.bind(n)()},[t.LIST_FILE]:function(){return H.fromArrayBuffer.bind(n)()},[t.BYTE_ARRAY]:function(){return C.fromArrayBuffer.bind(n)()}};try{i[e](),this.type=e}catch(r){throw new S(`Error translating from ArrayBuffer to ${t.enumLookup(e)}: ${r}`)}}},{key:"size",get:function(){switch(this.type){case t.BYTE_ARRAY:case t.STRING:case t.HTML:return this.value.length;case t.NUMBER:case t.BIG_NUMBER:return this.value.toString().length;case t.ARRAY_BUFFER:return this.value.byteLength;case t.JSON:return JSON.stringify(this.value).length;case t.FILE:return this.value.size;case t.LIST_FILE:return this.value.reduce(function(t,e){return t+e.size},0);default:return-1}}}],[{key:"typeEnum",value:function(e){switch(e.toLowerCase()){case"bytearray":case"byte array":return t.BYTE_ARRAY;case"string":return t.STRING;case"number":return t.NUMBER;case"html":return t.HTML;case"arraybuffer":case"array buffer":return t.ARRAY_BUFFER;case"bignumber":case"big number":return t.BIG_NUMBER;case"json":case"object":return t.JSON;case"file":return t.FILE;case"list<file>":return t.LIST_FILE;default:throw new S("Invalid data type string. No matching enum.")}}},{key:"enumLookup",value:function(e){switch(e){case t.BYTE_ARRAY:return"byteArray";case t.STRING:return"string";case t.NUMBER:return"number";case t.HTML:return"html";case t.ARRAY_BUFFER:return"ArrayBuffer";case t.BIG_NUMBER:return"BigNumber";case t.JSON:return"JSON";case t.FILE:return"File";case t.LIST_FILE:return"List<File>";default:throw new S("Invalid data type enum. No matching type.")}}}]),t}();O.BYTE_ARRAY=0,O.STRING=1,O.NUMBER=2,O.HTML=3,O.ARRAY_BUFFER=4,O.BIG_NUMBER=5,O.JSON=6,O.FILE=7,O.LIST_FILE=8;e.a=O},16:function(t,e,r){var n;!function(i){"use strict";var s,a=/^-?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?$/i,o=Math.ceil,u=Math.floor,h="[BigNumber Error] ",c=h+"Number primitive has more than 15 significant digits: ",l=1e14,f=14,d=9007199254740991,g=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9,1e10,1e11,1e12,1e13],p=1e7,y=1e9;function v(t){var e=0|t;return t>0||t===e?e:e-1}function m(t){for(var e,r,n=1,i=t.length,s=t[0]+"";n<i;){for(e=t[n++]+"",r=f-e.length;r--;e="0"+e);s+=e}for(i=s.length;48===s.charCodeAt(--i););return s.slice(0,i+1||1)}function F(t,e){var r,n,i=t.c,s=e.c,a=t.s,o=e.s,u=t.e,h=e.e;if(!a||!o)return null;if(r=i&&!i[0],n=s&&!s[0],r||n)return r?n?0:-o:a;if(a!=o)return a;if(r=a<0,n=u==h,!i||!s)return n?0:!i^r?1:-1;if(!n)return u>h^r?1:-1;for(o=(u=i.length)<(h=s.length)?u:h,a=0;a<o;a++)if(i[a]!=s[a])return i[a]>s[a]^r?1:-1;return u==h?0:u>h^r?1:-1}function S(t,e,r,n){if(t<e||t>r||t!==u(t))throw Error(h+(n||"Argument")+("number"==typeof t?t<e||t>r?" out of range: ":" not an integer: ":" not a primitive number: ")+String(t))}function b(t){var e=t.c.length-1;return v(t.e/f)==e&&t.c[e]%2!=0}function x(t,e){return(t.length>1?t.charAt(0)+"."+t.slice(1):t)+(e<0?"e":"e+")+e}function A(t,e,r){var n,i;if(e<0){for(i=r+".";++e;i+=r);t=i+t}else if(++e>(n=t.length)){for(i=r,e-=n;--e;i+=r);t+=i}else e<n&&(t=t.slice(0,e)+"."+t.slice(e));return t}(s=function t(e){var r,n,i,s,w,E,B,C,D,T=U.prototype={constructor:U,toString:null,valueOf:null},I=new U(1),P=20,R=4,H=-7,N=21,O=-1e7,k=1e7,j=!1,L=1,V=0,_={prefix:"",groupSize:3,secondaryGroupSize:0,groupSeparator:",",decimalSeparator:".",fractionGroupSize:0,fractionGroupSeparator:" ",suffix:""},M="0123456789abcdefghijklmnopqrstuvwxyz";function U(t,e){var r,s,o,h,l,g,p,y,v=this;if(!(v instanceof U))return new U(t,e);if(null==e){if(t&&!0===t._isBigNumber)return v.s=t.s,void(!t.c||t.e>k?v.c=v.e=null:t.e<O?v.c=[v.e=0]:(v.e=t.e,v.c=t.c.slice()));if((g="number"==typeof t)&&0*t==0){if(v.s=1/t<0?(t=-t,-1):1,t===~~t){for(h=0,l=t;l>=10;l/=10,h++);return void(h>k?v.c=v.e=null:(v.e=h,v.c=[t]))}y=String(t)}else{if(!a.test(y=String(t)))return i(v,y,g);v.s=45==y.charCodeAt(0)?(y=y.slice(1),-1):1}(h=y.indexOf("."))>-1&&(y=y.replace(".","")),(l=y.search(/e/i))>0?(h<0&&(h=l),h+=+y.slice(l+1),y=y.substring(0,l)):h<0&&(h=y.length)}else{if(S(e,2,M.length,"Base"),10==e)return G(v=new U(t),P+v.e+1,R);if(y=String(t),g="number"==typeof t){if(0*t!=0)return i(v,y,g,e);if(v.s=1/t<0?(y=y.slice(1),-1):1,U.DEBUG&&y.replace(/^0\.0*|\./,"").length>15)throw Error(c+t)}else v.s=45===y.charCodeAt(0)?(y=y.slice(1),-1):1;for(r=M.slice(0,e),h=l=0,p=y.length;l<p;l++)if(r.indexOf(s=y.charAt(l))<0){if("."==s){if(l>h){h=p;continue}}else if(!o&&(y==y.toUpperCase()&&(y=y.toLowerCase())||y==y.toLowerCase()&&(y=y.toUpperCase()))){o=!0,l=-1,h=0;continue}return i(v,String(t),g,e)}g=!1,(h=(y=n(y,e,10,v.s)).indexOf("."))>-1?y=y.replace(".",""):h=y.length}for(l=0;48===y.charCodeAt(l);l++);for(p=y.length;48===y.charCodeAt(--p););if(y=y.slice(l,++p)){if(p-=l,g&&U.DEBUG&&p>15&&(t>d||t!==u(t)))throw Error(c+v.s*t);if((h=h-l-1)>k)v.c=v.e=null;else if(h<O)v.c=[v.e=0];else{if(v.e=h,v.c=[],l=(h+1)%f,h<0&&(l+=f),l<p){for(l&&v.c.push(+y.slice(0,l)),p-=f;l<p;)v.c.push(+y.slice(l,l+=f));l=f-(y=y.slice(l)).length}else l-=p;for(;l--;y+="0");v.c.push(+y)}}else v.c=[v.e=0]}function K(t,e,r,n){var i,s,a,o,u;if(null==r?r=R:S(r,0,8),!t.c)return t.toString();if(i=t.c[0],a=t.e,null==e)u=m(t.c),u=1==n||2==n&&(a<=H||a>=N)?x(u,a):A(u,a,"0");else if(s=(t=G(new U(t),e,r)).e,o=(u=m(t.c)).length,1==n||2==n&&(e<=s||s<=H)){for(;o<e;u+="0",o++);u=x(u,s)}else if(e-=a,u=A(u,s,"0"),s+1>o){if(--e>0)for(u+=".";e--;u+="0");}else if((e+=s-o)>0)for(s+1==o&&(u+=".");e--;u+="0");return t.s<0&&i?"-"+u:u}function q(t,e){for(var r,n=1,i=new U(t[0]);n<t.length;n++){if(!(r=new U(t[n])).s){i=r;break}e.call(i,r)&&(i=r)}return i}function z(t,e,r){for(var n=1,i=e.length;!e[--i];e.pop());for(i=e[0];i>=10;i/=10,n++);return(r=n+r*f-1)>k?t.c=t.e=null:r<O?t.c=[t.e=0]:(t.e=r,t.c=e),t}function G(t,e,r,n){var i,s,a,h,c,d,p,y=t.c,v=g;if(y){t:{for(i=1,h=y[0];h>=10;h/=10,i++);if((s=e-i)<0)s+=f,a=e,p=(c=y[d=0])/v[i-a-1]%10|0;else if((d=o((s+1)/f))>=y.length){if(!n)break t;for(;y.length<=d;y.push(0));c=p=0,i=1,a=(s%=f)-f+1}else{for(c=h=y[d],i=1;h>=10;h/=10,i++);p=(a=(s%=f)-f+i)<0?0:c/v[i-a-1]%10|0}if(n=n||e<0||null!=y[d+1]||(a<0?c:c%v[i-a-1]),n=r<4?(p||n)&&(0==r||r==(t.s<0?3:2)):p>5||5==p&&(4==r||n||6==r&&(s>0?a>0?c/v[i-a]:0:y[d-1])%10&1||r==(t.s<0?8:7)),e<1||!y[0])return y.length=0,n?(e-=t.e+1,y[0]=v[(f-e%f)%f],t.e=-e||0):y[0]=t.e=0,t;if(0==s?(y.length=d,h=1,d--):(y.length=d+1,h=v[f-s],y[d]=a>0?u(c/v[i-a]%v[a])*h:0),n)for(;;){if(0==d){for(s=1,a=y[0];a>=10;a/=10,s++);for(a=y[0]+=h,h=1;a>=10;a/=10,h++);s!=h&&(t.e++,y[0]==l&&(y[0]=1));break}if(y[d]+=h,y[d]!=l)break;y[d--]=0,h=1}for(s=y.length;0===y[--s];y.pop());}t.e>k?t.c=t.e=null:t.e<O&&(t.c=[t.e=0])}return t}function Y(t){var e,r=t.e;return null===r?t.toString():(e=m(t.c),e=r<=H||r>=N?x(e,r):A(e,r,"0"),t.s<0?"-"+e:e)}return U.clone=t,U.ROUND_UP=0,U.ROUND_DOWN=1,U.ROUND_CEIL=2,U.ROUND_FLOOR=3,U.ROUND_HALF_UP=4,U.ROUND_HALF_DOWN=5,U.ROUND_HALF_EVEN=6,U.ROUND_HALF_CEIL=7,U.ROUND_HALF_FLOOR=8,U.EUCLID=9,U.config=U.set=function(t){var e,r;if(null!=t){if("object"!=typeof t)throw Error(h+"Object expected: "+t);if(t.hasOwnProperty(e="DECIMAL_PLACES")&&(S(r=t[e],0,y,e),P=r),t.hasOwnProperty(e="ROUNDING_MODE")&&(S(r=t[e],0,8,e),R=r),t.hasOwnProperty(e="EXPONENTIAL_AT")&&((r=t[e])&&r.pop?(S(r[0],-y,0,e),S(r[1],0,y,e),H=r[0],N=r[1]):(S(r,-y,y,e),H=-(N=r<0?-r:r))),t.hasOwnProperty(e="RANGE"))if((r=t[e])&&r.pop)S(r[0],-y,-1,e),S(r[1],1,y,e),O=r[0],k=r[1];else{if(S(r,-y,y,e),!r)throw Error(h+e+" cannot be zero: "+r);O=-(k=r<0?-r:r)}if(t.hasOwnProperty(e="CRYPTO")){if((r=t[e])!==!!r)throw Error(h+e+" not true or false: "+r);if(r){if("undefined"==typeof crypto||!crypto||!crypto.getRandomValues&&!crypto.randomBytes)throw j=!r,Error(h+"crypto unavailable");j=r}else j=r}if(t.hasOwnProperty(e="MODULO_MODE")&&(S(r=t[e],0,9,e),L=r),t.hasOwnProperty(e="POW_PRECISION")&&(S(r=t[e],0,y,e),V=r),t.hasOwnProperty(e="FORMAT")){if("object"!=typeof(r=t[e]))throw Error(h+e+" not an object: "+r);_=r}if(t.hasOwnProperty(e="ALPHABET")){if("string"!=typeof(r=t[e])||/^.$|[+-.\s]|(.).*\1/.test(r))throw Error(h+e+" invalid: "+r);M=r}}return{DECIMAL_PLACES:P,ROUNDING_MODE:R,EXPONENTIAL_AT:[H,N],RANGE:[O,k],CRYPTO:j,MODULO_MODE:L,POW_PRECISION:V,FORMAT:_,ALPHABET:M}},U.isBigNumber=function(t){if(!t||!0!==t._isBigNumber)return!1;if(!U.DEBUG)return!0;var e,r,n=t.c,i=t.e,s=t.s;t:if("[object Array]"=={}.toString.call(n)){if((1===s||-1===s)&&i>=-y&&i<=y&&i===u(i)){if(0===n[0]){if(0===i&&1===n.length)return!0;break t}if((e=(i+1)%f)<1&&(e+=f),String(n[0]).length==e){for(e=0;e<n.length;e++)if((r=n[e])<0||r>=l||r!==u(r))break t;if(0!==r)return!0}}}else if(null===n&&null===i&&(null===s||1===s||-1===s))return!0;throw Error(h+"Invalid BigNumber: "+t)},U.maximum=U.max=function(){return q(arguments,T.lt)},U.minimum=U.min=function(){return q(arguments,T.gt)},U.random=(s=9007199254740992*Math.random()&2097151?function(){return u(9007199254740992*Math.random())}:function(){return 8388608*(1073741824*Math.random()|0)+(8388608*Math.random()|0)},function(t){var e,r,n,i,a,c=0,l=[],d=new U(I);if(null==t?t=P:S(t,0,y),i=o(t/f),j)if(crypto.getRandomValues){for(e=crypto.getRandomValues(new Uint32Array(i*=2));c<i;)(a=131072*e[c]+(e[c+1]>>>11))>=9e15?(r=crypto.getRandomValues(new Uint32Array(2)),e[c]=r[0],e[c+1]=r[1]):(l.push(a%1e14),c+=2);c=i/2}else{if(!crypto.randomBytes)throw j=!1,Error(h+"crypto unavailable");for(e=crypto.randomBytes(i*=7);c<i;)(a=281474976710656*(31&e[c])+1099511627776*e[c+1]+4294967296*e[c+2]+16777216*e[c+3]+(e[c+4]<<16)+(e[c+5]<<8)+e[c+6])>=9e15?crypto.randomBytes(7).copy(e,c):(l.push(a%1e14),c+=7);c=i/7}if(!j)for(;c<i;)(a=s())<9e15&&(l[c++]=a%1e14);for(i=l[--c],t%=f,i&&t&&(a=g[f-t],l[c]=u(i/a)*a);0===l[c];l.pop(),c--);if(c<0)l=[n=0];else{for(n=-1;0===l[0];l.splice(0,1),n-=f);for(c=1,a=l[0];a>=10;a/=10,c++);c<f&&(n-=f-c)}return d.e=n,d.c=l,d}),U.sum=function(){for(var t=1,e=arguments,r=new U(e[0]);t<e.length;)r=r.plus(e[t++]);return r},n=function(){function t(t,e,r,n){for(var i,s,a=[0],o=0,u=t.length;o<u;){for(s=a.length;s--;a[s]*=e);for(a[0]+=n.indexOf(t.charAt(o++)),i=0;i<a.length;i++)a[i]>r-1&&(null==a[i+1]&&(a[i+1]=0),a[i+1]+=a[i]/r|0,a[i]%=r)}return a.reverse()}return function(e,n,i,s,a){var o,u,h,c,l,f,d,g,p=e.indexOf("."),y=P,v=R;for(p>=0&&(c=V,V=0,e=e.replace(".",""),f=(g=new U(n)).pow(e.length-p),V=c,g.c=t(A(m(f.c),f.e,"0"),10,i,"0123456789"),g.e=g.c.length),h=c=(d=t(e,n,i,a?(o=M,"0123456789"):(o="0123456789",M))).length;0==d[--c];d.pop());if(!d[0])return o.charAt(0);if(p<0?--h:(f.c=d,f.e=h,f.s=s,d=(f=r(f,g,y,v,i)).c,l=f.r,h=f.e),p=d[u=h+y+1],c=i/2,l=l||u<0||null!=d[u+1],l=v<4?(null!=p||l)&&(0==v||v==(f.s<0?3:2)):p>c||p==c&&(4==v||l||6==v&&1&d[u-1]||v==(f.s<0?8:7)),u<1||!d[0])e=l?A(o.charAt(1),-y,o.charAt(0)):o.charAt(0);else{if(d.length=u,l)for(--i;++d[--u]>i;)d[u]=0,u||(++h,d=[1].concat(d));for(c=d.length;!d[--c];);for(p=0,e="";p<=c;e+=o.charAt(d[p++]));e=A(e,h,o.charAt(0))}return e}}(),r=function(){function t(t,e,r){var n,i,s,a,o=0,u=t.length,h=e%p,c=e/p|0;for(t=t.slice();u--;)o=((i=h*(s=t[u]%p)+(n=c*s+(a=t[u]/p|0)*h)%p*p+o)/r|0)+(n/p|0)+c*a,t[u]=i%r;return o&&(t=[o].concat(t)),t}function e(t,e,r,n){var i,s;if(r!=n)s=r>n?1:-1;else for(i=s=0;i<r;i++)if(t[i]!=e[i]){s=t[i]>e[i]?1:-1;break}return s}function r(t,e,r,n){for(var i=0;r--;)t[r]-=i,i=t[r]<e[r]?1:0,t[r]=i*n+t[r]-e[r];for(;!t[0]&&t.length>1;t.splice(0,1));}return function(n,i,s,a,o){var h,c,d,g,p,y,m,F,S,b,x,A,w,E,B,C,D,T=n.s==i.s?1:-1,I=n.c,P=i.c;if(!(I&&I[0]&&P&&P[0]))return new U(n.s&&i.s&&(I?!P||I[0]!=P[0]:P)?I&&0==I[0]||!P?0*T:T/0:NaN);for(S=(F=new U(T)).c=[],T=s+(c=n.e-i.e)+1,o||(o=l,c=v(n.e/f)-v(i.e/f),T=T/f|0),d=0;P[d]==(I[d]||0);d++);if(P[d]>(I[d]||0)&&c--,T<0)S.push(1),g=!0;else{for(E=I.length,C=P.length,d=0,T+=2,(p=u(o/(P[0]+1)))>1&&(P=t(P,p,o),I=t(I,p,o),C=P.length,E=I.length),w=C,x=(b=I.slice(0,C)).length;x<C;b[x++]=0);D=P.slice(),D=[0].concat(D),B=P[0],P[1]>=o/2&&B++;do{if(p=0,(h=e(P,b,C,x))<0){if(A=b[0],C!=x&&(A=A*o+(b[1]||0)),(p=u(A/B))>1)for(p>=o&&(p=o-1),m=(y=t(P,p,o)).length,x=b.length;1==e(y,b,m,x);)p--,r(y,C<m?D:P,m,o),m=y.length,h=1;else 0==p&&(h=p=1),m=(y=P.slice()).length;if(m<x&&(y=[0].concat(y)),r(b,y,x,o),x=b.length,-1==h)for(;e(P,b,C,x)<1;)p++,r(b,C<x?D:P,x,o),x=b.length}else 0===h&&(p++,b=[0]);S[d++]=p,b[0]?b[x++]=I[w]||0:(b=[I[w]],x=1)}while((w++<E||null!=b[0])&&T--);g=null!=b[0],S[0]||S.splice(0,1)}if(o==l){for(d=1,T=S[0];T>=10;T/=10,d++);G(F,s+(F.e=d+c*f-1)+1,a,g)}else F.e=c,F.r=+g;return F}}(),w=/^(-?)0([xbo])(?=\w[\w.]*$)/i,E=/^([^.]+)\.$/,B=/^\.([^.]+)$/,C=/^-?(Infinity|NaN)$/,D=/^\s*\+(?=[\w.])|^\s+|\s+$/g,i=function(t,e,r,n){var i,s=r?e:e.replace(D,"");if(C.test(s))t.s=isNaN(s)?null:s<0?-1:1;else{if(!r&&(s=s.replace(w,function(t,e,r){return i="x"==(r=r.toLowerCase())?16:"b"==r?2:8,n&&n!=i?t:e}),n&&(i=n,s=s.replace(E,"$1").replace(B,"0.$1")),e!=s))return new U(s,i);if(U.DEBUG)throw Error(h+"Not a"+(n?" base "+n:"")+" number: "+e);t.s=null}t.c=t.e=null},T.absoluteValue=T.abs=function(){var t=new U(this);return t.s<0&&(t.s=1),t},T.comparedTo=function(t,e){return F(this,new U(t,e))},T.decimalPlaces=T.dp=function(t,e){var r,n,i,s=this;if(null!=t)return S(t,0,y),null==e?e=R:S(e,0,8),G(new U(s),t+s.e+1,e);if(!(r=s.c))return null;if(n=((i=r.length-1)-v(this.e/f))*f,i=r[i])for(;i%10==0;i/=10,n--);return n<0&&(n=0),n},T.dividedBy=T.div=function(t,e){return r(this,new U(t,e),P,R)},T.dividedToIntegerBy=T.idiv=function(t,e){return r(this,new U(t,e),0,1)},T.exponentiatedBy=T.pow=function(t,e){var r,n,i,s,a,c,l,d,g=this;if((t=new U(t)).c&&!t.isInteger())throw Error(h+"Exponent not an integer: "+Y(t));if(null!=e&&(e=new U(e)),a=t.e>14,!g.c||!g.c[0]||1==g.c[0]&&!g.e&&1==g.c.length||!t.c||!t.c[0])return d=new U(Math.pow(+Y(g),a?2-b(t):+Y(t))),e?d.mod(e):d;if(c=t.s<0,e){if(e.c?!e.c[0]:!e.s)return new U(NaN);(n=!c&&g.isInteger()&&e.isInteger())&&(g=g.mod(e))}else{if(t.e>9&&(g.e>0||g.e<-1||(0==g.e?g.c[0]>1||a&&g.c[1]>=24e7:g.c[0]<8e13||a&&g.c[0]<=9999975e7)))return s=g.s<0&&b(t)?-0:0,g.e>-1&&(s=1/s),new U(c?1/s:s);V&&(s=o(V/f+2))}for(a?(r=new U(.5),c&&(t.s=1),l=b(t)):l=(i=Math.abs(+Y(t)))%2,d=new U(I);;){if(l){if(!(d=d.times(g)).c)break;s?d.c.length>s&&(d.c.length=s):n&&(d=d.mod(e))}if(i){if(0===(i=u(i/2)))break;l=i%2}else if(G(t=t.times(r),t.e+1,1),t.e>14)l=b(t);else{if(0===(i=+Y(t)))break;l=i%2}g=g.times(g),s?g.c&&g.c.length>s&&(g.c.length=s):n&&(g=g.mod(e))}return n?d:(c&&(d=I.div(d)),e?d.mod(e):s?G(d,V,R,void 0):d)},T.integerValue=function(t){var e=new U(this);return null==t?t=R:S(t,0,8),G(e,e.e+1,t)},T.isEqualTo=T.eq=function(t,e){return 0===F(this,new U(t,e))},T.isFinite=function(){return!!this.c},T.isGreaterThan=T.gt=function(t,e){return F(this,new U(t,e))>0},T.isGreaterThanOrEqualTo=T.gte=function(t,e){return 1===(e=F(this,new U(t,e)))||0===e},T.isInteger=function(){return!!this.c&&v(this.e/f)>this.c.length-2},T.isLessThan=T.lt=function(t,e){return F(this,new U(t,e))<0},T.isLessThanOrEqualTo=T.lte=function(t,e){return-1===(e=F(this,new U(t,e)))||0===e},T.isNaN=function(){return!this.s},T.isNegative=function(){return this.s<0},T.isPositive=function(){return this.s>0},T.isZero=function(){return!!this.c&&0==this.c[0]},T.minus=function(t,e){var r,n,i,s,a=this,o=a.s;if(e=(t=new U(t,e)).s,!o||!e)return new U(NaN);if(o!=e)return t.s=-e,a.plus(t);var u=a.e/f,h=t.e/f,c=a.c,d=t.c;if(!u||!h){if(!c||!d)return c?(t.s=-e,t):new U(d?a:NaN);if(!c[0]||!d[0])return d[0]?(t.s=-e,t):new U(c[0]?a:3==R?-0:0)}if(u=v(u),h=v(h),c=c.slice(),o=u-h){for((s=o<0)?(o=-o,i=c):(h=u,i=d),i.reverse(),e=o;e--;i.push(0));i.reverse()}else for(n=(s=(o=c.length)<(e=d.length))?o:e,o=e=0;e<n;e++)if(c[e]!=d[e]){s=c[e]<d[e];break}if(s&&(i=c,c=d,d=i,t.s=-t.s),(e=(n=d.length)-(r=c.length))>0)for(;e--;c[r++]=0);for(e=l-1;n>o;){if(c[--n]<d[n]){for(r=n;r&&!c[--r];c[r]=e);--c[r],c[n]+=l}c[n]-=d[n]}for(;0==c[0];c.splice(0,1),--h);return c[0]?z(t,c,h):(t.s=3==R?-1:1,t.c=[t.e=0],t)},T.modulo=T.mod=function(t,e){var n,i,s=this;return t=new U(t,e),!s.c||!t.s||t.c&&!t.c[0]?new U(NaN):!t.c||s.c&&!s.c[0]?new U(s):(9==L?(i=t.s,t.s=1,n=r(s,t,0,3),t.s=i,n.s*=i):n=r(s,t,0,L),(t=s.minus(n.times(t))).c[0]||1!=L||(t.s=s.s),t)},T.multipliedBy=T.times=function(t,e){var r,n,i,s,a,o,u,h,c,d,g,y,m,F,S,b=this,x=b.c,A=(t=new U(t,e)).c;if(!(x&&A&&x[0]&&A[0]))return!b.s||!t.s||x&&!x[0]&&!A||A&&!A[0]&&!x?t.c=t.e=t.s=null:(t.s*=b.s,x&&A?(t.c=[0],t.e=0):t.c=t.e=null),t;for(n=v(b.e/f)+v(t.e/f),t.s*=b.s,(u=x.length)<(d=A.length)&&(m=x,x=A,A=m,i=u,u=d,d=i),i=u+d,m=[];i--;m.push(0));for(F=l,S=p,i=d;--i>=0;){for(r=0,g=A[i]%S,y=A[i]/S|0,s=i+(a=u);s>i;)r=((h=g*(h=x[--a]%S)+(o=y*h+(c=x[a]/S|0)*g)%S*S+m[s]+r)/F|0)+(o/S|0)+y*c,m[s--]=h%F;m[s]=r}return r?++n:m.splice(0,1),z(t,m,n)},T.negated=function(){var t=new U(this);return t.s=-t.s||null,t},T.plus=function(t,e){var r,n=this,i=n.s;if(e=(t=new U(t,e)).s,!i||!e)return new U(NaN);if(i!=e)return t.s=-e,n.minus(t);var s=n.e/f,a=t.e/f,o=n.c,u=t.c;if(!s||!a){if(!o||!u)return new U(i/0);if(!o[0]||!u[0])return u[0]?t:new U(o[0]?n:0*i)}if(s=v(s),a=v(a),o=o.slice(),i=s-a){for(i>0?(a=s,r=u):(i=-i,r=o),r.reverse();i--;r.push(0));r.reverse()}for((i=o.length)-(e=u.length)<0&&(r=u,u=o,o=r,e=i),i=0;e;)i=(o[--e]=o[e]+u[e]+i)/l|0,o[e]=l===o[e]?0:o[e]%l;return i&&(o=[i].concat(o),++a),z(t,o,a)},T.precision=T.sd=function(t,e){var r,n,i,s=this;if(null!=t&&t!==!!t)return S(t,1,y),null==e?e=R:S(e,0,8),G(new U(s),t,e);if(!(r=s.c))return null;if(n=(i=r.length-1)*f+1,i=r[i]){for(;i%10==0;i/=10,n--);for(i=r[0];i>=10;i/=10,n++);}return t&&s.e+1>n&&(n=s.e+1),n},T.shiftedBy=function(t){return S(t,-d,d),this.times("1e"+t)},T.squareRoot=T.sqrt=function(){var t,e,n,i,s,a=this,o=a.c,u=a.s,h=a.e,c=P+4,l=new U("0.5");if(1!==u||!o||!o[0])return new U(!u||u<0&&(!o||o[0])?NaN:o?a:1/0);if(0==(u=Math.sqrt(+Y(a)))||u==1/0?(((e=m(o)).length+h)%2==0&&(e+="0"),u=Math.sqrt(+e),h=v((h+1)/2)-(h<0||h%2),n=new U(e=u==1/0?"1e"+h:(e=u.toExponential()).slice(0,e.indexOf("e")+1)+h)):n=new U(u+""),n.c[0])for((u=(h=n.e)+c)<3&&(u=0);;)if(s=n,n=l.times(s.plus(r(a,s,c,1))),m(s.c).slice(0,u)===(e=m(n.c)).slice(0,u)){if(n.e<h&&--u,"9999"!=(e=e.slice(u-3,u+1))&&(i||"4999"!=e)){+e&&(+e.slice(1)||"5"!=e.charAt(0))||(G(n,n.e+P+2,1),t=!n.times(n).eq(a));break}if(!i&&(G(s,s.e+P+2,0),s.times(s).eq(a))){n=s;break}c+=4,u+=4,i=1}return G(n,n.e+P+1,R,t)},T.toExponential=function(t,e){return null!=t&&(S(t,0,y),t++),K(this,t,e,1)},T.toFixed=function(t,e){return null!=t&&(S(t,0,y),t=t+this.e+1),K(this,t,e)},T.toFormat=function(t,e,r){var n,i=this;if(null==r)null!=t&&e&&"object"==typeof e?(r=e,e=null):t&&"object"==typeof t?(r=t,t=e=null):r=_;else if("object"!=typeof r)throw Error(h+"Argument not an object: "+r);if(n=i.toFixed(t,e),i.c){var s,a=n.split("."),o=+r.groupSize,u=+r.secondaryGroupSize,c=r.groupSeparator||"",l=a[0],f=a[1],d=i.s<0,g=d?l.slice(1):l,p=g.length;if(u&&(s=o,o=u,u=s,p-=s),o>0&&p>0){for(s=p%o||o,l=g.substr(0,s);s<p;s+=o)l+=c+g.substr(s,o);u>0&&(l+=c+g.slice(s)),d&&(l="-"+l)}n=f?l+(r.decimalSeparator||"")+((u=+r.fractionGroupSize)?f.replace(new RegExp("\\d{"+u+"}\\B","g"),"$&"+(r.fractionGroupSeparator||"")):f):l}return(r.prefix||"")+n+(r.suffix||"")},T.toFraction=function(t){var e,n,i,s,a,o,u,c,l,d,p,y,v=this,F=v.c;if(null!=t&&(!(u=new U(t)).isInteger()&&(u.c||1!==u.s)||u.lt(I)))throw Error(h+"Argument "+(u.isInteger()?"out of range: ":"not an integer: ")+Y(u));if(!F)return new U(v);for(e=new U(I),l=n=new U(I),i=c=new U(I),y=m(F),a=e.e=y.length-v.e-1,e.c[0]=g[(o=a%f)<0?f+o:o],t=!t||u.comparedTo(e)>0?a>0?e:l:u,o=k,k=1/0,u=new U(y),c.c[0]=0;d=r(u,e,0,1),1!=(s=n.plus(d.times(i))).comparedTo(t);)n=i,i=s,l=c.plus(d.times(s=l)),c=s,e=u.minus(d.times(s=e)),u=s;return s=r(t.minus(n),i,0,1),c=c.plus(s.times(l)),n=n.plus(s.times(i)),c.s=l.s=v.s,p=r(l,i,a*=2,R).minus(v).abs().comparedTo(r(c,n,a,R).minus(v).abs())<1?[l,i]:[c,n],k=o,p},T.toNumber=function(){return+Y(this)},T.toPrecision=function(t,e){return null!=t&&S(t,1,y),K(this,t,e,2)},T.toString=function(t){var e,r=this,i=r.s,s=r.e;return null===s?i?(e="Infinity",i<0&&(e="-"+e)):e="NaN":(null==t?e=s<=H||s>=N?x(m(r.c),s):A(m(r.c),s,"0"):10===t?e=A(m((r=G(new U(r),P+s+1,R)).c),r.e,"0"):(S(t,2,M.length,"Base"),e=n(A(m(r.c),s,"0"),10,t,i,!0)),i<0&&r.c[0]&&(e="-"+e)),e},T.valueOf=T.toJSON=function(){return Y(this)},T._isBigNumber=!0,null!=e&&U.set(e),U}()).default=s.BigNumber=s,void 0===(n=function(){return s}.call(e,r,e,t))||(t.exports=n)}()},17:function(t,e,r){"use strict";r.d(e,"b",function(){return i}),r.d(e,"c",function(){return s}),r.d(e,"a",function(){return a});var n=r(0);
/**
 * Hexadecimal functions.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2016
 * @license Apache-2.0
 */function i(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:" ",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2;if(!t)return"";t instanceof ArrayBuffer&&(t=new Uint8Array(t));for(var n="",i=0;i<t.length;i++)n+=t[i].toString(16).padStart(r,"0")+e;return"0x"===e&&(n="0x"+n),"\\x"===e&&(n="\\x"+n),e.length?n.slice(0,-e.length):n}function s(t){if(!t)return"";t instanceof ArrayBuffer&&(t=new Uint8Array(t));for(var e=[],r=0;r<t.length;r++)e.push((t[r]>>>4).toString(16)),e.push((15&t[r]).toString(16));return e.join("")}function a(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Auto",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2;if("None"!==e){var i="Auto"===e?/[^a-f\d]/gi:n.b.regexRep(e);t=t.replace(i,"")}for(var s=[],a=0;a<t.length;a+=r)s.push(parseInt(t.substr(a,r),16));return s}["Auto"].concat(["Space","Percent","Comma","Semi-colon","Colon","Line feed","CRLF","0x","\\x","None"])},18:function(t,e,r){"use strict";var n=r(13),i=r.n(n),s=r(10),a={Images:[{name:"Joint Photographic Experts Group image",extension:"jpg,jpeg,jpe,thm,mpo",mime:"image/jpeg",description:"",signature:{0:255,1:216,2:255,3:[192,196,219,221,224,225,226,227,228,229,231,232,234,235,236,237,238,254]},extractor:function(t,e){var r=new s.a(t.slice(e));for(;r.hasMore();){var n=r.getBytes(2);if(255!==n[0])throw new Error(`Invalid marker while parsing JPEG at pos ${r.position}: ${n}`);var i=0;switch(n[1]){case 216:case 1:break;case 217:return r.carve();case 192:case 193:case 194:case 195:case 196:case 197:case 198:case 199:case 200:case 201:case 202:case 203:case 204:case 205:case 206:case 207:case 219:case 222:case 224:case 225:case 226:case 227:case 228:case 229:case 230:case 231:case 232:case 233:case 234:case 235:case 236:case 237:case 238:case 239:case 254:i=r.readInt(2,"be"),r.position+=i-2;break;case 223:r.position++;break;case 220:case 221:r.position+=2;break;case 218:i=r.readInt(2,"be"),r.position+=i-2,r.continueUntil(255);break;case 0:case 208:case 209:case 210:case 211:case 212:case 213:case 214:case 215:default:r.continueUntil(255)}}throw new Error("Unable to parse JPEG successfully")}},{name:"Graphics Interchange Format image",extension:"gif",mime:"image/gif",description:"",signature:{0:71,1:73,2:70,3:56,4:[55,57],5:97},extractor:null},{name:"Portable Network Graphics image",extension:"png",mime:"image/png",description:"",signature:{0:137,1:80,2:78,3:71,4:13,5:10,6:26,7:10},extractor:function(t,e){var r=new s.a(t.slice(e));r.moveForwardsBy(8);var n=0,i="";for(;"IEND"!==i;)n=r.readInt(4,"be"),i=r.readString(4),r.moveForwardsBy(n+4);return r.carve()}},{name:"WEBP Image",extension:"webp",mime:"image/webp",description:"",signature:{8:87,9:69,10:66,11:80},extractor:null},{name:"Camera Image File Format",extension:"crw",mime:"image/x-canon-crw",description:"",signature:{6:72,7:69,8:65,9:80,10:67,11:67,12:68,13:82},extractor:null},{name:"Canon CR2 raw image",extension:"cr2",mime:"image/x-canon-cr2",description:"",signature:[{0:73,1:73,2:42,3:0,8:67,9:82},{0:77,1:77,2:0,3:42,8:67,9:82}],extractor:null},{name:"Tagged Image File Format image",extension:"tif",mime:"image/tiff",description:"",signature:[{0:73,1:73,2:42,3:0},{0:77,1:77,2:0,3:42}],extractor:null},{name:"Bitmap image",extension:"bmp",mime:"image/bmp",description:"",signature:{0:66,1:77,7:0,9:0,14:[12,40,56,64,108,124],15:0,16:0,17:0},extractor:function(t,e){var r=new s.a(t.slice(e));r.moveForwardsBy(2);var n=r.readInt(4,"le");return r.moveForwardsBy(n-6),r.carve()}},{name:"JPEG Extended Range image",extension:"jxr",mime:"image/vnd.ms-photo",description:"",signature:{0:73,1:73,2:188},extractor:null},{name:"Photoshop image",extension:"psd",mime:"image/vnd.adobe.photoshop",description:"",signature:{0:56,1:66,2:80,3:83,4:0,5:1,6:0,7:0,8:0,9:0,10:0,11:0},extractor:null},{name:"Photoshop Large Document",extension:"psb",mime:"application/x-photoshop",description:"",signature:{0:56,1:66,2:80,3:83,4:0,5:2,6:0,7:0,8:0,9:0,10:0,11:0,12:0},extractor:null},{name:"Paint Shop Pro image",extension:"psp",mime:"image/psp",description:"",signature:[{0:80,1:97,2:105,3:110,4:116,5:32,6:83,7:104,8:111,9:112,10:32,11:80,12:114,13:111,14:32,15:73,16:109},{0:126,1:66,2:75,3:0}],extractor:null},{name:"Icon image",extension:"ico",mime:"image/x-icon",description:"",signature:{0:0,1:0,2:1,3:0,4:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],5:0,6:[16,32,48,64,128],7:[16,32,48,64,128],9:0,10:[0,1]},extractor:null},{name:"Radiance High Dynamic Range image",extension:"hdr",mime:"image/vnd.radiance",description:"",signature:{0:35,1:63,2:82,3:65,4:68,5:73,6:65,7:78,8:67,9:69,10:10},extractor:null},{name:"Sony ARW image",extension:"arw",mime:"image/x-raw",description:"",signature:{0:5,1:0,2:0,3:0,4:65,5:87,6:49,7:46},extractor:null},{name:"Fujifilm Raw Image",extension:"raf",mime:"image/x-raw",description:"",signature:{0:70,1:85,2:74,3:73,4:70,5:73,6:76,7:77,8:67,9:67,10:68,11:45,12:82,13:65,14:87},extractor:null},{name:"Minolta RAW image",extension:"mrw",mime:"image/x-raw",description:"",signature:{0:0,1:77,2:82,3:77},extractor:null},{name:"Adobe Bridge Thumbnail Cache",extension:"bct",mime:"application/octet-stream",description:"",signature:{0:108,1:110,2:98,3:116,4:2,5:0,6:0,7:0},extractor:null},{name:"Microsoft Document Imaging",extension:"mdi",mime:"image/vnd.ms-modi",description:"",signature:{0:69,1:80,2:42,3:0},extractor:null}],Video:[{name:"Matroska Multimedia Container",extension:"mkv",mime:"video/x-matroska",description:"",signature:{31:109,32:97,33:116,34:114,35:111,36:115,37:107,38:97},extractor:null},{name:"WEBM video",extension:"webm",mime:"video/webm",description:"",signature:{0:26,1:69,2:223,3:163},extractor:null},{name:"MPEG-4 video",extension:"mp4",mime:"video/mp4",description:"",signature:[{0:0,1:0,2:0,3:[24,32],4:102,5:116,6:121,7:112},{0:51,1:103,2:112,3:53},{0:0,1:0,2:0,3:28,4:102,5:116,6:121,7:112,8:109,9:112,10:52,11:50,16:109,17:112,18:52,19:49,20:109,21:112,22:52,23:50,24:105,25:115,26:111,27:109}],extractor:null},{name:"M4V video",extension:"m4v",mime:"video/x-m4v",description:"",signature:{0:0,1:0,2:0,3:28,4:102,5:116,6:121,7:112,8:77,9:52,10:86},extractor:null},{name:"Quicktime video",extension:"mov",mime:"video/quicktime",description:"",signature:{0:0,1:0,2:0,3:20,4:102,5:116,6:121,7:112},extractor:null},{name:"Audio Video Interleave",extension:"avi",mime:"video/x-msvideo",description:"",signature:{0:82,1:73,2:70,3:70,8:65,9:86,10:73},extractor:null},{name:"Windows Media Video",extension:"wmv",mime:"video/x-ms-wmv",description:"",signature:{0:48,1:38,2:178,3:117,4:142,5:102,6:207,7:17,8:166,9:217},extractor:null},{name:"MPEG video",extension:"mpg",mime:"video/mpeg",description:"",signature:{0:0,1:0,2:1,3:186},extractor:null},{name:"Flash Video",extension:"flv",mime:"video/x-flv",description:"",signature:{0:70,1:76,2:86,3:1},extractor:function(t,e){var r=new s.a(t.slice(e));r.moveForwardsBy(5);var n=r.readInt(4,"be");r.moveForwardsBy(n-9);var i=-11;for(;r.hasMore();){var a=r.readInt(4,"be"),o=r.readInt(1);if([8,9,18].indexOf(o)<0){r.moveBackwardsBy(1);break}if(a!==i+11){r.moveBackwardsBy(i+11+5);break}i=r.readInt(3,"be"),r.moveForwardsBy(7+i)}return r.carve()}}],Audio:[{name:"Waveform Audio",extension:"wav",mime:"audio/x-wav",description:"",signature:{0:82,1:73,2:70,3:70,8:87,9:65,10:86,11:69},extractor:null},{name:"OGG audio",extension:"ogg",mime:"audio/ogg",description:"",signature:{0:79,1:103,2:103,3:83},extractor:null},{name:"Musical Instrument Digital Interface audio",extension:"midi",mime:"audio/midi",description:"",signature:{0:77,1:84,2:104,3:100},extractor:null},{name:"MPEG-3 audio",extension:"mp3",mime:"audio/mpeg",description:"",signature:[{0:73,1:68,2:51},{0:255,1:251}],extractor:null},{name:"MPEG-4 Part 14 audio",extension:"m4a",mime:"audio/m4a",description:"",signature:[{4:102,5:116,6:121,7:112,8:77,9:52,10:65},{0:77,1:52,2:65,3:32}],extractor:null},{name:"Free Lossless Audio Codec",extension:"flac",mime:"audio/x-flac",description:"",signature:{0:102,1:76,2:97,3:67},extractor:null},{name:"Adaptive Multi-Rate audio codec",extension:"amr",mime:"audio/amr",description:"",signature:{0:35,1:33,2:65,3:77,4:82,5:10},extractor:null},{name:"Audacity",extension:"au",mime:"audio/x-au",description:"",signature:{0:100,1:110,2:115,3:46,24:65,25:117,26:100,27:97,28:99,29:105,30:116,31:121,32:66,33:108,34:111,35:99,36:107,37:70,38:105,39:108,40:101},extractor:null},{name:"Audacity Block",extension:"auf",mime:"application/octet-stream",description:"",signature:{0:65,1:117,2:100,3:97,4:99,5:105,6:116,7:121,8:66,9:108,10:111,11:99,12:107,13:70,14:105,15:108,16:101},extractor:null},{name:"Audio Interchange File",extension:"aif",mime:"audio/x-aiff",description:"",signature:{0:70,1:79,2:82,3:77,8:65,9:73,10:70,11:70},extractor:null},{name:"Audio Interchange File (compressed)",extension:"aifc",mime:"audio/x-aifc",description:"",signature:{0:70,1:79,2:82,3:77,8:65,9:73,10:70,11:67},extractor:null}],Documents:[{name:"Portable Document Format",extension:"pdf",mime:"application/pdf",description:"",signature:{0:37,1:80,2:68,3:70},extractor:function(t,e){var r=new s.a(t.slice(e));return r.continueUntil([37,37,69,79,70]),r.moveForwardsBy(5),r.consumeIf(13),r.consumeIf(10),r.carve()}},{name:"PostScript",extension:"ps",mime:"application/postscript",description:"",signature:{0:37,1:33},extractor:null},{name:"Rich Text Format",extension:"rtf",mime:"application/rtf",description:"",signature:{0:123,1:92,2:114,3:116,4:102},extractor:function(t,e){var r=new s.a(t.slice(e)),n=0;if(123!==r.readInt(1))throw new Error("Not a valid RTF file");n++;for(;n>0&&r.hasMore();)switch(r.readInt(1)){case 123:n++;break;case 125:n--;break;case 92:r.consumeIf(92),r.position++}return r.carve()}},{name:"Microsoft Office documents/OLE2",extension:"ole2,doc,xls,dot,ppt,xla,ppa,pps,pot,msi,sdw,db,vsd,msg",mime:"application/msword,application/vnd.ms-excel,application/vnd.ms-powerpoint",description:"Microsoft Office documents",signature:{0:208,1:207,2:17,3:224,4:161,5:177,6:26,7:225},extractor:null},{name:"Microsoft Office 2007+ documents",extension:"docx,xlsx,pptx",mime:"application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.openxmlformats-officedocument.presentationml.presentation",description:"",signature:{38:95,39:84,40:121,41:112,42:101,43:115,44:93,45:46,46:120,47:109,48:108},extractor:o},{name:"EPUB e-book",extension:"epub",mime:"application/epub+zip",description:"",signature:{0:80,1:75,2:3,3:4,30:109,31:105,32:109,33:101,34:116,35:121,36:112,37:101,38:97,39:112,40:112,41:108,42:105,43:99,44:97,45:116,46:105,47:111,48:110,49:47,50:101,51:112,52:117,53:98,54:43,55:122,56:105,57:112},extractor:o}],Applications:[{name:"Windows Portable Executable",extension:"exe,dll,drv,vxd,sys,ocx,vbx,com,fon,scr",mime:"application/x-msdownload",description:"",signature:{0:77,1:90,3:[0,1,2],5:[0,1,2]},extractor:function(t,e){var r=new s.a(t.slice(e));r.moveTo(60);var n=r.readInt(4,"le");r.moveTo(n),r.moveForwardsBy(6);var i=r.readInt(2,"le");r.moveForwardsBy(12);var a=r.readInt(2,"le");r.moveForwardsBy(2+a),r.moveForwardsBy(40*(i-1)),r.moveForwardsBy(16);var o=r.readInt(4,"le"),u=r.readInt(4,"le");return r.moveTo(u+o),r.carve()}},{name:"Executable and Linkable Format file",extension:"elf,bin,axf,o,prx,so",mime:"application/x-executable",description:"Executable and Linkable Format file. No standard file extension.",signature:{0:127,1:69,2:76,3:70},extractor:function(t,e){var r=new s.a(t.slice(e));r.moveForwardsBy(4);var n=1===r.readInt(1),i=1===r.readInt(1)?"le":"be";r.moveForwardsBy(n?26:34);var a=n?r.readInt(4,i):r.readInt(8,i);r.moveForwardsBy(10);var o=r.readInt(2,i),u=r.readInt(2,i);return r.moveTo(a),r.moveForwardsBy(o*u),r.carve()}},{name:"Adobe Flash",extension:"swf",mime:"application/x-shockwave-flash",description:"",signature:{0:[67,70],1:87,2:83},extractor:null},{name:"Java Class",extension:"class",mime:"application/java-vm",description:"",signature:{0:202,1:254,2:186,3:190},extractor:null},{name:"Dalvik Executable",extension:"dex",mime:"application/octet-stream",description:"Dalvik Executable as used by Android",signature:{0:100,1:101,2:120,3:10,4:48,5:51,6:53,7:0},extractor:null},{name:"Google Chrome Extension",extension:"crx",mime:"application/crx",description:"Google Chrome extension or packaged app",signature:{0:67,1:114,2:50,3:52},extractor:null}],Archives:[{name:"PKZIP archive",extension:"zip",mime:"application/zip",description:"",signature:{0:80,1:75,2:[3,5,7],3:[4,6,8]},extractor:o},{name:"TAR archive",extension:"tar",mime:"application/x-tar",description:"",signature:{257:117,258:115,259:116,260:97,261:114},extractor:null},{name:"Roshal Archive",extension:"rar",mime:"application/x-rar-compressed",description:"",signature:{0:82,1:97,2:114,3:33,4:26,5:7,6:[0,1]},extractor:null},{name:"Gzip",extension:"gz",mime:"application/gzip",description:"",signature:{0:31,1:139,2:8},extractor:function(t,e){var r=new s.a(t.slice(e));r.moveForwardsBy(3);var n=r.readInt(1);if(r.moveForwardsBy(4),r.readInt(1),r.moveForwardsBy(1),4&n){var i=r.readInt(2,"le");r.moveForwardsby(i)}8&n&&(r.continueUntil(0),r.moveForwardsBy(1));16&n&&(r.continueUntil(0),r.moveForwardsBy(1));2&n&&r.moveForwardsBy(2);return d(r),r.moveForwardsBy(8),r.carve()}},{name:"Bzip2",extension:"bz2",mime:"application/x-bzip2",description:"",signature:{0:66,1:90,2:104},extractor:null},{name:"7zip",extension:"7z",mime:"application/x-7z-compressed",description:"",signature:{0:55,1:122,2:188,3:175,4:39,5:28},extractor:null},{name:"Zlib Deflate",extension:"zlib",mime:"application/x-deflate",description:"",signature:{0:120,1:[1,156,218,94]},extractor:function(t,e){var r=new s.a(t.slice(e));r.moveForwardsBy(1),32&r.readInt(1)&&r.moveForwardsBy(4);return d(r),r.moveForwardsBy(4),r.carve()}},{name:"xz compression",extension:"xz",mime:"application/x-xz",description:"",signature:{0:253,1:55,2:122,3:88,4:90,5:0},extractor:null},{name:"Tarball",extension:"tar.z",mime:"application/x-gtar",description:"",signature:{0:31,1:[157,160]},extractor:null},{name:"ISO disk image",extension:"iso",mime:"application/octet-stream",description:"ISO 9660 CD/DVD image file",signature:[{32769:67,32770:68,32771:48,32772:48,32773:49},{34817:67,34818:68,34819:48,34820:48,34821:49},{36865:67,36866:68,36867:48,36868:48,36869:49}],extractor:null},{name:"Virtual Machine Disk",extension:"vmdk",mime:"application/vmdk,application/x-virtualbox-vmdk",description:"",signature:{0:75,1:68,2:77},extractor:null},{name:"ARJ Archive",extension:"arj",mime:"application/x-arj-compressed",description:"",signature:{0:96,1:234,8:[0,16,20],9:0,10:2},extractor:null},{name:"WinAce Archive",extension:"ace",mime:"application/x-ace-compressed",description:"",signature:{7:42,8:42,9:65,10:67,11:69,12:42,13:42},extractor:null},{name:"Macintosh BinHex Encoded File",extension:"hqx",mime:"application/mac-binhex",description:"",signature:{11:109,12:117,13:115,14:116,15:32,16:98,17:101,18:32,19:99,20:111,21:110,22:118,23:101,24:114,25:116,26:101,27:100,28:32,29:119,30:105,31:116,32:104,33:32,34:66,35:105,36:110,37:72,38:101,39:120},extractor:null},{name:"ALZip Archive",extension:"alz",mime:"application/octet-stream",description:"",signature:{0:65,1:76,2:90,3:1,4:10,5:0,6:0,7:0},extractor:null},{name:"KGB Compressed Archive",extension:"kgb",mime:"application/x-kgb-compressed",description:"",signature:{0:75,1:71,2:66,3:95,4:97,5:114,6:99,7:104,8:32,9:45},extractor:null}],Miscellaneous:[{name:"UTF-8 text file",extension:"txt",mime:"text/plain",description:"UTF-8 encoded Unicode byte order mark, commonly but not exclusively seen in text files.",signature:{0:239,1:187,2:191},extractor:null},{name:"UTF-32 LE file",extension:"utf32le",mime:"charset/utf32le",description:"Little-endian UTF-32 encoded Unicode byte order mark.",signature:{0:255,1:254,2:0,3:0},extractor:null},{name:"UTF-16 LE file",extension:"utf16le",mime:"charset/utf16le",description:"Little-endian UTF-16 encoded Unicode byte order mark.",signature:{0:255,1:254},extractor:null},{name:"Web Open Font Format",extension:"woff",mime:"application/font-woff",description:"",signature:{0:119,1:79,2:70,3:70,4:0,5:1,6:0,7:0},extractor:null},{name:"Web Open Font Format 2",extension:"woff2",mime:"application/font-woff",description:"",signature:{0:119,1:79,2:70,3:50,4:0,5:1,6:0,7:0},extractor:null},{name:"Embedded OpenType font",extension:"eot",mime:"application/octet-stream",description:"",signature:[{8:2,9:0,10:1,34:76,35:80},{8:1,9:0,10:0,34:76,35:80},{8:2,9:0,10:2,34:76,35:80}],extractor:null},{name:"TrueType Font",extension:"ttf",mime:"application/font-sfnt",description:"",signature:{0:0,1:1,2:0,3:0,4:0},extractor:null},{name:"OpenType Font",extension:"otf",mime:"application/font-sfnt",description:"",signature:{0:79,1:84,2:84,3:79,4:0},extractor:null},{name:"SQLite",extension:"sqlite",mime:"application/x-sqlite3",description:"",signature:{0:83,1:81,2:76,3:105},extractor:null},{name:"BitTorrent link",extension:"torrent",mime:"application/x-bittorrent",description:"",signature:[{0:100,1:56,2:58,3:97,4:110,5:110,6:111,7:117,8:110,9:99,10:101,11:35,12:35,13:58},{0:100,1:52,2:58,3:105,4:110,5:102,6:111,7:100,8:[52,53,54],9:58}],extractor:null}]};function o(t,e){var r=new s.a(t.slice(e));r.continueUntil([80,75,5,6]),r.moveForwardsBy(20);var n=r.readInt(2,"le");return r.moveForwardsBy(n),r.carve()}for(var u=new Array(288),h=0;h<u.length;h++)u[h]=h<=143?8:h<=255?9:h<=279?7:8;var c=v(u),l=v(new Array(30).fill(5)),f=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];function d(t){for(var e=0;!e;){e=t.readBits(1);var r=t.readBits(2);if(0===r){t.moveForwardsBy(1);var n=t.readInt(2,"le");t.moveForwardsBy(2+n)}else if(1===r)y(t,c,l);else{if(2!==r)throw new Error(`Invalid block type while parsing DEFLATE stream at pos ${t.position}`);for(var i=t.readBits(5)+257,s=t.readBits(5)+1,a=t.readBits(4)+4,o=new Uint8Array(f.length),u=0;u<a;u++)o[f[u]]=t.readBits(3);for(var h=v(o),d=new Uint8Array(i+s),g=void 0,p=void 0,F=void 0,S=0;S<i+s;)switch(g=m(t,h)){case 16:for(p=3+t.readBits(2);p--;)d[S++]=F;break;case 17:for(p=3+t.readBits(3);p--;)d[S++]=0;F=0;break;case 18:for(p=11+t.readBits(7);p--;)d[S++]=0;F=0;break;default:d[S++]=g,F=g}y(t,v(d.subarray(0,i)),v(d.subarray(i)))}}t.bitPos>0&&t.moveForwardsBy(1)}var g=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0],p=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13];function y(t,e,r){for(var n,i=0;(n=m(t,e))&&256!==n;){if(++i>1e4)throw new Error("Caught in probable infinite loop while parsing Huffman Block");n<256||(t.readBits(g[n-257]),n=m(t,r),t.readBits(p[n]))}}function v(t){for(var e=Math.max.apply(Math,t),r=Math.min.apply(Math,t),n=1<<e,i=new Uint32Array(n),s=1,a=0,o=2;s<=e;){for(var u=0;u<t.length;u++)if(t[u]===s){var h=void 0,c=void 0,l=void 0;for(h=0,c=a,l=0;l<s;l++)h=h<<1|1&c,c>>=1;for(var f=s<<16|u,d=h;d<n;d+=o)i[d]=f;a++}s++,a<<=1,o<<=1}return[i,e,r]}function m(t,e){var r=i()(e,2),n=r[0],s=r[1],a=n[t.readBits(s)&(1<<s)-1],o=a>>>16;if(o>s)throw new Error(`Invalid Huffman Code length while parsing DEFLATE block at pos ${t.position}: ${o}`);return t.moveBackwardsByBits(s-o),65535&a}r(0);
/**
 * File type functions
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 *
 */
function F(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(t.length){for(var n=0;n<t.length;n++)if(S(t[n],e,r))return!0;return!1}return S(t,e,r)}function S(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;for(var n in t){var i=parseInt(n,10)+r;switch(typeof t[n]){case"number":if(e[i]!==t[n])return!1;break;case"object":if(t[n].indexOf(e[i])<0)return!1;break;case"function":if(!t[n](e[i]))return!1;break;default:throw new Error(`Unrecognised signature type at offset ${n}`)}}return!0}function b(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Object.keys(a);if(t instanceof ArrayBuffer&&(t=new Uint8Array(t)),!(t&&t.length>1))return[];var r=[],n={};for(var i in a)e.includes(i)&&(n[i]=a[i]);for(var s in n){n[s].forEach(function(e){F(e.signature,t)&&r.push(e)})}return r}function x(t){return function(t,e){var r=b(e);if(!r||!r.length)return!1;if("string"==typeof t)return r.reduce(function(e,r){var n=!!r.mime.startsWith(t)&&r.mime;return e||n},!1);if(t instanceof RegExp)return r.reduce(function(e,r){var n=!!t.test(r.mime)&&r.mime;return e||n},!1);throw new Error("Invalid type input.")}("image",t)}r.d(e,"a",function(){return b}),r.d(e,"b",function(){return x})},2:function(t,e){function r(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}t.exports=function(t,e,n){return e&&r(t.prototype,e),n&&r(t,n),t}},24:function(t,e,r){!function(t){var e,r,n,i=String.fromCharCode;function s(t){for(var e,r,n=[],i=0,s=t.length;i<s;)(e=t.charCodeAt(i++))>=55296&&e<=56319&&i<s?56320==(64512&(r=t.charCodeAt(i++)))?n.push(((1023&e)<<10)+(1023&r)+65536):(n.push(e),i--):n.push(e);return n}function a(t){if(t>=55296&&t<=57343)throw Error("Lone surrogate U+"+t.toString(16).toUpperCase()+" is not a scalar value")}function o(t,e){return i(t>>e&63|128)}function u(t){if(0==(4294967168&t))return i(t);var e="";return 0==(4294965248&t)?e=i(t>>6&31|192):0==(4294901760&t)?(a(t),e=i(t>>12&15|224),e+=o(t,6)):0==(4292870144&t)&&(e=i(t>>18&7|240),e+=o(t,12),e+=o(t,6)),e+=i(63&t|128)}function h(){if(n>=r)throw Error("Invalid byte index");var t=255&e[n];if(n++,128==(192&t))return 63&t;throw Error("Invalid continuation byte")}function c(){var t,i;if(n>r)throw Error("Invalid byte index");if(n==r)return!1;if(t=255&e[n],n++,0==(128&t))return t;if(192==(224&t)){if((i=(31&t)<<6|h())>=128)return i;throw Error("Invalid continuation byte")}if(224==(240&t)){if((i=(15&t)<<12|h()<<6|h())>=2048)return a(i),i;throw Error("Invalid continuation byte")}if(240==(248&t)&&(i=(7&t)<<18|h()<<12|h()<<6|h())>=65536&&i<=1114111)return i;throw Error("Invalid UTF-8 detected")}t.version="3.0.0",t.encode=function(t){for(var e=s(t),r=e.length,n=-1,i="";++n<r;)i+=u(e[n]);return i},t.decode=function(t){e=s(t),r=e.length,n=0;for(var a,o=[];!1!==(a=c());)o.push(a);return function(t){for(var e,r=t.length,n=-1,s="";++n<r;)(e=t[n])>65535&&(s+=i((e-=65536)>>>10&1023|55296),e=56320|1023&e),s+=i(e);return s}(o)}}(e)},25:function(t,e){t.exports=function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}},26:function(t,e,r){var n,i;!function(s,a){"use strict";void 0===(i="function"==typeof(n=function(){var t=function(){},e="undefined",r=["trace","debug","info","warn","error"];function n(t,e){var r=t[e];if("function"==typeof r.bind)return r.bind(t);try{return Function.prototype.bind.call(r,t)}catch(e){return function(){return Function.prototype.apply.apply(r,[t,arguments])}}}function i(e,n){for(var i=0;i<r.length;i++){var s=r[i];this[s]=i<e?t:this.methodFactory(s,e,n)}this.log=this.debug}function s(t,r,n){return function(){typeof console!==e&&(i.call(this,r,n),this[t].apply(this,arguments))}}function a(r,i,a){return function(r){return"debug"===r&&(r="log"),typeof console!==e&&(void 0!==console[r]?n(console,r):void 0!==console.log?n(console,"log"):t)}(r)||s.apply(this,arguments)}function o(t,n,s){var o,u=this,h="loglevel";function c(){var t;if(typeof window!==e){try{t=window.localStorage[h]}catch(t){}if(typeof t===e)try{var r=window.document.cookie,n=r.indexOf(encodeURIComponent(h)+"=");-1!==n&&(t=/^([^;]+)/.exec(r.slice(n))[1])}catch(t){}return void 0===u.levels[t]&&(t=void 0),t}}t&&(h+=":"+t),u.name=t,u.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},u.methodFactory=s||a,u.getLevel=function(){return o},u.setLevel=function(n,s){if("string"==typeof n&&void 0!==u.levels[n.toUpperCase()]&&(n=u.levels[n.toUpperCase()]),!("number"==typeof n&&n>=0&&n<=u.levels.SILENT))throw"log.setLevel() called with invalid level: "+n;if(o=n,!1!==s&&function(t){var n=(r[t]||"silent").toUpperCase();if(typeof window!==e){try{return void(window.localStorage[h]=n)}catch(t){}try{window.document.cookie=encodeURIComponent(h)+"="+n+";"}catch(t){}}}(n),i.call(u,n,t),typeof console===e&&n<u.levels.SILENT)return"No console available for logging"},u.setDefaultLevel=function(t){c()||u.setLevel(t,!1)},u.enableAll=function(t){u.setLevel(u.levels.TRACE,t)},u.disableAll=function(t){u.setLevel(u.levels.SILENT,t)};var l=c();null==l&&(l=null==n?"WARN":n),u.setLevel(l,!1)}var u=new o,h={};u.getLogger=function(t){if("string"!=typeof t||""===t)throw new TypeError("You must supply a name when creating a logger.");var e=h[t];return e||(e=h[t]=new o(t,u.getLevel(),u.methodFactory)),e};var c=typeof window!==e?window.log:void 0;return u.noConflict=function(){return typeof window!==e&&window.log===u&&(window.log=c),u},u.getLoggers=function(){return h},u})?n.call(e,r,e,t):n)||(t.exports=i)}()},27:function(t,e){var r,n,i=t.exports={};function s(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function o(t){if(r===setTimeout)return setTimeout(t,0);if((r===s||!r)&&setTimeout)return r=setTimeout,setTimeout(t,0);try{return r(t,0)}catch(e){try{return r.call(null,t,0)}catch(e){return r.call(this,t,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:s}catch(t){r=s}try{n="function"==typeof clearTimeout?clearTimeout:a}catch(t){n=a}}();var u,h=[],c=!1,l=-1;function f(){c&&u&&(c=!1,u.length?h=u.concat(h):l=-1,h.length&&d())}function d(){if(!c){var t=o(f);c=!0;for(var e=h.length;e;){for(u=h,h=[];++l<e;)u&&u[l].run();l=-1,e=h.length}u=null,c=!1,function(t){if(n===clearTimeout)return clearTimeout(t);if((n===a||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(t);try{n(t)}catch(e){try{return n.call(null,t)}catch(e){return n.call(this,t)}}}(t)}}function g(t,e){this.fun=t,this.array=e}function p(){}i.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];h.push(new g(t,e)),1!==h.length||c||o(d)},g.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=p,i.addListener=p,i.once=p,i.off=p,i.removeListener=p,i.removeAllListeners=p,i.emit=p,i.prependListener=p,i.prependOnceListener=p,i.listeners=function(t){return[]},i.binding=function(t){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(t){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},28:function(t,e){var r;r=function(){return this}();try{r=r||new Function("return this")()}catch(t){"object"==typeof window&&(r=window)}t.exports=r},29:function(t,e,r){"use strict";r.d(e,"a",function(){return i});var n=r(0);
/**
 * Binary functions.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 */function i(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Space",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:8,i=n.b.regexRep(e);t=t.replace(i,"");for(var s=[],a=0;a<t.length;a+=r)s.push(parseInt(t.substr(a,r),2));return s}},3:function(t,e){function r(e){return t.exports=r=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},r(e)}t.exports=r},30:function(t,e,r){"use strict";r.d(e,"a",function(){return i});var n=r(0);
/**
 * Decimal functions.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 */function i(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Auto";e=n.b.charRep(e);var r=[],i=t.split(e);""===i[i.length-1]&&(i=i.slice(0,i.length-1));for(var s=0;s<i.length;s++)r[s]=parseInt(i[s],10);return r}},4:function(t,e,r){var n=r(43),i=r(25);t.exports=function(t,e){return!e||"object"!==n(e)&&"function"!=typeof e?i(t):e}},43:function(t,e){function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function n(e){return"function"==typeof Symbol&&"symbol"===r(Symbol.iterator)?t.exports=n=function(t){return r(t)}:t.exports=n=function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":r(t)},n(e)}t.exports=n},44:function(t,e){function r(e,n){return t.exports=r=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},r(e,n)}t.exports=r},45:function(t,e,r){"use strict";e.byteLength=function(t){var e=h(t),r=e[0],n=e[1];return 3*(r+n)/4-n},e.toByteArray=function(t){for(var e,r=h(t),n=r[0],a=r[1],o=new s(function(t,e,r){return 3*(e+r)/4-r}(0,n,a)),u=0,c=a>0?n-4:n,l=0;l<c;l+=4)e=i[t.charCodeAt(l)]<<18|i[t.charCodeAt(l+1)]<<12|i[t.charCodeAt(l+2)]<<6|i[t.charCodeAt(l+3)],o[u++]=e>>16&255,o[u++]=e>>8&255,o[u++]=255&e;2===a&&(e=i[t.charCodeAt(l)]<<2|i[t.charCodeAt(l+1)]>>4,o[u++]=255&e);1===a&&(e=i[t.charCodeAt(l)]<<10|i[t.charCodeAt(l+1)]<<4|i[t.charCodeAt(l+2)]>>2,o[u++]=e>>8&255,o[u++]=255&e);return o},e.fromByteArray=function(t){for(var e,r=t.length,i=r%3,s=[],a=0,o=r-i;a<o;a+=16383)s.push(c(t,a,a+16383>o?o:a+16383));1===i?(e=t[r-1],s.push(n[e>>2]+n[e<<4&63]+"==")):2===i&&(e=(t[r-2]<<8)+t[r-1],s.push(n[e>>10]+n[e>>4&63]+n[e<<2&63]+"="));return s.join("")};for(var n=[],i=[],s="undefined"!=typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,u=a.length;o<u;++o)n[o]=a[o],i[a.charCodeAt(o)]=o;function h(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");return-1===r&&(r=e),[r,r===e?0:4-r%4]}function c(t,e,r){for(var i,s,a=[],o=e;o<r;o+=3)i=(t[o]<<16&16711680)+(t[o+1]<<8&65280)+(255&t[o+2]),a.push(n[(s=i)>>18&63]+n[s>>12&63]+n[s>>6&63]+n[63&s]);return a.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},46:function(t,e){e.read=function(t,e,r,n,i){var s,a,o=8*i-n-1,u=(1<<o)-1,h=u>>1,c=-7,l=r?i-1:0,f=r?-1:1,d=t[e+l];for(l+=f,s=d&(1<<-c)-1,d>>=-c,c+=o;c>0;s=256*s+t[e+l],l+=f,c-=8);for(a=s&(1<<-c)-1,s>>=-c,c+=n;c>0;a=256*a+t[e+l],l+=f,c-=8);if(0===s)s=1-h;else{if(s===u)return a?NaN:1/0*(d?-1:1);a+=Math.pow(2,n),s-=h}return(d?-1:1)*a*Math.pow(2,s-n)},e.write=function(t,e,r,n,i,s){var a,o,u,h=8*s-i-1,c=(1<<h)-1,l=c>>1,f=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,d=n?0:s-1,g=n?1:-1,p=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(o=isNaN(e)?1:0,a=c):(a=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-a))<1&&(a--,u*=2),(e+=a+l>=1?f/u:f*Math.pow(2,1-l))*u>=2&&(a++,u/=2),a+l>=c?(o=0,a=c):a+l>=1?(o=(e*u-1)*Math.pow(2,i),a+=l):(o=e*Math.pow(2,l-1)*Math.pow(2,i),a=0));i>=8;t[r+d]=255&o,d+=g,o/=256,i-=8);for(a=a<<i|o,h+=i;h>0;t[r+d]=255&a,d+=g,a/=256,h-=8);t[r+d-g]|=128*p}},47:function(t,e){var r={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==r.call(t)}},48:function(t,e,r){var n=function(t){"use strict";var e,r=Object.prototype,n=r.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},s=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",o=i.toStringTag||"@@toStringTag";function u(t,e,r,n){var i=e&&e.prototype instanceof p?e:p,s=Object.create(i.prototype),a=new C(n||[]);return s._invoke=function(t,e,r){var n=c;return function(i,s){if(n===f)throw new Error("Generator is already running");if(n===d){if("throw"===i)throw s;return T()}for(r.method=i,r.arg=s;;){var a=r.delegate;if(a){var o=w(a,r);if(o){if(o===g)continue;return o}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===c)throw n=d,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=f;var u=h(t,e,r);if("normal"===u.type){if(n=r.done?d:l,u.arg===g)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n=d,r.method="throw",r.arg=u.arg)}}}(t,r,a),s}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var c="suspendedStart",l="suspendedYield",f="executing",d="completed",g={};function p(){}function y(){}function v(){}var m={};m[s]=function(){return this};var F=Object.getPrototypeOf,S=F&&F(F(D([])));S&&S!==r&&n.call(S,s)&&(m=S);var b=v.prototype=p.prototype=Object.create(m);function x(t){["next","throw","return"].forEach(function(e){t[e]=function(t){return this._invoke(e,t)}})}function A(t){var e;this._invoke=function(r,i){function s(){return new Promise(function(e,s){!function e(r,i,s,a){var o=h(t[r],t,i);if("throw"!==o.type){var u=o.arg,c=u.value;return c&&"object"==typeof c&&n.call(c,"__await")?Promise.resolve(c.__await).then(function(t){e("next",t,s,a)},function(t){e("throw",t,s,a)}):Promise.resolve(c).then(function(t){u.value=t,s(u)},function(t){return e("throw",t,s,a)})}a(o.arg)}(r,i,e,s)})}return e=e?e.then(s,s):s()}}function w(t,r){var n=t.iterator[r.method];if(n===e){if(r.delegate=null,"throw"===r.method){if(t.iterator.return&&(r.method="return",r.arg=e,w(t,r),"throw"===r.method))return g;r.method="throw",r.arg=new TypeError("The iterator does not provide a 'throw' method")}return g}var i=h(n,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,g;var s=i.arg;return s?s.done?(r[t.resultName]=s.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,g):s:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function B(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function C(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function D(t){if(t){var r=t[s];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,a=function r(){for(;++i<t.length;)if(n.call(t,i))return r.value=t[i],r.done=!1,r;return r.value=e,r.done=!0,r};return a.next=a}}return{next:T}}function T(){return{value:e,done:!0}}return y.prototype=b.constructor=v,v.constructor=y,v[o]=y.displayName="GeneratorFunction",t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,o in t||(t[o]="GeneratorFunction")),t.prototype=Object.create(b),t},t.awrap=function(t){return{__await:t}},x(A.prototype),A.prototype[a]=function(){return this},t.AsyncIterator=A,t.async=function(e,r,n,i){var s=new A(u(e,r,n,i));return t.isGeneratorFunction(r)?s:s.next().then(function(t){return t.done?t.value:s.next()})},x(b),b[o]="Generator",b[s]=function(){return this},b.toString=function(){return"[object Generator]"},t.keys=function(t){var e=[];for(var r in t)e.push(r);return e.reverse(),function r(){for(;e.length;){var n=e.pop();if(n in t)return r.value=n,r.done=!1,r}return r.done=!0,r}},t.values=D,C.prototype={constructor:C,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(B),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function i(n,i){return o.type="throw",o.arg=t,r.next=n,i&&(r.method="next",r.arg=e),!!i}for(var s=this.tryEntries.length-1;s>=0;--s){var a=this.tryEntries[s],o=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),h=n.call(a,"finallyLoc");if(u&&h){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!h)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var s=i;break}}s&&("break"===t||"continue"===t)&&s.tryLoc<=e&&e<=s.finallyLoc&&(s=null);var a=s?s.completion:{};return a.type=t,a.arg=e,s?(this.method="next",this.next=s.finallyLoc,g):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),B(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;B(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:D(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),g}},t}(t.exports);try{regeneratorRuntime=n}catch(t){Function("r","regeneratorRuntime = r")(n)}},49:function(t,e){t.exports=function(t){if(Array.isArray(t))return t}},5:function(t,e,r){var n=r(44);t.exports=function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&n(t,e)}},50:function(t,e){t.exports=function(t,e){var r=[],n=!0,i=!1,s=void 0;try{for(var a,o=t[Symbol.iterator]();!(n=(a=o.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){i=!0,s=t}finally{try{n||null==o.return||o.return()}finally{if(i)throw s}}return r}},51:function(t,e){t.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}},6:function(t,e,r){"use strict";var n=r(1),i=r.n(n),s=r(2),a=r.n(s),o=r(15),u=r(0),h=r(17),c=function(){function t(e){i()(this,t),this.name="",this.type="",this._value=null,this.disabled=!1,this.hint="",this.rows=0,this.toggleValues=[],this.target=null,this.defaultIndex=0,this.min=null,this.max=null,this.step=1,e&&this._parseConfig(e)}return a()(t,[{key:"_parseConfig",value:function(t){this.name=t.name,this.type=t.type,this.defaultValue=t.value,this.disabled=!!t.disabled,this.hint=t.hint||!1,this.rows=t.rows||!1,this.toggleValues=t.toggleValues,this.target=void 0!==t.target?t.target:null,this.defaultIndex=void 0!==t.defaultIndex?t.defaultIndex:0,this.min=t.min,this.max=t.max,this.step=t.step}},{key:"config",get:function(){return this._value}},{key:"value",set:function(e){this._value=t.prepare(e,this.type)},get:function(){return this._value}}],[{key:"prepare",value:function(t,e){var r;switch(e){case"binaryString":case"binaryShortString":case"editableOption":case"editableOptionShort":return u.b.parseEscapedChars(t);case"byteArray":return"string"==typeof t?(t=t.replace(/\s+/g,""),Object(h.a)(t)):t;case"number":if(r=parseFloat(t),isNaN(r))throw"Invalid ingredient value. Not a number: "+u.b.truncate(t.toString(),10);return r;default:return t}}}]),t}(),l=function(){function t(){i()(this,t),this._inputType=-1,this._outputType=-1,this._presentType=-1,this._breakpoint=!1,this._disabled=!1,this._flowControl=!1,this._manualBake=!1,this._ingList=[],this.name="",this.module="",this.description="",this.infoURL=null}return a()(t,[{key:"run",value:function(t,e){return t}},{key:"highlight",value:function(t,e){return!1}},{key:"highlightReverse",value:function(t,e){return!1}},{key:"present",value:function(t,e){return t}},{key:"addIngredient",value:function(t){this._ingList.push(t)}},{key:"inputType",set:function(t){this._inputType=o.a.typeEnum(t)},get:function(){return o.a.enumLookup(this._inputType)}},{key:"outputType",set:function(t){this._outputType=o.a.typeEnum(t),this._presentType<0&&(this._presentType=this._outputType)},get:function(){return o.a.enumLookup(this._outputType)}},{key:"presentType",set:function(t){this._presentType=o.a.typeEnum(t)},get:function(){return o.a.enumLookup(this._presentType)}},{key:"args",set:function(t){var e=this;t.forEach(function(t){var r=new c(t);e.addIngredient(r)})},get:function(){return this._ingList.map(function(t){var e={name:t.name,type:t.type,value:t.defaultValue};return t.toggleValues&&(e.toggleValues=t.toggleValues),t.hint&&(e.hint=t.hint),t.rows&&(e.rows=t.rows),t.disabled&&(e.disabled=t.disabled),t.target&&(e.target=t.target),t.defaultIndex&&(e.defaultIndex=t.defaultIndex),"number"==typeof t.min&&(e.min=t.min),"number"==typeof t.max&&(e.max=t.max),t.step&&(e.step=t.step),e})}},{key:"config",get:function(){return{op:this.name,args:this._ingList.map(function(t){return t.config})}}},{key:"ingValues",set:function(t){var e=this;t.forEach(function(t,r){e._ingList[r].value=t})},get:function(){return this._ingList.map(function(t){return t.value})}},{key:"breakpoint",set:function(t){this._breakpoint=!!t},get:function(){return this._breakpoint}},{key:"disabled",set:function(t){this._disabled=!!t},get:function(){return this._disabled}},{key:"flowControl",get:function(){return this._flowControl},set:function(t){this._flowControl=!!t}},{key:"manualBake",get:function(){return this._manualBake},set:function(t){this._manualBake=!!t}}]),t}();e.a=l},7:function(t,e,r){t.exports=r(48)},85:function(t,e,r){(function(t){var r={userAgent:!1},n={};
/*!
Copyright (c) 2011, Yahoo! Inc. All rights reserved.
Code licensed under the BSD License:
http://developer.yahoo.com/yui/license.html
version: 2.9.0
*/
if(void 0===i)var i={};i.lang={extend:function(t,e,n){if(!e||!t)throw new Error("YAHOO.lang.extend failed, please check that all dependencies are included.");var i=function(){};if(i.prototype=e.prototype,t.prototype=new i,t.prototype.constructor=t,t.superclass=e.prototype,e.prototype.constructor==Object.prototype.constructor&&(e.prototype.constructor=e),n){var s;for(s in n)t.prototype[s]=n[s];var a=function(){},o=["toString","valueOf"];try{/MSIE/.test(r.userAgent)&&(a=function(t,e){for(s=0;s<o.length;s+=1){var r=o[s],n=e[r];"function"==typeof n&&n!=Object.prototype[r]&&(t[r]=n)}})}catch(t){}a(t.prototype,n)}}};
/*! CryptoJS v3.1.2 core-fix.js
 * code.google.com/p/crypto-js
 * (c) 2009-2013 by Jeff Mott. All rights reserved.
 * code.google.com/p/crypto-js/wiki/License
 * THIS IS FIX of 'core.js' to fix Hmac issue.
 * https://code.google.com/p/crypto-js/issues/detail?id=84
 * https://crypto-js.googlecode.com/svn-history/r667/branches/3.x/src/core.js
 */
var s,a,o,u,h,c,l,f,d,g,p,y=y||(s=Math,o=(a={}).lib={},u=o.Base=function(){function t(){}return{extend:function(e){t.prototype=this;var r=new t;return e&&r.mixIn(e),r.hasOwnProperty("init")||(r.init=function(){r.$super.init.apply(this,arguments)}),r.init.prototype=r,r.$super=this,r},create:function(){var t=this.extend();return t.init.apply(t,arguments),t},init:function(){},mixIn:function(t){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function(){return this.init.prototype.extend(this)}}}(),h=o.WordArray=u.extend({init:function(t,e){t=this.words=t||[],this.sigBytes=null!=e?e:4*t.length},toString:function(t){return(t||l).stringify(this)},concat:function(t){var e=this.words,r=t.words,n=this.sigBytes,i=t.sigBytes;if(this.clamp(),n%4)for(var s=0;s<i;s++){var a=r[s>>>2]>>>24-s%4*8&255;e[n+s>>>2]|=a<<24-(n+s)%4*8}else for(s=0;s<i;s+=4)e[n+s>>>2]=r[s>>>2];return this.sigBytes+=i,this},clamp:function(){var t=this.words,e=this.sigBytes;t[e>>>2]&=4294967295<<32-e%4*8,t.length=s.ceil(e/4)},clone:function(){var t=u.clone.call(this);return t.words=this.words.slice(0),t},random:function(t){for(var e=[],r=0;r<t;r+=4)e.push(4294967296*s.random()|0);return new h.init(e,t)}}),c=a.enc={},l=c.Hex={stringify:function(t){for(var e=t.words,r=t.sigBytes,n=[],i=0;i<r;i++){var s=e[i>>>2]>>>24-i%4*8&255;n.push((s>>>4).toString(16)),n.push((15&s).toString(16))}return n.join("")},parse:function(t){for(var e=t.length,r=[],n=0;n<e;n+=2)r[n>>>3]|=parseInt(t.substr(n,2),16)<<24-n%8*4;return new h.init(r,e/2)}},f=c.Latin1={stringify:function(t){for(var e=t.words,r=t.sigBytes,n=[],i=0;i<r;i++){var s=e[i>>>2]>>>24-i%4*8&255;n.push(String.fromCharCode(s))}return n.join("")},parse:function(t){for(var e=t.length,r=[],n=0;n<e;n++)r[n>>>2]|=(255&t.charCodeAt(n))<<24-n%4*8;return new h.init(r,e)}},d=c.Utf8={stringify:function(t){try{return decodeURIComponent(escape(f.stringify(t)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(t){return f.parse(unescape(encodeURIComponent(t)))}},g=o.BufferedBlockAlgorithm=u.extend({reset:function(){this._data=new h.init,this._nDataBytes=0},_append:function(t){"string"==typeof t&&(t=d.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes},_process:function(t){var e=this._data,r=e.words,n=e.sigBytes,i=this.blockSize,a=n/(4*i),o=(a=t?s.ceil(a):s.max((0|a)-this._minBufferSize,0))*i,u=s.min(4*o,n);if(o){for(var c=0;c<o;c+=i)this._doProcessBlock(r,c);var l=r.splice(0,o);e.sigBytes-=u}return new h.init(l,u)},clone:function(){var t=u.clone.call(this);return t._data=this._data.clone(),t},_minBufferSize:0}),o.Hasher=g.extend({cfg:u.extend(),init:function(t){this.cfg=this.cfg.extend(t),this.reset()},reset:function(){g.reset.call(this),this._doReset()},update:function(t){return this._append(t),this._process(),this},finalize:function(t){return t&&this._append(t),this._doFinalize()},blockSize:16,_createHelper:function(t){return function(e,r){return new t.init(r).finalize(e)}},_createHmacHelper:function(t){return function(e,r){return new p.HMAC.init(t,r).finalize(e)}}}),p=a.algo={},a);!function(t){var e,r=(e=y).lib,n=r.Base,i=r.WordArray;(e=e.x64={}).Word=n.extend({init:function(t,e){this.high=t,this.low=e}}),e.WordArray=n.extend({init:function(t,e){t=this.words=t||[],this.sigBytes=null!=e?e:8*t.length},toX32:function(){for(var t=this.words,e=t.length,r=[],n=0;n<e;n++){var s=t[n];r.push(s.high),r.push(s.low)}return i.create(r,this.sigBytes)},clone:function(){for(var t=n.clone.call(this),e=t.words=this.words.slice(0),r=e.length,i=0;i<r;i++)e[i]=e[i].clone();return t}})}(),y.lib.Cipher||function(t){var e=(d=y).lib,r=e.Base,n=e.WordArray,i=e.BufferedBlockAlgorithm,s=d.enc.Base64,a=d.algo.EvpKDF,o=e.Cipher=i.extend({cfg:r.extend(),createEncryptor:function(t,e){return this.create(this._ENC_XFORM_MODE,t,e)},createDecryptor:function(t,e){return this.create(this._DEC_XFORM_MODE,t,e)},init:function(t,e,r){this.cfg=this.cfg.extend(r),this._xformMode=t,this._key=e,this.reset()},reset:function(){i.reset.call(this),this._doReset()},process:function(t){return this._append(t),this._process()},finalize:function(t){return t&&this._append(t),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(t){return{encrypt:function(e,r,n){return("string"==typeof r?g:f).encrypt(t,e,r,n)},decrypt:function(e,r,n){return("string"==typeof r?g:f).decrypt(t,e,r,n)}}}});e.StreamCipher=o.extend({_doFinalize:function(){return this._process(!0)},blockSize:1});var u=d.mode={},h=function(t,e,r){var n=this._iv;n?this._iv=void 0:n=this._prevBlock;for(var i=0;i<r;i++)t[e+i]^=n[i]},c=(e.BlockCipherMode=r.extend({createEncryptor:function(t,e){return this.Encryptor.create(t,e)},createDecryptor:function(t,e){return this.Decryptor.create(t,e)},init:function(t,e){this._cipher=t,this._iv=e}})).extend();c.Encryptor=c.extend({processBlock:function(t,e){var r=this._cipher,n=r.blockSize;h.call(this,t,e,n),r.encryptBlock(t,e),this._prevBlock=t.slice(e,e+n)}}),c.Decryptor=c.extend({processBlock:function(t,e){var r=this._cipher,n=r.blockSize,i=t.slice(e,e+n);r.decryptBlock(t,e),h.call(this,t,e,n),this._prevBlock=i}}),u=u.CBC=c,c=(d.pad={}).Pkcs7={pad:function(t,e){for(var r,i=(r=(r=4*e)-t.sigBytes%r)<<24|r<<16|r<<8|r,s=[],a=0;a<r;a+=4)s.push(i);r=n.create(s,r),t.concat(r)},unpad:function(t){t.sigBytes-=255&t.words[t.sigBytes-1>>>2]}},e.BlockCipher=o.extend({cfg:o.cfg.extend({mode:u,padding:c}),reset:function(){o.reset.call(this);var t=(e=this.cfg).iv,e=e.mode;if(this._xformMode==this._ENC_XFORM_MODE)var r=e.createEncryptor;else r=e.createDecryptor,this._minBufferSize=1;this._mode=r.call(e,this,t&&t.words)},_doProcessBlock:function(t,e){this._mode.processBlock(t,e)},_doFinalize:function(){var t=this.cfg.padding;if(this._xformMode==this._ENC_XFORM_MODE){t.pad(this._data,this.blockSize);var e=this._process(!0)}else e=this._process(!0),t.unpad(e);return e},blockSize:4});var l=e.CipherParams=r.extend({init:function(t){this.mixIn(t)},toString:function(t){return(t||this.formatter).stringify(this)}}),f=(u=(d.format={}).OpenSSL={stringify:function(t){var e=t.ciphertext;return((t=t.salt)?n.create([1398893684,1701076831]).concat(t).concat(e):e).toString(s)},parse:function(t){var e=(t=s.parse(t)).words;if(1398893684==e[0]&&1701076831==e[1]){var r=n.create(e.slice(2,4));e.splice(0,4),t.sigBytes-=16}return l.create({ciphertext:t,salt:r})}},e.SerializableCipher=r.extend({cfg:r.extend({format:u}),encrypt:function(t,e,r,n){n=this.cfg.extend(n);var i=t.createEncryptor(r,n);return e=i.finalize(e),i=i.cfg,l.create({ciphertext:e,key:r,iv:i.iv,algorithm:t,mode:i.mode,padding:i.padding,blockSize:t.blockSize,formatter:n.format})},decrypt:function(t,e,r,n){return n=this.cfg.extend(n),e=this._parse(e,n.format),t.createDecryptor(r,n).finalize(e.ciphertext)},_parse:function(t,e){return"string"==typeof t?e.parse(t,this):t}})),d=(d.kdf={}).OpenSSL={execute:function(t,e,r,i){return i||(i=n.random(8)),t=a.create({keySize:e+r}).compute(t,i),r=n.create(t.words.slice(e),4*r),t.sigBytes=4*e,l.create({key:t,iv:r,salt:i})}},g=e.PasswordBasedCipher=f.extend({cfg:f.cfg.extend({kdf:d}),encrypt:function(t,e,r,n){return r=(n=this.cfg.extend(n)).kdf.execute(r,t.keySize,t.ivSize),n.iv=r.iv,(t=f.encrypt.call(this,t,e,r.key,n)).mixIn(r),t},decrypt:function(t,e,r,n){return n=this.cfg.extend(n),e=this._parse(e,n.format),r=n.kdf.execute(r,t.keySize,t.ivSize,e.salt),n.iv=r.iv,f.decrypt.call(this,t,e,r.key,n)}})}(),function(){for(var t=y,e=t.lib.BlockCipher,r=t.algo,n=[],i=[],s=[],a=[],o=[],u=[],h=[],c=[],l=[],f=[],d=[],g=0;256>g;g++)d[g]=128>g?g<<1:g<<1^283;var p=0,v=0;for(g=0;256>g;g++){var m=(m=v^v<<1^v<<2^v<<3^v<<4)>>>8^255&m^99;n[p]=m,i[m]=p;var F=d[p],S=d[F],b=d[S],x=257*d[m]^16843008*m;s[p]=x<<24|x>>>8,a[p]=x<<16|x>>>16,o[p]=x<<8|x>>>24,u[p]=x,x=16843009*b^65537*S^257*F^16843008*p,h[m]=x<<24|x>>>8,c[m]=x<<16|x>>>16,l[m]=x<<8|x>>>24,f[m]=x,p?(p=F^d[d[d[b^F]]],v^=d[d[v]]):p=v=1}var A=[0,1,2,4,8,16,32,64,128,27,54];r=r.AES=e.extend({_doReset:function(){for(var t=(r=this._key).words,e=r.sigBytes/4,r=4*((this._nRounds=e+6)+1),i=this._keySchedule=[],s=0;s<r;s++)if(s<e)i[s]=t[s];else{var a=i[s-1];s%e?6<e&&4==s%e&&(a=n[a>>>24]<<24|n[a>>>16&255]<<16|n[a>>>8&255]<<8|n[255&a]):(a=n[(a=a<<8|a>>>24)>>>24]<<24|n[a>>>16&255]<<16|n[a>>>8&255]<<8|n[255&a],a^=A[s/e|0]<<24),i[s]=i[s-e]^a}for(t=this._invKeySchedule=[],e=0;e<r;e++)s=r-e,a=e%4?i[s]:i[s-4],t[e]=4>e||4>=s?a:h[n[a>>>24]]^c[n[a>>>16&255]]^l[n[a>>>8&255]]^f[n[255&a]]},encryptBlock:function(t,e){this._doCryptBlock(t,e,this._keySchedule,s,a,o,u,n)},decryptBlock:function(t,e){var r=t[e+1];t[e+1]=t[e+3],t[e+3]=r,this._doCryptBlock(t,e,this._invKeySchedule,h,c,l,f,i),r=t[e+1],t[e+1]=t[e+3],t[e+3]=r},_doCryptBlock:function(t,e,r,n,i,s,a,o){for(var u=this._nRounds,h=t[e]^r[0],c=t[e+1]^r[1],l=t[e+2]^r[2],f=t[e+3]^r[3],d=4,g=1;g<u;g++){var p=n[h>>>24]^i[c>>>16&255]^s[l>>>8&255]^a[255&f]^r[d++],y=n[c>>>24]^i[l>>>16&255]^s[f>>>8&255]^a[255&h]^r[d++],v=n[l>>>24]^i[f>>>16&255]^s[h>>>8&255]^a[255&c]^r[d++];f=n[f>>>24]^i[h>>>16&255]^s[c>>>8&255]^a[255&l]^r[d++],h=p,c=y,l=v}p=(o[h>>>24]<<24|o[c>>>16&255]<<16|o[l>>>8&255]<<8|o[255&f])^r[d++],y=(o[c>>>24]<<24|o[l>>>16&255]<<16|o[f>>>8&255]<<8|o[255&h])^r[d++],v=(o[l>>>24]<<24|o[f>>>16&255]<<16|o[h>>>8&255]<<8|o[255&c])^r[d++],f=(o[f>>>24]<<24|o[h>>>16&255]<<16|o[c>>>8&255]<<8|o[255&l])^r[d++],t[e]=p,t[e+1]=y,t[e+2]=v,t[e+3]=f},keySize:8});t.AES=e._createHelper(r)}(),function(){function t(t,e){var r=(this._lBlock>>>t^this._rBlock)&e;this._rBlock^=r,this._lBlock^=r<<t}function e(t,e){var r=(this._rBlock>>>t^this._lBlock)&e;this._lBlock^=r,this._rBlock^=r<<t}var r=y,n=(i=r.lib).WordArray,i=i.BlockCipher,s=r.algo,a=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],o=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],u=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],h=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],c=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],l=s.DES=i.extend({_doReset:function(){for(var t=this._key.words,e=[],r=0;56>r;r++){var n=a[r]-1;e[r]=t[n>>>5]>>>31-n%32&1}for(t=this._subKeys=[],n=0;16>n;n++){var i=t[n]=[],s=u[n];for(r=0;24>r;r++)i[r/6|0]|=e[(o[r]-1+s)%28]<<31-r%6,i[4+(r/6|0)]|=e[28+(o[r+24]-1+s)%28]<<31-r%6;for(i[0]=i[0]<<1|i[0]>>>31,r=1;7>r;r++)i[r]>>>=4*(r-1)+3;i[7]=i[7]<<5|i[7]>>>27}for(e=this._invSubKeys=[],r=0;16>r;r++)e[r]=t[15-r]},encryptBlock:function(t,e){this._doCryptBlock(t,e,this._subKeys)},decryptBlock:function(t,e){this._doCryptBlock(t,e,this._invSubKeys)},_doCryptBlock:function(r,n,i){this._lBlock=r[n],this._rBlock=r[n+1],t.call(this,4,252645135),t.call(this,16,65535),e.call(this,2,858993459),e.call(this,8,16711935),t.call(this,1,1431655765);for(var s=0;16>s;s++){for(var a=i[s],o=this._lBlock,u=this._rBlock,l=0,f=0;8>f;f++)l|=h[f][((u^a[f])&c[f])>>>0];this._lBlock=u,this._rBlock=o^l}i=this._lBlock,this._lBlock=this._rBlock,this._rBlock=i,t.call(this,1,1431655765),e.call(this,8,16711935),e.call(this,2,858993459),t.call(this,16,65535),t.call(this,4,252645135),r[n]=this._lBlock,r[n+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});r.DES=i._createHelper(l),s=s.TripleDES=i.extend({_doReset:function(){var t=this._key.words;this._des1=l.createEncryptor(n.create(t.slice(0,2))),this._des2=l.createEncryptor(n.create(t.slice(2,4))),this._des3=l.createEncryptor(n.create(t.slice(4,6)))},encryptBlock:function(t,e){this._des1.encryptBlock(t,e),this._des2.decryptBlock(t,e),this._des3.encryptBlock(t,e)},decryptBlock:function(t,e){this._des3.decryptBlock(t,e),this._des2.encryptBlock(t,e),this._des1.decryptBlock(t,e)},keySize:6,ivSize:2,blockSize:2}),r.TripleDES=i._createHelper(s)}(),function(){var t=y,e=t.lib.WordArray;t.enc.Base64={stringify:function(t){var e=t.words,r=t.sigBytes,n=this._map;t.clamp(),t=[];for(var i=0;i<r;i+=3)for(var s=(e[i>>>2]>>>24-i%4*8&255)<<16|(e[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|e[i+2>>>2]>>>24-(i+2)%4*8&255,a=0;4>a&&i+.75*a<r;a++)t.push(n.charAt(s>>>6*(3-a)&63));if(e=n.charAt(64))for(;t.length%4;)t.push(e);return t.join("")},parse:function(t){var r=t.length,n=this._map;(i=n.charAt(64))&&(-1!=(i=t.indexOf(i))&&(r=i));for(var i=[],s=0,a=0;a<r;a++)if(a%4){var o=n.indexOf(t.charAt(a-1))<<a%4*2,u=n.indexOf(t.charAt(a))>>>6-a%4*2;i[s>>>2]|=(o|u)<<24-s%4*8,s++}return e.create(i,s)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),function(t){function e(t,e,r,n,i,s,a){return((t=t+(e&r|~e&n)+i+a)<<s|t>>>32-s)+e}function r(t,e,r,n,i,s,a){return((t=t+(e&n|r&~n)+i+a)<<s|t>>>32-s)+e}function n(t,e,r,n,i,s,a){return((t=t+(e^r^n)+i+a)<<s|t>>>32-s)+e}function i(t,e,r,n,i,s,a){return((t=t+(r^(e|~n))+i+a)<<s|t>>>32-s)+e}for(var s=y,a=(u=s.lib).WordArray,o=u.Hasher,u=s.algo,h=[],c=0;64>c;c++)h[c]=4294967296*t.abs(t.sin(c+1))|0;u=u.MD5=o.extend({_doReset:function(){this._hash=new a.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(t,s){for(var a=0;16>a;a++){var o=t[u=s+a];t[u]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8)}a=this._hash.words;var u=t[s+0],c=(o=t[s+1],t[s+2]),l=t[s+3],f=t[s+4],d=t[s+5],g=t[s+6],p=t[s+7],y=t[s+8],v=t[s+9],m=t[s+10],F=t[s+11],S=t[s+12],b=t[s+13],x=t[s+14],A=t[s+15],w=e(w=a[0],C=a[1],B=a[2],E=a[3],u,7,h[0]),E=e(E,w,C,B,o,12,h[1]),B=e(B,E,w,C,c,17,h[2]),C=e(C,B,E,w,l,22,h[3]);w=e(w,C,B,E,f,7,h[4]),E=e(E,w,C,B,d,12,h[5]),B=e(B,E,w,C,g,17,h[6]),C=e(C,B,E,w,p,22,h[7]),w=e(w,C,B,E,y,7,h[8]),E=e(E,w,C,B,v,12,h[9]),B=e(B,E,w,C,m,17,h[10]),C=e(C,B,E,w,F,22,h[11]),w=e(w,C,B,E,S,7,h[12]),E=e(E,w,C,B,b,12,h[13]),B=e(B,E,w,C,x,17,h[14]),w=r(w,C=e(C,B,E,w,A,22,h[15]),B,E,o,5,h[16]),E=r(E,w,C,B,g,9,h[17]),B=r(B,E,w,C,F,14,h[18]),C=r(C,B,E,w,u,20,h[19]),w=r(w,C,B,E,d,5,h[20]),E=r(E,w,C,B,m,9,h[21]),B=r(B,E,w,C,A,14,h[22]),C=r(C,B,E,w,f,20,h[23]),w=r(w,C,B,E,v,5,h[24]),E=r(E,w,C,B,x,9,h[25]),B=r(B,E,w,C,l,14,h[26]),C=r(C,B,E,w,y,20,h[27]),w=r(w,C,B,E,b,5,h[28]),E=r(E,w,C,B,c,9,h[29]),B=r(B,E,w,C,p,14,h[30]),w=n(w,C=r(C,B,E,w,S,20,h[31]),B,E,d,4,h[32]),E=n(E,w,C,B,y,11,h[33]),B=n(B,E,w,C,F,16,h[34]),C=n(C,B,E,w,x,23,h[35]),w=n(w,C,B,E,o,4,h[36]),E=n(E,w,C,B,f,11,h[37]),B=n(B,E,w,C,p,16,h[38]),C=n(C,B,E,w,m,23,h[39]),w=n(w,C,B,E,b,4,h[40]),E=n(E,w,C,B,u,11,h[41]),B=n(B,E,w,C,l,16,h[42]),C=n(C,B,E,w,g,23,h[43]),w=n(w,C,B,E,v,4,h[44]),E=n(E,w,C,B,S,11,h[45]),B=n(B,E,w,C,A,16,h[46]),w=i(w,C=n(C,B,E,w,c,23,h[47]),B,E,u,6,h[48]),E=i(E,w,C,B,p,10,h[49]),B=i(B,E,w,C,x,15,h[50]),C=i(C,B,E,w,d,21,h[51]),w=i(w,C,B,E,S,6,h[52]),E=i(E,w,C,B,l,10,h[53]),B=i(B,E,w,C,m,15,h[54]),C=i(C,B,E,w,o,21,h[55]),w=i(w,C,B,E,y,6,h[56]),E=i(E,w,C,B,A,10,h[57]),B=i(B,E,w,C,g,15,h[58]),C=i(C,B,E,w,b,21,h[59]),w=i(w,C,B,E,f,6,h[60]),E=i(E,w,C,B,F,10,h[61]),B=i(B,E,w,C,c,15,h[62]),C=i(C,B,E,w,v,21,h[63]);a[0]=a[0]+w|0,a[1]=a[1]+C|0,a[2]=a[2]+B|0,a[3]=a[3]+E|0},_doFinalize:function(){var e=this._data,r=e.words,n=8*this._nDataBytes,i=8*e.sigBytes;r[i>>>5]|=128<<24-i%32;var s=t.floor(n/4294967296);for(r[15+(i+64>>>9<<4)]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),r[14+(i+64>>>9<<4)]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8),e.sigBytes=4*(r.length+1),this._process(),r=(e=this._hash).words,n=0;4>n;n++)i=r[n],r[n]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8);return e},clone:function(){var t=o.clone.call(this);return t._hash=this._hash.clone(),t}}),s.MD5=o._createHelper(u),s.HmacMD5=o._createHmacHelper(u)}(Math),function(){var t=y,e=(i=t.lib).WordArray,r=i.Hasher,n=[],i=t.algo.SHA1=r.extend({_doReset:function(){this._hash=new e.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,e){for(var r=this._hash.words,i=r[0],s=r[1],a=r[2],o=r[3],u=r[4],h=0;80>h;h++){if(16>h)n[h]=0|t[e+h];else{var c=n[h-3]^n[h-8]^n[h-14]^n[h-16];n[h]=c<<1|c>>>31}c=(i<<5|i>>>27)+u+n[h],c=20>h?c+(1518500249+(s&a|~s&o)):40>h?c+(1859775393+(s^a^o)):60>h?c+((s&a|s&o|a&o)-1894007588):c+((s^a^o)-899497514),u=o,o=a,a=s<<30|s>>>2,s=i,i=c}r[0]=r[0]+i|0,r[1]=r[1]+s|0,r[2]=r[2]+a|0,r[3]=r[3]+o|0,r[4]=r[4]+u|0},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,n=8*t.sigBytes;return e[n>>>5]|=128<<24-n%32,e[14+(n+64>>>9<<4)]=Math.floor(r/4294967296),e[15+(n+64>>>9<<4)]=r,t.sigBytes=4*e.length,this._process(),this._hash},clone:function(){var t=r.clone.call(this);return t._hash=this._hash.clone(),t}});t.SHA1=r._createHelper(i),t.HmacSHA1=r._createHmacHelper(i)}(),function(t){for(var e=y,r=(i=e.lib).WordArray,n=i.Hasher,i=e.algo,s=[],a=[],o=function(t){return 4294967296*(t-(0|t))|0},u=2,h=0;64>h;){var c;t:{c=u;for(var l=t.sqrt(c),f=2;f<=l;f++)if(!(c%f)){c=!1;break t}c=!0}c&&(8>h&&(s[h]=o(t.pow(u,.5))),a[h]=o(t.pow(u,1/3)),h++),u++}var d=[];i=i.SHA256=n.extend({_doReset:function(){this._hash=new r.init(s.slice(0))},_doProcessBlock:function(t,e){for(var r=this._hash.words,n=r[0],i=r[1],s=r[2],o=r[3],u=r[4],h=r[5],c=r[6],l=r[7],f=0;64>f;f++){if(16>f)d[f]=0|t[e+f];else{var g=d[f-15],p=d[f-2];d[f]=((g<<25|g>>>7)^(g<<14|g>>>18)^g>>>3)+d[f-7]+((p<<15|p>>>17)^(p<<13|p>>>19)^p>>>10)+d[f-16]}g=l+((u<<26|u>>>6)^(u<<21|u>>>11)^(u<<7|u>>>25))+(u&h^~u&c)+a[f]+d[f],p=((n<<30|n>>>2)^(n<<19|n>>>13)^(n<<10|n>>>22))+(n&i^n&s^i&s),l=c,c=h,h=u,u=o+g|0,o=s,s=i,i=n,n=g+p|0}r[0]=r[0]+n|0,r[1]=r[1]+i|0,r[2]=r[2]+s|0,r[3]=r[3]+o|0,r[4]=r[4]+u|0,r[5]=r[5]+h|0,r[6]=r[6]+c|0,r[7]=r[7]+l|0},_doFinalize:function(){var e=this._data,r=e.words,n=8*this._nDataBytes,i=8*e.sigBytes;return r[i>>>5]|=128<<24-i%32,r[14+(i+64>>>9<<4)]=t.floor(n/4294967296),r[15+(i+64>>>9<<4)]=n,e.sigBytes=4*r.length,this._process(),this._hash},clone:function(){var t=n.clone.call(this);return t._hash=this._hash.clone(),t}});e.SHA256=n._createHelper(i),e.HmacSHA256=n._createHmacHelper(i)}(Math),function(){var t=y,e=t.lib.WordArray,r=(n=t.algo).SHA256,n=n.SHA224=r.extend({_doReset:function(){this._hash=new e.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var t=r._doFinalize.call(this);return t.sigBytes-=4,t}});t.SHA224=r._createHelper(n),t.HmacSHA224=r._createHmacHelper(n)}(),function(){function t(){return n.create.apply(n,arguments)}for(var e=y,r=e.lib.Hasher,n=(s=e.x64).Word,i=s.WordArray,s=e.algo,a=[t(1116352408,3609767458),t(1899447441,602891725),t(3049323471,3964484399),t(3921009573,2173295548),t(961987163,4081628472),t(1508970993,3053834265),t(2453635748,2937671579),t(2870763221,3664609560),t(3624381080,2734883394),t(310598401,1164996542),t(607225278,1323610764),t(1426881987,3590304994),t(1925078388,4068182383),t(2162078206,991336113),t(2614888103,633803317),t(3248222580,3479774868),t(3835390401,2666613458),t(4022224774,944711139),t(264347078,2341262773),t(604807628,2007800933),t(770255983,1495990901),t(1249150122,1856431235),t(1555081692,3175218132),t(1996064986,2198950837),t(2554220882,3999719339),t(2821834349,766784016),t(2952996808,2566594879),t(3210313671,3203337956),t(3336571891,1034457026),t(3584528711,2466948901),t(113926993,3758326383),t(338241895,168717936),t(666307205,1188179964),t(773529912,1546045734),t(1294757372,1522805485),t(1396182291,2643833823),t(1695183700,2343527390),t(1986661051,1014477480),t(2177026350,1206759142),t(2456956037,344077627),t(2730485921,1290863460),t(2820302411,3158454273),t(3259730800,3505952657),t(3345764771,106217008),t(3516065817,3606008344),t(3600352804,1432725776),t(4094571909,1467031594),t(275423344,851169720),t(430227734,3100823752),t(506948616,1363258195),t(659060556,3750685593),t(883997877,3785050280),t(958139571,3318307427),t(1322822218,3812723403),t(1537002063,2003034995),t(1747873779,3602036899),t(1955562222,1575990012),t(2024104815,1125592928),t(2227730452,2716904306),t(2361852424,442776044),t(2428436474,593698344),t(2756734187,3733110249),t(3204031479,2999351573),t(3329325298,3815920427),t(3391569614,3928383900),t(3515267271,566280711),t(3940187606,3454069534),t(4118630271,4000239992),t(116418474,1914138554),t(174292421,2731055270),t(289380356,3203993006),t(460393269,320620315),t(685471733,587496836),t(852142971,1086792851),t(1017036298,365543100),t(1126000580,2618297676),t(1288033470,3409855158),t(1501505948,4234509866),t(1607167915,987167468),t(1816402316,1246189591)],o=[],u=0;80>u;u++)o[u]=t();s=s.SHA512=r.extend({_doReset:function(){this._hash=new i.init([new n.init(1779033703,4089235720),new n.init(3144134277,2227873595),new n.init(1013904242,4271175723),new n.init(2773480762,1595750129),new n.init(1359893119,2917565137),new n.init(2600822924,725511199),new n.init(528734635,4215389547),new n.init(1541459225,327033209)])},_doProcessBlock:function(t,e){for(var r=(l=this._hash.words)[0],n=l[1],i=l[2],s=l[3],u=l[4],h=l[5],c=l[6],l=l[7],f=r.high,d=r.low,g=n.high,p=n.low,y=i.high,v=i.low,m=s.high,F=s.low,S=u.high,b=u.low,x=h.high,A=h.low,w=c.high,E=c.low,B=l.high,C=l.low,D=f,T=d,I=g,P=p,R=y,H=v,N=m,O=F,k=S,j=b,L=x,V=A,_=w,M=E,U=B,K=C,q=0;80>q;q++){var z=o[q];if(16>q)var G=z.high=0|t[e+2*q],Y=z.low=0|t[e+2*q+1];else{G=((Y=(G=o[q-15]).high)>>>1|($=G.low)<<31)^(Y>>>8|$<<24)^Y>>>7;var $=($>>>1|Y<<31)^($>>>8|Y<<24)^($>>>7|Y<<25),J=((Y=(J=o[q-2]).high)>>>19|(W=J.low)<<13)^(Y<<3|W>>>29)^Y>>>6,W=(W>>>19|Y<<13)^(W<<3|Y>>>29)^(W>>>6|Y<<26),X=(Y=o[q-7]).high,Z=(Q=o[q-16]).high,Q=Q.low;G=(G=(G=G+X+((Y=$+Y.low)>>>0<$>>>0?1:0))+J+((Y=Y+W)>>>0<W>>>0?1:0))+Z+((Y=Y+Q)>>>0<Q>>>0?1:0);z.high=G,z.low=Y}X=k&L^~k&_,Q=j&V^~j&M,z=D&I^D&R^I&R;var tt=T&P^T&H^P&H,et=($=(D>>>28|T<<4)^(D<<30|T>>>2)^(D<<25|T>>>7),J=(T>>>28|D<<4)^(T<<30|D>>>2)^(T<<25|D>>>7),(W=a[q]).high),rt=W.low;Z=U+((k>>>14|j<<18)^(k>>>18|j<<14)^(k<<23|j>>>9))+((W=K+((j>>>14|k<<18)^(j>>>18|k<<14)^(j<<23|k>>>9)))>>>0<K>>>0?1:0),U=_,K=M,_=L,M=V,L=k,V=j,k=N+(Z=(Z=(Z=Z+X+((W=W+Q)>>>0<Q>>>0?1:0))+et+((W=W+rt)>>>0<rt>>>0?1:0))+G+((W=W+Y)>>>0<Y>>>0?1:0))+((j=O+W|0)>>>0<O>>>0?1:0)|0,N=R,O=H,R=I,H=P,I=D,P=T,D=Z+(z=$+z+((Y=J+tt)>>>0<J>>>0?1:0))+((T=W+Y|0)>>>0<W>>>0?1:0)|0}d=r.low=d+T,r.high=f+D+(d>>>0<T>>>0?1:0),p=n.low=p+P,n.high=g+I+(p>>>0<P>>>0?1:0),v=i.low=v+H,i.high=y+R+(v>>>0<H>>>0?1:0),F=s.low=F+O,s.high=m+N+(F>>>0<O>>>0?1:0),b=u.low=b+j,u.high=S+k+(b>>>0<j>>>0?1:0),A=h.low=A+V,h.high=x+L+(A>>>0<V>>>0?1:0),E=c.low=E+M,c.high=w+_+(E>>>0<M>>>0?1:0),C=l.low=C+K,l.high=B+U+(C>>>0<K>>>0?1:0)},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,n=8*t.sigBytes;return e[n>>>5]|=128<<24-n%32,e[30+(n+128>>>10<<5)]=Math.floor(r/4294967296),e[31+(n+128>>>10<<5)]=r,t.sigBytes=4*e.length,this._process(),this._hash.toX32()},clone:function(){var t=r.clone.call(this);return t._hash=this._hash.clone(),t},blockSize:32}),e.SHA512=r._createHelper(s),e.HmacSHA512=r._createHmacHelper(s)}(),function(){var t=y,e=(i=t.x64).Word,r=i.WordArray,n=(i=t.algo).SHA512,i=i.SHA384=n.extend({_doReset:function(){this._hash=new r.init([new e.init(3418070365,3238371032),new e.init(1654270250,914150663),new e.init(2438529370,812702999),new e.init(355462360,4144912697),new e.init(1731405415,4290775857),new e.init(2394180231,1750603025),new e.init(3675008525,1694076839),new e.init(1203062813,3204075428)])},_doFinalize:function(){var t=n._doFinalize.call(this);return t.sigBytes-=16,t}});t.SHA384=n._createHelper(i),t.HmacSHA384=n._createHmacHelper(i)}(),function(){var t=y,e=(n=t.lib).WordArray,r=n.Hasher,n=t.algo,i=e.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),s=e.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),a=e.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),o=e.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),u=e.create([0,1518500249,1859775393,2400959708,2840853838]),h=e.create([1352829926,1548603684,1836072691,2053994217,0]);n=n.RIPEMD160=r.extend({_doReset:function(){this._hash=e.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,e){for(var r=0;16>r;r++){var n=t[S=e+r];t[S]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8)}var c,l,f,d,g,p,y,v,m,F,S=this._hash.words,b=(n=u.words,h.words),x=i.words,A=s.words,w=a.words,E=o.words;p=c=S[0],y=l=S[1],v=f=S[2],m=d=S[3],F=g=S[4];var B;for(r=0;80>r;r+=1)B=c+t[e+x[r]]|0,B=16>r?B+((l^f^d)+n[0]):32>r?B+((l&f|~l&d)+n[1]):48>r?B+(((l|~f)^d)+n[2]):64>r?B+((l&d|f&~d)+n[3]):B+((l^(f|~d))+n[4]),B=(B=(B|=0)<<w[r]|B>>>32-w[r])+g|0,c=g,g=d,d=f<<10|f>>>22,f=l,l=B,B=p+t[e+A[r]]|0,B=16>r?B+((y^(v|~m))+b[0]):32>r?B+((y&m|v&~m)+b[1]):48>r?B+(((y|~v)^m)+b[2]):64>r?B+((y&v|~y&m)+b[3]):B+((y^v^m)+b[4]),B=(B=(B|=0)<<E[r]|B>>>32-E[r])+F|0,p=F,F=m,m=v<<10|v>>>22,v=y,y=B;B=S[1]+f+m|0,S[1]=S[2]+d+F|0,S[2]=S[3]+g+p|0,S[3]=S[4]+c+y|0,S[4]=S[0]+l+v|0,S[0]=B},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,n=8*t.sigBytes;for(e[n>>>5]|=128<<24-n%32,e[14+(n+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),t.sigBytes=4*(e.length+1),this._process(),e=(t=this._hash).words,r=0;5>r;r++)n=e[r],e[r]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8);return t},clone:function(){var t=r.clone.call(this);return t._hash=this._hash.clone(),t}});t.RIPEMD160=r._createHelper(n),t.HmacRIPEMD160=r._createHmacHelper(n)}(Math),function(){var t=y,e=t.enc.Utf8;t.algo.HMAC=t.lib.Base.extend({init:function(t,r){t=this._hasher=new t.init,"string"==typeof r&&(r=e.parse(r));var n=t.blockSize,i=4*n;r.sigBytes>i&&(r=t.finalize(r)),r.clamp();for(var s=this._oKey=r.clone(),a=this._iKey=r.clone(),o=s.words,u=a.words,h=0;h<n;h++)o[h]^=1549556828,u[h]^=909522486;s.sigBytes=a.sigBytes=i,this.reset()},reset:function(){var t=this._hasher;t.reset(),t.update(this._iKey)},update:function(t){return this._hasher.update(t),this},finalize:function(t){var e=this._hasher;return t=e.finalize(t),e.reset(),e.finalize(this._oKey.clone().concat(t))}})}(),function(){var t,e=y,r=(t=e.lib).Base,n=t.WordArray,i=(t=e.algo).HMAC,s=t.PBKDF2=r.extend({cfg:r.extend({keySize:4,hasher:t.SHA1,iterations:1}),init:function(t){this.cfg=this.cfg.extend(t)},compute:function(t,e){var r=this.cfg,s=i.create(r.hasher,t),a=n.create(),o=n.create([1]),u=a.words,h=o.words,c=r.keySize;for(r=r.iterations;u.length<c;){var l=s.update(e).finalize(o);s.reset();for(var f=l.words,d=f.length,g=l,p=1;p<r;p++){g=s.finalize(g),s.reset();for(var y=g.words,v=0;v<d;v++)f[v]^=y[v]}a.concat(l),h[0]++}return a.sigBytes=4*c,a}});e.PBKDF2=function(t,e,r){return s.create(r).compute(t,e)}}();
/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
 */
var v,m="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",F="=";function S(t){var e,r,n="";for(e=0;e+3<=t.length;e+=3)r=parseInt(t.substring(e,e+3),16),n+=m.charAt(r>>6)+m.charAt(63&r);if(e+1==t.length?(r=parseInt(t.substring(e,e+1),16),n+=m.charAt(r<<2)):e+2==t.length&&(r=parseInt(t.substring(e,e+2),16),n+=m.charAt(r>>2)+m.charAt((3&r)<<4)),F)for(;(3&n.length)>0;)n+=F;return n}function b(t){var e,r,n,i="",s=0;for(e=0;e<t.length&&t.charAt(e)!=F;++e)(n=m.indexOf(t.charAt(e)))<0||(0==s?(i+=T(n>>2),r=3&n,s=1):1==s?(i+=T(r<<2|n>>4),r=15&n,s=2):2==s?(i+=T(r),i+=T(n>>2),r=3&n,s=3):(i+=T(r<<2|n>>4),i+=T(15&n),s=0));return 1==s&&(i+=T(r<<2)),i}function x(t){var e,r=b(t),n=new Array;for(e=0;2*e<r.length;++e)n[e]=parseInt(r.substring(2*e,2*e+2),16);return n}function A(t,e,r){null!=t&&("number"==typeof t?this.fromNumber(t,e,r):null==e&&"string"!=typeof t?this.fromString(t,256):this.fromString(t,e))}function w(){return new A(null)}"Microsoft Internet Explorer"==r.appName?(A.prototype.am=function(t,e,r,n,i,s){for(var a=32767&e,o=e>>15;--s>=0;){var u=32767&this[t],h=this[t++]>>15,c=o*u+h*a;i=((u=a*u+((32767&c)<<15)+r[n]+(**********&i))>>>30)+(c>>>15)+o*h+(i>>>30),r[n++]=**********&u}return i},v=30):"Netscape"!=r.appName?(A.prototype.am=function(t,e,r,n,i,s){for(;--s>=0;){var a=e*this[t++]+r[n]+i;i=Math.floor(a/67108864),r[n++]=67108863&a}return i},v=26):(A.prototype.am=function(t,e,r,n,i,s){for(var a=16383&e,o=e>>14;--s>=0;){var u=16383&this[t],h=this[t++]>>14,c=o*u+h*a;i=((u=a*u+((16383&c)<<14)+r[n]+i)>>28)+(c>>14)+o*h,r[n++]=268435455&u}return i},v=28),A.prototype.DB=v,A.prototype.DM=(1<<v)-1,A.prototype.DV=1<<v;A.prototype.FV=Math.pow(2,52),A.prototype.F1=52-v,A.prototype.F2=2*v-52;var E,B,C="0123456789abcdefghijklmnopqrstuvwxyz",D=new Array;for(E="0".charCodeAt(0),B=0;B<=9;++B)D[E++]=B;for(E="a".charCodeAt(0),B=10;B<36;++B)D[E++]=B;for(E="A".charCodeAt(0),B=10;B<36;++B)D[E++]=B;function T(t){return C.charAt(t)}function I(t,e){var r=D[t.charCodeAt(e)];return null==r?-1:r}function P(t){var e=w();return e.fromInt(t),e}function R(t){var e,r=1;return 0!=(e=t>>>16)&&(t=e,r+=16),0!=(e=t>>8)&&(t=e,r+=8),0!=(e=t>>4)&&(t=e,r+=4),0!=(e=t>>2)&&(t=e,r+=2),0!=(e=t>>1)&&(t=e,r+=1),r}function H(t){this.m=t}function N(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}function O(t,e){return t&e}function k(t,e){return t|e}function j(t,e){return t^e}function L(t,e){return t&~e}function V(t){if(0==t)return-1;var e=0;return 0==(65535&t)&&(t>>=16,e+=16),0==(255&t)&&(t>>=8,e+=8),0==(15&t)&&(t>>=4,e+=4),0==(3&t)&&(t>>=2,e+=2),0==(1&t)&&++e,e}function _(t){for(var e=0;0!=t;)t&=t-1,++e;return e}function M(){}function U(t){return t}function K(t){this.r2=w(),this.q3=w(),A.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t),this.m=t}H.prototype.convert=function(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t},H.prototype.revert=function(t){return t},H.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},H.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},H.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},N.prototype.convert=function(t){var e=w();return t.abs().dlShiftTo(this.m.t,e),e.divRemTo(this.m,null,e),t.s<0&&e.compareTo(A.ZERO)>0&&this.m.subTo(e,e),e},N.prototype.revert=function(t){var e=w();return t.copyTo(e),this.reduce(e),e},N.prototype.reduce=function(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var e=0;e<this.m.t;++e){var r=32767&t[e],n=r*this.mpl+((r*this.mph+(t[e]>>15)*this.mpl&this.um)<<15)&t.DM;for(t[r=e+this.m.t]+=this.m.am(0,n,t,e,0,this.m.t);t[r]>=t.DV;)t[r]-=t.DV,t[++r]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)},N.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},N.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},A.prototype.copyTo=function(t){for(var e=this.t-1;e>=0;--e)t[e]=this[e];t.t=this.t,t.s=this.s},A.prototype.fromInt=function(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0},A.prototype.fromString=function(t,e){var r;if(16==e)r=4;else if(8==e)r=3;else if(256==e)r=8;else if(2==e)r=1;else if(32==e)r=5;else{if(4!=e)return void this.fromRadix(t,e);r=2}this.t=0,this.s=0;for(var n=t.length,i=!1,s=0;--n>=0;){var a=8==r?255&t[n]:I(t,n);a<0?"-"==t.charAt(n)&&(i=!0):(i=!1,0==s?this[this.t++]=a:s+r>this.DB?(this[this.t-1]|=(a&(1<<this.DB-s)-1)<<s,this[this.t++]=a>>this.DB-s):this[this.t-1]|=a<<s,(s+=r)>=this.DB&&(s-=this.DB))}8==r&&0!=(128&t[0])&&(this.s=-1,s>0&&(this[this.t-1]|=(1<<this.DB-s)-1<<s)),this.clamp(),i&&A.ZERO.subTo(this,this)},A.prototype.clamp=function(){for(var t=this.s&this.DM;this.t>0&&this[this.t-1]==t;)--this.t},A.prototype.dlShiftTo=function(t,e){var r;for(r=this.t-1;r>=0;--r)e[r+t]=this[r];for(r=t-1;r>=0;--r)e[r]=0;e.t=this.t+t,e.s=this.s},A.prototype.drShiftTo=function(t,e){for(var r=t;r<this.t;++r)e[r-t]=this[r];e.t=Math.max(this.t-t,0),e.s=this.s},A.prototype.lShiftTo=function(t,e){var r,n=t%this.DB,i=this.DB-n,s=(1<<i)-1,a=Math.floor(t/this.DB),o=this.s<<n&this.DM;for(r=this.t-1;r>=0;--r)e[r+a+1]=this[r]>>i|o,o=(this[r]&s)<<n;for(r=a-1;r>=0;--r)e[r]=0;e[a]=o,e.t=this.t+a+1,e.s=this.s,e.clamp()},A.prototype.rShiftTo=function(t,e){e.s=this.s;var r=Math.floor(t/this.DB);if(r>=this.t)e.t=0;else{var n=t%this.DB,i=this.DB-n,s=(1<<n)-1;e[0]=this[r]>>n;for(var a=r+1;a<this.t;++a)e[a-r-1]|=(this[a]&s)<<i,e[a-r]=this[a]>>n;n>0&&(e[this.t-r-1]|=(this.s&s)<<i),e.t=this.t-r,e.clamp()}},A.prototype.subTo=function(t,e){for(var r=0,n=0,i=Math.min(t.t,this.t);r<i;)n+=this[r]-t[r],e[r++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n-=t.s;r<this.t;)n+=this[r],e[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<t.t;)n-=t[r],e[r++]=n&this.DM,n>>=this.DB;n-=t.s}e.s=n<0?-1:0,n<-1?e[r++]=this.DV+n:n>0&&(e[r++]=n),e.t=r,e.clamp()},A.prototype.multiplyTo=function(t,e){var r=this.abs(),n=t.abs(),i=r.t;for(e.t=i+n.t;--i>=0;)e[i]=0;for(i=0;i<n.t;++i)e[i+r.t]=r.am(0,n[i],e,i,0,r.t);e.s=0,e.clamp(),this.s!=t.s&&A.ZERO.subTo(e,e)},A.prototype.squareTo=function(t){for(var e=this.abs(),r=t.t=2*e.t;--r>=0;)t[r]=0;for(r=0;r<e.t-1;++r){var n=e.am(r,e[r],t,2*r,0,1);(t[r+e.t]+=e.am(r+1,2*e[r],t,2*r+1,n,e.t-r-1))>=e.DV&&(t[r+e.t]-=e.DV,t[r+e.t+1]=1)}t.t>0&&(t[t.t-1]+=e.am(r,e[r],t,2*r,0,1)),t.s=0,t.clamp()},A.prototype.divRemTo=function(t,e,r){var n=t.abs();if(!(n.t<=0)){var i=this.abs();if(i.t<n.t)return null!=e&&e.fromInt(0),void(null!=r&&this.copyTo(r));null==r&&(r=w());var s=w(),a=this.s,o=t.s,u=this.DB-R(n[n.t-1]);u>0?(n.lShiftTo(u,s),i.lShiftTo(u,r)):(n.copyTo(s),i.copyTo(r));var h=s.t,c=s[h-1];if(0!=c){var l=c*(1<<this.F1)+(h>1?s[h-2]>>this.F2:0),f=this.FV/l,d=(1<<this.F1)/l,g=1<<this.F2,p=r.t,y=p-h,v=null==e?w():e;for(s.dlShiftTo(y,v),r.compareTo(v)>=0&&(r[r.t++]=1,r.subTo(v,r)),A.ONE.dlShiftTo(h,v),v.subTo(s,s);s.t<h;)s[s.t++]=0;for(;--y>=0;){var m=r[--p]==c?this.DM:Math.floor(r[p]*f+(r[p-1]+g)*d);if((r[p]+=s.am(0,m,r,y,0,h))<m)for(s.dlShiftTo(y,v),r.subTo(v,r);r[p]<--m;)r.subTo(v,r)}null!=e&&(r.drShiftTo(h,e),a!=o&&A.ZERO.subTo(e,e)),r.t=h,r.clamp(),u>0&&r.rShiftTo(u,r),a<0&&A.ZERO.subTo(r,r)}}},A.prototype.invDigit=function(){if(this.t<1)return 0;var t=this[0];if(0==(1&t))return 0;var e=3&t;return(e=(e=(e=(e=e*(2-(15&t)*e)&15)*(2-(255&t)*e)&255)*(2-((65535&t)*e&65535))&65535)*(2-t*e%this.DV)%this.DV)>0?this.DV-e:-e},A.prototype.isEven=function(){return 0==(this.t>0?1&this[0]:this.s)},A.prototype.exp=function(t,e){if(t>4294967295||t<1)return A.ONE;var r=w(),n=w(),i=e.convert(this),s=R(t)-1;for(i.copyTo(r);--s>=0;)if(e.sqrTo(r,n),(t&1<<s)>0)e.mulTo(n,i,r);else{var a=r;r=n,n=a}return e.revert(r)},A.prototype.toString=function(t){if(this.s<0)return"-"+this.negate().toString(t);var e;if(16==t)e=4;else if(8==t)e=3;else if(2==t)e=1;else if(32==t)e=5;else{if(4!=t)return this.toRadix(t);e=2}var r,n=(1<<e)-1,i=!1,s="",a=this.t,o=this.DB-a*this.DB%e;if(a-- >0)for(o<this.DB&&(r=this[a]>>o)>0&&(i=!0,s=T(r));a>=0;)o<e?(r=(this[a]&(1<<o)-1)<<e-o,r|=this[--a]>>(o+=this.DB-e)):(r=this[a]>>(o-=e)&n,o<=0&&(o+=this.DB,--a)),r>0&&(i=!0),i&&(s+=T(r));return i?s:"0"},A.prototype.negate=function(){var t=w();return A.ZERO.subTo(this,t),t},A.prototype.abs=function(){return this.s<0?this.negate():this},A.prototype.compareTo=function(t){var e=this.s-t.s;if(0!=e)return e;var r=this.t;if(0!=(e=r-t.t))return this.s<0?-e:e;for(;--r>=0;)if(0!=(e=this[r]-t[r]))return e;return 0},A.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+R(this[this.t-1]^this.s&this.DM)},A.prototype.mod=function(t){var e=w();return this.abs().divRemTo(t,null,e),this.s<0&&e.compareTo(A.ZERO)>0&&t.subTo(e,e),e},A.prototype.modPowInt=function(t,e){var r;return r=t<256||e.isEven()?new H(e):new N(e),this.exp(t,r)},A.ZERO=P(0),A.ONE=P(1),M.prototype.convert=U,M.prototype.revert=U,M.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r)},M.prototype.sqrTo=function(t,e){t.squareTo(e)},K.prototype.convert=function(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var e=w();return t.copyTo(e),this.reduce(e),e},K.prototype.revert=function(t){return t},K.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)},K.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},K.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)};var q=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],z=(1<<26)/q[q.length-1];
/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
 */
function G(){this.i=0,this.j=0,this.S=new Array}A.prototype.chunkSize=function(t){return Math.floor(Math.LN2*this.DB/Math.log(t))},A.prototype.toRadix=function(t){if(null==t&&(t=10),0==this.signum()||t<2||t>36)return"0";var e=this.chunkSize(t),r=Math.pow(t,e),n=P(r),i=w(),s=w(),a="";for(this.divRemTo(n,i,s);i.signum()>0;)a=(r+s.intValue()).toString(t).substr(1)+a,i.divRemTo(n,i,s);return s.intValue().toString(t)+a},A.prototype.fromRadix=function(t,e){this.fromInt(0),null==e&&(e=10);for(var r=this.chunkSize(e),n=Math.pow(e,r),i=!1,s=0,a=0,o=0;o<t.length;++o){var u=I(t,o);u<0?"-"==t.charAt(o)&&0==this.signum()&&(i=!0):(a=e*a+u,++s>=r&&(this.dMultiply(n),this.dAddOffset(a,0),s=0,a=0))}s>0&&(this.dMultiply(Math.pow(e,s)),this.dAddOffset(a,0)),i&&A.ZERO.subTo(this,this)},A.prototype.fromNumber=function(t,e,r){if("number"==typeof e)if(t<2)this.fromInt(1);else for(this.fromNumber(t,r),this.testBit(t-1)||this.bitwiseTo(A.ONE.shiftLeft(t-1),k,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(e);)this.dAddOffset(2,0),this.bitLength()>t&&this.subTo(A.ONE.shiftLeft(t-1),this);else{var n=new Array,i=7&t;n.length=1+(t>>3),e.nextBytes(n),i>0?n[0]&=(1<<i)-1:n[0]=0,this.fromString(n,256)}},A.prototype.bitwiseTo=function(t,e,r){var n,i,s=Math.min(t.t,this.t);for(n=0;n<s;++n)r[n]=e(this[n],t[n]);if(t.t<this.t){for(i=t.s&this.DM,n=s;n<this.t;++n)r[n]=e(this[n],i);r.t=this.t}else{for(i=this.s&this.DM,n=s;n<t.t;++n)r[n]=e(i,t[n]);r.t=t.t}r.s=e(this.s,t.s),r.clamp()},A.prototype.changeBit=function(t,e){var r=A.ONE.shiftLeft(t);return this.bitwiseTo(r,e,r),r},A.prototype.addTo=function(t,e){for(var r=0,n=0,i=Math.min(t.t,this.t);r<i;)n+=this[r]+t[r],e[r++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n+=t.s;r<this.t;)n+=this[r],e[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<t.t;)n+=t[r],e[r++]=n&this.DM,n>>=this.DB;n+=t.s}e.s=n<0?-1:0,n>0?e[r++]=n:n<-1&&(e[r++]=this.DV+n),e.t=r,e.clamp()},A.prototype.dMultiply=function(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()},A.prototype.dAddOffset=function(t,e){if(0!=t){for(;this.t<=e;)this[this.t++]=0;for(this[e]+=t;this[e]>=this.DV;)this[e]-=this.DV,++e>=this.t&&(this[this.t++]=0),++this[e]}},A.prototype.multiplyLowerTo=function(t,e,r){var n,i=Math.min(this.t+t.t,e);for(r.s=0,r.t=i;i>0;)r[--i]=0;for(n=r.t-this.t;i<n;++i)r[i+this.t]=this.am(0,t[i],r,i,0,this.t);for(n=Math.min(t.t,e);i<n;++i)this.am(0,t[i],r,i,0,e-i);r.clamp()},A.prototype.multiplyUpperTo=function(t,e,r){--e;var n=r.t=this.t+t.t-e;for(r.s=0;--n>=0;)r[n]=0;for(n=Math.max(e-this.t,0);n<t.t;++n)r[this.t+n-e]=this.am(e-n,t[n],r,0,0,this.t+n-e);r.clamp(),r.drShiftTo(1,r)},A.prototype.modInt=function(t){if(t<=0)return 0;var e=this.DV%t,r=this.s<0?t-1:0;if(this.t>0)if(0==e)r=this[0]%t;else for(var n=this.t-1;n>=0;--n)r=(e*r+this[n])%t;return r},A.prototype.millerRabin=function(t){var e=this.subtract(A.ONE),r=e.getLowestSetBit();if(r<=0)return!1;var n=e.shiftRight(r);(t=t+1>>1)>q.length&&(t=q.length);for(var i=w(),s=0;s<t;++s){i.fromInt(q[Math.floor(Math.random()*q.length)]);var a=i.modPow(n,this);if(0!=a.compareTo(A.ONE)&&0!=a.compareTo(e)){for(var o=1;o++<r&&0!=a.compareTo(e);)if(0==(a=a.modPowInt(2,this)).compareTo(A.ONE))return!1;if(0!=a.compareTo(e))return!1}}return!0},A.prototype.clone=
/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
 */
function(){var t=w();return this.copyTo(t),t},A.prototype.intValue=function(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},A.prototype.byteValue=function(){return 0==this.t?this.s:this[0]<<24>>24},A.prototype.shortValue=function(){return 0==this.t?this.s:this[0]<<16>>16},A.prototype.signum=function(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1},A.prototype.toByteArray=function(){var t=this.t,e=new Array;e[0]=this.s;var r,n=this.DB-t*this.DB%8,i=0;if(t-- >0)for(n<this.DB&&(r=this[t]>>n)!=(this.s&this.DM)>>n&&(e[i++]=r|this.s<<this.DB-n);t>=0;)n<8?(r=(this[t]&(1<<n)-1)<<8-n,r|=this[--t]>>(n+=this.DB-8)):(r=this[t]>>(n-=8)&255,n<=0&&(n+=this.DB,--t)),0!=(128&r)&&(r|=-256),0==i&&(128&this.s)!=(128&r)&&++i,(i>0||r!=this.s)&&(e[i++]=r);return e},A.prototype.equals=function(t){return 0==this.compareTo(t)},A.prototype.min=function(t){return this.compareTo(t)<0?this:t},A.prototype.max=function(t){return this.compareTo(t)>0?this:t},A.prototype.and=function(t){var e=w();return this.bitwiseTo(t,O,e),e},A.prototype.or=function(t){var e=w();return this.bitwiseTo(t,k,e),e},A.prototype.xor=function(t){var e=w();return this.bitwiseTo(t,j,e),e},A.prototype.andNot=function(t){var e=w();return this.bitwiseTo(t,L,e),e},A.prototype.not=function(){for(var t=w(),e=0;e<this.t;++e)t[e]=this.DM&~this[e];return t.t=this.t,t.s=~this.s,t},A.prototype.shiftLeft=function(t){var e=w();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e},A.prototype.shiftRight=function(t){var e=w();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e},A.prototype.getLowestSetBit=function(){for(var t=0;t<this.t;++t)if(0!=this[t])return t*this.DB+V(this[t]);return this.s<0?this.t*this.DB:-1},A.prototype.bitCount=function(){for(var t=0,e=this.s&this.DM,r=0;r<this.t;++r)t+=_(this[r]^e);return t},A.prototype.testBit=function(t){var e=Math.floor(t/this.DB);return e>=this.t?0!=this.s:0!=(this[e]&1<<t%this.DB)},A.prototype.setBit=function(t){return this.changeBit(t,k)},A.prototype.clearBit=function(t){return this.changeBit(t,L)},A.prototype.flipBit=function(t){return this.changeBit(t,j)},A.prototype.add=function(t){var e=w();return this.addTo(t,e),e},A.prototype.subtract=function(t){var e=w();return this.subTo(t,e),e},A.prototype.multiply=function(t){var e=w();return this.multiplyTo(t,e),e},A.prototype.divide=function(t){var e=w();return this.divRemTo(t,e,null),e},A.prototype.remainder=function(t){var e=w();return this.divRemTo(t,null,e),e},A.prototype.divideAndRemainder=function(t){var e=w(),r=w();return this.divRemTo(t,e,r),new Array(e,r)},A.prototype.modPow=function(t,e){var r,n,i=t.bitLength(),s=P(1);if(i<=0)return s;r=i<18?1:i<48?3:i<144?4:i<768?5:6,n=i<8?new H(e):e.isEven()?new K(e):new N(e);var a=new Array,o=3,u=r-1,h=(1<<r)-1;if(a[1]=n.convert(this),r>1){var c=w();for(n.sqrTo(a[1],c);o<=h;)a[o]=w(),n.mulTo(c,a[o-2],a[o]),o+=2}var l,f,d=t.t-1,g=!0,p=w();for(i=R(t[d])-1;d>=0;){for(i>=u?l=t[d]>>i-u&h:(l=(t[d]&(1<<i+1)-1)<<u-i,d>0&&(l|=t[d-1]>>this.DB+i-u)),o=r;0==(1&l);)l>>=1,--o;if((i-=o)<0&&(i+=this.DB,--d),g)a[l].copyTo(s),g=!1;else{for(;o>1;)n.sqrTo(s,p),n.sqrTo(p,s),o-=2;o>0?n.sqrTo(s,p):(f=s,s=p,p=f),n.mulTo(p,a[l],s)}for(;d>=0&&0==(t[d]&1<<i);)n.sqrTo(s,p),f=s,s=p,p=f,--i<0&&(i=this.DB-1,--d)}return n.revert(s)},A.prototype.modInverse=function(t){var e=t.isEven();if(this.isEven()&&e||0==t.signum())return A.ZERO;for(var r=t.clone(),n=this.clone(),i=P(1),s=P(0),a=P(0),o=P(1);0!=r.signum();){for(;r.isEven();)r.rShiftTo(1,r),e?(i.isEven()&&s.isEven()||(i.addTo(this,i),s.subTo(t,s)),i.rShiftTo(1,i)):s.isEven()||s.subTo(t,s),s.rShiftTo(1,s);for(;n.isEven();)n.rShiftTo(1,n),e?(a.isEven()&&o.isEven()||(a.addTo(this,a),o.subTo(t,o)),a.rShiftTo(1,a)):o.isEven()||o.subTo(t,o),o.rShiftTo(1,o);r.compareTo(n)>=0?(r.subTo(n,r),e&&i.subTo(a,i),s.subTo(o,s)):(n.subTo(r,n),e&&a.subTo(i,a),o.subTo(s,o))}return 0!=n.compareTo(A.ONE)?A.ZERO:o.compareTo(t)>=0?o.subtract(t):o.signum()<0?(o.addTo(t,o),o.signum()<0?o.add(t):o):o},A.prototype.pow=function(t){return this.exp(t,new M)},A.prototype.gcd=function(t){var e=this.s<0?this.negate():this.clone(),r=t.s<0?t.negate():t.clone();if(e.compareTo(r)<0){var n=e;e=r,r=n}var i=e.getLowestSetBit(),s=r.getLowestSetBit();if(s<0)return e;for(i<s&&(s=i),s>0&&(e.rShiftTo(s,e),r.rShiftTo(s,r));e.signum()>0;)(i=e.getLowestSetBit())>0&&e.rShiftTo(i,e),(i=r.getLowestSetBit())>0&&r.rShiftTo(i,r),e.compareTo(r)>=0?(e.subTo(r,e),e.rShiftTo(1,e)):(r.subTo(e,r),r.rShiftTo(1,r));return s>0&&r.lShiftTo(s,r),r},A.prototype.isProbablePrime=function(t){var e,r=this.abs();if(1==r.t&&r[0]<=q[q.length-1]){for(e=0;e<q.length;++e)if(r[0]==q[e])return!0;return!1}if(r.isEven())return!1;for(e=1;e<q.length;){for(var n=q[e],i=e+1;i<q.length&&n<z;)n*=q[i++];for(n=r.modInt(n);e<i;)if(n%q[e++]==0)return!1}return r.millerRabin(t)},A.prototype.square=function(){var t=w();return this.squareTo(t),t},G.prototype.init=function(t){var e,r,n;for(e=0;e<256;++e)this.S[e]=e;for(r=0,e=0;e<256;++e)r=r+this.S[e]+t[e%t.length]&255,n=this.S[e],this.S[e]=this.S[r],this.S[r]=n;this.i=0,this.j=0},G.prototype.next=function(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]};var Y,$,J,W=256;
/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
 */function X(){!function(t){$[J++]^=255&t,$[J++]^=t>>8&255,$[J++]^=t>>16&255,$[J++]^=t>>24&255,J>=W&&(J-=W)}((new Date).getTime())}if(null==$){var Z;if($=new Array,J=0,void 0!==n&&(void 0!==n.crypto||void 0!==n.msCrypto)){var Q=n.crypto||n.msCrypto;if(Q.getRandomValues){var tt=new Uint8Array(32);for(Q.getRandomValues(tt),Z=0;Z<32;++Z)$[J++]=tt[Z]}else if("Netscape"==r.appName&&r.appVersion<"5"){var et=n.crypto.random(32);for(Z=0;Z<et.length;++Z)$[J++]=255&et.charCodeAt(Z)}}for(;J<W;)Z=Math.floor(65536*Math.random()),$[J++]=Z>>>8,$[J++]=255&Z;J=0,X()}function rt(){if(null==Y){for(X(),(Y=new G).init($),J=0;J<$.length;++J)$[J]=0;J=0}return Y.next()}function nt(){}
/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
 */
function it(t,e){return new A(t,e)}function st(t,e,r){for(var n="",i=0;n.length<e;)n+=r(String.fromCharCode.apply(String,t.concat([(4278190080&i)>>24,(16711680&i)>>16,(65280&i)>>8,255&i]))),i+=1;return n}function at(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null}function ot(t,e,r){for(var n="",i=0;n.length<e;)n+=r(t+String.fromCharCode.apply(String,[(4278190080&i)>>24,(16711680&i)>>16,(65280&i)>>8,255&i])),i+=1;return n}
/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
 */
function ut(t,e){this.x=e,this.q=t}function ht(t,e,r,n){this.curve=t,this.x=e,this.y=r,this.z=null==n?A.ONE:n,this.zinv=null}function ct(t,e,r){this.q=t,this.a=this.fromBigInteger(e),this.b=this.fromBigInteger(r),this.infinity=new ht(this,null,null)}nt.prototype.nextBytes=function(t){var e;for(e=0;e<t.length;++e)t[e]=rt()},at.prototype.doPublic=function(t){return t.modPowInt(this.e,this.n)},at.prototype.setPublic=function(t,e){if(this.isPublic=!0,this.isPrivate=!1,"string"!=typeof t)this.n=t,this.e=e;else{if(!(null!=t&&null!=e&&t.length>0&&e.length>0))throw"Invalid RSA public key";this.n=it(t,16),this.e=parseInt(e,16)}},at.prototype.encrypt=function(t){var e=function(t,e){if(e<t.length+11)throw"Message too long for RSA";for(var r=new Array,n=t.length-1;n>=0&&e>0;){var i=t.charCodeAt(n--);i<128?r[--e]=i:i>127&&i<2048?(r[--e]=63&i|128,r[--e]=i>>6|192):(r[--e]=63&i|128,r[--e]=i>>6&63|128,r[--e]=i>>12|224)}r[--e]=0;for(var s=new nt,a=new Array;e>2;){for(a[0]=0;0==a[0];)s.nextBytes(a);r[--e]=a[0]}return r[--e]=2,r[--e]=0,new A(r)}(t,this.n.bitLength()+7>>3);if(null==e)return null;var r=this.doPublic(e);if(null==r)return null;var n=r.toString(16);return 0==(1&n.length)?n:"0"+n},at.prototype.encryptOAEP=function(t,e,r){var n=function(t,e,r,n){var i=ft.crypto.MessageDigest,s=ft.crypto.Util,a=null;if(r||(r="sha1"),"string"==typeof r&&(a=i.getCanonicalAlgName(r),n=i.getHashLength(a),r=function(t){return Bt(s.hashHex(Ct(t),a))}),t.length+2*n+2>e)throw"Message too long for RSA";var o,u="";for(o=0;o<e-t.length-2*n-2;o+=1)u+="\0";var h=r("")+u+""+t,c=new Array(n);(new nt).nextBytes(c);var l=st(c,h.length,r),f=[];for(o=0;o<h.length;o+=1)f[o]=h.charCodeAt(o)^l.charCodeAt(o);var d=st(f,c.length,r),g=[0];for(o=0;o<c.length;o+=1)g[o+1]=c[o]^d.charCodeAt(o);return new A(g.concat(f))}(t,this.n.bitLength()+7>>3,e,r);if(null==n)return null;var i=this.doPublic(n);if(null==i)return null;var s=i.toString(16);return 0==(1&s.length)?s:"0"+s},at.prototype.type="RSA",at.prototype.doPrivate=function(t){if(null==this.p||null==this.q)return t.modPow(this.d,this.n);for(var e=t.mod(this.p).modPow(this.dmp1,this.p),r=t.mod(this.q).modPow(this.dmq1,this.q);e.compareTo(r)<0;)e=e.add(this.p);return e.subtract(r).multiply(this.coeff).mod(this.p).multiply(this.q).add(r)},at.prototype.setPrivate=function(t,e,r){if(this.isPrivate=!0,"string"!=typeof t)this.n=t,this.e=e,this.d=r;else{if(!(null!=t&&null!=e&&t.length>0&&e.length>0))throw"Invalid RSA private key";this.n=it(t,16),this.e=parseInt(e,16),this.d=it(r,16)}},at.prototype.setPrivateEx=function(t,e,r,n,i,s,a,o){if(this.isPrivate=!0,this.isPublic=!1,null==t)throw"RSASetPrivateEx N == null";if(null==e)throw"RSASetPrivateEx E == null";if(0==t.length)throw"RSASetPrivateEx N.length == 0";if(0==e.length)throw"RSASetPrivateEx E.length == 0";if(!(null!=t&&null!=e&&t.length>0&&e.length>0))throw"Invalid RSA private key in RSASetPrivateEx";this.n=it(t,16),this.e=parseInt(e,16),this.d=it(r,16),this.p=it(n,16),this.q=it(i,16),this.dmp1=it(s,16),this.dmq1=it(a,16),this.coeff=it(o,16)},at.prototype.generate=function(t,e){var r=new nt,n=t>>1;this.e=parseInt(e,16);for(var i=new A(e,16);;){for(;this.p=new A(t-n,1,r),0!=this.p.subtract(A.ONE).gcd(i).compareTo(A.ONE)||!this.p.isProbablePrime(10););for(;this.q=new A(n,1,r),0!=this.q.subtract(A.ONE).gcd(i).compareTo(A.ONE)||!this.q.isProbablePrime(10););if(this.p.compareTo(this.q)<=0){var s=this.p;this.p=this.q,this.q=s}var a=this.p.subtract(A.ONE),o=this.q.subtract(A.ONE),u=a.multiply(o);if(0==u.gcd(i).compareTo(A.ONE)){this.n=this.p.multiply(this.q),this.d=i.modInverse(u),this.dmp1=this.d.mod(a),this.dmq1=this.d.mod(o),this.coeff=this.q.modInverse(this.p);break}}this.isPrivate=!0},at.prototype.decrypt=function(t){var e=it(t,16),r=this.doPrivate(e);return null==r?null:
/*! (c) Tom Wu | http://www-cs-students.stanford.edu/~tjw/jsbn/
 */
function(t,e){for(var r=t.toByteArray(),n=0;n<r.length&&0==r[n];)++n;if(r.length-n!=e-1||2!=r[n])return null;for(++n;0!=r[n];)if(++n>=r.length)return null;for(var i="";++n<r.length;){var s=255&r[n];s<128?i+=String.fromCharCode(s):s>191&&s<224?(i+=String.fromCharCode((31&s)<<6|63&r[n+1]),++n):(i+=String.fromCharCode((15&s)<<12|(63&r[n+1])<<6|63&r[n+2]),n+=2)}return i}(r,this.n.bitLength()+7>>3)},at.prototype.decryptOAEP=function(t,e,r){var n=it(t,16),i=this.doPrivate(n);return null==i?null:function(t,e,r,n){var i=ft.crypto.MessageDigest,s=ft.crypto.Util,a=null;for(r||(r="sha1"),"string"==typeof r&&(a=i.getCanonicalAlgName(r),n=i.getHashLength(a),r=function(t){return Bt(s.hashHex(Ct(t),a))}),t=t.toByteArray(),o=0;o<t.length;o+=1)t[o]&=255;for(;t.length<e;)t.unshift(0);if((t=String.fromCharCode.apply(String,t)).length<2*n+2)throw"Cipher too short";var o,u=t.substr(1,n),h=t.substr(n+1),c=ot(h,n,r),l=[];for(o=0;o<u.length;o+=1)l[o]=u.charCodeAt(o)^c.charCodeAt(o);var f=ot(String.fromCharCode.apply(String,l),t.length-n,r),d=[];for(o=0;o<h.length;o+=1)d[o]=h.charCodeAt(o)^f.charCodeAt(o);if((d=String.fromCharCode.apply(String,d)).substr(0,n)!==r(""))throw"Hash mismatch";var g=(d=d.substr(n)).indexOf("");if((-1!=g?d.substr(0,g).lastIndexOf("\0"):-1)+1!=g)throw"Malformed data";return d.substr(g+1)}(i,this.n.bitLength()+7>>3,e,r)},ut.prototype.equals=function(t){return t==this||this.q.equals(t.q)&&this.x.equals(t.x)},ut.prototype.toBigInteger=function(){return this.x},ut.prototype.negate=function(){return new ut(this.q,this.x.negate().mod(this.q))},ut.prototype.add=function(t){return new ut(this.q,this.x.add(t.toBigInteger()).mod(this.q))},ut.prototype.subtract=function(t){return new ut(this.q,this.x.subtract(t.toBigInteger()).mod(this.q))},ut.prototype.multiply=function(t){return new ut(this.q,this.x.multiply(t.toBigInteger()).mod(this.q))},ut.prototype.square=function(){return new ut(this.q,this.x.square().mod(this.q))},ut.prototype.divide=function(t){return new ut(this.q,this.x.multiply(t.toBigInteger().modInverse(this.q)).mod(this.q))},ht.prototype.getX=function(){return null==this.zinv&&(this.zinv=this.z.modInverse(this.curve.q)),this.curve.fromBigInteger(this.x.toBigInteger().multiply(this.zinv).mod(this.curve.q))},ht.prototype.getY=function(){return null==this.zinv&&(this.zinv=this.z.modInverse(this.curve.q)),this.curve.fromBigInteger(this.y.toBigInteger().multiply(this.zinv).mod(this.curve.q))},ht.prototype.equals=function(t){return t==this||(this.isInfinity()?t.isInfinity():t.isInfinity()?this.isInfinity():!!t.y.toBigInteger().multiply(this.z).subtract(this.y.toBigInteger().multiply(t.z)).mod(this.curve.q).equals(A.ZERO)&&t.x.toBigInteger().multiply(this.z).subtract(this.x.toBigInteger().multiply(t.z)).mod(this.curve.q).equals(A.ZERO))},ht.prototype.isInfinity=function(){return null==this.x&&null==this.y||this.z.equals(A.ZERO)&&!this.y.toBigInteger().equals(A.ZERO)},ht.prototype.negate=function(){return new ht(this.curve,this.x,this.y.negate(),this.z)},ht.prototype.add=function(t){if(this.isInfinity())return t;if(t.isInfinity())return this;var e=t.y.toBigInteger().multiply(this.z).subtract(this.y.toBigInteger().multiply(t.z)).mod(this.curve.q),r=t.x.toBigInteger().multiply(this.z).subtract(this.x.toBigInteger().multiply(t.z)).mod(this.curve.q);if(A.ZERO.equals(r))return A.ZERO.equals(e)?this.twice():this.curve.getInfinity();var n=new A("3"),i=this.x.toBigInteger(),s=this.y.toBigInteger(),a=(t.x.toBigInteger(),t.y.toBigInteger(),r.square()),o=a.multiply(r),u=i.multiply(a),h=e.square().multiply(this.z),c=h.subtract(u.shiftLeft(1)).multiply(t.z).subtract(o).multiply(r).mod(this.curve.q),l=u.multiply(n).multiply(e).subtract(s.multiply(o)).subtract(h.multiply(e)).multiply(t.z).add(e.multiply(o)).mod(this.curve.q),f=o.multiply(this.z).multiply(t.z).mod(this.curve.q);return new ht(this.curve,this.curve.fromBigInteger(c),this.curve.fromBigInteger(l),f)},ht.prototype.twice=function(){if(this.isInfinity())return this;if(0==this.y.toBigInteger().signum())return this.curve.getInfinity();var t=new A("3"),e=this.x.toBigInteger(),r=this.y.toBigInteger(),n=r.multiply(this.z),i=n.multiply(r).mod(this.curve.q),s=this.curve.a.toBigInteger(),a=e.square().multiply(t);A.ZERO.equals(s)||(a=a.add(this.z.square().multiply(s)));var o=(a=a.mod(this.curve.q)).square().subtract(e.shiftLeft(3).multiply(i)).shiftLeft(1).multiply(n).mod(this.curve.q),u=a.multiply(t).multiply(e).subtract(i.shiftLeft(1)).shiftLeft(2).multiply(i).subtract(a.square().multiply(a)).mod(this.curve.q),h=n.square().multiply(n).shiftLeft(3).mod(this.curve.q);return new ht(this.curve,this.curve.fromBigInteger(o),this.curve.fromBigInteger(u),h)},ht.prototype.multiply=function(t){if(this.isInfinity())return this;if(0==t.signum())return this.curve.getInfinity();var e,r=t,n=r.multiply(new A("3")),i=this.negate(),s=this;for(e=n.bitLength()-2;e>0;--e){s=s.twice();var a=n.testBit(e);a!=r.testBit(e)&&(s=s.add(a?this:i))}return s},ht.prototype.multiplyTwo=function(t,e,r){var n;n=t.bitLength()>r.bitLength()?t.bitLength()-1:r.bitLength()-1;for(var i=this.curve.getInfinity(),s=this.add(e);n>=0;)i=i.twice(),t.testBit(n)?i=r.testBit(n)?i.add(s):i.add(this):r.testBit(n)&&(i=i.add(e)),--n;return i},ct.prototype.getQ=function(){return this.q},ct.prototype.getA=function(){return this.a},ct.prototype.getB=function(){return this.b},ct.prototype.equals=function(t){return t==this||this.q.equals(t.q)&&this.a.equals(t.a)&&this.b.equals(t.b)},ct.prototype.getInfinity=function(){return this.infinity},ct.prototype.fromBigInteger=function(t){return new ut(this.q,t)},ct.prototype.decodePointHex=function(t){switch(parseInt(t.substr(0,2),16)){case 0:return this.infinity;case 2:case 3:return null;case 4:case 6:case 7:var e=(t.length-2)/2,r=t.substr(2,e),n=t.substr(e+2,e);return new ht(this,this.fromBigInteger(new A(r,16)),this.fromBigInteger(new A(n,16)));default:return null}},
/*! (c) Stefan Thomas | https://github.com/bitcoinjs/bitcoinjs-lib
 */
ut.prototype.getByteLength=function(){return Math.floor((this.toBigInteger().bitLength()+7)/8)},ht.prototype.getEncoded=function(t){var e=function(t,e){var r=t.toByteArrayUnsigned();if(e<r.length)r=r.slice(r.length-e);else for(;e>r.length;)r.unshift(0);return r},r=this.getX().toBigInteger(),n=this.getY().toBigInteger(),i=e(r,32);return t?n.isEven()?i.unshift(2):i.unshift(3):(i.unshift(4),i=i.concat(e(n,32))),i},ht.decodeFrom=function(t,e){e[0];var r=e.length-1,n=e.slice(1,1+r/2),i=e.slice(1+r/2,1+r);n.unshift(0),i.unshift(0);var s=new A(n),a=new A(i);return new ht(t,t.fromBigInteger(s),t.fromBigInteger(a))},ht.decodeFromHex=function(t,e){e.substr(0,2);var r=e.length-2,n=e.substr(2,r/2),i=e.substr(2+r/2,r/2),s=new A(n,16),a=new A(i,16);return new ht(t,t.fromBigInteger(s),t.fromBigInteger(a))},ht.prototype.add2D=function(t){if(this.isInfinity())return t;if(t.isInfinity())return this;if(this.x.equals(t.x))return this.y.equals(t.y)?this.twice():this.curve.getInfinity();var e=t.x.subtract(this.x),r=t.y.subtract(this.y).divide(e),n=r.square().subtract(this.x).subtract(t.x),i=r.multiply(this.x.subtract(n)).subtract(this.y);return new ht(this.curve,n,i)},ht.prototype.twice2D=function(){if(this.isInfinity())return this;if(0==this.y.toBigInteger().signum())return this.curve.getInfinity();var t=this.curve.fromBigInteger(A.valueOf(2)),e=this.curve.fromBigInteger(A.valueOf(3)),r=this.x.square().multiply(e).add(this.curve.a).divide(this.y.multiply(t)),n=r.square().subtract(this.x.multiply(t)),i=r.multiply(this.x.subtract(n)).subtract(this.y);return new ht(this.curve,n,i)},ht.prototype.multiply2D=function(t){if(this.isInfinity())return this;if(0==t.signum())return this.curve.getInfinity();var e,r=t,n=r.multiply(new A("3")),i=this.negate(),s=this;for(e=n.bitLength()-2;e>0;--e){s=s.twice();var a=n.testBit(e);a!=r.testBit(e)&&(s=s.add2D(a?this:i))}return s},ht.prototype.isOnCurve=function(){var t=this.getX().toBigInteger(),e=this.getY().toBigInteger(),r=this.curve.getA().toBigInteger(),n=this.curve.getB().toBigInteger(),i=this.curve.getQ(),s=e.multiply(e).mod(i),a=t.multiply(t).multiply(t).add(r.multiply(t)).add(n).mod(i);return s.equals(a)},ht.prototype.toString=function(){return"("+this.getX().toBigInteger().toString()+","+this.getY().toBigInteger().toString()+")"},ht.prototype.validate=function(){var t=this.curve.getQ();if(this.isInfinity())throw new Error("Point is at infinity.");var e=this.getX().toBigInteger(),r=this.getY().toBigInteger();if(e.compareTo(A.ONE)<0||e.compareTo(t.subtract(A.ONE))>0)throw new Error("x coordinate out of bounds");if(r.compareTo(A.ONE)<0||r.compareTo(t.subtract(A.ONE))>0)throw new Error("y coordinate out of bounds");if(!this.isOnCurve())throw new Error("Point is not on the curve.");if(this.multiply(t).isInfinity())throw new Error("Point is not a scalar multiple of G.");return!0};
/*! Mike Samuel (c) 2009 | code.google.com/p/json-sans-eval
 */
var lt=function(){var t=new RegExp('(?:false|true|null|[\\{\\}\\[\\]]|(?:-?\\b(?:0|[1-9][0-9]*)(?:\\.[0-9]+)?(?:[eE][+-]?[0-9]+)?\\b)|(?:"(?:[^\\0-\\x08\\x0a-\\x1f"\\\\]|\\\\(?:["/\\\\bfnrt]|u[0-9A-Fa-f]{4}))*"))',"g"),e=new RegExp("\\\\(?:([^u])|u(.{4}))","g"),r={'"':'"',"/":"/","\\":"\\",b:"\b",f:"\f",n:"\n",r:"\r",t:"\t"};function n(t,e,n){return e?r[e]:String.fromCharCode(parseInt(n,16))}var i=new String(""),s=(Object,Array,Object.hasOwnProperty);return function(r,a){var o,u,h=r.match(t),c=h[0],l=!1;"{"===c?o={}:"["===c?o=[]:(o=[],l=!0);for(var f=[o],d=1-l,g=h.length;d<g;++d){var p;switch((c=h[d]).charCodeAt(0)){default:(p=f[0])[u||p.length]=+c,u=void 0;break;case 34:if(-1!==(c=c.substring(1,c.length-1)).indexOf("\\")&&(c=c.replace(e,n)),p=f[0],!u){if(!(p instanceof Array)){u=c||i;break}u=p.length}p[u]=c,u=void 0;break;case 91:p=f[0],f.unshift(p[u||p.length]=[]),u=void 0;break;case 93:f.shift();break;case 102:(p=f[0])[u||p.length]=!1,u=void 0;break;case 110:(p=f[0])[u||p.length]=null,u=void 0;break;case 116:(p=f[0])[u||p.length]=!0,u=void 0;break;case 123:p=f[0],f.unshift(p[u||p.length]={}),u=void 0;break;case 125:f.shift()}}if(l){if(1!==f.length)throw new Error;o=o[0]}else if(f.length)throw new Error;if(a){var y=function(t,e){var r=t[e];if(r&&"object"==typeof r){var n=null;for(var i in r)if(s.call(r,i)&&r!==t){var o=y(r,i);void 0!==o?r[i]=o:(n||(n=[]),n.push(i))}if(n)for(var u=n.length;--u>=0;)delete r[n[u]]}return a.call(t,e,r)};o=y({"":o},"")}return o}}();void 0!==ft&&ft||(ft={}),void 0!==ft.asn1&&ft.asn1||(ft.asn1={}),ft.asn1.ASN1Util=new function(){this.integerToByteHex=function(t){var e=t.toString(16);return e.length%2==1&&(e="0"+e),e},this.bigIntToMinTwosComplementsHex=function(t){var e=t.toString(16);if("-"!=e.substr(0,1))e.length%2==1?e="0"+e:e.match(/^[0-7]/)||(e="00"+e);else{var r=e.substr(1).length;r%2==1?r+=1:e.match(/^[0-7]/)||(r+=2);for(var n="",i=0;i<r;i++)n+="f";e=new A(n,16).xor(t).add(A.ONE).toString(16).replace(/^-/,"")}return e},this.getPEMStringFromHex=function(t,e){return Pt(t,e)},this.newObject=function(t){var e=ft.asn1,r=e.DERBoolean,n=e.DERInteger,i=e.DERBitString,s=e.DEROctetString,a=e.DERNull,o=e.DERObjectIdentifier,u=e.DEREnumerated,h=e.DERUTF8String,c=e.DERNumericString,l=e.DERPrintableString,f=e.DERTeletexString,d=e.DERIA5String,g=e.DERUTCTime,p=e.DERGeneralizedTime,y=e.DERSequence,v=e.DERSet,m=e.DERTaggedObject,F=e.ASN1Util.newObject,S=Object.keys(t);if(1!=S.length)throw"key of param shall be only one.";var b=S[0];if(-1==":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:tag:".indexOf(":"+b+":"))throw"undefined key: "+b;if("bool"==b)return new r(t[b]);if("int"==b)return new n(t[b]);if("bitstr"==b)return new i(t[b]);if("octstr"==b)return new s(t[b]);if("null"==b)return new a(t[b]);if("oid"==b)return new o(t[b]);if("enum"==b)return new u(t[b]);if("utf8str"==b)return new h(t[b]);if("numstr"==b)return new c(t[b]);if("prnstr"==b)return new l(t[b]);if("telstr"==b)return new f(t[b]);if("ia5str"==b)return new d(t[b]);if("utctime"==b)return new g(t[b]);if("gentime"==b)return new p(t[b]);if("seq"==b){for(var x=t[b],A=[],w=0;w<x.length;w++){var E=F(x[w]);A.push(E)}return new y({array:A})}if("set"==b){for(x=t[b],A=[],w=0;w<x.length;w++){E=F(x[w]);A.push(E)}return new v({array:A})}if("tag"==b){var B=t[b];if("[object Array]"===Object.prototype.toString.call(B)&&3==B.length){var C=F(B[2]);return new m({tag:B[0],explicit:B[1],obj:C})}var D={};if(void 0!==B.explicit&&(D.explicit=B.explicit),void 0!==B.tag&&(D.tag=B.tag),void 0===B.obj)throw"obj shall be specified for 'tag'.";return D.obj=F(B.obj),new m(D)}},this.jsonToASN1HEX=function(t){return this.newObject(t).getEncodedHex()}},ft.asn1.ASN1Util.oidHexToInt=function(t){for(var e="",r=parseInt(t.substr(0,2),16),n=(e=Math.floor(r/40)+"."+r%40,""),i=2;i<t.length;i+=2){var s=("00000000"+parseInt(t.substr(i,2),16).toString(2)).slice(-8);if(n+=s.substr(1,7),"0"==s.substr(0,1))e=e+"."+new A(n,2).toString(10),n=""}return e},ft.asn1.ASN1Util.oidIntToHex=function(t){var e=function(t){var e=t.toString(16);return 1==e.length&&(e="0"+e),e},r=function(t){var r="",n=new A(t,10).toString(2),i=7-n.length%7;7==i&&(i=0);for(var s="",a=0;a<i;a++)s+="0";n=s+n;for(a=0;a<n.length-1;a+=7){var o=n.substr(a,7);a!=n.length-7&&(o="1"+o),r+=e(parseInt(o,2))}return r};if(!t.match(/^[0-9.]+$/))throw"malformed oid string: "+t;var n="",i=t.split("."),s=40*parseInt(i[0])+parseInt(i[1]);n+=e(s),i.splice(0,2);for(var a=0;a<i.length;a++)n+=r(i[a]);return n},ft.asn1.ASN1Object=function(){this.getLengthHexFromValue=function(){if(void 0===this.hV||null==this.hV)throw"this.hV is null or undefined.";if(this.hV.length%2==1)throw"value hex must be even length: n="+"".length+",v="+this.hV;var t=this.hV.length/2,e=t.toString(16);if(e.length%2==1&&(e="0"+e),t<128)return e;var r=e.length/2;if(r>15)throw"ASN.1 length too long to represent by 8x: n = "+t.toString(16);return(128+r).toString(16)+e},this.getEncodedHex=function(){return(null==this.hTLV||this.isModified)&&(this.hV=this.getFreshValueHex(),this.hL=this.getLengthHexFromValue(),this.hTLV=this.hT+this.hL+this.hV,this.isModified=!1),this.hTLV},this.getValueHex=function(){return this.getEncodedHex(),this.hV},this.getFreshValueHex=function(){return""}},ft.asn1.DERAbstractString=function(t){ft.asn1.DERAbstractString.superclass.constructor.call(this);this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=wt(this.s).toLowerCase()},this.setStringHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t?this.setString(t):void 0!==t.str?this.setString(t.str):void 0!==t.hex&&this.setStringHex(t.hex))},i.lang.extend(ft.asn1.DERAbstractString,ft.asn1.ASN1Object),ft.asn1.DERAbstractTime=function(t){ft.asn1.DERAbstractTime.superclass.constructor.call(this);this.localDateToUTC=function(t){return utc=t.getTime()+6e4*t.getTimezoneOffset(),new Date(utc)},this.formatDate=function(t,e,r){var n=this.zeroPadding,i=this.localDateToUTC(t),s=String(i.getFullYear());"utc"==e&&(s=s.substr(2,2));var a=s+n(String(i.getMonth()+1),2)+n(String(i.getDate()),2)+n(String(i.getHours()),2)+n(String(i.getMinutes()),2)+n(String(i.getSeconds()),2);if(!0===r){var o=i.getMilliseconds();if(0!=o){var u=n(String(o),3);a=a+"."+(u=u.replace(/[0]+$/,""))}}return a+"Z"},this.zeroPadding=function(t,e){return t.length>=e?t:new Array(e-t.length+1).join("0")+t},this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=Ft(t)},this.setByDateValue=function(t,e,r,n,i,s){var a=new Date(Date.UTC(t,e-1,r,n,i,s,0));this.setByDate(a)},this.getFreshValueHex=function(){return this.hV}},i.lang.extend(ft.asn1.DERAbstractTime,ft.asn1.ASN1Object),ft.asn1.DERAbstractStructured=function(t){ft.asn1.DERAbstractString.superclass.constructor.call(this);this.setByASN1ObjectArray=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array=t},this.appendASN1Object=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array.push(t)},this.asn1Array=new Array,void 0!==t&&void 0!==t.array&&(this.asn1Array=t.array)},i.lang.extend(ft.asn1.DERAbstractStructured,ft.asn1.ASN1Object),ft.asn1.DERBoolean=function(){ft.asn1.DERBoolean.superclass.constructor.call(this),this.hT="01",this.hTLV="0101ff"},i.lang.extend(ft.asn1.DERBoolean,ft.asn1.ASN1Object),ft.asn1.DERInteger=function(t){ft.asn1.DERInteger.superclass.constructor.call(this),this.hT="02",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=ft.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new A(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.bigint?this.setByBigInteger(t.bigint):void 0!==t.int?this.setByInteger(t.int):"number"==typeof t?this.setByInteger(t):void 0!==t.hex&&this.setValueHex(t.hex))},i.lang.extend(ft.asn1.DERInteger,ft.asn1.ASN1Object),ft.asn1.DERBitString=function(t){if(void 0!==t&&void 0!==t.obj){var e=ft.asn1.ASN1Util.newObject(t.obj);t.hex="00"+e.getEncodedHex()}ft.asn1.DERBitString.superclass.constructor.call(this),this.hT="03",this.setHexValueIncludingUnusedBits=function(t){this.hTLV=null,this.isModified=!0,this.hV=t},this.setUnusedBitsAndHexValue=function(t,e){if(t<0||7<t)throw"unused bits shall be from 0 to 7: u = "+t;var r="0"+t;this.hTLV=null,this.isModified=!0,this.hV=r+e},this.setByBinaryString=function(t){var e=8-(t=t.replace(/0+$/,"")).length%8;8==e&&(e=0);for(var r=0;r<=e;r++)t+="0";var n="";for(r=0;r<t.length-1;r+=8){var i=t.substr(r,8),s=parseInt(i,2).toString(16);1==s.length&&(s="0"+s),n+=s}this.hTLV=null,this.isModified=!0,this.hV="0"+e+n},this.setByBooleanArray=function(t){for(var e="",r=0;r<t.length;r++)1==t[r]?e+="1":e+="0";this.setByBinaryString(e)},this.newFalseArray=function(t){for(var e=new Array(t),r=0;r<t;r++)e[r]=!1;return e},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t&&t.toLowerCase().match(/^[0-9a-f]+$/)?this.setHexValueIncludingUnusedBits(t):void 0!==t.hex?this.setHexValueIncludingUnusedBits(t.hex):void 0!==t.bin?this.setByBinaryString(t.bin):void 0!==t.array&&this.setByBooleanArray(t.array))},i.lang.extend(ft.asn1.DERBitString,ft.asn1.ASN1Object),ft.asn1.DEROctetString=function(t){if(void 0!==t&&void 0!==t.obj){var e=ft.asn1.ASN1Util.newObject(t.obj);t.hex=e.getEncodedHex()}ft.asn1.DEROctetString.superclass.constructor.call(this,t),this.hT="04"},i.lang.extend(ft.asn1.DEROctetString,ft.asn1.DERAbstractString),ft.asn1.DERNull=function(){ft.asn1.DERNull.superclass.constructor.call(this),this.hT="05",this.hTLV="0500"},i.lang.extend(ft.asn1.DERNull,ft.asn1.ASN1Object),ft.asn1.DERObjectIdentifier=function(t){var e=function(t){var e=t.toString(16);return 1==e.length&&(e="0"+e),e},r=function(t){var r="",n=new A(t,10).toString(2),i=7-n.length%7;7==i&&(i=0);for(var s="",a=0;a<i;a++)s+="0";n=s+n;for(a=0;a<n.length-1;a+=7){var o=n.substr(a,7);a!=n.length-7&&(o="1"+o),r+=e(parseInt(o,2))}return r};ft.asn1.DERObjectIdentifier.superclass.constructor.call(this),this.hT="06",this.setValueHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.setValueOidString=function(t){if(!t.match(/^[0-9.]+$/))throw"malformed oid string: "+t;var n="",i=t.split("."),s=40*parseInt(i[0])+parseInt(i[1]);n+=e(s),i.splice(0,2);for(var a=0;a<i.length;a++)n+=r(i[a]);this.hTLV=null,this.isModified=!0,this.s=null,this.hV=n},this.setValueName=function(t){var e=ft.asn1.x509.OID.name2oid(t);if(""===e)throw"DERObjectIdentifier oidName undefined: "+t;this.setValueOidString(e)},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t?t.match(/^[0-2].[0-9.]+$/)?this.setValueOidString(t):this.setValueName(t):void 0!==t.oid?this.setValueOidString(t.oid):void 0!==t.hex?this.setValueHex(t.hex):void 0!==t.name&&this.setValueName(t.name))},i.lang.extend(ft.asn1.DERObjectIdentifier,ft.asn1.ASN1Object),ft.asn1.DEREnumerated=function(t){ft.asn1.DEREnumerated.superclass.constructor.call(this),this.hT="0a",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=ft.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new A(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.int?this.setByInteger(t.int):"number"==typeof t?this.setByInteger(t):void 0!==t.hex&&this.setValueHex(t.hex))},i.lang.extend(ft.asn1.DEREnumerated,ft.asn1.ASN1Object),ft.asn1.DERUTF8String=function(t){ft.asn1.DERUTF8String.superclass.constructor.call(this,t),this.hT="0c"},i.lang.extend(ft.asn1.DERUTF8String,ft.asn1.DERAbstractString),ft.asn1.DERNumericString=function(t){ft.asn1.DERNumericString.superclass.constructor.call(this,t),this.hT="12"},i.lang.extend(ft.asn1.DERNumericString,ft.asn1.DERAbstractString),ft.asn1.DERPrintableString=function(t){ft.asn1.DERPrintableString.superclass.constructor.call(this,t),this.hT="13"},i.lang.extend(ft.asn1.DERPrintableString,ft.asn1.DERAbstractString),ft.asn1.DERTeletexString=function(t){ft.asn1.DERTeletexString.superclass.constructor.call(this,t),this.hT="14"},i.lang.extend(ft.asn1.DERTeletexString,ft.asn1.DERAbstractString),ft.asn1.DERIA5String=function(t){ft.asn1.DERIA5String.superclass.constructor.call(this,t),this.hT="16"},i.lang.extend(ft.asn1.DERIA5String,ft.asn1.DERAbstractString),ft.asn1.DERUTCTime=function(t){ft.asn1.DERUTCTime.superclass.constructor.call(this,t),this.hT="17",this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"utc"),this.hV=Ft(this.s)},this.getFreshValueHex=function(){return void 0===this.date&&void 0===this.s&&(this.date=new Date,this.s=this.formatDate(this.date,"utc"),this.hV=Ft(this.s)),this.hV},void 0!==t&&(void 0!==t.str?this.setString(t.str):"string"==typeof t&&t.match(/^[0-9]{12}Z$/)?this.setString(t):void 0!==t.hex?this.setStringHex(t.hex):void 0!==t.date&&this.setByDate(t.date))},i.lang.extend(ft.asn1.DERUTCTime,ft.asn1.DERAbstractTime),ft.asn1.DERGeneralizedTime=function(t){ft.asn1.DERGeneralizedTime.superclass.constructor.call(this,t),this.hT="18",this.withMillis=!1,this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=Ft(this.s)},this.getFreshValueHex=function(){return void 0===this.date&&void 0===this.s&&(this.date=new Date,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=Ft(this.s)),this.hV},void 0!==t&&(void 0!==t.str?this.setString(t.str):"string"==typeof t&&t.match(/^[0-9]{14}Z$/)?this.setString(t):void 0!==t.hex?this.setStringHex(t.hex):void 0!==t.date&&this.setByDate(t.date),!0===t.millis&&(this.withMillis=!0))},i.lang.extend(ft.asn1.DERGeneralizedTime,ft.asn1.DERAbstractTime),ft.asn1.DERSequence=function(t){ft.asn1.DERSequence.superclass.constructor.call(this,t),this.hT="30",this.getFreshValueHex=function(){for(var t="",e=0;e<this.asn1Array.length;e++){t+=this.asn1Array[e].getEncodedHex()}return this.hV=t,this.hV}},i.lang.extend(ft.asn1.DERSequence,ft.asn1.DERAbstractStructured),ft.asn1.DERSet=function(t){ft.asn1.DERSet.superclass.constructor.call(this,t),this.hT="31",this.sortFlag=!0,this.getFreshValueHex=function(){for(var t=new Array,e=0;e<this.asn1Array.length;e++){var r=this.asn1Array[e];t.push(r.getEncodedHex())}return 1==this.sortFlag&&t.sort(),this.hV=t.join(""),this.hV},void 0!==t&&void 0!==t.sortflag&&0==t.sortflag&&(this.sortFlag=!1)},i.lang.extend(ft.asn1.DERSet,ft.asn1.DERAbstractStructured),ft.asn1.DERTaggedObject=function(t){ft.asn1.DERTaggedObject.superclass.constructor.call(this),this.hT="a0",this.hV="",this.isExplicit=!0,this.asn1Object=null,this.setASN1Object=function(t,e,r){this.hT=e,this.isExplicit=t,this.asn1Object=r,this.isExplicit?(this.hV=this.asn1Object.getEncodedHex(),this.hTLV=null,this.isModified=!0):(this.hV=null,this.hTLV=r.getEncodedHex(),this.hTLV=this.hTLV.replace(/^../,e),this.isModified=!1)},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.tag&&(this.hT=t.tag),void 0!==t.explicit&&(this.isExplicit=t.explicit),void 0!==t.obj&&(this.asn1Object=t.obj,this.setASN1Object(this.isExplicit,this.hT,this.asn1Object)))},i.lang.extend(ft.asn1.DERTaggedObject,ft.asn1.ASN1Object);var ft,dt,gt,pt=new function(){};function yt(t){for(var e=new Array,r=0;r<t.length;r++)e[r]=t.charCodeAt(r);return e}function vt(t){for(var e="",r=0;r<t.length;r++)e+=String.fromCharCode(t[r]);return e}function mt(t){for(var e="",r=0;r<t.length;r++){var n=t[r].toString(16);1==n.length&&(n="0"+n),e+=n}return e}function Ft(t){return mt(yt(t))}function St(t){return t=(t=(t=t.replace(/\=/g,"")).replace(/\+/g,"-")).replace(/\//g,"_")}function bt(t){return t.length%4==2?t+="==":t.length%4==3&&(t+="="),t=(t=t.replace(/-/g,"+")).replace(/_/g,"/")}function xt(t){return t.length%2==1&&(t="0"+t),St(S(t))}function At(t){return b(bt(t))}function wt(t){return Ot(_t(t))}function Et(t){return decodeURIComponent(kt(t))}function Bt(t){for(var e="",r=0;r<t.length-1;r+=2)e+=String.fromCharCode(parseInt(t.substr(r,2),16));return e}function Ct(t){for(var e="",r=0;r<t.length;r++)e+=("0"+t.charCodeAt(r).toString(16)).slice(-2);return e}function Dt(t){return S(t)}function Tt(t){var e=Dt(t).replace(/(.{64})/g,"$1\r\n");return e=e.replace(/\r\n$/,"")}function It(t){return b(t.replace(/[^0-9A-Za-z\/+=]*/g,""))}function Pt(t,e){return"-----BEGIN "+e+"-----\r\n"+Tt(t)+"\r\n-----END "+e+"-----\r\n"}function Rt(t,e){if(-1==t.indexOf("-----BEGIN "))throw"can't find PEM header: "+e;return It(t=void 0!==e?(t=t.replace("-----BEGIN "+e+"-----","")).replace("-----END "+e+"-----",""):(t=t.replace(/-----BEGIN [^-]+-----/,"")).replace(/-----END [^-]+-----/,""))}function Ht(t){var e,r,n,i,s,a,o,u,h,c,l;if(l=t.match(/^(\d{2}|\d{4})(\d\d)(\d\d)(\d\d)(\d\d)(\d\d)(|\.\d+)Z$/))return u=l[1],e=parseInt(u),2===u.length&&(50<=e&&e<100?e=1900+e:0<=e&&e<50&&(e=2e3+e)),r=parseInt(l[2])-1,n=parseInt(l[3]),i=parseInt(l[4]),s=parseInt(l[5]),a=parseInt(l[6]),o=0,""!==(h=l[7])&&(c=(h.substr(1)+"00").substr(0,3),o=parseInt(c)),Date.UTC(e,r,n,i,s,a,o);throw"unsupported zulu format: "+t}function Nt(t){return~~(Ht(t)/1e3)}function Ot(t){return t.replace(/%/g,"")}function kt(t){return t.replace(/(..)/g,"%$1")}function jt(t){var e="malformed IPv6 address";if(!t.match(/^[0-9A-Fa-f:]+$/))throw e;var r=(t=t.toLowerCase()).split(":").length-1;if(r<2)throw e;var n=":".repeat(7-r+2),i=(t=t.replace("::",n)).split(":");if(8!=i.length)throw e;for(var s=0;s<8;s++)i[s]=("0000"+i[s]).slice(-4);return i.join("")}function Lt(t){if(!t.match(/^[0-9A-Fa-f]{32}$/))throw"malformed IPv6 address octet";for(var e=(t=t.toLowerCase()).match(/.{1,4}/g),r=0;r<8;r++)e[r]=e[r].replace(/^0+/,""),""==e[r]&&(e[r]="0");var n=(t=":"+e.join(":")+":").match(/:(0:){2,}/g);if(null===n)return t.slice(1,-1);var i="";for(r=0;r<n.length;r++)n[r].length>i.length&&(i=n[r]);return(t=t.replace(i,"::")).slice(1,-1)}function Vt(t){var e="malformed hex value";if(!t.match(/^([0-9A-Fa-f][0-9A-Fa-f]){1,}$/))throw e;if(8!=t.length)return 32==t.length?Lt(t):t;try{return parseInt(t.substr(0,2),16)+"."+parseInt(t.substr(2,2),16)+"."+parseInt(t.substr(4,2),16)+"."+parseInt(t.substr(6,2),16)}catch(t){throw e}}function _t(t){for(var e=encodeURIComponent(t),r="",n=0;n<e.length;n++)"%"==e[n]?(r+=e.substr(n,3),n+=2):r=r+"%"+Ft(e[n]);return r}function Mt(t){return t.length%2==1?"0"+t:t.substr(0,1)>"7"?"00"+t:t}function Ut(t){t=(t=(t=t.replace(/^\s*\[\s*/,"")).replace(/\s*\]\s*$/,"")).replace(/\s*/g,"");try{return t.split(/,/).map(function(t,e,r){var n=parseInt(t);if(n<0||255<n)throw"integer not in range 0-255";return("00"+n.toString(16)).slice(-2)}).join("")}catch(t){throw"malformed integer array string: "+t}}pt.getLblen=function(t,e){if("8"!=t.substr(e+2,1))return 1;var r=parseInt(t.substr(e+3,1));return 0==r?-1:0<r&&r<10?r+1:-2},pt.getL=function(t,e){var r=pt.getLblen(t,e);return r<1?"":t.substr(e+2,2*r)},pt.getVblen=function(t,e){var r;return""==(r=pt.getL(t,e))?-1:("8"===r.substr(0,1)?new A(r.substr(2),16):new A(r,16)).intValue()},pt.getVidx=function(t,e){var r=pt.getLblen(t,e);return r<0?r:e+2*(r+1)},pt.getV=function(t,e){var r=pt.getVidx(t,e),n=pt.getVblen(t,e);return t.substr(r,2*n)},pt.getTLV=function(t,e){return t.substr(e,2)+pt.getL(t,e)+pt.getV(t,e)},pt.getNextSiblingIdx=function(t,e){return pt.getVidx(t,e)+2*pt.getVblen(t,e)},pt.getChildIdx=function(t,e){var r=pt,n=new Array,i=r.getVidx(t,e);"03"==t.substr(e,2)?n.push(i+2):n.push(i);for(var s=r.getVblen(t,e),a=i,o=0;;){var u=r.getNextSiblingIdx(t,a);if(null==u||u-i>=2*s)break;if(o>=200)break;n.push(u),a=u,o++}return n},pt.getNthChildIdx=function(t,e,r){return pt.getChildIdx(t,e)[r]},pt.getIdxbyList=function(t,e,r,n){var i,s,a=pt;if(0==r.length){if(void 0!==n&&t.substr(e,2)!==n)throw"checking tag doesn't match: "+t.substr(e,2)+"!="+n;return e}return i=r.shift(),s=a.getChildIdx(t,e),a.getIdxbyList(t,s[i],r,n)},pt.getTLVbyList=function(t,e,r,n){var i=pt,s=i.getIdxbyList(t,e,r);if(void 0===s)throw"can't find nthList object";if(void 0!==n&&t.substr(s,2)!=n)throw"checking tag doesn't match: "+t.substr(s,2)+"!="+n;return i.getTLV(t,s)},pt.getVbyList=function(t,e,r,n,i){var s,a,o=pt;if(void 0===(s=o.getIdxbyList(t,e,r,n)))throw"can't find nthList object";return a=o.getV(t,s),!0===i&&(a=a.substr(2)),a},pt.hextooidstr=function(t){var e=function(t,e){return t.length>=e?t:new Array(e-t.length+1).join("0")+t},r=[],n=t.substr(0,2),i=parseInt(n,16);r[0]=new String(Math.floor(i/40)),r[1]=new String(i%40);for(var s=t.substr(2),a=[],o=0;o<s.length/2;o++)a.push(parseInt(s.substr(2*o,2),16));var u=[],h="";for(o=0;o<a.length;o++)128&a[o]?h+=e((127&a[o]).toString(2),7):(h+=e((127&a[o]).toString(2),7),u.push(new String(parseInt(h,2))),h="");var c=r.join(".");return u.length>0&&(c=c+"."+u.join(".")),c},pt.dump=function(t,e,r,n){var i=pt,s=i.getV,a=i.dump,o=i.getChildIdx,u=t;t instanceof ft.asn1.ASN1Object&&(u=t.getEncodedHex());var h=function(t,e){return t.length<=2*e?t:t.substr(0,e)+"..(total "+t.length/2+"bytes).."+t.substr(t.length-e,e)};void 0===e&&(e={ommit_long_octet:32}),void 0===r&&(r=0),void 0===n&&(n="");var c=e.ommit_long_octet;if("01"==u.substr(r,2))return"00"==(l=s(u,r))?n+"BOOLEAN FALSE\n":n+"BOOLEAN TRUE\n";if("02"==u.substr(r,2))return n+"INTEGER "+h(l=s(u,r),c)+"\n";if("03"==u.substr(r,2))return n+"BITSTRING "+h(l=s(u,r),c)+"\n";if("04"==u.substr(r,2)){var l=s(u,r);if(i.isASN1HEX(l)){var f=n+"OCTETSTRING, encapsulates\n";return f+=a(l,e,0,n+"  ")}return n+"OCTETSTRING "+h(l,c)+"\n"}if("05"==u.substr(r,2))return n+"NULL\n";if("06"==u.substr(r,2)){var d=s(u,r),g=ft.asn1.ASN1Util.oidHexToInt(d),p=ft.asn1.x509.OID.oid2name(g),y=g.replace(/\./g," ");return""!=p?n+"ObjectIdentifier "+p+" ("+y+")\n":n+"ObjectIdentifier ("+y+")\n"}if("0c"==u.substr(r,2))return n+"UTF8String '"+Et(s(u,r))+"'\n";if("13"==u.substr(r,2))return n+"PrintableString '"+Et(s(u,r))+"'\n";if("14"==u.substr(r,2))return n+"TeletexString '"+Et(s(u,r))+"'\n";if("16"==u.substr(r,2))return n+"IA5String '"+Et(s(u,r))+"'\n";if("17"==u.substr(r,2))return n+"UTCTime "+Et(s(u,r))+"\n";if("18"==u.substr(r,2))return n+"GeneralizedTime "+Et(s(u,r))+"\n";if("30"==u.substr(r,2)){if("3000"==u.substr(r,4))return n+"SEQUENCE {}\n";f=n+"SEQUENCE\n";var v=e;if((2==(S=o(u,r)).length||3==S.length)&&"06"==u.substr(S[0],2)&&"04"==u.substr(S[S.length-1],2)){p=i.oidname(s(u,S[0]));var m=JSON.parse(JSON.stringify(e));m.x509ExtName=p,v=m}for(var F=0;F<S.length;F++)f+=a(u,v,S[F],n+"  ");return f}if("31"==u.substr(r,2)){f=n+"SET\n";var S=o(u,r);for(F=0;F<S.length;F++)f+=a(u,e,S[F],n+"  ");return f}var b=parseInt(u.substr(r,2),16);if(0!=(128&b)){var x=31&b;if(0!=(32&b)){var f=n+"["+x+"]\n";for(S=o(u,r),F=0;F<S.length;F++)f+=a(u,e,S[F],n+"  ");return f}return"68747470"==(l=s(u,r)).substr(0,8)&&(l=Et(l)),"subjectAltName"===e.x509ExtName&&2==x&&(l=Et(l)),f=n+"["+x+"] "+l+"\n"}return n+"UNKNOWN("+u.substr(r,2)+") "+s(u,r)+"\n"},pt.isASN1HEX=function(t){var e=pt;if(t.length%2==1)return!1;var r=e.getVblen(t,0),n=t.substr(0,2),i=e.getL(t,0);return t.length-n.length-i.length==2*r},pt.oidname=function(t){var e=ft.asn1;ft.lang.String.isHex(t)&&(t=e.ASN1Util.oidHexToInt(t));var r=e.x509.OID.oid2name(t);return""===r&&(r=t),r},void 0!==ft&&ft||(ft={}),void 0!==ft.asn1&&ft.asn1||(ft.asn1={}),void 0!==ft.asn1.x509&&ft.asn1.x509||(ft.asn1.x509={}),ft.asn1.x509.Certificate=function(t){ft.asn1.x509.Certificate.superclass.constructor.call(this);var e=ft,r=(e.crypto,e.asn1),n=r.DERSequence,i=r.DERBitString;this.sign=function(){this.asn1SignatureAlg=this.asn1TBSCert.asn1SignatureAlg;var t=new ft.crypto.Signature({alg:this.asn1SignatureAlg.nameAlg});t.init(this.prvKey),t.updateHex(this.asn1TBSCert.getEncodedHex()),this.hexSig=t.sign(),this.asn1Sig=new i({hex:"00"+this.hexSig});var e=new n({array:[this.asn1TBSCert,this.asn1SignatureAlg,this.asn1Sig]});this.hTLV=e.getEncodedHex(),this.isModified=!1},this.setSignatureHex=function(t){this.asn1SignatureAlg=this.asn1TBSCert.asn1SignatureAlg,this.hexSig=t,this.asn1Sig=new i({hex:"00"+this.hexSig});var e=new n({array:[this.asn1TBSCert,this.asn1SignatureAlg,this.asn1Sig]});this.hTLV=e.getEncodedHex(),this.isModified=!1},this.getEncodedHex=function(){if(0==this.isModified&&null!=this.hTLV)return this.hTLV;throw"not signed yet"},this.getPEMString=function(){return"-----BEGIN CERTIFICATE-----\r\n"+Tt(this.getEncodedHex())+"\r\n-----END CERTIFICATE-----\r\n"},void 0!==t&&(void 0!==t.tbscertobj&&(this.asn1TBSCert=t.tbscertobj),void 0!==t.prvkeyobj&&(this.prvKey=t.prvkeyobj))},i.lang.extend(ft.asn1.x509.Certificate,ft.asn1.ASN1Object),ft.asn1.x509.TBSCertificate=function(t){ft.asn1.x509.TBSCertificate.superclass.constructor.call(this);var e=ft.asn1,r=e.DERSequence,n=e.DERInteger,i=e.DERTaggedObject,s=e.x509,a=s.Time,o=s.X500Name,u=s.SubjectPublicKeyInfo;this._initialize=function(){this.asn1Array=new Array,this.asn1Version=new i({obj:new n({int:2})}),this.asn1SerialNumber=null,this.asn1SignatureAlg=null,this.asn1Issuer=null,this.asn1NotBefore=null,this.asn1NotAfter=null,this.asn1Subject=null,this.asn1SubjPKey=null,this.extensionsArray=new Array},this.setSerialNumberByParam=function(t){this.asn1SerialNumber=new n(t)},this.setSignatureAlgByParam=function(t){this.asn1SignatureAlg=new s.AlgorithmIdentifier(t)},this.setIssuerByParam=function(t){this.asn1Issuer=new o(t)},this.setNotBeforeByParam=function(t){this.asn1NotBefore=new a(t)},this.setNotAfterByParam=function(t){this.asn1NotAfter=new a(t)},this.setSubjectByParam=function(t){this.asn1Subject=new o(t)},this.setSubjectPublicKey=function(t){this.asn1SubjPKey=new u(t)},this.setSubjectPublicKeyByGetKey=function(t){var e=Kt.getKey(t);this.asn1SubjPKey=new u(e)},this.appendExtension=function(t){this.extensionsArray.push(t)},this.appendExtensionByName=function(t,e){ft.asn1.x509.Extension.appendByNameToArray(t,e,this.extensionsArray)},this.getEncodedHex=function(){if(null==this.asn1NotBefore||null==this.asn1NotAfter)throw"notBefore and/or notAfter not set";var t=new r({array:[this.asn1NotBefore,this.asn1NotAfter]});if(this.asn1Array=new Array,this.asn1Array.push(this.asn1Version),this.asn1Array.push(this.asn1SerialNumber),this.asn1Array.push(this.asn1SignatureAlg),this.asn1Array.push(this.asn1Issuer),this.asn1Array.push(t),this.asn1Array.push(this.asn1Subject),this.asn1Array.push(this.asn1SubjPKey),this.extensionsArray.length>0){var e=new r({array:this.extensionsArray}),n=new i({explicit:!0,tag:"a3",obj:e});this.asn1Array.push(n)}var s=new r({array:this.asn1Array});return this.hTLV=s.getEncodedHex(),this.isModified=!1,this.hTLV},this._initialize()},i.lang.extend(ft.asn1.x509.TBSCertificate,ft.asn1.ASN1Object),ft.asn1.x509.Extension=function(t){ft.asn1.x509.Extension.superclass.constructor.call(this);var e=ft.asn1,r=e.DERObjectIdentifier,n=e.DEROctetString,i=(e.DERBitString,e.DERBoolean),s=e.DERSequence;this.getEncodedHex=function(){var t=new r({oid:this.oid}),e=new n({hex:this.getExtnValueHex()}),a=new Array;return a.push(t),this.critical&&a.push(new i),a.push(e),new s({array:a}).getEncodedHex()},this.critical=!1,void 0!==t&&void 0!==t.critical&&(this.critical=t.critical)},i.lang.extend(ft.asn1.x509.Extension,ft.asn1.ASN1Object),ft.asn1.x509.Extension.appendByNameToArray=function(t,e,r){var n=t.toLowerCase(),i=ft.asn1.x509;if("basicconstraints"==n){var s=new i.BasicConstraints(e);r.push(s)}else if("keyusage"==n){s=new i.KeyUsage(e);r.push(s)}else if("crldistributionpoints"==n){s=new i.CRLDistributionPoints(e);r.push(s)}else if("extkeyusage"==n){s=new i.ExtKeyUsage(e);r.push(s)}else if("authoritykeyidentifier"==n){s=new i.AuthorityKeyIdentifier(e);r.push(s)}else if("authorityinfoaccess"==n){s=new i.AuthorityInfoAccess(e);r.push(s)}else if("subjectaltname"==n){s=new i.SubjectAltName(e);r.push(s)}else{if("issueraltname"!=n)throw"unsupported extension name: "+t;s=new i.IssuerAltName(e);r.push(s)}},ft.asn1.x509.KeyUsage=function(t){ft.asn1.x509.KeyUsage.superclass.constructor.call(this,t);var e=$t.KEYUSAGE_NAME;if(this.getExtnValueHex=function(){return this.asn1ExtnValue.getEncodedHex()},this.oid="*********",void 0!==t&&(void 0!==t.bin&&(this.asn1ExtnValue=new ft.asn1.DERBitString(t)),void 0!==t.names&&void 0!==t.names.length)){for(var r=t.names,n="000000000",i=0;i<r.length;i++)for(var s=0;s<e.length;s++)r[i]===e[s]&&(n=n.substring(0,s)+"1"+n.substring(s+1,n.length));this.asn1ExtnValue=new ft.asn1.DERBitString({bin:n})}},i.lang.extend(ft.asn1.x509.KeyUsage,ft.asn1.x509.Extension),ft.asn1.x509.BasicConstraints=function(t){ft.asn1.x509.BasicConstraints.superclass.constructor.call(this,t);this.getExtnValueHex=function(){var t=new Array;this.cA&&t.push(new ft.asn1.DERBoolean),this.pathLen>-1&&t.push(new ft.asn1.DERInteger({int:this.pathLen}));var e=new ft.asn1.DERSequence({array:t});return this.asn1ExtnValue=e,this.asn1ExtnValue.getEncodedHex()},this.oid="*********",this.cA=!1,this.pathLen=-1,void 0!==t&&(void 0!==t.cA&&(this.cA=t.cA),void 0!==t.pathLen&&(this.pathLen=t.pathLen))},i.lang.extend(ft.asn1.x509.BasicConstraints,ft.asn1.x509.Extension),ft.asn1.x509.CRLDistributionPoints=function(t){ft.asn1.x509.CRLDistributionPoints.superclass.constructor.call(this,t);var e=ft.asn1,r=e.x509;this.getExtnValueHex=function(){return this.asn1ExtnValue.getEncodedHex()},this.setByDPArray=function(t){this.asn1ExtnValue=new e.DERSequence({array:t})},this.setByOneURI=function(t){var e=new r.GeneralNames([{uri:t}]),n=new r.DistributionPointName(e),i=new r.DistributionPoint({dpobj:n});this.setByDPArray([i])},this.oid="*********",void 0!==t&&(void 0!==t.array?this.setByDPArray(t.array):void 0!==t.uri&&this.setByOneURI(t.uri))},i.lang.extend(ft.asn1.x509.CRLDistributionPoints,ft.asn1.x509.Extension),ft.asn1.x509.ExtKeyUsage=function(t){ft.asn1.x509.ExtKeyUsage.superclass.constructor.call(this,t);var e=ft.asn1;this.setPurposeArray=function(t){this.asn1ExtnValue=new e.DERSequence;for(var r=0;r<t.length;r++){var n=new e.DERObjectIdentifier(t[r]);this.asn1ExtnValue.appendASN1Object(n)}},this.getExtnValueHex=function(){return this.asn1ExtnValue.getEncodedHex()},this.oid="*********",void 0!==t&&void 0!==t.array&&this.setPurposeArray(t.array)},i.lang.extend(ft.asn1.x509.ExtKeyUsage,ft.asn1.x509.Extension),ft.asn1.x509.AuthorityKeyIdentifier=function(t){ft.asn1.x509.AuthorityKeyIdentifier.superclass.constructor.call(this,t);var e=ft.asn1,r=e.DERTaggedObject;this.asn1KID=null,this.asn1CertIssuer=null,this.asn1CertSN=null,this.getExtnValueHex=function(){var t=new Array;this.asn1KID&&t.push(new r({explicit:!1,tag:"80",obj:this.asn1KID})),this.asn1CertIssuer&&t.push(new r({explicit:!1,tag:"a1",obj:this.asn1CertIssuer})),this.asn1CertSN&&t.push(new r({explicit:!1,tag:"82",obj:this.asn1CertSN}));var n=new e.DERSequence({array:t});return this.asn1ExtnValue=n,this.asn1ExtnValue.getEncodedHex()},this.setKIDByParam=function(t){this.asn1KID=new ft.asn1.DEROctetString(t)},this.setCertIssuerByParam=function(t){this.asn1CertIssuer=new ft.asn1.x509.X500Name(t)},this.setCertSNByParam=function(t){this.asn1CertSN=new ft.asn1.DERInteger(t)},this.oid="*********",void 0!==t&&(void 0!==t.kid&&this.setKIDByParam(t.kid),void 0!==t.issuer&&this.setCertIssuerByParam(t.issuer),void 0!==t.sn&&this.setCertSNByParam(t.sn))},i.lang.extend(ft.asn1.x509.AuthorityKeyIdentifier,ft.asn1.x509.Extension),ft.asn1.x509.AuthorityInfoAccess=function(t){ft.asn1.x509.AuthorityInfoAccess.superclass.constructor.call(this,t),this.setAccessDescriptionArray=function(t){for(var e=new Array,r=ft.asn1,n=r.DERSequence,i=0;i<t.length;i++){var s=new n({array:[new r.DERObjectIdentifier(t[i].accessMethod),new r.x509.GeneralName(t[i].accessLocation)]});e.push(s)}this.asn1ExtnValue=new n({array:e})},this.getExtnValueHex=function(){return this.asn1ExtnValue.getEncodedHex()},this.oid="*******.*******.1",void 0!==t&&void 0!==t.array&&this.setAccessDescriptionArray(t.array)},i.lang.extend(ft.asn1.x509.AuthorityInfoAccess,ft.asn1.x509.Extension),ft.asn1.x509.SubjectAltName=function(t){ft.asn1.x509.SubjectAltName.superclass.constructor.call(this,t),this.setNameArray=function(t){this.asn1ExtnValue=new ft.asn1.x509.GeneralNames(t)},this.getExtnValueHex=function(){return this.asn1ExtnValue.getEncodedHex()},this.oid="*********",void 0!==t&&void 0!==t.array&&this.setNameArray(t.array)},i.lang.extend(ft.asn1.x509.SubjectAltName,ft.asn1.x509.Extension),ft.asn1.x509.IssuerAltName=function(t){ft.asn1.x509.IssuerAltName.superclass.constructor.call(this,t),this.setNameArray=function(t){this.asn1ExtnValue=new ft.asn1.x509.GeneralNames(t)},this.getExtnValueHex=function(){return this.asn1ExtnValue.getEncodedHex()},this.oid="*********",void 0!==t&&void 0!==t.array&&this.setNameArray(t.array)},i.lang.extend(ft.asn1.x509.IssuerAltName,ft.asn1.x509.Extension),ft.asn1.x509.CRL=function(t){ft.asn1.x509.CRL.superclass.constructor.call(this);this.sign=function(){this.asn1SignatureAlg=this.asn1TBSCertList.asn1SignatureAlg,sig=new ft.crypto.Signature({alg:"SHA1withRSA",prov:"cryptojs/jsrsa"}),sig.init(this.prvKey),sig.updateHex(this.asn1TBSCertList.getEncodedHex()),this.hexSig=sig.sign(),this.asn1Sig=new ft.asn1.DERBitString({hex:"00"+this.hexSig});var t=new ft.asn1.DERSequence({array:[this.asn1TBSCertList,this.asn1SignatureAlg,this.asn1Sig]});this.hTLV=t.getEncodedHex(),this.isModified=!1},this.getEncodedHex=function(){if(0==this.isModified&&null!=this.hTLV)return this.hTLV;throw"not signed yet"},this.getPEMString=function(){return"-----BEGIN X509 CRL-----\r\n"+Tt(this.getEncodedHex())+"\r\n-----END X509 CRL-----\r\n"},void 0!==t&&(void 0!==t.tbsobj&&(this.asn1TBSCertList=t.tbsobj),void 0!==t.prvkeyobj&&(this.prvKey=t.prvkeyobj))},i.lang.extend(ft.asn1.x509.CRL,ft.asn1.ASN1Object),ft.asn1.x509.TBSCertList=function(t){ft.asn1.x509.TBSCertList.superclass.constructor.call(this);var e=ft.asn1,r=e.DERSequence,n=e.x509,i=n.Time;this.setSignatureAlgByParam=function(t){this.asn1SignatureAlg=new n.AlgorithmIdentifier(t)},this.setIssuerByParam=function(t){this.asn1Issuer=new n.X500Name(t)},this.setThisUpdateByParam=function(t){this.asn1ThisUpdate=new i(t)},this.setNextUpdateByParam=function(t){this.asn1NextUpdate=new i(t)},this.addRevokedCert=function(t,e){var r={};null!=t&&null!=t&&(r.sn=t),null!=e&&null!=e&&(r.time=e);var i=new n.CRLEntry(r);this.aRevokedCert.push(i)},this.getEncodedHex=function(){if(this.asn1Array=new Array,null!=this.asn1Version&&this.asn1Array.push(this.asn1Version),this.asn1Array.push(this.asn1SignatureAlg),this.asn1Array.push(this.asn1Issuer),this.asn1Array.push(this.asn1ThisUpdate),null!=this.asn1NextUpdate&&this.asn1Array.push(this.asn1NextUpdate),this.aRevokedCert.length>0){var t=new r({array:this.aRevokedCert});this.asn1Array.push(t)}var e=new r({array:this.asn1Array});return this.hTLV=e.getEncodedHex(),this.isModified=!1,this.hTLV},this._initialize=function(){this.asn1Version=null,this.asn1SignatureAlg=null,this.asn1Issuer=null,this.asn1ThisUpdate=null,this.asn1NextUpdate=null,this.aRevokedCert=new Array},this._initialize()},i.lang.extend(ft.asn1.x509.TBSCertList,ft.asn1.ASN1Object),ft.asn1.x509.CRLEntry=function(t){ft.asn1.x509.CRLEntry.superclass.constructor.call(this);var e=ft.asn1;this.setCertSerial=function(t){this.sn=new e.DERInteger(t)},this.setRevocationDate=function(t){this.time=new e.x509.Time(t)},this.getEncodedHex=function(){var t=new e.DERSequence({array:[this.sn,this.time]});return this.TLV=t.getEncodedHex(),this.TLV},void 0!==t&&(void 0!==t.time&&this.setRevocationDate(t.time),void 0!==t.sn&&this.setCertSerial(t.sn))},i.lang.extend(ft.asn1.x509.CRLEntry,ft.asn1.ASN1Object),ft.asn1.x509.X500Name=function(t){ft.asn1.x509.X500Name.superclass.constructor.call(this),this.asn1Array=new Array;var e=ft.asn1,r=e.x509,n=Rt;if(this.setByString=function(t){var e=t.split("/");e.shift();for(var n=[],i=0;i<e.length;i++)if(e[i].match(/^[^=]+=.+$/))n.push(e[i]);else{var s=n.length-1;n[s]=n[s]+"/"+e[i]}for(i=0;i<n.length;i++)this.asn1Array.push(new r.RDN({str:n[i]}))},this.setByLdapString=function(t){var e=r.X500Name.ldapToOneline(t);this.setByString(e)},this.setByObject=function(t){for(var e in t)if(t.hasOwnProperty(e)){var r=new ft.asn1.x509.RDN({str:e+"="+t[e]});this.asn1Array?this.asn1Array.push(r):this.asn1Array=[r]}},this.getEncodedHex=function(){if("string"==typeof this.hTLV)return this.hTLV;var t=new e.DERSequence({array:this.asn1Array});return this.hTLV=t.getEncodedHex(),this.hTLV},void 0!==t){var i;if(void 0!==t.str?this.setByString(t.str):void 0!==t.ldapstr?this.setByLdapString(t.ldapstr):"object"==typeof t&&this.setByObject(t),void 0!==t.certissuer)(i=new $t).hex=n(t.certissuer),this.hTLV=i.getIssuerHex();if(void 0!==t.certsubject)(i=new $t).hex=n(t.certsubject),this.hTLV=i.getSubjectHex()}},i.lang.extend(ft.asn1.x509.X500Name,ft.asn1.ASN1Object),ft.asn1.x509.X500Name.onelineToLDAP=function(t){if("/"!==t.substr(0,1))throw"malformed input";var e=(t=t.substr(1)).split("/");return e.reverse(),(e=e.map(function(t){return t.replace(/,/,"\\,")})).join(",")},ft.asn1.x509.X500Name.ldapToOneline=function(t){for(var e=t.split(","),r=!1,n=[],i=0;e.length>0;i++){var s=e.shift();if(!0===r){var a=(n.pop()+","+s).replace(/\\,/g,",");n.push(a),r=!1}else n.push(s);"\\"===s.substr(-1,1)&&(r=!0)}return(n=n.map(function(t){return t.replace("/","\\/")})).reverse(),"/"+n.join("/")},ft.asn1.x509.RDN=function(t){ft.asn1.x509.RDN.superclass.constructor.call(this),this.asn1Array=new Array,this.addByString=function(t){this.asn1Array.push(new ft.asn1.x509.AttributeTypeAndValue({str:t}))},this.addByMultiValuedString=function(t){for(var e=ft.asn1.x509.RDN.parseString(t),r=0;r<e.length;r++)this.addByString(e[r])},this.getEncodedHex=function(){var t=new ft.asn1.DERSet({array:this.asn1Array});return this.TLV=t.getEncodedHex(),this.TLV},void 0!==t&&void 0!==t.str&&this.addByMultiValuedString(t.str)},i.lang.extend(ft.asn1.x509.RDN,ft.asn1.ASN1Object),ft.asn1.x509.RDN.parseString=function(t){for(var e=t.split(/\+/),r=!1,n=[],i=0;e.length>0;i++){var s=e.shift();if(!0===r){var a=(n.pop()+"+"+s).replace(/\\\+/g,"+");n.push(a),r=!1}else n.push(s);"\\"===s.substr(-1,1)&&(r=!0)}var o=!1,u=[];for(i=0;n.length>0;i++){s=n.shift();if(!0===o){var h=u.pop();if(s.match(/"$/)){a=(h+"+"+s).replace(/^([^=]+)="(.*)"$/,"$1=$2");u.push(a),o=!1}else u.push(h+"+"+s)}else u.push(s);s.match(/^[^=]+="/)&&(o=!0)}return u},ft.asn1.x509.AttributeTypeAndValue=function(t){ft.asn1.x509.AttributeTypeAndValue.superclass.constructor.call(this);var e=ft.asn1;this.setByString=function(t){var e=t.match(/^([^=]+)=(.+)$/);if(!e)throw"malformed attrTypeAndValueStr: "+t;this.setByAttrTypeAndValueStr(e[1],e[2])},this.setByAttrTypeAndValueStr=function(t,e){this.typeObj=ft.asn1.x509.OID.atype2obj(t);var r="utf8";"C"==t&&(r="prn"),this.valueObj=this.getValueObj(r,e)},this.getValueObj=function(t,r){if("utf8"==t)return new e.DERUTF8String({str:r});if("prn"==t)return new e.DERPrintableString({str:r});if("tel"==t)return new e.DERTeletexString({str:r});if("ia5"==t)return new e.DERIA5String({str:r});throw"unsupported directory string type: type="+t+" value="+r},this.getEncodedHex=function(){var t=new e.DERSequence({array:[this.typeObj,this.valueObj]});return this.TLV=t.getEncodedHex(),this.TLV},void 0!==t&&void 0!==t.str&&this.setByString(t.str)},i.lang.extend(ft.asn1.x509.AttributeTypeAndValue,ft.asn1.ASN1Object),ft.asn1.x509.SubjectPublicKeyInfo=function(t){ft.asn1.x509.SubjectPublicKeyInfo.superclass.constructor.call(this);var e=ft,r=e.asn1,n=r.DERInteger,i=r.DERBitString,s=r.DERObjectIdentifier,a=r.DERSequence,o=r.ASN1Util.newObject,u=r.x509.AlgorithmIdentifier,h=e.crypto;h.ECDSA,h.DSA;this.getASN1Object=function(){if(null==this.asn1AlgId||null==this.asn1SubjPKey)throw"algId and/or subjPubKey not set";return new a({array:[this.asn1AlgId,this.asn1SubjPKey]})},this.getEncodedHex=function(){var t=this.getASN1Object();return this.hTLV=t.getEncodedHex(),this.hTLV},this.setPubKey=function(t){try{if(t instanceof at){var e=o({seq:[{int:{bigint:t.n}},{int:{int:t.e}}]}).getEncodedHex();this.asn1AlgId=new u({name:"rsaEncryption"}),this.asn1SubjPKey=new i({hex:"00"+e})}}catch(t){}try{if(t instanceof ft.crypto.ECDSA){var r=new s({name:t.curveName});this.asn1AlgId=new u({name:"ecPublicKey",asn1params:r}),this.asn1SubjPKey=new i({hex:"00"+t.pubKeyHex})}}catch(t){}try{if(t instanceof ft.crypto.DSA){r=new o({seq:[{int:{bigint:t.p}},{int:{bigint:t.q}},{int:{bigint:t.g}}]});this.asn1AlgId=new u({name:"dsa",asn1params:r});var a=new n({bigint:t.y});this.asn1SubjPKey=new i({hex:"00"+a.getEncodedHex()})}}catch(t){}},void 0!==t&&this.setPubKey(t)},i.lang.extend(ft.asn1.x509.SubjectPublicKeyInfo,ft.asn1.ASN1Object),ft.asn1.x509.Time=function(t){ft.asn1.x509.Time.superclass.constructor.call(this);var e=ft.asn1,r=e.DERUTCTime,n=e.DERGeneralizedTime;this.setTimeParams=function(t){this.timeParams=t},this.getEncodedHex=function(){var t=null;return t=null!=this.timeParams?"utc"==this.type?new r(this.timeParams):new n(this.timeParams):"utc"==this.type?new r:new n,this.TLV=t.getEncodedHex(),this.TLV},this.type="utc",void 0!==t&&(void 0!==t.type?this.type=t.type:void 0!==t.str&&(t.str.match(/^[0-9]{12}Z$/)&&(this.type="utc"),t.str.match(/^[0-9]{14}Z$/)&&(this.type="gen")),this.timeParams=t)},i.lang.extend(ft.asn1.x509.Time,ft.asn1.ASN1Object),ft.asn1.x509.AlgorithmIdentifier=function(t){ft.asn1.x509.AlgorithmIdentifier.superclass.constructor.call(this),this.nameAlg=null,this.asn1Alg=null,this.asn1Params=null,this.paramEmpty=!1;var e=ft.asn1;if(this.getEncodedHex=function(){if(null===this.nameAlg&&null===this.asn1Alg)throw"algorithm not specified";null!==this.nameAlg&&null===this.asn1Alg&&(this.asn1Alg=e.x509.OID.name2obj(this.nameAlg));var t=[this.asn1Alg];null!==this.asn1Params&&t.push(this.asn1Params);var r=new e.DERSequence({array:t});return this.hTLV=r.getEncodedHex(),this.hTLV},void 0!==t&&(void 0!==t.name&&(this.nameAlg=t.name),void 0!==t.asn1params&&(this.asn1Params=t.asn1params),void 0!==t.paramempty&&(this.paramEmpty=t.paramempty)),null===this.asn1Params&&!1===this.paramEmpty&&null!==this.nameAlg){var r=this.nameAlg.toLowerCase();"withdsa"!==r.substr(-7,7)&&"withecdsa"!==r.substr(-9,9)&&(this.asn1Params=new e.DERNull)}},i.lang.extend(ft.asn1.x509.AlgorithmIdentifier,ft.asn1.ASN1Object),ft.asn1.x509.GeneralName=function(t){ft.asn1.x509.GeneralName.superclass.constructor.call(this);var e={rfc822:"81",dns:"82",dn:"a4",uri:"86",ip:"87"},r=ft.asn1,n=(r.DERSequence,r.DEROctetString),i=r.DERIA5String,s=r.DERTaggedObject,a=r.ASN1Object,o=r.x509.X500Name,u=Rt;this.explicit=!1,this.setByParam=function(t){var r=null;if(void 0!==t){if(void 0!==t.rfc822&&(this.type="rfc822",r=new i({str:t[this.type]})),void 0!==t.dns&&(this.type="dns",r=new i({str:t[this.type]})),void 0!==t.uri&&(this.type="uri",r=new i({str:t[this.type]})),void 0!==t.dn&&(this.type="dn",this.explicit=!0,r=new o({str:t.dn})),void 0!==t.ldapdn&&(this.type="dn",this.explicit=!0,r=new o({ldapstr:t.ldapdn})),void 0!==t.certissuer){this.type="dn",this.explicit=!0;var h=null;if((l=t.certissuer).match(/^[0-9A-Fa-f]+$/),-1!=l.indexOf("-----BEGIN ")&&(h=u(l)),null==h)throw"certissuer param not cert";(f=new $t).hex=h;var c=f.getIssuerHex();(r=new a).hTLV=c}if(void 0!==t.certsubj){this.type="dn",this.explicit=!0;var l,f;h=null;if((l=t.certsubj).match(/^[0-9A-Fa-f]+$/),-1!=l.indexOf("-----BEGIN ")&&(h=u(l)),null==h)throw"certsubj param not cert";(f=new $t).hex=h;c=f.getSubjectHex();(r=new a).hTLV=c}if(void 0!==t.ip){this.type="ip",this.explicit=!1;var d,g=t.ip,p="malformed IP address";if(g.match(/^[0-9.]+[.][0-9.]+$/)){if(8!==(d=Ut("["+g.split(".").join(",")+"]")).length)throw p}else if(g.match(/^[0-9A-Fa-f:]+:[0-9A-Fa-f:]+$/))d=jt(g);else{if(!g.match(/^([0-9A-Fa-f][0-9A-Fa-f]){1,}$/))throw p;d=g}r=new n({hex:d})}if(null==this.type)throw"unsupported type in params="+t;this.asn1Obj=new s({explicit:this.explicit,tag:e[this.type],obj:r})}},this.getEncodedHex=function(){return this.asn1Obj.getEncodedHex()},void 0!==t&&this.setByParam(t)},i.lang.extend(ft.asn1.x509.GeneralName,ft.asn1.ASN1Object),ft.asn1.x509.GeneralNames=function(t){ft.asn1.x509.GeneralNames.superclass.constructor.call(this);var e=ft.asn1;this.setByParamArray=function(t){for(var r=0;r<t.length;r++){var n=new e.x509.GeneralName(t[r]);this.asn1Array.push(n)}},this.getEncodedHex=function(){return new e.DERSequence({array:this.asn1Array}).getEncodedHex()},this.asn1Array=new Array,void 0!==t&&this.setByParamArray(t)},i.lang.extend(ft.asn1.x509.GeneralNames,ft.asn1.ASN1Object),ft.asn1.x509.DistributionPointName=function(t){ft.asn1.x509.DistributionPointName.superclass.constructor.call(this);var e=ft.asn1,r=e.DERTaggedObject;if(this.getEncodedHex=function(){if("full"!=this.type)throw"currently type shall be 'full': "+this.type;return this.asn1Obj=new r({explicit:!1,tag:this.tag,obj:this.asn1V}),this.hTLV=this.asn1Obj.getEncodedHex(),this.hTLV},void 0!==t){if(!e.x509.GeneralNames.prototype.isPrototypeOf(t))throw"This class supports GeneralNames only as argument";this.type="full",this.tag="a0",this.asn1V=t}},i.lang.extend(ft.asn1.x509.DistributionPointName,ft.asn1.ASN1Object),ft.asn1.x509.DistributionPoint=function(t){ft.asn1.x509.DistributionPoint.superclass.constructor.call(this);var e=ft.asn1;this.getEncodedHex=function(){var t=new e.DERSequence;if(null!=this.asn1DP){var r=new e.DERTaggedObject({explicit:!0,tag:"a0",obj:this.asn1DP});t.appendASN1Object(r)}return this.hTLV=t.getEncodedHex(),this.hTLV},void 0!==t&&void 0!==t.dpobj&&(this.asn1DP=t.dpobj)},i.lang.extend(ft.asn1.x509.DistributionPoint,ft.asn1.ASN1Object),ft.asn1.x509.OID=new function(t){this.atype2oidList={CN:"*******",L:"*******",ST:"*******",O:"********",OU:"********",C:"*******",STREET:"*******",DC:"0.9.2342.19200300.100.1.25",UID:"0.9.2342.19200300.100.1.1",SN:"*******",T:"********",DN:"*******9",E:"1.2.840.113549.1.9.1",description:"********",businessCategory:"********",postalCode:"********",serialNumber:"*******",uniqueIdentifier:"*******5",organizationIdentifier:"*******7",jurisdictionOfIncorporationL:"*******.4.1.311.********",jurisdictionOfIncorporationSP:"*******.4.1.311.********",jurisdictionOfIncorporationC:"*******.4.1.311.********"},this.name2oidList={sha1:"********.2.26",sha256:"2.16.840.*********.2.1",sha384:"2.16.840.*********.2.2",sha512:"2.16.840.*********.2.3",sha224:"2.16.840.*********.2.4",md5:"1.2.840.113549.2.5",md2:"********.2.2.1",ripemd160:"********.2.1",MD2withRSA:"1.2.840.113549.1.1.2",MD4withRSA:"1.2.840.113549.1.1.3",MD5withRSA:"1.2.840.113549.1.1.4",SHA1withRSA:"1.2.840.113549.1.1.5",SHA224withRSA:"1.2.840.113549.1.1.14",SHA256withRSA:"1.2.840.113549.1.1.11",SHA384withRSA:"1.2.840.113549.1.1.12",SHA512withRSA:"1.2.840.113549.1.1.13",SHA1withECDSA:"1.2.840.10045.4.1",SHA224withECDSA:"1.2.840.10045.4.3.1",SHA256withECDSA:"1.2.840.10045.4.3.2",SHA384withECDSA:"1.2.840.10045.4.3.3",SHA512withECDSA:"1.2.840.10045.4.3.4",dsa:"1.2.840.10040.4.1",SHA1withDSA:"1.2.840.10040.4.3",SHA224withDSA:"2.16.840.*********.3.1",SHA256withDSA:"2.16.840.*********.3.2",rsaEncryption:"1.2.840.113549.1.1.1",commonName:"*******",countryName:"*******",localityName:"*******",stateOrProvinceName:"*******",streetAddress:"*******",organizationName:"********",organizationalUnitName:"********",domainComponent:"0.9.2342.19200300.100.1.25",userId:"0.9.2342.19200300.100.1.1",surname:"*******",title:"********",distinguishedName:"*******9",emailAddress:"1.2.840.113549.1.9.1",description:"********",businessCategory:"********",postalCode:"********",uniqueIdentifier:"*******5",organizationIdentifier:"*******7",jurisdictionOfIncorporationL:"*******.4.1.311.********",jurisdictionOfIncorporationSP:"*******.4.1.311.********",jurisdictionOfIncorporationC:"*******.4.1.311.********",subjectKeyIdentifier:"*********",keyUsage:"*********",subjectAltName:"*********",issuerAltName:"*********",basicConstraints:"*********",nameConstraints:"*********",cRLDistributionPoints:"*********",certificatePolicies:"*********",authorityKeyIdentifier:"*********",policyConstraints:"*********",extKeyUsage:"*********",authorityInfoAccess:"*******.*******.1",ocsp:"*******.********.1",caIssuers:"*******.********.2",anyExtendedKeyUsage:"*********.0",serverAuth:"*******.*******.1",clientAuth:"*******.*******.2",codeSigning:"*******.*******.3",emailProtection:"*******.*******.4",timeStamping:"*******.*******.8",ocspSigning:"*******.*******.9",ecPublicKey:"1.2.840.10045.2.1",secp256r1:"1.2.840.10045.3.1.7",secp256k1:"1.3.132.0.10",secp384r1:"1.3.132.0.34",pkcs5PBES2:"1.2.840.113549.1.5.13",pkcs5PBKDF2:"1.2.840.113549.1.5.12","des-EDE3-CBC":"1.2.840.113549.3.7",data:"1.2.840.113549.1.7.1","signed-data":"1.2.840.113549.1.7.2","enveloped-data":"1.2.840.113549.1.7.3","digested-data":"1.2.840.113549.1.7.5","encrypted-data":"1.2.840.113549.1.7.6","authenticated-data":"1.2.840.113549.1.9.16.1.2",tstinfo:"1.2.840.113549.1.9.16.1.4",extensionRequest:"1.2.840.113549.1.9.14"},this.objCache={},this.name2obj=function(t){if(void 0!==this.objCache[t])return this.objCache[t];if(void 0===this.name2oidList[t])throw"Name of ObjectIdentifier not defined: "+t;var e=this.name2oidList[t],r=new ft.asn1.DERObjectIdentifier({oid:e});return this.objCache[t]=r,r},this.atype2obj=function(t){if(void 0!==this.objCache[t])return this.objCache[t];if(void 0===this.atype2oidList[t])throw"AttributeType name undefined: "+t;var e=this.atype2oidList[t],r=new ft.asn1.DERObjectIdentifier({oid:e});return this.objCache[t]=r,r}},ft.asn1.x509.OID.oid2name=function(t){var e=ft.asn1.x509.OID.name2oidList;for(var r in e)if(e[r]==t)return r;return""},ft.asn1.x509.OID.oid2atype=function(t){var e=ft.asn1.x509.OID.atype2oidList;for(var r in e)if(e[r]==t)return r;return t},ft.asn1.x509.OID.name2oid=function(t){var e=ft.asn1.x509.OID.name2oidList;return void 0===e[t]?"":e[t]},ft.asn1.x509.X509Util={},ft.asn1.x509.X509Util.newCertPEM=function(t){var e=ft.asn1.x509,r=e.TBSCertificate,n=e.Certificate,i=new r;if(void 0===t.serial)throw"serial number undefined.";if(i.setSerialNumberByParam(t.serial),"string"!=typeof t.sigalg.name)throw"unproper signature algorithm name";if(i.setSignatureAlgByParam(t.sigalg),void 0===t.issuer)throw"issuer name undefined.";if(i.setIssuerByParam(t.issuer),void 0===t.notbefore)throw"notbefore undefined.";if(i.setNotBeforeByParam(t.notbefore),void 0===t.notafter)throw"notafter undefined.";if(i.setNotAfterByParam(t.notafter),void 0===t.subject)throw"subject name undefined.";if(i.setSubjectByParam(t.subject),void 0===t.sbjpubkey)throw"subject public key undefined.";if(i.setSubjectPublicKeyByGetKey(t.sbjpubkey),void 0!==t.ext&&void 0!==t.ext.length)for(var s=0;s<t.ext.length;s++)for(key in t.ext[s])i.appendExtensionByName(key,t.ext[s][key]);if(void 0===t.cakey&&void 0===t.sighex)throw"param cakey and sighex undefined.";var a=null;return t.cakey&&(a=new n({tbscertobj:i,prvkeyobj:!0===t.cakey.isPrivate?t.cakey:Kt.getKey.apply(null,t.cakey)})).sign(),t.sighex&&(a=new n({tbscertobj:i})).setSignatureHex(t.sighex),a.getPEMString()},void 0!==ft&&ft||(ft={}),void 0!==ft.asn1&&ft.asn1||(ft.asn1={}),void 0!==ft.asn1.cms&&ft.asn1.cms||(ft.asn1.cms={}),ft.asn1.cms.Attribute=function(t){var e=ft.asn1;e.cms.Attribute.superclass.constructor.call(this),this.getEncodedHex=function(){var t,r,n;t=new e.DERObjectIdentifier({oid:this.attrTypeOid}),r=new e.DERSet({array:this.valueList});try{r.getEncodedHex()}catch(t){throw"fail valueSet.getEncodedHex in Attribute(1)/"+t}n=new e.DERSequence({array:[t,r]});try{this.hTLV=n.getEncodedHex()}catch(t){throw"failed seq.getEncodedHex in Attribute(2)/"+t}return this.hTLV}},i.lang.extend(ft.asn1.cms.Attribute,ft.asn1.ASN1Object),ft.asn1.cms.ContentType=function(t){var e=ft.asn1;e.cms.ContentType.superclass.constructor.call(this),this.attrTypeOid="1.2.840.113549.1.9.3";var r=null;if(void 0!==t){r=new e.DERObjectIdentifier(t);this.valueList=[r]}},i.lang.extend(ft.asn1.cms.ContentType,ft.asn1.cms.Attribute),ft.asn1.cms.MessageDigest=function(t){var e=ft,r=e.asn1,n=r.DEROctetString,i=r.cms;if(i.MessageDigest.superclass.constructor.call(this),this.attrTypeOid="1.2.840.113549.1.9.4",void 0!==t)if(t.eciObj instanceof i.EncapsulatedContentInfo&&"string"==typeof t.hashAlg){var s=t.eciObj.eContentValueHex,a=t.hashAlg;(o=new n({hex:e.crypto.Util.hashHex(s,a)})).getEncodedHex(),this.valueList=[o]}else{var o;(o=new n(t)).getEncodedHex(),this.valueList=[o]}},i.lang.extend(ft.asn1.cms.MessageDigest,ft.asn1.cms.Attribute),ft.asn1.cms.SigningTime=function(t){var e=ft.asn1;if(e.cms.SigningTime.superclass.constructor.call(this),this.attrTypeOid="1.2.840.113549.1.9.5",void 0!==t){var r=new e.x509.Time(t);try{r.getEncodedHex()}catch(t){throw"SigningTime.getEncodedHex() failed/"+t}this.valueList=[r]}},i.lang.extend(ft.asn1.cms.SigningTime,ft.asn1.cms.Attribute),ft.asn1.cms.SigningCertificate=function(t){var e=ft,r=e.asn1,n=r.DERSequence,i=r.cms;e.crypto;i.SigningCertificate.superclass.constructor.call(this),this.attrTypeOid="1.2.840.113549.1.9.16.2.12",this.setCerts=function(t){for(var s=[],a=0;a<t.length;a++){var o=Rt(t[a]),u=e.crypto.Util.hashHex(o,"sha1"),h=new r.DEROctetString({hex:u});h.getEncodedHex();var c=new i.IssuerAndSerialNumber({cert:t[a]});c.getEncodedHex();var l=new n({array:[h,c]});l.getEncodedHex(),s.push(l)}var f=new n({array:s});f.getEncodedHex(),this.valueList=[f]},void 0!==t&&"object"==typeof t.array&&this.setCerts(t.array)},i.lang.extend(ft.asn1.cms.SigningCertificate,ft.asn1.cms.Attribute),ft.asn1.cms.SigningCertificateV2=function(t){var e=ft,r=e.asn1,n=r.DERSequence,i=r.x509,s=r.cms,a=e.crypto;if(s.SigningCertificateV2.superclass.constructor.call(this),this.attrTypeOid="1.2.840.113549.1.9.16.2.47",this.setCerts=function(t,e){for(var o=[],u=0;u<t.length;u++){var h=Rt(t[u]),c=[];"sha256"!==e&&c.push(new i.AlgorithmIdentifier({name:e}));var l=a.Util.hashHex(h,e),f=new r.DEROctetString({hex:l});f.getEncodedHex(),c.push(f);var d=new s.IssuerAndSerialNumber({cert:t[u]});d.getEncodedHex(),c.push(d);var g=new n({array:c});g.getEncodedHex(),o.push(g)}var p=new n({array:o});p.getEncodedHex(),this.valueList=[p]},void 0!==t&&"object"==typeof t.array){var o="sha256";"string"==typeof t.hashAlg&&(o=t.hashAlg),this.setCerts(t.array,o)}},i.lang.extend(ft.asn1.cms.SigningCertificateV2,ft.asn1.cms.Attribute),ft.asn1.cms.IssuerAndSerialNumber=function(t){var e=ft.asn1,r=e.DERInteger,n=e.cms,i=e.x509.X500Name,s=$t;n.IssuerAndSerialNumber.superclass.constructor.call(this);this.setByCertPEM=function(t){var e=Rt(t),n=new s;n.hex=e;var a=n.getIssuerHex();this.dIssuer=new i,this.dIssuer.hTLV=a;var o=n.getSerialNumberHex();this.dSerial=new r({hex:o})},this.getEncodedHex=function(){var t=new e.DERSequence({array:[this.dIssuer,this.dSerial]});return this.hTLV=t.getEncodedHex(),this.hTLV},void 0!==t&&("string"==typeof t&&-1!=t.indexOf("-----BEGIN ")&&this.setByCertPEM(t),t.issuer&&t.serial&&(t.issuer instanceof i?this.dIssuer=t.issuer:this.dIssuer=new i(t.issuer),t.serial instanceof r?this.dSerial=t.serial:this.dSerial=new r(t.serial)),"string"==typeof t.cert&&this.setByCertPEM(t.cert))},i.lang.extend(ft.asn1.cms.IssuerAndSerialNumber,ft.asn1.ASN1Object),ft.asn1.cms.AttributeList=function(t){var e=ft.asn1,r=e.cms;r.AttributeList.superclass.constructor.call(this),this.list=new Array,this.sortFlag=!0,this.add=function(t){t instanceof r.Attribute&&this.list.push(t)},this.length=function(){return this.list.length},this.clear=function(){this.list=new Array,this.hTLV=null,this.hV=null},this.getEncodedHex=function(){if("string"==typeof this.hTLV)return this.hTLV;var t=new e.DERSet({array:this.list,sortflag:this.sortFlag});return this.hTLV=t.getEncodedHex(),this.hTLV},void 0!==t&&void 0!==t.sortflag&&0==t.sortflag&&(this.sortFlag=!1)},i.lang.extend(ft.asn1.cms.AttributeList,ft.asn1.ASN1Object),ft.asn1.cms.SignerInfo=function(t){var e=ft,r=e.asn1,n=r.DERTaggedObject,i=r.cms,s=i.AttributeList,a=i.ContentType,o=i.EncapsulatedContentInfo,u=i.MessageDigest,h=i.SignedData,c=r.x509.AlgorithmIdentifier,l=e.crypto,f=Kt;i.SignerInfo.superclass.constructor.call(this),this.dCMSVersion=new r.DERInteger({int:1}),this.dSignerIdentifier=null,this.dDigestAlgorithm=null,this.dSignedAttrs=new s,this.dSigAlg=null,this.dSig=null,this.dUnsignedAttrs=new s,this.setSignerIdentifier=function(t){if("string"==typeof t&&-1!=t.indexOf("CERTIFICATE")&&-1!=t.indexOf("BEGIN")&&-1!=t.indexOf("END")){this.dSignerIdentifier=new i.IssuerAndSerialNumber({cert:t})}},this.setForContentAndHash=function(t){void 0!==t&&(t.eciObj instanceof o&&(this.dSignedAttrs.add(new a({oid:"1.2.840.113549.1.7.1"})),this.dSignedAttrs.add(new u({eciObj:t.eciObj,hashAlg:t.hashAlg}))),void 0!==t.sdObj&&t.sdObj instanceof h&&-1==t.sdObj.digestAlgNameList.join(":").indexOf(t.hashAlg)&&t.sdObj.digestAlgNameList.push(t.hashAlg),"string"==typeof t.hashAlg&&(this.dDigestAlgorithm=new c({name:t.hashAlg})))},this.sign=function(t,e){this.dSigAlg=new c({name:e});var n=this.dSignedAttrs.getEncodedHex(),i=f.getKey(t),s=new l.Signature({alg:e});s.init(i),s.updateHex(n);var a=s.sign();this.dSig=new r.DEROctetString({hex:a})},this.addUnsigned=function(t){this.hTLV=null,this.dUnsignedAttrs.hTLV=null,this.dUnsignedAttrs.add(t)},this.getEncodedHex=function(){if(this.dSignedAttrs instanceof s&&0==this.dSignedAttrs.length())throw"SignedAttrs length = 0 (empty)";var t=new n({obj:this.dSignedAttrs,tag:"a0",explicit:!1}),e=null;this.dUnsignedAttrs.length()>0&&(e=new n({obj:this.dUnsignedAttrs,tag:"a1",explicit:!1}));var i=[this.dCMSVersion,this.dSignerIdentifier,this.dDigestAlgorithm,t,this.dSigAlg,this.dSig];null!=e&&i.push(e);var a=new r.DERSequence({array:i});return this.hTLV=a.getEncodedHex(),this.hTLV}},i.lang.extend(ft.asn1.cms.SignerInfo,ft.asn1.ASN1Object),ft.asn1.cms.EncapsulatedContentInfo=function(t){var e=ft.asn1,r=e.DERTaggedObject,n=e.DERSequence,i=e.DERObjectIdentifier,s=e.DEROctetString;e.cms.EncapsulatedContentInfo.superclass.constructor.call(this),this.dEContentType=new i({name:"data"}),this.dEContent=null,this.isDetached=!1,this.eContentValueHex=null,this.setContentType=function(t){t.match(/^[0-2][.][0-9.]+$/)?this.dEContentType=new i({oid:t}):this.dEContentType=new i({name:t})},this.setContentValue=function(t){void 0!==t&&("string"==typeof t.hex?this.eContentValueHex=t.hex:"string"==typeof t.str&&(this.eContentValueHex=wt(t.str)))},this.setContentValueHex=function(t){this.eContentValueHex=t},this.setContentValueStr=function(t){this.eContentValueHex=wt(t)},this.getEncodedHex=function(){if("string"!=typeof this.eContentValueHex)throw"eContentValue not yet set";var t=new s({hex:this.eContentValueHex});this.dEContent=new r({obj:t,tag:"a0",explicit:!0});var e=[this.dEContentType];this.isDetached||e.push(this.dEContent);var i=new n({array:e});return this.hTLV=i.getEncodedHex(),this.hTLV}},i.lang.extend(ft.asn1.cms.EncapsulatedContentInfo,ft.asn1.ASN1Object),ft.asn1.cms.ContentInfo=function(t){var e=ft.asn1,r=e.DERTaggedObject,n=e.DERSequence,i=e.x509;ft.asn1.cms.ContentInfo.superclass.constructor.call(this),this.dContentType=null,this.dContent=null,this.setContentType=function(t){"string"==typeof t&&(this.dContentType=i.OID.name2obj(t))},this.getEncodedHex=function(){var t=new r({obj:this.dContent,tag:"a0",explicit:!0}),e=new n({array:[this.dContentType,t]});return this.hTLV=e.getEncodedHex(),this.hTLV},void 0!==t&&(t.type&&this.setContentType(t.type),t.obj&&t.obj instanceof e.ASN1Object&&(this.dContent=t.obj))},i.lang.extend(ft.asn1.cms.ContentInfo,ft.asn1.ASN1Object),ft.asn1.cms.SignedData=function(t){var e=ft.asn1,r=e.ASN1Object,n=e.DERInteger,i=e.DERSet,s=e.DERSequence,a=e.DERTaggedObject,o=e.cms,u=o.EncapsulatedContentInfo,h=o.SignerInfo,c=o.ContentInfo,l=e.x509.AlgorithmIdentifier;ft.asn1.cms.SignedData.superclass.constructor.call(this),this.dCMSVersion=new n({int:1}),this.dDigestAlgs=null,this.digestAlgNameList=[],this.dEncapContentInfo=new u,this.dCerts=null,this.certificateList=[],this.crlList=[],this.signerInfoList=[new h],this.addCertificatesByPEM=function(t){var e=Rt(t),n=new r;n.hTLV=e,this.certificateList.push(n)},this.getEncodedHex=function(){if("string"==typeof this.hTLV)return this.hTLV;if(null==this.dDigestAlgs){for(var t=[],e=0;e<this.digestAlgNameList.length;e++){var r=this.digestAlgNameList[e],n=new l({name:r});t.push(n)}this.dDigestAlgs=new i({array:t})}var o=[this.dCMSVersion,this.dDigestAlgs,this.dEncapContentInfo];if(null==this.dCerts&&this.certificateList.length>0){var u=new i({array:this.certificateList});this.dCerts=new a({obj:u,tag:"a0",explicit:!1})}null!=this.dCerts&&o.push(this.dCerts);var h=new i({array:this.signerInfoList});o.push(h);var c=new s({array:o});return this.hTLV=c.getEncodedHex(),this.hTLV},this.getContentInfo=function(){return this.getEncodedHex(),new c({type:"signed-data",obj:this})},this.getContentInfoEncodedHex=function(){return this.getContentInfo().getEncodedHex()},this.getPEM=function(){return Pt(this.getContentInfoEncodedHex(),"CMS")}},i.lang.extend(ft.asn1.cms.SignedData,ft.asn1.ASN1Object),ft.asn1.cms.CMSUtil=new function(){},ft.asn1.cms.CMSUtil.newSignedData=function(t){var e=ft.asn1,r=e.cms,n=r.SignerInfo,i=r.SignedData,s=r.SigningTime,a=r.SigningCertificate,o=r.SigningCertificateV2,u=e.cades.SignaturePolicyIdentifier,h=new i;if(h.dEncapContentInfo.setContentValue(t.content),"object"==typeof t.certs)for(var c=0;c<t.certs.length;c++)h.addCertificatesByPEM(t.certs[c]);h.signerInfoList=[];for(c=0;c<t.signerInfos.length;c++){var l=t.signerInfos[c],f=new n;for(attrName in f.setSignerIdentifier(l.signerCert),f.setForContentAndHash({sdObj:h,eciObj:h.dEncapContentInfo,hashAlg:l.hashAlg}),l.sAttr){var d=l.sAttr[attrName];if("SigningTime"==attrName){var g=new s(d);f.dSignedAttrs.add(g)}if("SigningCertificate"==attrName){g=new a(d);f.dSignedAttrs.add(g)}if("SigningCertificateV2"==attrName){g=new o(d);f.dSignedAttrs.add(g)}if("SignaturePolicyIdentifier"==attrName){g=new u(d);f.dSignedAttrs.add(g)}}f.sign(l.signerPrvKey,l.sigAlg),h.signerInfoList.push(f)}return h},ft.asn1.cms.CMSUtil.verifySignedData=function(t){var e=ft,r=e.asn1,n=r.cms,i=(n.SignerInfo,n.SignedData,n.SigningTime,n.SigningCertificate,n.SigningCertificateV2,r.cades.SignaturePolicyIdentifier,e.lang.String.isHex),s=pt,a=s.getVbyList,o=s.getTLVbyList,u=s.getIdxbyList,h=s.getChildIdx,c=s.getTLV,l=s.oidname,f=e.crypto.Util.hashHex;void 0===t.cms&&i(t.cms);var d=t.cms,g=function(t,e){var r=e.idx;e.signerid_issuer1=o(t,r,[1,0],"30"),e.signerid_serial1=a(t,r,[1,1],"02"),e.hashalg=l(a(t,r,[2,0],"06"));var n=u(t,r,[3],"a0");e.idxSignedAttrs=n,p(t,e,n);var i=h(t,r).length;if(i<6)throw"malformed SignerInfo";e.sigalg=l(a(t,r,[i-2,0],"06")),e.sigval=a(t,r,[i-1],"04")},p=function(t,e,r){var n=h(t,r);e.signedAttrIdxList=n;for(var i=0;i<n.length;i++){var s,o=n[i],u=a(t,o,[0],"06");"2a864886f70d010905"===u?(s=Et(a(t,o,[1,0])),e.saSigningTime=s):"2a864886f70d010904"===u&&(s=a(t,o,[1,0],"04"),e.saMessageDigest=s)}},y=function(t,e,r,n){r.verifyDetail={};var i=r.verifyDetail,s=e.parse.econtent,a=r.hashalg,o=r.saMessageDigest;i.validMessageDigest=!1,f(s,a)===o&&(i.validMessageDigest=!0),function(t,e,r,n){var i,s=e.parse.certsIdx;if(void 0===e.certs){i=[],e.certkeys=[];for(var a=h(t,s),o=0;o<a.length;o++){var u=c(t,a[o]),l=new $t;l.readCertHex(u),i[o]=l,e.certkeys[o]=l.getPublicKey()}e.certs=i}else i=e.certs;e.cccc=i.length,e.cccci=a.length;for(o=0;o<i.length;o++){var f=l.getIssuerHex(),d=l.getSerialNumberHex();r.signerid_issuer1===f&&r.signerid_serial1===d&&(r.certkey_idx=o)}}(t,e,r),i.validSignatureValue=!1;var u=r.sigalg,l="31"+c(t,r.idxSignedAttrs).substr(2);r.signedattrshex=l;var d=e.certs[r.certkey_idx].getPublicKey(),g=new ft.crypto.Signature({alg:u});g.init(d),g.updateHex(l);var p=g.verify(r.sigval);i.validSignatureValue_isValid=p,!0===p&&(i.validSignatureValue=!0),r.isValid=!1,i.validMessageDigest&&i.validSignatureValue&&(r.isValid=!0)},v={isValid:!1,parse:{}};return function(t,e){if("2a864886f70d010702"!==a(t,0,[0],"06"))return e;e.cmsType="signedData",e.econtent=a(t,0,[1,0,2,1,0]),function(t,e){for(var r,n=3;n<6;n++)if(void 0!==(r=u(t,0,[1,0,n]))){var i=t.substr(r,2);"a0"===i&&(e.certsIdx=r),"a1"===i&&(e.revinfosIdx=r),"31"===i&&(e.signerinfosIdx=r)}}(t,e),e.signerInfos=[],function(t,e){var r=e.signerinfosIdx;if(void 0!==r){var n=h(t,r);e.signerInfoIdxList=n;for(var i=0;i<n.length;i++){var s={idx:n[i]};g(t,s),e.signerInfos.push(s)}}}(t,e)}(d,v.parse),function(t,e){for(var r=e.parse.signerInfos,n=r.length,i=!0,s=0;s<n;s++){var a=r[s];y(t,e,a,s),a.isValid||(i=!1)}e.isValid=i}(d,v),v},void 0!==ft&&ft||(ft={}),void 0!==ft.asn1&&ft.asn1||(ft.asn1={}),void 0!==ft.asn1.tsp&&ft.asn1.tsp||(ft.asn1.tsp={}),ft.asn1.tsp.Accuracy=function(t){var e=ft.asn1,r=e.DERInteger,n=e.DERSequence,i=e.DERTaggedObject;e.tsp.Accuracy.superclass.constructor.call(this),this.seconds=null,this.millis=null,this.micros=null,this.getEncodedHex=function(){var t=null,e=null,s=null,a=[];if(null!=this.seconds&&(t=new r({int:this.seconds}),a.push(t)),null!=this.millis){var o=new r({int:this.millis});e=new i({obj:o,tag:"80",explicit:!1}),a.push(e)}if(null!=this.micros){var u=new r({int:this.micros});s=new i({obj:u,tag:"81",explicit:!1}),a.push(s)}var h=new n({array:a});return this.hTLV=h.getEncodedHex(),this.hTLV},void 0!==t&&("number"==typeof t.seconds&&(this.seconds=t.seconds),"number"==typeof t.millis&&(this.millis=t.millis),"number"==typeof t.micros&&(this.micros=t.micros))},i.lang.extend(ft.asn1.tsp.Accuracy,ft.asn1.ASN1Object),ft.asn1.tsp.MessageImprint=function(t){var e=ft.asn1,r=e.DERSequence,n=e.DEROctetString,i=e.x509.AlgorithmIdentifier;e.tsp.MessageImprint.superclass.constructor.call(this),this.dHashAlg=null,this.dHashValue=null,this.getEncodedHex=function(){return"string"==typeof this.hTLV?this.hTLV:new r({array:[this.dHashAlg,this.dHashValue]}).getEncodedHex()},void 0!==t&&("string"==typeof t.hashAlg&&(this.dHashAlg=new i({name:t.hashAlg})),"string"==typeof t.hashValue&&(this.dHashValue=new n({hex:t.hashValue})))},i.lang.extend(ft.asn1.tsp.MessageImprint,ft.asn1.ASN1Object),ft.asn1.tsp.TimeStampReq=function(t){var e=ft.asn1,r=e.DERSequence,n=e.DERInteger,i=e.DERBoolean,s=e.DERObjectIdentifier,a=e.tsp,o=a.MessageImprint;a.TimeStampReq.superclass.constructor.call(this),this.dVersion=new n({int:1}),this.dMessageImprint=null,this.dPolicy=null,this.dNonce=null,this.certReq=!0,this.setMessageImprint=function(t){t instanceof o?this.dMessageImprint=t:"object"==typeof t&&(this.dMessageImprint=new o(t))},this.getEncodedHex=function(){if(null==this.dMessageImprint)throw"messageImprint shall be specified";var t=[this.dVersion,this.dMessageImprint];null!=this.dPolicy&&t.push(this.dPolicy),null!=this.dNonce&&t.push(this.dNonce),this.certReq&&t.push(new i);var e=new r({array:t});return this.hTLV=e.getEncodedHex(),this.hTLV},void 0!==t&&("object"==typeof t.mi&&this.setMessageImprint(t.mi),"object"==typeof t.policy&&(this.dPolicy=new s(t.policy)),"object"==typeof t.nonce&&(this.dNonce=new n(t.nonce)),"boolean"==typeof t.certreq&&(this.certReq=t.certreq))},i.lang.extend(ft.asn1.tsp.TimeStampReq,ft.asn1.ASN1Object),ft.asn1.tsp.TSTInfo=function(t){var e=ft.asn1,r=e.DERSequence,n=e.DERInteger,i=e.DERBoolean,s=e.DERGeneralizedTime,a=e.DERObjectIdentifier,o=e.tsp,u=o.MessageImprint,h=o.Accuracy,c=e.x509.X500Name;if(o.TSTInfo.superclass.constructor.call(this),this.dVersion=new n({int:1}),this.dPolicy=null,this.dMessageImprint=null,this.dSerialNumber=null,this.dGenTime=null,this.dAccuracy=null,this.dOrdering=null,this.dNonce=null,this.dTsa=null,this.getEncodedHex=function(){var t=[this.dVersion];if(null==this.dPolicy)throw"policy shall be specified.";if(t.push(this.dPolicy),null==this.dMessageImprint)throw"messageImprint shall be specified.";if(t.push(this.dMessageImprint),null==this.dSerialNumber)throw"serialNumber shall be specified.";if(t.push(this.dSerialNumber),null==this.dGenTime)throw"genTime shall be specified.";t.push(this.dGenTime),null!=this.dAccuracy&&t.push(this.dAccuracy),null!=this.dOrdering&&t.push(this.dOrdering),null!=this.dNonce&&t.push(this.dNonce),null!=this.dTsa&&t.push(this.dTsa);var e=new r({array:t});return this.hTLV=e.getEncodedHex(),this.hTLV},void 0!==t){if("string"==typeof t.policy){if(!t.policy.match(/^[0-9.]+$/))throw"policy shall be oid like 0.1.4.134";this.dPolicy=new a({oid:t.policy})}void 0!==t.messageImprint&&(this.dMessageImprint=new u(t.messageImprint)),void 0!==t.serialNumber&&(this.dSerialNumber=new n(t.serialNumber)),void 0!==t.genTime&&(this.dGenTime=new s(t.genTime)),void 0!==t.accuracy&&(this.dAccuracy=new h(t.accuracy)),void 0!==t.ordering&&1==t.ordering&&(this.dOrdering=new i),void 0!==t.nonce&&(this.dNonce=new n(t.nonce)),void 0!==t.tsa&&(this.dTsa=new c(t.tsa))}},i.lang.extend(ft.asn1.tsp.TSTInfo,ft.asn1.ASN1Object),ft.asn1.tsp.TimeStampResp=function(t){var e=ft.asn1,r=e.DERSequence,n=e.ASN1Object,i=e.tsp,s=i.PKIStatusInfo;i.TimeStampResp.superclass.constructor.call(this),this.dStatus=null,this.dTST=null,this.getEncodedHex=function(){if(null==this.dStatus)throw"status shall be specified";var t=[this.dStatus];null!=this.dTST&&t.push(this.dTST);var e=new r({array:t});return this.hTLV=e.getEncodedHex(),this.hTLV},void 0!==t&&("object"==typeof t.status&&(this.dStatus=new s(t.status)),void 0!==t.tst&&t.tst instanceof n&&(this.dTST=t.tst.getContentInfo()))},i.lang.extend(ft.asn1.tsp.TimeStampResp,ft.asn1.ASN1Object),ft.asn1.tsp.PKIStatusInfo=function(t){var e=ft.asn1,r=e.DERSequence,n=e.tsp,i=n.PKIStatus,s=n.PKIFreeText,a=n.PKIFailureInfo;n.PKIStatusInfo.superclass.constructor.call(this),this.dStatus=null,this.dStatusString=null,this.dFailureInfo=null,this.getEncodedHex=function(){if(null==this.dStatus)throw"status shall be specified";var t=[this.dStatus];null!=this.dStatusString&&t.push(this.dStatusString),null!=this.dFailureInfo&&t.push(this.dFailureInfo);var e=new r({array:t});return this.hTLV=e.getEncodedHex(),this.hTLV},void 0!==t&&("object"==typeof t.status&&(this.dStatus=new i(t.status)),"object"==typeof t.statstr&&(this.dStatusString=new s({array:t.statstr})),"object"==typeof t.failinfo&&(this.dFailureInfo=new a(t.failinfo)))},i.lang.extend(ft.asn1.tsp.PKIStatusInfo,ft.asn1.ASN1Object),ft.asn1.tsp.PKIStatus=function(t){var e=ft.asn1,r=e.DERInteger,n=e.tsp,i=n.PKIStatus;n.PKIStatus.superclass.constructor.call(this);if(this.getEncodedHex=function(){return this.hTLV=this.dStatus.getEncodedHex(),this.hTLV},void 0!==t)if(void 0!==t.name){var s=i.valueList;if(void 0===s[t.name])throw"name undefined: "+t.name;this.dStatus=new r({int:s[t.name]})}else this.dStatus=new r(t)},i.lang.extend(ft.asn1.tsp.PKIStatus,ft.asn1.ASN1Object),ft.asn1.tsp.PKIStatus.valueList={granted:0,grantedWithMods:1,rejection:2,waiting:3,revocationWarning:4,revocationNotification:5},ft.asn1.tsp.PKIFreeText=function(t){var e=ft.asn1,r=e.DERSequence,n=e.DERUTF8String;e.tsp.PKIFreeText.superclass.constructor.call(this),this.textList=[],this.getEncodedHex=function(){for(var t=[],e=0;e<this.textList.length;e++)t.push(new n({str:this.textList[e]}));var i=new r({array:t});return this.hTLV=i.getEncodedHex(),this.hTLV},void 0!==t&&"object"==typeof t.array&&(this.textList=t.array)},i.lang.extend(ft.asn1.tsp.PKIFreeText,ft.asn1.ASN1Object),ft.asn1.tsp.PKIFailureInfo=function(t){var e=ft.asn1,r=e.DERBitString,n=e.tsp.PKIFailureInfo;if(n.superclass.constructor.call(this),this.value=null,this.getEncodedHex=function(){if(null==this.value)throw"value shall be specified";var t=new Number(this.value).toString(2),e=new r;return e.setByBinaryString(t),this.hTLV=e.getEncodedHex(),this.hTLV},void 0!==t)if("string"==typeof t.name){var i=n.valueList;if(void 0===i[t.name])throw"name undefined: "+t.name;this.value=i[t.name]}else"number"==typeof t.int&&(this.value=t.int)},i.lang.extend(ft.asn1.tsp.PKIFailureInfo,ft.asn1.ASN1Object),ft.asn1.tsp.PKIFailureInfo.valueList={badAlg:0,badRequest:2,badDataFormat:5,timeNotAvailable:14,unacceptedPolicy:15,unacceptedExtension:16,addInfoNotAvailable:17,systemFailure:25},ft.asn1.tsp.AbstractTSAAdapter=function(t){this.getTSTHex=function(t,e){throw"not implemented yet"}},ft.asn1.tsp.SimpleTSAAdapter=function(t){var e=ft,r=e.asn1.tsp,n=e.crypto.Util.hashHex;r.SimpleTSAAdapter.superclass.constructor.call(this),this.params=null,this.serial=0,this.getTSTHex=function(t,e){var i=n(t,e);this.params.tstInfo.messageImprint={hashAlg:e,hashValue:i},this.params.tstInfo.serialNumber={int:this.serial++};var s=Math.floor(1e9*Math.random());return this.params.tstInfo.nonce={int:s},r.TSPUtil.newTimeStampToken(this.params).getContentInfoEncodedHex()},void 0!==t&&(this.params=t)},i.lang.extend(ft.asn1.tsp.SimpleTSAAdapter,ft.asn1.tsp.AbstractTSAAdapter),ft.asn1.tsp.FixedTSAAdapter=function(t){var e=ft,r=e.asn1.tsp,n=e.crypto.Util.hashHex;r.FixedTSAAdapter.superclass.constructor.call(this),this.params=null,this.getTSTHex=function(t,e){var i=n(t,e);return this.params.tstInfo.messageImprint={hashAlg:e,hashValue:i},r.TSPUtil.newTimeStampToken(this.params).getContentInfoEncodedHex()},void 0!==t&&(this.params=t)},i.lang.extend(ft.asn1.tsp.FixedTSAAdapter,ft.asn1.tsp.AbstractTSAAdapter),ft.asn1.tsp.TSPUtil=new function(){},ft.asn1.tsp.TSPUtil.newTimeStampToken=function(t){var e=ft.asn1,r=e.cms,n=(e.tsp,e.tsp.TSTInfo),i=new r.SignedData,s=new n(t.tstInfo).getEncodedHex();if(i.dEncapContentInfo.setContentValue({hex:s}),i.dEncapContentInfo.setContentType("tstinfo"),"object"==typeof t.certs)for(var a=0;a<t.certs.length;a++)i.addCertificatesByPEM(t.certs[a]);var o=i.signerInfoList[0];o.setSignerIdentifier(t.signerCert),o.setForContentAndHash({sdObj:i,eciObj:i.dEncapContentInfo,hashAlg:t.hashAlg});var u=new r.SigningCertificate({array:[t.signerCert]});return o.dSignedAttrs.add(u),o.sign(t.signerPrvKey,t.sigAlg),i},ft.asn1.tsp.TSPUtil.parseTimeStampReq=function(t){var e=pt,r=e.getChildIdx,n=e.getV,i=e.getTLV,s={certreq:!1},a=r(t,0);if(a.length<2)throw"TimeStampReq must have at least 2 items";var o=i(t,a[1]);s.mi=ft.asn1.tsp.TSPUtil.parseMessageImprint(o);for(var u=2;u<a.length;u++){var h=a[u],c=t.substr(h,2);if("06"==c){var l=n(t,h);s.policy=e.hextooidstr(l)}"02"==c&&(s.nonce=n(t,h)),"01"==c&&(s.certreq=!0)}return s},ft.asn1.tsp.TSPUtil.parseMessageImprint=function(t){var e=pt,r=e.getChildIdx,n=e.getV,i=e.getIdxbyList,s={};if("30"!=t.substr(0,2))throw"head of messageImprint hex shall be '30'";r(t,0);var a=n(t,i(t,0,[0,0])),o=e.hextooidstr(a),u=ft.asn1.x509.OID.oid2name(o);if(""==u)throw"hashAlg name undefined: "+o;var h=u,c=i(t,0,[1]);return s.hashAlg=h,s.hashValue=n(t,c),s},void 0!==ft&&ft||(ft={}),void 0!==ft.asn1&&ft.asn1||(ft.asn1={}),void 0!==ft.asn1.cades&&ft.asn1.cades||(ft.asn1.cades={}),ft.asn1.cades.SignaturePolicyIdentifier=function(t){var e=ft.asn1,r=e.DERObjectIdentifier,n=e.DERSequence,i=e.cades,s=i.OtherHashAlgAndValue;if(i.SignaturePolicyIdentifier.superclass.constructor.call(this),this.attrTypeOid="1.2.840.113549.1.9.16.2.15",void 0!==t&&"string"==typeof t.oid&&"object"==typeof t.hash){var a=new n({array:[new r({oid:t.oid}),new s(t.hash)]});this.valueList=[a]}},i.lang.extend(ft.asn1.cades.SignaturePolicyIdentifier,ft.asn1.cms.Attribute),ft.asn1.cades.OtherHashAlgAndValue=function(t){var e=ft.asn1,r=e.DERSequence,n=e.DEROctetString,i=e.x509.AlgorithmIdentifier;e.cades.OtherHashAlgAndValue.superclass.constructor.call(this),this.dAlg=null,this.dHash=null,this.getEncodedHex=function(){var t=new r({array:[this.dAlg,this.dHash]});return this.hTLV=t.getEncodedHex(),this.hTLV},void 0!==t&&"string"==typeof t.alg&&"string"==typeof t.hash&&(this.dAlg=new i({name:t.alg}),this.dHash=new n({hex:t.hash}))},i.lang.extend(ft.asn1.cades.OtherHashAlgAndValue,ft.asn1.ASN1Object),ft.asn1.cades.SignatureTimeStamp=function(t){var e=ft.asn1,r=e.ASN1Object;e.x509;if(e.cades.SignatureTimeStamp.superclass.constructor.call(this),this.attrTypeOid="1.2.840.113549.1.9.16.2.14",this.tstHex=null,void 0!==t){if(void 0!==t.res)if("string"==typeof t.res&&t.res.match(/^[0-9A-Fa-f]+$/));else if(!(t.res instanceof r))throw"res param shall be ASN1Object or hex string";if(void 0!==t.tst)if("string"==typeof t.tst&&t.tst.match(/^[0-9A-Fa-f]+$/)){var n=new r;this.tstHex=t.tst,n.hTLV=this.tstHex,n.getEncodedHex(),this.valueList=[n]}else if(!(t.tst instanceof r))throw"tst param shall be ASN1Object or hex string"}},i.lang.extend(ft.asn1.cades.SignatureTimeStamp,ft.asn1.cms.Attribute),ft.asn1.cades.CompleteCertificateRefs=function(t){var e=ft.asn1.cades;e.CompleteCertificateRefs.superclass.constructor.call(this),this.attrTypeOid="1.2.840.113549.1.9.16.2.21",this.setByArray=function(t){this.valueList=[];for(var r=0;r<t.length;r++){var n=new e.OtherCertID(t[r]);this.valueList.push(n)}},void 0!==t&&"object"==typeof t&&"number"==typeof t.length&&this.setByArray(t)},i.lang.extend(ft.asn1.cades.CompleteCertificateRefs,ft.asn1.cms.Attribute),ft.asn1.cades.OtherCertID=function(t){var e=ft.asn1,r=e.cms,n=e.cades;n.OtherCertID.superclass.constructor.call(this),this.hasIssuerSerial=!0,this.dOtherCertHash=null,this.dIssuerSerial=null,this.setByCertPEM=function(t){this.dOtherCertHash=new n.OtherHash(t),this.hasIssuerSerial&&(this.dIssuerSerial=new r.IssuerAndSerialNumber(t))},this.getEncodedHex=function(){if(null!=this.hTLV)return this.hTLV;if(null==this.dOtherCertHash)throw"otherCertHash not set";var t=[this.dOtherCertHash];null!=this.dIssuerSerial&&t.push(this.dIssuerSerial);var r=new e.DERSequence({array:t});return this.hTLV=r.getEncodedHex(),this.hTLV},void 0!==t&&("string"==typeof t&&-1!=t.indexOf("-----BEGIN ")&&this.setByCertPEM(t),"object"==typeof t&&(!1===t.hasis&&(this.hasIssuerSerial=!1),"string"==typeof t.cert&&this.setByCertPEM(t.cert)))},i.lang.extend(ft.asn1.cades.OtherCertID,ft.asn1.ASN1Object),ft.asn1.cades.OtherHash=function(t){var e=ft,r=e.asn1,n=(r.cms,r.cades),i=n.OtherHashAlgAndValue,s=e.crypto.Util.hashHex;if(n.OtherHash.superclass.constructor.call(this),this.alg="sha256",this.dOtherHash=null,this.setByCertPEM=function(t){if(-1==t.indexOf("-----BEGIN "))throw"certPEM not to seem PEM format";var e=Rt(t),r=s(e,this.alg);this.dOtherHash=new i({alg:this.alg,hash:r})},this.getEncodedHex=function(){if(null==this.dOtherHash)throw"OtherHash not set";return this.dOtherHash.getEncodedHex()},void 0!==t)if("string"==typeof t)if(-1!=t.indexOf("-----BEGIN "))this.setByCertPEM(t);else{if(!t.match(/^[0-9A-Fa-f]+$/))throw"unsupported string value for params";this.dOtherHash=new r.DEROctetString({hex:t})}else"object"==typeof t&&("string"==typeof t.cert?("string"==typeof t.alg&&(this.alg=t.alg),this.setByCertPEM(t.cert)):this.dOtherHash=new i(t))},i.lang.extend(ft.asn1.cades.OtherHash,ft.asn1.ASN1Object),ft.asn1.cades.CAdESUtil=new function(){},ft.asn1.cades.CAdESUtil.addSigTS=function(t,e,r){},ft.asn1.cades.CAdESUtil.parseSignedDataForAddingUnsigned=function(t){var e=pt,r=e.getChildIdx,n=e.getTLV,i=e.getTLVbyList,s=e.getIdxbyList,a=ft.asn1,o=a.ASN1Object,u=a.cms.SignedData,h=a.cades.CAdESUtil,c={};if("06092a864886f70d010702"!=i(t,0,[0]))throw"hex is not CMS SignedData";var l=r(t,s(t,0,[1,0]));if(l.length<4)throw"num of SignedData elem shall be 4 at least";var f=l.shift();c.version=n(t,f);var d=l.shift();c.algs=n(t,d);var g=l.shift();c.encapcontent=n(t,g),c.certs=null,c.revs=null,c.si=[];var p=l.shift();"a0"==t.substr(p,2)&&(c.certs=n(t,p),p=l.shift()),"a1"==t.substr(p,2)&&(c.revs=n(t,p),p=l.shift());var y=p;if("31"!=t.substr(y,2))throw"Can't find signerInfos";for(var v=r(t,y),m=0;m<v.length;m++){var F=v[m],S=h.parseSignerInfoForAddingUnsigned(t,F,m);c.si[m]=S}var b=null;c.obj=new u,(b=new o).hTLV=c.version,c.obj.dCMSVersion=b,(b=new o).hTLV=c.algs,c.obj.dDigestAlgs=b,(b=new o).hTLV=c.encapcontent,c.obj.dEncapContentInfo=b,(b=new o).hTLV=c.certs,c.obj.dCerts=b,c.obj.signerInfoList=[];for(m=0;m<c.si.length;m++)c.obj.signerInfoList.push(c.si[m].obj);return c},ft.asn1.cades.CAdESUtil.parseSignerInfoForAddingUnsigned=function(t,e,r){var n=pt,i=n.getChildIdx,s=n.getTLV,a=n.getV,o=ft.asn1,u=o.ASN1Object,h=o.cms,c=h.AttributeList,l=h.SignerInfo,f={},d=i(t,e);if(6!=d.length)throw"not supported items for SignerInfo (!=6)";var g=d.shift();f.version=s(t,g);var p=d.shift();f.si=s(t,p);var y=d.shift();f.digalg=s(t,y);var v=d.shift();f.sattrs=s(t,v);var m=d.shift();f.sigalg=s(t,m);var F=d.shift();f.sig=s(t,F),f.sigval=a(t,F);var S=null;return f.obj=new l,(S=new u).hTLV=f.version,f.obj.dCMSVersion=S,(S=new u).hTLV=f.si,f.obj.dSignerIdentifier=S,(S=new u).hTLV=f.digalg,f.obj.dDigestAlgorithm=S,(S=new u).hTLV=f.sattrs,f.obj.dSignedAttrs=S,(S=new u).hTLV=f.sigalg,f.obj.dSigAlg=S,(S=new u).hTLV=f.sig,f.obj.dSig=S,f.obj.dUnsignedAttrs=new c,f},void 0!==ft.asn1.csr&&ft.asn1.csr||(ft.asn1.csr={}),ft.asn1.csr.CertificationRequest=function(t){var e=ft,r=e.asn1,n=r.DERBitString,i=r.DERSequence,s=r.csr,a=r.x509;s.CertificationRequest.superclass.constructor.call(this);this.sign=function(t,r){null==this.prvKey&&(this.prvKey=r),this.asn1SignatureAlg=new a.AlgorithmIdentifier({name:t}),sig=new e.crypto.Signature({alg:t}),sig.init(this.prvKey),sig.updateHex(this.asn1CSRInfo.getEncodedHex()),this.hexSig=sig.sign(),this.asn1Sig=new n({hex:"00"+this.hexSig});var s=new i({array:[this.asn1CSRInfo,this.asn1SignatureAlg,this.asn1Sig]});this.hTLV=s.getEncodedHex(),this.isModified=!1},this.getPEMString=function(){return Pt(this.getEncodedHex(),"CERTIFICATE REQUEST")},this.getEncodedHex=function(){if(0==this.isModified&&null!=this.hTLV)return this.hTLV;throw"not signed yet"},void 0!==t&&void 0!==t.csrinfo&&(this.asn1CSRInfo=t.csrinfo)},i.lang.extend(ft.asn1.csr.CertificationRequest,ft.asn1.ASN1Object),ft.asn1.csr.CertificationRequestInfo=function(t){var e=ft.asn1,r=e.DERInteger,n=e.DERSequence,i=e.DERSet,s=e.DERNull,a=e.DERTaggedObject,o=e.DERObjectIdentifier,u=e.csr,h=e.x509,c=h.X500Name,l=h.Extension,f=Kt;u.CertificationRequestInfo.superclass.constructor.call(this),this._initialize=function(){this.asn1Array=new Array,this.asn1Version=new r({int:0}),this.asn1Subject=null,this.asn1SubjPKey=null,this.extensionsArray=new Array},this.setSubjectByParam=function(t){this.asn1Subject=new c(t)},this.setSubjectPublicKeyByGetKey=function(t){var e=f.getKey(t);this.asn1SubjPKey=new h.SubjectPublicKeyInfo(e)},this.appendExtensionByName=function(t,e){l.appendByNameToArray(t,e,this.extensionsArray)},this.getEncodedHex=function(){if(this.asn1Array=new Array,this.asn1Array.push(this.asn1Version),this.asn1Array.push(this.asn1Subject),this.asn1Array.push(this.asn1SubjPKey),this.extensionsArray.length>0){var t=new n({array:this.extensionsArray}),e=new i({array:[t]}),r=new n({array:[new o({oid:"1.2.840.113549.1.9.14"}),e]}),u=new a({explicit:!0,tag:"a0",obj:r});this.asn1Array.push(u)}else{u=new a({explicit:!1,tag:"a0",obj:new s});this.asn1Array.push(u)}var h=new n({array:this.asn1Array});return this.hTLV=h.getEncodedHex(),this.isModified=!1,this.hTLV},this._initialize()},i.lang.extend(ft.asn1.csr.CertificationRequestInfo,ft.asn1.ASN1Object),ft.asn1.csr.CSRUtil=new function(){},ft.asn1.csr.CSRUtil.newCSRPEM=function(t){var e=Kt,r=ft.asn1.csr;if(void 0===t.subject)throw"parameter subject undefined";if(void 0===t.sbjpubkey)throw"parameter sbjpubkey undefined";if(void 0===t.sigalg)throw"parameter sigalg undefined";if(void 0===t.sbjprvkey)throw"parameter sbjpubkey undefined";var n=new r.CertificationRequestInfo;if(n.setSubjectByParam(t.subject),n.setSubjectPublicKeyByGetKey(t.sbjpubkey),void 0!==t.ext&&void 0!==t.ext.length)for(var i=0;i<t.ext.length;i++)for(key in t.ext[i])n.appendExtensionByName(key,t.ext[i][key]);var s=new r.CertificationRequest({csrinfo:n}),a=e.getKey(t.sbjprvkey);return s.sign(t.sigalg,a),s.getPEMString()},ft.asn1.csr.CSRUtil.getInfo=function(t){var e=pt.getTLVbyList,r={subject:{},pubkey:{}};if(-1==t.indexOf("-----BEGIN CERTIFICATE REQUEST"))throw"argument is not PEM file";var n=Rt(t,"CERTIFICATE REQUEST");return r.subject.hex=e(n,0,[0,1]),r.subject.name=$t.hex2dn(r.subject.hex),r.pubkey.hex=e(n,0,[0,2]),r.pubkey.obj=Kt.getKey(r.pubkey.hex,null,"pkcs8pub"),r},void 0!==ft&&ft||(ft={}),void 0!==ft.asn1&&ft.asn1||(ft.asn1={}),void 0!==ft.asn1.ocsp&&ft.asn1.ocsp||(ft.asn1.ocsp={}),ft.asn1.ocsp.DEFAULT_HASH="sha1",ft.asn1.ocsp.CertID=function(t){var e=ft,r=e.asn1,n=r.DEROctetString,i=r.DERInteger,s=r.DERSequence,a=r.x509.AlgorithmIdentifier,o=r.ocsp,u=o.DEFAULT_HASH,h=e.crypto.Util.hashHex,c=$t,l=pt;if(o.CertID.superclass.constructor.call(this),this.dHashAlg=null,this.dIssuerNameHash=null,this.dIssuerKeyHash=null,this.dSerialNumber=null,this.setByValue=function(t,e,r,s){void 0===s&&(s=u),this.dHashAlg=new a({name:s}),this.dIssuerNameHash=new n({hex:t}),this.dIssuerKeyHash=new n({hex:e}),this.dSerialNumber=new i({hex:r})},this.setByCert=function(t,e,r){void 0===r&&(r=u);var n=new c;n.readCertPEM(e);var i=new c;i.readCertPEM(t);var s=i.getPublicKeyHex(),a=l.getTLVbyList(s,0,[1,0],"30"),o=n.getSerialNumberHex(),f=h(i.getSubjectHex(),r),d=h(a,r);this.setByValue(f,d,o,r),this.hoge=n.getSerialNumberHex()},this.getEncodedHex=function(){if(null===this.dHashAlg&&null===this.dIssuerNameHash&&null===this.dIssuerKeyHash&&null===this.dSerialNumber)throw"not yet set values";var t=[this.dHashAlg,this.dIssuerNameHash,this.dIssuerKeyHash,this.dSerialNumber],e=new s({array:t});return this.hTLV=e.getEncodedHex(),this.hTLV},void 0!==t){var f=t;if(void 0!==f.issuerCert&&void 0!==f.subjectCert){var d=u;void 0===f.alg&&(d=void 0),this.setByCert(f.issuerCert,f.subjectCert,d)}else{if(void 0===f.namehash||void 0===f.keyhash||void 0===f.serial)throw"invalid constructor arguments";d=u;void 0===f.alg&&(d=void 0),this.setByValue(f.namehash,f.keyhash,f.serial,d)}}},i.lang.extend(ft.asn1.ocsp.CertID,ft.asn1.ASN1Object),ft.asn1.ocsp.Request=function(t){var e=ft.asn1,r=e.DERSequence,n=e.ocsp;if(n.Request.superclass.constructor.call(this),this.dReqCert=null,this.dExt=null,this.getEncodedHex=function(){var t=[];if(null===this.dReqCert)throw"reqCert not set";t.push(this.dReqCert);var e=new r({array:t});return this.hTLV=e.getEncodedHex(),this.hTLV},void 0!==t){var i=new n.CertID(t);this.dReqCert=i}},i.lang.extend(ft.asn1.ocsp.Request,ft.asn1.ASN1Object),ft.asn1.ocsp.TBSRequest=function(t){var e=ft.asn1,r=e.DERSequence,n=e.ocsp;n.TBSRequest.superclass.constructor.call(this),this.version=0,this.dRequestorName=null,this.dRequestList=[],this.dRequestExt=null,this.setRequestListByParam=function(t){for(var e=[],r=0;r<t.length;r++){var i=new n.Request(t[0]);e.push(i)}this.dRequestList=e},this.getEncodedHex=function(){var t=[];if(0!==this.version)throw"not supported version: "+this.version;if(null!==this.dRequestorName)throw"requestorName not supported";var e=new r({array:this.dRequestList});if(t.push(e),null!==this.dRequestExt)throw"requestExtensions not supported";var n=new r({array:t});return this.hTLV=n.getEncodedHex(),this.hTLV},void 0!==t&&void 0!==t.reqList&&this.setRequestListByParam(t.reqList)},i.lang.extend(ft.asn1.ocsp.TBSRequest,ft.asn1.ASN1Object),ft.asn1.ocsp.OCSPRequest=function(t){var e=ft.asn1,r=e.DERSequence,n=e.ocsp;if(n.OCSPRequest.superclass.constructor.call(this),this.dTbsRequest=null,this.dOptionalSignature=null,this.getEncodedHex=function(){var t=[];if(null===this.dTbsRequest)throw"tbsRequest not set";if(t.push(this.dTbsRequest),null!==this.dOptionalSignature)throw"optionalSignature not supported";var e=new r({array:t});return this.hTLV=e.getEncodedHex(),this.hTLV},void 0!==t&&void 0!==t.reqList){var i=new n.TBSRequest(t);this.dTbsRequest=i}},i.lang.extend(ft.asn1.ocsp.OCSPRequest,ft.asn1.ASN1Object),ft.asn1.ocsp.OCSPUtil={},ft.asn1.ocsp.OCSPUtil.getRequestHex=function(t,e,r){var n=ft.asn1.ocsp;void 0===r&&(r=n.DEFAULT_HASH);var i={alg:r,issuerCert:t,subjectCert:e};return new n.OCSPRequest({reqList:[i]}).getEncodedHex()},ft.asn1.ocsp.OCSPUtil.getOCSPResponseInfo=function(t){var e=pt,r=e.getVbyList,n=e.getIdxbyList,i=(r=e.getVbyList,e.getV),s={};try{var a=r(t,0,[0],"0a");s.responseStatus=parseInt(a,16)}catch(t){}if(0!==s.responseStatus)return s;try{var o=n(t,0,[1,0,1,0,0,2,0,1]);"80"===t.substr(o,2)?s.certStatus="good":"a1"===t.substr(o,2)?(s.certStatus="revoked",s.revocationTime=Et(r(t,o,[0]))):"82"===t.substr(o,2)&&(s.certStatus="unknown")}catch(t){}try{var u=n(t,0,[1,0,1,0,0,2,0,2]);s.thisUpdate=Et(i(t,u))}catch(t){}try{var h=n(t,0,[1,0,1,0,0,2,0,3]);"a0"===t.substr(h,2)&&(s.nextUpdate=Et(r(t,h,[0])))}catch(t){}return s},void 0!==ft&&ft||(ft={}),void 0!==ft.lang&&ft.lang||(ft.lang={}),ft.lang.String=function(){},"function"==typeof t?(dt=function(e){return St(new t(e,"utf8").toString("base64"))},gt=function(e){return new t(bt(e),"base64").toString("utf8")}):(dt=function(t){return xt(Ot(_t(t)))},gt=function(t){return decodeURIComponent(kt(At(t)))}),ft.lang.String.isInteger=function(t){return!!t.match(/^[0-9]+$/)||!!t.match(/^-[0-9]+$/)},ft.lang.String.isHex=function(t){return!(t.length%2!=0||!t.match(/^[0-9a-f]+$/)&&!t.match(/^[0-9A-F]+$/))},ft.lang.String.isBase64=function(t){return!(!(t=t.replace(/\s+/g,"")).match(/^[0-9A-Za-z+\/]+={0,3}$/)||t.length%4!=0)},ft.lang.String.isBase64URL=function(t){return!t.match(/[+\/=]/)&&(t=bt(t),ft.lang.String.isBase64(t))},ft.lang.String.isIntegerArray=function(t){return!!(t=t.replace(/\s+/g,"")).match(/^\[[0-9,]+\]$/)};void 0!==ft&&ft||(ft={}),void 0!==ft.crypto&&ft.crypto||(ft.crypto={}),ft.crypto.Util=new function(){this.DIGESTINFOHEAD={sha1:"3021300906052b0e03021a05000414",sha224:"302d300d06096086480165030402040500041c",sha256:"3031300d060960864801650304020105000420",sha384:"3041300d060960864801650304020205000430",sha512:"3051300d060960864801650304020305000440",md2:"3020300c06082a864886f70d020205000410",md5:"3020300c06082a864886f70d020505000410",ripemd160:"3021300906052b2403020105000414"},this.DEFAULTPROVIDER={md5:"cryptojs",sha1:"cryptojs",sha224:"cryptojs",sha256:"cryptojs",sha384:"cryptojs",sha512:"cryptojs",ripemd160:"cryptojs",hmacmd5:"cryptojs",hmacsha1:"cryptojs",hmacsha224:"cryptojs",hmacsha256:"cryptojs",hmacsha384:"cryptojs",hmacsha512:"cryptojs",hmacripemd160:"cryptojs",MD5withRSA:"cryptojs/jsrsa",SHA1withRSA:"cryptojs/jsrsa",SHA224withRSA:"cryptojs/jsrsa",SHA256withRSA:"cryptojs/jsrsa",SHA384withRSA:"cryptojs/jsrsa",SHA512withRSA:"cryptojs/jsrsa",RIPEMD160withRSA:"cryptojs/jsrsa",MD5withECDSA:"cryptojs/jsrsa",SHA1withECDSA:"cryptojs/jsrsa",SHA224withECDSA:"cryptojs/jsrsa",SHA256withECDSA:"cryptojs/jsrsa",SHA384withECDSA:"cryptojs/jsrsa",SHA512withECDSA:"cryptojs/jsrsa",RIPEMD160withECDSA:"cryptojs/jsrsa",SHA1withDSA:"cryptojs/jsrsa",SHA224withDSA:"cryptojs/jsrsa",SHA256withDSA:"cryptojs/jsrsa",MD5withRSAandMGF1:"cryptojs/jsrsa",SHA1withRSAandMGF1:"cryptojs/jsrsa",SHA224withRSAandMGF1:"cryptojs/jsrsa",SHA256withRSAandMGF1:"cryptojs/jsrsa",SHA384withRSAandMGF1:"cryptojs/jsrsa",SHA512withRSAandMGF1:"cryptojs/jsrsa",RIPEMD160withRSAandMGF1:"cryptojs/jsrsa"},this.CRYPTOJSMESSAGEDIGESTNAME={md5:y.algo.MD5,sha1:y.algo.SHA1,sha224:y.algo.SHA224,sha256:y.algo.SHA256,sha384:y.algo.SHA384,sha512:y.algo.SHA512,ripemd160:y.algo.RIPEMD160},this.getDigestInfoHex=function(t,e){if(void 0===this.DIGESTINFOHEAD[e])throw"alg not supported in Util.DIGESTINFOHEAD: "+e;return this.DIGESTINFOHEAD[e]+t},this.getPaddedDigestInfoHex=function(t,e,r){var n=this.getDigestInfoHex(t,e),i=r/4;if(n.length+22>i)throw"key is too short for SigAlg: keylen="+r+","+e;for(var s="0001",a="00"+n,o="",u=i-s.length-a.length,h=0;h<u;h+=2)o+="ff";return s+o+a},this.hashString=function(t,e){return new ft.crypto.MessageDigest({alg:e}).digestString(t)},this.hashHex=function(t,e){return new ft.crypto.MessageDigest({alg:e}).digestHex(t)},this.sha1=function(t){return new ft.crypto.MessageDigest({alg:"sha1",prov:"cryptojs"}).digestString(t)},this.sha256=function(t){return new ft.crypto.MessageDigest({alg:"sha256",prov:"cryptojs"}).digestString(t)},this.sha256Hex=function(t){return new ft.crypto.MessageDigest({alg:"sha256",prov:"cryptojs"}).digestHex(t)},this.sha512=function(t){return new ft.crypto.MessageDigest({alg:"sha512",prov:"cryptojs"}).digestString(t)},this.sha512Hex=function(t){return new ft.crypto.MessageDigest({alg:"sha512",prov:"cryptojs"}).digestHex(t)}},ft.crypto.Util.md5=function(t){return new ft.crypto.MessageDigest({alg:"md5",prov:"cryptojs"}).digestString(t)},ft.crypto.Util.ripemd160=function(t){return new ft.crypto.MessageDigest({alg:"ripemd160",prov:"cryptojs"}).digestString(t)},ft.crypto.Util.SECURERANDOMGEN=new nt,ft.crypto.Util.getRandomHexOfNbytes=function(t){var e=new Array(t);return ft.crypto.Util.SECURERANDOMGEN.nextBytes(e),mt(e)},ft.crypto.Util.getRandomBigIntegerOfNbytes=function(t){return new A(ft.crypto.Util.getRandomHexOfNbytes(t),16)},ft.crypto.Util.getRandomHexOfNbits=function(t){var e=t%8,r=new Array((t-e)/8+1);return ft.crypto.Util.SECURERANDOMGEN.nextBytes(r),r[0]=(255<<e&255^255)&r[0],mt(r)},ft.crypto.Util.getRandomBigIntegerOfNbits=function(t){return new A(ft.crypto.Util.getRandomHexOfNbits(t),16)},ft.crypto.Util.getRandomBigIntegerZeroToMax=function(t){for(var e=t.bitLength();;){var r=ft.crypto.Util.getRandomBigIntegerOfNbits(e);if(-1!=t.compareTo(r))return r}},ft.crypto.Util.getRandomBigIntegerMinToMax=function(t,e){var r=t.compareTo(e);if(1==r)throw"biMin is greater than biMax";if(0==r)return t;var n=e.subtract(t);return ft.crypto.Util.getRandomBigIntegerZeroToMax(n).add(t)},ft.crypto.MessageDigest=function(t){this.setAlgAndProvider=function(t,e){if(null!==(t=ft.crypto.MessageDigest.getCanonicalAlgName(t))&&void 0===e&&(e=ft.crypto.Util.DEFAULTPROVIDER[t]),-1!=":md5:sha1:sha224:sha256:sha384:sha512:ripemd160:".indexOf(t)&&"cryptojs"==e){try{this.md=ft.crypto.Util.CRYPTOJSMESSAGEDIGESTNAME[t].create()}catch(e){throw"setAlgAndProvider hash alg set fail alg="+t+"/"+e}this.updateString=function(t){this.md.update(t)},this.updateHex=function(t){var e=y.enc.Hex.parse(t);this.md.update(e)},this.digest=function(){return this.md.finalize().toString(y.enc.Hex)},this.digestString=function(t){return this.updateString(t),this.digest()},this.digestHex=function(t){return this.updateHex(t),this.digest()}}if(-1!=":sha256:".indexOf(t)&&"sjcl"==e){try{this.md=new sjcl.hash.sha256}catch(e){throw"setAlgAndProvider hash alg set fail alg="+t+"/"+e}this.updateString=function(t){this.md.update(t)},this.updateHex=function(t){var e=sjcl.codec.hex.toBits(t);this.md.update(e)},this.digest=function(){var t=this.md.finalize();return sjcl.codec.hex.fromBits(t)},this.digestString=function(t){return this.updateString(t),this.digest()},this.digestHex=function(t){return this.updateHex(t),this.digest()}}},this.updateString=function(t){throw"updateString(str) not supported for this alg/prov: "+this.algName+"/"+this.provName},this.updateHex=function(t){throw"updateHex(hex) not supported for this alg/prov: "+this.algName+"/"+this.provName},this.digest=function(){throw"digest() not supported for this alg/prov: "+this.algName+"/"+this.provName},this.digestString=function(t){throw"digestString(str) not supported for this alg/prov: "+this.algName+"/"+this.provName},this.digestHex=function(t){throw"digestHex(hex) not supported for this alg/prov: "+this.algName+"/"+this.provName},void 0!==t&&void 0!==t.alg&&(this.algName=t.alg,void 0===t.prov&&(this.provName=ft.crypto.Util.DEFAULTPROVIDER[this.algName]),this.setAlgAndProvider(this.algName,this.provName))},ft.crypto.MessageDigest.getCanonicalAlgName=function(t){return"string"==typeof t&&(t=(t=t.toLowerCase()).replace(/-/,"")),t},ft.crypto.MessageDigest.getHashLength=function(t){var e=ft.crypto.MessageDigest,r=e.getCanonicalAlgName(t);if(void 0===e.HASHLENGTH[r])throw"not supported algorithm: "+t;return e.HASHLENGTH[r]},ft.crypto.MessageDigest.HASHLENGTH={md5:16,sha1:20,sha224:28,sha256:32,sha384:48,sha512:64,ripemd160:20},ft.crypto.Mac=function(t){this.setAlgAndProvider=function(t,e){if(null==(t=t.toLowerCase())&&(t="hmacsha1"),"hmac"!=(t=t.toLowerCase()).substr(0,4))throw"setAlgAndProvider unsupported HMAC alg: "+t;void 0===e&&(e=ft.crypto.Util.DEFAULTPROVIDER[t]),this.algProv=t+"/"+e;var r=t.substr(4);if(-1!=":md5:sha1:sha224:sha256:sha384:sha512:ripemd160:".indexOf(r)&&"cryptojs"==e){try{var n=ft.crypto.Util.CRYPTOJSMESSAGEDIGESTNAME[r];this.mac=y.algo.HMAC.create(n,this.pass)}catch(t){throw"setAlgAndProvider hash alg set fail hashAlg="+r+"/"+t}this.updateString=function(t){this.mac.update(t)},this.updateHex=function(t){var e=y.enc.Hex.parse(t);this.mac.update(e)},this.doFinal=function(){return this.mac.finalize().toString(y.enc.Hex)},this.doFinalString=function(t){return this.updateString(t),this.doFinal()},this.doFinalHex=function(t){return this.updateHex(t),this.doFinal()}}},this.updateString=function(t){throw"updateString(str) not supported for this alg/prov: "+this.algProv},this.updateHex=function(t){throw"updateHex(hex) not supported for this alg/prov: "+this.algProv},this.doFinal=function(){throw"digest() not supported for this alg/prov: "+this.algProv},this.doFinalString=function(t){throw"digestString(str) not supported for this alg/prov: "+this.algProv},this.doFinalHex=function(t){throw"digestHex(hex) not supported for this alg/prov: "+this.algProv},this.setPassword=function(t){if("string"==typeof t){var e=t;return t.length%2!=1&&t.match(/^[0-9A-Fa-f]+$/)||(e=Ct(t)),void(this.pass=y.enc.Hex.parse(e))}if("object"!=typeof t)throw"KJUR.crypto.Mac unsupported password type: "+t;e=null;if(void 0!==t.hex){if(t.hex.length%2!=0||!t.hex.match(/^[0-9A-Fa-f]+$/))throw"Mac: wrong hex password: "+t.hex;e=t.hex}if(void 0!==t.utf8&&(e=wt(t.utf8)),void 0!==t.rstr&&(e=Ct(t.rstr)),void 0!==t.b64&&(e=b(t.b64)),void 0!==t.b64u&&(e=At(t.b64u)),null==e)throw"KJUR.crypto.Mac unsupported password type: "+t;this.pass=y.enc.Hex.parse(e)},void 0!==t&&(void 0!==t.pass&&this.setPassword(t.pass),void 0!==t.alg&&(this.algName=t.alg,void 0===t.prov&&(this.provName=ft.crypto.Util.DEFAULTPROVIDER[this.algName]),this.setAlgAndProvider(this.algName,this.provName)))},ft.crypto.Signature=function(t){var e=null;if(this._setAlgNames=function(){var t=this.algName.match(/^(.+)with(.+)$/);t&&(this.mdAlgName=t[1].toLowerCase(),this.pubkeyAlgName=t[2].toLowerCase())},this._zeroPaddingOfSignature=function(t,e){for(var r="",n=e/4-t.length,i=0;i<n;i++)r+="0";return r+t},this.setAlgAndProvider=function(t,e){if(this._setAlgNames(),"cryptojs/jsrsa"!=e)throw"provider not supported: "+e;if(-1!=":md5:sha1:sha224:sha256:sha384:sha512:ripemd160:".indexOf(this.mdAlgName)){try{this.md=new ft.crypto.MessageDigest({alg:this.mdAlgName})}catch(t){throw"setAlgAndProvider hash alg set fail alg="+this.mdAlgName+"/"+t}this.init=function(t,e){var r=null;try{r=void 0===e?Kt.getKey(t):Kt.getKey(t,e)}catch(t){throw"init failed:"+t}if(!0===r.isPrivate)this.prvKey=r,this.state="SIGN";else{if(!0!==r.isPublic)throw"init failed.:"+r;this.pubKey=r,this.state="VERIFY"}},this.updateString=function(t){this.md.updateString(t)},this.updateHex=function(t){this.md.updateHex(t)},this.sign=function(){if(this.sHashHex=this.md.digest(),void 0!==this.ecprvhex&&void 0!==this.eccurvename){var t=new ft.crypto.ECDSA({curve:this.eccurvename});this.hSign=t.signHex(this.sHashHex,this.ecprvhex)}else if(this.prvKey instanceof at&&"rsaandmgf1"===this.pubkeyAlgName)this.hSign=this.prvKey.signWithMessageHashPSS(this.sHashHex,this.mdAlgName,this.pssSaltLen);else if(this.prvKey instanceof at&&"rsa"===this.pubkeyAlgName)this.hSign=this.prvKey.signWithMessageHash(this.sHashHex,this.mdAlgName);else if(this.prvKey instanceof ft.crypto.ECDSA)this.hSign=this.prvKey.signWithMessageHash(this.sHashHex);else{if(!(this.prvKey instanceof ft.crypto.DSA))throw"Signature: unsupported private key alg: "+this.pubkeyAlgName;this.hSign=this.prvKey.signWithMessageHash(this.sHashHex)}return this.hSign},this.signString=function(t){return this.updateString(t),this.sign()},this.signHex=function(t){return this.updateHex(t),this.sign()},this.verify=function(t){if(this.sHashHex=this.md.digest(),void 0!==this.ecpubhex&&void 0!==this.eccurvename)return new ft.crypto.ECDSA({curve:this.eccurvename}).verifyHex(this.sHashHex,t,this.ecpubhex);if(this.pubKey instanceof at&&"rsaandmgf1"===this.pubkeyAlgName)return this.pubKey.verifyWithMessageHashPSS(this.sHashHex,t,this.mdAlgName,this.pssSaltLen);if(this.pubKey instanceof at&&"rsa"===this.pubkeyAlgName)return this.pubKey.verifyWithMessageHash(this.sHashHex,t);if(void 0!==ft.crypto.ECDSA&&this.pubKey instanceof ft.crypto.ECDSA)return this.pubKey.verifyWithMessageHash(this.sHashHex,t);if(void 0!==ft.crypto.DSA&&this.pubKey instanceof ft.crypto.DSA)return this.pubKey.verifyWithMessageHash(this.sHashHex,t);throw"Signature: unsupported public key alg: "+this.pubkeyAlgName}}},this.init=function(t,e){throw"init(key, pass) not supported for this alg:prov="+this.algProvName},this.updateString=function(t){throw"updateString(str) not supported for this alg:prov="+this.algProvName},this.updateHex=function(t){throw"updateHex(hex) not supported for this alg:prov="+this.algProvName},this.sign=function(){throw"sign() not supported for this alg:prov="+this.algProvName},this.signString=function(t){throw"digestString(str) not supported for this alg:prov="+this.algProvName},this.signHex=function(t){throw"digestHex(hex) not supported for this alg:prov="+this.algProvName},this.verify=function(t){throw"verify(hSigVal) not supported for this alg:prov="+this.algProvName},this.initParams=t,void 0!==t&&(void 0!==t.alg&&(this.algName=t.alg,void 0===t.prov?this.provName=ft.crypto.Util.DEFAULTPROVIDER[this.algName]:this.provName=t.prov,this.algProvName=this.algName+":"+this.provName,this.setAlgAndProvider(this.algName,this.provName),this._setAlgNames()),void 0!==t.psssaltlen&&(this.pssSaltLen=t.psssaltlen),void 0!==t.prvkeypem)){if(void 0!==t.prvkeypas)throw"both prvkeypem and prvkeypas parameters not supported";try{e=Kt.getKey(t.prvkeypem);this.init(e)}catch(t){throw"fatal error to load pem private key: "+t}}},ft.crypto.Cipher=function(t){},ft.crypto.Cipher.encrypt=function(t,e,r){if(e instanceof at&&e.isPublic){var n=ft.crypto.Cipher.getAlgByKeyAndName(e,r);if("RSA"===n)return e.encrypt(t);if("RSAOAEP"===n)return e.encryptOAEP(t,"sha1");var i=n.match(/^RSAOAEP(\d+)$/);if(null!==i)return e.encryptOAEP(t,"sha"+i[1]);throw"Cipher.encrypt: unsupported algorithm for RSAKey: "+r}throw"Cipher.encrypt: unsupported key or algorithm"},ft.crypto.Cipher.decrypt=function(t,e,r){if(e instanceof at&&e.isPrivate){var n=ft.crypto.Cipher.getAlgByKeyAndName(e,r);if("RSA"===n)return e.decrypt(t);if("RSAOAEP"===n)return e.decryptOAEP(t,"sha1");var i=n.match(/^RSAOAEP(\d+)$/);if(null!==i)return e.decryptOAEP(t,"sha"+i[1]);throw"Cipher.decrypt: unsupported algorithm for RSAKey: "+r}throw"Cipher.decrypt: unsupported key or algorithm"},ft.crypto.Cipher.getAlgByKeyAndName=function(t,e){if(t instanceof at){if(-1!=":RSA:RSAOAEP:RSAOAEP224:RSAOAEP256:RSAOAEP384:RSAOAEP512:".indexOf(e))return e;if(null==e)return"RSA";throw"getAlgByKeyAndName: not supported algorithm name for RSAKey: "+e}throw"getAlgByKeyAndName: not supported algorithm name: "+e},ft.crypto.OID=new function(){this.oidhex2name={"2a864886f70d010101":"rsaEncryption","2a8648ce3d0201":"ecPublicKey","2a8648ce380401":"dsa","2a8648ce3d030107":"secp256r1","2b8104001f":"secp192k1","2b81040021":"secp224r1","2b8104000a":"secp256k1","2b81040023":"secp521r1","2b81040022":"secp384r1","2a8648ce380403":"SHA1withDSA","608648016503040301":"SHA224withDSA","608648016503040302":"SHA256withDSA"}},void 0!==ft&&ft||(ft={}),void 0!==ft.crypto&&ft.crypto||(ft.crypto={}),ft.crypto.ECDSA=function(t){var e=new nt;this.type="EC",this.isPrivate=!1,this.isPublic=!1,this.getBigRandom=function(t){return new A(t.bitLength(),e).mod(t.subtract(A.ONE)).add(A.ONE)},this.setNamedCurve=function(t){this.ecparams=ft.crypto.ECParameterDB.getByName(t),this.prvKeyHex=null,this.pubKeyHex=null,this.curveName=t},this.setPrivateKeyHex=function(t){this.isPrivate=!0,this.prvKeyHex=t},this.setPublicKeyHex=function(t){this.isPublic=!0,this.pubKeyHex=t},this.getPublicKeyXYHex=function(){var t=this.pubKeyHex;if("04"!==t.substr(0,2))throw"this method supports uncompressed format(04) only";var e=this.ecparams.keylen/4;if(t.length!==2+2*e)throw"malformed public key hex length";var r={};return r.x=t.substr(2,e),r.y=t.substr(2+e),r},this.getShortNISTPCurveName=function(){var t=this.curveName;return"secp256r1"===t||"NIST P-256"===t||"P-256"===t||"prime256v1"===t?"P-256":"secp384r1"===t||"NIST P-384"===t||"P-384"===t?"P-384":null},this.generateKeyPairHex=function(){var t=this.ecparams.n,e=this.getBigRandom(t),r=this.ecparams.G.multiply(e),n=r.getX().toBigInteger(),i=r.getY().toBigInteger(),s=this.ecparams.keylen/4,a=("0000000000"+e.toString(16)).slice(-s),o="04"+("0000000000"+n.toString(16)).slice(-s)+("0000000000"+i.toString(16)).slice(-s);return this.setPrivateKeyHex(a),this.setPublicKeyHex(o),{ecprvhex:a,ecpubhex:o}},this.signWithMessageHash=function(t){return this.signHex(t,this.prvKeyHex)},this.signHex=function(t,e){var r=new A(e,16),n=this.ecparams.n,i=new A(t,16);do{var s=this.getBigRandom(n),a=this.ecparams.G.multiply(s).getX().toBigInteger().mod(n)}while(a.compareTo(A.ZERO)<=0);var o=s.modInverse(n).multiply(i.add(r.multiply(a))).mod(n);return ft.crypto.ECDSA.biRSSigToASN1Sig(a,o)},this.sign=function(t,e){var r=e,n=this.ecparams.n,i=A.fromByteArrayUnsigned(t);do{var s=this.getBigRandom(n),a=this.ecparams.G.multiply(s).getX().toBigInteger().mod(n)}while(a.compareTo(A.ZERO)<=0);var o=s.modInverse(n).multiply(i.add(r.multiply(a))).mod(n);return this.serializeSig(a,o)},this.verifyWithMessageHash=function(t,e){return this.verifyHex(t,e,this.pubKeyHex)},this.verifyHex=function(t,e,r){var n,i,s,a=ft.crypto.ECDSA.parseSigHex(e);n=a.r,i=a.s,s=ht.decodeFromHex(this.ecparams.curve,r);var o=new A(t,16);return this.verifyRaw(o,n,i,s)},this.verify=function(t,e,r){var n,i,s;if(Bitcoin.Util.isArray(e)){var a=this.parseSig(e);n=a.r,i=a.s}else{if("object"!=typeof e||!e.r||!e.s)throw"Invalid value for signature";n=e.r,i=e.s}if(r instanceof ht)s=r;else{if(!Bitcoin.Util.isArray(r))throw"Invalid format for pubkey value, must be byte array or ECPointFp";s=ht.decodeFrom(this.ecparams.curve,r)}var o=A.fromByteArrayUnsigned(t);return this.verifyRaw(o,n,i,s)},this.verifyRaw=function(t,e,r,n){var i=this.ecparams.n,s=this.ecparams.G;if(e.compareTo(A.ONE)<0||e.compareTo(i)>=0)return!1;if(r.compareTo(A.ONE)<0||r.compareTo(i)>=0)return!1;var a=r.modInverse(i),o=t.multiply(a).mod(i),u=e.multiply(a).mod(i);return s.multiply(o).add(n.multiply(u)).getX().toBigInteger().mod(i).equals(e)},this.serializeSig=function(t,e){var r=t.toByteArraySigned(),n=e.toByteArraySigned(),i=[];return i.push(2),i.push(r.length),(i=i.concat(r)).push(2),i.push(n.length),(i=i.concat(n)).unshift(i.length),i.unshift(48),i},this.parseSig=function(t){var e;if(48!=t[0])throw new Error("Signature not a valid DERSequence");if(2!=t[e=2])throw new Error("First element in signature must be a DERInteger");var r=t.slice(e+2,e+2+t[e+1]);if(2!=t[e+=2+t[e+1]])throw new Error("Second element in signature must be a DERInteger");var n=t.slice(e+2,e+2+t[e+1]);return e+=2+t[e+1],{r:A.fromByteArrayUnsigned(r),s:A.fromByteArrayUnsigned(n)}},this.parseSigCompact=function(t){if(65!==t.length)throw"Signature has the wrong length";var e=t[0]-27;if(e<0||e>7)throw"Invalid signature type";var r=this.ecparams.n;return{r:A.fromByteArrayUnsigned(t.slice(1,33)).mod(r),s:A.fromByteArrayUnsigned(t.slice(33,65)).mod(r),i:e}},this.readPKCS5PrvKeyHex=function(t){var e,r,n,i=pt,s=ft.crypto.ECDSA.getName,a=i.getVbyList;if(!1===i.isASN1HEX(t))throw"not ASN.1 hex string";try{e=a(t,0,[2,0],"06"),r=a(t,0,[1],"04");try{n=a(t,0,[3,0],"03").substr(2)}catch(t){}}catch(t){throw"malformed PKCS#1/5 plain ECC private key"}if(this.curveName=s(e),void 0===this.curveName)throw"unsupported curve name";this.setNamedCurve(this.curveName),this.setPublicKeyHex(n),this.setPrivateKeyHex(r),this.isPublic=!1},this.readPKCS8PrvKeyHex=function(t){var e,r,n,i=pt,s=ft.crypto.ECDSA.getName,a=i.getVbyList;if(!1===i.isASN1HEX(t))throw"not ASN.1 hex string";try{a(t,0,[1,0],"06"),e=a(t,0,[1,1],"06"),r=a(t,0,[2,0,1],"04");try{n=a(t,0,[2,0,2,0],"03").substr(2)}catch(t){}}catch(t){throw"malformed PKCS#8 plain ECC private key"}if(this.curveName=s(e),void 0===this.curveName)throw"unsupported curve name";this.setNamedCurve(this.curveName),this.setPublicKeyHex(n),this.setPrivateKeyHex(r),this.isPublic=!1},this.readPKCS8PubKeyHex=function(t){var e,r,n=pt,i=ft.crypto.ECDSA.getName,s=n.getVbyList;if(!1===n.isASN1HEX(t))throw"not ASN.1 hex string";try{s(t,0,[0,0],"06"),e=s(t,0,[0,1],"06"),r=s(t,0,[1],"03").substr(2)}catch(t){throw"malformed PKCS#8 ECC public key"}if(this.curveName=i(e),null===this.curveName)throw"unsupported curve name";this.setNamedCurve(this.curveName),this.setPublicKeyHex(r)},this.readCertPubKeyHex=function(t,e){5!==e&&(e=6);var r,n,i=pt,s=ft.crypto.ECDSA.getName,a=i.getVbyList;if(!1===i.isASN1HEX(t))throw"not ASN.1 hex string";try{r=a(t,0,[0,e,0,1],"06"),n=a(t,0,[0,e,1],"03").substr(2)}catch(t){throw"malformed X.509 certificate ECC public key"}if(this.curveName=s(r),null===this.curveName)throw"unsupported curve name";this.setNamedCurve(this.curveName),this.setPublicKeyHex(n)},void 0!==t&&void 0!==t.curve&&(this.curveName=t.curve),void 0===this.curveName&&(this.curveName="secp256r1"),this.setNamedCurve(this.curveName),void 0!==t&&(void 0!==t.prv&&this.setPrivateKeyHex(t.prv),void 0!==t.pub&&this.setPublicKeyHex(t.pub))},ft.crypto.ECDSA.parseSigHex=function(t){var e=ft.crypto.ECDSA.parseSigHexInHexRS(t);return{r:new A(e.r,16),s:new A(e.s,16)}},ft.crypto.ECDSA.parseSigHexInHexRS=function(t){var e=pt,r=e.getChildIdx,n=e.getV;if("30"!=t.substr(0,2))throw"signature is not a ASN.1 sequence";var i=r(t,0);if(2!=i.length)throw"number of signature ASN.1 sequence elements seem wrong";var s=i[0],a=i[1];if("02"!=t.substr(s,2))throw"1st item of sequene of signature is not ASN.1 integer";if("02"!=t.substr(a,2))throw"2nd item of sequene of signature is not ASN.1 integer";return{r:n(t,s),s:n(t,a)}},ft.crypto.ECDSA.asn1SigToConcatSig=function(t){var e=ft.crypto.ECDSA.parseSigHexInHexRS(t),r=e.r,n=e.s;if("00"==r.substr(0,2)&&r.length%32==2&&(r=r.substr(2)),"00"==n.substr(0,2)&&n.length%32==2&&(n=n.substr(2)),r.length%32==30&&(r="00"+r),n.length%32==30&&(n="00"+n),r.length%32!=0)throw"unknown ECDSA sig r length error";if(n.length%32!=0)throw"unknown ECDSA sig s length error";return r+n},ft.crypto.ECDSA.concatSigToASN1Sig=function(t){if(t.length/2*8%128!=0)throw"unknown ECDSA concatinated r-s sig  length error";var e=t.substr(0,t.length/2),r=t.substr(t.length/2);return ft.crypto.ECDSA.hexRSSigToASN1Sig(e,r)},ft.crypto.ECDSA.hexRSSigToASN1Sig=function(t,e){var r=new A(t,16),n=new A(e,16);return ft.crypto.ECDSA.biRSSigToASN1Sig(r,n)},ft.crypto.ECDSA.biRSSigToASN1Sig=function(t,e){var r=ft.asn1,n=new r.DERInteger({bigint:t}),i=new r.DERInteger({bigint:e});return new r.DERSequence({array:[n,i]}).getEncodedHex()},ft.crypto.ECDSA.getName=function(t){return"2a8648ce3d030107"===t?"secp256r1":"2b8104000a"===t?"secp256k1":"2b81040022"===t?"secp384r1":-1!=="|secp256r1|NIST P-256|P-256|prime256v1|".indexOf(t)?"secp256r1":-1!=="|secp256k1|".indexOf(t)?"secp256k1":-1!=="|secp384r1|NIST P-384|P-384|".indexOf(t)?"secp384r1":null},void 0!==ft&&ft||(ft={}),void 0!==ft.crypto&&ft.crypto||(ft.crypto={}),ft.crypto.ECParameterDB=new function(){var t={},e={};function r(t){return new A(t,16)}this.getByName=function(r){var n=r;if(void 0!==e[n]&&(n=e[r]),void 0!==t[n])return t[n];throw"unregistered EC curve name: "+n},this.regist=function(n,i,s,a,o,u,h,c,l,f,d,g){t[n]={};var p=r(s),y=r(a),v=r(o),m=r(u),F=r(h),S=new ct(p,y,v),b=S.decodePointHex("04"+c+l);t[n].name=n,t[n].keylen=i,t[n].curve=S,t[n].G=b,t[n].n=m,t[n].h=F,t[n].oid=d,t[n].info=g;for(var x=0;x<f.length;x++)e[f[x]]=n}},ft.crypto.ECParameterDB.regist("secp128r1",128,"FFFFFFFDFFFFFFFFFFFFFFFFFFFFFFFF","FFFFFFFDFFFFFFFFFFFFFFFFFFFFFFFC","E87579C11079F43DD824993C2CEE5ED3","FFFFFFFE0000000075A30D1B9038A115","1","161FF7528B899B2D0C28607CA52C5B86","CF5AC8395BAFEB13C02DA292DDED7A83",[],"","secp128r1 : SECG curve over a 128 bit prime field"),ft.crypto.ECParameterDB.regist("secp160k1",160,"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFAC73","0","7","0100000000000000000001B8FA16DFAB9ACA16B6B3","1","3B4C382CE37AA192A4019E763036F4F5DD4D7EBB","938CF935318FDCED6BC28286531733C3F03C4FEE",[],"","secp160k1 : SECG curve over a 160 bit prime field"),ft.crypto.ECParameterDB.regist("secp160r1",160,"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF7FFFFFFF","FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF7FFFFFFC","****************************************","0100000000000000000001F4C8F927AED3CA752257","1","4A96B5688EF573284664698968C38BB913CBFC82","23A628553168947D59DCC912042351377AC5FB32",[],"","secp160r1 : SECG curve over a 160 bit prime field"),ft.crypto.ECParameterDB.regist("secp192k1",192,"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFEE37","0","3","FFFFFFFFFFFFFFFFFFFFFFFE26F2FC170F69466A74DEFD8D","1","DB4FF10EC057E9AE26B07D0280B7F4341DA5D1B1EAE06C7D","9B2F2F6D9C5628A7844163D015BE86344082AA88D95E2F9D",[]),ft.crypto.ECParameterDB.regist("secp192r1",192,"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFF","FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFC","64210519E59C80E70FA7E9AB72243049FEB8DEECC146B9B1","FFFFFFFFFFFFFFFFFFFFFFFF99DEF836146BC9B1B4D22831","1","188DA80EB03090F67CBF20EB43A18800F4FF0AFD82FF1012","07192B95FFC8DA78631011ED6B24CDD573F977A11E794811",[]),ft.crypto.ECParameterDB.regist("secp224r1",224,"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF000000000000000000000001","FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFE","B4050A850C04B3ABF54132565044B0B7D7BFD8BA270B39432355FFB4","FFFFFFFFFFFFFFFFFFFFFFFFFFFF16A2E0B8F03E13DD29455C5C2A3D","1","B70E0CBD6BB4BF7F321390B94A03C1D356C21122343280D6115C1D21","BD376388B5F723FB4C22DFE6CD4375A05A07476444D5819985007E34",[]),ft.crypto.ECParameterDB.regist("secp256k1",256,"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFC2F","0","7","FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141","1","79BE667EF9DCBBAC55A06295CE870B07029BFCDB2DCE28D959F2815B16F81798","483ADA7726A3C4655DA4FBFC0E1108A8FD17B448A68554199C47D08FFB10D4B8",[]),ft.crypto.ECParameterDB.regist("secp256r1",256,"FFFFFFFF00000001000000000000000000000000FFFFFFFFFFFFFFFFFFFFFFFF","FFFFFFFF00000001000000000000000000000000FFFFFFFFFFFFFFFFFFFFFFFC","5AC635D8AA3A93E7B3EBBD55769886BC651D06B0CC53B0F63BCE3C3E27D2604B","FFFFFFFF00000000FFFFFFFFFFFFFFFFBCE6FAADA7179E84F3B9CAC2FC632551","1","6B17D1F2E12C4247F8BCE6E563A440F277037D812DEB33A0F4A13945D898C296","4FE342E2FE1A7F9B8EE7EB4A7C0F9E162BCE33576B315ECECBB6406837BF51F5",["NIST P-256","P-256","prime256v1"]),ft.crypto.ECParameterDB.regist("secp384r1",384,"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFF0000000000000000FFFFFFFF","FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFF0000000000000000FFFFFFFC","B3312FA7E23EE7E4988E056BE3F82D19181D9C6EFE8141120314088F5013875AC656398D8A2ED19D2A85C8EDD3EC2AEF","FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFC7634D81F4372DDF581A0DB248B0A77AECEC196ACCC52973","1","AA87CA22BE8B05378EB1C71EF320AD746E1D3B628BA79B9859F741E082542A385502F25DBF55296C3A545E3872760AB7","3617de4a96262c6f5d9e98bf9292dc29f8f41dbd289a147ce9da3113b5f0b8c00a60b1ce1d7e819d7a431d7c90ea0e5f",["NIST P-384","P-384"]),ft.crypto.ECParameterDB.regist("secp521r1",521,"1FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF","1FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFC","051953EB9618E1C9A1F929A21A0B68540EEA2DA725B99B315F3B8B489918EF109E156193951EC7E937B1652C0BD3BB1BF073573DF883D2C34F1EF451FD46B503F00","1FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA51868783BF2F966B7FCC0148F709A5D03BB5C9B8899C47AEBB6FB71E91386409","1","C6858E06B70404E9CD9E3ECB662395B4429C648139053FB521F828AF606B4D3DBAA14B5E77EFE75928FE1DC127A2FFA8DE3348B3C1856A429BF97E7E31C2E5BD66","011839296a789a3bc0045c8a5fb42c7d1bd998f54449579b446817afbd17273e662c97ee72995ef42640c550b9013fad0761353c7086a272c24088be94769fd16650",["NIST P-521","P-521"]),void 0!==ft&&ft||(ft={}),void 0!==ft.crypto&&ft.crypto||(ft.crypto={}),ft.crypto.DSA=function(){this.p=null,this.q=null,this.g=null,this.y=null,this.x=null,this.type="DSA",this.isPrivate=!1,this.isPublic=!1,this.setPrivate=function(t,e,r,n,i){this.isPrivate=!0,this.p=t,this.q=e,this.g=r,this.y=n,this.x=i},this.setPrivateHex=function(t,e,r,n,i){var s,a,o,u,h;s=new A(t,16),a=new A(e,16),o=new A(r,16),u="string"==typeof n&&n.length>1?new A(n,16):null,h=new A(i,16),this.setPrivate(s,a,o,u,h)},this.setPublic=function(t,e,r,n){this.isPublic=!0,this.p=t,this.q=e,this.g=r,this.y=n,this.x=null},this.setPublicHex=function(t,e,r,n){var i,s,a,o;i=new A(t,16),s=new A(e,16),a=new A(r,16),o=new A(n,16),this.setPublic(i,s,a,o)},this.signWithMessageHash=function(t){var e=this.p,r=this.q,n=this.g,i=(this.y,this.x),s=ft.crypto.Util.getRandomBigIntegerMinToMax(A.ONE.add(A.ONE),r.subtract(A.ONE)),a=new A(t.substr(0,r.bitLength()/4),16),o=n.modPow(s,e).mod(r),u=s.modInverse(r).multiply(a.add(i.multiply(o))).mod(r);return ft.asn1.ASN1Util.jsonToASN1HEX({seq:[{int:{bigint:o}},{int:{bigint:u}}]})},this.verifyWithMessageHash=function(t,e){var r=this.p,n=this.q,i=this.g,s=this.y,a=this.parseASN1Signature(e),o=a[0],u=a[1],h=new A(t.substr(0,n.bitLength()/4),16);if(A.ZERO.compareTo(o)>0||o.compareTo(n)>0)throw"invalid DSA signature";if(A.ZERO.compareTo(u)>=0||u.compareTo(n)>0)throw"invalid DSA signature";var c=u.modInverse(n),l=h.multiply(c).mod(n),f=o.multiply(c).mod(n);return 0==i.modPow(l,r).multiply(s.modPow(f,r)).mod(r).mod(n).compareTo(o)},this.parseASN1Signature=function(t){try{return[new A(pt.getVbyList(t,0,[0],"02"),16),new A(pt.getVbyList(t,0,[1],"02"),16)]}catch(t){throw"malformed ASN.1 DSA signature"}},this.readPKCS5PrvKeyHex=function(t){var e,r,n,i,s,a=pt,o=a.getVbyList;if(!1===a.isASN1HEX(t))throw"not ASN.1 hex string";try{e=o(t,0,[1],"02"),r=o(t,0,[2],"02"),n=o(t,0,[3],"02"),i=o(t,0,[4],"02"),s=o(t,0,[5],"02")}catch(t){throw console.log("EXCEPTION:"+t),"malformed PKCS#1/5 plain DSA private key"}this.setPrivateHex(e,r,n,i,s)},this.readPKCS8PrvKeyHex=function(t){var e,r,n,i,s=pt,a=s.getVbyList;if(!1===s.isASN1HEX(t))throw"not ASN.1 hex string";try{e=a(t,0,[1,1,0],"02"),r=a(t,0,[1,1,1],"02"),n=a(t,0,[1,1,2],"02"),i=a(t,0,[2,0],"02")}catch(t){throw console.log("EXCEPTION:"+t),"malformed PKCS#8 plain DSA private key"}this.setPrivateHex(e,r,n,null,i)},this.readPKCS8PubKeyHex=function(t){var e,r,n,i,s=pt,a=s.getVbyList;if(!1===s.isASN1HEX(t))throw"not ASN.1 hex string";try{e=a(t,0,[0,1,0],"02"),r=a(t,0,[0,1,1],"02"),n=a(t,0,[0,1,2],"02"),i=a(t,0,[1,0],"02")}catch(t){throw console.log("EXCEPTION:"+t),"malformed PKCS#8 DSA public key"}this.setPublicHex(e,r,n,i)},this.readCertPubKeyHex=function(t,e){var r,n,i,s;5!==e&&(e=6);var a=pt,o=a.getVbyList;if(!1===a.isASN1HEX(t))throw"not ASN.1 hex string";try{r=o(t,0,[0,e,0,1,0],"02"),n=o(t,0,[0,e,0,1,1],"02"),i=o(t,0,[0,e,0,1,2],"02"),s=o(t,0,[0,e,1,0],"02")}catch(t){throw console.log("EXCEPTION:"+t),"malformed X.509 certificate DSA public key"}this.setPublicHex(r,n,i,s)}};var Kt=function(){var t=function(t,r,n){return e(y.AES,t,r,n)},e=function(t,e,r,n){var i=y.enc.Hex.parse(e),s=y.enc.Hex.parse(r),a=y.enc.Hex.parse(n),o={};o.key=s,o.iv=a,o.ciphertext=i;var u=t.decrypt(o,s,{iv:a});return y.enc.Hex.stringify(u)},r=function(t,e,r){return n(y.AES,t,e,r)},n=function(t,e,r,n){var i=y.enc.Hex.parse(e),s=y.enc.Hex.parse(r),a=y.enc.Hex.parse(n),o=t.encrypt(i,s,{iv:a}),u=y.enc.Hex.parse(o.toString());return y.enc.Base64.stringify(u)},i={"AES-256-CBC":{proc:t,eproc:r,keylen:32,ivlen:16},"AES-192-CBC":{proc:t,eproc:r,keylen:24,ivlen:16},"AES-128-CBC":{proc:t,eproc:r,keylen:16,ivlen:16},"DES-EDE3-CBC":{proc:function(t,r,n){return e(y.TripleDES,t,r,n)},eproc:function(t,e,r){return n(y.TripleDES,t,e,r)},keylen:24,ivlen:8},"DES-CBC":{proc:function(t,r,n){return e(y.DES,t,r,n)},eproc:function(t,e,r){return n(y.DES,t,e,r)},keylen:8,ivlen:8}},s=function(t){var e={},r=t.match(new RegExp("DEK-Info: ([^,]+),([0-9A-Fa-f]+)","m"));r&&(e.cipher=r[1],e.ivsalt=r[2]);var n=t.match(new RegExp("-----BEGIN ([A-Z]+) PRIVATE KEY-----"));n&&(e.type=n[1]);var i=-1,s=0;-1!=t.indexOf("\r\n\r\n")&&(i=t.indexOf("\r\n\r\n"),s=2),-1!=t.indexOf("\n\n")&&(i=t.indexOf("\n\n"),s=1);var a=t.indexOf("-----END");if(-1!=i&&-1!=a){var o=t.substring(i+2*s,a-s);o=o.replace(/\s+/g,""),e.data=o}return e},a=function(t,e,r){for(var n=r.substring(0,16),s=y.enc.Hex.parse(n),a=y.enc.Utf8.parse(e),o=i[t].keylen+i[t].ivlen,u="",h=null;;){var c=y.algo.MD5.create();if(null!=h&&c.update(h),c.update(a),c.update(s),h=c.finalize(),(u+=y.enc.Hex.stringify(h)).length>=2*o)break}var l={};return l.keyhex=u.substr(0,2*i[t].keylen),l.ivhex=u.substr(2*i[t].keylen,2*i[t].ivlen),l},o=function(t,e,r,n){var s=y.enc.Base64.parse(t),a=y.enc.Hex.stringify(s);return(0,i[e].proc)(a,r,n)};return{version:"1.0.0",parsePKCS5PEM:function(t){return s(t)},getKeyAndUnusedIvByPasscodeAndIvsalt:function(t,e,r){return a(t,e,r)},decryptKeyB64:function(t,e,r,n){return o(t,e,r,n)},getDecryptedKeyHex:function(t,e){var r=s(t),n=(r.type,r.cipher),i=r.ivsalt,u=r.data,h=a(n,e,i).keyhex;return o(u,n,h,i)},getEncryptedPKCS5PEMFromPrvKeyHex:function(t,e,r,n,s){var o="";if(void 0!==n&&null!=n||(n="AES-256-CBC"),void 0===i[n])throw"KEYUTIL unsupported algorithm: "+n;void 0!==s&&null!=s||(s=function(t){var e=y.lib.WordArray.random(t);return y.enc.Hex.stringify(e)}(i[n].ivlen).toUpperCase());var u=function(t,e,r,n){return(0,i[e].eproc)(t,r,n)}(e,n,a(n,r,s).keyhex,s);o="-----BEGIN "+t+" PRIVATE KEY-----\r\n";return o+="Proc-Type: 4,ENCRYPTED\r\n",o+="DEK-Info: "+n+","+s+"\r\n",o+="\r\n",o+=u.replace(/(.{64})/g,"$1\r\n"),o+="\r\n-----END "+t+" PRIVATE KEY-----\r\n"},parseHexOfEncryptedPKCS8:function(t){var e=pt,r=e.getChildIdx,n=e.getV,i={},s=r(t,0);if(2!=s.length)throw"malformed format: SEQUENCE(0).items != 2: "+s.length;i.ciphertext=n(t,s[1]);var a=r(t,s[0]);if(2!=a.length)throw"malformed format: SEQUENCE(0.0).items != 2: "+a.length;if("2a864886f70d01050d"!=n(t,a[0]))throw"this only supports pkcs5PBES2";var o=r(t,a[1]);if(2!=a.length)throw"malformed format: SEQUENCE(0.0.1).items != 2: "+o.length;var u=r(t,o[1]);if(2!=u.length)throw"malformed format: SEQUENCE(*******).items != 2: "+u.length;if("2a864886f70d0307"!=n(t,u[0]))throw"this only supports TripleDES";i.encryptionSchemeAlg="TripleDES",i.encryptionSchemeIV=n(t,u[1]);var h=r(t,o[0]);if(2!=h.length)throw"malformed format: SEQUENCE(*******).items != 2: "+h.length;if("2a864886f70d01050c"!=n(t,h[0]))throw"this only supports pkcs5PBKDF2";var c=r(t,h[1]);if(c.length<2)throw"malformed format: SEQUENCE(*******.1).items < 2: "+c.length;i.pbkdf2Salt=n(t,c[0]);var l=n(t,c[1]);try{i.pbkdf2Iter=parseInt(l,16)}catch(t){throw"malformed format pbkdf2Iter: "+l}return i},getPBKDF2KeyHexFromParam:function(t,e){var r=y.enc.Hex.parse(t.pbkdf2Salt),n=t.pbkdf2Iter,i=y.PBKDF2(e,r,{keySize:6,iterations:n});return y.enc.Hex.stringify(i)},_getPlainPKCS8HexFromEncryptedPKCS8PEM:function(t,e){var r=Rt(t,"ENCRYPTED PRIVATE KEY"),n=this.parseHexOfEncryptedPKCS8(r),i=Kt.getPBKDF2KeyHexFromParam(n,e),s={};s.ciphertext=y.enc.Hex.parse(n.ciphertext);var a=y.enc.Hex.parse(i),o=y.enc.Hex.parse(n.encryptionSchemeIV),u=y.TripleDES.decrypt(s,a,{iv:o});return y.enc.Hex.stringify(u)},getKeyFromEncryptedPKCS8PEM:function(t,e){var r=this._getPlainPKCS8HexFromEncryptedPKCS8PEM(t,e);return this.getKeyFromPlainPrivatePKCS8Hex(r)},parsePlainPrivatePKCS8Hex:function(t){var e=pt,r=e.getChildIdx,n=e.getV,i={algparam:null};if("30"!=t.substr(0,2))throw"malformed plain PKCS8 private key(code:001)";var s=r(t,0);if(3!=s.length)throw"malformed plain PKCS8 private key(code:002)";if("30"!=t.substr(s[1],2))throw"malformed PKCS8 private key(code:003)";var a=r(t,s[1]);if(2!=a.length)throw"malformed PKCS8 private key(code:004)";if("06"!=t.substr(a[0],2))throw"malformed PKCS8 private key(code:005)";if(i.algoid=n(t,a[0]),"06"==t.substr(a[1],2)&&(i.algparam=n(t,a[1])),"04"!=t.substr(s[2],2))throw"malformed PKCS8 private key(code:006)";return i.keyidx=e.getVidx(t,s[2]),i},getKeyFromPlainPrivatePKCS8PEM:function(t){var e=Rt(t,"PRIVATE KEY");return this.getKeyFromPlainPrivatePKCS8Hex(e)},getKeyFromPlainPrivatePKCS8Hex:function(t){var e,r=this.parsePlainPrivatePKCS8Hex(t);if("2a864886f70d010101"==r.algoid)e=new at;else if("2a8648ce380401"==r.algoid)e=new ft.crypto.DSA;else{if("2a8648ce3d0201"!=r.algoid)throw"unsupported private key algorithm";e=new ft.crypto.ECDSA}return e.readPKCS8PrvKeyHex(t),e},_getKeyFromPublicPKCS8Hex:function(t){var e,r=pt.getVbyList(t,0,[0,0],"06");if("2a864886f70d010101"===r)e=new at;else if("2a8648ce380401"===r)e=new ft.crypto.DSA;else{if("2a8648ce3d0201"!==r)throw"unsupported PKCS#8 public key hex";e=new ft.crypto.ECDSA}return e.readPKCS8PubKeyHex(t),e},parsePublicRawRSAKeyHex:function(t){var e=pt,r=e.getChildIdx,n=e.getV,i={};if("30"!=t.substr(0,2))throw"malformed RSA key(code:001)";var s=r(t,0);if(2!=s.length)throw"malformed RSA key(code:002)";if("02"!=t.substr(s[0],2))throw"malformed RSA key(code:003)";if(i.n=n(t,s[0]),"02"!=t.substr(s[1],2))throw"malformed RSA key(code:004)";return i.e=n(t,s[1]),i},parsePublicPKCS8Hex:function(t){var e=pt,r=e.getChildIdx,n=e.getV,i={algparam:null},s=r(t,0);if(2!=s.length)throw"outer DERSequence shall have 2 elements: "+s.length;var a=s[0];if("30"!=t.substr(a,2))throw"malformed PKCS8 public key(code:001)";var o=r(t,a);if(2!=o.length)throw"malformed PKCS8 public key(code:002)";if("06"!=t.substr(o[0],2))throw"malformed PKCS8 public key(code:003)";if(i.algoid=n(t,o[0]),"06"==t.substr(o[1],2)?i.algparam=n(t,o[1]):"30"==t.substr(o[1],2)&&(i.algparam={},i.algparam.p=e.getVbyList(t,o[1],[0],"02"),i.algparam.q=e.getVbyList(t,o[1],[1],"02"),i.algparam.g=e.getVbyList(t,o[1],[2],"02")),"03"!=t.substr(s[1],2))throw"malformed PKCS8 public key(code:004)";return i.key=n(t,s[1]).substr(2),i}}}();Kt.getKey=function(t,e,r){var n=(y=pt).getChildIdx,i=(y.getV,y.getVbyList),s=ft.crypto,a=s.ECDSA,o=s.DSA,u=at,h=Rt,c=Kt;if(void 0!==u&&t instanceof u)return t;if(void 0!==a&&t instanceof a)return t;if(void 0!==o&&t instanceof o)return t;if(void 0!==t.curve&&void 0!==t.xy&&void 0===t.d)return new a({pub:t.xy,curve:t.curve});if(void 0!==t.curve&&void 0!==t.d)return new a({prv:t.d,curve:t.curve});if(void 0===t.kty&&void 0!==t.n&&void 0!==t.e&&void 0===t.d)return(D=new u).setPublic(t.n,t.e),D;if(void 0===t.kty&&void 0!==t.n&&void 0!==t.e&&void 0!==t.d&&void 0!==t.p&&void 0!==t.q&&void 0!==t.dp&&void 0!==t.dq&&void 0!==t.co&&void 0===t.qi)return(D=new u).setPrivateEx(t.n,t.e,t.d,t.p,t.q,t.dp,t.dq,t.co),D;if(void 0===t.kty&&void 0!==t.n&&void 0!==t.e&&void 0!==t.d&&void 0===t.p)return(D=new u).setPrivate(t.n,t.e,t.d),D;if(void 0!==t.p&&void 0!==t.q&&void 0!==t.g&&void 0!==t.y&&void 0===t.x)return(D=new o).setPublic(t.p,t.q,t.g,t.y),D;if(void 0!==t.p&&void 0!==t.q&&void 0!==t.g&&void 0!==t.y&&void 0!==t.x)return(D=new o).setPrivate(t.p,t.q,t.g,t.y,t.x),D;if("RSA"===t.kty&&void 0!==t.n&&void 0!==t.e&&void 0===t.d)return(D=new u).setPublic(At(t.n),At(t.e)),D;if("RSA"===t.kty&&void 0!==t.n&&void 0!==t.e&&void 0!==t.d&&void 0!==t.p&&void 0!==t.q&&void 0!==t.dp&&void 0!==t.dq&&void 0!==t.qi)return(D=new u).setPrivateEx(At(t.n),At(t.e),At(t.d),At(t.p),At(t.q),At(t.dp),At(t.dq),At(t.qi)),D;if("RSA"===t.kty&&void 0!==t.n&&void 0!==t.e&&void 0!==t.d)return(D=new u).setPrivate(At(t.n),At(t.e),At(t.d)),D;if("EC"===t.kty&&void 0!==t.crv&&void 0!==t.x&&void 0!==t.y&&void 0===t.d){var l=(C=new a({curve:t.crv})).ecparams.keylen/4,f="04"+("0000000000"+At(t.x)).slice(-l)+("0000000000"+At(t.y)).slice(-l);return C.setPublicKeyHex(f),C}if("EC"===t.kty&&void 0!==t.crv&&void 0!==t.x&&void 0!==t.y&&void 0!==t.d){l=(C=new a({curve:t.crv})).ecparams.keylen/4,f="04"+("0000000000"+At(t.x)).slice(-l)+("0000000000"+At(t.y)).slice(-l);var d=("0000000000"+At(t.d)).slice(-l);return C.setPublicKeyHex(f),C.setPrivateKeyHex(d),C}if("pkcs5prv"===r){var g,p=t,y=pt;if(9===(g=n(p,0)).length)(D=new u).readPKCS5PrvKeyHex(p);else if(6===g.length)(D=new o).readPKCS5PrvKeyHex(p);else{if(!(g.length>2&&"04"===p.substr(g[1],2)))throw"unsupported PKCS#1/5 hexadecimal key";(D=new a).readPKCS5PrvKeyHex(p)}return D}if("pkcs8prv"===r)return D=c.getKeyFromPlainPrivatePKCS8Hex(t);if("pkcs8pub"===r)return c._getKeyFromPublicPKCS8Hex(t);if("x509pub"===r)return $t.getPublicKeyFromCertHex(t);if(-1!=t.indexOf("-END CERTIFICATE-",0)||-1!=t.indexOf("-END X509 CERTIFICATE-",0)||-1!=t.indexOf("-END TRUSTED CERTIFICATE-",0))return $t.getPublicKeyFromCertPEM(t);if(-1!=t.indexOf("-END PUBLIC KEY-")){var v=Rt(t,"PUBLIC KEY");return c._getKeyFromPublicPKCS8Hex(v)}if(-1!=t.indexOf("-END RSA PRIVATE KEY-")&&-1==t.indexOf("4,ENCRYPTED")){var m=h(t,"RSA PRIVATE KEY");return c.getKey(m,null,"pkcs5prv")}if(-1!=t.indexOf("-END DSA PRIVATE KEY-")&&-1==t.indexOf("4,ENCRYPTED")){var F=i(P=h(t,"DSA PRIVATE KEY"),0,[1],"02"),S=i(P,0,[2],"02"),b=i(P,0,[3],"02"),x=i(P,0,[4],"02"),w=i(P,0,[5],"02");return(D=new o).setPrivate(new A(F,16),new A(S,16),new A(b,16),new A(x,16),new A(w,16)),D}if(-1!=t.indexOf("-END PRIVATE KEY-"))return c.getKeyFromPlainPrivatePKCS8PEM(t);if(-1!=t.indexOf("-END RSA PRIVATE KEY-")&&-1!=t.indexOf("4,ENCRYPTED")){var E=c.getDecryptedKeyHex(t,e),B=new at;return B.readPKCS5PrvKeyHex(E),B}if(-1!=t.indexOf("-END EC PRIVATE KEY-")&&-1!=t.indexOf("4,ENCRYPTED")){var C,D=i(P=c.getDecryptedKeyHex(t,e),0,[1],"04"),T=i(P,0,[2,0],"06"),I=i(P,0,[3,0],"03").substr(2);if(void 0===ft.crypto.OID.oidhex2name[T])throw"undefined OID(hex) in KJUR.crypto.OID: "+T;return(C=new a({curve:ft.crypto.OID.oidhex2name[T]})).setPublicKeyHex(I),C.setPrivateKeyHex(D),C.isPublic=!1,C}if(-1!=t.indexOf("-END DSA PRIVATE KEY-")&&-1!=t.indexOf("4,ENCRYPTED")){var P;F=i(P=c.getDecryptedKeyHex(t,e),0,[1],"02"),S=i(P,0,[2],"02"),b=i(P,0,[3],"02"),x=i(P,0,[4],"02"),w=i(P,0,[5],"02");return(D=new o).setPrivate(new A(F,16),new A(S,16),new A(b,16),new A(x,16),new A(w,16)),D}if(-1!=t.indexOf("-END ENCRYPTED PRIVATE KEY-"))return c.getKeyFromEncryptedPKCS8PEM(t,e);throw"not supported argument"},Kt.generateKeypair=function(t,e){if("RSA"==t){var r=e;(a=new at).generate(r,"10001"),a.isPrivate=!0,a.isPublic=!0;var n=new at,i=a.n.toString(16),s=a.e.toString(16);return n.setPublic(i,s),n.isPrivate=!1,n.isPublic=!0,(o={}).prvKeyObj=a,o.pubKeyObj=n,o}if("EC"==t){var a,o,u=e,h=new ft.crypto.ECDSA({curve:u}).generateKeyPairHex();return(a=new ft.crypto.ECDSA({curve:u})).setPublicKeyHex(h.ecpubhex),a.setPrivateKeyHex(h.ecprvhex),a.isPrivate=!0,a.isPublic=!1,(n=new ft.crypto.ECDSA({curve:u})).setPublicKeyHex(h.ecpubhex),n.isPrivate=!1,n.isPublic=!0,(o={}).prvKeyObj=a,o.pubKeyObj=n,o}throw"unknown algorithm: "+t},Kt.getPEM=function(t,e,r,n,i,s){var a=ft,o=a.asn1,u=o.DERObjectIdentifier,h=o.DERInteger,c=o.ASN1Util.newObject,l=o.x509.SubjectPublicKeyInfo,f=a.crypto,d=f.DSA,g=f.ECDSA,p=at;function v(t){return c({seq:[{int:0},{int:{bigint:t.n}},{int:t.e},{int:{bigint:t.d}},{int:{bigint:t.p}},{int:{bigint:t.q}},{int:{bigint:t.dmp1}},{int:{bigint:t.dmq1}},{int:{bigint:t.coeff}}]})}function m(t){return c({seq:[{int:1},{octstr:{hex:t.prvKeyHex}},{tag:["a0",!0,{oid:{name:t.curveName}}]},{tag:["a1",!0,{bitstr:{hex:"00"+t.pubKeyHex}}]}]})}function F(t){return c({seq:[{int:0},{int:{bigint:t.p}},{int:{bigint:t.q}},{int:{bigint:t.g}},{int:{bigint:t.y}},{int:{bigint:t.x}}]})}if((void 0!==p&&t instanceof p||void 0!==d&&t instanceof d||void 0!==g&&t instanceof g)&&1==t.isPublic&&(void 0===e||"PKCS8PUB"==e))return Pt(A=new l(t).getEncodedHex(),"PUBLIC KEY");if("PKCS1PRV"==e&&void 0!==p&&t instanceof p&&(void 0===r||null==r)&&1==t.isPrivate)return Pt(A=v(t).getEncodedHex(),"RSA PRIVATE KEY");if("PKCS1PRV"==e&&void 0!==g&&t instanceof g&&(void 0===r||null==r)&&1==t.isPrivate){var S=new u({name:t.curveName}).getEncodedHex(),b=m(t).getEncodedHex(),x="";return x+=Pt(S,"EC PARAMETERS"),x+=Pt(b,"EC PRIVATE KEY")}if("PKCS1PRV"==e&&void 0!==d&&t instanceof d&&(void 0===r||null==r)&&1==t.isPrivate)return Pt(A=F(t).getEncodedHex(),"DSA PRIVATE KEY");if("PKCS5PRV"==e&&void 0!==p&&t instanceof p&&void 0!==r&&null!=r&&1==t.isPrivate){var A=v(t).getEncodedHex();return void 0===n&&(n="DES-EDE3-CBC"),this.getEncryptedPKCS5PEMFromPrvKeyHex("RSA",A,r,n,s)}if("PKCS5PRV"==e&&void 0!==g&&t instanceof g&&void 0!==r&&null!=r&&1==t.isPrivate){A=m(t).getEncodedHex();return void 0===n&&(n="DES-EDE3-CBC"),this.getEncryptedPKCS5PEMFromPrvKeyHex("EC",A,r,n,s)}if("PKCS5PRV"==e&&void 0!==d&&t instanceof d&&void 0!==r&&null!=r&&1==t.isPrivate){A=F(t).getEncodedHex();return void 0===n&&(n="DES-EDE3-CBC"),this.getEncryptedPKCS5PEMFromPrvKeyHex("DSA",A,r,n,s)}var w=function(t,e){var r=E(t,e);return new c({seq:[{seq:[{oid:{name:"pkcs5PBES2"}},{seq:[{seq:[{oid:{name:"pkcs5PBKDF2"}},{seq:[{octstr:{hex:r.pbkdf2Salt}},{int:r.pbkdf2Iter}]}]},{seq:[{oid:{name:"des-EDE3-CBC"}},{octstr:{hex:r.encryptionSchemeIV}}]}]}]},{octstr:{hex:r.ciphertext}}]}).getEncodedHex()},E=function(t,e){var r=y.lib.WordArray.random(8),n=y.lib.WordArray.random(8),i=y.PBKDF2(e,r,{keySize:6,iterations:100}),s=y.enc.Hex.parse(t),a=y.TripleDES.encrypt(s,i,{iv:n})+"",o={};return o.ciphertext=a,o.pbkdf2Salt=y.enc.Hex.stringify(r),o.pbkdf2Iter=100,o.encryptionSchemeAlg="DES-EDE3-CBC",o.encryptionSchemeIV=y.enc.Hex.stringify(n),o};if("PKCS8PRV"==e&&null!=p&&t instanceof p&&1==t.isPrivate){var B=v(t).getEncodedHex();A=c({seq:[{int:0},{seq:[{oid:{name:"rsaEncryption"}},{null:!0}]},{octstr:{hex:B}}]}).getEncodedHex();return void 0===r||null==r?Pt(A,"PRIVATE KEY"):Pt(b=w(A,r),"ENCRYPTED PRIVATE KEY")}if("PKCS8PRV"==e&&void 0!==g&&t instanceof g&&1==t.isPrivate){B=new c({seq:[{int:1},{octstr:{hex:t.prvKeyHex}},{tag:["a1",!0,{bitstr:{hex:"00"+t.pubKeyHex}}]}]}).getEncodedHex(),A=c({seq:[{int:0},{seq:[{oid:{name:"ecPublicKey"}},{oid:{name:t.curveName}}]},{octstr:{hex:B}}]}).getEncodedHex();return void 0===r||null==r?Pt(A,"PRIVATE KEY"):Pt(b=w(A,r),"ENCRYPTED PRIVATE KEY")}if("PKCS8PRV"==e&&void 0!==d&&t instanceof d&&1==t.isPrivate){B=new h({bigint:t.x}).getEncodedHex(),A=c({seq:[{int:0},{seq:[{oid:{name:"dsa"}},{seq:[{int:{bigint:t.p}},{int:{bigint:t.q}},{int:{bigint:t.g}}]}]},{octstr:{hex:B}}]}).getEncodedHex();return void 0===r||null==r?Pt(A,"PRIVATE KEY"):Pt(b=w(A,r),"ENCRYPTED PRIVATE KEY")}throw"unsupported object nor format"},Kt.getKeyFromCSRPEM=function(t){var e=Rt(t,"CERTIFICATE REQUEST");return Kt.getKeyFromCSRHex(e)},Kt.getKeyFromCSRHex=function(t){var e=Kt.parseCSRHex(t);return Kt.getKey(e.p8pubkeyhex,null,"pkcs8pub")},Kt.parseCSRHex=function(t){var e=pt,r=e.getChildIdx,n=e.getTLV,i={},s=t;if("30"!=s.substr(0,2))throw"malformed CSR(code:001)";var a=r(s,0);if(a.length<1)throw"malformed CSR(code:002)";if("30"!=s.substr(a[0],2))throw"malformed CSR(code:003)";var o=r(s,a[0]);if(o.length<3)throw"malformed CSR(code:004)";return i.p8pubkeyhex=n(s,o[2]),i},Kt.getJWKFromKey=function(t){var e={};if(t instanceof at&&t.isPrivate)return e.kty="RSA",e.n=xt(t.n.toString(16)),e.e=xt(t.e.toString(16)),e.d=xt(t.d.toString(16)),e.p=xt(t.p.toString(16)),e.q=xt(t.q.toString(16)),e.dp=xt(t.dmp1.toString(16)),e.dq=xt(t.dmq1.toString(16)),e.qi=xt(t.coeff.toString(16)),e;if(t instanceof at&&t.isPublic)return e.kty="RSA",e.n=xt(t.n.toString(16)),e.e=xt(t.e.toString(16)),e;if(t instanceof ft.crypto.ECDSA&&t.isPrivate){if("P-256"!==(n=t.getShortNISTPCurveName())&&"P-384"!==n)throw"unsupported curve name for JWT: "+n;var r=t.getPublicKeyXYHex();return e.kty="EC",e.crv=n,e.x=xt(r.x),e.y=xt(r.y),e.d=xt(t.prvKeyHex),e}if(t instanceof ft.crypto.ECDSA&&t.isPublic){var n;if("P-256"!==(n=t.getShortNISTPCurveName())&&"P-384"!==n)throw"unsupported curve name for JWT: "+n;r=t.getPublicKeyXYHex();return e.kty="EC",e.crv=n,e.x=xt(r.x),e.y=xt(r.y),e}throw"not supported key object"},at.getPosArrayOfChildrenFromHex=function(t){return pt.getChildIdx(t,0)},at.getHexValueArrayOfChildrenFromHex=function(t){var e,r=pt.getV,n=r(t,(e=at.getPosArrayOfChildrenFromHex(t))[0]),i=r(t,e[1]),s=r(t,e[2]),a=r(t,e[3]),o=r(t,e[4]),u=r(t,e[5]),h=r(t,e[6]),c=r(t,e[7]),l=r(t,e[8]);return(e=new Array).push(n,i,s,a,o,u,h,c,l),e},at.prototype.readPrivateKeyFromPEMString=function(t){var e=Rt(t),r=at.getHexValueArrayOfChildrenFromHex(e);this.setPrivateEx(r[1],r[2],r[3],r[4],r[5],r[6],r[7],r[8])},at.prototype.readPKCS5PrvKeyHex=function(t){var e=at.getHexValueArrayOfChildrenFromHex(t);this.setPrivateEx(e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8])},at.prototype.readPKCS8PrvKeyHex=function(t){var e,r,n,i,s,a,o,u,h=pt,c=h.getVbyList;if(!1===h.isASN1HEX(t))throw"not ASN.1 hex string";try{e=c(t,0,[2,0,1],"02"),r=c(t,0,[2,0,2],"02"),n=c(t,0,[2,0,3],"02"),i=c(t,0,[2,0,4],"02"),s=c(t,0,[2,0,5],"02"),a=c(t,0,[2,0,6],"02"),o=c(t,0,[2,0,7],"02"),u=c(t,0,[2,0,8],"02")}catch(t){throw"malformed PKCS#8 plain RSA private key"}this.setPrivateEx(e,r,n,i,s,a,o,u)},at.prototype.readPKCS5PubKeyHex=function(t){var e=pt,r=e.getV;if(!1===e.isASN1HEX(t))throw"keyHex is not ASN.1 hex string";var n=e.getChildIdx(t,0);if(2!==n.length||"02"!==t.substr(n[0],2)||"02"!==t.substr(n[1],2))throw"wrong hex for PKCS#5 public key";var i=r(t,n[0]),s=r(t,n[1]);this.setPublic(i,s)},at.prototype.readPKCS8PubKeyHex=function(t){var e=pt;if(!1===e.isASN1HEX(t))throw"not ASN.1 hex string";if("06092a864886f70d010101"!==e.getTLVbyList(t,0,[0,0]))throw"not PKCS8 RSA public key";var r=e.getTLVbyList(t,0,[1,0]);this.readPKCS5PubKeyHex(r)},at.prototype.readCertPubKeyHex=function(t,e){var r,n;(r=new $t).readCertHex(t),n=r.getPublicKeyHex(),this.readPKCS8PubKeyHex(n)};var qt=new RegExp("");function zt(t,e){for(var r="",n=e/4-t.length,i=0;i<n;i++)r+="0";return r+t}function Gt(t,e,r){for(var n="",i=0;n.length<e;)n+=Bt(r(Ct(t+String.fromCharCode.apply(String,[(4278190080&i)>>24,(16711680&i)>>16,(65280&i)>>8,255&i])))),i+=1;return n}function Yt(t){for(var e in ft.crypto.Util.DIGESTINFOHEAD){var r=ft.crypto.Util.DIGESTINFOHEAD[e],n=r.length;if(t.substring(0,n)==r)return[e,t.substring(n)]}return[]}function $t(){var t=pt,e=t.getChildIdx,r=t.getV,n=t.getTLV,i=t.getVbyList,s=t.getTLVbyList,a=t.getIdxbyList,o=t.getVidx,u=t.oidname,h=$t,c=Rt;this.hex=null,this.version=0,this.foffset=0,this.aExtInfo=null,this.getVersion=function(){return null===this.hex||0!==this.version?this.version:"a003020102"!==s(this.hex,0,[0,0])?(this.version=1,this.foffset=-1,1):(this.version=3,3)},this.getSerialNumberHex=function(){return i(this.hex,0,[0,1+this.foffset],"02")},this.getSignatureAlgorithmField=function(){return u(i(this.hex,0,[0,2+this.foffset,0],"06"))},this.getIssuerHex=function(){return s(this.hex,0,[0,3+this.foffset],"30")},this.getIssuerString=function(){return h.hex2dn(this.getIssuerHex())},this.getSubjectHex=function(){return s(this.hex,0,[0,5+this.foffset],"30")},this.getSubjectString=function(){return h.hex2dn(this.getSubjectHex())},this.getNotBefore=function(){var t=i(this.hex,0,[0,4+this.foffset,0]);return t=t.replace(/(..)/g,"%$1"),t=decodeURIComponent(t)},this.getNotAfter=function(){var t=i(this.hex,0,[0,4+this.foffset,1]);return t=t.replace(/(..)/g,"%$1"),t=decodeURIComponent(t)},this.getPublicKeyHex=function(){return t.getTLVbyList(this.hex,0,[0,6+this.foffset],"30")},this.getPublicKeyIdx=function(){return a(this.hex,0,[0,6+this.foffset],"30")},this.getPublicKeyContentIdx=function(){var t=this.getPublicKeyIdx();return a(this.hex,t,[1,0],"30")},this.getPublicKey=function(){return Kt.getKey(this.getPublicKeyHex(),null,"pkcs8pub")},this.getSignatureAlgorithmName=function(){return u(i(this.hex,0,[1,0],"06"))},this.getSignatureValueHex=function(){return i(this.hex,0,[2],"03",!0)},this.verifySignature=function(t){var e=this.getSignatureAlgorithmName(),r=this.getSignatureValueHex(),n=s(this.hex,0,[0],"30"),i=new ft.crypto.Signature({alg:e});return i.init(t),i.updateHex(n),i.verify(r)},this.parseExt=function(){if(3!==this.version)return-1;var r=a(this.hex,0,[0,7,0],"30"),n=e(this.hex,r);this.aExtInfo=new Array;for(var s=0;s<n.length;s++){var u={critical:!1},h=0;3===e(this.hex,n[s]).length&&(u.critical=!0,h=1),u.oid=t.hextooidstr(i(this.hex,n[s],[0],"06"));var c=a(this.hex,n[s],[1+h]);u.vidx=o(this.hex,c),this.aExtInfo.push(u)}},this.getExtInfo=function(t){var e=this.aExtInfo,r=t;if(t.match(/^[0-9.]+$/)||(r=ft.asn1.x509.OID.name2oid(t)),""!==r)for(var n=0;n<e.length;n++)if(e[n].oid===r)return e[n]},this.getExtBasicConstraints=function(){var t=this.getExtInfo("basicConstraints");if(void 0===t)return t;var e=r(this.hex,t.vidx);if(""===e)return{};if("0101ff"===e)return{cA:!0};if("0101ff02"===e.substr(0,8)){var n=r(e,6);return{cA:!0,pathLen:parseInt(n,16)}}throw"basicConstraints parse error"},this.getExtKeyUsageBin=function(){var t=this.getExtInfo("keyUsage");if(void 0===t)return"";var e=r(this.hex,t.vidx);if(e.length%2!=0||e.length<=2)throw"malformed key usage value";var n=parseInt(e.substr(0,2)),i=parseInt(e.substr(2),16).toString(2);return i.substr(0,i.length-n)},this.getExtKeyUsageString=function(){for(var t=this.getExtKeyUsageBin(),e=new Array,r=0;r<t.length;r++)"1"==t.substr(r,1)&&e.push($t.KEYUSAGE_NAME[r]);return e.join(",")},this.getExtSubjectKeyIdentifier=function(){var t=this.getExtInfo("subjectKeyIdentifier");return void 0===t?t:r(this.hex,t.vidx)},this.getExtAuthorityKeyIdentifier=function(){var t=this.getExtInfo("authorityKeyIdentifier");if(void 0===t)return t;for(var i={},s=n(this.hex,t.vidx),a=e(s,0),o=0;o<a.length;o++)"80"===s.substr(a[o],2)&&(i.kid=r(s,a[o]));return i},this.getExtExtKeyUsageName=function(){var t=this.getExtInfo("extKeyUsage");if(void 0===t)return t;var i=new Array,s=n(this.hex,t.vidx);if(""===s)return i;for(var a=e(s,0),o=0;o<a.length;o++)i.push(u(r(s,a[o])));return i},this.getExtSubjectAltName=function(){for(var t=this.getExtSubjectAltName2(),e=new Array,r=0;r<t.length;r++)"DNS"===t[r][0]&&e.push(t[r][1]);return e},this.getExtSubjectAltName2=function(){var t,i,s,a=this.getExtInfo("subjectAltName");if(void 0===a)return a;for(var o=new Array,u=n(this.hex,a.vidx),h=e(u,0),c=0;c<h.length;c++)s=u.substr(h[c],2),t=r(u,h[c]),"81"===s&&(i=Et(t),o.push(["MAIL",i])),"82"===s&&(i=Et(t),o.push(["DNS",i])),"84"===s&&(i=$t.hex2dn(t,0),o.push(["DN",i])),"86"===s&&(i=Et(t),o.push(["URI",i])),"87"===s&&(i=Vt(t),o.push(["IP",i]));return o},this.getExtCRLDistributionPointsURI=function(){var t=this.getExtInfo("cRLDistributionPoints");if(void 0===t)return t;for(var r=new Array,n=e(this.hex,t.vidx),s=0;s<n.length;s++)try{var a=Et(i(this.hex,n[s],[0,0,0],"86"));r.push(a)}catch(t){}return r},this.getExtAIAInfo=function(){var t=this.getExtInfo("authorityInfoAccess");if(void 0===t)return t;for(var r={ocsp:[],caissuer:[]},n=e(this.hex,t.vidx),s=0;s<n.length;s++){var a=i(this.hex,n[s],[0],"06"),o=i(this.hex,n[s],[1],"86");"2b06010505073001"===a&&r.ocsp.push(Et(o)),"2b06010505073002"===a&&r.caissuer.push(Et(o))}return r},this.getExtCertificatePolicies=function(){var t=this.getExtInfo("certificatePolicies");if(void 0===t)return t;for(var s=n(this.hex,t.vidx),a=[],o=e(s,0),h=0;h<o.length;h++){var c={},l=e(s,o[h]);if(c.id=u(r(s,l[0])),2===l.length)for(var f=e(s,l[1]),d=0;d<f.length;d++){var g=i(s,f[d],[0],"06");"2b06010505070201"===g?c.cps=Et(i(s,f[d],[1])):"2b06010505070202"===g&&(c.unotice=Et(i(s,f[d],[1,0])))}a.push(c)}return a},this.readCertPEM=function(t){this.readCertHex(c(t))},this.readCertHex=function(t){this.hex=t,this.getVersion();try{a(this.hex,0,[0,7],"a3"),this.parseExt()}catch(t){}},this.getInfo=function(){var t,e,r;if(t="Basic Fields\n",t+="  serial number: "+this.getSerialNumberHex()+"\n",t+="  signature algorithm: "+this.getSignatureAlgorithmField()+"\n",t+="  issuer: "+this.getIssuerString()+"\n",t+="  notBefore: "+this.getNotBefore()+"\n",t+="  notAfter: "+this.getNotAfter()+"\n",t+="  subject: "+this.getSubjectString()+"\n",t+="  subject public key info: \n",t+="    key algorithm: "+(e=this.getPublicKey()).type+"\n","RSA"===e.type&&(t+="    n="+Mt(e.n.toString(16)).substr(0,16)+"...\n",t+="    e="+Mt(e.e.toString(16))+"\n"),null!=(r=this.aExtInfo)){t+="X509v3 Extensions:\n";for(var n=0;n<r.length;n++){var i=r[n],s=ft.asn1.x509.OID.oid2name(i.oid);""===s&&(s=i.oid);var a="";if(!0===i.critical&&(a="CRITICAL"),t+="  "+s+" "+a+":\n","basicConstraints"===s){var o=this.getExtBasicConstraints();void 0===o.cA?t+="    {}\n":(t+="    cA=true",void 0!==o.pathLen&&(t+=", pathLen="+o.pathLen),t+="\n")}else if("keyUsage"===s)t+="    "+this.getExtKeyUsageString()+"\n";else if("subjectKeyIdentifier"===s)t+="    "+this.getExtSubjectKeyIdentifier()+"\n";else if("authorityKeyIdentifier"===s){var u=this.getExtAuthorityKeyIdentifier();void 0!==u.kid&&(t+="    kid="+u.kid+"\n")}else{if("extKeyUsage"===s)t+="    "+this.getExtExtKeyUsageName().join(", ")+"\n";else if("subjectAltName"===s)t+="    "+this.getExtSubjectAltName2()+"\n";else if("cRLDistributionPoints"===s)t+="    "+this.getExtCRLDistributionPointsURI()+"\n";else if("authorityInfoAccess"===s){var h=this.getExtAIAInfo();void 0!==h.ocsp&&(t+="    ocsp: "+h.ocsp.join(",")+"\n"),void 0!==h.caissuer&&(t+="    caissuer: "+h.caissuer.join(",")+"\n")}else if("certificatePolicies"===s)for(var c=this.getExtCertificatePolicies(),l=0;l<c.length;l++)void 0!==c[l].id&&(t+="    policy oid: "+c[l].id+"\n"),void 0!==c[l].cps&&(t+="    cps: "+c[l].cps+"\n")}}}return t+="signature algorithm: "+this.getSignatureAlgorithmName()+"\n",t+="signature: "+this.getSignatureValueHex().substr(0,16)+"...\n"}}qt.compile("[^0-9a-f]","gi"),at.prototype.sign=function(t,e){var r=function(t){return ft.crypto.Util.hashString(t,e)}(t);return this.signWithMessageHash(r,e)},at.prototype.signWithMessageHash=function(t,e){var r=it(ft.crypto.Util.getPaddedDigestInfoHex(t,e,this.n.bitLength()),16);return zt(this.doPrivate(r).toString(16),this.n.bitLength())},at.prototype.signPSS=function(t,e,r){var n,i=(n=Ct(t),ft.crypto.Util.hashHex(n,e));return void 0===r&&(r=-1),this.signWithMessageHashPSS(i,e,r)},at.prototype.signWithMessageHashPSS=function(t,e,r){var n,i=Bt(t),s=i.length,a=this.n.bitLength()-1,o=Math.ceil(a/8),u=function(t){return ft.crypto.Util.hashHex(t,e)};if(-1===r||void 0===r)r=s;else if(-2===r)r=o-s-2;else if(r<-2)throw"invalid salt length";if(o<s+r+2)throw"data too long";var h="";r>0&&(h=new Array(r),(new nt).nextBytes(h),h=String.fromCharCode.apply(String,h));var c=Bt(u(Ct("\0\0\0\0\0\0\0\0"+i+h))),l=[];for(n=0;n<o-r-s-2;n+=1)l[n]=0;var f=String.fromCharCode.apply(String,l)+""+h,d=Gt(c,f.length,u),g=[];for(n=0;n<f.length;n+=1)g[n]=f.charCodeAt(n)^d.charCodeAt(n);var p=65280>>8*o-a&255;for(g[0]&=~p,n=0;n<s;n++)g.push(c.charCodeAt(n));return g.push(188),zt(this.doPrivate(new A(g)).toString(16),this.n.bitLength())},at.prototype.verify=function(t,e){var r=it(e=(e=e.replace(qt,"")).replace(/[ \n]+/g,""),16);if(r.bitLength()>this.n.bitLength())return 0;var n=Yt(this.doPublic(r).toString(16).replace(/^1f+00/,""));if(0==n.length)return!1;var i=n[0];return n[1]==function(t){return ft.crypto.Util.hashString(t,i)}(t)},at.prototype.verifyWithMessageHash=function(t,e){var r=it(e=(e=e.replace(qt,"")).replace(/[ \n]+/g,""),16);if(r.bitLength()>this.n.bitLength())return 0;var n=Yt(this.doPublic(r).toString(16).replace(/^1f+00/,""));if(0==n.length)return!1;n[0];return n[1]==t},at.prototype.verifyPSS=function(t,e,r,n){var i,s=(i=Ct(t),ft.crypto.Util.hashHex(i,r));return void 0===n&&(n=-1),this.verifyWithMessageHashPSS(s,e,r,n)},at.prototype.verifyWithMessageHashPSS=function(t,e,r,n){var i=new A(e,16);if(i.bitLength()>this.n.bitLength())return!1;var s,a=function(t){return ft.crypto.Util.hashHex(t,r)},o=Bt(t),u=o.length,h=this.n.bitLength()-1,c=Math.ceil(h/8);if(-1===n||void 0===n)n=u;else if(-2===n)n=c-u-2;else if(n<-2)throw"invalid salt length";if(c<u+n+2)throw"data too long";var l=this.doPublic(i).toByteArray();for(s=0;s<l.length;s+=1)l[s]&=255;for(;l.length<c;)l.unshift(0);if(188!==l[c-1])throw"encoded message does not end in 0xbc";var f=(l=String.fromCharCode.apply(String,l)).substr(0,c-u-1),d=l.substr(f.length,u),g=65280>>8*c-h&255;if(0!=(f.charCodeAt(0)&g))throw"bits beyond keysize not zero";var p=Gt(d,f.length,a),y=[];for(s=0;s<f.length;s+=1)y[s]=f.charCodeAt(s)^p.charCodeAt(s);y[0]&=~g;var v=c-u-n-2;for(s=0;s<v;s+=1)if(0!==y[s])throw"leftmost octets not zero";if(1!==y[v])throw"0x01 marker not found";return d===Bt(a(Ct("\0\0\0\0\0\0\0\0"+o+String.fromCharCode.apply(String,y.slice(-n)))))},at.SALT_LEN_HLEN=-1,at.SALT_LEN_MAX=-2,at.SALT_LEN_RECOVER=-2,$t.hex2dn=function(t,e){if(void 0===e&&(e=0),"30"!==t.substr(e,2))throw"malformed DN";for(var r=new Array,n=pt.getChildIdx(t,e),i=0;i<n.length;i++)r.push($t.hex2rdn(t,n[i]));return"/"+(r=r.map(function(t){return t.replace("/","\\/")})).join("/")},$t.hex2rdn=function(t,e){if(void 0===e&&(e=0),"31"!==t.substr(e,2))throw"malformed RDN";for(var r=new Array,n=pt.getChildIdx(t,e),i=0;i<n.length;i++)r.push($t.hex2attrTypeValue(t,n[i]));return(r=r.map(function(t){return t.replace("+","\\+")})).join("+")},$t.hex2attrTypeValue=function(t,e){var r=pt,n=r.getV;if(void 0===e&&(e=0),"30"!==t.substr(e,2))throw"malformed attribute type and value";var i=r.getChildIdx(t,e);2!==i.length||t.substr(i[0],2);var s=n(t,i[0]),a=ft.asn1.ASN1Util.oidHexToInt(s);return ft.asn1.x509.OID.oid2atype(a)+"="+Bt(n(t,i[1]))},$t.getPublicKeyFromCertHex=function(t){var e=new $t;return e.readCertHex(t),e.getPublicKey()},$t.getPublicKeyFromCertPEM=function(t){var e=new $t;return e.readCertPEM(t),e.getPublicKey()},$t.getPublicKeyInfoPropOfCertPEM=function(t){var e,r,n=pt.getVbyList,i={};return i.algparam=null,(e=new $t).readCertPEM(t),r=e.getPublicKeyHex(),i.keyhex=n(r,0,[1],"03").substr(2),i.algoid=n(r,0,[0,0],"06"),"2a8648ce3d0201"===i.algoid&&(i.algparam=n(r,0,[0,1],"06")),i},$t.KEYUSAGE_NAME=["digitalSignature","nonRepudiation","keyEncipherment","dataEncipherment","keyAgreement","keyCertSign","cRLSign","encipherOnly","decipherOnly"],void 0!==ft&&ft||(ft={}),void 0!==ft.jws&&ft.jws||(ft.jws={}),ft.jws.JWS=function(){var t=ft.jws.JWS.isSafeJSONString;this.parseJWS=function(e,r){if(void 0===this.parsedJWS||!r&&void 0===this.parsedJWS.sigvalH){var n=e.match(/^([^.]+)\.([^.]+)\.([^.]+)$/);if(null==n)throw"JWS signature is not a form of 'Head.Payload.SigValue'.";var i=n[1],s=n[2],a=n[3],o=i+"."+s;if(this.parsedJWS={},this.parsedJWS.headB64U=i,this.parsedJWS.payloadB64U=s,this.parsedJWS.sigvalB64U=a,this.parsedJWS.si=o,!r){var u=At(a),h=it(u,16);this.parsedJWS.sigvalH=u,this.parsedJWS.sigvalBI=h}var c=gt(i),l=gt(s);if(this.parsedJWS.headS=c,this.parsedJWS.payloadS=l,!t(c,this.parsedJWS,"headP"))throw"malformed JSON string for JWS Head: "+c}}},ft.jws.JWS.sign=function(t,e,r,n,i){var s,a,o,u=ft,h=u.jws.JWS,c=h.readSafeJSONString,l=h.isSafeJSONString,f=u.crypto,d=(f.ECDSA,f.Mac),g=f.Signature,p=JSON;if("string"!=typeof e&&"object"!=typeof e)throw"spHeader must be JSON string or object: "+e;if("object"==typeof e&&(a=e,s=p.stringify(a)),"string"==typeof e){if(!l(s=e))throw"JWS Head is not safe JSON string: "+s;a=c(s)}if(o=r,"object"==typeof r&&(o=p.stringify(r)),""!=t&&null!=t||void 0===a.alg||(t=a.alg),""!=t&&null!=t&&void 0===a.alg&&(a.alg=t,s=p.stringify(a)),t!==a.alg)throw"alg and sHeader.alg doesn't match: "+t+"!="+a.alg;var y=null;if(void 0===h.jwsalg2sigalg[t])throw"unsupported alg name: "+t;y=h.jwsalg2sigalg[t];var v=dt(s)+"."+dt(o),m="";if("Hmac"==y.substr(0,4)){if(void 0===n)throw"mac key shall be specified for HS* alg";var F=new d({alg:y,prov:"cryptojs",pass:n});F.updateString(v),m=F.doFinal()}else{var S;if(-1!=y.indexOf("withECDSA"))(S=new g({alg:y})).init(n,i),S.updateString(v),hASN1Sig=S.sign(),m=ft.crypto.ECDSA.asn1SigToConcatSig(hASN1Sig);else if("none"!=y)(S=new g({alg:y})).init(n,i),S.updateString(v),m=S.sign()}return v+"."+xt(m)},ft.jws.JWS.verify=function(t,e,r){var n,i=ft,s=i.jws.JWS,a=s.readSafeJSONString,o=i.crypto,u=o.ECDSA,h=o.Mac,c=o.Signature;n=at;var l=t.split(".");if(3!==l.length)return!1;var f=l[0]+"."+l[1],d=At(l[2]),g=a(gt(l[0])),p=null,y=null;if(void 0===g.alg)throw"algorithm not specified in header";if((y=(p=g.alg).substr(0,2),null!=r&&"[object Array]"===Object.prototype.toString.call(r)&&r.length>0)&&-1==(":"+r.join(":")+":").indexOf(":"+p+":"))throw"algorithm '"+p+"' not accepted in the list";if("none"!=p&&null===e)throw"key shall be specified to verify.";if("string"==typeof e&&-1!=e.indexOf("-----BEGIN ")&&(e=Kt.getKey(e)),!("RS"!=y&&"PS"!=y||e instanceof n))throw"key shall be a RSAKey obj for RS* and PS* algs";if("ES"==y&&!(e instanceof u))throw"key shall be a ECDSA obj for ES* algs";var v=null;if(void 0===s.jwsalg2sigalg[g.alg])throw"unsupported alg name: "+p;if("none"==(v=s.jwsalg2sigalg[p]))throw"not supported";if("Hmac"==v.substr(0,4)){if(void 0===e)throw"hexadecimal key shall be specified for HMAC";var m=new h({alg:v,pass:e});return m.updateString(f),d==m.doFinal()}if(-1!=v.indexOf("withECDSA")){var F,S=null;try{S=u.concatSigToASN1Sig(d)}catch(t){return!1}return(F=new c({alg:v})).init(e),F.updateString(f),F.verify(S)}return(F=new c({alg:v})).init(e),F.updateString(f),F.verify(d)},ft.jws.JWS.parse=function(t){var e,r,n,i=t.split("."),s={};if(2!=i.length&&3!=i.length)throw"malformed sJWS: wrong number of '.' splitted elements";return e=i[0],r=i[1],3==i.length&&(n=i[2]),s.headerObj=ft.jws.JWS.readSafeJSONString(gt(e)),s.payloadObj=ft.jws.JWS.readSafeJSONString(gt(r)),s.headerPP=JSON.stringify(s.headerObj,null,"  "),null==s.payloadObj?s.payloadPP=gt(r):s.payloadPP=JSON.stringify(s.payloadObj,null,"  "),void 0!==n&&(s.sigHex=At(n)),s},ft.jws.JWS.verifyJWT=function(t,e,r){var n=ft.jws,i=n.JWS,s=i.readSafeJSONString,a=i.inArray,o=i.includedArray,u=t.split("."),h=u[0],c=u[1],l=(At(u[2]),s(gt(h))),f=s(gt(c));if(void 0===l.alg)return!1;if(void 0===r.alg)throw"acceptField.alg shall be specified";if(!a(l.alg,r.alg))return!1;if(void 0!==f.iss&&"object"==typeof r.iss&&!a(f.iss,r.iss))return!1;if(void 0!==f.sub&&"object"==typeof r.sub&&!a(f.sub,r.sub))return!1;if(void 0!==f.aud&&"object"==typeof r.aud)if("string"==typeof f.aud){if(!a(f.aud,r.aud))return!1}else if("object"==typeof f.aud&&!o(f.aud,r.aud))return!1;var d=n.IntDate.getNow();return void 0!==r.verifyAt&&"number"==typeof r.verifyAt&&(d=r.verifyAt),void 0!==r.gracePeriod&&"number"==typeof r.gracePeriod||(r.gracePeriod=0),!(void 0!==f.exp&&"number"==typeof f.exp&&f.exp+r.gracePeriod<d)&&(!(void 0!==f.nbf&&"number"==typeof f.nbf&&d<f.nbf-r.gracePeriod)&&(!(void 0!==f.iat&&"number"==typeof f.iat&&d<f.iat-r.gracePeriod)&&((void 0===f.jti||void 0===r.jti||f.jti===r.jti)&&!!i.verify(t,e,r.alg))))},ft.jws.JWS.includedArray=function(t,e){var r=ft.jws.JWS.inArray;if(null===t)return!1;if("object"!=typeof t)return!1;if("number"!=typeof t.length)return!1;for(var n=0;n<t.length;n++)if(!r(t[n],e))return!1;return!0},ft.jws.JWS.inArray=function(t,e){if(null===e)return!1;if("object"!=typeof e)return!1;if("number"!=typeof e.length)return!1;for(var r=0;r<e.length;r++)if(e[r]==t)return!0;return!1},ft.jws.JWS.jwsalg2sigalg={HS256:"HmacSHA256",HS384:"HmacSHA384",HS512:"HmacSHA512",RS256:"SHA256withRSA",RS384:"SHA384withRSA",RS512:"SHA512withRSA",ES256:"SHA256withECDSA",ES384:"SHA384withECDSA",PS256:"SHA256withRSAandMGF1",PS384:"SHA384withRSAandMGF1",PS512:"SHA512withRSAandMGF1",none:"none"},ft.jws.JWS.isSafeJSONString=function(t,e,r){var n=null;try{return"object"!=typeof(n=lt(t))?0:n.constructor===Array?0:(e&&(e[r]=n),1)}catch(t){return 0}},ft.jws.JWS.readSafeJSONString=function(t){var e=null;try{return"object"!=typeof(e=lt(t))?null:e.constructor===Array?null:e}catch(t){return null}},ft.jws.JWS.getEncodedSignatureValueFromJWS=function(t){var e=t.match(/^[^.]+\.[^.]+\.([^.]+)$/);if(null==e)throw"JWS signature is not a form of 'Head.Payload.SigValue'.";return e[1]},ft.jws.JWS.getJWKthumbprint=function(t){if("RSA"!==t.kty&&"EC"!==t.kty&&"oct"!==t.kty)throw"unsupported algorithm for JWK Thumprint";var e="{";if("RSA"===t.kty){if("string"!=typeof t.n||"string"!=typeof t.e)throw"wrong n and e value for RSA key";e+='"e":"'+t.e+'",',e+='"kty":"'+t.kty+'",',e+='"n":"'+t.n+'"}'}else if("EC"===t.kty){if("string"!=typeof t.crv||"string"!=typeof t.x||"string"!=typeof t.y)throw"wrong crv, x and y value for EC key";e+='"crv":"'+t.crv+'",',e+='"kty":"'+t.kty+'",',e+='"x":"'+t.x+'",',e+='"y":"'+t.y+'"}'}else if("oct"===t.kty){if("string"!=typeof t.k)throw"wrong k value for oct(symmetric) key";e+='"kty":"'+t.kty+'",',e+='"k":"'+t.k+'"}'}var r=Ct(e);return xt(ft.crypto.Util.hashHex(r,"sha256"))},ft.jws.IntDate={},ft.jws.IntDate.get=function(t){var e=ft.jws.IntDate,r=e.getNow,n=e.getZulu;if("now"==t)return r();if("now + 1hour"==t)return r()+3600;if("now + 1day"==t)return r()+86400;if("now + 1month"==t)return r()+2592e3;if("now + 1year"==t)return r()+31536e3;if(t.match(/Z$/))return n(t);if(t.match(/^[0-9]+$/))return parseInt(t);throw"unsupported format: "+t},ft.jws.IntDate.getZulu=function(t){return Nt(t)},ft.jws.IntDate.getNow=function(){return~~(new Date/1e3)},ft.jws.IntDate.intDate2UTCString=function(t){return new Date(1e3*t).toUTCString()},ft.jws.IntDate.intDate2Zulu=function(t){var e=new Date(1e3*t);return("0000"+e.getUTCFullYear()).slice(-4)+("00"+(e.getUTCMonth()+1)).slice(-2)+("00"+e.getUTCDate()).slice(-2)+("00"+e.getUTCHours()).slice(-2)+("00"+e.getUTCMinutes()).slice(-2)+("00"+e.getUTCSeconds()).slice(-2)+"Z"},void 0!==ft&&ft||(ft={}),void 0!==ft.jws&&ft.jws||(ft.jws={}),ft.jws.JWSJS=function(){var t=ft.jws.JWS,e=t.readSafeJSONString;this.aHeader=[],this.sPayload="",this.aSignature=[],this.init=function(){this.aHeader=[],this.sPayload=void 0,this.aSignature=[]},this.initWithJWS=function(t){this.init();var e=t.split(".");if(3!=e.length)throw"malformed input JWS";this.aHeader.push(e[0]),this.sPayload=e[1],this.aSignature.push(e[2])},this.addSignature=function(t,e,r,n){if(void 0===this.sPayload||null===this.sPayload)throw"there's no JSON-JS signature to add.";var i=this.aHeader.length;if(this.aHeader.length!=this.aSignature.length)throw"aHeader.length != aSignature.length";try{var s=ft.jws.JWS.sign(t,e,this.sPayload,r,n).split(".");s[0],s[2];this.aHeader.push(s[0]),this.aSignature.push(s[2])}catch(t){throw this.aHeader.length>i&&this.aHeader.pop(),this.aSignature.length>i&&this.aSignature.pop(),"addSignature failed: "+t}},this.verifyAll=function(t){if(this.aHeader.length!==t.length||this.aSignature.length!==t.length)return!1;for(var e=0;e<t.length;e++){var r=t[e];if(2!==r.length)return!1;if(!1===this.verifyNth(e,r[0],r[1]))return!1}return!0},this.verifyNth=function(e,r,n){if(this.aHeader.length<=e||this.aSignature.length<=e)return!1;var i=this.aHeader[e],s=this.aSignature[e],a=i+"."+this.sPayload+"."+s,o=!1;try{o=t.verify(a,r,n)}catch(t){return!1}return o},this.readJWSJS=function(t){if("string"==typeof t){var r=e(t);if(null==r)throw"argument is not safe JSON object string";this.aHeader=r.headers,this.sPayload=r.payload,this.aSignature=r.signatures}else try{if(!(t.headers.length>0))throw"malformed header";if(this.aHeader=t.headers,"string"!=typeof t.payload)throw"malformed signatures";if(this.sPayload=t.payload,!(t.signatures.length>0))throw"malformed signatures";this.aSignatures=t.signatures}catch(t){throw"malformed JWS-JS JSON object: "+t}},this.getJSON=function(){return{headers:this.aHeader,payload:this.sPayload,signatures:this.aSignature}},this.isEmpty=function(){return 0==this.aHeader.length?1:0}},e.SecureRandom=nt,e.rng_seed_time=X,e.BigInteger=A,e.RSAKey=at,e.ECDSA=ft.crypto.ECDSA,e.DSA=ft.crypto.DSA,e.Signature=ft.crypto.Signature,e.MessageDigest=ft.crypto.MessageDigest,e.Mac=ft.crypto.Mac,e.Cipher=ft.crypto.Cipher,e.KEYUTIL=Kt,e.ASN1HEX=pt,e.X509=$t,e.CryptoJS=y,e.b64tohex=b,e.b64toBA=x,e.stoBA=yt,e.BAtos=vt,e.BAtohex=mt,e.stohex=Ft,e.stob64=function(t){return S(Ft(t))},e.stob64u=function(t){return St(S(Ft(t)))},e.b64utos=function(t){return vt(x(bt(t)))},e.b64tob64u=St,e.b64utob64=bt,e.hex2b64=S,e.hextob64u=xt,e.b64utohex=At,e.utf8tob64u=dt,e.b64utoutf8=gt,e.utf8tob64=function(t){return S(Ot(_t(t)))},e.b64toutf8=function(t){return decodeURIComponent(kt(b(t)))},e.utf8tohex=wt,e.hextoutf8=Et,e.hextorstr=Bt,e.rstrtohex=Ct,e.hextob64=Dt,e.hextob64nl=Tt,e.b64nltohex=It,e.hextopem=Pt,e.pemtohex=Rt,e.hextoArrayBuffer=function(t){if(t.length%2!=0)throw"input is not even length";if(null==t.match(/^[0-9A-Fa-f]+$/))throw"input is not hexadecimal";for(var e=new ArrayBuffer(t.length/2),r=new DataView(e),n=0;n<t.length/2;n++)r.setUint8(n,parseInt(t.substr(2*n,2),16));return e},e.ArrayBuffertohex=function(t){for(var e="",r=new DataView(t),n=0;n<t.byteLength;n++)e+=("00"+r.getUint8(n).toString(16)).slice(-2);return e},e.zulutomsec=Ht,e.zulutosec=Nt,e.zulutodate=function(t){return new Date(Ht(t))},e.datetozulu=function(t,e,r){var n,i=t.getUTCFullYear();if(e){if(i<1950||2049<i)throw"not proper year for UTCTime: "+i;n=(""+i).slice(-2)}else n=("000"+i).slice(-4);if(n+=("0"+(t.getUTCMonth()+1)).slice(-2),n+=("0"+t.getUTCDate()).slice(-2),n+=("0"+t.getUTCHours()).slice(-2),n+=("0"+t.getUTCMinutes()).slice(-2),n+=("0"+t.getUTCSeconds()).slice(-2),r){var s=t.getUTCMilliseconds();0!==s&&(n+="."+(s=(s=("00"+s).slice(-3)).replace(/0+$/g,"")))}return n+="Z"},e.uricmptohex=Ot,e.hextouricmp=kt,e.ipv6tohex=jt,e.hextoipv6=Lt,e.hextoip=Vt,e.iptohex=function(t){var e="malformed IP address";if(!(t=t.toLowerCase(t)).match(/^[0-9.]+$/)){if(t.match(/^[0-9a-f:]+$/)&&-1!==t.indexOf(":"))return jt(t);throw e}var r=t.split(".");if(4!==r.length)throw e;var n="";try{for(var i=0;i<4;i++){n+=("0"+parseInt(r[i]).toString(16)).slice(-2)}return n}catch(t){throw e}},e.encodeURIComponentAll=_t,e.newline_toUnix=function(t){return t=t.replace(/\r\n/gm,"\n")},e.newline_toDos=function(t){return t=(t=t.replace(/\r\n/gm,"\n")).replace(/\n/gm,"\r\n")},e.hextoposhex=Mt,e.intarystrtohex=Ut,e.strdiffidx=function(t,e){var r=t.length;t.length>e.length&&(r=e.length);for(var n=0;n<r;n++)if(t.charCodeAt(n)!=e.charCodeAt(n))return n;return t.length!=e.length?r:-1},e.KJUR=ft,e.crypto=ft.crypto,e.asn1=ft.asn1,e.jws=ft.jws,e.lang=ft.lang}).call(this,r(12).Buffer)}});