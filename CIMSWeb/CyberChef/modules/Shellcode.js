/**
 * CyberChef - The Cyber Swiss Army Knife
 *
 * @copyright Crown Copyright 2016
 * @license Apache-2.0
 *
 *   Copyright 2016 Crown Copyright
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */!function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)r.d(n,i,function(t){return e[t]}.bind(null,i));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=1341)}({0:function(e,t,r){"use strict";(function(e,n){r.d(t,"c",function(){return h}),r.d(t,"d",function(){return M}),r.d(t,"e",function(){return p}),r.d(t,"a",function(){return R});var i=r(7),o=r.n(i),u=r(11),a=r.n(u),A=r(1),s=r.n(A),c=r(2),f=r.n(c),S=r(24),l=r.n(S),B=r(14),D=r(17),P=r(30),C=r(29),E=function(){function t(){s()(this,t)}var r;return f()(t,null,[{key:"chr",value:function(e){if(e>65535){e-=65536;var t=String.fromCharCode(e>>>10&1023|55296);return e=56320|1023&e,t+String.fromCharCode(e)}return String.fromCharCode(e)}},{key:"ord",value:function(e){if(2===e.length){var t=e.charCodeAt(0),r=e.charCodeAt(1);if(t>=55296&&t<56320&&r>=56320&&r<57344)return 1024*(t-55296)+r-56320+65536}return e.charCodeAt(0)}},{key:"padBytesRight",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=new Array(t);return n.fill(r),[...e].forEach(function(e,t){n[t]=e}),n}},{key:"truncate",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"...";return e.length>t&&(e=e.slice(0,t-r.length)+r),e}},{key:"hex",value:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return(e="string"==typeof e?t.ord(e):e).toString(16).padStart(r,"0")}},{key:"bin",value:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:8;return(e="string"==typeof e?t.ord(e):e).toString(2).padStart(r,"0")}},{key:"printable",value:function(e){var r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];F()&&window.app&&!window.app.options.treatAsUtf8&&(e=t.byteArrayToChars(t.strToByteArray(e)));var n=/[\0-\x08\x0B-\x0C\x0E-\x1F\x7F-\x9F\xAD\u0378\u0379\u037F-\u0383\u038B\u038D\u03A2\u0528-\u0530\u0557\u0558\u0560\u0588\u058B-\u058E\u0590\u05C8-\u05CF\u05EB-\u05EF\u05F5-\u0605\u061C\u061D\u06DD\u070E\u070F\u074B\u074C\u07B2-\u07BF\u07FB-\u07FF\u082E\u082F\u083F\u085C\u085D\u085F-\u089F\u08A1\u08AD-\u08E3\u08FF\u0978\u0980\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09FC-\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF2-\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B55\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B78-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BFB-\u0C00\u0C04\u0C0D\u0C11\u0C29\u0C34\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5A-\u0C5F\u0C64\u0C65\u0C70-\u0C77\u0C80\u0C81\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0D01\u0D04\u0D0D\u0D11\u0D3B\u0D3C\u0D45\u0D49\u0D4F-\u0D56\u0D58-\u0D5F\u0D64\u0D65\u0D76-\u0D78\u0D80\u0D81\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DF1\u0DF5-\u0E00\u0E3B-\u0E3E\u0E5C-\u0E80\u0E83\u0E85\u0E86\u0E89\u0E8B\u0E8C\u0E8E-\u0E93\u0E98\u0EA0\u0EA4\u0EA6\u0EA8\u0EA9\u0EAC\u0EBA\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F48\u0F6D-\u0F70\u0F98\u0FBD\u0FCD\u0FDB-\u0FFF\u10C6\u10C8-\u10CC\u10CE\u10CF\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u137D-\u137F\u139A-\u139F\u13F5-\u13FF\u169D-\u169F\u16F1-\u16FF\u170D\u1715-\u171F\u1737-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17DE\u17DF\u17EA-\u17EF\u17FA-\u17FF\u180F\u181A-\u181F\u1878-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191D-\u191F\u192C-\u192F\u193C-\u193F\u1941-\u1943\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DB-\u19DD\u1A1C\u1A1D\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1A9F\u1AAE-\u1AFF\u1B4C-\u1B4F\u1B7D-\u1B7F\u1BF4-\u1BFB\u1C38-\u1C3A\u1C4A-\u1C4C\u1C80-\u1CBF\u1CC8-\u1CCF\u1CF7-\u1CFF\u1DE7-\u1DFB\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FC5\u1FD4\u1FD5\u1FDC\u1FF0\u1FF1\u1FF5\u1FFF\u200B-\u200F\u202A-\u202E\u2060-\u206F\u2072\u2073\u208F\u209D-\u209F\u20BB-\u20CF\u20F1-\u20FF\u218A-\u218F\u23F4-\u23FF\u2427-\u243F\u244B-\u245F\u2700\u2B4D-\u2B4F\u2B5A-\u2BFF\u2C2F\u2C5F\u2CF4-\u2CF8\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D71-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E3C-\u2E7F\u2E9A\u2EF4-\u2EFF\u2FD6-\u2FEF\u2FFC-\u2FFF\u3040\u3097\u3098\u3100-\u3104\u312E-\u3130\u318F\u31BB-\u31BF\u31E4-\u31EF\u321F\u32FF\u4DB6-\u4DBF\u9FCD-\u9FFF\uA48D-\uA48F\uA4C7-\uA4CF\uA62C-\uA63F\uA698-\uA69E\uA6F8-\uA6FF\uA78F\uA794-\uA79F\uA7AB-\uA7F7\uA82C-\uA82F\uA83A-\uA83F\uA878-\uA87F\uA8C5-\uA8CD\uA8DA-\uA8DF\uA8FC-\uA8FF\uA954-\uA95E\uA97D-\uA97F\uA9CE\uA9DA-\uA9DD\uA9E0-\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A\uAA5B\uAA7C-\uAA7F\uAAC3-\uAADA\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F-\uABBF\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBC2-\uFBD2\uFD40-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFE\uFDFF\uFE1A-\uFE1F\uFE27-\uFE2F\uFE53\uFE67\uFE6C-\uFE6F\uFE75\uFEFD-\uFF00\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFDF\uFFE7\uFFEF-\uFFFB\uFFFE\uFFFF]/g,i=/[\x09-\x10\x0D\u2028\u2029]/g;return e=e.replace(n,"."),r||(e=e.replace(i,".")),e}},{key:"parseEscapedChars",value:function(e){return e.replace(/(\\)?\\([bfnrtv'"]|[0-3][0-7]{2}|[0-7]{1,2}|x[\da-fA-F]{2}|u[\da-fA-F]{4}|u\{[\da-fA-F]{1,6}\}|\\)/g,function(e,t,r){if("\\"===t)return"\\"+r;switch(r[0]){case"\\":return"\\";case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":return String.fromCharCode(parseInt(r,8));case"b":return"\b";case"t":return"\t";case"n":return"\n";case"v":return"\v";case"f":return"\f";case"r":return"\r";case'"':return'"';case"'":return"'";case"x":return String.fromCharCode(parseInt(r.substr(1),16));case"u":return"{"===r[1]?String.fromCodePoint(parseInt(r.slice(2,-1),16)):String.fromCharCode(parseInt(r.substr(1),16))}})}},{key:"escapeRegex",value:function(e){return e.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}},{key:"expandAlphRange",value:function(e){for(var r=[],n=0;n<e.length;n++)if(n<e.length-2&&"-"===e[n+1]&&"\\"!==e[n]){for(var i=t.ord(e[n]),o=t.ord(e[n+2]),u=i;u<=o;u++)r.push(t.chr(u));n+=2}else n<e.length-2&&"\\"===e[n]&&"-"===e[n+1]?(r.push("-"),n++):r.push(e[n]);return r}},{key:"convertToByteArray",value:function(e,r){switch(r.toLowerCase()){case"binary":return Object(C.a)(e);case"hex":return Object(D.a)(e);case"decimal":return Object(P.a)(e);case"base64":return Object(B.a)(e,null,"byteArray");case"utf8":return t.strToUtf8ByteArray(e);case"latin1":default:return t.strToByteArray(e)}}},{key:"convertToByteString",value:function(e,r){switch(r.toLowerCase()){case"binary":return t.byteArrayToChars(Object(C.a)(e));case"hex":return t.byteArrayToChars(Object(D.a)(e));case"decimal":return t.byteArrayToChars(Object(P.a)(e));case"base64":return t.byteArrayToChars(Object(B.a)(e,null,"byteArray"));case"utf8":return l.a.encode(e);case"latin1":default:return e}}},{key:"strToArrayBuffer",value:function(e){for(var r,n=new Uint8Array(e.length),i=e.length;i--;)if(r=e.charCodeAt(i),n[i]=r,r>255)return t.strToUtf8ArrayBuffer(e);return n.buffer}},{key:"strToUtf8ArrayBuffer",value:function(e){var r=l.a.encode(e);return e.length!==r.length&&(M()?self.setOption("attemptHighlight",!1):F()&&(window.app.options.attemptHighlight=!1)),t.strToArrayBuffer(r)}},{key:"strToByteArray",value:function(e){for(var r,n=new Array(e.length),i=e.length;i--;)if(r=e.charCodeAt(i),n[i]=r,r>255)return t.strToUtf8ByteArray(e);return n}},{key:"strToUtf8ByteArray",value:function(e){var r=l.a.encode(e);return e.length!==r.length&&(M()?self.setOption("attemptHighlight",!1):F()&&(window.app.options.attemptHighlight=!1)),t.strToByteArray(r)}},{key:"strToCharcode",value:function(e){for(var r=[],n=0;n<e.length;n++){var i=e.charCodeAt(n);if(n<e.length-1&&i>=55296&&i<56320){var o=e[n+1].charCodeAt(0);o>=56320&&o<57344&&(i=t.ord(e[n]+e[++n]))}r.push(i)}return r}},{key:"byteArrayToUtf8",value:function(e){var r=t.byteArrayToChars(e);try{var n=l.a.decode(r);return r.length!==n.length&&(M()?self.setOption("attemptHighlight",!1):F()&&(window.app.options.attemptHighlight=!1)),n}catch(e){return r}}},{key:"byteArrayToChars",value:function(e){if(!e)return"";for(var t="",r=0;r<e.length;)t+=String.fromCharCode(e[r++]);return t}},{key:"arrayBufferToStr",value:function(e){var r=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=new Uint8Array(e);return r?t.byteArrayToUtf8(n):t.byteArrayToChars(n)}},{key:"parseCSV",value:function(e){var t,r,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[","],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:["\n","\r"],o=!1,u=!1,a="",A=[],s=[];e.length&&"\ufeff"===e[0]&&(e=e.substr(1));for(var c=0;c<e.length;c++)t=e[c],r=e[c+1]||"",o?(a+=t,o=!1):'"'!==t||u?'"'===t&&u?'"'===r?o=!0:u=!1:!u&&n.indexOf(t)>=0?(A.push(a),a=""):!u&&i.indexOf(t)>=0?(A.push(a),a="",s.push(A),A=[],i.indexOf(r)>=0&&r!==t&&c++):a+=t:u=!0;return A.length&&(A.push(a),s.push(A)),s}},{key:"stripHtmlTags",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return t&&(e=e.replace(/<(script|style)[^>]*>.*<\/(script|style)>/gim,"")),e.replace(/<[^>]+>/g,"")}},{key:"escapeHtml",value:function(e){var t={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","/":"&#x2F;","`":"&#x60;"};return e.replace(/[&<>"'\/`]/g,function(e){return t[e]})}},{key:"unescapeHtml",value:function(e){var t={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#x27;":"'","&#x2F;":"/","&#x60;":"`"};return e.replace(/&#?x?[a-z0-9]{2,4};/gi,function(e){return t[e]||e})}},{key:"encodeURIFragment",value:function(e){var t={"%2D":"-","%2E":".","%5F":"_","%7E":"~","%21":"!","%24":"$","%27":"'","%28":"(","%29":")","%2A":"*","%2C":",","%3B":";","%3A":":","%40":"@","%2F":"/","%3F":"?"};return(e=encodeURIComponent(e)).replace(/%[0-9A-F]{2}/g,function(e){return t[e]||e})}},{key:"generatePrettyRecipe",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r="",n="",i="",o="",u="";return e.forEach(function(e){n=e.op.replace(/ /g,"_"),i=JSON.stringify(e.args).slice(1,-1).replace(/'/g,"\\'").replace(/"((?:[^"\\]|\\.)*)"/g,"'$1'").replace(/\\"/g,'"'),o=e.disabled?"/disabled":"",u=e.breakpoint?"/breakpoint":"",r+=`${n}(${i}${o}${u})`,t&&(r+="\n")}),r}},{key:"parseRecipeConfig",value:function(e){if(0===(e=e.trim()).length)return[];if("["===e[0])return JSON.parse(e);var t,r;e=e.replace(/\n/g,"");for(var n=/([^(]+)\(((?:'[^'\\]*(?:\\.[^'\\]*)*'|[^)\/'])*)(\/[^)]+)?\)/g,i=[];t=n.exec(e);){r="["+(r=t[2].replace(/"/g,'\\"').replace(/(^|,|{|:)'/g,'$1"').replace(/([^\\]|(?:\\\\)+)'(,|:|}|$)/g,'$1"$2').replace(/\\'/g,"'"))+"]";var o={op:t[1].replace(/_/g," "),args:JSON.parse(r)};t[3]&&t[3].indexOf("disabled")>0&&(o.disabled=!0),t[3]&&t[3].indexOf("breakpoint")>0&&(o.breakpoint=!0),i.push(o)}return i}},{key:"displayFilesAsHTML",value:(r=a()(o.a.mark(function e(r){var n,i,u,A,s;return o.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:n=function(e){return`<div class='card' style='white-space: normal;'>\n                    <div class='card-header'>\n                        <h6 class="mb-0">\n                            ${t.escapeHtml(e.name)}\n                        </h6>\n                    </div>\n                </div>`},i=function(e,r){if(r.startsWith("image")){var n="data:";return n+=r+";","<img style='max-width: 100%;' src='"+(n+="base64,"+Object(B.b)(e))+"'>"}return`<pre>${t.escapeHtml(t.arrayBufferToStr(e.buffer))}</pre>`},u=function(){var e=a()(o.a.mark(function e(r,n){var u,a,A,s;return o.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.readFile(r);case 2:return u=e.sent,a=new Blob([u],{type:r.type||"octet/stream"}),A=URL.createObjectURL(a),s=`<div class='card' style='white-space: normal;'>\n                    <div class='card-header' id='heading${n}'>\n                        <h6 class='mb-0'>\n                            <a class='collapsed'\n                                data-toggle='collapse'\n                                href='#collapse${n}'\n                                aria-expanded='false'\n                                aria-controls='collapse${n}'\n                                title="Show/hide contents of '${t.escapeHtml(r.name)}'">\n                                ${t.escapeHtml(r.name)}</a>\n                            <span class='float-right' style="margin-top: -3px">\n                                ${r.size.toLocaleString()} bytes\n                                <a title="Download ${t.escapeHtml(r.name)}"\n                                    href="${A}"\n                                    download="${t.escapeHtml(r.name)}"\n                                    data-toggle="tooltip">\n                                    <i class="material-icons" style="vertical-align: bottom">save</i>\n                                </a>\n                                <a title="Move to input"\n                                    href="#"\n                                    blob-url="${A}"\n                                    file-name="${t.escapeHtml(r.name)}"\n                                    class="extract-file"\n                                    data-toggle="tooltip">\n                                    <i class="material-icons" style="vertical-align: bottom">open_in_browser</i>\n                                </a>\n                            </span>\n                        </h6>\n                    </div>\n                    <div id='collapse${n}' class='collapse' aria-labelledby='heading${n}' data-parent="#files">\n                        <div class='card-body'>\n                            ${i(u,r.type)}\n                        </div>\n                    </div>\n                </div>`,e.abrupt("return",s);case 7:case"end":return e.stop()}},e)}));return function(t,r){return e.apply(this,arguments)}}(),A=`<div style='padding: 5px; white-space: normal;'>\n                ${r.length} file(s) found\n            </div><div id="files" style="padding: 20px">`,s=0;case 5:if(!(s<r.length)){e.next=17;break}if(!r[s].name.endsWith("/")){e.next=10;break}A+=n(r[s]),e.next=14;break;case 10:return e.t0=A,e.next=13,u(r[s],s);case 13:A=e.t0+=e.sent;case 14:s++,e.next=5;break;case 17:return e.abrupt("return",A+="</div>");case 18:case"end":return e.stop()}},e)})),function(e){return r.apply(this,arguments)})},{key:"parseURIParams",value:function(e){if(""===e)return{};"?"!==e[0]&&"#"!==e[0]||(e=e.substr(1));for(var t=e.split("&"),r={},n=0;n<t.length;n++){var i=t[n].split("=");2!==i.length?r[t[n]]=!0:r[i[0]]=decodeURIComponent(i[1].replace(/\+/g," "))}return r}},{key:"readFile",value:function(t){return h()?e.from(t).buffer:new Promise(function(e,r){var n=new FileReader,i=new Uint8Array(t.size),o=0,u=function(){if(o>=t.size)e(i);else{var r=t.slice(o,o+10485760);n.readAsArrayBuffer(r)}};n.onload=function(e){i.set(new Uint8Array(n.result),o),o+=10485760,u()},n.onerror=function(e){r(n.error.message)},u()})}},{key:"readFileSync",value:function(e){if(!h())throw new TypeError("Browser environment cannot support readFileSync");return Uint8Array.from(e.data).buffer}},{key:"mod",value:function(e,t){return(e%t+t)%t}},{key:"gcd",value:function(e,r){return r?t.gcd(r,e%r):e}},{key:"modInv",value:function(e,t){e%=t;for(var r=1;r<t;r++)if(e*r%26==1)return r}},{key:"charRep",value:function(e){return{Space:" ",Percent:"%",Comma:",","Semi-colon":";",Colon:":",Tab:"\t","Line feed":"\n",CRLF:"\r\n","Forward slash":"/",Backslash:"\\","0x":"0x","\\x":"\\x","Nothing (separate chars)":"",None:""}[e]}},{key:"regexRep",value:function(e){return{Space:/\s+/g,Percent:/%/g,Comma:/,/g,"Semi-colon":/;/g,Colon:/:/g,"Line feed":/\n/g,CRLF:/\r\n/g,"Forward slash":/\//g,Backslash:/\\/g,"0x":/0x/g,"\\x":/\\x/g,None:/\s+/g}[e]}}]),t}();function h(){return void 0!==n&&null!=n.versions&&null!=n.versions.node}function F(){return"object"==typeof window}function M(){return"function"==typeof importScripts}function p(e){M()?self.sendStatusMessage(e):F()?app.alert(e,1e4):h()&&console.debug(e)}t.b=E,Array.prototype.unique=function(){for(var e={},t=[],r=0,n=this.length;r<n;r++)Object.prototype.hasOwnProperty.call(e,this[r])||(t.push(this[r]),e[this[r]]=1);return t},Array.prototype.max=function(){return Math.max.apply(null,this)},Array.prototype.min=function(){return Math.min.apply(null,this)},Array.prototype.sum=function(){return this.reduce(function(e,t){return e+t},0)},Array.prototype.equals=function(e){if(!e)return!1;var t=this.length;if(t!==e.length)return!1;for(;t--;)if(this[t]!==e[t])return!1;return!0},String.prototype.count=function(e){return this.split(e).length-1};var T={};function R(e,t,r,n,i){return function(){clearTimeout(T[r]),T[r]=setTimeout(function(){e.apply(n,i)},t)}}String.prototype.padStart||(String.prototype.padStart=function(e,t){return e>>=0,t=String(void 0!==t?t:" "),this.length>e?String(this):((e-=this.length)>t.length&&(t+=t.repeat(e/t.length)),t.slice(0,e)+String(this))}),String.prototype.padEnd||(String.prototype.padEnd=function(e,t){return e>>=0,t=String(void 0!==t?t:" "),this.length>e?String(this):((e-=this.length)>t.length&&(t+=t.repeat(e/t.length)),String(this)+t.slice(0,e))})}).call(this,r(12).Buffer,r(27))},1:function(e,t){e.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}},10:function(e,t,r){"use strict";r.d(t,"a",function(){return a});var n=r(1),i=r.n(n),o=r(2),u=r.n(o),a=function(){function e(t){i()(this,e),this.bytes=t,this.length=this.bytes.length,this.position=0,this.bitPos=0}return u()(e,[{key:"getBytes",value:function(e){if(!(this.position>this.length)){var t=this.position+e,r=this.bytes.slice(this.position,t);return this.position=t,this.bitPos=0,r}}},{key:"readString",value:function(e){if(!(this.position>this.length)){for(var t="",r=this.position;r<this.position+e;r++){var n=this.bytes[r];if(0===n)break;t+=String.fromCharCode(n)}return this.position+=e,this.bitPos=0,t}}},{key:"readInt",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"be";if(!(this.position>this.length)){var r=0;if("be"===t)for(var n=this.position;n<this.position+e;n++)r<<=8,r|=this.bytes[n];else for(var i=this.position+e-1;i>=this.position;i--)r<<=8,r|=this.bytes[i];return this.position+=e,this.bitPos=0,r}}},{key:"readBits",value:function(e){if(!(this.position>this.length)){var t=0,r=0;for(t=(this.bytes[this.position++]&256-(1<<this.bitPos))>>>this.bitPos,r=8-this.bitPos,this.bitPos=0;r<e;)t|=this.bytes[this.position++]<<r,r+=8;if(r>e){var n=r-e;t&=(1<<e)-1,r-=n,this.position--,this.bitPos=8-n}return t}}},{key:"continueUntil",value:function(e){if(!(this.position>this.length))if(this.bitPos=0,"number"!=typeof e)for(var t=!1;!t&&this.position<this.length;){for(;++this.position<this.length&&this.bytes[this.position]!==e[0];);t=!0;for(var r=1;r<e.length;r++)(this.position+r>this.length||this.bytes[this.position+r]!==e[r])&&(t=!1)}else for(;++this.position<this.length&&this.bytes[this.position]!==e;);}},{key:"consumeIf",value:function(e){this.bytes[this.position]===e&&(this.position++,this.bitPos=0)}},{key:"moveForwardsBy",value:function(e){var t=this.position+e;if(t<0||t>this.length)throw new Error("Cannot move to position "+t+" in stream. Out of bounds.");this.position=t,this.bitPos=0}},{key:"moveBackwardsBy",value:function(e){var t=this.position-e;if(t<0||t>this.length)throw new Error("Cannot move to position "+t+" in stream. Out of bounds.");this.position=t,this.bitPos=0}},{key:"moveBackwardsByBits",value:function(e){if(e<=this.bitPos)this.bitPos-=e;else for(this.bitPos>0&&(e-=this.bitPos,this.bitPos=0);e>0;)this.moveBackwardsBy(1),this.bitPos=8,this.moveBackwardsByBits(e),e-=8}},{key:"moveTo",value:function(e){if(e<0||e>this.length)throw new Error("Cannot move to position "+e+" in stream. Out of bounds.");this.position=e,this.bitPos=0}},{key:"hasMore",value:function(){return this.position<this.length}},{key:"carve",value:function(){return this.bitPos>0&&this.position++,this.bytes.slice(0,this.position)}}]),e}()},11:function(e,t){function r(e,t,r,n,i,o,u){try{var a=e[o](u),A=a.value}catch(e){return void r(e)}a.done?t(A):Promise.resolve(A).then(n,i)}e.exports=function(e){return function(){var t=this,n=arguments;return new Promise(function(i,o){var u=e.apply(t,n);function a(e){r(u,i,o,a,A,"next",e)}function A(e){r(u,i,o,a,A,"throw",e)}a(void 0)})}}},12:function(e,t,r){"use strict";(function(e){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <<EMAIL>> <http://feross.org>
 * @license  MIT
 */
var n=r(45),i=r(46),o=r(47);function u(){return A.TYPED_ARRAY_SUPPORT?**********:**********}function a(e,t){if(u()<t)throw new RangeError("Invalid typed array length");return A.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=A.prototype:(null===e&&(e=new A(t)),e.length=t),e}function A(e,t,r){if(!(A.TYPED_ARRAY_SUPPORT||this instanceof A))return new A(e,t,r);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return f(this,e)}return s(this,e,t,r)}function s(e,t,r,n){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?function(e,t,r,n){if(t.byteLength,r<0||t.byteLength<r)throw new RangeError("'offset' is out of bounds");if(t.byteLength<r+(n||0))throw new RangeError("'length' is out of bounds");t=void 0===r&&void 0===n?new Uint8Array(t):void 0===n?new Uint8Array(t,r):new Uint8Array(t,r,n);A.TYPED_ARRAY_SUPPORT?(e=t).__proto__=A.prototype:e=S(e,t);return e}(e,t,r,n):"string"==typeof t?function(e,t,r){"string"==typeof r&&""!==r||(r="utf8");if(!A.isEncoding(r))throw new TypeError('"encoding" must be a valid string encoding');var n=0|B(t,r),i=(e=a(e,n)).write(t,r);i!==n&&(e=e.slice(0,i));return e}(e,t,r):function(e,t){if(A.isBuffer(t)){var r=0|l(t.length);return 0===(e=a(e,r)).length?e:(t.copy(e,0,0,r),e)}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||(n=t.length)!=n?a(e,0):S(e,t);if("Buffer"===t.type&&o(t.data))return S(e,t.data)}var n;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,t)}function c(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function f(e,t){if(c(t),e=a(e,t<0?0:0|l(t)),!A.TYPED_ARRAY_SUPPORT)for(var r=0;r<t;++r)e[r]=0;return e}function S(e,t){var r=t.length<0?0:0|l(t.length);e=a(e,r);for(var n=0;n<r;n+=1)e[n]=255&t[n];return e}function l(e){if(e>=u())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+u().toString(16)+" bytes");return 0|e}function B(e,t){if(A.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var r=e.length;if(0===r)return 0;for(var n=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return H(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return W(e).length;default:if(n)return H(e).length;t=(""+t).toLowerCase(),n=!0}}function D(e,t,r){var n=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return m(this,t,r);case"utf8":case"utf-8":return g(this,t,r);case"ascii":return y(this,t,r);case"latin1":case"binary":return O(this,t,r);case"base64":return d(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return V(this,t,r);default:if(n)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),n=!0}}function P(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function C(e,t,r,n,i){if(0===e.length)return-1;if("string"==typeof r?(n=r,r=0):r>**********?r=**********:r<-2147483648&&(r=-2147483648),r=+r,isNaN(r)&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return-1;r=e.length-1}else if(r<0){if(!i)return-1;r=0}if("string"==typeof t&&(t=A.from(t,n)),A.isBuffer(t))return 0===t.length?-1:E(e,t,r,n,i);if("number"==typeof t)return t&=255,A.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):E(e,[t],r,n,i);throw new TypeError("val must be string, number or Buffer")}function E(e,t,r,n,i){var o,u=1,a=e.length,A=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return-1;u=2,a/=2,A/=2,r/=2}function s(e,t){return 1===u?e[t]:e.readUInt16BE(t*u)}if(i){var c=-1;for(o=r;o<a;o++)if(s(e,o)===s(t,-1===c?0:o-c)){if(-1===c&&(c=o),o-c+1===A)return c*u}else-1!==c&&(o-=o-c),c=-1}else for(r+A>a&&(r=a-A),o=r;o>=0;o--){for(var f=!0,S=0;S<A;S++)if(s(e,o+S)!==s(t,S)){f=!1;break}if(f)return o}return-1}function h(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=t.length;if(o%2!=0)throw new TypeError("Invalid hex string");n>o/2&&(n=o/2);for(var u=0;u<n;++u){var a=parseInt(t.substr(2*u,2),16);if(isNaN(a))return u;e[r+u]=a}return u}function F(e,t,r,n){return k(H(t,e.length-r),e,r,n)}function M(e,t,r,n){return k(function(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(t),e,r,n)}function p(e,t,r,n){return M(e,t,r,n)}function T(e,t,r,n){return k(W(t),e,r,n)}function R(e,t,r,n){return k(function(e,t){for(var r,n,i,o=[],u=0;u<e.length&&!((t-=2)<0);++u)r=e.charCodeAt(u),n=r>>8,i=r%256,o.push(i),o.push(n);return o}(t,e.length-r),e,r,n)}function d(e,t,r){return 0===t&&r===e.length?n.fromByteArray(e):n.fromByteArray(e.slice(t,r))}function g(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var o,u,a,A,s=e[i],c=null,f=s>239?4:s>223?3:s>191?2:1;if(i+f<=r)switch(f){case 1:s<128&&(c=s);break;case 2:128==(192&(o=e[i+1]))&&(A=(31&s)<<6|63&o)>127&&(c=A);break;case 3:o=e[i+1],u=e[i+2],128==(192&o)&&128==(192&u)&&(A=(15&s)<<12|(63&o)<<6|63&u)>2047&&(A<55296||A>57343)&&(c=A);break;case 4:o=e[i+1],u=e[i+2],a=e[i+3],128==(192&o)&&128==(192&u)&&128==(192&a)&&(A=(15&s)<<18|(63&o)<<12|(63&u)<<6|63&a)>65535&&A<1114112&&(c=A)}null===c?(c=65533,f=1):c>65535&&(c-=65536,n.push(c>>>10&1023|55296),c=56320|1023&c),n.push(c),i+=f}return function(e){var t=e.length;if(t<=v)return String.fromCharCode.apply(String,e);var r="",n=0;for(;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=v));return r}(n)}t.Buffer=A,t.SlowBuffer=function(e){+e!=e&&(e=0);return A.alloc(+e)},t.INSPECT_MAX_BYTES=50,A.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}(),t.kMaxLength=u(),A.poolSize=8192,A._augment=function(e){return e.__proto__=A.prototype,e},A.from=function(e,t,r){return s(null,e,t,r)},A.TYPED_ARRAY_SUPPORT&&(A.prototype.__proto__=Uint8Array.prototype,A.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&A[Symbol.species]===A&&Object.defineProperty(A,Symbol.species,{value:null,configurable:!0})),A.alloc=function(e,t,r){return function(e,t,r,n){return c(t),t<=0?a(e,t):void 0!==r?"string"==typeof n?a(e,t).fill(r,n):a(e,t).fill(r):a(e,t)}(null,e,t,r)},A.allocUnsafe=function(e){return f(null,e)},A.allocUnsafeSlow=function(e){return f(null,e)},A.isBuffer=function(e){return!(null==e||!e._isBuffer)},A.compare=function(e,t){if(!A.isBuffer(e)||!A.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var r=e.length,n=t.length,i=0,o=Math.min(r,n);i<o;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:n<r?1:0},A.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},A.concat=function(e,t){if(!o(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return A.alloc(0);var r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;var n=A.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var u=e[r];if(!A.isBuffer(u))throw new TypeError('"list" argument must be an Array of Buffers');u.copy(n,i),i+=u.length}return n},A.byteLength=B,A.prototype._isBuffer=!0,A.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)P(this,t,t+1);return this},A.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)P(this,t,t+3),P(this,t+1,t+2);return this},A.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)P(this,t,t+7),P(this,t+1,t+6),P(this,t+2,t+5),P(this,t+3,t+4);return this},A.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?g(this,0,e):D.apply(this,arguments)},A.prototype.equals=function(e){if(!A.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===A.compare(this,e)},A.prototype.inspect=function(){var e="",r=t.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,r).match(/.{2}/g).join(" "),this.length>r&&(e+=" ... ")),"<Buffer "+e+">"},A.prototype.compare=function(e,t,r,n,i){if(!A.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return-1;if(t>=r)return 1;if(this===e)return 0;for(var o=(i>>>=0)-(n>>>=0),u=(r>>>=0)-(t>>>=0),a=Math.min(o,u),s=this.slice(n,i),c=e.slice(t,r),f=0;f<a;++f)if(s[f]!==c[f]){o=s[f],u=c[f];break}return o<u?-1:u<o?1:0},A.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},A.prototype.indexOf=function(e,t,r){return C(this,e,t,r,!0)},A.prototype.lastIndexOf=function(e,t,r){return C(this,e,t,r,!1)},A.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(r)?(r|=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var i=this.length-t;if((void 0===r||r>i)&&(r=i),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var o=!1;;)switch(n){case"hex":return h(this,e,t,r);case"utf8":case"utf-8":return F(this,e,t,r);case"ascii":return M(this,e,t,r);case"latin1":case"binary":return p(this,e,t,r);case"base64":return T(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return R(this,e,t,r);default:if(o)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),o=!0}},A.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var v=4096;function y(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}function O(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}function m(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=t;o<r;++o)i+=X(e[o]);return i}function V(e,t,r){for(var n=e.slice(t,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}function U(e,t,r){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function N(e,t,r,n,i,o){if(!A.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<o)throw new RangeError('"value" argument is out of bounds');if(r+n>e.length)throw new RangeError("Index out of range")}function L(e,t,r,n){t<0&&(t=65535+t+1);for(var i=0,o=Math.min(e.length-r,2);i<o;++i)e[r+i]=(t&255<<8*(n?i:1-i))>>>8*(n?i:1-i)}function I(e,t,r,n){t<0&&(t=4294967295+t+1);for(var i=0,o=Math.min(e.length-r,4);i<o;++i)e[r+i]=t>>>8*(n?i:3-i)&255}function w(e,t,r,n,i,o){if(r+n>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function b(e,t,r,n,o){return o||w(e,0,r,4),i.write(e,t,r,n,23,4),r+4}function x(e,t,r,n,o){return o||w(e,0,r,8),i.write(e,t,r,n,52,8),r+8}A.prototype.slice=function(e,t){var r,n=this.length;if((e=~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),(t=void 0===t?n:~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),t<e&&(t=e),A.TYPED_ARRAY_SUPPORT)(r=this.subarray(e,t)).__proto__=A.prototype;else{var i=t-e;r=new A(i,void 0);for(var o=0;o<i;++o)r[o]=this[o+e]}return r},A.prototype.readUIntLE=function(e,t,r){e|=0,t|=0,r||U(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n},A.prototype.readUIntBE=function(e,t,r){e|=0,t|=0,r||U(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},A.prototype.readUInt8=function(e,t){return t||U(e,1,this.length),this[e]},A.prototype.readUInt16LE=function(e,t){return t||U(e,2,this.length),this[e]|this[e+1]<<8},A.prototype.readUInt16BE=function(e,t){return t||U(e,2,this.length),this[e]<<8|this[e+1]},A.prototype.readUInt32LE=function(e,t){return t||U(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},A.prototype.readUInt32BE=function(e,t){return t||U(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},A.prototype.readIntLE=function(e,t,r){e|=0,t|=0,r||U(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},A.prototype.readIntBE=function(e,t,r){e|=0,t|=0,r||U(e,t,this.length);for(var n=t,i=1,o=this[e+--n];n>0&&(i*=256);)o+=this[e+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*t)),o},A.prototype.readInt8=function(e,t){return t||U(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},A.prototype.readInt16LE=function(e,t){t||U(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},A.prototype.readInt16BE=function(e,t){t||U(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},A.prototype.readInt32LE=function(e,t){return t||U(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},A.prototype.readInt32BE=function(e,t){return t||U(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},A.prototype.readFloatLE=function(e,t){return t||U(e,4,this.length),i.read(this,e,!0,23,4)},A.prototype.readFloatBE=function(e,t){return t||U(e,4,this.length),i.read(this,e,!1,23,4)},A.prototype.readDoubleLE=function(e,t){return t||U(e,8,this.length),i.read(this,e,!0,52,8)},A.prototype.readDoubleBE=function(e,t){return t||U(e,8,this.length),i.read(this,e,!1,52,8)},A.prototype.writeUIntLE=function(e,t,r,n){(e=+e,t|=0,r|=0,n)||N(this,e,t,r,Math.pow(2,8*r)-1,0);var i=1,o=0;for(this[t]=255&e;++o<r&&(i*=256);)this[t+o]=e/i&255;return t+r},A.prototype.writeUIntBE=function(e,t,r,n){(e=+e,t|=0,r|=0,n)||N(this,e,t,r,Math.pow(2,8*r)-1,0);var i=r-1,o=1;for(this[t+i]=255&e;--i>=0&&(o*=256);)this[t+i]=e/o&255;return t+r},A.prototype.writeUInt8=function(e,t,r){return e=+e,t|=0,r||N(this,e,t,1,255,0),A.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},A.prototype.writeUInt16LE=function(e,t,r){return e=+e,t|=0,r||N(this,e,t,2,65535,0),A.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):L(this,e,t,!0),t+2},A.prototype.writeUInt16BE=function(e,t,r){return e=+e,t|=0,r||N(this,e,t,2,65535,0),A.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):L(this,e,t,!1),t+2},A.prototype.writeUInt32LE=function(e,t,r){return e=+e,t|=0,r||N(this,e,t,4,4294967295,0),A.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):I(this,e,t,!0),t+4},A.prototype.writeUInt32BE=function(e,t,r){return e=+e,t|=0,r||N(this,e,t,4,4294967295,0),A.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):I(this,e,t,!1),t+4},A.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);N(this,e,t,r,i-1,-i)}var o=0,u=1,a=0;for(this[t]=255&e;++o<r&&(u*=256);)e<0&&0===a&&0!==this[t+o-1]&&(a=1),this[t+o]=(e/u>>0)-a&255;return t+r},A.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);N(this,e,t,r,i-1,-i)}var o=r-1,u=1,a=0;for(this[t+o]=255&e;--o>=0&&(u*=256);)e<0&&0===a&&0!==this[t+o+1]&&(a=1),this[t+o]=(e/u>>0)-a&255;return t+r},A.prototype.writeInt8=function(e,t,r){return e=+e,t|=0,r||N(this,e,t,1,127,-128),A.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},A.prototype.writeInt16LE=function(e,t,r){return e=+e,t|=0,r||N(this,e,t,2,32767,-32768),A.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):L(this,e,t,!0),t+2},A.prototype.writeInt16BE=function(e,t,r){return e=+e,t|=0,r||N(this,e,t,2,32767,-32768),A.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):L(this,e,t,!1),t+2},A.prototype.writeInt32LE=function(e,t,r){return e=+e,t|=0,r||N(this,e,t,4,**********,-2147483648),A.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):I(this,e,t,!0),t+4},A.prototype.writeInt32BE=function(e,t,r){return e=+e,t|=0,r||N(this,e,t,4,**********,-2147483648),e<0&&(e=4294967295+e+1),A.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):I(this,e,t,!1),t+4},A.prototype.writeFloatLE=function(e,t,r){return b(this,e,t,!0,r)},A.prototype.writeFloatBE=function(e,t,r){return b(this,e,t,!1,r)},A.prototype.writeDoubleLE=function(e,t,r){return x(this,e,t,!0,r)},A.prototype.writeDoubleBE=function(e,t,r){return x(this,e,t,!1,r)},A.prototype.copy=function(e,t,r,n){if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i,o=n-r;if(this===e&&r<t&&t<n)for(i=o-1;i>=0;--i)e[i+t]=this[i+r];else if(o<1e3||!A.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)e[i+t]=this[i+r];else Uint8Array.prototype.set.call(e,this.subarray(r,r+o),t);return o},A.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),1===e.length){var i=e.charCodeAt(0);i<256&&(e=i)}if(void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!A.isEncoding(n))throw new TypeError("Unknown encoding: "+n)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw new RangeError("Out of range index");if(r<=t)return this;var o;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(o=t;o<r;++o)this[o]=e;else{var u=A.isBuffer(e)?e:H(new A(e,n).toString()),a=u.length;for(o=0;o<r-t;++o)this[o+t]=u[o%a]}return this};var Q=/[^+\/0-9A-Za-z-_]/g;function X(e){return e<16?"0"+e.toString(16):e.toString(16)}function H(e,t){var r;t=t||1/0;for(var n=e.length,i=null,o=[],u=0;u<n;++u){if((r=e.charCodeAt(u))>55295&&r<57344){if(!i){if(r>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(u+1===n){(t-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&o.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;o.push(r)}else if(r<2048){if((t-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function W(e){return n.toByteArray(function(e){if((e=function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}(e).replace(Q,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function k(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length||i>=e.length);++i)t[i+r]=e[i];return i}}).call(this,r(28))},13:function(e,t,r){var n=r(49),i=r(50),o=r(51);e.exports=function(e,t){return n(e)||i(e,t)||o()}},1341:function(e,t,r){"use strict";r.r(t);var n=r(13),i=r.n(n),o=r(1),u=r.n(o),a=r(2),A=r.n(a),s=r(4),c=r.n(s),f=r(3),S=r.n(f),l=r(5),B=r.n(l),D=r(6),P=[],C=2,E=0,h=0,F=0,M=0,p="",T="",R=!0,d=!0,g=0,v=["ADD","ADD","ADD","ADD","ADD","ADD","PUSH ES","POP ES","OR","OR","OR","OR","OR","OR","PUSH CS","","ADC","ADC","ADC","ADC","ADC","ADC","PUSH SS","POP SS","SBB","SBB","SBB","SBB","SBB","SBB","PUSH DS","POP DS","AND","AND","AND","AND","AND","AND","ES:[","DAA","SUB","SUB","SUB","SUB","SUB","SUB","CS:[","DAS","XOR","XOR","XOR","XOR","XOR","XOR","SS:[","AAA","CMP","CMP","CMP","CMP","CMP","CMP","DS:[","AAS","INC","INC","INC","INC","INC","INC","INC","INC","DEC","DEC","DEC","DEC","DEC","DEC","DEC","DEC","PUSH","PUSH","PUSH","PUSH","PUSH","PUSH","PUSH","PUSH","POP","POP","POP","POP","POP","POP","POP","POP",["PUSHA","PUSHAD",""],["POPA","POPAD",""],["BOUND","BOUND",""],"MOVSXD","FS:[","GS:[","","","PUSH","IMUL","PUSH","IMUL","INS","INS","OUTS","OUTS","JO","JNO","JB","JAE","JE","JNE","JBE","JA","JS","JNS","JP","JNP","JL","JGE","JLE","JG",["ADD","OR","ADC","SBB","AND","SUB","XOR","CMP"],["ADD","OR","ADC","SBB","AND","SUB","XOR","CMP"],["ADD","OR","ADC","SBB","AND","SUB","XOR","CMP"],["ADD","OR","ADC","SBB","AND","SUB","XOR","CMP"],"TEST","TEST","XCHG","XCHG","MOV","MOV","MOV","MOV","MOV",["LEA","???"],"MOV",["POP","???","???","???","???","???","???","???"],[["NOP","","",""],["NOP","","",""],["PAUSE","","",""],["NOP","","",""]],"XCHG","XCHG","XCHG","XCHG","XCHG","XCHG","XCHG",["CWDE","CBW","CDQE"],["CDQ","CWD","CQO"],"CALL","WAIT",["PUSHFQ","PUSHF","PUSHFQ"],["POPFQ","POPF","POPFQ"],"SAHF","LAHF","MOV","MOV","MOV","MOV","MOVS","MOVS","CMPS","CMPS","TEST","TEST","STOS","STOS","LODS","LODS","SCAS","SCAS","MOV","MOV","MOV","MOV","MOV","MOV","MOV","MOV","MOV","MOV","MOV","MOV","MOV","MOV","MOV","MOV",["ROL","ROR","RCL","RCR","SHL","SHR","SAL","SAR"],["ROL","ROR","RCL","RCR","SHL","SHR","SAL","SAR"],"RET","RET","LES","LDS",["MOV","???","???","???","???","???","???",["XABORT","XABORT","XABORT","XABORT","XABORT","XABORT","XABORT","XABORT"]],["MOV","???","???","???","???","???","???",["XBEGIN","XBEGIN","XBEGIN","XBEGIN","XBEGIN","XBEGIN","XBEGIN","XBEGIN"]],"ENTER","LEAVE","RETF","RETF","INT","INT","INTO",["IRETD","IRET","IRETQ"],["ROL","ROR","RCL","RCR","SHL","SHR","SAL","SAR"],["ROL","ROR","RCL","RCR","SHL","SHR","SAL","SAR"],["ROL","ROR","RCL","RCR","SHL","SHR","SAL","SAR"],["ROL","ROR","RCL","RCR","SHL","SHR","SAL","SAR"],"AAMB","AADB","???","XLAT",[["FADD","FMUL","FCOM","FCOMP","FSUB","FSUBR","FDIV","FDIVR"],["FADD","FMUL","FCOM","FCOMP","FSUB","FSUBR","FDIV","FDIVR"]],[["FLD","???","FST","FSTP","FLDENV","FLDCW","FNSTENV","FNSTCW"],["FLD","FXCH",["FNOP","???","???","???","???","???","???","???"],"FSTP1",["FCHS","FABS","???","???","FTST","FXAM","???","???"],["FLD1","FLDL2T","FLDL2E","FLDPI","FLDLG2","FLDLN2","FLDZ","???"],["F2XM1","FYL2X","FPTAN","FPATAN","FXTRACT","FPREM1","FDECSTP","FINCSTP"],["FPREM","FYL2XP1","FSQRT","FSINCOS","FRNDINT","FSCALE","FSIN","FCOS"]]],[["FIADD","FIMUL","FICOM","FICOMP","FISUB","FISUBR","FIDIV","FIDIVR"],["FCMOVB","FCMOVE","FCMOVBE","FCMOVU","???",["???","FUCOMPP","???","???","???","???","???","???"],"???","???"]],[["FILD","FISTTP","FIST","FISTP","???","FLD","???","FSTP"],["CMOVNB","FCMOVNE","FCMOVNBE","FCMOVNU",["FENI","FDISI","FNCLEX","FNINIT","FSETPM","???","???","???"],"FUCOMI","FCOMI","???"]],[["FADD","FMUL","FCOM","DCOMP","FSUB","FSUBR","FDIV","FDIVR"],["FADD","FMUL","FCOM2","FCOMP3","FSUBR","FSUB","FDIVR","FDIV"]],[["FLD","FISTTP","FST","FSTP","FRSTOR","???","FNSAVE","FNSTSW"],["FFREE","FXCH4","FST","FSTP","FUCOM","FUCOMP","???","???"]],[["FIADD","FIMUL","FICOM","FICOMP","FISUB","FISUBR","FIDIV","FIDIVR"],["FADDP","FMULP","FCOMP5",["???","FCOMPP","???","???","???","???","???","???"],"FSUBRP","FSUBP","FDIVRP","FDIVP"]],[["FILD","FISTTP","FIST","FISTP","FBLD","FILD","FBSTP","FISTP"],["FFREEP","FXCH7","FSTP8","FSTP9",["FNSTSW","???","???","???","???","???","???","???"],"FUCOMIP","FCOMIP","???"]],"LOOPNE","LOOPE","LOOP","JRCXZ","IN","IN","OUT","OUT","CALL","JMP","JMP","JMP","IN","IN","OUT","OUT","LOCK","ICEBP","REPNE","REP","HLT","CMC",["TEST","???","NOT","NEG","MUL","IMUL","DIV","IDIV"],["TEST","???","NOT","NEG","MUL","IMUL","DIV","IDIV"],"CLC","STC","CLI","STI","CLD","STD",["INC","DEC","???","???","???","???","???","???"],[["INC","DEC","CALL","CALL","JMP","JMP","PUSH","???"],["INC","DEC","CALL","???","JMP","???","PUSH","???"]],[["SLDT","STR","LLDT","LTR","VERR","VERW","JMPE","???"],["SLDT","STR","LLDT","LTR","VERR","VERW","JMPE","???"]],[["SGDT","SIDT","LGDT","LIDT","SMSW","???","LMSW","INVLPG"],[["???","VMCALL","VMLAUNCH","VMRESUME","VMXOFF","???","???","???"],["MONITOR","MWAIT","CLAC","STAC","???","???","???","ENCLS"],["XGETBV","XSETBV","???","???","VMFUNC","XEND","XTEST","ENCLU"],["VMRUN","VMMCALL","VMLOAD","VMSAVE","STGI","CLGI","SKINIT","INVLPGA"],"SMSW","???","LMSW",["SWAPGS","RDTSCP","MONITORX","MWAITX","???","???","???","???"]]],["LAR","LAR"],["LSL","LSL"],"???","SYSCALL","CLTS","SYSRET","INVD","WBINVD","???","UD2","???",[["PREFETCH","PREFETCHW","???","???","???","???","???","???"],"???"],"FEMMS","",[["MOVUPS","MOVUPD","MOVSS","MOVSD"],["MOVUPS","MOVUPD","MOVSS","MOVSD"]],[["MOVUPS","MOVUPD","MOVSS","MOVSD"],["MOVUPS","MOVUPD","MOVSS","MOVSD"]],[["MOVLPS","MOVLPD","MOVSLDUP","MOVDDUP"],["MOVHLPS","???","MOVSLDUP","MOVDDUP"]],[["MOVLPS","MOVLPD","???","???"],"???"],["UNPCKLPS","UNPCKLPD","???","???"],["UNPCKHPS","UNPCKHPD","???","???"],[["MOVHPS","MOVHPD","MOVSHDUP","???"],["MOVLHPS","???","MOVSHDUP","???"]],[["MOVHPS","MOVHPD","???","???"],"???"],[["PREFETCHNTA","PREFETCHT0","PREFETCHT1","PREFETCHT2","???","???","???","???"],"???"],"???",[[["BNDLDX","","",""],["BNDMOV","","",""],["BNDCL","","",""],["BNDCU","","",""]],["???",["BNDMOV","","",""],["BNDCL","","",""],["BNDCU","","",""]]],[[["BNDSTX","","",""],["BNDMOV","","",""],["BNDMK","","",""],["BNDCN","","",""]],["???",["BNDMOV","","",""],"???",["BNDCN","","",""]]],"???","???","???","NOP",["???","MOV"],["???","MOV"],["???","MOV"],["???","MOV"],["???","MOV"],"???",["???","MOV"],"???",[["MOVAPS","MOVAPS","MOVAPS","MOVAPS"],["MOVAPD","MOVAPD","MOVAPD","MOVAPD"],"???","???"],[[["MOVAPS","MOVAPS","MOVAPS","MOVAPS"],["MOVAPD","MOVAPD","MOVAPD","MOVAPD"],["","","",["MOVNRAPS","MOVNRNGOAPS","MOVNRAPS"]],["","","",["MOVNRAPD","MOVNRNGOAPD","MOVNRAPD"]]],[["MOVAPS","MOVAPS","MOVAPS","MOVAPS"],["MOVAPD","MOVAPD","MOVAPD","MOVAPD"],"???","???"]],[["CVTPI2PS","","",""],["CVTPI2PD","","",""],"CVTSI2SS","CVTSI2SD"],[["MOVNTPS","MOVNTPD",["MOVNTSS","","",""],["MOVNTSD","","",""]],"???"],[["CVTTPS2PI","","",""],["CVTTPD2PI","","",""],"CVTTSS2SI","CVTTSD2SI"],[["CVTPS2PI","","",""],["CVTPD2PI","","",""],"CVTSS2SI","CVTSD2SI"],["UCOMISS","UCOMISD","???","???"],["COMISS","COMISD","???","???"],"WRMSR","RDTSC","RDMSR","RDPMC","SYSENTER","SYSEXIT","???","GETSEC","","???","","???","???","???","???","???","CMOVO",[["CMOVNO",["KANDW","","KANDQ"],"",""],["CMOVNO",["KANDB","","KANDD"],"",""],"",""],[["CMOVB",["KANDNW","","KANDNQ"],"",""],["CMOVB",["KANDNB","","KANDND"],"",""],"",""],[["CMOVAE","KANDNR","",""],"","",""],[["CMOVE",["KNOTW","","KNOTQ"],"",""],["CMOVE",["KNOTB","","KNOTD"],"",""],"",""],[["CMOVNE",["KORW","","KORQ"],"",""],["CMOVNE",["KORB","","KORD"],"",""],"",""],[["CMOVBE",["KXNORW","","KXNORQ"],"",""],["CMOVBE",["KXNORB","","KXNORD"],"",""],"",""],[["CMOVA",["KXORW","","KXORQ"],"",""],["CMOVA",["KXORB","","KXORD"],"",""],"",""],[["CMOVS","KMERGE2L1H","",""],"","",""],[["CMOVNS","KMERGE2L1L","",""],"","",""],[["CMOVP",["KADDW","","KADDQ"],"",""],["CMOVP",["KADDB","","KADDD"],"",""],"",""],[["CMOVNP",["KUNPCKWD","","KUNPCKDQ"],"",""],["CMOVNP",["KUNPCKBW","","???"],"",""],"",""],"CMOVL","CMOVGE","CMOVLE","CMOVG",["???",[["MOVMSKPS","MOVMSKPS","",""],["MOVMSKPD","MOVMSKPD","",""],"???","???"]],["SQRTPS","SQRTPD","SQRTSS","SQRTSD"],[["RSQRTPS","RSQRTPS","",""],"???",["RSQRTSS","RSQRTSS","",""],"???"],[["RCPPS","RCPPS","",""],"???",["RCPSS","RCPSS","",""],"???"],["ANDPS","ANDPD","???","???"],["ANDNPS","ANDNPD","???","???"],["ORPS","ORPD","???","???"],["XORPS","XORPD","???","???"],[["ADDPS","ADDPS","ADDPS","ADDPS"],["ADDPD","ADDPD","ADDPD","ADDPD"],"ADDSS","ADDSD"],[["MULPS","MULPS","MULPS","MULPS"],["MULPD","MULPD","MULPD","MULPD"],"MULSS","MULSD"],[["CVTPS2PD","CVTPS2PD","CVTPS2PD","CVTPS2PD"],["CVTPD2PS","CVTPD2PS","CVTPD2PS","CVTPD2PS"],"CVTSS2SD","CVTSD2SS"],[["CVTDQ2PS","","CVTQQ2PS"],["CVTPS2DQ","","???"],"CVTTPS2DQ","???"],[["SUBPS","SUBPS","SUBPS","SUBPS"],["SUBPD","SUBPD","SUBPD","SUBPD"],"SUBSS","SUBSD"],["MINPS","MINPD","MINSS","MINSD"],["DIVPS","DIVPD","DIVSS","DIVSD"],["MAXPS","MAXPD","MAXSS","MAXSD"],[["PUNPCKLBW","","",""],"PUNPCKLBW","",""],[["PUNPCKLWD","","",""],"PUNPCKLWD","",""],[["PUNPCKLDQ","","",""],"PUNPCKLDQ","",""],[["PACKSSWB","","",""],"PACKSSWB","",""],[["PCMPGTB","","",""],["PCMPGTB","PCMPGTB","PCMPGTB",""],"",""],[["PCMPGTW","","",""],["PCMPGTW","PCMPGTW","PCMPGTW",""],"",""],[["PCMPGTD","","",""],["PCMPGTD","PCMPGTD",["PCMPGTD","","???"],["PCMPGTD","","???"]],"",""],[["PACKUSWB","","",""],"PACKUSWB","",""],[["PUNPCKHBW","","",""],"PUNPCKHBW","",""],[["PUNPCKHWD","","",""],"PUNPCKHWD","",""],[["PUNPCKHDQ","","",""],["PUNPCKHDQ","","???"],"",""],[["PACKSSDW","","",""],["PACKSSDW","","???"],"",""],["???","PUNPCKLQDQ","???","???"],["???","PUNPCKHQDQ","???","???"],[["MOVD","","",""],["MOVD","","MOVQ"],"",""],[[["MOVQ","","",""],["MOVDQA","MOVDQA",["MOVDQA32","","MOVDQA64"],["MOVDQA32","","MOVDQA64"]],["MOVDQU","MOVDQU",["MOVDQU32","","MOVDQU64"],""],["","",["MOVDQU8","","MOVDQU16"],""]],[["MOVQ","","",""],["MOVDQA","MOVDQA",["MOVDQA32","","MOVDQA64"],["MOVDQA32","","MOVDQA64"]],["MOVDQU","MOVDQU",["MOVDQU32","","MOVDQU64"],""],["","",["MOVDQU8","","MOVDQU16"],""]]],[["PSHUFW","","",""],["PSHUFD","PSHUFD",["PSHUFD","","???"],["PSHUFD","","???"]],"PSHUFHW","PSHUFLW"],["???",["???","???",[["PSRLW","","",""],"PSRLW","",""],"???",[["PSRAW","","",""],"PSRAW","",""],"???",[["PSLLW","","",""],"PSLLW","",""],"???"]],[["???",["","",["PRORD","","PRORQ"],""],"???","???"],["???",["","",["PROLD","","PROLQ"],""],"???","???"],[["PSRLD","","",""],["PSRLD","PSRLD",["PSRLD","","???"],["PSRLD","","???"]],"",""],"???",[["PSRAD","","",""],["PSRAD","PSRAD",["PSRAD","","PSRAQ"],["PSRAD","","???"]],"",""],"???",[["PSLLD","","",""],["PSLLD","PSLLD",["PSLLD","","???"],["PSLLD","","???"]],"",""],"???"],["???",["???","???",[["PSRLQ","PSRLQ","",""],"PSRLQ","",""],["???","PSRLDQ","???","???"],"???","???",[["PSLLQ","PSLLQ","",""],"PSLLQ","",""],["???","PSLLDQ","???","???"]]],[["PCMPEQB","","",""],["PCMPEQB","PCMPEQB","PCMPEQB",""],"",""],[["PCMPEQW","","",""],["PCMPEQW","PCMPEQW","PCMPEQW",""],"",""],[["PCMPEQD","","",""],["PCMPEQD","PCMPEQD",["PCMPEQD","","???"],["PCMPEQD","","???"]],"",""],[["EMMS",["ZEROUPPER","ZEROALL",""],"",""],"???","???","???"],[["VMREAD","",["CVTTPS2UDQ","","CVTTPD2UDQ"],""],["EXTRQ","",["CVTTPS2UQQ","","CVTTPD2UQQ"],""],["???","","CVTTSS2USI",""],["INSERTQ","","CVTTSD2USI",""]],[["VMWRITE","",["CVTPS2UDQ","","CVTPD2UDQ"],""],["EXTRQ","",["CVTPS2UQQ","","CVTPD2UQQ"],""],["???","","CVTSS2USI",""],["INSERTQ","","CVTSD2USI",""]],["???",["","",["CVTTPS2QQ","","CVTTPD2QQ"],""],["","",["CVTUDQ2PD","","CVTUQQ2PD"],"CVTUDQ2PD"],["","",["CVTUDQ2PS","","CVTUQQ2PS"],""]],["???",["","",["CVTPS2QQ","","CVTPD2QQ"],""],["","","CVTUSI2SS",""],["","","CVTUSI2SD",""]],["???",["HADDPD","HADDPD","",""],"???",["HADDPS","HADDPS","",""]],["???",["HSUBPD","HSUBPD","",""],"???",["HSUBPS","HSUBPS","",""]],[["MOVD","","",""],["MOVD","","MOVQ"],["MOVQ","MOVQ",["???","","MOVQ"],""],"???"],[["MOVQ","","",""],["MOVDQA","MOVDQA",["MOVDQA32","","MOVDQA64"],["MOVDQA32","","MOVDQA64"]],["MOVDQU","MOVDQU",["MOVDQU32","","MOVDQU64"],""],["???","",["MOVDQU8","","MOVDQU16"],""]],"JO","JNO","JB","JAE",[["JE","JKZD","",""],"","",""],[["JNE","JKNZD","",""],"","",""],"JBE","JA","JS","JNS","JP","JNP","JL","JGE","JLE","JG",[["SETO",["KMOVW","","KMOVQ"],"",""],["SETO",["KMOVB","","KMOVD"],"",""],"",""],[["SETNO",["KMOVW","","KMOVQ"],"",""],["SETNO",["KMOVB","","KMOVD"],"",""],"",""],[["SETB",["KMOVW","","???"],"",""],["SETB",["KMOVB","","???"],"",""],"",["SETB",["KMOVD","","KMOVQ"],"",""]],[["SETAE",["KMOVW","","???"],"",""],["SETAE",["KMOVB","","???"],"",""],"",["SETAE",["KMOVD","","KMOVQ"],"",""]],"SETE",[["SETNE","KCONCATH","",""],"","",""],"SETBE",[["SETA","KCONCATL","",""],"","",""],[["SETS",["KORTESTW","","KORTESTQ"],"",""],["SETS",["KORTESTB","","KORTESTD"],"",""],"",""],[["SETNS",["KTESTW","","KTESTQ"],"",""],["SETNS",["KTESTB","","KTESTD"],"",""],"",""],"SETP","SETNP","SETL","SETGE","SETLE","SETG","PUSH","POP","CPUID","BT","SHLD","SHLD","XBTS","IBTS","PUSH","POP","RSM","BTS","SHRD","SHRD",[[["FXSAVE","???","FXSAVE64"],["FXRSTOR","???","FXRSTOR64"],"LDMXCSR","STMXCSR",["XSAVE","","XSAVE64"],["XRSTOR","","XRSTOR64"],["XSAVEOPT","CLWB","XSAVEOPT64"],["CLFLUSHOPT","CLFLUSH",""]],[["???","???",["RDFSBASE","","",""],"???"],["???","???",["RDGSBASE","","",""],"???"],["???","???",["WRFSBASE","","",""],"???"],["???","???",["WRGSBASE","","",""],"???"],"???",["LFENCE","???","???","???","???","???","???","???"],["MFENCE","???","???","???","???","???","???","???"],["SFENCE","???","???","???","???","???","???","???"]]],"IMUL","CMPXCHG","CMPXCHG",["LSS","???"],"BTR",["LFS","???"],["LGS","???"],"MOVZX","MOVZX",[["JMPE","","",""],"???",["POPCNT","POPCNT","",""],"???"],"???",["???","???","???","???","BT","BTS","BTR","BTC"],"BTC",[["BSF","","",""],"???",["TZCNT","TZCNT","",""],["BSF","TZCNTI","",""]],[["BSR","","",""],"???",["LZCNT","LZCNT","",""],["BSR","","",""]],"MOVSX","MOVSX","XADD","XADD",[["CMP,PS,","CMP,PS,","CMP,PS,","CMP,PS,"],["CMP,PD,","CMP,PD,","CMP,PD,","CMP,PD,"],["CMP,SS,","CMP,SS,","CMP,SS,",""],["CMP,SD,","CMP,SD,","CMP,SD,",""]],["MOVNTI","???"],[["PINSRW","","",""],"PINSRW","",""],["???",[["PEXTRW","","",""],"PEXTRW","",""]],["SHUFPS","SHUFPD","???","???"],[["???",["CMPXCHG8B","","CMPXCHG16B"],"???",["XRSTORS","","XRSTORS64"],["XSAVEC","","XSAVEC64"],["XSAVES","","XSAVES64"],["VMPTRLD","VMCLEAR","VMXON","???"],["VMPTRST","???","???","???"]],["???",["SSS","???","???","???","???","???","???","???"],"???","???","???","???","RDRAND","RDSEED"]],"BSWAP","BSWAP","BSWAP","BSWAP","BSWAP","BSWAP","BSWAP","BSWAP",["???",["ADDSUBPD","ADDSUBPD","",""],"???",["ADDSUBPS","ADDSUBPS","",""]],[["PSRLW","","",""],"PSRLW","",""],[["PSRLD","","",""],["PSRLD","PSRLD",["PSRLD","","???"],""],"",""],[["PSRLQ","","",""],"PSRLQ","",""],[["PADDQ","","",""],"PADDQ","",""],[["PMULLW","","",""],"PMULLW","",""],[["???","MOVQ","???","???"],["???","MOVQ",["MOVQ2DQ","","",""],["MOVDQ2Q","","",""]]],["???",[["PMOVMSKB","","",""],["PMOVMSKB","PMOVMSKB","",""],"???","???"]],[["PSUBUSB","","",""],"PSUBUSB","",""],[["PSUBUSW","","",""],"PSUBUSW","",""],[["PMINUB","","",""],"PMINUB","",""],[["PAND","","",""],["PAND","PAND",["PANDD","","PANDQ"],["PANDD","","PANDQ"]],"",""],[["PADDUSB","","",""],"PADDUSB","",""],[["PADDUSW","","",""],"PADDUSW","",""],[["PMAXUB","","",""],"PMAXUB","",""],[["PANDN","","",""],["PANDN","PANDN",["PANDND","","PANDNQ"],["PANDND","","PANDNQ"]],"",""],[["PAVGB","","",""],"PAVGB","",""],[[["PSRAW","","",""],["PSRAW","PSRAW","PSRAW",""],"",""],[["PSRAW","","",""],["PSRAW","PSRAW","PSRAW",""],"",""]],[["PSRAD","","",""],["PSRAD","PSRAD",["PSRAD","","PSRAQ"],""],"",""],[["PAVGW","","",""],"PAVGW","",""],[["PMULHUW","","",""],"PMULHUW","",""],[["PMULHW","","",""],"PMULHW","",""],["???",["CVTTPD2DQ","CVTTPD2DQ","CVTTPD2DQ",""],["CVTDQ2PD","CVTDQ2PD",["CVTDQ2PD","CVTDQ2PD","CVTQQ2PD"],"CVTDQ2PD"],"CVTPD2DQ"],[[["MOVNTQ","","",""],["MOVNTDQ","","???"],"???","???"],"???"],[["PSUBSB","","",""],"PSUBSB","",""],[["PSUBSW","","",""],"PSUBSW","",""],[["PMINSW","","",""],"PMINSW","",""],[["POR","","",""],["POR","POR",["PORD","","PORQ"],["PORD","","PORQ"]],"",""],[["PADDSB","","",""],"PADDSB","",""],[["PADDSW","","",""],"PADDSW","",""],[["PMAXSW","","",""],"PMAXSW","",""],[["PXOR","","",""],["PXOR","PXOR",["PXORD","","PXORQ"],["PXORD","","PXORQ"]],"",""],[["???","???","???",["LDDQU","LDDQU","",""]],"???"],[["PSLLW","","",""],"PSLLW","",""],[["PSLLD","","",""],["PSLLD","","???"],"",""],[["PSLLQ","","",""],"PSLLQ","",""],[["PMULUDQ","","",""],"PMULUDQ","",""],[["PMADDWD","","",""],"PMADDWD","",""],[["PSADBW","","",""],"PSADBW","",""],["???",[["MASKMOVQ","","",""],["MASKMOVDQU","MASKMOVDQU","",""],"???","???"]],[["PSUBB","","",""],"PSUBB","",""],[["PSUBW","","",""],"PSUBW","",""],[["PSUBD","","",""],["PSUBD","PSUBD",["PSUBD","","???"],["PSUBD","","???"]],"",""],[["PSUBQ","","",""],"PSUBQ","",""],[["PADDB","","",""],"PADDB","",""],[["PADDW","","",""],"PADDW","",""],[["PADDD","","",""],["PADDD","PADDD",["PADDD","","???"],["PADDD","","???"]],"",""],"???",[["PSHUFB","","",""],"PSHUFB","???","???"],[["PHADDW","","",""],["PHADDW","PHADDW","",""],"???","???"],[["PHADDD","","",""],["PHADDD","PHADDD","",""],"???","???"],[["PHADDSW","","",""],["PHADDSW","PHADDSW","",""],"???","???"],[["PMADDUBSW","","",""],"PMADDUBSW","???","???"],[["PHSUBW","","",""],["PHSUBW","PHSUBW","",""],"???","???"],[["PHSUBD","","",""],["PHSUBD","PHSUBD","",""],"???","???"],[["PHSUBSW","","",""],["PHSUBSW","PHSUBSW","",""],"???","???"],[["PSIGNB","","",""],["PSIGNB","PSIGNB","",""],"???","???"],[["PSIGNW","","",""],["PSIGNW","PSIGNW","",""],"???","???"],[["PSIGND","","",""],["PSIGND","PSIGND","",""],"???","???"],[["PMULHRSW","","",""],"PMULHRSW","???","???"],["???",["","PERMILPS",["PERMILPS","","???"],""],"???","???"],["???",["","PERMILPD","PERMILPD",""],"???","???"],["???",["","TESTPS","",""],"???","???"],["???",["","TESTPD","",""],"???","???"],["???",["PBLENDVB","PBLENDVB","PSRLVW",""],["","","PMOVUSWB",""],"???"],["???",["","","PSRAVW",""],["","","PMOVUSDB",""],"???"],["???",["","","PSLLVW",""],["","","PMOVUSQB",""],"???"],["???",["","CVTPH2PS",["CVTPH2PS","","???"],""],["","","PMOVUSDW",""],"???"],["???",["BLENDVPS","BLENDVPS",["PRORVD","","PRORVQ"],""],["","","PMOVUSQW",""],"???"],["???",["BLENDVPD","BLENDVPD",["PROLVD","","PROLVQ"],""],["","","PMOVUSQD",""],"???"],["???",["","PERMPS",["PERMPS","","PERMPD"],""],"???","???"],["???",["PTEST","PTEST","",""],"???","???"],["???",["","BROADCASTSS",["BROADCASTSS","","???"],["BROADCASTSS","","???"]],"???","???"],["???",["","BROADCASTSD",["BROADCASTF32X2","","BROADCASTSD"],["???","","BROADCASTSD"]],"???","???"],["???",["","BROADCASTF128",["BROADCASTF32X4","","BROADCASTF64X2"],["BROADCASTF32X4","","???"]],"???","???"],["???",["","",["BROADCASTF32X8","","BROADCASTF64X4"],["???","","BROADCASTF64X4"]],"???","???"],[["PABSB","","",""],"PABSB","???","???"],[["PABSW","","",""],"PABSW","???","???"],[["PABSD","","",""],["PABSD","","???"],"???","???"],["???",["","","PABSQ",""],"???","???"],["???","PMOVSXBW",["","","PMOVSWB",""],"???"],["???","PMOVSXBD",["","","PMOVSDB",""],"???"],["???","PMOVSXBQ",["","","PMOVSQB",""],"???"],["???","PMOVSXWD",["","","PMOVSDW",""],"???"],["???","PMOVSXWQ",["","","PMOVSQW",""],"???"],["???","PMOVSXDQ",["","","PMOVSQD",""],"???"],["???",["","",["PTESTMB","","PTESTMW"],""],["","",["PTESTNMB","","PTESTNMW"],""],"???"],["???",["","",["PTESTMD","","PTESTMQ"],["PTESTMD","","???"]],["","",["PTESTNMD","","PTESTNMQ"],""],"???"],["???","PMULDQ",["","",["PMOVM2B","","PMOVM2W"],""],"???"],["???",["PCMPEQQ","PCMPEQQ","PCMPEQQ",""],["","",["PMOVB2M","","PMOVW2M"],""],"???"],[["???",["MOVNTDQA","","???"],"???","???"],["???","???",["","",["???","","PBROADCASTMB2Q"],""],"???"]],["???",["PACKUSDW","","???"],"???","???"],["???",["","MASKMOVPS",["SCALEFPS","","SCALEFPD"],""],"???","???"],["???",["","MASKMOVPD",["SCALEFSS","","SCALEFSD"],""],"???","???"],["???",["","MASKMOVPS","",""],"???","???"],["???",["","MASKMOVPD","",""],"???","???"],["???","PMOVZXBW",["","","PMOVWB",""],"???"],["???","PMOVZXBD",["","","PMOVDB",""],"???"],["???","PMOVZXBQ",["","","PMOVQB",""],"???"],["???","PMOVZXWD",["","","PMOVDW",""],"???"],["???","PMOVZXWQ",["","","PMOVQW",""],"???"],["???","PMOVZXDQ",["","",["PMOVQD","PMOVQD",""],""],"???"],["???",["","PERMD",["PERMD","","PERMQ"],["PERMD","","???"]],"???","???"],["???",["PCMPGTQ","PCMPGTQ","PCMPGTQ",""],"???","???"],["???","PMINSB",["","",["PMOVM2D","","PMOVM2Q"],""],"???"],["???",["PMINSD","PMINSD",["PMINSD","","PMINSQ"],["PMINSD","","???"]],["","",["PMOVD2M","","PMOVQ2M"],""],"???"],["???","PMINUW",["","","PBROADCASTMW2D",""],"???"],["???",["PMINUD","PMINUD",["PMINUD","","PMINUQ"],["PMINUD","","???"]],"???","???"],["???","PMAXSB","???","???"],["???",["PMAXSD","PMAXSD",["PMAXSD","","PMAXSQ"],["PMAXSD","","???"]],"???","???"],["???","PMAXUW","???","???"],["???",["PMAXUD","PMAXUD",["PMAXUD","","PMAXUQ"],["PMAXUD","","???"]],"???","???"],["???",["PMULLD","PMULLD",["PMULLD","","PMULLQ"],["PMULLD","",""]],"???","???"],["???",["PHMINPOSUW",["PHMINPOSUW","PHMINPOSUW",""],"",""],"???","???"],["???",["","",["GETEXPPS","","GETEXPPD"],["GETEXPPS","","GETEXPPD"]],"???","???"],["???",["","",["GETEXPSS","","GETEXPSD"],""],"???","???"],["???",["","",["PLZCNTD","","PLZCNTQ"],""],"???","???"],["???",["",["PSRLVD","","PSRLVQ"],["PSRLVD","","PSRLVQ"],["PSRLVD","","???"]],"???","???"],["???",["",["PSRAVD","",""],["PSRAVD","","PSRAVQ"],["PSRAVD","","???"]],"???","???"],["???",["",["PSLLVD","","PSLLVQ"],["PSLLVD","","PSLLVQ"],["PSLLVD","","???"]],"???","???"],"???","???","???","???",["???",["","",["RCP14PS","","RCP14PD"],""],"???","???"],["???",["","",["RCP14SS","","RCP14SD"],""],"???","???"],["???",["","",["RSQRT14PS","","RSQRT14PD"],""],"???","???"],["???",["","",["RSQRT14SS","","RSQRT14SD"],""],"???","???"],["???",["","","",["ADDNPS","","ADDNPD"]],"???","???"],["???",["","","",["GMAXABSPS","","???"]],"???","???"],["???",["","","",["GMINPS","","GMINPD"]],"???","???"],["???",["","","",["GMAXPS","","GMAXPD"]],"???","???"],"",["???",["","","",["FIXUPNANPS","","FIXUPNANPD"]],"???","???"],"","",["???",["","PBROADCASTD",["PBROADCASTD","","???"],["PBROADCASTD","","???"]],"???","???"],["???",["","PBROADCASTQ",["BROADCASTI32X2","","PBROADCASTQ"],["???","","PBROADCASTQ"]],"???","???"],["???",["","BROADCASTI128",["BROADCASTI32X4","","BROADCASTI64X2"],["BROADCASTI32X4","","???"]],"???","???"],["???",["","",["BROADCASTI32X8","","BROADCASTI64X4"],["???","","BROADCASTI64X4"]],"???","???"],["???",["","","",["PADCD","","???"]],"???","???"],["???",["","","",["PADDSETCD","","???"]],"???","???"],["???",["","","",["PSBBD","","???"]],"???","???"],["???",["","","",["PSUBSETBD","","???"]],"???","???"],"???","???","???","???",["???",["","",["PBLENDMD","","PBLENDMQ"],["PBLENDMD","","PBLENDMQ"]],"???","???"],["???",["","",["BLENDMPS","","BLENDMPD"],["BLENDMPS","","BLENDMPD"]],"???","???"],["???",["","",["PBLENDMB","","PBLENDMW"],""],"???","???"],"???","???","???","???","???",["???",["","","",["PSUBRD","","???"]],"???","???"],["???",["","","",["SUBRPS","","SUBRPD"]],"???","???"],["???",["","","",["PSBBRD","","???"]],"???","???"],["???",["","","",["PSUBRSETBD","","???"]],"???","???"],"???","???","???","???",["???",["","","",["PCMPLTD","","???"]],"???","???"],["???",["","",["PERMI2B","","PERMI2W"],""],"???","???"],["???",["","",["PERMI2D","","PERMI2Q"],""],"???","???"],["???",["","",["PERMI2PS","","PERMI2PD"],""],"???","???"],["???",["","PBROADCASTB",["PBROADCASTB","","???"],""],"???","???"],["???",["","PBROADCASTW",["PBROADCASTW","","???"],""],"???","???"],["???",["???",["","",["PBROADCASTB","","???"],""],"???","???"]],["???",["???",["","",["PBROADCASTW","","???"],""],"???","???"]],["???",["","",["PBROADCASTD","","PBROADCASTQ"],""],"???","???"],["???",["","",["PERMT2B","","PERMT2W"],""],"???","???"],["???",["","",["PERMT2D","","PERMT2Q"],""],"???","???"],["???",["","",["PERMT2PS","","PERMT2PD"],""],"???","???"],[["???","INVEPT","???","???"],"???"],[["???","INVVPID","???","???"],"???"],[["???","INVPCID","???","???"],"???"],["???",["???","???","PMULTISHIFTQB","???"],"???","???"],["???",["","","",["SCALEPS","","???"]],"???","???"],"???",["???",["","","",["PMULHUD","","???"]],"???","???"],["???",["","","",["PMULHD","","???"]],"???","???"],["???",["","",["EXPANDPS","","EXPANDPD"],""],"???","???"],["???",["","",["PEXPANDD","","PEXPANDQ"],""],"???","???"],["???",["","",["COMPRESSPS","","COMPRESSPD"],""],"???","???"],["???",["","",["PCOMPRESSD","","PCOMPRESSQ"],""],"???","???"],"???",["???",["","",["PERMB","","PERMW"],""],"???","???"],"???","???",["???",["",["PGATHERDD","","PGATHERDQ"],["PGATHERDD","","PGATHERDQ"],["PGATHERDD","","PGATHERDQ"]],"???","???"],["???",["",["PGATHERQD","","PGATHERQQ"],["PGATHERQD","","PGATHERQQ"],""],"???","???"],["???",["",["GATHERDPS","","GATHERDPD"],["GATHERDPS","","GATHERDPD"],["GATHERDPS","","GATHERDPD"]],"???","???"],["???",["",["GATHERQPS","","GATHERQPD"],["GATHERQPS","","GATHERQPD"],""],"???","???"],"???","???",["???",["",["FMADDSUB132PS","","FMADDSUB132PD"],["FMADDSUB132PS","","FMADDSUB132PD"],""],"???","???"],["???",["",["FMSUBADD132PS","","FMSUBADD132PD"],["FMSUBADD132PS","","FMSUBADD132PD"],""],"???","???"],["???",["",["FMADD132PS","","FMADD132PD"],["FMADD132PS","","FMADD132PD"],["FMADD132PS","","FMADD132PD"]],"???","???"],["???",["",["FMADD132SS","","FMADD132SD"],["FMADD132SS","","FMADD132SD"],""],"???","???"],["???",["",["FMSUB132PS","","FMSUB132PD"],["FMSUB132PS","","FMSUB132PD"],["FMSUB132PS","","FMSUB132PD"]],"???","???"],["???",["",["FMSUB132SS","","FMSUB132SD"],["FMSUB132SS","","FMSUB132SD"],""],"???","???"],["???",["",["FNMADD132PS","","FNMADD132PD"],["FNMADD132PS","","FNMADD132PD"],["NMADD132PS","","FNMADD132PD"]],"???","???"],["???",["",["FNMADD132SS","","FNMADD132SD"],["FNMADD132SS","","FNMADD132SD"],""],"???","???"],["???",["",["FNMSUB132PS","","FNMSUB132PD"],["FNMSUB132PS","","FNMSUB132PD"],["FNMSUB132PS","","FNMSUB132PS"]],"???","???"],["???",["",["FNMSUB132SS","","FNMSUB132SD"],["FNMSUB132SS","","FNMSUB132SD"],""],"???","???"],["???",["","",["PSCATTERDD","","PSCATTERDQ"],["PSCATTERDD","","PSCATTERDQ"]],"???","???"],["???",["","",["PSCATTERQD","","PSCATTERQQ"],""],"???","???"],["???",["","",["SCATTERDPS","","SCATTERDPD"],["SCATTERDPS","","SCATTERDPD"]],"???","???"],["???",["","",["SCATTERQPS","","SCATTERQPD"],""],"???","???"],["???",["","","",["FMADD233PS","","???"]],"???","???"],"???",["???",["",["FMADDSUB213PS","","FMADDSUB213PD"],["FMADDSUB213PS","","FMADDSUB213PD"],""],"???","???"],["???",["",["FMSUBADD213PS","","FMSUBADD213PD"],["FMSUBADD213PS","","FMSUBADD213PD"],""],"???","???"],["???",["",["FMADD213PS","","FMADD213PD"],["FMADD213PS","","FMADD213PD"],["FMADD213PS","","FMADD213PD"]],"???","???"],["???",["",["FMADD213SS","","FMADD213SD"],["FMADD213SS","","FMADD213SD"],""],"???","???"],["???",["",["FMSUB213PS","","FMSUB213PD"],["FMSUB213PS","","FMSUB213PD"],["FMSUB213PS","","FMSUB213PD"]],"???","???"],["???",["",["FMSUB213SS","","FMSUB213SD"],["FMSUB213SS","","FMSUB213SD"],""],"???","???"],["???",["",["FNMADD213PS","","FNMADD213PD"],["FNMADD213PS","","FNMADD213PD"],["FNMADD213PS","","FNMADD213PD"]],"???","???"],["???",["",["FNMADD213SS","","FNMADD213SD"],["FNMADD213SS","","FNMADD213SD"],""],"???","???"],["???",["",["FNMSUB213PS","","FNMSUB213PD"],["FNMSUB213PS","","FNMSUB213PD"],["FNMSUB213PS","","FNMSUB213PD"]],"???","???"],["???",["",["FNMSUB213SS","","FNMSUB213SD"],["FNMSUB213SS","","FNMSUB213SD"],""],"???","???"],"???","???","???","???",["???",["","","PMADD52LUQ",["PMADD233D","","???"]],"???","???"],["???",["","","PMADD52HUQ",["PMADD231D","","???"]],"???","???"],["???",["",["FMADDSUB231PS","","FMADDSUB231PD"],["FMADDSUB231PS","","FMADDSUB231PD"],""],"???","???"],["???",["",["FMSUBADD231PS","","FMSUBADD231PD"],["FMSUBADD231PS","","FMSUBADD231PD"],""],"???","???"],["???",["",["FMADD231PS","","FMADD231PD"],["FMADD231PS","","FMADD231PD"],["FMADD231PS","","FMADD231PD"]],"???","???"],["???",["",["FMADD231SS","","FMADD231SD"],["FMADD231SS","","FMADD231SD"],""],"???","???"],["???",["",["FMSUB231PS","","FMSUB231PD"],["FMSUB231PS","","FMSUB231PD"],["FMSUB231PS","","FMSUB231PD"]],"???","???"],["???",["",["FMSUB231SS","","FMSUB231SD"],["FMSUB231SS","","FMSUB231SD"],""],"???","???"],["???",["",["FNMADD231PS","","FNMADD231PD"],["FNMADD231PS","","FNMADD231PD"],["FNMADD231PS","","FNMADD231PD"]],"???","???"],["???",["",["FNMADD231SS","","FNMADD231SD"],["FNMADD231SS","","FNMADD231SD"],""],"???","???"],["???",["",["FNMSUB231PS","","FNMSUB231PD"],["FNMSUB231PS","","FNMSUB231PD"],["FNMSUB231PS","","FNMSUB231PD"]],"???","???"],["???",["",["FNMSUB231SS","","FNMSUB231SD"],["FNMSUB231SS","","FNMSUB231SD"],""],"???","???"],"???","???","???","???",["???",["","",["PCONFLICTD","","PCONFLICTQ"],""],"???","???"],"???",[[["???",["","","",["GATHERPF0HINTDPS","","GATHERPF0HINTDPD"]],"???","???"],["???",["","",["GATHERPF0DPS","","GATHERPF0DPD"],["GATHERPF0DPS","",""]],"???","???"],["???",["","",["GATHERPF1DPS","","GATHERPF1DPD"],["GATHERPF1DPS","",""]],"???","???"],"???",["???",["","","",["SCATTERPF0HINTDPS","","SCATTERPF0HINTDPD"]],"???","???"],["???",["","",["SCATTERPF0DPS","","SCATTERPF0DPD"],["VSCATTERPF0DPS","",""]],"???","???"],["???",["","",["SCATTERPF1DPS","","SCATTERPF1DPD"],["VSCATTERPF1DPS","",""]],"???","???"],"???"],"???"],[["???",["???",["","",["GATHERPF0QPS","","GATHERPF0QPD"],""],"???","???"],["???",["","",["GATHERPF1QPS","","GATHERPF1QPD"],""],"???","???"],"???","???",["???",["","",["SCATTERPF0QPS","","SCATTERPF0QPD"],""],"???","???"],["???",["","",["SCATTERPF1QPS","","SCATTERPF1QPD"],""],"???","???"],"???"],"???"],[["SHA1NEXTE","","",""],["","",["EXP2PS","","EXP2PD"],["EXP223PS","","???"]],"???","???"],[["SHA1MSG1","","",""],["","","",["LOG2PS","","???"]],"???","???"],[["SHA1MSG2","","",""],["","",["RCP28PS","","RCP28PD"],["RCP23PS","","???"]],"???","???"],[["SHA256RNDS2","","",""],["","",["RCP28SS","","RCP28SD"],["RSQRT23PS","","???"]],"???","???"],[["SHA256MSG1","","",""],["","",["RSQRT28PS","","RSQRT28PD"],["ADDSETSPS","","???"]],"???","???"],[["SHA256MSG2","","",""],["","",["RSQRT28SS","","RSQRT28SD"],["PADDSETSD","","???"]],"???","???"],"???","???",[[["","","",["LOADUNPACKLD","","LOADUNPACKLQ"]],["","","",["PACKSTORELD","","PACKSTORELQ"]],"???","???"],"???"],[[["","","",["LOADUNPACKLPS","","LOADUNPACKLPD"]],["","","",["PACKSTORELPS","","PACKSTORELPD"]],"???","???"],"???"],"???","???",[[["","","",["LOADUNPACKHD","","LOADUNPACKHQ"]],["","","",["PACKSTOREHD","","PACKSTOREHQ"]],"???","???"],"???"],[[["","","",["LOADUNPACKHPS","","LOADUNPACKHPD"]],["","","",["PACKSTOREHPS","","PACKSTOREHPD"]],"???","???"],"???"],"???","???","???","???","???",["???",["AESIMC","AESIMC","",""],"???","???"],["???",["AESENC","AESENC","",""],"???","???"],["???",["AESENCLAST","AESENCLAST","",""],"???","???"],["???",["AESDEC","AESDEC","",""],"???","???"],["???",["AESDECLAST","AESDECLAST","",""],"???","???"],"???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???",[["MOVBE","","",""],["MOVBE","","",""],"???",["CRC32","","",""]],[["MOVBE","","",""],["MOVBE","","",""],"???",["CRC32","","",""]],["???",["","ANDN","",""],"???","???"],["???",["???",["","BLSR","",""],"???","???"],["???",["","BLSMSK","",""],"???","???"],["???",["","BLSI","",""],"???","???"],"???","???","???","???"],"???",[["","BZHI","",""],"???",["","PEXT","",""],["","PDEP","",""]],["???",["ADCX","","",""],["ADOX","","",""],["","MULX","",""]],[["","BEXTR","",""],["","SHLX","",""],["","SARX","",""],["","SHRX","",""]],"???","???","???","???","???","???","???","???",["???",["","PERMQ","PERMQ",""],"???","???"],["???",["","PERMPD","PERMPD",""],"???","???"],["???",["",["PBLENDD","",""],"",""],"???","???"],["???",["","",["ALIGND","","ALIGNQ"],["ALIGND","","???"]],"???","???"],["???",["","PERMILPS",["PERMILPS","","???"],""],"???","???"],["???",["","PERMILPD","PERMILPD",""],"???","???"],["???",["","PERM2F128","",""],"???","???"],["???",["","","",["PERMF32X4","","???"]],"???","???"],["???",["ROUNDPS","ROUNDPS",["RNDSCALEPS","","???"],""],"???","???"],["???",["ROUNDPD","ROUNDPD","RNDSCALEPD",""],"???","???"],["???",["ROUNDSS","ROUNDSS",["RNDSCALESS","","???"],""],"???","???"],["???",["ROUNDSD","ROUNDSD","RNDSCALESD",""],"???","???"],["???",["BLENDPS","BLENDPS","",""],"???","???"],["???",["BLENDPD","BLENDPD","",""],"???","???"],["???",["PBLENDW","PBLENDW","",""],"???","???"],[["PALIGNR","","",""],"PALIGNR","???","???"],"???","???","???","???",[["???","PEXTRB","???","???"],["???","PEXTRB","???","???"]],[["???","PEXTRW","???","???"],["???","PEXTRW","???","???"]],["???",["PEXTRD","","PEXTRQ"],"???","???"],["???","EXTRACTPS","???","???"],["???",["","INSERTF128",["INSERTF32X4","","INSERTF64X2"],""],"???","???"],["???",["","EXTRACTF128",["EXTRACTF32X4","","EXTRACTF64X2"],""],"???","???"],["???",["","",["INSERTF32X8","","INSERTF64X4"],""],"???","???"],["???",["","",["EXTRACTF32X8","","EXTRACTF64X4"],""],"???","???"],"???",["???",["","CVTPS2PH",["CVTPS2PH","","???"],""],"???","???"],["???",["","",["PCMP,UD,","","PCMP,UQ,"],["PCMP,UD,","","???"]],"???","???"],["???",["","",["PCM,PD,","","PCM,PQ,"],["PCM,PD,","","???"]],"???","???"],["???","PINSRB","???","???"],["???",["INSERTPS","","???"],"???","???"],["???",["",["PINSRD","","PINSRQ"],["PINSRD","","PINSRQ"],""],"???","???"],["???",["","",["SHUFF32X4","","SHUFF64X2"],""],"???","???"],"???",["???",["","",["PTERNLOGD","","PTERNLOGQ"],""],"???","???"],["???",["","",["GETMANTPS","","GETMANTPD"],["GETMANTPS","","GETMANTPD"]],"???","???"],["???",["","",["GETMANTSS","","GETMANTSD"],""],"???","???"],"???","???","???","???","???","???","???","???",["???",["",["KSHIFTRB","","KSHIFTRW"],"",""],"???","???"],["???",["",["KSHIFTRD","","KSHIFTRQ"],"",""],"???","???"],["???",["",["KSHIFTLB","","KSHIFTLW"],"",""],"???","???"],["???",["",["KSHIFTLD","","KSHIFTLQ"],"",""],"???","???"],"???","???","???","???",["???",["","INSERTI128",["INSERTI32X4","","INSERTI64X2"],""],"???","???"],["???",["","EXTRACTI128",["EXTRACTI32X4","","EXTRACTI64X2"],""],"???","???"],["???",["","",["INSERTI32X8","","INSERTI64X4"],""],"???","???"],["???",["","",["EXTRACTI32X8","","EXTRACTI64X4"],""],"???","???"],"???","???",["???",["","KEXTRACT",["PCMP,UB,","","PCMP,UW,"],""],"???","???"],["???",["","",["PCM,PB,","","PCM,PW,"],""],"???","???"],["???",["DPPS","DPPS","",""],"???","???"],["???",["DPPD","DPPD","",""],"???","???"],["???",["MPSADBW","MPSADBW",["DBPSADBW","","???"],""],"???","???"],["???",["","",["SHUFI32X4","","SHUFI64X2"],""],"???","???"],["???",["PCLMULQDQ","PCLMULQDQ","",""],"???","???"],"???",["???",["","PERM2I128","",""],"???","???"],"???",["???",["",["PERMIL2PS","","PERMIL2PS"],"",""],"???","???"],["???",["",["PERMIL2PD","","PERMIL2PD"],"",""],"???","???"],["???",["","BLENDVPS","",""],"???","???"],["???",["","BLENDVPD","",""],"???","???"],["???",["","PBLENDVB","",""],"???","???"],"???","???","???",["???",["","",["RANGEPS","","RANGEPD"],""],"???","???"],["???",["","",["RANGESS","","RANGESD"],""],"???","???"],["???",["","","",["RNDFXPNTPS","","RNDFXPNTPD"]],"???","???"],"???",["???",["","",["FIXUPIMMPS","","FIXUPIMMPD"],""],"???","???"],["???",["","",["FIXUPIMMSS","","FIXUPIMMSD"],""],"???","???"],["???",["","",["REDUCEPS","","REDUCEPD"],""],"???","???"],["???",["","",["REDUCESS","","REDUCESD"],""],"???","???"],"???","???","???","???",["???",["",["FMADDSUBPS","","FMADDSUBPS"],"",""],"???","???"],["???",["",["FMADDSUBPD","","FMADDSUBPD"],"",""],"???","???"],["???",["",["FMSUBADDPS","","FMSUBADDPS"],"",""],"???","???"],["???",["",["FMSUBADDPD","","FMSUBADDPD"],"",""],"???","???"],["???",["PCMPESTRM","PCMPESTRM","",""],"???","???"],["???",["PCMPESTRI","PCMPESTRI","",""],"???","???"],["???",["PCMPISTRM","PCMPISTRM","",""],"???","???"],["???",["PCMPISTRI","PCMPISTRI","",""],"???","???"],"???","???",["???",["","",["FPCLASSPS","","FPCLASSPD"],""],"???","???"],["???",["","",["FPCLASSSS","","FPCLASSSD"],""],"???","???"],["???",["",["FMADDPS","","FMADDPS"],"",""],"???","???"],["???",["",["FMADDPD","","FMADDPD"],"",""],"???","???"],["???",["",["FMADDSS","","FMADDSS"],"",""],"???","???"],["???",["",["FMADDSD","","FMADDSD"],"",""],"???","???"],["???",["",["FMSUBPS","","FMSUBPS"],"",""],"???","???"],["???",["",["FMSUBPD","","FMSUBPD"],"",""],"???","???"],["???",["",["FMSUBSS","","FMSUBSS"],"",""],"???","???"],["???",["",["FMSUBSD","","FMSUBSD"],"",""],"???","???"],"???","???","???","???","???","???","???","???",["???",["",["FNMADDPS","","FNMADDPS"],"",""],"???","???"],["???",["",["FNMADDPD","","FNMADDPD"],"",""],"???","???"],["???",["",["FNMADDSS","","FNMADDSS"],"",""],"???","???"],["???",["",["FNMADDSD","","FNMADDSD"],"",""],"???","???"],["???",["",["FNMSUBPS","","FNMSUBPS"],"",""],"???","???"],["???",["",["FNMSUBPD","","FNMSUBPD"],"",""],"???","???"],["???",["",["FNMSUBSS","","FNMSUBSS"],"",""],"???","???"],["???",["",["FNMSUBSD","","FNMSUBSD"],"",""],"???","???"],"???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???",[["","","","CVTFXPNTUDQ2PS"],["","","",["CVTFXPNTPS2UDQ","","???"]],"???",["","","","CVTFXPNTPD2UDQ"]],[["","","","CVTFXPNTDQ2PS"],["","","",["CVTFXPNTPS2DQ","","???"]],"???","???"],"SHA1RNDS4","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???",["???",["AESKEYGENASSIST","AESKEYGENASSIST","",""],"???","???"],"???","???","???","???","???","???",["???","???","???",["","","","CVTFXPNTPD2DQ"]],"???","???","???","???","???","???","???","???","???",["???","???","???",["","RORX","",""]],"???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","VPMACSSWW","VPMACSSWD","VPMACSSDQL","???","???","???","???","???","???","VPMACSSDD","VPMACSSDQH","???","???","???","???","???","VPMACSWW","VPMACSWD","VPMACSDQL","???","???","???","???","???","???","VPMACSDD","VPMACSDQH","???","???",["VPCMOV","","VPCMOV"],["VPPERM","","VPPERM"],"???","???","VPMADCSSWD","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","VPMADCSWD","???","???","???","???","???","???","???","???","???","VPROTB","VPROTW","VPROTD","VPROTQ","???","???","???","???","???","???","???","???","VPCOM,B,","VPCOM,W,","VPCOM,D,","VPCOM,Q,","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","VPCOM,UB,","VPCOM,UW,","VPCOM,UD,","VPCOM,UQ,","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???",["???","BLCFILL","BLSFILL","BLCS","TZMSK","BLCIC","BLSIC","T1MSKC"],["???","BLCMSK","???","???","???","???","BLCI","???"],"???","???","???","???","???","???","???","???","???","???","???","???","???","???","???",["???",["LLWPCB","SLWPCB","???","???","???","???","???","???"]],"???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","VFRCZPS","VFRCZPD","VFRCZSS","VFRCZSD","???","???","???","???","???","???","???","???","???","???","???","???",["VPROTB","","VPROTB"],["VPROTW","","VPROTW"],["VPROTD","","VPROTD"],["VPROTQ","","VPROTQ"],["VPSHLB","","VPSHLB"],["VPSHLW","","VPSHLW"],["VPSHLD","","VPSHLD"],["VPSHLQ","","VPSHLQ"],["VPSHAB","","VPSHAB"],["VPSHAW","","VPSHAW"],["VPSHAD","","VPSHAD"],["VPSHAQ","","VPSHAQ"],"???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","VPHADDBW","VPHADDBD","VPHADDBQ","???","???","VPHADDWD","VPHADDWQ","???","???","???","VPHADDDQ","???","???","???","???","???","VPHADDUBWD","VPHADDUBD","VPHADDUBQ","???","???","VPHADDUWD","VPHADDUWQ","???","???","???","VPHADDUDQ","???","???","???","???","???","VPHSUBBW","VPHSUBWD","VPHSUBDQ","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","BEXTR","???",["LWPINS","LWPVAL","???","???","???","???","???","???"],"???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","DELAY","???","???","???","???","???","???","???","???","???","???","???",[["VLOADD","VLOADQ","",""],"???"],"???",[["VLOADUNPACKLD","VLOADUNPACKLQ","",""],"???"],[["VLOADUNPACKHD","VLOADUNPACKHQ","",""],"???"],[["VSTORED","VSTOREQ","",""],"???"],"???",[["VPACKSTORELD","VPACKSTORELQ","",""],"???"],[["VPACKSTOREHD","VPACKSTOREHQ","",""],"???"],["VGATHERD","???"],["VGATHERPFD","???"],"???",["VGATHERPF2D","???"],["VSCATTERD","???"],["VSCATTERPFD","???"],"???",["VSCATTERPF2D","???"],["VCMP,PS,","VCMP,PD,","",""],"VCMP,PI,","VCMP,PU,","???",["VCMP,PS,","VCMP,PD,","",""],"VCMP,PI,","VCMP,PU,","???","???","???","???","???","???","???","???","???","VTESTPI","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???",["VADDPS","VADDPD","",""],"VADDPI","???","VADDSETCPI","???","VADCPI","VADDSETSPS","VADDSETSPI",["VADDNPS","VADDNPD","",""],"???","???","???","???","???","???","???",["VSUBPS","VSUBPD","",""],"VSUBPI","???","VSUBSETBPI","???","VSBBPI","???","???",["VSUBRPS","VSUBRPD","",""],"VSUBRPI","???","VSUBRSETBPI","???","VSBBRPI","???","???",["VMADD231PS","VMADD231PD","",""],"VMADD231PI",["VMADD213PS","VMADD213PD","",""],"???",["VMADD132PS","VMADD132PD","",""],"???","VMADD233PS","VMADD233PI",["VMSUB231PS","VMSUB231PD","",""],"???",["VMSUB213PS","VMSUB213PD","",""],"???",["VMSUB132PS","VMSUB132PD","",""],"???","???","???",["VMADDN231PS","VMADDN231PD","",""],"???",["VMADDN213PS","VMADDN213PD","",""],"???",["VMADDN132PS","VMADDN132PD","",""],"???","???","???",["VMSUBR231PS","VMSUBR231PD","",""],"???",["VMSUBR213PS","VMSUBR213PD","",""],"???",["VMSUBR132PS","VMSUBR132PD","",""],"???",["VMSUBR23C1PS","VMSUBR23C1PD","",""],"???",["VMULPS","VMULPD","",""],"VMULHPI","VMULHPU","VMULLPI","???","???","VCLAMPZPS","VCLAMPZPI",["VMAXPS","VMAXPD","",""],"VMAXPI","VMAXPU","???",["VMINPS","VMINPD","",""],"VMINPI","VMINPU","???",["???","VCVT,PD2PS,","",""],["VCVTPS2PI","VCVT,PD2PI,","",""],["VCVTPS2PU","VCVT,PD2PU,","",""],"???",["???","VCVT,PS2PD,","",""],["VCVTPI2PS","VCVT,PI2PD,","",""],["VCVTPU2PS","VCVT,PU2PD,","",""],"???","VROUNDPS","???","VCVTINSPS2U10","VCVTINSPS2F11","???","VCVTPS2SRGB8","VMAXABSPS","???","VSLLPI","VSRAPI","VSRLPI","???",["VANDNPI","VANDNPQ","",""],["VANDPI","VANDPQ","",""],["VORPI","VORPQ","",""],["VXORPI","VXORPQ","",""],"VBINTINTERLEAVE11PI","VBINTINTERLEAVE21PI","???","???","???","???","???","???","VEXP2LUTPS","VLOG2LUTPS","VRSQRTLUTPS","???","VGETEXPPS","???","???","???","VSCALEPS","???","???","???","???","???","???","???","VRCPRESPS","???","VRCPREFINEPS","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","???","VFIXUPPS","VSHUF128X32","VINSERTFIELDPI","VROTATEFIELDPI","???","???","???","???","???","???","???","???","???","???","???","???",["???","BSFI"],["???","BSFI"],["???","BSFI"],["???","BSFI"],["???","BSRI"],["???","BSRI"],["???","BSRI"],["???","BSRI"],["???","BSFF"],["???","BSFF"],["???","BSFF"],["???","BSFF"],["???","BSRF"],["???","BSRF"],["???","BSRF"],["???","BSRF"],["???","BITINTERLEAVE11"],["???","BITINTERLEAVE11"],["???","BITINTERLEAVE11"],["???","BITINTERLEAVE11"],["???","BITINTERLEAVE21"],["???","BITINTERLEAVE21"],["???","BITINTERLEAVE21"],["???","BITINTERLEAVE21"],["???","INSERTFIELD"],["???","INSERTFIELD"],["???","INSERTFIELD"],["???","INSERTFIELD"],["???","ROTATEFIELD"],["???","ROTATEFIELD"],["???","ROTATEFIELD"],["???","ROTATEFIELD"],["???","COUNTBITS"],["???","COUNTBITS"],["???","COUNTBITS"],["???","COUNTBITS"],["???","QUADMASK16"],["???","QUADMASK16"],["???","QUADMASK16"],["???","QUADMASK16"],"???","???","???","???","VKMOVLHB",[["CLEVICT1","CLEVICT2","LDVXCSR","STVXCSR","???","???","???","???"],"???"],[["VPREFETCH1","VPREFETCH2","???","???","???","???","???","???"],"???"],[["VPREFETCH1","VPREFETCH2","???","???","???","???","???","???"],"???"],"VKMOV","VKMOV","VKMOV","VKMOV","VKNOT","VKANDNR","VKANDN","VKAND","VKXNOR","VKXOR","VKORTEST","VKOR","???","VKSWAPB",["???",["DELAY","SPFLT","???","???","???","???","???","???"]],["???",["DELAY","SPFLT","???","???","???","???","???","???"]]],y=["06000A000003","070E0B0E0003","0A0006000003","0B0E070E0003","16000C000003","170E0DE60003","","","06000A000003","070E0B0E0003","0A0006000003","0B0E070E0003","16000C000003","170E0DE60003","","","06000A000003","070E0B0E0003","0A0006000003","0B0E070E0003","16000C000003","170E0DE60003","","","06000A000003","070E0B0E0003","0A0006000003","0B0E070E0003","16000C000003","170E0DE60003","","","06000A000003","070E0B0E0003","0A0006000003","0B0E070E0003","16000C000003","170E0DE60003","","","06000A000003","070E0B0E0003","0A0006000003","0B0E070E0003","16000C000003","170E0DE60003","","","06000A000003","070E0B0E0003","0A0006000003","0B0E070E0003","16000C000003","170E0DE60003","","","06000A00","070E0B0E","0A000600","0B0E070E","16000C00","170E0DE6","","","03060003","03060003","03060003","03060003","03060003","03060003","03060003","03060003","03060003","03060003","03060003","03060003","03060003","03060003","03060003","03060003","030A","030A","030A","030A","030A","030A","030A","030A","030A","030A","030A","030A","030A","030A","030A","030A",["","",""],["","",""],["0A020606","0A010604",""],"0B0E0704","","","","","0DE6","0B0E070E0DE6","0DA1","0B0E070E0DE1","22001A01","230E1A01","1A012000","1A01210E","10000002000C","10000002000C","10000002000C","10000002000C","10000002000C","10000002000C","10000002000C","10000002000C","10000002000C","10000002000C","10000002000C","10000002000C","10000002000C","10000002000C","10000002000C","10000002000C",["06000C000003","06000C000003","06000C000003","06000C000003","06000C000003","06000C000003","06000C000003","06000C00"],["070E0DE60003","070E0DE60003","070E0DE60003","070E0DE60003","070E0DE60003","070E0DE60003","070E0DE60003","070E0DE6"],["06000C000003","06000C000003","06000C000003","06000C000003","06000C000003","06000C000003","06000C000003","06000C00"],["070E0DE10003","070E0DE10003","070E0DE10003","070E0DE10003","070E0DE10003","070E0DE10003","070E0DE10003","070E0DE1"],"06000A00","070E0B0E","0A0006000003","0B0E070E0003","06000A000001","070E0B0E0001","0A0006000001","0B0E070E0001","06020A080001",["0B0E0601",""],"0A0806020001",["070A","","","","","","",""],[["","","",""],["","","",""],["","","",""],["","","",""]],"170E030E0003","170E030E0003","170E030E0003","170E030E0003","170E030E0003","170E030E0003","170E030E0003",["","",""],["","",""],"0D060C01","",["","",""],["","",""],"","","160004000001","170E050E0001","040016000001","050E170E0001","22002000","230E210E","22002000","230E210E","16000C00","170E0DE6","22001600","230E170E","16002000","170E210E","16002200","170E230E","02000C000001","02000C000001","02000C000001","02000C000001","02000C000001","02000C000001","02000C000001","02000C000001","030E0D0E0001","030E0D0E0001","030E0D0E0001","030E0D0E0001","030E0D0E0001","030E0D0E0001","030E0D0E0001","030E0D0E0001",["06000C00","06000C00","06000C00","06000C00","06000C00","06000C00","06000C00","06000C00"],["070E0C00","070E0C00","070E0C00","070E0C00","070E0C00","070E0C00","070E0C00","070E0C00"],"0C010008","0008","0B060906","0B060906",["06000C000001","","","","","","",["0C00","0C00","0C00","0C00","0C00","0C00","0C00","0C00"]],["070E0D060001","","","","","","",["1002","1002","1002","1002","1002","1002","1002","1002"]],"0C010C00","","0C01","","2C00","0C00","",["","",""],["06002A00","06002A00","06002A00","06002A00","06002A00","06002A00","06002A00","06002A00"],["070E2A00","070E2A00","070E2A00","070E2A00","070E2A00","070E2A00","070E2A00","070E2A00"],["06001800","06001800","06001800","06001800","06001800","06001800","06001800","06001800"],["070E1800","070E1800","070E1800","070E1800","070E1800","070E1800","070E1800","070E1800"],"0C00","0C00","","1E00",[["0604","0604","0604","0604","0604","0604","0604","0604"],["24080609","24080609","0609","0609","24080609","24080609","24080609","24080609"]],[["0604","","0604","0604","0601","0602","0601","0602"],["0609","0609",["","","","","","","",""],"0609",["","","","","","","",""],["","","","","","","",""],["","","","","","","",""],["","","","","","","",""]]],[["0604","0604","0604","0604","0604","0604","0604","0604"],["24080609","24080609","24080609","24080609","",["","","","","","","",""],"",""]],[["0604","0604","0604","0604","","0607","","0607",""],["24080609","24080609","24080609","24080609",["","","","","","","",""],"24080609","24080609",""]],[["0606","0606","0606","0606","0606","0606","0606","0606"],["06092408","06092408","0609","0609","06092408","06092408","06092408","06092408"]],[["0606","0606","0606","0606","0606","","0601","0602"],["0609","0609","0609","0609","0609","0609","",""]],[["0602","0602","0602","0602","0602","0602","0602","0602"],["06092408","06092408","0609",["","","","","","","",""],"06092408","06092408","06092408","06092408"]],[["0602","0602","0602","0602","0607","0606","0607","0606"],["0609","0609","0609","0609",["1601","","","","","","",""],"24080609","24080609",""]],"10000004","10000004","10000004","10000004","16000C00","170E0C00","0C001600","0C00170E","110E0008","110E0008","0D060C01","100000040004","16001A01","170E1A01","1A011600","1A01170E","","","","","","",["06000C00","","06000003","06000003","16000600","0600","16000600","0600"],["070E0D06","","070E0003","070E0003","170E070E","070E","170E070E","170E070E"],"","","","","","",["06000003","06000003","","","","","",""],[["070E0003","070E0003","070A0004","090E0008","070A0008","090E0008","070A",""],["070E0003","070E0003","070A0008","","070A0008","","070A",""]],[["0602","0602","0602","0602","0602","0602","070E",""],["070E","070E","0601","0601","0601","0601","070E",""]],[["0908","0908","0908","0908","0602","","0602","0601"],[["","","","","","","",""],["170819081B08","17081908","","","","","",""],["","","","","","","",""],["1708","","1708","1708","","","1602","17081802"],"070E","","0601",["","","170819081B08","170819081B08","","","",""]]],["0B0E0612","0B0E070E"],["0B0E0612","0B0E070E"],"","","","","","","","","",[["0601","0601","","","","","",""],""],"","0A0A06A9",[["0B700770","0B700770","0A040603","0A040609"],["0B700770","0B700770","0A0412040604","0A0412040604"]],[["07700B70","07700B70","06030A04","06090A04"],["07700B70","07700B70","060412040A04","060412040A04"]],[["0A0412040606","0A0412040606","0B700770","0B700768"],["0A0412040604","","0B700770","0B700770"]],[["06060A04","06060A04","",""],""],["0B70137007700140","0B70137007700140","",""],["0B70137007700140","0B70137007700140","",""],[["0A0412040606","0A0412040606","0B700770",""],["0A0412040604","","0B700770",""]],[["06060A04","06060A04","",""],""],[["0601","0601","0601","0601","","","",""],""],"",[[["0A0B07080180","","",""],["0A0B07100180","","",""],["0A0B07080180","","",""],["0A0B07080180","","",""]],["",["0A0B060B","","",""],["0A0B07080180","","",""],["0A0B07080180","","",""]]],[[["07080A0B0180","","",""],["07100A0B0180","","",""],["0A0B07080180","","",""],["0A0B07080180","","",""]],["",["0A0B060B","","",""],"",["0A0B07080180","","",""]]],"","","","070E",["","07080A0C0001"],["","07080A0D0001"],["","0A0C07080001"],["","0A0D07080001"],["","07080A0E0001"],"",["","0A0E07080001"],"",[["0A040648","0B300730","0B700770","0A06066C0130"],["0A040648","0B300730","0B700770","0A06066C0130"],"",""],[[["06480A04","07300B30","07700B70","066C0A060130"],["06480A04","07300B30","07700B70","066C0A060130"],["","","",["066C0A060138","066C0A060138","066C0A060138"]],["","","",["066C0A060138","066C0A060138","066C0A060138"]]],[["06480A04","07300B30","07700B70","066C0A06"],["06480A04","07300B30","07700B70","066C0A06"],"",""]],[["0A0406A9","","",""],["0A0406A9","","",""],"0A041204070C010A","0A041204070C010A"],[["07700B70","07700B70",["06030A04","","",""],["06060A04","","",""]],""],[["0A0A0649","","",""],["0A0A0648","","",""],"0B0C06430109","0B0C06490109"],[["0A0A0649","","",""],["0A0A0648","","",""],"0B0C0643010A","0B0C0649010A"],["0A0406430101","0A0406490101","",""],["0A0406430101","0A0406490101","",""],"","","","","","","","","","","","","","","","","0B0E070E",[["0B0E070E0180",["0A0F120F06FF","","0A0F120F06FF"],"",""],["0B0E070E0180",["0A0F120F06FF","","0A0F120F06FF"],"",""],"",""],[["0B0E070E0180",["0A0F120F06FF","","0A0F120F06FF"],"",""],["0B0E070E0180",["0A0F120F06FF","","0A0F120F06FF"],"",""],"",""],[["0B0E070E0180","0A0F06FF","",""],"","",""],[["0B0E070E0180",["0A0F06FF","","0A0F06FF"],"",""],["0B0E070E0180",["0A0F06FF","","0A0F06FF"],"",""],"",""],[["0A02070E0180",["0A0F120F06FF","","0A0F120F06FF"],"",""],["0A02070E0180",["0A0F120F06FF","","0A0F120F06FF"],"",""],"",""],[["0B0E070E0180",["0A0F120F06FF","","0A0F120F06FF"],"",""],["0B0E070E0180",["0A0F120F06FF","","0A0F120F06FF"],"",""],"",""],[["0B0E070E0180",["0A0F120F06FF","","0A0F120F06FF"],"",""],["0B0E070E0180",["0A0F120F06FF","","0A0F120F06FF"],"",""],"",""],[["0B0E070E0180","0A0F06FF","",""],"","",""],[["0B0E070E0180","0A0F06FF","",""],"","",""],[["0B0E070E0180",["0A0F120F06FF","","0A0F120F06FF"],"",""],["0B0E070E0180",["0A0F120F06FF","","0A0F120F06FF"],"",""],"",""],[["0B0E070E0180",["0A0F120F06FF","","0A0F120F06FF"],"",""],["0B0E070E0180",["0A0F120F06FF","",""],"",""],"",""],"0B0E070E","0B0E070E","0B0E070E","0B0E070E",["",[["0B0C0648","0B0C0730","",""],["0B0C0648","0B0C0730","",""],"",""]],["0B7007700142","0B7007700142","0A04120406430102","0A04120406490102"],[["0A040648","0A040648","",""],"",["0A040643","0A0412040643","",""],""],[["0A040648","0A040648","",""],"",["0A040643","0A0412040643","",""],""],["0B70137007700140","0B70137007700140","",""],["0B70137007700140","0B70137007700140","",""],["0B70137007700140","0B70137007700140","",""],["0B70137007700140","0B70137007700140","",""],[["0A040648","0B3013300730","0B70137007700152","0A061206066C0152"],["0A040648","0B3013300730","0B70137007700152","0A061206066C0152"],"0A04120406430102","0A04120406460102"],[["0A040648","0B3013300730","0B70137007700152","0A061206066C0152"],["0A040648","0B3013300730","0B70137007700152","0A061206066C0152"],"0A04120406430102","0A04120406460102"],[["0A040648","0B300718","0B7007380151","0A06065A0171"],["0A040648","0B180730","0B3807700152","0A05066C0152"],"0A04120406430101","0A04120406460102"],[["0B7007700142","","0B380770014A"],["0B700770014A","",""],"0B7007700141",""],[["0A040648","0B3013300730","0B70137007700152","0A061206066C0152"],["0A040648","0B3013300730","0B70137007700152","0A061206066C0152"],"0A04120406430102","0A04120406460102"],["0B70137007700141","0B70137007700141","0A04120406430101","0A04120406460101"],["0B70137007700142","0B70137007700142","0A04120406430102","0A04120406460102"],["0B70137007700141","0B70137007700141","0A04120406430101","0A04120406460101"],[["0A0A06A3","","",""],"0B70137007700108","",""],[["0A0A06A3","","",""],"0B70137007700108","",""],[["0A0A06A3","","",""],"0B701370077001400108","",""],[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],["0A040648","0B3013300730","0A0F137007700108",""],"",""],[["0A0A06A9","","",""],["0A040648","0B3013300730","0A0F137007700108",""],"",""],[["0A0A06A9","","",""],["0A040648","0B3013300730",["0A0F137007700148","",""],["0A0F1206066C0148","",""]],"",""],[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],["0B70137007700148","",""],"",""],[["0A0A06A9","","",""],["0B70137007700148","",""],"",""],["","0B70137007700140","",""],["","0B70137007700140","",""],[["0A0A070C","","",""],["0A04070C0108","","0A04070C0108"],"",""],[[["0A0A06A9","","",""],["0B700770","0B700770",["0B7007700108","","0B700770"],["0A06066C0128","","0A06066C0120"]],["0A040710","0B700770",["0B700770","","0B7007700108"],""],["","",["0B7007700108","","0B700770"],""]],[["0A0A06A9","","",""],["0B700770","0B700770",["0B7007700108","","0B700770"],["0A06066C0148","","0A06066C0140"]],["0A040710","0B700770",["0B700770","","0B7007700108"],""],["","",["0B7007700108","","0B700770"],""]]],[["0A0A06A90C00","","",""],["0A0406480C00","0B3007300C00",["0B7007700C000108","",""],["0A06066C0C000108","",""]],"0B7007700C000108","0B7007700C000108"],["",["","",[["060A0C00","","",""],"137007700C000108","",""],"",[["060A0C00","","",""],"137007700C000108","",""],"",[["060A0C00","","",""],"137007700C000108","",""],""]],[["",["","",["137007700C000148","","137007700C000140"],""],"",""],["",["","",["137007700C000148","","137007700C000140"],""],"",""],[["060A0C00","","",""],["06480C00","133007300C00",["137007700C000148","",""],["1206066C0C000148","",""]],"",""],"",[["060A0C00","","",""],["06480C00","133007300C00",["137007700C000148","","137007700C000140"],["1206066C0C000148","",""]],"",""],"",[["060A0C00","","",""],["06480C00","133007300C00",["137007700C000148","",""],["1206066C0C000148","",""]],"",""],""],["",["","",[["137007700C00","137007700C00","",""],"137007700C000140","",""],["","137007700C000108","",""],"","",[["137007700C00","137007700C00","",""],"137007100C000140","",""],["","137007700C000108","",""]]],[["0A0A06A9","","",""],["0A040710","13300B300730","0A0F137007700108",""],"",""],[["0A0A06A9","","",""],["0A040710","13300B300730","0A0F137007700108",""],"",""],[["0A0A06A9","","",""],["0A040710","13300B300730",["0A0F137007700148","",""],["0A0F1206066C0148","",""]],"",""],[["",["","",""],"",""],"","",""],[["07080B080180","",["0B7007700141","","0B3807700149"],""],["064F0C000C00","",["0B7007380149","","0B7007700141"],""],["","","0B0C06440109",""],["0A04064F0C000C00","","0B0C06460109",""]],[["0B0807080180","",["0B7007700142","","0B380770014A"],""],["0A04064F","",["0B700738014A","","0B7007700142"],""],["","","0B0C0644010A",""],["0A04064F","","0B0C0646010A",""]],["",["","",["0B7007380149","","0B7007700141"],""],["","",["0B7007380142","","0B700770014A"],"0A06065A0170"],["","",["0B700770014A","","0B3807700142"],""]],["",["","",["0B700738014A","","0B7007700142"],""],["","","0A041204070C010A",""],["","","0A041204070C010A",""]],["",["0A040604","0B7013700770","",""],"",["0A040604","0B7013700770","",""]],["",["0A040604","0B7013700770","",""],"",["0A040604","0B7013700770","",""]],[["070C0A0A","","",""],["06240A040108","","06360A040108"],["0A040646","0A040646",["","","0A0406460108"],""],""],[["06A90A0A","","",""],["06480A04","07300B30",["07700B700108","","07700B70"],["066C0A060128","","066C0A060120"]],["06480A04","07300B30",["07700B70","","07700B700108"],""],["","",["07700B700108","","07700B70"],""]],"1106000C","1106000C","1106000C","1106000C",[["1106000C","120F1002","",""],"","",""],[["1106000C","120F1002","",""],"","",""],"1106000C","1106000C","1106000C","1106000C","1106000C","1106000C","1106000C","1106000C","1106000C","1106000C",[["0600",["0A0F06F2","","0A0F06F6"],"",""],["0600",["0A0F06F0","","0A0F06F4"],"",""],"",""],[["0600",["06120A0F","","06360A0F"],"",""],["0600",["06000A0F","","06240A0F"],"",""],"",""],[["0600",["0A0F062F","",""],"",""],["0600",["0A0F062F","",""],"",""],"",["0600",["0A0F062F","","0A0F063F"],"",""]],[["0600",["062F0A0F","",""],"",""],["0600",["062F0A0F","",""],"",""],"",["0600",["062F0A0F","","063F0A0F"],"",""]],"0600",[["0600","0A03120F06FF","",""],"","",""],"0600",[["0600","0A03120F06FF","",""],"","",""],[["0600",["0A0F06FF","","0A0F06FF"],"",""],["0600",["0A0F06FF","","0A0F06FF"],"",""],"",""],[["0600",["0A0F06FF","","0A0F06FF"],"",""],["0600",["0A0F06FF","","0A0F06FF"],"",""],"",""],"0600","0600","0600","0600","0600","0600","2608","2608","","070E0B0E0003","070E0B0E0C00","070E0B0E1800","0B0E070E","070E0B0E","2808","2808","","070E0B0E0003","070E0B0E0C00","070E0B0E1800",[[["0601","","0601"],["0601","","0601"],"0603","0603",["0601","","0601"],["0601","","0601"],["0601","0601","0601"],["0601","0601",""]],[["","",["0602","","",""],""],["","",["0602","","",""],""],["","",["0602","","",""],""],["","",["0602","","",""],""],"",["","","","","","","",""],["","","","","","","",""],["","","","","","","",""]]],"0B0E070E","06000A000003","070E0B0E0003",["0B0E090E",""],"070E0B0E0003",["0B0E090E",""],["0B0E090E",""],"0B0E0600","0B0E0602",[["1002","","",""],"",["0B060706","0A020602","",""],""],"",["","","","","070E0C000003","070E0C000003","070E0C000003","070E0C000003"],"0B0E070E0003",[["0B0E070E0180","","",""],"",["0B0E070E0180","0A020602","",""],["0B0E070E0180","0A020602","",""]],[["0B0E070E0180","","",""],"",["0B0E070E0180","0A020602","",""],["0B0E070E0180","","",""]],"0B0E0600","0B0E0602","06000A000003","070E0B0E0003",[["0A0406480C00","0B30133007300C00","0A0F137007700C000151","0A0F066C0C000151"],["0A0406480C00","0B30133007300C00","0A0F137007700C000151","0A0F066C0C000151"],["0A0406440C00","0A04120406480C00","0A0F120406440C000151",""],["0A0406490C00","0A04120406480C00","0A0F120406460C000151",""]],["06030A02",""],[["0A0A06220C00","","",""],"0A04120406220C000108","",""],["",[["06020A0A0C00","","",""],"06020A040C000108","",""]],["0B70137007700C000140","0B70137007700C000140","",""],[["",["06060003","","060B0003"],"",["0601","","0601"],["0601","","0601"],["0601","","0601"],["0606","0606","0606",""],["0606","","",""]],["",["","","","","","","",""],"","","","","070E","070E"]],"030E","030E","030E","030E","030E","030E","030E","030E",["",["0A040648","0B3013300730","",""],"",["0A040648","0B3013300730","",""]],[["0A0A06A9","","",""],"0B70137006480108","",""],[["0A0A06A9","","",""],["0A040648","0B3013300648",["0B70137006480108","",""],""],"",""],[["0A0A06A9","","",""],"0B70137006480100","",""],[["0A0A06A9","","",""],"0B70137007700140","",""],[["0A0A06A9","","",""],"0B70137007700108","",""],[["","06490A040100","",""],["","06490A040100",["0A040649","","",""],["0A040649","","",""]]],["",[["0B0C06A0","","",""],["0B0C0640","0B0C0730","",""],"",""]],[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],["0A040648","0B3013300730",["0B70137007700148","","0B70137007700140"],["0A061206066C0148","","0A061206066C0140"]],"",""],[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],["0A040648","0B3013300730",["0B70137007700148","","0B70137007700140"],["0A061206066C0148","","0A061206066C0140"]],"",""],[["0A0A06A9","","",""],"0B70137007700108","",""],[[["0A0A06A9","","",""],["0A040648","0B3013300648","0B70137006480108",""],"",""],[["0A0A06A9","","",""],["0A040648","0B3013300730","0B70137006480108",""],"",""]],[["0A0A06A9","","",""],["0A040648","0B3013300648",["0B70137006480108","","0B7013700648"],""],"",""],[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],"0B70137007700108","",""],["",["0A040648","0A040730","0B3807700141",""],["0A040649","0B300738",["0A0406480140","0B7007380140","0B700770014A"],"0A06065A0170"],"0B3807700142"],[[["06090A0A","","",""],["07700B700108","",""],"",""],""],[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],["0A040648","0B3013300730",["0B70137007700148","","0B70137007700140"],["0A061206066C0148","","0A061206066C0140"]],"",""],[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],["0A040648","0B3013300730",["0B70137007700148","","0B70137007700140"],["0A061206066C0148","","0A061206066C0140"]],"",""],[["","","",["0A040648","0A040730","",""]],"0000"],[["0A0A06A9","","",""],"0B70137006480108","",""],[["0A0A06A9","","",""],["0B70137006480108","",""],"",""],[["0A0A06A9","","",""],"0B7013700648","",""],[["0A0A06A9","","",""],"0B70137007700140","",""],[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],"0B70137007700108","",""],["",[["0A0A060A","","",""],["0B040648","0B040648","",""],"",""]],[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],["0A040648","0B3013300730",["0B70137007700148","",""],["0A061206066C0148","",""]],"",""],[["0A0A06A9","","",""],"0B70137007700140","",""],[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],["0A040648","0B3013300730",["0B70137007700148","",""],["0A061206066C0148","",""]],"",""],"",[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],["0A040648","0B3013300730","",""],"",""],[["0A0A06A9","","",""],["0A040648","0B3013300730","",""],"",""],[["0A0A06A9","","",""],["0A040648","0B3013300730","",""],"",""],[["0A0A06A9","","",""],"0B70137007700108","",""],[["0A0A06A9","","",""],["0A040648","0B3013300730","",""],"",""],[["0A0A06A9","","",""],["0A040648","0B3013300730","",""],"",""],[["0A0A06A9","","",""],["0A040648","0B3013300730","",""],"",""],[["0A0A06A9","","",""],["0A040648","0B3013300730","",""],"",""],[["0A0A06A9","","",""],["0A040648","0B3013300730","",""],"",""],[["0A0A06A9","","",""],["0A040648","0B3013300730","",""],"",""],[["0A0A06A9","","",""],"0B70137007700108","",""],["",["","0B3013300730",["0B70137007700148","",""],""],"",""],["",["","0B3013300730","0B70137007700140",""],"",""],["",["","0B300730","",""],"",""],["",["","0B300730","",""],"",""],["",["0A0406482E00","0B30133007301530","0B7013700770",""],["","","07380B70",""],""],["",["","","0B7013700770",""],["","","071C0B70",""],""],["",["","","0B7013700770",""],["","","070E0B70",""],""],["",["","0B300718",["0B7007380109","",""],""],["","","07380B70",""],""],["",["0A0407102E00","0B30133007301530",["0B70137007700148","","0B70137007700140"],""],["","","071C0B70",""],""],["",["0A0407102E00","0B30133007301530",["0B70137007700148","","0B70137007700140"],""],["","","07380B70",""],""],["",["","0B3013300730",["0B70137007700148","","0B70137007700140"],""],"",""],["",["0A040648","0B300730","",""],"",""],["",["","0B300644",["0B7006440138","",""],["0A0606440138","",""]],"",""],["",["","0A050646",["0B6806460108","","0B700646"],["","","0A060646"]],"",""],["",["","0A050648",["0B6806480138","","0B680648"],["0A0606480138","",""]],"",""],["",["","",["0A06065A0108","","0A06065A"],["","","0A06065A"]],"",""],[["0A0A06A9","","",""],"0B7007700108","",""],[["0A0A06A9","","",""],"0B7007700108","",""],[["0A0A06A9","","",""],["0B7007700148","",""],"",""],["",["","","0B7007700140",""],"",""],["","0B7007380108",["","","07380B70",""],""],["","0B70071C0108",["","","071C0B70",""],""],["","0B70070E0108",["","","070E0B70",""],""],["","0B7007380108",["","","07380B70",""],""],["","0B70071C0108",["","","071C0B70",""],""],["","0B7007380108",["","","07380B70",""],""],["",["","",["0A0F137007700108","","0A0F13700770"],""],["","",["0A0F13700770","","0A0F137007700108"],""],""],["",["","",["0A0F137007700148","","0A0F137007700140"],["0A0F1206066C0148","",""]],["","",["0A0F137007700140","","0A0F137007700148"],""],""],["","0B70137007700140",["","",["0B7006FF","","0B7006FF0108"],""],""],["",["0A040648","0B3013300730","0A0F137007700140",""],["","",["0A0F0770","","0A0F07700108"],""],""],[["",["0B7007700108","",""],"",""],["","",["","",["","","0B7006FF0108"],""],""]],["",["0B70137007700148","",""],"",""],["",["","0B3013300730",["0B7013700770014A","","0B70137007700142"],""],"",""],["",["","0B3013300730",["0A0412040644014A","","0A04120406480142"],""],"",""],["",["","073013300B30","",""],"",""],["",["","0B3013300730","",""],"",""],["","0B7007380108",["","","07380B70",""],""],["","0B70071C0108",["","","071C0B70",""],""],["","0B70070E0108",["","","070E0B70",""],""],["","0B7007380108",["","","07380B70",""],""],["","0B70071C0108",["","","071C0B70",""],""],["","0B7007380108",["","",["06480A04","07380B70",""],""],""],["",["","0A051205065A",["0B70137007700148","","0B70137007700140"],["0A061206066C0108","",""]],"",""],["",["0A040710","0B3013300730","0A0F137007700140",""],"",""],["","0B70137007700108",["","",["0B7006FF","","0B7006FF0108"],""],""],["",["0A0412040648","0B3013300730",["0B70137007700148","","0B70137007700140"],["0A061206066C0148","",""]],["","",["0A0F0770","","0A0F07700108"],""],""],["","0B70137007700108",["","","0B7006FF0100",""],""],["",["0A0412040648","0B3013300730",["0B70137007700148","","0B70137007700140"],["0A061206066C0148","",""]],"",""],["","0B70137007700108","",""],["",["0A0412040648","0B3013300730",["0B70137007700148","","0B70137007700140"],["0A061206066C0148","",""]],"",""],["","0B70137007700108","",""],["",["0A0412040648","0B3013300730",["0B70137007700148","","0B70137007700140"],["0A061206066C0148","",""]],"",""],["",["0A0412040648","0B3013300730",["0B70137007700148","","0B70137007700140"],["0A061206066C0148","",""]],"",""],["",["0A040648",["0A040648","0A040648","",""],"",""],"",""],["",["","",["0B7007700159","","0B7007700151"],["0A06066C0159","","0A06066C0151"]],"",""],["",["","",["0A0412040644010A","","0A04120406460102"],""],"",""],["",["","",["0B7007700148","","0B7007700140"],""],"",""],["",["",["0B3013300730","","0B3013300730"],["0B70137007700148","","0B70137007700140"],["0A061206066C0148","",""]],"",""],["",["",["0B3013300730","",""],["0B70137007700148","","0B70137007700140"],["0A061206066C0148","",""]],"",""],["",["",["0B3013300730","","0B3013300730"],["0B70137007700148","","0B70137007700140"],["0A061206066C0148","",""]],"",""],"","","","",["",["","",["0B7007700148","","0B7007700140"],""],"",""],["",["","",["0A04120406440108","","0A0412040646"],""],"",""],["",["","",["0B7007700148","","0B7007700140"],""],"",""],["",["","",["0A04120406440108","","0A0412040646"],""],"",""],["",["","","",["0A061206066C015A","","0A061206066C0152"]],"",""],["",["","","",["0A061206066C0159","",""]],"",""],["",["","","",["0A061206066C0159","","0A061206066C0151"]],"",""],["",["","","",["0A061206066C0159","","0A061206066C0151"]],"",""],"",["",["","","",["0A061206066C0149","","0A061206066C0141"]],"",""],"","",["",["","0B300644",["0B7006440128","",""],["0A0606440128","",""]],"",""],["",["","0B300646",["0B7006460128","","0B7006460120"],["","","0A0606460120"]],"",""],["",["","0A050648",["0B6806480128","","0B6806480120"],["0A0606480128","",""]],"",""],["",["","",["0A06065A0128","","0A06065A0120"],["","","0A06065A0120"]],"",""],["",["","","",["0A06120F066C0148","",""]],"",""],["",["","","",["0A06120F066C0148","",""]],"",""],["",["","","",["0A06120F066C0148","",""]],"",""],["",["","","",["0A06120F066C0148","",""]],"",""],"","","","",["",["","",["0B70137007700148","","0B70137007700140"],["0A061206066C0148","","0A061206066C0140"]],"",""],["",["","",["0B70137007700158","","0B70137007700150"],["0A061206066C0158","","0A061206066C0150"]],"",""],["",["","",["0B70137007700108","","0B7013700770"],""],"",""],"","","","","",["",["","","",["0A061206066C0148","",""]],"",""],["",["","","",["0A061206066C015A","","0A061206066C0152"]],"",""],["",["","","",["0A06120F066C0148","",""]],"",""],["",["","","",["0A06120F066C0148","",""]],"",""],"","","","",["",["","","",["0A0F1206066C0148","",""]],"",""],["",["","",["0B70137007700108","","0B7013700770"],""],"",""],["",["","",["0B70137007700148","","0B70137007700140"],""],"",""],["",["","",["0B70137007700148","","0B70137007700140"],""],"",""],["",["","0B300640",["0B7006400108","",""],""],"",""],["",["","0B300642",["0B7006420108","",""],""],"",""],["",["",["","",["0B7006000108","",""],""],"",""]],["",["",["","",["0B7006100108","",""],""],"",""]],["",["","",["0B70062F0108","","0B70063F"],""],"",""],["",["","",["0B70137007700108","","0B7013700770"],""],"",""],["",["","",["0B70137007700148","","0B70137007700140"],""],"",""],["",["","",["0B70137007700148","","0B70137007700140"],""],"",""],[["","0B0C060B0180","",""],""],[["","0B0C060B0180","",""],""],[["","0B0C060B0180","",""],""],["",["","","0B70137007700140",""],"",""],["",["","","",["0A061206066C014A","",""]],"",""],"",["",["","","",["0A061206066C0148","",""]],"",""],["",["","","",["0A061206066C0148","",""]],"",""],["",["","",["0B7007700108","","0B700770"],""],"",""],["",["","",["0B7007700108","","0B700770"],""],"",""],["",["","",["07700B700108","","07700B70"],""],"",""],["",["","",["07700B700108","","07700B70"],""],"",""],"",["",["","",["0B70137007700108","","0B7013700770"],""],"",""],"","",["",["",["0B30073013300124","","0B30064813300124"],["0B700770012C","","0B7007380124"],["0A06066C012C","","0A06065A0124"]],"",""],["",["",["0A04073012040104","","0B30073013300104"],["0B380770010C","","0B7007700104"],""],"",""],["",["",["0B30073013300134","","0B30064813300134"],["0B700770013C","","0B7007380134"],["0A06066C013C","","0A06065A0104"]],"",""],["",["",["0A04073012040104","","0B30073013300104"],["0B380770010C","","0B7007700104"],""],"",""],"","",["",["",["0B3013300730","","0B3013300730"],["0B7013700770014A","","0B70137007700142"],""],"",""],["",["",["0B3013300730","","0B3013300730"],["0B7013700770014A","","0B70137007700142"],""],"",""],["",["",["0B3013300730","","0B3013300730"],["0B7013700770014A","","0B70137007700142"],["0A061206066C015A","","0A061206066C0152"]],"",""],["",["",["0A0412040714","","0A0412040718"],["0A0412040644010A","","0A04120406460102"],""],"",""],["",["",["0B3013300730","","0B3013300730"],["0B7013700770014A","","0B70137007700142"],["0A061206066C015A","","0A061206066C0152"]],"",""],["",["",["0A0412040714","","0A0412040718"],["0A0412040644010A","","0A04120406460102"],""],"",""],["",["",["0B3013300730","","0B3013300730"],["0B7013700770014A","","0B70137007700142"],["0A061206066C015A","","0A061206066C0152"]],"",""],["",["",["0A0412040714","","0A0412040718"],["0A0412040644010A","","0A04120406460102"],""],"",""],["",["",["0B3013300730","","0B3013300730"],["0B7013700770014A","","0B70137007700142"],["0A061206066C015A","","0A061206066C0152"]],"",""],["",["",["0A0412040714","","0A0412040718"],["0A0412040644010A","","0A04120406460102"],""],"",""],["",["","",["07700B70010C","","07380B700104"],["066C0A06012C","","065A0A060124"]],"",""],["",["","",["07700B38010C","","07700B700104"],""],"",""],["",["","",["07700B70013C","","07380B700134"],["066C0A06013C","","065A0A060134"]],"",""],["",["","",["07700B38010C","","07700B700104"],""],"",""],["",["","","",["0A061206066C011A","",""]],"",""],"",["",["",["0B3013300730","","0B3013300730"],["0B7013700770014A","","0B70137007700142"],""],"",""],["",["",["0B3013300730","","0B3013300730"],["0B7013700770014A","","0B70137007700142"],""],"",""],["",["",["0B3013300730","","0B3013300730"],["0B7013700770014A","","0B70137007700142"],["0A061206066C015A","","0A061206066C0152"]],"",""],["",["",["0A0412040644","","0A0412040646"],["0A0412040644010A","","0A04120406460102"],""],"",""],["",["",["0B3013300730","","0B3013300730"],["0B7013700770014A","","0B70137007700142"],["0A061206066C015A","","0A061206066C0152"]],"",""],["",["",["0A0412040644","","0A0412040646"],["0A0412040644010A","","0A04120406460102"],""],"",""],["",["",["0B3013300730","","0B3013300730"],["0B7013700770014A","","0B70137007700142"],["0A061206066C015A","","0A061206066C0152"]],"",""],["",["",["0A0412040644","","0A0412040646"],["0A0412040644010A","","0A04120406460102"],""],"",""],["",["",["0B3013300730","","0B3013300730"],["0B7013700770014A","","0B70137007700142"],["0A061206066C015A","","0A061206066C0152"]],"",""],["",["",["0A0412040644","","0A0412040646"],["0A0412040644010A","","0A04120406460102"],""],"",""],"","","","",["",["","","0B70137007700140",["0A061206066C0118","",""]],"",""],["",["","","0B70137007700140",["0A061206066C0148","",""]],"",""],["",["",["0B3013300730","","0B3013300730"],["0B7013700770014A","","0B70137007700142"],""],"",""],["",["",["0B3013300730","","0B3013300730"],["0B7013700770014A","","0B70137007700142"],""],"",""],["",["",["0B3013300730","","0B3013300730"],["0B7013700770014A","","0B70137007700142"],["0A061206066C015A","","0A061206066C0152"]],"",""],["",["",["0A0412040644","","0A0412040646"],["0A0412040644010A","","0A04120406460102"],""],"",""],["",["",["0B3013300730","","0B3013300730"],["0B7013700770014A","","0B70137007700142"],["0A061206066C015A","","0A061206066C0152"]],"",""],["",["",["0A0412040644","","0A0412040646"],["0A0412040644010A","","0A04120406460102"],""],"",""],["",["",["0B3013300730","","0B3013300730"],["0B7013700770014A","","0B70137007700142"],["0A061206066C015A","","0A061206066C0152"]],"",""],["",["",["0A0412040644","","0A0412040646"],["0A0412040644010A","","0A04120406460102"],""],"",""],["",["",["0B3013300730","","0B3013300730"],["0B7013700770014A","","0B70137007700142"],["0A061206066C015A","","0A061206066C0152"]],"",""],["",["",["0A0412040644","","0A0412040646"],["0A0412040644010A","","0A04120406460102"],""],"",""],"","","","",["",["","",["0B7007700148","","0B7007700140"],""],"",""],"",[[["",["","","",["060C013C","","060A0134"]],"",""],["",["","",["060C013C","","060A0134"],["060C013C","",""]],"",""],["",["","",["060C013C","","070A0134"],["060C013C","",""]],"",""],"",["",["","","",["060C013C","","060A0134"]],"",""],["",["","",["060C013C","","060A0134"],["060C013C","",""]],"",""],["",["","",["060C013C","","060A0134"],["060C013C","",""]],"",""],""],""],[["",["",["","",["060C010C","","060C0104"],""],"",""],["",["","",["060C010C","","060C0104"],""],"",""],"","",["",["","",["060C010C","","060C0104"],""],"",""],["",["","",["060C010C","","060C0104"],""],"",""],""],""],[["0A040648","","",""],["","",["0A06066C0159","","0A06066C0151"],["0A06066C0109","",""]],"",""],[["0A040648","","",""],["","","",["0A06066C0109","",""]],"",""],[["0A040648","","",""],["","",["0A06066C0159","","0A06066C0151"],["0A06066C0109","",""]],"",""],[["0A0406482E00","","",""],["","",["0A04120406440109","","0A04120406460101"],["0A06066C0109","",""]],"",""],[["0A040648","","",""],["","",["0A06066C0159","","0A06066C0151"],["0A06066C015A","",""]],"",""],[["0A040648","","",""],["","",["0A04120406440109","","0A04120406460101"],["0A06066C0148","",""]],"",""],"","",[[["","","",["0A06060C0120","","0A06060C0128"]],["","","",["060C0A060128","","060C0A060120"]],"",""],""],[[["","","",["0A06060C0130","","0A06060C0138"]],["","","",["060C0A060138","","060C0A060130"]],"",""],""],"","",[[["","","",["0A06060C0120","","0A06060C0128"]],["","","",["060C0A060128","","060C0A060120"]],"",""],""],[[["","","",["0A06060C0130","","0A06060C0138"]],["","","",["060C0A060138","","060C0A060130"]],"",""],""],"","","","","",["",["0A040648","0A040648","",""],"",""],["",["0A040648","0A0412040648","",""],"",""],["",["0A040648","0A0412040648","",""],"",""],["",["0A040648","0A0412040648","",""],"",""],["",["0A040648","0A0412040648","",""],"",""],"","","","","","","","","","","","","","","","",[["0B0E070E0180","","",""],["0B0E070E0180","","",""],"",["0B0C06000180","","",""]],[["070E0B0E0180","","",""],["070E0B0E0180","","",""],"",["0B0C070E0180","","",""]],["",["","0B0C130C070C","",""],"",""],["",["",["","130C070C","",""],"",""],["",["","130C070C","",""],"",""],["",["","130C070C","",""],"",""],"","","",""],"",[["","0B0C070C130C","",""],"",["","0B0C130C070C","",""],["","0B0C130C070C","",""]],["",["0B0C070C","","",""],["0B0C070C","","",""],["","0B0C130C070C1B0C","",""]],[["","0B0C130C070C","",""],["","0B0C130C070C","",""],["","0B0C130C070C","",""],["","0B0C130C070C","",""]],"","","","","","","","",["",["","0A05065A0C00","0B7007700C000140",""],"",""],["",["","0A05065A0C00","0B7007700C000140",""],"",""],["",["",["0B30133007300C00","",""],"",""],"",""],["",["","",["0B70137007700C000148","","0B70137007700C000140"],["0A061206066C0C000108","",""]],"",""],["",["","0B3007300C00",["0B7007700C000148","",""],""],"",""],["",["","0B3007300C00","0B7007700C000140",""],"",""],["",["","0A051205065A0C00","",""],"",""],["",["","","",["0A06066C0C000108","",""]],"",""],["",["0A0406480C00","0B3007300C00",["0B7007700C000149","",""],""],"",""],["",["0A0406480C00","0B3007300C00","0B7007700C000141",""],"",""],["",["0A0406440C00","0A04120406440C00",["0A04120406440C000109","",""],""],"",""],["",["0A0406460C00","0A04120406460C00","0A04120406460C000101",""],"",""],["",["0A0406480C00","0B30133007300C00","",""],"",""],["",["0A0406480C00","0B30133007300C00","",""],"",""],["",["0A0406480C00","0B30133007300C00","",""],"",""],[["0A0A06A90C00","","",""],"0B70137007700C000108","",""],"","","","",[["","06000A040C000108","",""],["","070C0A040C000108","",""]],[["","06020A040C000108","",""],["","070C0A040C000108","",""]],["",["06240A040C000108","","06360A040C00"],"",""],["","070C0A040C000108","",""],["",["","0A05120506480C00",["0B70137006480C000108","","0B70137006480C00"],""],"",""],["",["","06480A050C00",["06480B700C000108","","06480B700C00"],""],"",""],["",["","",["0A061206065A0C000108","","0A061206065A0C00"],""],"",""],["",["","",["065A0A060C000108","","065A0A060C00"],""],"",""],"",["",["","07180B300C00",["07380B700C000109","",""],""],"",""],["",["","",["0A0F137007700C000148","","0A0F137007700C000140"],["0A0F1206066C0C000148","",""]],"",""],["",["","",["0A0F137007700C000148","","0A0F137007700C000140"],["0A0F1206066C0C000148","",""]],"",""],["","0A04120406200C000108","",""],["",["0A04120406440C000108","",""],"",""],["",["",["0A04120406240C00","","0A04120406360C00"],["0A04120406240C000108","","0A04120406360C00"],""],"",""],["",["","",["0B70137007700C000148","","0B70137007700C000140"],""],"",""],"",["",["","",["0B70137007700C000148","","0B70137007700C000140"],""],"",""],["",["","",["0B7007700C000149","","0B7007700C000141"],["0A06066C0C000159","","0A06066C0C000151"]],"",""],["",["","",["0A04120406440C000109","","0A04120406460C000101"],""],"",""],"","","","","","","","",["",["",["0A0F06FF0C00","","0A0F06FF0C00"],"",""],"",""],["",["",["0A0F06FF0C00","","0A0F06FF0C00"],"",""],"",""],["",["",["0A0F06FF0C00","","0A0F06FF0C00"],"",""],"",""],["",["",["0A0F06FF0C00","","0A0F06FF0C00"],"",""],"",""],"","","","",["",["","0A05120506480C00",["0B70137006480C000108","","0B70137006480C00"],""],"",""],["",["","06480A050C00",["06480B700C000108","","06480B700C00"],""],"",""],["",["","",["0A061206065A0C000108","","0A061206065A0C00"],""],"",""],["",["","",["065A0A060C000108","","065A0A060C00"],""],"",""],"","",["",["","0A0F063F0C00",["0A0F137007700C000108","","0A0F137007700C00"],""],"",""],["",["","",["0A0F137007700C000108","","0A0F137007700C00"],""],"",""],["",["0A0406480C00","0B30133007300C00","",""],"",""],["",["0A0406480C00","0A04120406480C00","",""],"",""],["",["0A0406480C00","0B30133007300C00",["0B70137007700C000108","",""],""],"",""],["",["","",["0B70137007700C000148","","0B70137007700C000140"],""],"",""],["",["0A0406480C00","0A04120406480C00","",""],"",""],"",["",["","0A051205065A0C00","",""],"",""],"",["",["",["0B301330073015300E00","","0B301330153007300E00"],"",""],"",""],["",["",["0B301330073015300E00","","0B301330153007300E00"],"",""],"",""],["",["","0B30133007301530","",""],"",""],["",["","0B30133007301530","",""],"",""],["",["","0A051205065A1505","",""],"",""],"","","",["",["","",["0B70137007700C000149","","0B70137007700C000141"],""],"",""],["",["","",["0A04120406440C000109","","0A04120406460C000101"],""],"",""],["",["","","",["0A06066C0C000159","","0A06066C0C000151"]],"",""],"",["",["","",["0B70137007700C000149","","0B70137007700C000141"],""],"",""],["",["","",["0A04120406440C000109","","0A04120406460C000101"],""],"",""],["",["","",["0B7007700C000149","","0B7007700C000141"],""],"",""],["",["","",["0A04120406440C000109","","0A04120406460C000101"],""],"",""],"","","","",["",["",["0B30133007301530","","0B30133015300730"],"",""],"",""],["",["",["0B30133007301530","","0B30133015300730"],"",""],"",""],["",["",["0B30133007301530","","0B30133015300730"],"",""],"",""],["",["",["0B30133007301530","","0B30133015300730"],"",""],"",""],["",["0A0406480C00","0A0406480C00","",""],"",""],["",["0A0406480C00","0A0406480C00","",""],"",""],["",["0A0406480C00","0A0406480C00","",""],"",""],["",["0A0406480C00","0A0406480C00","",""],"",""],"","",["",["","",["0A0F07700C000148","","0A0F07700C000140"],""],"",""],["",["","",["0A0F06440C000108","","0A0F06460C00"],""],"",""],["",["",["0B30133007301530","","0B30133015300730"],"",""],"",""],["",["",["0B30133007301530","","0B30133015300730"],"",""],"",""],["",["",["0A04120406441530","","0A04120415300644"],"",""],"",""],["",["",["0A04120406461530","","0A04120415300646"],"",""],"",""],["",["",["0B30133007301530","","0B30133015300730"],"",""],"",""],["",["",["0B30133007301530","","0B30133015300730"],"",""],"",""],["",["",["0A04120406441530","","0A04120415300644"],"",""],"",""],["",["",["0A04120406461530","","0A04120415300646"],"",""],"",""],"","","","","","","","",["",["",["0B30133007301530","","0B30133015300730"],"",""],"",""],["",["",["0B30133007301530","","0B30133015300730"],"",""],"",""],["",["",["0A04120406441530","","0A04120415300644"],"",""],"",""],["",["",["0A04120406461530","","0A04120415300646"],"",""],"",""],["",["",["0B30133007301530","","0B30133015300730"],"",""],"",""],["",["",["0B30133007301530","","0B30133015300730"],"",""],"",""],["",["",["0A04120406441530","","0A04120415300644"],"",""],"",""],["",["",["0A04120406461530","","0A04120415300646"],"",""],"",""],"","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","",[["","","","0A06066C0C000141"],["","","",["0A06066C0C000159","",""]],"",["","","","0A06066C0C000151"]],[["","","","0A06066C0C000141"],["","","",["0A06066C0C000159","",""]],"",""],"0A0406480C00","","","","","","","","","","","","","","","","","","",["",["0A0406480C00","0A0406480C00","",""],"",""],"","","","","","",["","","",["","","","0A06066C0C000151"]],"","","","","","","","","",["","","",["","0B0C070C0C00","",""]],"","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","0A04120406481404","0A04120406481404","0A04120406481404","","","","","","","0A04120406481404","0A04120406481404","","","","","","0A04120406481404","0A04120406481404","0A04120406481404","","","","","","","0A04120406481404","0A04120406481404","","",["0B30133007301530","","0B30133015300730"],["0A04120406481404","","0A04120414040648"],"","","0A04120406481404","","","","","","","","","","","","","","","","0A04120406481404","","","","","","","","","","0A0406480C00","0A0406480C00","0A0406480C00","0A0406480C00","","","","","","","","","0A04120406480C00","0A04120406480C00","0A04120406480C00","0A04120406480C00","","","","","","","","","","","","","","","","","","","","","","","","","","","","","0A04120406480C00","0A04120406480C00","0A04120406480C00","0A04120406480C00","","","","","","","","","","","","","","","","","",["","130C070C","130C070C","130C070C","130C070C","130C070C","130C070C","130C070C"],["","130C070C","","","","","130C070C",""],"","","","","","","","","","","","","","","",["",["070C","070C","","","","","",""]],"","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","0B300730","0B300730","0B300730","0B300730","","","","","","","","","","","","",["0A0406481204","","0A0412040648"],["0A0406481204","","0A0412040648"],["0A0406481204","","0A0412040648"],["0A0406481204","","0A0412040648"],["0A0406481204","","0A0412040648"],["0A0406481204","","0A0412040648"],["0A0406481204","","0A0412040648"],["0A0406481204","","0A0412040648"],["0A0406481204","","0A0412040648"],["0A0406481204","","0A0412040648"],["0A0406481204","","0A0412040648"],["0A0406481204","","0A0412040648"],"","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","0A040648","0A040648","0A040648","","","0A040648","0A040648","","","","0A040648","","","","","","0A040648","0A040648","0A040648","","","0A040648","0A040648","","","","0A040648","","","","","","0A040648","0A040648","0A040648","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","0B0C070C0C020180","",["130C06240C020180","130C06240C020180","","","","","",""],"","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","1206","","","","","","","","","","","",[["0A0606610120","0A0606610120","",""],""],"",[["0A0606610120","0A0606610120","",""],""],[["0A0606610120","0A0606610120","",""],""],[["0A0606610100","0A0606610100","",""],""],"",[["0A0606610100","0A0606610100","",""],""],[["0A0606610100","0A0606610100","",""],""],["0A06066C0124",""],["066C0124",""],"",["066C0124",""],["066C0A060104",""],["066C0104",""],"",["066C0104",""],["0A0F120606610150","0A0F120606610150","",""],"0A0F120606610140","0A0F120606610140","",["0A0F120606610150","0A0F120606610150","",""],"0A0F120606610140","0A0F120606610140","","","","","","","","","","0A0F120606610140","","","","","","","","","","","","","","","",["0A06120606610150","0A06120606610150","",""],"0A06120606610140","","0A06120F06610140","","0A06120F06610140","0A06120606610150","0A06120606610140",["0A06120606610150","0A06120606610150","",""],"","","","","","","",["0A06120606610150","0A06120606610150","",""],"0A06120606610140","","0A06120F06610140","","0A06120F06610140","","",["0A06120606610150","0A06120606610150","",""],"0A06120606610140","","0A06120F06610140","","0A06120F06610140","","",["0A06120606610150","0A06120606610150","",""],"0A06120606610140",["0A06120606610150","0A06120606610150","",""],"",["0A06120606610150","0A06120606610150","",""],"","0A06120606610150","0A06120606610140",["0A06120606610150","0A06120606610150","",""],"",["0A06120606610150","0A06120606610150","",""],"",["0A06120606610150","0A06120606610150","",""],"","","",["0A06120606610150","0A06120606610150","",""],"",["0A06120606610150","0A06120606610150","",""],"",["0A06120606610150","0A06120606610150","",""],"","","",["0A06120606610150","0A06120606610150","",""],"",["0A06120606610150","0A06120606610150","",""],"",["0A06120606610150","0A06120606610150","",""],"",["0A06120606610150","0A06120606610150","",""],"",["0A06120606610150","0A06120606610150","",""],"0A06120606610140","0A06120606610140","0A06120606610140","","","0A06120606610150","0A06120606610140",["0A06120606610150","0A06120606610150","",""],"0A06120606610140","0A06120606610140","",["0A06120606610150","0A06120606610150","",""],"0A06120606610140","0A06120606610140","",["","0A0606610152","",""],["0A0606610153","0A0606610152","",""],["0A0606610153","0A0606610152","",""],"",["","0A0606610158","",""],["0A0606610141","0A0606610148","",""],["0A0606610141","0A0606610148","",""],"","0A0606610153","","0A0606610150","0A0606610152","","0A0606610150","0A0606610150","","0A06120606610140","0A06120606610140","0A06120606610140","",["0A06120606610140","0A06120606610140","",""],["0A06120606610140","0A06120606610140","",""],["0A06120606610140","0A06120606610140","",""],["0A06120606610140","0A06120606610140","",""],"0A06120606610140","0A06120606610140","","","","","","","0A0606610140","0A0606610150","0A0606610150","","0A0606610150","","","","0A06120606610140","","","","","","","","0A0606610150","","0A06120606610150","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","0A0606610C010150","0A0606610C000C00","0A06120606610C010140","0A0606610C010140","","","","","","","","","","","","",["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E0C010C000C00"],["","0B0E070E0C010C000C00"],["","0B0E070E0C010C000C00"],["","0B0E070E0C010C000C00"],["","0B0E070E0C010C000C00"],["","0B0E070E0C010C000C00"],["","0B0E070E0C010C000C00"],["","0B0E070E0C010C000C00"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],["","0B0E070E"],"","","","","06FF0A0F",[["0601","0601","0604","0604","","","",""],""],[["0601","0601","","","","","",""],""],[["0601","0601","","","","","",""],""],"06FF0A0F","06FF0B06","07060A0F","06FF0B06","06FF0A0F","06FF0A0F","06FF0A0F","06FF0A0F","06FF0A0F","06FF0A0F","06FF0A0F","06FF0A0F","","06FF0A0F",["",["0B07","0B07","","","","","",""]],["",["0B07","0B07","","","","","",""]]],O=["","","","","","","","","","","","","PI2FW","PI2FD","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","PFNACC","","","","PFPNACC","","PFCMPGE","","","","PFMIN","","PFRCP","PFRSQRT","","","FPSUB","","","","FPADD","","PFCMPGT","","","","PFMAX","","PFRCPIT1","PFRSQIT1","","","PFSUBR","","","","PFACC","","PFCMPEQ","","","","PFMUL","","PFRCPIT2","PMULHRW","","","","PSWAPD","","","","PAVGUSB","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","",""],m=["VMGETINFO","VMSETINFO","VMDXDSBL","VMDXENBL","","VMCPUID","VMHLT","VMSPLAF","","","VMPUSHFD","VMPOPFD","VMCLI","VMSTI","VMIRETD","VMSGDT","VMSIDT","VMSLDT","VMSTR","","VMSDTE","","","",""],V=["EQ","LT","LE","UNORD","NEQ","NLT","NLE","ORD","EQ_UQ","NGE","NGT","FALSE","NEQ_OQ","GE","GT","TRUE","EQ_OS","LT_OQ","LE_OQ","UNORD_S","NEQ_US","NLT_UQ","NLE_UQ","ORD_S","EQ_US","NGE_UQ","NGT_UQ","FALSE_OS","NEQ_OS","GE_OQ","GT_OQ","TRUE_US","LT","LE","GT","GE","EQ","NEQ","FALSE","TRUE"],U="",N="",L=function(){return{Type:0,BySizeAttrubute:!1,Size:0,OperandNum:0,Active:!1,set:function(e,t,r,n){this.Type=e,this.BySizeAttrubute=t,this.Size=r,this.OpNum=n,this.Active=!0},Deactivate:function(){this.Active=!1}}},I=[new L,new L,new L,new L,new L,new L,new L,new L,new L,new L,new L,new L],w=1,b=0,x=0,Q=!1,X=0,H=0,W=0,k="[",_=0,K=0,G=!1,Y=!1,j=!1,Z=0,z=!1,J=!1,$=!1,q=0,ee=0,te=0,re=0,ne=["","","","","","","","",", {Error}",", {Error}",", {Error}",", {Error}",", {SAE}",", {SAE}",", {SAE}",", {SAE}",", {RN}",", {RD}",", {RU}",", {RZ}",", {RN-SAE}",", {RD-SAE}",", {RU-SAE}",", {RZ-SAE}","0B","4B","5B","8B","16B","24B","31B","32B"],ie=["","CDAB","BADC","DACB","AAAA","BBBB","CCCC","DDDD","DACB"],oe=["","","1To16","1To8","4To16","4To8","Float16","Error","Float16RZ","Error","SRGB8","Error","UInt8","Error","SInt8","Error","UNorm8","Error","SNorm8","Error","UInt16","Error","SInt16","Error","UNorm16","Error","SNorm16","Error","UInt8I","Error","SInt8I","Error","UInt16I","Error","SInt16I","Error","UNorm10A","Error","UNorm10B","Error","UNorm10C","Error","UNorm2D","Error","Float11A","Error","Float11B","Error","Float10C","Error","Error","Error","Error","Error","Error","Error","Error","Error","Error","Error","Error","Error","Error","Error","Error","Error","Error","Error"],ue=0,ae=0,Ae=!1,se=0,ce="",fe="",Se=!1,le=!1,Be=!1,De=!1,Pe=!1,Ce=!1,Ee=[[["AL","CL","DL","BL","AH","CH","DH","BH"],["AL","CL","DL","BL","SPL","BPL","SIL","DIL","R8B","R9B","R10B","R11B","R12B","R13B","R14B","R15B"]],["AX","CX","DX","BX","SP","BP","SI","DI","R8W","R9W","R10W","R11W","R12W","R13W","R14W","R15W"],["EAX","ECX","EDX","EBX","ESP","EBP","ESI","EDI","R8D","R9D","R10D","R11D","R12D","R13D","R14D","R15D"],["RAX","RCX","RDX","RBX","RSP","RBP","RSI","RDI","R8","R9","R10","R11","R12","R13","R14","R15"],["XMM0","XMM1","XMM2","XMM3","XMM4","XMM5","XMM6","XMM7","XMM8","XMM9","XMM10","XMM11","XMM12","XMM13","XMM14","XMM15","XMM16","XMM17","XMM18","XMM19","XMM20","XMM21","XMM22","XMM23","XMM24","XMM25","XMM26","XMM27","XMM28","XMM29","XMM30","XMM31"],["YMM0","YMM1","YMM2","YMM3","YMM4","YMM5","YMM6","YMM7","YMM8","YMM9","YMM10","YMM11","YMM12","YMM13","YMM14","YMM15","YMM16","YMM17","YMM18","YMM19","YMM20","YMM21","YMM22","YMM23","YMM24","YMM25","YMM26","YMM27","YMM28","YMM29","YMM30","YMM31"],["ZMM0","ZMM1","ZMM2","ZMM3","ZMM4","ZMM5","ZMM6","ZMM7","ZMM8","ZMM9","ZMM10","ZMM11","ZMM12","ZMM13","ZMM14","ZMM15","ZMM16","ZMM17","ZMM18","ZMM19","ZMM20","ZMM21","ZMM22","ZMM23","ZMM24","ZMM25","ZMM26","ZMM27","ZMM28","ZMM29","ZMM30","ZMM31"],["?MM0","?MM1","?MM2","?MM3","?MM4","?MM5","?MM6","?MM7","?MM8","?MM9","?MM10","?MM11","?MM12","?MM13","?MM14","?MM15","?MM16","?MM17","?MM18","?MM19","?MM20","?MM21","?MM22","?MM23","?MM24","?MM25","?MM26","?MM27","?MM28","?MM29","?MM30","?MM31"],["ES","CS","SS","DS","FS","GS","ST(-2)","ST(-1)"],["ST(0)","ST(1)","ST(2)","ST(3)","ST(4)","ST(5)","ST(6)","ST(7)"],["MM0","MM1","MM2","MM3","MM4","MM5","MM6","MM7"],["BND0","BND1","BND2","BND3","CR0","CR1","CR2","CR3"],["CR0","CR1","CR2","CR3","CR4","CR5","CR6","CR7","CR8","CR9","CR10","CR11","CR12","CR13","CR14","CR15"],["DR0","DR1","DR2","DR3","DR4","DR5","DR6","DR7","DR8","DR9","DR10","DR11","DR12","DR13","DR14","DR15"],["TR0","TR1","TR2","TR3","TR4","TR5","TR6","TR7"],["K0","K1","K2","K3","K4","K5","K6","K7","K0","K1","K2","K3","K4","K5","K6","K7","K0","K1","K2","K3","K4","K5","K6","K7","K0","K1","K2","K3","K4","K5","K6","K7"],["V0","V1","V2","V3","V4","V5","V6","V7","V8","V9","V10","V11","V12","V13","V14","V15","V16","V17","V18","V19","V20","V21","V22","V23","V24","V25","V26","V27","V28","V29","V30","V31"]],he=["BYTE PTR ","","WORD PTR ","DWORD PTR ","DWORD PTR ","FWORD PTR ","QWORD PTR ","TBYTE PTR ","XMMWORD PTR ","MMWORD PTR ","YMMWORD PTR ","OWORD PTR ","ZMMWORD PTR ","ERROR PTR ","?MMWORD PTR ","ERROR PTR "],Fe=["","*2","*4","*8"];function Me(e){v[98]=["BOUND","BOUND",""],v[272]=[["MOVUPS","MOVUPD","MOVSS","MOVSD"],["MOVUPS","MOVUPD","MOVSS","MOVSD"]],v[273]=[["MOVUPS","MOVUPD","MOVSS","MOVSD"],["MOVUPS","MOVUPD","MOVSS","MOVSD"]],v[274]=[["MOVLPS","MOVLPD","MOVSLDUP","MOVDDUP"],["MOVHLPS","???","MOVSLDUP","MOVDDUP"]],v[275]=[["MOVLPS","MOVLPD","???","???"],"???"],v[312]="",v[313]="???",v[314]="",v[315]="???",v[316]="???",v[317]="???",v[319]="???",v[321]=[["CMOVNO",["KANDW","","KANDQ"],"",""],["CMOVNO",["KANDB","","KANDD"],"",""],"",""],v[322]=[["CMOVB",["KANDNW","","KANDNQ"],"",""],["CMOVB",["KANDNB","","KANDND"],"",""],"",""],v[324]=[["CMOVE",["KNOTW","","KNOTQ"],"",""],["CMOVE",["KNOTB","","KNOTD"],"",""],"",""],v[325]=[["CMOVNE",["KORW","","KORQ"],"",""],["CMOVNE",["KORB","","KORD"],"",""],"",""],v[326]=[["CMOVBE",["KXNORW","","KXNORQ"],"",""],["CMOVBE",["KXNORB","","KXNORD"],"",""],"",""],v[327]=[["CMOVA",["KXORW","","KXORQ"],"",""],["CMOVA",["KXORB","","KXORD"],"",""],"",""],v[336]=["???",[["MOVMSKPS","MOVMSKPS","",""],["MOVMSKPD","MOVMSKPD","",""],"???","???"]],v[337]=["SQRTPS","SQRTPD","SQRTSS","SQRTSD"],v[338]=[["RSQRTPS","RSQRTPS","",""],"???",["RSQRTSS","RSQRTSS","",""],"???"],v[340]=["ANDPS","ANDPD","???","???"],v[341]=["ANDNPS","ANDNPD","???","???"],v[344]=[["ADDPS","ADDPS","ADDPS","ADDPS"],["ADDPD","ADDPD","ADDPD","ADDPD"],"ADDSS","ADDSD"],v[345]=[["MULPS","MULPS","MULPS","MULPS"],["MULPD","MULPD","MULPD","MULPD"],"MULSS","MULSD"],v[346]=[["CVTPS2PD","CVTPS2PD","CVTPS2PD","CVTPS2PD"],["CVTPD2PS","CVTPD2PS","CVTPD2PS","CVTPD2PS"],"CVTSS2SD","CVTSD2SS"],v[347]=[[["CVTDQ2PS","","CVTQQ2PS"],"CVTPS2DQ",""],"???","CVTTPS2DQ","???"],v[348]=[["SUBPS","SUBPS","SUBPS","SUBPS"],["SUBPD","SUBPD","SUBPD","SUBPD"],"SUBSS","SUBSD"],v[349]=["MINPS","MINPD","MINSS","MINSD"],v[350]=["DIVPS","DIVPD","DIVSS","DIVSD"],v[376]=[["VMREAD","",["CVTTPS2UDQ","","CVTTPD2UDQ"],""],["EXTRQ","",["CVTTPS2UQQ","","CVTTPD2UQQ"],""],["???","","CVTTSS2USI",""],["INSERTQ","","CVTTSD2USI",""]],v[377]=[["VMWRITE","",["CVTPS2UDQ","","CVTPD2UDQ"],""],["EXTRQ","",["CVTPS2UQQ","","CVTPD2UQQ"],""],["???","","CVTSS2USI",""],["INSERTQ","","CVTSD2USI",""]],v[378]=["???",["","",["CVTTPS2QQ","","CVTTPD2QQ"],""],["","",["CVTUDQ2PD","","CVTUQQ2PD"],"CVTUDQ2PD"],["","",["CVTUDQ2PS","","CVTUQQ2PS"],""]],v[379]=["???",["","",["CVTPS2QQ","","CVTPD2QQ"],""],["","","CVTUSI2SS",""],["","","CVTUSI2SD",""]],v[380]=["???",["HADDPD","HADDPD","",""],"???",["HADDPS","HADDPS","",""]],v[381]=["???",["HSUBPD","HSUBPD","",""],"???",["HSUBPS","HSUBPS","",""]],v[382]=[["MOVD","","",""],["MOVD","","MOVQ"],["MOVQ","MOVQ",["???","","MOVQ"],""],"???"],v[400]=[["SETO",["KMOVW","","KMOVQ"],"",""],["SETO",["KMOVB","","KMOVD"],"",""],"",""],v[402]=[["SETB",["KMOVW","","???"],"",""],["SETB",["KMOVB","","???"],"",""],"",["SETB",["KMOVD","","KMOVQ"],"",""]],v[403]=[["SETAE",["KMOVW","","???"],"",""],["SETAE",["KMOVB","","???"],"",""],"",["SETAE",["KMOVD","","KMOVQ"],"",""]],v[408]=[["SETS",["KORTESTW","","KORTESTQ"],"",""],["SETS",["KORTESTB","","KORTESTD"],"",""],"",""],v[422]="XBTS",v[423]="IBTS",y[272]=[["0B700770","0B700770","0A040603","0A040609"],["0B700770","0B700770","0A0412040604","0A0412040604"]],y[273]=[["07700B70","07700B70","06030A04","06090A04"],["07700B70","07700B70","060412040A04","060412040A04"]],y[274]=[["0A0412040606","0A0412040606","0B700770","0B700768"],["0A0412040604","","0B700770","0B700770"]],y[275]=[["06060A04","06060A04","",""],""],y[321]=[["0B0E070E0180",["0A0F120F06FF","","0A0F120F06FF"],"",""],["0B0E070E0180",["0A0F120F06FF","","0A0F120F06FF"],"",""],"",""],y[322]=[["0B0E070E0180",["0A0F120F06FF","","0A0F120F06FF"],"",""],["0B0E070E0180",["0A0F120F06FF","","0A0F120F06FF"],"",""],"",""],y[324]=[["0B0E070E0180",["0A0F06FF","","0A0F06FF"],"",""],["0B0E070E0180",["0A0F06FF","","0A0F06FF"],"",""],"",""],y[325]=[["0A02070E0180",["0A0F120F06FF","","0A0F120F06FF"],"",""],["0A02070E0180",["0A0F120F06FF","","0A0F120F06FF"],"",""],"",""],y[326]=[["0B0E070E0180",["0A0F120F06FF","","0A0F120F06FF"],"",""],["0B0E070E0180",["0A0F120F06FF","","0A0F120F06FF"],"",""],"",""],y[327]=[["0B0E070E0180",["0A0F120F06FF","","0A0F120F06FF"],"",""],["0B0E070E0180",["0A0F120F06FF","",""],"",""],"",""],y[336]=["",[["0B0C0648","0B0C0730","",""],["0B0C0648","0B0C0730","",""],"",""]],y[337]=["0B7007700112","0B7007700112","0A04120406430102","0A04120406490102"],y[338]=[["0A040648","0A040648","",""],"",["0A040643","0A0412040643","",""],""],y[340]=["0B70137007700110","0B70137007700110","",""],y[341]=["0B70137007700110","0B70137007700110","",""],y[344]=[["0A040648","0B3013300730","0B70137007700112","0A061206066C0172"],["0A040648","0B3013300730","0B70137007700112","0A061206066C0112"],"0A04120406430102","0A04120406460102"],y[345]=[["0A040648","0B3013300730","0B70137007700112","0A061206066C0172"],["0A040648","0B3013300730","0B70137007700112","0A061206066C0112"],"0A04120406430102","0A04120406460102"],y[346]=[["0A040648","0B300718","0B7007380111","0A06065A0111"],["0A040648","0B180730","0B3807700112","0A05066C0112"],"0A04120406430101","0A04120406460102"],y[347]=[[["0B7007700112","","0B380770011A"],"0B700770011A","",""],"","0B7007700111",""],y[348]=[["0A060648","0B3013300730","0B70137007700112","0A061206066C0172"],["0A060648","0B3013300730","0B70137007700112","0A061206066C0112"],"0A04120406430102","0A04120406460102"],y[349]=["0B70137007700111","0B70137007700111","0A04120406430101","0A04120406460101"],y[350]=["0B70137007700112","0B70137007700112","0A04120406430102","0A04120406460102"],y[376]=[["07080B080180","",["0B7007700111","","0B3807700119"],""],["064F0C000C00","",["0B7007380119","","0B7007700111"],""],["","","0B0C06440109",""],["0A04064F0C000C00","","0B0C06460109",""]],y[377]=[["0B0807080180","",["0B7007700112","","0B380770011A"],""],["0A04064F","",["0B700738011A","","0B7007700112"],""],["","","0B0C0644010A",""],["0A04064F","","0B0C0646010A",""]],y[378]=["",["","",["0B7007380119","","0B7007700111"],""],["","",["0B7007380112","","0B700770011A"],"0A06065A0112"],["","",["0B700770011A","","0B3807700112"],""]],y[379]=["",["","",["0B700738011A","","0B7007700112"],""],["","","0A041204070C010A",""],["","","0A041204070C010A",""]],y[380]=["",["0A040604","0B7013700770","",""],"",["0A040604","0B7013700770","",""]],y[381]=["",["0A040604","0B7013700770","",""],"",["0A040604","0B7013700770","",""]],y[382]=[["070C0A0A","","",""],["06240A040108","","06360A040108"],["0A040646","0A040646",["","","0A0406460108"],""],""],y[400]=[["0600",["0A0F0612","","0A0F0636"],"",""],["0600",["0A0F0600","","0A0F0624"],"",""],"",""],y[402]=[["0600",["0A0F06F4","",""],"",""],["0600",["0A0F06F4","",""],"",""],"",["0600",["0A0F06F6","","0A0F06F6"],"",""]],y[403]=[["0600",["06F40A0F","",""],"",""],["0600",["06F40A0F","",""],"",""],"",["0600",["06F60A0F","","06F60A0F"],"",""]],y[408]=[["0600",["0A0F06FF","","0A0F06FF"],"",""],["0600",["0A0F06FF","","0A0F06FF"],"",""],"",""],y[422]="0B0E070E",y[423]="070E0B0E",1===e&&(v[321]=[["CMOVNO","KAND","",""],"","",""],v[322]=[["CMOVB","KANDN","",""],"","",""],v[324]=[["CMOVE","KNOT","",""],"","",""],v[325]=[["CMOVNE","KOR","",""],"","",""],v[326]=[["CMOVBE","KXNOR","",""],"","",""],v[327]=[["CMOVA","KXOR","",""],"","",""],v[400]=[["SETO","KMOV","",""],"","",""],v[402]=[["SETB","KMOV","",""],"","",""],v[403]=[["SETAE","KMOV","",""],"","",""],v[408]=[["SETS","KORTEST","",""],"","",""],y[321]=[["0B0E070E0180","0A0F06FF","",""],"","",""],y[322]=[["0B0E070E0180","0A0F06FF","",""],"","",""],y[324]=[["0B0E070E0180","0A0F06FF","",""],"","",""],y[325]=[["0A02070E0180","0A0F06FF","",""],"","",""],y[326]=[["0B0E070E0180","0A0F06FF","",""],"","",""],y[327]=[["0B0E070E0180","0A0F06FF","",""],"","",""],y[400]=[["0600","0A0F06FF","",""],"","",""],y[402]=[["0600","06FF0B06","",""],"","",""],y[403]=[["0600","07060A0F","",""],"","",""],y[408]=[["0600","0A0F06FF","",""],"","",""]),2===e&&(v[98]=""),3===e&&(v[312]="SMINT",v[314]="BB0_RESET",v[315]="BB1_RESET",v[316]="CPU_WRITE",v[317]="CPU_READ",v[336]="PAVEB",v[337]="PADDSIW",v[338]="PMAGW",v[340]="PDISTIB",v[341]="PSUBSIW",v[344]="PMVZB",v[345]="PMULHRW",v[346]="PMVNZB",v[347]="PMVLZB",v[348]="PMVGEZB",v[349]="PMULHRIW",v[350]="PMACHRIW",v[376]="SVDC",v[377]="RSDC",v[378]="SVLDT",v[379]="RSLDT",v[380]="SVTS",v[381]="RSTS",v[382]="SMINT",y[336]="0A0A06A9",y[337]="0A0A06A9",v[338]="0A0A06A9",y[340]="0A0A06AF",y[341]="0A0A06A9",y[344]="0A0A06AF",y[345]="0A0A06A9",v[346]="0A0A06AF",y[347]="0A0A06AF",y[348]="0A0A06AF",v[349]="0A0A06A9",y[350]="0A0A06AF",y[376]="30000A08",y[377]="0A083000",y[378]="3000",y[379]="3000",y[380]="3000",y[381]="3000",y[382]=""),4===e&&(v[312]="SMINT",v[313]="DMINT",v[314]="RDM"),5===e&&(v[319]="ALTINST",v[422]=["???",["MONTMUL","XSA1","XSA256","???","???","???","???","???"]],v[423]=["???",["XSTORE",["???","???","XCRYPT-ECB","???"],["???","???","XCRYPT-CBC","???"],["???","???","XCRYPT-CTR","???"],["???","???","XCRYPT-CFB","???"],["???","???","XCRYPT-OFB","???"],"???","???"]],y[422]=["",["","","","","","","",""]],y[423]=["",["",["","","",""],["","","",""],["","","",""],["","","",""],["","","",""],"",""]]),6===e&&(v[272]="UMOV",v[273]="UMOV",v[274]="UMOV",v[275]="UMOV",v[422]="CMPXCHG",v[423]="CMPXCHG",y[272]="06000A00",y[273]="070E0B0E",y[274]="0A000600",y[275]="0B0E070E",y[422]="",y[423]="")}function pe(){var e;E<P.length&&(1===(e=P[E++].toString(16)).length&&(e="0"+e),p+=e,(F+=1)>4294967295&&(F=0,(h+=1)>4294967295&&(h=0)))}function Te(e){var t=0,r=0,n=0,i=-1;if(n=(0!=(240&(n=e))?(n>>=4,4):0)|(0!=(12&n)?(n>>=2,2):0)|0!=(n>>=1),0===e&&(n=-1),0!==(r=(0!=(240&(r=e-=1<<n))?(r>>=4,4):0)|(0!=(12&r)?(r>>=2,2):0)|0!=(r>>=1))?e-=1<<r:r=n,0!==(t=(0!=(240&(t=e))?(t>>=4,4):0)|(0!=(12&t)?(t>>=2,2):0)|0!=(t>>=1))?e-=1<<t:(t=r,2!==r&&(r=n)),C<=1&&r>=3&&!G&&((n|r|t)===t&&(n=2,t=2),r=2),0===C&&!G){var o=t;t=r,r=o}return(G||ee>0)&&n+r+t===7|n+r+t===5?(G=!1,[r,n][1&b]):(G&&1===te&&(i=n,t=n,r=n),[t,r,n,i][w])}function Re(){var e=P[E],t=[e>>6&3,e>>3&7,7&e];return pe(),t}function de(e,t,r){var n=0,i=0,o=0,u=0,a=0,A=0,s=15&r;A=r>>4,t&&(s=Te(s),A>0&&(A=Te(A)));var c=1<<s;o=Math.min(c,4),c>=8&&(u=8),se=P[E];for(var f=0,S=1;f<o;n+=P[E]*S,f++,S*=256,pe());for(S=1;f<u;i+=P[E]*S,f++,S*=256,pe());if(o<<=1,u<<=1,1===e&&(n&=(1<<(c<<3)-4)-1),2===e){o=4+(Math.min(C,1)<<2),u=Math.max(Math.min(C,2),1)<<3;var l=0;n>=(c=Math.min(4294967296,Math.pow(2,4<<s+1)))>>1&&(n-=c),(l=(n+=F)>=4294967296)&&(n-=4294967296),s<=2&&(l=!1),(i+=h+l)>4294967295&&(i-=4294967296)}if(3===e){u=0;var B=2*(1<<(c<<3)-2);if(a=1,j&&0===s){var D=2|b;B<<=D,n<<=D}n>=B&&(n=2*B-n,a=2)}for(var M=n.toString(16),p=o;M.length<p;M="0"+M);if(u>8)for(M=i.toString(16)+M,p=u;M.length<p;M="0"+M);if(A!==s){A=2*Math.pow(2,A);var T="00";for((8&parseInt(M.substring(0,1),16))>>3&&(T="FF");M.length<A;M=T+M);}return(a>0?a>1?"-":"+":"")+M.toUpperCase()}function ge(e,t,r){if(G&&0===ee&&(w=0),t&&(r=Te(r),G&&r<4&&(r=4)),g>=1024?e&=15:C<=1&&ee>=1&&(e&=7),g>=1792&&6===r)r=16;else if(0===r)return Ee[0][_][e];return Ee[r][e]}function ve(e,t,r){var n="",i="{";if(3!==e[0]){G&&0===ee&&(w=0),t&&(16!==r||G?r=Te(r)<<1|x:G||(r=11-5*(C<=1))),r&=15,0!==ee&&9===r&&(r=6),1===te||2===te||j?n+=he[b>0?6:4]:n=he[r],n+=k;var o=C+1;Q&&0===(o-=1)&&(o=2);var u=e[0]-1;o>=2&&2===e[0]&&(u+=1);var a=3;if(1===o)1===o&&0===e[0]&&6===e[2]&&(u=1,a=0),e[2]<4&&(n+=Ee[o][3+(2&e[2])]+"+"),e[2]<6?n+=Ee[o][6+(1&e[2])]:0!==a&&(n+=Ee[o][17-(e[2]<<1)]);else if(0===e[0]&&5===e[2]&&(u=2,a=2),4===e[2]){var A=Re(),s=W|A[1];0!==e[0]||5!==A[2]||j?(n+=Ee[o][8&H|A[2]],(4!==s||j)&&(n+="+")):(u=2,4===s&&(a=0,3===o&&(u=50))),4===s||j?j&&(g<1792&&(s|=16&ue),n+=ge(W|s,!1,r=r<8?4:r>>1),n+=Fe[A[0]]):(n+=Ee[o][W|s],n+=Fe[A[0]])}else 2!==a&&(n+=Ee[o][8&H|e[2]]);u>=0&&(n+=de(a,!1,u)),n+="]",0===te||3===te&&(g>=1792||!(g>=1792||$))||!(g>=1792)&&(0===q||5===te&&5===q||(1!==te&&1===q)^(te<3&&!z))?0!==te&&(n+=i+"Error",i=","):(te>=4&&(te+=2),te>=8&&(te+=2),g>=1792&&(!z&&te>2?te=31:$&&(7===te&&te++,10===te&&(te=3))),n+=i+oe[te<<1|1&(b^!(g>=1792)&7===q)],i=",")}else(3===ee&&Ae||2===ee&&1===te)&&(re|=Z),(240&r)>0&&!t&&(r>>=4),n=ge(H|e[2],t,r),(g>=1792||3===ee&&!Ae&&z)&&(g>=1792&&te>=3&&te++,0!==te&&(n+=i+ie[te],i=",")),2!==ee&&(Ae=!1);return g>=1792&&(z?1946===g?(n+=i+oe[(18|3&ue)<<1],i="}"):1947===g?(n+=i+oe[22+(3&ue)<<1],i="}"):8==(8&Z)&&(n+=i+ne[24|7&ue],i="}"):0!==ue&&(J&&2!==ue||!J&&3!==ue&&ue<=15?(n+=i+oe[ue+2<<1|b],i="}"):(n+=i+"Error",i="}"))),","===i&&(i="}"),"}"===i&&(n+=i),Ae&&(3===ee?n+="{EH}":g>=1792&&(n+="{NT}")),n}function ye(){!function(){g=0,w=1,U="",N="",_=0,X=0,H=0,W=0,k="[",Q=!1,x=0,ee=0,K=0,G=!1,te=0,b=!1,ue=0,ae=0,Ae=!1,re=0,Y=!1,j=!1,Z=0,z=!1,J=!1,$=!1,q=0,se=0,ce="",fe="",Se=!1,le=!1,Be=!1,De=!1,Pe=!1,Ce=!1,p="";for(var e=0;e<I.length;I[e++].Deactivate());}();var e="";if(T=function(){if(0===C|1===C&M>=36){for(var e=(65535&F).toString(16);e.length<4;e="0"+e);for(var t=M.toString(16);t.length<4;t="0"+t);return(t+":"+e).toUpperCase()}var r="",n="";if(C>=1)for(n=F.toString(16);n.length<8;n="0"+n);if(2===C)for(r=h.toString(16);r.length<8;r="0"+r);return(r+n).toUpperCase()}(),function e(){if(g=768&g|P[E],pe(),15===g)return g=256,e();if(312===g&&""===v[312])return g=512,e();if(314===g&&""===v[314])return g=768,e();if(g>=64&g<=79&&2===C)return _=1,H=(1&g)<<3,W=(2&g)<<2,X=(4&g)<<1,w=(b=(8&g)>>3)?2:1,e();if(197===g&&(P[E]>=192||2===C))return ee=1,g=P[E],pe(),g^=248,2===C&&(X=(128&g)>>4,ue=(120&g)>>3),w=(4&g)>>2,K=3&g,g=768&(g=256)|P[E],pe(),null;if(196===g&&(P[E]>=192||2===C))return ee=1,g=P[E],pe(),g|=P[E]<<8,pe(),g^=30944,2===C&&(X=(128&g)>>4,W=(64&g)>>3,H=(32&g)>>2),b=(32768&g)>>15,ue=(30720&g)>>11,w=(1024&g)>>10,K=(768&g)>>8,g=768&(g=(31&g)<<8)|P[E],pe(),null;if(143===g){var t=15&P[E];if(t>=8&&t<=10)return ee=1,g=P[E],pe(),g|=P[E]<<8,pe(),X=(128&(g^=30944))>>4,W=(64&g)>>3,H=(32&g)>>2,b=(32768&g)>>15,ue=(30720&g)>>11,w=(1024&g)>>10,(K=(768&g)>>8)>0&&(Ce=!0),g=1792&(g=1024|(3&g)<<8)|P[E],pe(),null}return 214===g?(g=P[E],pe(),g|=P[E]<<8,pe(),b=1&K,re=(ue=(63488&g)>>11)>>3,ae=(1792&g)>>8,Ae=(128&g)>>7,te=(112&g)>>4,X=(12&g)<<1,H=(3&g)<<3,W=(2&g)<<2,g=1792|P[E],pe(),null):""===v[98]&&98===g?(g=P[E],pe(),W=(128&(g^=240))>>4,H=(64&g)>>3,X=(32&g)>>2,1!==K?w=16==(16&g)?2:1:K=0,g=2048|(48&g)>>4|(15&g)<<2,null):98===g&&(P[E]>=192||2===C)?(ee=2,g=P[E],pe(),g|=P[E]<<8,pe(),g|=P[E]<<16,pe(),Ce=(12&(g^=555248))>0,2===C&&(X=(128&g)>>4|16&g,H=(96&g)>>2,W=(64&g)>>3),ue=(30720&g)>>11|(524288&g)>>15,b=(32768&g)>>15,K=(768&g)>>8,Ae=(8388608&g)>>23,(1024&g)>0?(re=4|(w=(6291456&g)>>21),te=(1048576&g)>>20):(w=2,re=te=(7340032&g)>>20,ee=3),ae=(458752&g)>>16,g=768&(g=(3&g)<<8)|P[E],pe(),null):38==(2023&g)||100==(2046&g)?(k=v[g],e()):102===g?(K=1,w=0,e()):103===g?(Q=!0,e()):242===g||243===g?(K=2&g|1-g&1,ce=v[g],Be=!0,e()):240===g?(fe=v[g],Be=!1,e()):void(2===C&&(Ce|=(7&g)>=6&g<=64,Ce|=96===g|97===g,Ce|=212===g|213===g,Ce|=154===g|234===g,Ce|=130===g))}(),Ce||(!function(){U=v[g],N=y[g];var e=P[E];if(U instanceof Array&&2==U.length&&(U=U[t=e>>6&e>>7],N=N[t]),U instanceof Array&&8==U.length&&(U=U[t=(56&e)>>3],N=N[t],U instanceof Array&&8==U.length&&(U=U[t=7&e],N=N[t],pe())),U instanceof Array&&4==U.length?(G=!0,""!==U[2]&&""!==U[3]?ce="":K=1===K&1,U=U[K],N=N[K],U instanceof Array&&4==U.length?""!==U[ee]?(U=U[ee],N=N[ee]):(U="???",N=""):3===ee&&(U="???",N="")):g>=1792&&K>0&&(U="???",N=""),U instanceof Array&&3==U.length){var t=0===ee&0!==C^w>=1;b&&(t=2),3===ee&&Ae&&""!==U[1]&&(Ae=!1,t=1),""!==U[t]?(U=U[t],N=N[t]):(U=U[0],N=N[0])}g<=1024&&ee>0&&"K"!==U.charAt(0)&&"???"!==U&&(U="V"+U),C<=1&&"MOVSXD"===U&&(U="ARPL",N="06020A01")}(),g>=1792&&","===U.slice(-1)&&(U=U.split(","),g>=1824&&g<=1839?(se=ue>>2,U=$||3!==se&&7!==se?U[0]+V[se]+U[1]:U[0]+U[1],se=0,ue&=3):U=U[0]+(1==(1&ue)?"H":"L")+U[1]),function(){for(var e=0,t=0,r=0,n=0,i=8,o=3,u=0,a=0;u<N.length;u+=4)r=(256&(e=parseInt(N.substring(u,u+4),16)))>>8,n=255&e,0===(t=(65024&e)>>9)?r?(Z=(3&n)<<3,g>=1792&&Z>=16&&(re|=16),j=n>>2&1,Y=n>>3&1,z=(q=n>>4&7)>>2&1,J=q>>1&1,$=1&q,128==(128&n)&&(G=!1)):(Se=1&n,le=(2&n)>>1,De=(4&n)>>2,Pe=(8&n)>>3):1===t?I[0].set(0,r,n,a++):t>=2&&t<=4?(I[1].set(t-2,r,n,a++),4==t&&(x=1)):5===t?I[2].set(0,r,n,a++):t>=6&&t<=8&&o<=5?I[o++].set(t-6,r,n,a++):9===t&&(ee>0||g>=1792)?I[6].set(0,r,n,a++):10===t?I[7].set(0,r,n,a++):t>=11&&i<=11&&(I[i].set(t-11,r,n,a++),i++)}(),!G&&ee>0&&g<=1024&&(Ce=!0),G&&!Y&&ee>=2&&(Ce=(1&K)!=(1&b)),g>=1792&&(b^=Y)),Ce)e="???";else{if(function(){var e=[],t=[-1,0,0],r=!1;if(I[0].Active&&(e[I[0].OpNum]=ge(X|7&g,I[0].BySizeAttrubute,I[0].Size)),I[1].Active)if(0!==I[1].Type)t=Re(),e[I[1].OpNum]=ve(t,I[1].BySizeAttrubute,I[1].Size);else{var n=0,i=0;I[1].BySizeAttrubute?(i=Math.pow(2,C)<<1,n=Te(I[1].Size)<<1):(i=C+1,n=I[1].Size),e[I[1].OpNum]=he[n],e[I[1].OpNum]+=k+de(0,I[1].BySizeAttrubute,i)+"]"}if(I[2].Active&&(-1===t[0]&&(t=Re()),e[I[2].OpNum]=ge(X|7&t[1],I[2].BySizeAttrubute,I[2].Size)),I[3].Active){var o=de(I[3].Type,I[3].BySizeAttrubute,I[3].Size);","===U.slice(-1)?(U=U.split(","),ee>=1&&ee<=2&&g<=1024&&se<32||se<8?(se|=(g>1024&1)<<5,U=U[0]+V[se]+U[1]):(U=U[0]+U[1],e[I[3].OpNum]=o)):e[I[3].OpNum]=o,r=!0}I[4].Active&&(e[I[4].OpNum]=de(I[4].Type,I[4].BySizeAttrubute,I[4].Size)),I[5].Active&&(e[I[5].OpNum]=de(I[5].Type,I[5].BySizeAttrubute,I[5].Size)),I[6].Active&&(e[I[6].OpNum]=ge(ue,I[6].BySizeAttrubute,I[6].Size)),I[7].Active&&(r||de(0,!1,0),e[I[7].OpNum]=ge((240&se)>>4|(8&se)<<1,I[7].BySizeAttrubute,I[7].Size));for(var u=8;u<11&&I[u].Active;u++)I[u].Type<=3?e[I[u].OpNum]=ge(I[u].Type,I[u].BySizeAttrubute,I[u].Size):4===I[u].Type?(n=3,(0===C&&!Q||1===C&&Q)&&(n=7),e[I[u].OpNum]=ve([0,0,n],I[u].BySizeAttrubute,I[u].Size)):5===I[u].Type|6===I[u].Type?(n=1,(0===C&&!Q||1===C&Q)&&(n=-1),e[I[u].OpNum]=ve([0,0,I[u].Type+n],I[u].BySizeAttrubute,I[u].Size)):I[u].Type>=7&&(e[I[u].OpNum]=["ST","FS","GS","1","3","XMM0","M10"][I[u].Type-7]);0!==ae&&(e[0]+="{K"+ae+"}"),2===ee&&Ae&&(e[0]+="{Z}"),N=e.toString()}(),271===g)U=O[P[E]],pe(),""!==U&&null!=U||(U="???",N="");else if("SSS"===U){var t=P[E];pe();var r=P[E];pe(),t>=5||r>=5?U="???":""!==(U=m[5*t+r])&&null!=U||(U="???")}if(154===g||234===g){var n=N.split(",");N=n[1]+":"+n[0]}if(ce===v[243]&&fe===v[240]&&Se&&(ce="XRELEASE"),ce===v[242]&&fe===v[240]&&le&&(ce="XACQUIRE"),"XRELEASE"!==ce&&"XACQUIRE"!==ce||!Be||(n=ce,ce=fe,fe=n),De&&(k===v[46]?ce="HNT":k===v[62]&&(ce="HT")),ce===v[242]&&Pe&&(ce="BND"),p.length>30){var i=p.length-30>>1;p=p.substring(0,30),(i=F-i)<0&&(i+=4294967296);for(var o=i.toString(16);o.length<8;o="0"+o);for(var u=h.toString(16);u.length<8;u="0"+u);!function(e){var t=F,r=h,n=M,i=e.split(":");void 0!==i[1]&&(n=parseInt(i[0].slice(i[0].length-4),16),e=i[1]);var o=e.length;o>=9&&2===C&&(r=parseInt(e.slice(o-16,o-8),16)),o>=5&&C>=1&!(1===C&M>=36)?t=parseInt(e.slice(o-8),16):o>=1&&C>=0&&(t=t-t+parseInt(e.slice(o-4),16));var u=F-t;(1===C&M>=36||0===C)&&(u+=M-n<<4),i=E,(E-=4294967296*(h-r)+u)<0||E>P.length?E=i:(M=n,F=t,h=r)}(u+o),ce="",fe="",U="???",N=""}e=(e=ce+" "+fe+" "+U+" "+N).replace(/^[ ]+|[ ]+$/g,""),(g>=1792||0!==re)&&(e+=ne[re])}return e}function Oe(e){C=e}var me=r(9),Ve=function(e){function t(){var e;return u()(this,t),(e=c()(this,S()(t).call(this))).name="Disassemble x86",e.module="Shellcode",e.description="Disassembly is the process of translating machine language into assembly language.<br><br>This operation supports 64-bit, 32-bit and 16-bit code written for Intel or AMD x86 processors. It is particularly useful for reverse engineering shellcode.<br><br>Input should be in hexadecimal.",e.infoURL="https://wikipedia.org/wiki/X86",e.inputType="string",e.outputType="string",e.args=[{name:"Bit mode",type:"option",value:["64","32","16"]},{name:"Compatibility",type:"option",value:["Full x86 architecture","Knights Corner","Larrabee","Cyrix","Geode","Centaur","X86/486"]},{name:"Code Segment (CS)",type:"number",value:16},{name:"Offset (IP)",type:"number",value:0},{name:"Show instruction hex",type:"boolean",value:!0},{name:"Show instruction position",type:"boolean",value:!0}],e}return B()(t,e),A()(t,[{key:"run",value:function(e,t){var r=i()(t,6),n=r[0],o=r[1],u=r[2],a=r[3],A=r[4],s=r[5];switch(n){case"64":Oe(2);break;case"32":Oe(1);break;case"16":Oe(0);break;default:throw new me.a("Invalid mode value")}switch(o){case"Full x86 architecture":Me(0);break;case"Knights Corner":Me(1);break;case"Larrabee":Me(2);break;case"Cyrix":Me(3);break;case"Geode":Me(4);break;case"Centaur":Me(5);break;case"X86/486":Me(6)}return function(e){var t=e.split(":");void 0!==t[1]&&(M=parseInt(t[0].slice(t[0].length-4),16),e=t[1]);var r=e.length;r>=9&&2==C&&(h=parseInt(e.slice(r-16,r-8),16)),r>=5&&C>=1&&!(1==C&M>=36)?F=parseInt(e.slice(r-8),16):r>=1&&C>=0&&(F=4294901760&F|parseInt(e.slice(r-4),16)),F<0&&(F+=4294967296)}(u+":"+a),R=A,function(e){d=e}(s),function(e){P=[],E=0;for(var t=e.length,r=0,n=0,i=0,o=0;r<t;r+=8){if(o=parseInt(e.slice(r,r+8),16),isNaN(o))return!1;t-r<8&&(o<<=8-t-r<<2),i=o,o=(o^=2147483648&o)>>24|o<<8&**********,P[n++]=255&(i>>24&128|o),o=o>>24|o<<8&**********,P[n++]=255&(i>>16&128|o),o=o>>24|o<<8&**********,P[n++]=255&(i>>8&128|o),o=o>>24|o<<8&**********,P[n++]=255&(128&i|o)}for(t>>=1;t<P.length;P.pop());}(e.replace(/\s/g,"")),function(){for(var e="",t="",r=P.length,n=h,i=F;E<r;){if(e=ye(),d&&(t+=T+" "),R){for(p=p.toUpperCase();p.length<32;p+=" ");t+=p+""}t+=e+"\r\n",T="",p=""}return E=0,F=i,h=n,t}()}}]),t}(D.a),Ue="undefined"==typeof self?{}:self.OpModules||{};
/**
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2017
 * @license Apache-2.0
 */Ue.Shellcode={"Disassemble x86":Ve};t.default=Ue},14:function(e,t,r){"use strict";r.d(t,"b",function(){return i}),r.d(t,"a",function(){return o});var n=r(0);
/**
 * Base64 functions.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2016
 * @license Apache-2.0
 */function i(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"A-Za-z0-9+/=";if(!e)return"";e instanceof ArrayBuffer&&(e=new Uint8Array(e)),"string"==typeof e&&(e=n.b.strToByteArray(e)),t=n.b.expandAlphRange(t).join("");for(var r,i,o,u,a,A,s,c="",f=0;f<e.length;)u=(r=e[f++])>>2,a=(3&r)<<4|(i=e[f++])>>4,A=(15&i)<<2|(o=e[f++])>>6,s=63&o,isNaN(i)?A=s=64:isNaN(o)&&(s=64),c+=t.charAt(u)+t.charAt(a)+t.charAt(A)+t.charAt(s);return c}function o(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"A-Za-z0-9+/=",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"string",i=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];if(!e)return"string"===r?"":[];t=t||"A-Za-z0-9+/=",t=n.b.expandAlphRange(t).join("");var o,u,a,A,s,c,f=[],S=0;if(i){var l=new RegExp("[^"+t.replace(/[[\]\\\-^$]/g,"\\$&")+"]","g");e=e.replace(l,"")}for(;S<e.length;)o=t.indexOf(e.charAt(S++))<<2|(A=-1===(A=t.indexOf(e.charAt(S++)||"="))?64:A)>>4,u=(15&A)<<4|(s=-1===(s=t.indexOf(e.charAt(S++)||"="))?64:s)>>2,a=(3&s)<<6|(c=-1===(c=t.indexOf(e.charAt(S++)||"="))?64:c),f.push(o),64!==s&&f.push(u),64!==c&&f.push(a);return"string"===r?n.b.byteArrayToUtf8(f):f}},15:function(e,t,r){"use strict";var n=r(7),i=r.n(n),o=r(11),u=r.n(o),a=r(1),A=r.n(a),s=r(2),c=r.n(s),f=r(0),S=r(4),l=r.n(S),B=r(3),D=r.n(B),P=r(25),C=r.n(P),E=r(5),h=r.n(E);
/**
 * Custom error type for handling Dish type errors.
 * i.e. where the Dish cannot be successfully translated between types
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 */
var F=function(e){function t(){var e;A()(this,t);for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return(e=l()(this,D()(t).call(this,...n))).type="DishError",Error.captureStackTrace&&Error.captureStackTrace(C()(e),t),e}return h()(t,e),t}(function(e){function t(){var t=Reflect.construct(e,Array.from(arguments));return Object.setPrototypeOf(t,Object.getPrototypeOf(this)),t}return t.prototype=Object.create(e.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e,t}(Error)),M=r(16),p=r.n(M),T=r(18),R=r(26),d=r.n(R),g=function(){function e(){A()(this,e)}return c()(e,null,[{key:"checkForValue",value:function(e){if(void 0===e)throw new Error("only use translation methods with .bind")}},{key:"toArrayBuffer",value:function(){throw new Error("toArrayBuffer has not been implemented")}},{key:"fromArrayBuffer",value:function(){arguments.length>0&&void 0!==arguments[0]&&arguments[0];throw new Error("fromArrayBuffer has not been implemented")}}]),e}(),v=function(e){function t(){return A()(this,t),l()(this,D()(t).apply(this,arguments))}return h()(t,e),c()(t,null,[{key:"toArrayBuffer",value:function(){t.checkForValue(this.value),this.value=new Uint8Array(this.value).buffer}},{key:"fromArrayBuffer",value:function(){t.checkForValue(this.value),this.value=Array.prototype.slice.call(new Uint8Array(this.value))}}]),t}(g),y=function(e){function t(){return A()(this,t),l()(this,D()(t).apply(this,arguments))}return h()(t,e),c()(t,null,[{key:"toArrayBuffer",value:function(){t.checkForValue(this.value),this.value=p.a.isBigNumber(this.value)?f.b.strToArrayBuffer(this.value.toFixed()):new ArrayBuffer}},{key:"fromArrayBuffer",value:function(e){t.checkForValue(this.value);try{this.value=new p.a(f.b.arrayBufferToStr(this.value,!e))}catch(e){this.value=new p.a(NaN)}}}]),t}(g),O=function(e){function t(){return A()(this,t),l()(this,D()(t).apply(this,arguments))}return h()(t,e),c()(t,null,[{key:"toArrayBuffer",value:function(){var e=this;if(t.checkForValue(this.value),!Object(f.c)())return new Promise(function(t,r){f.b.readFile(e.value).then(function(t){return e.value=t.buffer}).then(t).catch(r)});this.value=f.b.readFileSync(this.value)}},{key:"fromArrayBuffer",value:function(){t.checkForValue(this.value),this.value=new File(this.value,"unknown")}}]),t}(g),m=function(e){function t(){return A()(this,t),l()(this,D()(t).apply(this,arguments))}return h()(t,e),c()(t,null,[{key:"toArrayBuffer",value:function(){t.checkForValue(this.value),this.value=this.value?f.b.strToArrayBuffer(this.value):new ArrayBuffer}},{key:"fromArrayBuffer",value:function(e){t.checkForValue(this.value),this.value=this.value?f.b.arrayBufferToStr(this.value,!e):""}}]),t}(g),V=function(e){function t(){return A()(this,t),l()(this,D()(t).apply(this,arguments))}return h()(t,e),c()(t,null,[{key:"toArrayBuffer",value:function(){t.checkForValue(this.value),this.value=this.value?f.b.strToArrayBuffer(f.b.unescapeHtml(f.b.stripHtmlTags(this.value,!0))):new ArrayBuffer}}]),t}(m),U=function(e){function t(){return A()(this,t),l()(this,D()(t).apply(this,arguments))}return h()(t,e),c()(t,null,[{key:"toArrayBuffer",value:function(){t.checkForValue(this.value),this.value=this.value?f.b.strToArrayBuffer(JSON.stringify(this.value,null,4)):new ArrayBuffer}},{key:"fromArrayBuffer",value:function(e){t.checkForValue(this.value),this.value=JSON.parse(f.b.arrayBufferToStr(this.value,!e))}}]),t}(g),N=function(e){function t(){return A()(this,t),l()(this,D()(t).apply(this,arguments))}return h()(t,e),c()(t,null,[{key:"toArrayBuffer",value:function(){t.checkForValue(this.value),Object(f.c)()&&(this.value=this.value.map(function(e){return Uint8Array.from(e.data)})),this.value=t.concatenateTypedArrays(...this.value).buffer}},{key:"fromArrayBuffer",value:function(){t.checkForValue(this.value),this.value=[new File(this.value,"unknown")]}},{key:"concatenateTypedArrays",value:function(){for(var e=0,t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];for(var i=0,o=r;i<o.length;i++){var u=o[i];e+=u.length}for(var a=new Uint8Array(e),A=0,s=0,c=r;s<c.length;s++){var f=c[s];a.set(f,A),A+=f.length}return a}}]),t}(g),L=function(e){function t(){return A()(this,t),l()(this,D()(t).apply(this,arguments))}return h()(t,e),c()(t,null,[{key:"toArrayBuffer",value:function(){t.checkForValue(this.value),this.value="number"==typeof this.value?f.b.strToArrayBuffer(this.value.toString()):new ArrayBuffer}},{key:"fromArrayBuffer",value:function(e){t.checkForValue(this.value),this.value=this.value?parseFloat(f.b.arrayBufferToStr(this.value,!e)):0}}]),t}(g),I=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(A()(this,e),this.value=new ArrayBuffer(0),this.type=e.ARRAY_BUFFER,t&&Object.prototype.hasOwnProperty.call(t,"value")&&Object.prototype.hasOwnProperty.call(t,"type"))this.set(t.value,t.type);else if(t&&null!==r)this.set(t,r);else if(t){var n=e.typeEnum(t.constructor.name);this.set(t,n)}}var t;return c()(e,[{key:"get",value:function(t){var r=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return"string"==typeof t&&(t=e.typeEnum(t)),this.type!==t?Object(f.c)()?(this._translate(t,n),this.value):new Promise(function(e,i){r._translate(t,n).then(function(){e(r.value)}).catch(i)}):this.value}},{key:"set",value:function(t,r){if("string"==typeof r&&(r=e.typeEnum(r)),d.a.debug("Dish type: "+e.enumLookup(r)),this.value=t,this.type=r,!this.valid()){var n=f.b.truncate(JSON.stringify(this.value),25);throw new F(`Data is not a valid ${e.enumLookup(r)}: ${n}`)}}},{key:"presentAs",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.clone();return r.get(e,t)}},{key:"detectDishType",value:function(){var e=new Uint8Array(this.value.slice(0,2048)),t=Object(T.a)(e);return t.length&&t[0].mime&&"text/plain"!==!t[0].mime?t[0].mime:null}},{key:"getTitle",value:(t=u()(i.a.mark(function t(r){var n,o;return i.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:n="",t.t0=this.type,t.next=t.t0===e.FILE?4:t.t0===e.LIST_FILE?6:t.t0===e.JSON?8:t.t0===e.NUMBER?10:t.t0===e.BIG_NUMBER?10:t.t0===e.ARRAY_BUFFER?12:t.t0===e.BYTE_ARRAY?12:15;break;case 4:return n=this.value.name,t.abrupt("break",26);case 6:return n=`${this.value.length} file(s)`,t.abrupt("break",26);case 8:return n="application/json",t.abrupt("break",26);case 10:return n=this.value.toString(),t.abrupt("break",26);case 12:if(null===(n=this.detectDishType())){t.next=15;break}return t.abrupt("break",26);case 15:return t.prev=15,(o=this.clone()).value=o.value.slice(0,256),t.next=20,o.get(e.STRING);case 20:n=t.sent,t.next=26;break;case 23:t.prev=23,t.t1=t.catch(15),d.a.error(`${e.enumLookup(this.type)} cannot be sliced. ${t.t1}`);case 26:return t.abrupt("return",n.slice(0,r));case 27:case"end":return t.stop()}},t,this,[[15,23]])})),function(e){return t.apply(this,arguments)})},{key:"valid",value:function(){switch(this.type){case e.BYTE_ARRAY:if(!(this.value instanceof Uint8Array||this.value instanceof Array))return!1;for(var t=0;t<this.value.length;t++)if("number"!=typeof this.value[t]||this.value[t]<0||this.value[t]>255)return!1;return!0;case e.STRING:case e.HTML:return"string"==typeof this.value;case e.NUMBER:return"number"==typeof this.value;case e.ARRAY_BUFFER:return this.value instanceof ArrayBuffer;case e.BIG_NUMBER:if(p.a.isBigNumber(this.value))return!0;if(Object.keys(this.value).sort().equals(["c","e","s"])){var r=new p.a;return r.c=this.value.c,r.e=this.value.e,r.s=this.value.s,this.value=r,!0}return!1;case e.JSON:return!0;case e.FILE:return this.value instanceof File;case e.LIST_FILE:return this.value instanceof Array&&this.value.reduce(function(e,t){return e&&t instanceof File},!0);default:return!1}}},{key:"clone",value:function(){var t=new e;switch(this.type){case e.STRING:case e.HTML:case e.NUMBER:case e.BIG_NUMBER:t.set(this.value,this.type);break;case e.BYTE_ARRAY:case e.JSON:t.set(JSON.parse(JSON.stringify(this.value)),this.type);break;case e.ARRAY_BUFFER:t.set(this.value.slice(0),this.type);break;case e.FILE:t.set(new File([this.value],this.value.name,{type:this.value.type,lastModified:this.value.lastModified}),this.type);break;case e.LIST_FILE:t.set(this.value.map(function(e){return new File([e],e.name,{type:e.type,lastModified:e.lastModified})}),this.type);break;default:throw new F("Cannot clone Dish, unknown type")}return t}},{key:"_translate",value:function(t){var r=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(d.a.debug(`Translating Dish from ${e.enumLookup(this.type)} to ${e.enumLookup(t)}`),!Object(f.c)())return new Promise(function(n,i){r._toArrayBuffer().then(function(){return r.type=e.ARRAY_BUFFER}).then(function(){r._fromArrayBuffer(t),n()}).catch(i)});this._toArrayBuffer(),this.type=e.ARRAY_BUFFER,this._fromArrayBuffer(t,n)}},{key:"_toArrayBuffer",value:function(){var t=this,r={browser:{[e.STRING]:function(){return Promise.resolve(m.toArrayBuffer.bind(t)())},[e.NUMBER]:function(){return Promise.resolve(L.toArrayBuffer.bind(t)())},[e.HTML]:function(){return Promise.resolve(V.toArrayBuffer.bind(t)())},[e.ARRAY_BUFFER]:function(){return Promise.resolve()},[e.BIG_NUMBER]:function(){return Promise.resolve(y.toArrayBuffer.bind(t)())},[e.JSON]:function(){return Promise.resolve(U.toArrayBuffer.bind(t)())},[e.FILE]:function(){return O.toArrayBuffer.bind(t)()},[e.LIST_FILE]:function(){return Promise.resolve(N.toArrayBuffer.bind(t)())},[e.BYTE_ARRAY]:function(){return Promise.resolve(v.toArrayBuffer.bind(t)())}},node:{[e.STRING]:function(){return m.toArrayBuffer.bind(t)()},[e.NUMBER]:function(){return L.toArrayBuffer.bind(t)()},[e.HTML]:function(){return V.toArrayBuffer.bind(t)()},[e.ARRAY_BUFFER]:function(){},[e.BIG_NUMBER]:function(){return y.toArrayBuffer.bind(t)()},[e.JSON]:function(){return U.toArrayBuffer.bind(t)()},[e.FILE]:function(){return O.toArrayBuffer.bind(t)()},[e.LIST_FILE]:function(){return N.toArrayBuffer.bind(t)()},[e.BYTE_ARRAY]:function(){return v.toArrayBuffer.bind(t)()}}};try{return r[Object(f.c)()?"node":"browser"][this.type]()}catch(t){throw new F(`Error translating from ${e.enumLookup(this.type)} to ArrayBuffer: ${t}`)}}},{key:"_fromArrayBuffer",value:function(t,r){var n=this,i={[e.STRING]:function(){return m.fromArrayBuffer.bind(n)(r)},[e.NUMBER]:function(){return L.fromArrayBuffer.bind(n)(r)},[e.HTML]:function(){return V.fromArrayBuffer.bind(n)(r)},[e.ARRAY_BUFFER]:function(){},[e.BIG_NUMBER]:function(){return y.fromArrayBuffer.bind(n)(r)},[e.JSON]:function(){return U.fromArrayBuffer.bind(n)(r)},[e.FILE]:function(){return O.fromArrayBuffer.bind(n)()},[e.LIST_FILE]:function(){return N.fromArrayBuffer.bind(n)()},[e.BYTE_ARRAY]:function(){return v.fromArrayBuffer.bind(n)()}};try{i[t](),this.type=t}catch(r){throw new F(`Error translating from ArrayBuffer to ${e.enumLookup(t)}: ${r}`)}}},{key:"size",get:function(){switch(this.type){case e.BYTE_ARRAY:case e.STRING:case e.HTML:return this.value.length;case e.NUMBER:case e.BIG_NUMBER:return this.value.toString().length;case e.ARRAY_BUFFER:return this.value.byteLength;case e.JSON:return JSON.stringify(this.value).length;case e.FILE:return this.value.size;case e.LIST_FILE:return this.value.reduce(function(e,t){return e+t.size},0);default:return-1}}}],[{key:"typeEnum",value:function(t){switch(t.toLowerCase()){case"bytearray":case"byte array":return e.BYTE_ARRAY;case"string":return e.STRING;case"number":return e.NUMBER;case"html":return e.HTML;case"arraybuffer":case"array buffer":return e.ARRAY_BUFFER;case"bignumber":case"big number":return e.BIG_NUMBER;case"json":case"object":return e.JSON;case"file":return e.FILE;case"list<file>":return e.LIST_FILE;default:throw new F("Invalid data type string. No matching enum.")}}},{key:"enumLookup",value:function(t){switch(t){case e.BYTE_ARRAY:return"byteArray";case e.STRING:return"string";case e.NUMBER:return"number";case e.HTML:return"html";case e.ARRAY_BUFFER:return"ArrayBuffer";case e.BIG_NUMBER:return"BigNumber";case e.JSON:return"JSON";case e.FILE:return"File";case e.LIST_FILE:return"List<File>";default:throw new F("Invalid data type enum. No matching type.")}}}]),e}();I.BYTE_ARRAY=0,I.STRING=1,I.NUMBER=2,I.HTML=3,I.ARRAY_BUFFER=4,I.BIG_NUMBER=5,I.JSON=6,I.FILE=7,I.LIST_FILE=8;t.a=I},16:function(e,t,r){var n;!function(i){"use strict";var o,u=/^-?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?$/i,a=Math.ceil,A=Math.floor,s="[BigNumber Error] ",c=s+"Number primitive has more than 15 significant digits: ",f=1e14,S=14,l=9007199254740991,B=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9,1e10,1e11,1e12,1e13],D=1e7,P=1e9;function C(e){var t=0|e;return e>0||e===t?t:t-1}function E(e){for(var t,r,n=1,i=e.length,o=e[0]+"";n<i;){for(t=e[n++]+"",r=S-t.length;r--;t="0"+t);o+=t}for(i=o.length;48===o.charCodeAt(--i););return o.slice(0,i+1||1)}function h(e,t){var r,n,i=e.c,o=t.c,u=e.s,a=t.s,A=e.e,s=t.e;if(!u||!a)return null;if(r=i&&!i[0],n=o&&!o[0],r||n)return r?n?0:-a:u;if(u!=a)return u;if(r=u<0,n=A==s,!i||!o)return n?0:!i^r?1:-1;if(!n)return A>s^r?1:-1;for(a=(A=i.length)<(s=o.length)?A:s,u=0;u<a;u++)if(i[u]!=o[u])return i[u]>o[u]^r?1:-1;return A==s?0:A>s^r?1:-1}function F(e,t,r,n){if(e<t||e>r||e!==A(e))throw Error(s+(n||"Argument")+("number"==typeof e?e<t||e>r?" out of range: ":" not an integer: ":" not a primitive number: ")+String(e))}function M(e){var t=e.c.length-1;return C(e.e/S)==t&&e.c[t]%2!=0}function p(e,t){return(e.length>1?e.charAt(0)+"."+e.slice(1):e)+(t<0?"e":"e+")+t}function T(e,t,r){var n,i;if(t<0){for(i=r+".";++t;i+=r);e=i+e}else if(++t>(n=e.length)){for(i=r,t-=n;--t;i+=r);e+=i}else t<n&&(e=e.slice(0,t)+"."+e.slice(t));return e}(o=function e(t){var r,n,i,o,R,d,g,v,y,O=W.prototype={constructor:W,toString:null,valueOf:null},m=new W(1),V=20,U=4,N=-7,L=21,I=-1e7,w=1e7,b=!1,x=1,Q=0,X={prefix:"",groupSize:3,secondaryGroupSize:0,groupSeparator:",",decimalSeparator:".",fractionGroupSize:0,fractionGroupSeparator:" ",suffix:""},H="0123456789abcdefghijklmnopqrstuvwxyz";function W(e,t){var r,o,a,s,f,B,D,P,C=this;if(!(C instanceof W))return new W(e,t);if(null==t){if(e&&!0===e._isBigNumber)return C.s=e.s,void(!e.c||e.e>w?C.c=C.e=null:e.e<I?C.c=[C.e=0]:(C.e=e.e,C.c=e.c.slice()));if((B="number"==typeof e)&&0*e==0){if(C.s=1/e<0?(e=-e,-1):1,e===~~e){for(s=0,f=e;f>=10;f/=10,s++);return void(s>w?C.c=C.e=null:(C.e=s,C.c=[e]))}P=String(e)}else{if(!u.test(P=String(e)))return i(C,P,B);C.s=45==P.charCodeAt(0)?(P=P.slice(1),-1):1}(s=P.indexOf("."))>-1&&(P=P.replace(".","")),(f=P.search(/e/i))>0?(s<0&&(s=f),s+=+P.slice(f+1),P=P.substring(0,f)):s<0&&(s=P.length)}else{if(F(t,2,H.length,"Base"),10==t)return G(C=new W(e),V+C.e+1,U);if(P=String(e),B="number"==typeof e){if(0*e!=0)return i(C,P,B,t);if(C.s=1/e<0?(P=P.slice(1),-1):1,W.DEBUG&&P.replace(/^0\.0*|\./,"").length>15)throw Error(c+e)}else C.s=45===P.charCodeAt(0)?(P=P.slice(1),-1):1;for(r=H.slice(0,t),s=f=0,D=P.length;f<D;f++)if(r.indexOf(o=P.charAt(f))<0){if("."==o){if(f>s){s=D;continue}}else if(!a&&(P==P.toUpperCase()&&(P=P.toLowerCase())||P==P.toLowerCase()&&(P=P.toUpperCase()))){a=!0,f=-1,s=0;continue}return i(C,String(e),B,t)}B=!1,(s=(P=n(P,t,10,C.s)).indexOf("."))>-1?P=P.replace(".",""):s=P.length}for(f=0;48===P.charCodeAt(f);f++);for(D=P.length;48===P.charCodeAt(--D););if(P=P.slice(f,++D)){if(D-=f,B&&W.DEBUG&&D>15&&(e>l||e!==A(e)))throw Error(c+C.s*e);if((s=s-f-1)>w)C.c=C.e=null;else if(s<I)C.c=[C.e=0];else{if(C.e=s,C.c=[],f=(s+1)%S,s<0&&(f+=S),f<D){for(f&&C.c.push(+P.slice(0,f)),D-=S;f<D;)C.c.push(+P.slice(f,f+=S));f=S-(P=P.slice(f)).length}else f-=D;for(;f--;P+="0");C.c.push(+P)}}else C.c=[C.e=0]}function k(e,t,r,n){var i,o,u,a,A;if(null==r?r=U:F(r,0,8),!e.c)return e.toString();if(i=e.c[0],u=e.e,null==t)A=E(e.c),A=1==n||2==n&&(u<=N||u>=L)?p(A,u):T(A,u,"0");else if(o=(e=G(new W(e),t,r)).e,a=(A=E(e.c)).length,1==n||2==n&&(t<=o||o<=N)){for(;a<t;A+="0",a++);A=p(A,o)}else if(t-=u,A=T(A,o,"0"),o+1>a){if(--t>0)for(A+=".";t--;A+="0");}else if((t+=o-a)>0)for(o+1==a&&(A+=".");t--;A+="0");return e.s<0&&i?"-"+A:A}function _(e,t){for(var r,n=1,i=new W(e[0]);n<e.length;n++){if(!(r=new W(e[n])).s){i=r;break}t.call(i,r)&&(i=r)}return i}function K(e,t,r){for(var n=1,i=t.length;!t[--i];t.pop());for(i=t[0];i>=10;i/=10,n++);return(r=n+r*S-1)>w?e.c=e.e=null:r<I?e.c=[e.e=0]:(e.e=r,e.c=t),e}function G(e,t,r,n){var i,o,u,s,c,l,D,P=e.c,C=B;if(P){e:{for(i=1,s=P[0];s>=10;s/=10,i++);if((o=t-i)<0)o+=S,u=t,D=(c=P[l=0])/C[i-u-1]%10|0;else if((l=a((o+1)/S))>=P.length){if(!n)break e;for(;P.length<=l;P.push(0));c=D=0,i=1,u=(o%=S)-S+1}else{for(c=s=P[l],i=1;s>=10;s/=10,i++);D=(u=(o%=S)-S+i)<0?0:c/C[i-u-1]%10|0}if(n=n||t<0||null!=P[l+1]||(u<0?c:c%C[i-u-1]),n=r<4?(D||n)&&(0==r||r==(e.s<0?3:2)):D>5||5==D&&(4==r||n||6==r&&(o>0?u>0?c/C[i-u]:0:P[l-1])%10&1||r==(e.s<0?8:7)),t<1||!P[0])return P.length=0,n?(t-=e.e+1,P[0]=C[(S-t%S)%S],e.e=-t||0):P[0]=e.e=0,e;if(0==o?(P.length=l,s=1,l--):(P.length=l+1,s=C[S-o],P[l]=u>0?A(c/C[i-u]%C[u])*s:0),n)for(;;){if(0==l){for(o=1,u=P[0];u>=10;u/=10,o++);for(u=P[0]+=s,s=1;u>=10;u/=10,s++);o!=s&&(e.e++,P[0]==f&&(P[0]=1));break}if(P[l]+=s,P[l]!=f)break;P[l--]=0,s=1}for(o=P.length;0===P[--o];P.pop());}e.e>w?e.c=e.e=null:e.e<I&&(e.c=[e.e=0])}return e}function Y(e){var t,r=e.e;return null===r?e.toString():(t=E(e.c),t=r<=N||r>=L?p(t,r):T(t,r,"0"),e.s<0?"-"+t:t)}return W.clone=e,W.ROUND_UP=0,W.ROUND_DOWN=1,W.ROUND_CEIL=2,W.ROUND_FLOOR=3,W.ROUND_HALF_UP=4,W.ROUND_HALF_DOWN=5,W.ROUND_HALF_EVEN=6,W.ROUND_HALF_CEIL=7,W.ROUND_HALF_FLOOR=8,W.EUCLID=9,W.config=W.set=function(e){var t,r;if(null!=e){if("object"!=typeof e)throw Error(s+"Object expected: "+e);if(e.hasOwnProperty(t="DECIMAL_PLACES")&&(F(r=e[t],0,P,t),V=r),e.hasOwnProperty(t="ROUNDING_MODE")&&(F(r=e[t],0,8,t),U=r),e.hasOwnProperty(t="EXPONENTIAL_AT")&&((r=e[t])&&r.pop?(F(r[0],-P,0,t),F(r[1],0,P,t),N=r[0],L=r[1]):(F(r,-P,P,t),N=-(L=r<0?-r:r))),e.hasOwnProperty(t="RANGE"))if((r=e[t])&&r.pop)F(r[0],-P,-1,t),F(r[1],1,P,t),I=r[0],w=r[1];else{if(F(r,-P,P,t),!r)throw Error(s+t+" cannot be zero: "+r);I=-(w=r<0?-r:r)}if(e.hasOwnProperty(t="CRYPTO")){if((r=e[t])!==!!r)throw Error(s+t+" not true or false: "+r);if(r){if("undefined"==typeof crypto||!crypto||!crypto.getRandomValues&&!crypto.randomBytes)throw b=!r,Error(s+"crypto unavailable");b=r}else b=r}if(e.hasOwnProperty(t="MODULO_MODE")&&(F(r=e[t],0,9,t),x=r),e.hasOwnProperty(t="POW_PRECISION")&&(F(r=e[t],0,P,t),Q=r),e.hasOwnProperty(t="FORMAT")){if("object"!=typeof(r=e[t]))throw Error(s+t+" not an object: "+r);X=r}if(e.hasOwnProperty(t="ALPHABET")){if("string"!=typeof(r=e[t])||/^.$|[+-.\s]|(.).*\1/.test(r))throw Error(s+t+" invalid: "+r);H=r}}return{DECIMAL_PLACES:V,ROUNDING_MODE:U,EXPONENTIAL_AT:[N,L],RANGE:[I,w],CRYPTO:b,MODULO_MODE:x,POW_PRECISION:Q,FORMAT:X,ALPHABET:H}},W.isBigNumber=function(e){if(!e||!0!==e._isBigNumber)return!1;if(!W.DEBUG)return!0;var t,r,n=e.c,i=e.e,o=e.s;e:if("[object Array]"=={}.toString.call(n)){if((1===o||-1===o)&&i>=-P&&i<=P&&i===A(i)){if(0===n[0]){if(0===i&&1===n.length)return!0;break e}if((t=(i+1)%S)<1&&(t+=S),String(n[0]).length==t){for(t=0;t<n.length;t++)if((r=n[t])<0||r>=f||r!==A(r))break e;if(0!==r)return!0}}}else if(null===n&&null===i&&(null===o||1===o||-1===o))return!0;throw Error(s+"Invalid BigNumber: "+e)},W.maximum=W.max=function(){return _(arguments,O.lt)},W.minimum=W.min=function(){return _(arguments,O.gt)},W.random=(o=9007199254740992*Math.random()&2097151?function(){return A(9007199254740992*Math.random())}:function(){return 8388608*(1073741824*Math.random()|0)+(8388608*Math.random()|0)},function(e){var t,r,n,i,u,c=0,f=[],l=new W(m);if(null==e?e=V:F(e,0,P),i=a(e/S),b)if(crypto.getRandomValues){for(t=crypto.getRandomValues(new Uint32Array(i*=2));c<i;)(u=131072*t[c]+(t[c+1]>>>11))>=9e15?(r=crypto.getRandomValues(new Uint32Array(2)),t[c]=r[0],t[c+1]=r[1]):(f.push(u%1e14),c+=2);c=i/2}else{if(!crypto.randomBytes)throw b=!1,Error(s+"crypto unavailable");for(t=crypto.randomBytes(i*=7);c<i;)(u=281474976710656*(31&t[c])+1099511627776*t[c+1]+4294967296*t[c+2]+16777216*t[c+3]+(t[c+4]<<16)+(t[c+5]<<8)+t[c+6])>=9e15?crypto.randomBytes(7).copy(t,c):(f.push(u%1e14),c+=7);c=i/7}if(!b)for(;c<i;)(u=o())<9e15&&(f[c++]=u%1e14);for(i=f[--c],e%=S,i&&e&&(u=B[S-e],f[c]=A(i/u)*u);0===f[c];f.pop(),c--);if(c<0)f=[n=0];else{for(n=-1;0===f[0];f.splice(0,1),n-=S);for(c=1,u=f[0];u>=10;u/=10,c++);c<S&&(n-=S-c)}return l.e=n,l.c=f,l}),W.sum=function(){for(var e=1,t=arguments,r=new W(t[0]);e<t.length;)r=r.plus(t[e++]);return r},n=function(){function e(e,t,r,n){for(var i,o,u=[0],a=0,A=e.length;a<A;){for(o=u.length;o--;u[o]*=t);for(u[0]+=n.indexOf(e.charAt(a++)),i=0;i<u.length;i++)u[i]>r-1&&(null==u[i+1]&&(u[i+1]=0),u[i+1]+=u[i]/r|0,u[i]%=r)}return u.reverse()}return function(t,n,i,o,u){var a,A,s,c,f,S,l,B,D=t.indexOf("."),P=V,C=U;for(D>=0&&(c=Q,Q=0,t=t.replace(".",""),S=(B=new W(n)).pow(t.length-D),Q=c,B.c=e(T(E(S.c),S.e,"0"),10,i,"0123456789"),B.e=B.c.length),s=c=(l=e(t,n,i,u?(a=H,"0123456789"):(a="0123456789",H))).length;0==l[--c];l.pop());if(!l[0])return a.charAt(0);if(D<0?--s:(S.c=l,S.e=s,S.s=o,l=(S=r(S,B,P,C,i)).c,f=S.r,s=S.e),D=l[A=s+P+1],c=i/2,f=f||A<0||null!=l[A+1],f=C<4?(null!=D||f)&&(0==C||C==(S.s<0?3:2)):D>c||D==c&&(4==C||f||6==C&&1&l[A-1]||C==(S.s<0?8:7)),A<1||!l[0])t=f?T(a.charAt(1),-P,a.charAt(0)):a.charAt(0);else{if(l.length=A,f)for(--i;++l[--A]>i;)l[A]=0,A||(++s,l=[1].concat(l));for(c=l.length;!l[--c];);for(D=0,t="";D<=c;t+=a.charAt(l[D++]));t=T(t,s,a.charAt(0))}return t}}(),r=function(){function e(e,t,r){var n,i,o,u,a=0,A=e.length,s=t%D,c=t/D|0;for(e=e.slice();A--;)a=((i=s*(o=e[A]%D)+(n=c*o+(u=e[A]/D|0)*s)%D*D+a)/r|0)+(n/D|0)+c*u,e[A]=i%r;return a&&(e=[a].concat(e)),e}function t(e,t,r,n){var i,o;if(r!=n)o=r>n?1:-1;else for(i=o=0;i<r;i++)if(e[i]!=t[i]){o=e[i]>t[i]?1:-1;break}return o}function r(e,t,r,n){for(var i=0;r--;)e[r]-=i,i=e[r]<t[r]?1:0,e[r]=i*n+e[r]-t[r];for(;!e[0]&&e.length>1;e.splice(0,1));}return function(n,i,o,u,a){var s,c,l,B,D,P,E,h,F,M,p,T,R,d,g,v,y,O=n.s==i.s?1:-1,m=n.c,V=i.c;if(!(m&&m[0]&&V&&V[0]))return new W(n.s&&i.s&&(m?!V||m[0]!=V[0]:V)?m&&0==m[0]||!V?0*O:O/0:NaN);for(F=(h=new W(O)).c=[],O=o+(c=n.e-i.e)+1,a||(a=f,c=C(n.e/S)-C(i.e/S),O=O/S|0),l=0;V[l]==(m[l]||0);l++);if(V[l]>(m[l]||0)&&c--,O<0)F.push(1),B=!0;else{for(d=m.length,v=V.length,l=0,O+=2,(D=A(a/(V[0]+1)))>1&&(V=e(V,D,a),m=e(m,D,a),v=V.length,d=m.length),R=v,p=(M=m.slice(0,v)).length;p<v;M[p++]=0);y=V.slice(),y=[0].concat(y),g=V[0],V[1]>=a/2&&g++;do{if(D=0,(s=t(V,M,v,p))<0){if(T=M[0],v!=p&&(T=T*a+(M[1]||0)),(D=A(T/g))>1)for(D>=a&&(D=a-1),E=(P=e(V,D,a)).length,p=M.length;1==t(P,M,E,p);)D--,r(P,v<E?y:V,E,a),E=P.length,s=1;else 0==D&&(s=D=1),E=(P=V.slice()).length;if(E<p&&(P=[0].concat(P)),r(M,P,p,a),p=M.length,-1==s)for(;t(V,M,v,p)<1;)D++,r(M,v<p?y:V,p,a),p=M.length}else 0===s&&(D++,M=[0]);F[l++]=D,M[0]?M[p++]=m[R]||0:(M=[m[R]],p=1)}while((R++<d||null!=M[0])&&O--);B=null!=M[0],F[0]||F.splice(0,1)}if(a==f){for(l=1,O=F[0];O>=10;O/=10,l++);G(h,o+(h.e=l+c*S-1)+1,u,B)}else h.e=c,h.r=+B;return h}}(),R=/^(-?)0([xbo])(?=\w[\w.]*$)/i,d=/^([^.]+)\.$/,g=/^\.([^.]+)$/,v=/^-?(Infinity|NaN)$/,y=/^\s*\+(?=[\w.])|^\s+|\s+$/g,i=function(e,t,r,n){var i,o=r?t:t.replace(y,"");if(v.test(o))e.s=isNaN(o)?null:o<0?-1:1;else{if(!r&&(o=o.replace(R,function(e,t,r){return i="x"==(r=r.toLowerCase())?16:"b"==r?2:8,n&&n!=i?e:t}),n&&(i=n,o=o.replace(d,"$1").replace(g,"0.$1")),t!=o))return new W(o,i);if(W.DEBUG)throw Error(s+"Not a"+(n?" base "+n:"")+" number: "+t);e.s=null}e.c=e.e=null},O.absoluteValue=O.abs=function(){var e=new W(this);return e.s<0&&(e.s=1),e},O.comparedTo=function(e,t){return h(this,new W(e,t))},O.decimalPlaces=O.dp=function(e,t){var r,n,i,o=this;if(null!=e)return F(e,0,P),null==t?t=U:F(t,0,8),G(new W(o),e+o.e+1,t);if(!(r=o.c))return null;if(n=((i=r.length-1)-C(this.e/S))*S,i=r[i])for(;i%10==0;i/=10,n--);return n<0&&(n=0),n},O.dividedBy=O.div=function(e,t){return r(this,new W(e,t),V,U)},O.dividedToIntegerBy=O.idiv=function(e,t){return r(this,new W(e,t),0,1)},O.exponentiatedBy=O.pow=function(e,t){var r,n,i,o,u,c,f,l,B=this;if((e=new W(e)).c&&!e.isInteger())throw Error(s+"Exponent not an integer: "+Y(e));if(null!=t&&(t=new W(t)),u=e.e>14,!B.c||!B.c[0]||1==B.c[0]&&!B.e&&1==B.c.length||!e.c||!e.c[0])return l=new W(Math.pow(+Y(B),u?2-M(e):+Y(e))),t?l.mod(t):l;if(c=e.s<0,t){if(t.c?!t.c[0]:!t.s)return new W(NaN);(n=!c&&B.isInteger()&&t.isInteger())&&(B=B.mod(t))}else{if(e.e>9&&(B.e>0||B.e<-1||(0==B.e?B.c[0]>1||u&&B.c[1]>=24e7:B.c[0]<8e13||u&&B.c[0]<=9999975e7)))return o=B.s<0&&M(e)?-0:0,B.e>-1&&(o=1/o),new W(c?1/o:o);Q&&(o=a(Q/S+2))}for(u?(r=new W(.5),c&&(e.s=1),f=M(e)):f=(i=Math.abs(+Y(e)))%2,l=new W(m);;){if(f){if(!(l=l.times(B)).c)break;o?l.c.length>o&&(l.c.length=o):n&&(l=l.mod(t))}if(i){if(0===(i=A(i/2)))break;f=i%2}else if(G(e=e.times(r),e.e+1,1),e.e>14)f=M(e);else{if(0===(i=+Y(e)))break;f=i%2}B=B.times(B),o?B.c&&B.c.length>o&&(B.c.length=o):n&&(B=B.mod(t))}return n?l:(c&&(l=m.div(l)),t?l.mod(t):o?G(l,Q,U,void 0):l)},O.integerValue=function(e){var t=new W(this);return null==e?e=U:F(e,0,8),G(t,t.e+1,e)},O.isEqualTo=O.eq=function(e,t){return 0===h(this,new W(e,t))},O.isFinite=function(){return!!this.c},O.isGreaterThan=O.gt=function(e,t){return h(this,new W(e,t))>0},O.isGreaterThanOrEqualTo=O.gte=function(e,t){return 1===(t=h(this,new W(e,t)))||0===t},O.isInteger=function(){return!!this.c&&C(this.e/S)>this.c.length-2},O.isLessThan=O.lt=function(e,t){return h(this,new W(e,t))<0},O.isLessThanOrEqualTo=O.lte=function(e,t){return-1===(t=h(this,new W(e,t)))||0===t},O.isNaN=function(){return!this.s},O.isNegative=function(){return this.s<0},O.isPositive=function(){return this.s>0},O.isZero=function(){return!!this.c&&0==this.c[0]},O.minus=function(e,t){var r,n,i,o,u=this,a=u.s;if(t=(e=new W(e,t)).s,!a||!t)return new W(NaN);if(a!=t)return e.s=-t,u.plus(e);var A=u.e/S,s=e.e/S,c=u.c,l=e.c;if(!A||!s){if(!c||!l)return c?(e.s=-t,e):new W(l?u:NaN);if(!c[0]||!l[0])return l[0]?(e.s=-t,e):new W(c[0]?u:3==U?-0:0)}if(A=C(A),s=C(s),c=c.slice(),a=A-s){for((o=a<0)?(a=-a,i=c):(s=A,i=l),i.reverse(),t=a;t--;i.push(0));i.reverse()}else for(n=(o=(a=c.length)<(t=l.length))?a:t,a=t=0;t<n;t++)if(c[t]!=l[t]){o=c[t]<l[t];break}if(o&&(i=c,c=l,l=i,e.s=-e.s),(t=(n=l.length)-(r=c.length))>0)for(;t--;c[r++]=0);for(t=f-1;n>a;){if(c[--n]<l[n]){for(r=n;r&&!c[--r];c[r]=t);--c[r],c[n]+=f}c[n]-=l[n]}for(;0==c[0];c.splice(0,1),--s);return c[0]?K(e,c,s):(e.s=3==U?-1:1,e.c=[e.e=0],e)},O.modulo=O.mod=function(e,t){var n,i,o=this;return e=new W(e,t),!o.c||!e.s||e.c&&!e.c[0]?new W(NaN):!e.c||o.c&&!o.c[0]?new W(o):(9==x?(i=e.s,e.s=1,n=r(o,e,0,3),e.s=i,n.s*=i):n=r(o,e,0,x),(e=o.minus(n.times(e))).c[0]||1!=x||(e.s=o.s),e)},O.multipliedBy=O.times=function(e,t){var r,n,i,o,u,a,A,s,c,l,B,P,E,h,F,M=this,p=M.c,T=(e=new W(e,t)).c;if(!(p&&T&&p[0]&&T[0]))return!M.s||!e.s||p&&!p[0]&&!T||T&&!T[0]&&!p?e.c=e.e=e.s=null:(e.s*=M.s,p&&T?(e.c=[0],e.e=0):e.c=e.e=null),e;for(n=C(M.e/S)+C(e.e/S),e.s*=M.s,(A=p.length)<(l=T.length)&&(E=p,p=T,T=E,i=A,A=l,l=i),i=A+l,E=[];i--;E.push(0));for(h=f,F=D,i=l;--i>=0;){for(r=0,B=T[i]%F,P=T[i]/F|0,o=i+(u=A);o>i;)r=((s=B*(s=p[--u]%F)+(a=P*s+(c=p[u]/F|0)*B)%F*F+E[o]+r)/h|0)+(a/F|0)+P*c,E[o--]=s%h;E[o]=r}return r?++n:E.splice(0,1),K(e,E,n)},O.negated=function(){var e=new W(this);return e.s=-e.s||null,e},O.plus=function(e,t){var r,n=this,i=n.s;if(t=(e=new W(e,t)).s,!i||!t)return new W(NaN);if(i!=t)return e.s=-t,n.minus(e);var o=n.e/S,u=e.e/S,a=n.c,A=e.c;if(!o||!u){if(!a||!A)return new W(i/0);if(!a[0]||!A[0])return A[0]?e:new W(a[0]?n:0*i)}if(o=C(o),u=C(u),a=a.slice(),i=o-u){for(i>0?(u=o,r=A):(i=-i,r=a),r.reverse();i--;r.push(0));r.reverse()}for((i=a.length)-(t=A.length)<0&&(r=A,A=a,a=r,t=i),i=0;t;)i=(a[--t]=a[t]+A[t]+i)/f|0,a[t]=f===a[t]?0:a[t]%f;return i&&(a=[i].concat(a),++u),K(e,a,u)},O.precision=O.sd=function(e,t){var r,n,i,o=this;if(null!=e&&e!==!!e)return F(e,1,P),null==t?t=U:F(t,0,8),G(new W(o),e,t);if(!(r=o.c))return null;if(n=(i=r.length-1)*S+1,i=r[i]){for(;i%10==0;i/=10,n--);for(i=r[0];i>=10;i/=10,n++);}return e&&o.e+1>n&&(n=o.e+1),n},O.shiftedBy=function(e){return F(e,-l,l),this.times("1e"+e)},O.squareRoot=O.sqrt=function(){var e,t,n,i,o,u=this,a=u.c,A=u.s,s=u.e,c=V+4,f=new W("0.5");if(1!==A||!a||!a[0])return new W(!A||A<0&&(!a||a[0])?NaN:a?u:1/0);if(0==(A=Math.sqrt(+Y(u)))||A==1/0?(((t=E(a)).length+s)%2==0&&(t+="0"),A=Math.sqrt(+t),s=C((s+1)/2)-(s<0||s%2),n=new W(t=A==1/0?"1e"+s:(t=A.toExponential()).slice(0,t.indexOf("e")+1)+s)):n=new W(A+""),n.c[0])for((A=(s=n.e)+c)<3&&(A=0);;)if(o=n,n=f.times(o.plus(r(u,o,c,1))),E(o.c).slice(0,A)===(t=E(n.c)).slice(0,A)){if(n.e<s&&--A,"9999"!=(t=t.slice(A-3,A+1))&&(i||"4999"!=t)){+t&&(+t.slice(1)||"5"!=t.charAt(0))||(G(n,n.e+V+2,1),e=!n.times(n).eq(u));break}if(!i&&(G(o,o.e+V+2,0),o.times(o).eq(u))){n=o;break}c+=4,A+=4,i=1}return G(n,n.e+V+1,U,e)},O.toExponential=function(e,t){return null!=e&&(F(e,0,P),e++),k(this,e,t,1)},O.toFixed=function(e,t){return null!=e&&(F(e,0,P),e=e+this.e+1),k(this,e,t)},O.toFormat=function(e,t,r){var n,i=this;if(null==r)null!=e&&t&&"object"==typeof t?(r=t,t=null):e&&"object"==typeof e?(r=e,e=t=null):r=X;else if("object"!=typeof r)throw Error(s+"Argument not an object: "+r);if(n=i.toFixed(e,t),i.c){var o,u=n.split("."),a=+r.groupSize,A=+r.secondaryGroupSize,c=r.groupSeparator||"",f=u[0],S=u[1],l=i.s<0,B=l?f.slice(1):f,D=B.length;if(A&&(o=a,a=A,A=o,D-=o),a>0&&D>0){for(o=D%a||a,f=B.substr(0,o);o<D;o+=a)f+=c+B.substr(o,a);A>0&&(f+=c+B.slice(o)),l&&(f="-"+f)}n=S?f+(r.decimalSeparator||"")+((A=+r.fractionGroupSize)?S.replace(new RegExp("\\d{"+A+"}\\B","g"),"$&"+(r.fractionGroupSeparator||"")):S):f}return(r.prefix||"")+n+(r.suffix||"")},O.toFraction=function(e){var t,n,i,o,u,a,A,c,f,l,D,P,C=this,h=C.c;if(null!=e&&(!(A=new W(e)).isInteger()&&(A.c||1!==A.s)||A.lt(m)))throw Error(s+"Argument "+(A.isInteger()?"out of range: ":"not an integer: ")+Y(A));if(!h)return new W(C);for(t=new W(m),f=n=new W(m),i=c=new W(m),P=E(h),u=t.e=P.length-C.e-1,t.c[0]=B[(a=u%S)<0?S+a:a],e=!e||A.comparedTo(t)>0?u>0?t:f:A,a=w,w=1/0,A=new W(P),c.c[0]=0;l=r(A,t,0,1),1!=(o=n.plus(l.times(i))).comparedTo(e);)n=i,i=o,f=c.plus(l.times(o=f)),c=o,t=A.minus(l.times(o=t)),A=o;return o=r(e.minus(n),i,0,1),c=c.plus(o.times(f)),n=n.plus(o.times(i)),c.s=f.s=C.s,D=r(f,i,u*=2,U).minus(C).abs().comparedTo(r(c,n,u,U).minus(C).abs())<1?[f,i]:[c,n],w=a,D},O.toNumber=function(){return+Y(this)},O.toPrecision=function(e,t){return null!=e&&F(e,1,P),k(this,e,t,2)},O.toString=function(e){var t,r=this,i=r.s,o=r.e;return null===o?i?(t="Infinity",i<0&&(t="-"+t)):t="NaN":(null==e?t=o<=N||o>=L?p(E(r.c),o):T(E(r.c),o,"0"):10===e?t=T(E((r=G(new W(r),V+o+1,U)).c),r.e,"0"):(F(e,2,H.length,"Base"),t=n(T(E(r.c),o,"0"),10,e,i,!0)),i<0&&r.c[0]&&(t="-"+t)),t},O.valueOf=O.toJSON=function(){return Y(this)},O._isBigNumber=!0,null!=t&&W.set(t),W}()).default=o.BigNumber=o,void 0===(n=function(){return o}.call(t,r,t,e))||(e.exports=n)}()},17:function(e,t,r){"use strict";r.d(t,"b",function(){return i}),r.d(t,"c",function(){return o}),r.d(t,"a",function(){return u});var n=r(0);
/**
 * Hexadecimal functions.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2016
 * @license Apache-2.0
 */function i(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:" ",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2;if(!e)return"";e instanceof ArrayBuffer&&(e=new Uint8Array(e));for(var n="",i=0;i<e.length;i++)n+=e[i].toString(16).padStart(r,"0")+t;return"0x"===t&&(n="0x"+n),"\\x"===t&&(n="\\x"+n),t.length?n.slice(0,-t.length):n}function o(e){if(!e)return"";e instanceof ArrayBuffer&&(e=new Uint8Array(e));for(var t=[],r=0;r<e.length;r++)t.push((e[r]>>>4).toString(16)),t.push((15&e[r]).toString(16));return t.join("")}function u(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Auto",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2;if("None"!==t){var i="Auto"===t?/[^a-f\d]/gi:n.b.regexRep(t);e=e.replace(i,"")}for(var o=[],u=0;u<e.length;u+=r)o.push(parseInt(e.substr(u,r),16));return o}["Auto"].concat(["Space","Percent","Comma","Semi-colon","Colon","Line feed","CRLF","0x","\\x","None"])},18:function(e,t,r){"use strict";var n=r(13),i=r.n(n),o=r(10),u={Images:[{name:"Joint Photographic Experts Group image",extension:"jpg,jpeg,jpe,thm,mpo",mime:"image/jpeg",description:"",signature:{0:255,1:216,2:255,3:[192,196,219,221,224,225,226,227,228,229,231,232,234,235,236,237,238,254]},extractor:function(e,t){var r=new o.a(e.slice(t));for(;r.hasMore();){var n=r.getBytes(2);if(255!==n[0])throw new Error(`Invalid marker while parsing JPEG at pos ${r.position}: ${n}`);var i=0;switch(n[1]){case 216:case 1:break;case 217:return r.carve();case 192:case 193:case 194:case 195:case 196:case 197:case 198:case 199:case 200:case 201:case 202:case 203:case 204:case 205:case 206:case 207:case 219:case 222:case 224:case 225:case 226:case 227:case 228:case 229:case 230:case 231:case 232:case 233:case 234:case 235:case 236:case 237:case 238:case 239:case 254:i=r.readInt(2,"be"),r.position+=i-2;break;case 223:r.position++;break;case 220:case 221:r.position+=2;break;case 218:i=r.readInt(2,"be"),r.position+=i-2,r.continueUntil(255);break;case 0:case 208:case 209:case 210:case 211:case 212:case 213:case 214:case 215:default:r.continueUntil(255)}}throw new Error("Unable to parse JPEG successfully")}},{name:"Graphics Interchange Format image",extension:"gif",mime:"image/gif",description:"",signature:{0:71,1:73,2:70,3:56,4:[55,57],5:97},extractor:null},{name:"Portable Network Graphics image",extension:"png",mime:"image/png",description:"",signature:{0:137,1:80,2:78,3:71,4:13,5:10,6:26,7:10},extractor:function(e,t){var r=new o.a(e.slice(t));r.moveForwardsBy(8);var n=0,i="";for(;"IEND"!==i;)n=r.readInt(4,"be"),i=r.readString(4),r.moveForwardsBy(n+4);return r.carve()}},{name:"WEBP Image",extension:"webp",mime:"image/webp",description:"",signature:{8:87,9:69,10:66,11:80},extractor:null},{name:"Camera Image File Format",extension:"crw",mime:"image/x-canon-crw",description:"",signature:{6:72,7:69,8:65,9:80,10:67,11:67,12:68,13:82},extractor:null},{name:"Canon CR2 raw image",extension:"cr2",mime:"image/x-canon-cr2",description:"",signature:[{0:73,1:73,2:42,3:0,8:67,9:82},{0:77,1:77,2:0,3:42,8:67,9:82}],extractor:null},{name:"Tagged Image File Format image",extension:"tif",mime:"image/tiff",description:"",signature:[{0:73,1:73,2:42,3:0},{0:77,1:77,2:0,3:42}],extractor:null},{name:"Bitmap image",extension:"bmp",mime:"image/bmp",description:"",signature:{0:66,1:77,7:0,9:0,14:[12,40,56,64,108,124],15:0,16:0,17:0},extractor:function(e,t){var r=new o.a(e.slice(t));r.moveForwardsBy(2);var n=r.readInt(4,"le");return r.moveForwardsBy(n-6),r.carve()}},{name:"JPEG Extended Range image",extension:"jxr",mime:"image/vnd.ms-photo",description:"",signature:{0:73,1:73,2:188},extractor:null},{name:"Photoshop image",extension:"psd",mime:"image/vnd.adobe.photoshop",description:"",signature:{0:56,1:66,2:80,3:83,4:0,5:1,6:0,7:0,8:0,9:0,10:0,11:0},extractor:null},{name:"Photoshop Large Document",extension:"psb",mime:"application/x-photoshop",description:"",signature:{0:56,1:66,2:80,3:83,4:0,5:2,6:0,7:0,8:0,9:0,10:0,11:0,12:0},extractor:null},{name:"Paint Shop Pro image",extension:"psp",mime:"image/psp",description:"",signature:[{0:80,1:97,2:105,3:110,4:116,5:32,6:83,7:104,8:111,9:112,10:32,11:80,12:114,13:111,14:32,15:73,16:109},{0:126,1:66,2:75,3:0}],extractor:null},{name:"Icon image",extension:"ico",mime:"image/x-icon",description:"",signature:{0:0,1:0,2:1,3:0,4:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],5:0,6:[16,32,48,64,128],7:[16,32,48,64,128],9:0,10:[0,1]},extractor:null},{name:"Radiance High Dynamic Range image",extension:"hdr",mime:"image/vnd.radiance",description:"",signature:{0:35,1:63,2:82,3:65,4:68,5:73,6:65,7:78,8:67,9:69,10:10},extractor:null},{name:"Sony ARW image",extension:"arw",mime:"image/x-raw",description:"",signature:{0:5,1:0,2:0,3:0,4:65,5:87,6:49,7:46},extractor:null},{name:"Fujifilm Raw Image",extension:"raf",mime:"image/x-raw",description:"",signature:{0:70,1:85,2:74,3:73,4:70,5:73,6:76,7:77,8:67,9:67,10:68,11:45,12:82,13:65,14:87},extractor:null},{name:"Minolta RAW image",extension:"mrw",mime:"image/x-raw",description:"",signature:{0:0,1:77,2:82,3:77},extractor:null},{name:"Adobe Bridge Thumbnail Cache",extension:"bct",mime:"application/octet-stream",description:"",signature:{0:108,1:110,2:98,3:116,4:2,5:0,6:0,7:0},extractor:null},{name:"Microsoft Document Imaging",extension:"mdi",mime:"image/vnd.ms-modi",description:"",signature:{0:69,1:80,2:42,3:0},extractor:null}],Video:[{name:"Matroska Multimedia Container",extension:"mkv",mime:"video/x-matroska",description:"",signature:{31:109,32:97,33:116,34:114,35:111,36:115,37:107,38:97},extractor:null},{name:"WEBM video",extension:"webm",mime:"video/webm",description:"",signature:{0:26,1:69,2:223,3:163},extractor:null},{name:"MPEG-4 video",extension:"mp4",mime:"video/mp4",description:"",signature:[{0:0,1:0,2:0,3:[24,32],4:102,5:116,6:121,7:112},{0:51,1:103,2:112,3:53},{0:0,1:0,2:0,3:28,4:102,5:116,6:121,7:112,8:109,9:112,10:52,11:50,16:109,17:112,18:52,19:49,20:109,21:112,22:52,23:50,24:105,25:115,26:111,27:109}],extractor:null},{name:"M4V video",extension:"m4v",mime:"video/x-m4v",description:"",signature:{0:0,1:0,2:0,3:28,4:102,5:116,6:121,7:112,8:77,9:52,10:86},extractor:null},{name:"Quicktime video",extension:"mov",mime:"video/quicktime",description:"",signature:{0:0,1:0,2:0,3:20,4:102,5:116,6:121,7:112},extractor:null},{name:"Audio Video Interleave",extension:"avi",mime:"video/x-msvideo",description:"",signature:{0:82,1:73,2:70,3:70,8:65,9:86,10:73},extractor:null},{name:"Windows Media Video",extension:"wmv",mime:"video/x-ms-wmv",description:"",signature:{0:48,1:38,2:178,3:117,4:142,5:102,6:207,7:17,8:166,9:217},extractor:null},{name:"MPEG video",extension:"mpg",mime:"video/mpeg",description:"",signature:{0:0,1:0,2:1,3:186},extractor:null},{name:"Flash Video",extension:"flv",mime:"video/x-flv",description:"",signature:{0:70,1:76,2:86,3:1},extractor:function(e,t){var r=new o.a(e.slice(t));r.moveForwardsBy(5);var n=r.readInt(4,"be");r.moveForwardsBy(n-9);var i=-11;for(;r.hasMore();){var u=r.readInt(4,"be"),a=r.readInt(1);if([8,9,18].indexOf(a)<0){r.moveBackwardsBy(1);break}if(u!==i+11){r.moveBackwardsBy(i+11+5);break}i=r.readInt(3,"be"),r.moveForwardsBy(7+i)}return r.carve()}}],Audio:[{name:"Waveform Audio",extension:"wav",mime:"audio/x-wav",description:"",signature:{0:82,1:73,2:70,3:70,8:87,9:65,10:86,11:69},extractor:null},{name:"OGG audio",extension:"ogg",mime:"audio/ogg",description:"",signature:{0:79,1:103,2:103,3:83},extractor:null},{name:"Musical Instrument Digital Interface audio",extension:"midi",mime:"audio/midi",description:"",signature:{0:77,1:84,2:104,3:100},extractor:null},{name:"MPEG-3 audio",extension:"mp3",mime:"audio/mpeg",description:"",signature:[{0:73,1:68,2:51},{0:255,1:251}],extractor:null},{name:"MPEG-4 Part 14 audio",extension:"m4a",mime:"audio/m4a",description:"",signature:[{4:102,5:116,6:121,7:112,8:77,9:52,10:65},{0:77,1:52,2:65,3:32}],extractor:null},{name:"Free Lossless Audio Codec",extension:"flac",mime:"audio/x-flac",description:"",signature:{0:102,1:76,2:97,3:67},extractor:null},{name:"Adaptive Multi-Rate audio codec",extension:"amr",mime:"audio/amr",description:"",signature:{0:35,1:33,2:65,3:77,4:82,5:10},extractor:null},{name:"Audacity",extension:"au",mime:"audio/x-au",description:"",signature:{0:100,1:110,2:115,3:46,24:65,25:117,26:100,27:97,28:99,29:105,30:116,31:121,32:66,33:108,34:111,35:99,36:107,37:70,38:105,39:108,40:101},extractor:null},{name:"Audacity Block",extension:"auf",mime:"application/octet-stream",description:"",signature:{0:65,1:117,2:100,3:97,4:99,5:105,6:116,7:121,8:66,9:108,10:111,11:99,12:107,13:70,14:105,15:108,16:101},extractor:null},{name:"Audio Interchange File",extension:"aif",mime:"audio/x-aiff",description:"",signature:{0:70,1:79,2:82,3:77,8:65,9:73,10:70,11:70},extractor:null},{name:"Audio Interchange File (compressed)",extension:"aifc",mime:"audio/x-aifc",description:"",signature:{0:70,1:79,2:82,3:77,8:65,9:73,10:70,11:67},extractor:null}],Documents:[{name:"Portable Document Format",extension:"pdf",mime:"application/pdf",description:"",signature:{0:37,1:80,2:68,3:70},extractor:function(e,t){var r=new o.a(e.slice(t));return r.continueUntil([37,37,69,79,70]),r.moveForwardsBy(5),r.consumeIf(13),r.consumeIf(10),r.carve()}},{name:"PostScript",extension:"ps",mime:"application/postscript",description:"",signature:{0:37,1:33},extractor:null},{name:"Rich Text Format",extension:"rtf",mime:"application/rtf",description:"",signature:{0:123,1:92,2:114,3:116,4:102},extractor:function(e,t){var r=new o.a(e.slice(t)),n=0;if(123!==r.readInt(1))throw new Error("Not a valid RTF file");n++;for(;n>0&&r.hasMore();)switch(r.readInt(1)){case 123:n++;break;case 125:n--;break;case 92:r.consumeIf(92),r.position++}return r.carve()}},{name:"Microsoft Office documents/OLE2",extension:"ole2,doc,xls,dot,ppt,xla,ppa,pps,pot,msi,sdw,db,vsd,msg",mime:"application/msword,application/vnd.ms-excel,application/vnd.ms-powerpoint",description:"Microsoft Office documents",signature:{0:208,1:207,2:17,3:224,4:161,5:177,6:26,7:225},extractor:null},{name:"Microsoft Office 2007+ documents",extension:"docx,xlsx,pptx",mime:"application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.openxmlformats-officedocument.presentationml.presentation",description:"",signature:{38:95,39:84,40:121,41:112,42:101,43:115,44:93,45:46,46:120,47:109,48:108},extractor:a},{name:"EPUB e-book",extension:"epub",mime:"application/epub+zip",description:"",signature:{0:80,1:75,2:3,3:4,30:109,31:105,32:109,33:101,34:116,35:121,36:112,37:101,38:97,39:112,40:112,41:108,42:105,43:99,44:97,45:116,46:105,47:111,48:110,49:47,50:101,51:112,52:117,53:98,54:43,55:122,56:105,57:112},extractor:a}],Applications:[{name:"Windows Portable Executable",extension:"exe,dll,drv,vxd,sys,ocx,vbx,com,fon,scr",mime:"application/x-msdownload",description:"",signature:{0:77,1:90,3:[0,1,2],5:[0,1,2]},extractor:function(e,t){var r=new o.a(e.slice(t));r.moveTo(60);var n=r.readInt(4,"le");r.moveTo(n),r.moveForwardsBy(6);var i=r.readInt(2,"le");r.moveForwardsBy(12);var u=r.readInt(2,"le");r.moveForwardsBy(2+u),r.moveForwardsBy(40*(i-1)),r.moveForwardsBy(16);var a=r.readInt(4,"le"),A=r.readInt(4,"le");return r.moveTo(A+a),r.carve()}},{name:"Executable and Linkable Format file",extension:"elf,bin,axf,o,prx,so",mime:"application/x-executable",description:"Executable and Linkable Format file. No standard file extension.",signature:{0:127,1:69,2:76,3:70},extractor:function(e,t){var r=new o.a(e.slice(t));r.moveForwardsBy(4);var n=1===r.readInt(1),i=1===r.readInt(1)?"le":"be";r.moveForwardsBy(n?26:34);var u=n?r.readInt(4,i):r.readInt(8,i);r.moveForwardsBy(10);var a=r.readInt(2,i),A=r.readInt(2,i);return r.moveTo(u),r.moveForwardsBy(a*A),r.carve()}},{name:"Adobe Flash",extension:"swf",mime:"application/x-shockwave-flash",description:"",signature:{0:[67,70],1:87,2:83},extractor:null},{name:"Java Class",extension:"class",mime:"application/java-vm",description:"",signature:{0:202,1:254,2:186,3:190},extractor:null},{name:"Dalvik Executable",extension:"dex",mime:"application/octet-stream",description:"Dalvik Executable as used by Android",signature:{0:100,1:101,2:120,3:10,4:48,5:51,6:53,7:0},extractor:null},{name:"Google Chrome Extension",extension:"crx",mime:"application/crx",description:"Google Chrome extension or packaged app",signature:{0:67,1:114,2:50,3:52},extractor:null}],Archives:[{name:"PKZIP archive",extension:"zip",mime:"application/zip",description:"",signature:{0:80,1:75,2:[3,5,7],3:[4,6,8]},extractor:a},{name:"TAR archive",extension:"tar",mime:"application/x-tar",description:"",signature:{257:117,258:115,259:116,260:97,261:114},extractor:null},{name:"Roshal Archive",extension:"rar",mime:"application/x-rar-compressed",description:"",signature:{0:82,1:97,2:114,3:33,4:26,5:7,6:[0,1]},extractor:null},{name:"Gzip",extension:"gz",mime:"application/gzip",description:"",signature:{0:31,1:139,2:8},extractor:function(e,t){var r=new o.a(e.slice(t));r.moveForwardsBy(3);var n=r.readInt(1);if(r.moveForwardsBy(4),r.readInt(1),r.moveForwardsBy(1),4&n){var i=r.readInt(2,"le");r.moveForwardsby(i)}8&n&&(r.continueUntil(0),r.moveForwardsBy(1));16&n&&(r.continueUntil(0),r.moveForwardsBy(1));2&n&&r.moveForwardsBy(2);return l(r),r.moveForwardsBy(8),r.carve()}},{name:"Bzip2",extension:"bz2",mime:"application/x-bzip2",description:"",signature:{0:66,1:90,2:104},extractor:null},{name:"7zip",extension:"7z",mime:"application/x-7z-compressed",description:"",signature:{0:55,1:122,2:188,3:175,4:39,5:28},extractor:null},{name:"Zlib Deflate",extension:"zlib",mime:"application/x-deflate",description:"",signature:{0:120,1:[1,156,218,94]},extractor:function(e,t){var r=new o.a(e.slice(t));r.moveForwardsBy(1),32&r.readInt(1)&&r.moveForwardsBy(4);return l(r),r.moveForwardsBy(4),r.carve()}},{name:"xz compression",extension:"xz",mime:"application/x-xz",description:"",signature:{0:253,1:55,2:122,3:88,4:90,5:0},extractor:null},{name:"Tarball",extension:"tar.z",mime:"application/x-gtar",description:"",signature:{0:31,1:[157,160]},extractor:null},{name:"ISO disk image",extension:"iso",mime:"application/octet-stream",description:"ISO 9660 CD/DVD image file",signature:[{32769:67,32770:68,32771:48,32772:48,32773:49},{34817:67,34818:68,34819:48,34820:48,34821:49},{36865:67,36866:68,36867:48,36868:48,36869:49}],extractor:null},{name:"Virtual Machine Disk",extension:"vmdk",mime:"application/vmdk,application/x-virtualbox-vmdk",description:"",signature:{0:75,1:68,2:77},extractor:null},{name:"ARJ Archive",extension:"arj",mime:"application/x-arj-compressed",description:"",signature:{0:96,1:234,8:[0,16,20],9:0,10:2},extractor:null},{name:"WinAce Archive",extension:"ace",mime:"application/x-ace-compressed",description:"",signature:{7:42,8:42,9:65,10:67,11:69,12:42,13:42},extractor:null},{name:"Macintosh BinHex Encoded File",extension:"hqx",mime:"application/mac-binhex",description:"",signature:{11:109,12:117,13:115,14:116,15:32,16:98,17:101,18:32,19:99,20:111,21:110,22:118,23:101,24:114,25:116,26:101,27:100,28:32,29:119,30:105,31:116,32:104,33:32,34:66,35:105,36:110,37:72,38:101,39:120},extractor:null},{name:"ALZip Archive",extension:"alz",mime:"application/octet-stream",description:"",signature:{0:65,1:76,2:90,3:1,4:10,5:0,6:0,7:0},extractor:null},{name:"KGB Compressed Archive",extension:"kgb",mime:"application/x-kgb-compressed",description:"",signature:{0:75,1:71,2:66,3:95,4:97,5:114,6:99,7:104,8:32,9:45},extractor:null}],Miscellaneous:[{name:"UTF-8 text file",extension:"txt",mime:"text/plain",description:"UTF-8 encoded Unicode byte order mark, commonly but not exclusively seen in text files.",signature:{0:239,1:187,2:191},extractor:null},{name:"UTF-32 LE file",extension:"utf32le",mime:"charset/utf32le",description:"Little-endian UTF-32 encoded Unicode byte order mark.",signature:{0:255,1:254,2:0,3:0},extractor:null},{name:"UTF-16 LE file",extension:"utf16le",mime:"charset/utf16le",description:"Little-endian UTF-16 encoded Unicode byte order mark.",signature:{0:255,1:254},extractor:null},{name:"Web Open Font Format",extension:"woff",mime:"application/font-woff",description:"",signature:{0:119,1:79,2:70,3:70,4:0,5:1,6:0,7:0},extractor:null},{name:"Web Open Font Format 2",extension:"woff2",mime:"application/font-woff",description:"",signature:{0:119,1:79,2:70,3:50,4:0,5:1,6:0,7:0},extractor:null},{name:"Embedded OpenType font",extension:"eot",mime:"application/octet-stream",description:"",signature:[{8:2,9:0,10:1,34:76,35:80},{8:1,9:0,10:0,34:76,35:80},{8:2,9:0,10:2,34:76,35:80}],extractor:null},{name:"TrueType Font",extension:"ttf",mime:"application/font-sfnt",description:"",signature:{0:0,1:1,2:0,3:0,4:0},extractor:null},{name:"OpenType Font",extension:"otf",mime:"application/font-sfnt",description:"",signature:{0:79,1:84,2:84,3:79,4:0},extractor:null},{name:"SQLite",extension:"sqlite",mime:"application/x-sqlite3",description:"",signature:{0:83,1:81,2:76,3:105},extractor:null},{name:"BitTorrent link",extension:"torrent",mime:"application/x-bittorrent",description:"",signature:[{0:100,1:56,2:58,3:97,4:110,5:110,6:111,7:117,8:110,9:99,10:101,11:35,12:35,13:58},{0:100,1:52,2:58,3:105,4:110,5:102,6:111,7:100,8:[52,53,54],9:58}],extractor:null}]};function a(e,t){var r=new o.a(e.slice(t));r.continueUntil([80,75,5,6]),r.moveForwardsBy(20);var n=r.readInt(2,"le");return r.moveForwardsBy(n),r.carve()}for(var A=new Array(288),s=0;s<A.length;s++)A[s]=s<=143?8:s<=255?9:s<=279?7:8;var c=C(A),f=C(new Array(30).fill(5)),S=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];function l(e){for(var t=0;!t;){t=e.readBits(1);var r=e.readBits(2);if(0===r){e.moveForwardsBy(1);var n=e.readInt(2,"le");e.moveForwardsBy(2+n)}else if(1===r)P(e,c,f);else{if(2!==r)throw new Error(`Invalid block type while parsing DEFLATE stream at pos ${e.position}`);for(var i=e.readBits(5)+257,o=e.readBits(5)+1,u=e.readBits(4)+4,a=new Uint8Array(S.length),A=0;A<u;A++)a[S[A]]=e.readBits(3);for(var s=C(a),l=new Uint8Array(i+o),B=void 0,D=void 0,h=void 0,F=0;F<i+o;)switch(B=E(e,s)){case 16:for(D=3+e.readBits(2);D--;)l[F++]=h;break;case 17:for(D=3+e.readBits(3);D--;)l[F++]=0;h=0;break;case 18:for(D=11+e.readBits(7);D--;)l[F++]=0;h=0;break;default:l[F++]=B,h=B}P(e,C(l.subarray(0,i)),C(l.subarray(i)))}}e.bitPos>0&&e.moveForwardsBy(1)}var B=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0],D=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13];function P(e,t,r){for(var n,i=0;(n=E(e,t))&&256!==n;){if(++i>1e4)throw new Error("Caught in probable infinite loop while parsing Huffman Block");n<256||(e.readBits(B[n-257]),n=E(e,r),e.readBits(D[n]))}}function C(e){for(var t=Math.max.apply(Math,e),r=Math.min.apply(Math,e),n=1<<t,i=new Uint32Array(n),o=1,u=0,a=2;o<=t;){for(var A=0;A<e.length;A++)if(e[A]===o){var s=void 0,c=void 0,f=void 0;for(s=0,c=u,f=0;f<o;f++)s=s<<1|1&c,c>>=1;for(var S=o<<16|A,l=s;l<n;l+=a)i[l]=S;u++}o++,u<<=1,a<<=1}return[i,t,r]}function E(e,t){var r=i()(t,2),n=r[0],o=r[1],u=n[e.readBits(o)&(1<<o)-1],a=u>>>16;if(a>o)throw new Error(`Invalid Huffman Code length while parsing DEFLATE block at pos ${e.position}: ${a}`);return e.moveBackwardsByBits(o-a),65535&u}r(0);
/**
 * File type functions
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 *
 */
function h(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(e.length){for(var n=0;n<e.length;n++)if(F(e[n],t,r))return!0;return!1}return F(e,t,r)}function F(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;for(var n in e){var i=parseInt(n,10)+r;switch(typeof e[n]){case"number":if(t[i]!==e[n])return!1;break;case"object":if(e[n].indexOf(t[i])<0)return!1;break;case"function":if(!e[n](t[i]))return!1;break;default:throw new Error(`Unrecognised signature type at offset ${n}`)}}return!0}function M(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Object.keys(u);if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),!(e&&e.length>1))return[];var r=[],n={};for(var i in u)t.includes(i)&&(n[i]=u[i]);for(var o in n){n[o].forEach(function(t){h(t.signature,e)&&r.push(t)})}return r}function p(e){return function(e,t){var r=M(t);if(!r||!r.length)return!1;if("string"==typeof e)return r.reduce(function(t,r){var n=!!r.mime.startsWith(e)&&r.mime;return t||n},!1);if(e instanceof RegExp)return r.reduce(function(t,r){var n=!!e.test(r.mime)&&r.mime;return t||n},!1);throw new Error("Invalid type input.")}("image",e)}r.d(t,"a",function(){return M}),r.d(t,"b",function(){return p})},2:function(e,t){function r(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}e.exports=function(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),e}},24:function(e,t,r){!function(e){var t,r,n,i=String.fromCharCode;function o(e){for(var t,r,n=[],i=0,o=e.length;i<o;)(t=e.charCodeAt(i++))>=55296&&t<=56319&&i<o?56320==(64512&(r=e.charCodeAt(i++)))?n.push(((1023&t)<<10)+(1023&r)+65536):(n.push(t),i--):n.push(t);return n}function u(e){if(e>=55296&&e<=57343)throw Error("Lone surrogate U+"+e.toString(16).toUpperCase()+" is not a scalar value")}function a(e,t){return i(e>>t&63|128)}function A(e){if(0==(4294967168&e))return i(e);var t="";return 0==(4294965248&e)?t=i(e>>6&31|192):0==(4294901760&e)?(u(e),t=i(e>>12&15|224),t+=a(e,6)):0==(4292870144&e)&&(t=i(e>>18&7|240),t+=a(e,12),t+=a(e,6)),t+=i(63&e|128)}function s(){if(n>=r)throw Error("Invalid byte index");var e=255&t[n];if(n++,128==(192&e))return 63&e;throw Error("Invalid continuation byte")}function c(){var e,i;if(n>r)throw Error("Invalid byte index");if(n==r)return!1;if(e=255&t[n],n++,0==(128&e))return e;if(192==(224&e)){if((i=(31&e)<<6|s())>=128)return i;throw Error("Invalid continuation byte")}if(224==(240&e)){if((i=(15&e)<<12|s()<<6|s())>=2048)return u(i),i;throw Error("Invalid continuation byte")}if(240==(248&e)&&(i=(7&e)<<18|s()<<12|s()<<6|s())>=65536&&i<=1114111)return i;throw Error("Invalid UTF-8 detected")}e.version="3.0.0",e.encode=function(e){for(var t=o(e),r=t.length,n=-1,i="";++n<r;)i+=A(t[n]);return i},e.decode=function(e){t=o(e),r=t.length,n=0;for(var u,a=[];!1!==(u=c());)a.push(u);return function(e){for(var t,r=e.length,n=-1,o="";++n<r;)(t=e[n])>65535&&(o+=i((t-=65536)>>>10&1023|55296),t=56320|1023&t),o+=i(t);return o}(a)}}(t)},25:function(e,t){e.exports=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}},26:function(e,t,r){var n,i;!function(o,u){"use strict";void 0===(i="function"==typeof(n=function(){var e=function(){},t="undefined",r=["trace","debug","info","warn","error"];function n(e,t){var r=e[t];if("function"==typeof r.bind)return r.bind(e);try{return Function.prototype.bind.call(r,e)}catch(t){return function(){return Function.prototype.apply.apply(r,[e,arguments])}}}function i(t,n){for(var i=0;i<r.length;i++){var o=r[i];this[o]=i<t?e:this.methodFactory(o,t,n)}this.log=this.debug}function o(e,r,n){return function(){typeof console!==t&&(i.call(this,r,n),this[e].apply(this,arguments))}}function u(r,i,u){return function(r){return"debug"===r&&(r="log"),typeof console!==t&&(void 0!==console[r]?n(console,r):void 0!==console.log?n(console,"log"):e)}(r)||o.apply(this,arguments)}function a(e,n,o){var a,A=this,s="loglevel";function c(){var e;if(typeof window!==t){try{e=window.localStorage[s]}catch(e){}if(typeof e===t)try{var r=window.document.cookie,n=r.indexOf(encodeURIComponent(s)+"=");-1!==n&&(e=/^([^;]+)/.exec(r.slice(n))[1])}catch(e){}return void 0===A.levels[e]&&(e=void 0),e}}e&&(s+=":"+e),A.name=e,A.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},A.methodFactory=o||u,A.getLevel=function(){return a},A.setLevel=function(n,o){if("string"==typeof n&&void 0!==A.levels[n.toUpperCase()]&&(n=A.levels[n.toUpperCase()]),!("number"==typeof n&&n>=0&&n<=A.levels.SILENT))throw"log.setLevel() called with invalid level: "+n;if(a=n,!1!==o&&function(e){var n=(r[e]||"silent").toUpperCase();if(typeof window!==t){try{return void(window.localStorage[s]=n)}catch(e){}try{window.document.cookie=encodeURIComponent(s)+"="+n+";"}catch(e){}}}(n),i.call(A,n,e),typeof console===t&&n<A.levels.SILENT)return"No console available for logging"},A.setDefaultLevel=function(e){c()||A.setLevel(e,!1)},A.enableAll=function(e){A.setLevel(A.levels.TRACE,e)},A.disableAll=function(e){A.setLevel(A.levels.SILENT,e)};var f=c();null==f&&(f=null==n?"WARN":n),A.setLevel(f,!1)}var A=new a,s={};A.getLogger=function(e){if("string"!=typeof e||""===e)throw new TypeError("You must supply a name when creating a logger.");var t=s[e];return t||(t=s[e]=new a(e,A.getLevel(),A.methodFactory)),t};var c=typeof window!==t?window.log:void 0;return A.noConflict=function(){return typeof window!==t&&window.log===A&&(window.log=c),A},A.getLoggers=function(){return s},A})?n.call(t,r,t,e):n)||(e.exports=i)}()},27:function(e,t){var r,n,i=e.exports={};function o(){throw new Error("setTimeout has not been defined")}function u(){throw new Error("clearTimeout has not been defined")}function a(e){if(r===setTimeout)return setTimeout(e,0);if((r===o||!r)&&setTimeout)return r=setTimeout,setTimeout(e,0);try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:o}catch(e){r=o}try{n="function"==typeof clearTimeout?clearTimeout:u}catch(e){n=u}}();var A,s=[],c=!1,f=-1;function S(){c&&A&&(c=!1,A.length?s=A.concat(s):f=-1,s.length&&l())}function l(){if(!c){var e=a(S);c=!0;for(var t=s.length;t;){for(A=s,s=[];++f<t;)A&&A[f].run();f=-1,t=s.length}A=null,c=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===u||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(e)}}function B(e,t){this.fun=e,this.array=t}function D(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];s.push(new B(e,t)),1!==s.length||c||a(l)},B.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=D,i.addListener=D,i.once=D,i.off=D,i.removeListener=D,i.removeAllListeners=D,i.emit=D,i.prependListener=D,i.prependOnceListener=D,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},28:function(e,t){var r;r=function(){return this}();try{r=r||new Function("return this")()}catch(e){"object"==typeof window&&(r=window)}e.exports=r},29:function(e,t,r){"use strict";r.d(t,"a",function(){return i});var n=r(0);
/**
 * Binary functions.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 */function i(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Space",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:8,i=n.b.regexRep(t);e=e.replace(i,"");for(var o=[],u=0;u<e.length;u+=r)o.push(parseInt(e.substr(u,r),2));return o}},3:function(e,t){function r(t){return e.exports=r=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},r(t)}e.exports=r},30:function(e,t,r){"use strict";r.d(t,"a",function(){return i});var n=r(0);
/**
 * Decimal functions.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 */function i(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Auto";t=n.b.charRep(t);var r=[],i=e.split(t);""===i[i.length-1]&&(i=i.slice(0,i.length-1));for(var o=0;o<i.length;o++)r[o]=parseInt(i[o],10);return r}},4:function(e,t,r){var n=r(43),i=r(25);e.exports=function(e,t){return!t||"object"!==n(t)&&"function"!=typeof t?i(e):t}},43:function(e,t){function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function n(t){return"function"==typeof Symbol&&"symbol"===r(Symbol.iterator)?e.exports=n=function(e){return r(e)}:e.exports=n=function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":r(e)},n(t)}e.exports=n},44:function(e,t){function r(t,n){return e.exports=r=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},r(t,n)}e.exports=r},45:function(e,t,r){"use strict";t.byteLength=function(e){var t=s(e),r=t[0],n=t[1];return 3*(r+n)/4-n},t.toByteArray=function(e){for(var t,r=s(e),n=r[0],u=r[1],a=new o(function(e,t,r){return 3*(t+r)/4-r}(0,n,u)),A=0,c=u>0?n-4:n,f=0;f<c;f+=4)t=i[e.charCodeAt(f)]<<18|i[e.charCodeAt(f+1)]<<12|i[e.charCodeAt(f+2)]<<6|i[e.charCodeAt(f+3)],a[A++]=t>>16&255,a[A++]=t>>8&255,a[A++]=255&t;2===u&&(t=i[e.charCodeAt(f)]<<2|i[e.charCodeAt(f+1)]>>4,a[A++]=255&t);1===u&&(t=i[e.charCodeAt(f)]<<10|i[e.charCodeAt(f+1)]<<4|i[e.charCodeAt(f+2)]>>2,a[A++]=t>>8&255,a[A++]=255&t);return a},t.fromByteArray=function(e){for(var t,r=e.length,i=r%3,o=[],u=0,a=r-i;u<a;u+=16383)o.push(c(e,u,u+16383>a?a:u+16383));1===i?(t=e[r-1],o.push(n[t>>2]+n[t<<4&63]+"==")):2===i&&(t=(e[r-2]<<8)+e[r-1],o.push(n[t>>10]+n[t>>4&63]+n[t<<2&63]+"="));return o.join("")};for(var n=[],i=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0,A=u.length;a<A;++a)n[a]=u[a],i[u.charCodeAt(a)]=a;function s(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");return-1===r&&(r=t),[r,r===t?0:4-r%4]}function c(e,t,r){for(var i,o,u=[],a=t;a<r;a+=3)i=(e[a]<<16&16711680)+(e[a+1]<<8&65280)+(255&e[a+2]),u.push(n[(o=i)>>18&63]+n[o>>12&63]+n[o>>6&63]+n[63&o]);return u.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},46:function(e,t){t.read=function(e,t,r,n,i){var o,u,a=8*i-n-1,A=(1<<a)-1,s=A>>1,c=-7,f=r?i-1:0,S=r?-1:1,l=e[t+f];for(f+=S,o=l&(1<<-c)-1,l>>=-c,c+=a;c>0;o=256*o+e[t+f],f+=S,c-=8);for(u=o&(1<<-c)-1,o>>=-c,c+=n;c>0;u=256*u+e[t+f],f+=S,c-=8);if(0===o)o=1-s;else{if(o===A)return u?NaN:1/0*(l?-1:1);u+=Math.pow(2,n),o-=s}return(l?-1:1)*u*Math.pow(2,o-n)},t.write=function(e,t,r,n,i,o){var u,a,A,s=8*o-i-1,c=(1<<s)-1,f=c>>1,S=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,l=n?0:o-1,B=n?1:-1,D=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,u=c):(u=Math.floor(Math.log(t)/Math.LN2),t*(A=Math.pow(2,-u))<1&&(u--,A*=2),(t+=u+f>=1?S/A:S*Math.pow(2,1-f))*A>=2&&(u++,A/=2),u+f>=c?(a=0,u=c):u+f>=1?(a=(t*A-1)*Math.pow(2,i),u+=f):(a=t*Math.pow(2,f-1)*Math.pow(2,i),u=0));i>=8;e[r+l]=255&a,l+=B,a/=256,i-=8);for(u=u<<i|a,s+=i;s>0;e[r+l]=255&u,l+=B,u/=256,s-=8);e[r+l-B]|=128*D}},47:function(e,t){var r={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==r.call(e)}},48:function(e,t,r){var n=function(e){"use strict";var t,r=Object.prototype,n=r.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",a=i.toStringTag||"@@toStringTag";function A(e,t,r,n){var i=t&&t.prototype instanceof D?t:D,o=Object.create(i.prototype),u=new v(n||[]);return o._invoke=function(e,t,r){var n=c;return function(i,o){if(n===S)throw new Error("Generator is already running");if(n===l){if("throw"===i)throw o;return O()}for(r.method=i,r.arg=o;;){var u=r.delegate;if(u){var a=R(u,r);if(a){if(a===B)continue;return a}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===c)throw n=l,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=S;var A=s(e,t,r);if("normal"===A.type){if(n=r.done?l:f,A.arg===B)continue;return{value:A.arg,done:r.done}}"throw"===A.type&&(n=l,r.method="throw",r.arg=A.arg)}}}(e,r,u),o}function s(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=A;var c="suspendedStart",f="suspendedYield",S="executing",l="completed",B={};function D(){}function P(){}function C(){}var E={};E[o]=function(){return this};var h=Object.getPrototypeOf,F=h&&h(h(y([])));F&&F!==r&&n.call(F,o)&&(E=F);var M=C.prototype=D.prototype=Object.create(E);function p(e){["next","throw","return"].forEach(function(t){e[t]=function(e){return this._invoke(t,e)}})}function T(e){var t;this._invoke=function(r,i){function o(){return new Promise(function(t,o){!function t(r,i,o,u){var a=s(e[r],e,i);if("throw"!==a.type){var A=a.arg,c=A.value;return c&&"object"==typeof c&&n.call(c,"__await")?Promise.resolve(c.__await).then(function(e){t("next",e,o,u)},function(e){t("throw",e,o,u)}):Promise.resolve(c).then(function(e){A.value=e,o(A)},function(e){return t("throw",e,o,u)})}u(a.arg)}(r,i,t,o)})}return t=t?t.then(o,o):o()}}function R(e,r){var n=e.iterator[r.method];if(n===t){if(r.delegate=null,"throw"===r.method){if(e.iterator.return&&(r.method="return",r.arg=t,R(e,r),"throw"===r.method))return B;r.method="throw",r.arg=new TypeError("The iterator does not provide a 'throw' method")}return B}var i=s(n,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,B;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,B):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,B)}function d(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function g(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function v(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(d,this),this.reset(!0)}function y(e){if(e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,u=function r(){for(;++i<e.length;)if(n.call(e,i))return r.value=e[i],r.done=!1,r;return r.value=t,r.done=!0,r};return u.next=u}}return{next:O}}function O(){return{value:t,done:!0}}return P.prototype=M.constructor=C,C.constructor=P,C[a]=P.displayName="GeneratorFunction",e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===P||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,C):(e.__proto__=C,a in e||(e[a]="GeneratorFunction")),e.prototype=Object.create(M),e},e.awrap=function(e){return{__await:e}},p(T.prototype),T.prototype[u]=function(){return this},e.AsyncIterator=T,e.async=function(t,r,n,i){var o=new T(A(t,r,n,i));return e.isGeneratorFunction(r)?o:o.next().then(function(e){return e.done?e.value:o.next()})},p(M),M[a]="Generator",M[o]=function(){return this},M.toString=function(){return"[object Generator]"},e.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},e.values=y,v.prototype={constructor:v,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(g),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function i(n,i){return a.type="throw",a.arg=e,r.next=n,i&&(r.method="next",r.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var u=this.tryEntries[o],a=u.completion;if("root"===u.tryLoc)return i("end");if(u.tryLoc<=this.prev){var A=n.call(u,"catchLoc"),s=n.call(u,"finallyLoc");if(A&&s){if(this.prev<u.catchLoc)return i(u.catchLoc,!0);if(this.prev<u.finallyLoc)return i(u.finallyLoc)}else if(A){if(this.prev<u.catchLoc)return i(u.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return i(u.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var u=o?o.completion:{};return u.type=e,u.arg=t,o?(this.method="next",this.next=o.finallyLoc,B):this.complete(u)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),B},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),g(r),B}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;g(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:y(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),B}},e}(e.exports);try{regeneratorRuntime=n}catch(e){Function("r","regeneratorRuntime = r")(n)}},49:function(e,t){e.exports=function(e){if(Array.isArray(e))return e}},5:function(e,t,r){var n=r(44);e.exports=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&n(e,t)}},50:function(e,t){e.exports=function(e,t){var r=[],n=!0,i=!1,o=void 0;try{for(var u,a=e[Symbol.iterator]();!(n=(u=a.next()).done)&&(r.push(u.value),!t||r.length!==t);n=!0);}catch(e){i=!0,o=e}finally{try{n||null==a.return||a.return()}finally{if(i)throw o}}return r}},51:function(e,t){e.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}},6:function(e,t,r){"use strict";var n=r(1),i=r.n(n),o=r(2),u=r.n(o),a=r(15),A=r(0),s=r(17),c=function(){function e(t){i()(this,e),this.name="",this.type="",this._value=null,this.disabled=!1,this.hint="",this.rows=0,this.toggleValues=[],this.target=null,this.defaultIndex=0,this.min=null,this.max=null,this.step=1,t&&this._parseConfig(t)}return u()(e,[{key:"_parseConfig",value:function(e){this.name=e.name,this.type=e.type,this.defaultValue=e.value,this.disabled=!!e.disabled,this.hint=e.hint||!1,this.rows=e.rows||!1,this.toggleValues=e.toggleValues,this.target=void 0!==e.target?e.target:null,this.defaultIndex=void 0!==e.defaultIndex?e.defaultIndex:0,this.min=e.min,this.max=e.max,this.step=e.step}},{key:"config",get:function(){return this._value}},{key:"value",set:function(t){this._value=e.prepare(t,this.type)},get:function(){return this._value}}],[{key:"prepare",value:function(e,t){var r;switch(t){case"binaryString":case"binaryShortString":case"editableOption":case"editableOptionShort":return A.b.parseEscapedChars(e);case"byteArray":return"string"==typeof e?(e=e.replace(/\s+/g,""),Object(s.a)(e)):e;case"number":if(r=parseFloat(e),isNaN(r))throw"Invalid ingredient value. Not a number: "+A.b.truncate(e.toString(),10);return r;default:return e}}}]),e}(),f=function(){function e(){i()(this,e),this._inputType=-1,this._outputType=-1,this._presentType=-1,this._breakpoint=!1,this._disabled=!1,this._flowControl=!1,this._manualBake=!1,this._ingList=[],this.name="",this.module="",this.description="",this.infoURL=null}return u()(e,[{key:"run",value:function(e,t){return e}},{key:"highlight",value:function(e,t){return!1}},{key:"highlightReverse",value:function(e,t){return!1}},{key:"present",value:function(e,t){return e}},{key:"addIngredient",value:function(e){this._ingList.push(e)}},{key:"inputType",set:function(e){this._inputType=a.a.typeEnum(e)},get:function(){return a.a.enumLookup(this._inputType)}},{key:"outputType",set:function(e){this._outputType=a.a.typeEnum(e),this._presentType<0&&(this._presentType=this._outputType)},get:function(){return a.a.enumLookup(this._outputType)}},{key:"presentType",set:function(e){this._presentType=a.a.typeEnum(e)},get:function(){return a.a.enumLookup(this._presentType)}},{key:"args",set:function(e){var t=this;e.forEach(function(e){var r=new c(e);t.addIngredient(r)})},get:function(){return this._ingList.map(function(e){var t={name:e.name,type:e.type,value:e.defaultValue};return e.toggleValues&&(t.toggleValues=e.toggleValues),e.hint&&(t.hint=e.hint),e.rows&&(t.rows=e.rows),e.disabled&&(t.disabled=e.disabled),e.target&&(t.target=e.target),e.defaultIndex&&(t.defaultIndex=e.defaultIndex),"number"==typeof e.min&&(t.min=e.min),"number"==typeof e.max&&(t.max=e.max),e.step&&(t.step=e.step),t})}},{key:"config",get:function(){return{op:this.name,args:this._ingList.map(function(e){return e.config})}}},{key:"ingValues",set:function(e){var t=this;e.forEach(function(e,r){t._ingList[r].value=e})},get:function(){return this._ingList.map(function(e){return e.value})}},{key:"breakpoint",set:function(e){this._breakpoint=!!e},get:function(){return this._breakpoint}},{key:"disabled",set:function(e){this._disabled=!!e},get:function(){return this._disabled}},{key:"flowControl",get:function(){return this._flowControl},set:function(e){this._flowControl=!!e}},{key:"manualBake",get:function(){return this._manualBake},set:function(e){this._manualBake=!!e}}]),e}();t.a=f},7:function(e,t,r){e.exports=r(48)},9:function(e,t,r){"use strict";var n=r(1),i=r.n(n),o=r(4),u=r.n(o),a=r(3),A=r.n(a),s=r(25),c=r.n(s),f=r(5),S=r.n(f);
/**
 * Custom error type for handling operation input errors.
 * i.e. where the operation can handle the error and print a message to the screen.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 */
var l=function(e){function t(){var e;i()(this,t);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return(e=u()(this,A()(t).call(this,...n))).type="OperationError",Error.captureStackTrace&&Error.captureStackTrace(c()(e),t),e}return S()(t,e),t}(function(e){function t(){var t=Reflect.construct(e,Array.from(arguments));return Object.setPrototypeOf(t,Object.getPrototypeOf(this)),t}return t.prototype=Object.create(e.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e,t}(Error));t.a=l}});