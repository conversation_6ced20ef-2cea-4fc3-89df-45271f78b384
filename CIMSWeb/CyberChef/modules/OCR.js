/**
 * CyberChef - The Cyber Swiss Army Knife
 *
 * @copyright Crown Copyright 2016
 * @license Apache-2.0
 *
 *   Copyright 2016 Crown Copyright
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */!function(t){var e={};function r(n){if(e[n])return e[n].exports;var i=e[n]={i:n,l:!1,exports:{}};return t[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)r.d(n,i,function(e){return t[e]}.bind(null,i));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=1346)}({0:function(t,e,r){"use strict";(function(t,n){r.d(e,"c",function(){return b}),r.d(e,"d",function(){return A}),r.d(e,"e",function(){return x}),r.d(e,"a",function(){return B});var i=r(7),o=r.n(i),a=r(11),s=r.n(a),u=r(1),f=r.n(u),c=r(2),h=r.n(c),l=r(24),p=r.n(l),d=r(14),m=r(17),g=r(30),y=r(29),v=function(){function e(){f()(this,e)}var r;return h()(e,null,[{key:"chr",value:function(t){if(t>65535){t-=65536;var e=String.fromCharCode(t>>>10&1023|55296);return t=56320|1023&t,e+String.fromCharCode(t)}return String.fromCharCode(t)}},{key:"ord",value:function(t){if(2===t.length){var e=t.charCodeAt(0),r=t.charCodeAt(1);if(e>=55296&&e<56320&&r>=56320&&r<57344)return 1024*(e-55296)+r-56320+65536}return t.charCodeAt(0)}},{key:"padBytesRight",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=new Array(e);return n.fill(r),[...t].forEach(function(t,e){n[e]=t}),n}},{key:"truncate",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"...";return t.length>e&&(t=t.slice(0,e-r.length)+r),t}},{key:"hex",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return(t="string"==typeof t?e.ord(t):t).toString(16).padStart(r,"0")}},{key:"bin",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:8;return(t="string"==typeof t?e.ord(t):t).toString(2).padStart(r,"0")}},{key:"printable",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];w()&&window.app&&!window.app.options.treatAsUtf8&&(t=e.byteArrayToChars(e.strToByteArray(t)));var n=/[\0-\x08\x0B-\x0C\x0E-\x1F\x7F-\x9F\xAD\u0378\u0379\u037F-\u0383\u038B\u038D\u03A2\u0528-\u0530\u0557\u0558\u0560\u0588\u058B-\u058E\u0590\u05C8-\u05CF\u05EB-\u05EF\u05F5-\u0605\u061C\u061D\u06DD\u070E\u070F\u074B\u074C\u07B2-\u07BF\u07FB-\u07FF\u082E\u082F\u083F\u085C\u085D\u085F-\u089F\u08A1\u08AD-\u08E3\u08FF\u0978\u0980\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09FC-\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF2-\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B55\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B78-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BFB-\u0C00\u0C04\u0C0D\u0C11\u0C29\u0C34\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5A-\u0C5F\u0C64\u0C65\u0C70-\u0C77\u0C80\u0C81\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0D01\u0D04\u0D0D\u0D11\u0D3B\u0D3C\u0D45\u0D49\u0D4F-\u0D56\u0D58-\u0D5F\u0D64\u0D65\u0D76-\u0D78\u0D80\u0D81\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DF1\u0DF5-\u0E00\u0E3B-\u0E3E\u0E5C-\u0E80\u0E83\u0E85\u0E86\u0E89\u0E8B\u0E8C\u0E8E-\u0E93\u0E98\u0EA0\u0EA4\u0EA6\u0EA8\u0EA9\u0EAC\u0EBA\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F48\u0F6D-\u0F70\u0F98\u0FBD\u0FCD\u0FDB-\u0FFF\u10C6\u10C8-\u10CC\u10CE\u10CF\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u137D-\u137F\u139A-\u139F\u13F5-\u13FF\u169D-\u169F\u16F1-\u16FF\u170D\u1715-\u171F\u1737-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17DE\u17DF\u17EA-\u17EF\u17FA-\u17FF\u180F\u181A-\u181F\u1878-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191D-\u191F\u192C-\u192F\u193C-\u193F\u1941-\u1943\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DB-\u19DD\u1A1C\u1A1D\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1A9F\u1AAE-\u1AFF\u1B4C-\u1B4F\u1B7D-\u1B7F\u1BF4-\u1BFB\u1C38-\u1C3A\u1C4A-\u1C4C\u1C80-\u1CBF\u1CC8-\u1CCF\u1CF7-\u1CFF\u1DE7-\u1DFB\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FC5\u1FD4\u1FD5\u1FDC\u1FF0\u1FF1\u1FF5\u1FFF\u200B-\u200F\u202A-\u202E\u2060-\u206F\u2072\u2073\u208F\u209D-\u209F\u20BB-\u20CF\u20F1-\u20FF\u218A-\u218F\u23F4-\u23FF\u2427-\u243F\u244B-\u245F\u2700\u2B4D-\u2B4F\u2B5A-\u2BFF\u2C2F\u2C5F\u2CF4-\u2CF8\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D71-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E3C-\u2E7F\u2E9A\u2EF4-\u2EFF\u2FD6-\u2FEF\u2FFC-\u2FFF\u3040\u3097\u3098\u3100-\u3104\u312E-\u3130\u318F\u31BB-\u31BF\u31E4-\u31EF\u321F\u32FF\u4DB6-\u4DBF\u9FCD-\u9FFF\uA48D-\uA48F\uA4C7-\uA4CF\uA62C-\uA63F\uA698-\uA69E\uA6F8-\uA6FF\uA78F\uA794-\uA79F\uA7AB-\uA7F7\uA82C-\uA82F\uA83A-\uA83F\uA878-\uA87F\uA8C5-\uA8CD\uA8DA-\uA8DF\uA8FC-\uA8FF\uA954-\uA95E\uA97D-\uA97F\uA9CE\uA9DA-\uA9DD\uA9E0-\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A\uAA5B\uAA7C-\uAA7F\uAAC3-\uAADA\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F-\uABBF\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBC2-\uFBD2\uFD40-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFE\uFDFF\uFE1A-\uFE1F\uFE27-\uFE2F\uFE53\uFE67\uFE6C-\uFE6F\uFE75\uFEFD-\uFF00\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFDF\uFFE7\uFFEF-\uFFFB\uFFFE\uFFFF]/g,i=/[\x09-\x10\x0D\u2028\u2029]/g;return t=t.replace(n,"."),r||(t=t.replace(i,".")),t}},{key:"parseEscapedChars",value:function(t){return t.replace(/(\\)?\\([bfnrtv'"]|[0-3][0-7]{2}|[0-7]{1,2}|x[\da-fA-F]{2}|u[\da-fA-F]{4}|u\{[\da-fA-F]{1,6}\}|\\)/g,function(t,e,r){if("\\"===e)return"\\"+r;switch(r[0]){case"\\":return"\\";case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":return String.fromCharCode(parseInt(r,8));case"b":return"\b";case"t":return"\t";case"n":return"\n";case"v":return"\v";case"f":return"\f";case"r":return"\r";case'"':return'"';case"'":return"'";case"x":return String.fromCharCode(parseInt(r.substr(1),16));case"u":return"{"===r[1]?String.fromCodePoint(parseInt(r.slice(2,-1),16)):String.fromCharCode(parseInt(r.substr(1),16))}})}},{key:"escapeRegex",value:function(t){return t.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}},{key:"expandAlphRange",value:function(t){for(var r=[],n=0;n<t.length;n++)if(n<t.length-2&&"-"===t[n+1]&&"\\"!==t[n]){for(var i=e.ord(t[n]),o=e.ord(t[n+2]),a=i;a<=o;a++)r.push(e.chr(a));n+=2}else n<t.length-2&&"\\"===t[n]&&"-"===t[n+1]?(r.push("-"),n++):r.push(t[n]);return r}},{key:"convertToByteArray",value:function(t,r){switch(r.toLowerCase()){case"binary":return Object(y.a)(t);case"hex":return Object(m.a)(t);case"decimal":return Object(g.a)(t);case"base64":return Object(d.a)(t,null,"byteArray");case"utf8":return e.strToUtf8ByteArray(t);case"latin1":default:return e.strToByteArray(t)}}},{key:"convertToByteString",value:function(t,r){switch(r.toLowerCase()){case"binary":return e.byteArrayToChars(Object(y.a)(t));case"hex":return e.byteArrayToChars(Object(m.a)(t));case"decimal":return e.byteArrayToChars(Object(g.a)(t));case"base64":return e.byteArrayToChars(Object(d.a)(t,null,"byteArray"));case"utf8":return p.a.encode(t);case"latin1":default:return t}}},{key:"strToArrayBuffer",value:function(t){for(var r,n=new Uint8Array(t.length),i=t.length;i--;)if(r=t.charCodeAt(i),n[i]=r,r>255)return e.strToUtf8ArrayBuffer(t);return n.buffer}},{key:"strToUtf8ArrayBuffer",value:function(t){var r=p.a.encode(t);return t.length!==r.length&&(A()?self.setOption("attemptHighlight",!1):w()&&(window.app.options.attemptHighlight=!1)),e.strToArrayBuffer(r)}},{key:"strToByteArray",value:function(t){for(var r,n=new Array(t.length),i=t.length;i--;)if(r=t.charCodeAt(i),n[i]=r,r>255)return e.strToUtf8ByteArray(t);return n}},{key:"strToUtf8ByteArray",value:function(t){var r=p.a.encode(t);return t.length!==r.length&&(A()?self.setOption("attemptHighlight",!1):w()&&(window.app.options.attemptHighlight=!1)),e.strToByteArray(r)}},{key:"strToCharcode",value:function(t){for(var r=[],n=0;n<t.length;n++){var i=t.charCodeAt(n);if(n<t.length-1&&i>=55296&&i<56320){var o=t[n+1].charCodeAt(0);o>=56320&&o<57344&&(i=e.ord(t[n]+t[++n]))}r.push(i)}return r}},{key:"byteArrayToUtf8",value:function(t){var r=e.byteArrayToChars(t);try{var n=p.a.decode(r);return r.length!==n.length&&(A()?self.setOption("attemptHighlight",!1):w()&&(window.app.options.attemptHighlight=!1)),n}catch(t){return r}}},{key:"byteArrayToChars",value:function(t){if(!t)return"";for(var e="",r=0;r<t.length;)e+=String.fromCharCode(t[r++]);return e}},{key:"arrayBufferToStr",value:function(t){var r=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=new Uint8Array(t);return r?e.byteArrayToUtf8(n):e.byteArrayToChars(n)}},{key:"parseCSV",value:function(t){var e,r,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[","],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:["\n","\r"],o=!1,a=!1,s="",u=[],f=[];t.length&&"\ufeff"===t[0]&&(t=t.substr(1));for(var c=0;c<t.length;c++)e=t[c],r=t[c+1]||"",o?(s+=e,o=!1):'"'!==e||a?'"'===e&&a?'"'===r?o=!0:a=!1:!a&&n.indexOf(e)>=0?(u.push(s),s=""):!a&&i.indexOf(e)>=0?(u.push(s),s="",f.push(u),u=[],i.indexOf(r)>=0&&r!==e&&c++):s+=e:a=!0;return u.length&&(u.push(s),f.push(u)),f}},{key:"stripHtmlTags",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return e&&(t=t.replace(/<(script|style)[^>]*>.*<\/(script|style)>/gim,"")),t.replace(/<[^>]+>/g,"")}},{key:"escapeHtml",value:function(t){var e={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","/":"&#x2F;","`":"&#x60;"};return t.replace(/[&<>"'\/`]/g,function(t){return e[t]})}},{key:"unescapeHtml",value:function(t){var e={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#x27;":"'","&#x2F;":"/","&#x60;":"`"};return t.replace(/&#?x?[a-z0-9]{2,4};/gi,function(t){return e[t]||t})}},{key:"encodeURIFragment",value:function(t){var e={"%2D":"-","%2E":".","%5F":"_","%7E":"~","%21":"!","%24":"$","%27":"'","%28":"(","%29":")","%2A":"*","%2C":",","%3B":";","%3A":":","%40":"@","%2F":"/","%3F":"?"};return(t=encodeURIComponent(t)).replace(/%[0-9A-F]{2}/g,function(t){return e[t]||t})}},{key:"generatePrettyRecipe",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r="",n="",i="",o="",a="";return t.forEach(function(t){n=t.op.replace(/ /g,"_"),i=JSON.stringify(t.args).slice(1,-1).replace(/'/g,"\\'").replace(/"((?:[^"\\]|\\.)*)"/g,"'$1'").replace(/\\"/g,'"'),o=t.disabled?"/disabled":"",a=t.breakpoint?"/breakpoint":"",r+=`${n}(${i}${o}${a})`,e&&(r+="\n")}),r}},{key:"parseRecipeConfig",value:function(t){if(0===(t=t.trim()).length)return[];if("["===t[0])return JSON.parse(t);var e,r;t=t.replace(/\n/g,"");for(var n=/([^(]+)\(((?:'[^'\\]*(?:\\.[^'\\]*)*'|[^)\/'])*)(\/[^)]+)?\)/g,i=[];e=n.exec(t);){r="["+(r=e[2].replace(/"/g,'\\"').replace(/(^|,|{|:)'/g,'$1"').replace(/([^\\]|(?:\\\\)+)'(,|:|}|$)/g,'$1"$2').replace(/\\'/g,"'"))+"]";var o={op:e[1].replace(/_/g," "),args:JSON.parse(r)};e[3]&&e[3].indexOf("disabled")>0&&(o.disabled=!0),e[3]&&e[3].indexOf("breakpoint")>0&&(o.breakpoint=!0),i.push(o)}return i}},{key:"displayFilesAsHTML",value:(r=s()(o.a.mark(function t(r){var n,i,a,u,f;return o.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:n=function(t){return`<div class='card' style='white-space: normal;'>\n                    <div class='card-header'>\n                        <h6 class="mb-0">\n                            ${e.escapeHtml(t.name)}\n                        </h6>\n                    </div>\n                </div>`},i=function(t,r){if(r.startsWith("image")){var n="data:";return n+=r+";","<img style='max-width: 100%;' src='"+(n+="base64,"+Object(d.b)(t))+"'>"}return`<pre>${e.escapeHtml(e.arrayBufferToStr(t.buffer))}</pre>`},a=function(){var t=s()(o.a.mark(function t(r,n){var a,s,u,f;return o.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.readFile(r);case 2:return a=t.sent,s=new Blob([a],{type:r.type||"octet/stream"}),u=URL.createObjectURL(s),f=`<div class='card' style='white-space: normal;'>\n                    <div class='card-header' id='heading${n}'>\n                        <h6 class='mb-0'>\n                            <a class='collapsed'\n                                data-toggle='collapse'\n                                href='#collapse${n}'\n                                aria-expanded='false'\n                                aria-controls='collapse${n}'\n                                title="Show/hide contents of '${e.escapeHtml(r.name)}'">\n                                ${e.escapeHtml(r.name)}</a>\n                            <span class='float-right' style="margin-top: -3px">\n                                ${r.size.toLocaleString()} bytes\n                                <a title="Download ${e.escapeHtml(r.name)}"\n                                    href="${u}"\n                                    download="${e.escapeHtml(r.name)}"\n                                    data-toggle="tooltip">\n                                    <i class="material-icons" style="vertical-align: bottom">save</i>\n                                </a>\n                                <a title="Move to input"\n                                    href="#"\n                                    blob-url="${u}"\n                                    file-name="${e.escapeHtml(r.name)}"\n                                    class="extract-file"\n                                    data-toggle="tooltip">\n                                    <i class="material-icons" style="vertical-align: bottom">open_in_browser</i>\n                                </a>\n                            </span>\n                        </h6>\n                    </div>\n                    <div id='collapse${n}' class='collapse' aria-labelledby='heading${n}' data-parent="#files">\n                        <div class='card-body'>\n                            ${i(a,r.type)}\n                        </div>\n                    </div>\n                </div>`,t.abrupt("return",f);case 7:case"end":return t.stop()}},t)}));return function(e,r){return t.apply(this,arguments)}}(),u=`<div style='padding: 5px; white-space: normal;'>\n                ${r.length} file(s) found\n            </div><div id="files" style="padding: 20px">`,f=0;case 5:if(!(f<r.length)){t.next=17;break}if(!r[f].name.endsWith("/")){t.next=10;break}u+=n(r[f]),t.next=14;break;case 10:return t.t0=u,t.next=13,a(r[f],f);case 13:u=t.t0+=t.sent;case 14:f++,t.next=5;break;case 17:return t.abrupt("return",u+="</div>");case 18:case"end":return t.stop()}},t)})),function(t){return r.apply(this,arguments)})},{key:"parseURIParams",value:function(t){if(""===t)return{};"?"!==t[0]&&"#"!==t[0]||(t=t.substr(1));for(var e=t.split("&"),r={},n=0;n<e.length;n++){var i=e[n].split("=");2!==i.length?r[e[n]]=!0:r[i[0]]=decodeURIComponent(i[1].replace(/\+/g," "))}return r}},{key:"readFile",value:function(e){return b()?t.from(e).buffer:new Promise(function(t,r){var n=new FileReader,i=new Uint8Array(e.size),o=0,a=function(){if(o>=e.size)t(i);else{var r=e.slice(o,o+10485760);n.readAsArrayBuffer(r)}};n.onload=function(t){i.set(new Uint8Array(n.result),o),o+=10485760,a()},n.onerror=function(t){r(n.error.message)},a()})}},{key:"readFileSync",value:function(t){if(!b())throw new TypeError("Browser environment cannot support readFileSync");return Uint8Array.from(t.data).buffer}},{key:"mod",value:function(t,e){return(t%e+e)%e}},{key:"gcd",value:function(t,r){return r?e.gcd(r,t%r):t}},{key:"modInv",value:function(t,e){t%=e;for(var r=1;r<e;r++)if(t*r%26==1)return r}},{key:"charRep",value:function(t){return{Space:" ",Percent:"%",Comma:",","Semi-colon":";",Colon:":",Tab:"\t","Line feed":"\n",CRLF:"\r\n","Forward slash":"/",Backslash:"\\","0x":"0x","\\x":"\\x","Nothing (separate chars)":"",None:""}[t]}},{key:"regexRep",value:function(t){return{Space:/\s+/g,Percent:/%/g,Comma:/,/g,"Semi-colon":/;/g,Colon:/:/g,"Line feed":/\n/g,CRLF:/\r\n/g,"Forward slash":/\//g,Backslash:/\\/g,"0x":/0x/g,"\\x":/\\x/g,None:/\s+/g}[t]}}]),e}();function b(){return void 0!==n&&null!=n.versions&&null!=n.versions.node}function w(){return"object"==typeof window}function A(){return"function"==typeof importScripts}function x(t){A()?self.sendStatusMessage(t):w()?app.alert(t,1e4):b()&&console.debug(t)}e.b=v,Array.prototype.unique=function(){for(var t={},e=[],r=0,n=this.length;r<n;r++)Object.prototype.hasOwnProperty.call(t,this[r])||(e.push(this[r]),t[this[r]]=1);return e},Array.prototype.max=function(){return Math.max.apply(null,this)},Array.prototype.min=function(){return Math.min.apply(null,this)},Array.prototype.sum=function(){return this.reduce(function(t,e){return t+e},0)},Array.prototype.equals=function(t){if(!t)return!1;var e=this.length;if(e!==t.length)return!1;for(;e--;)if(this[e]!==t[e])return!1;return!0},String.prototype.count=function(t){return this.split(t).length-1};var E={};function B(t,e,r,n,i){return function(){clearTimeout(E[r]),E[r]=setTimeout(function(){t.apply(n,i)},e)}}String.prototype.padStart||(String.prototype.padStart=function(t,e){return t>>=0,e=String(void 0!==e?e:" "),this.length>t?String(this):((t-=this.length)>e.length&&(e+=e.repeat(t/e.length)),e.slice(0,t)+String(this))}),String.prototype.padEnd||(String.prototype.padEnd=function(t,e){return t>>=0,e=String(void 0!==e?e:" "),this.length>t?String(this):((t-=this.length)>e.length&&(e+=e.repeat(t/e.length)),String(this)+e.slice(0,t))})}).call(this,r(12).Buffer,r(27))},1:function(t,e){t.exports=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}},10:function(t,e,r){"use strict";r.d(e,"a",function(){return s});var n=r(1),i=r.n(n),o=r(2),a=r.n(o),s=function(){function t(e){i()(this,t),this.bytes=e,this.length=this.bytes.length,this.position=0,this.bitPos=0}return a()(t,[{key:"getBytes",value:function(t){if(!(this.position>this.length)){var e=this.position+t,r=this.bytes.slice(this.position,e);return this.position=e,this.bitPos=0,r}}},{key:"readString",value:function(t){if(!(this.position>this.length)){for(var e="",r=this.position;r<this.position+t;r++){var n=this.bytes[r];if(0===n)break;e+=String.fromCharCode(n)}return this.position+=t,this.bitPos=0,e}}},{key:"readInt",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"be";if(!(this.position>this.length)){var r=0;if("be"===e)for(var n=this.position;n<this.position+t;n++)r<<=8,r|=this.bytes[n];else for(var i=this.position+t-1;i>=this.position;i--)r<<=8,r|=this.bytes[i];return this.position+=t,this.bitPos=0,r}}},{key:"readBits",value:function(t){if(!(this.position>this.length)){var e=0,r=0;for(e=(this.bytes[this.position++]&256-(1<<this.bitPos))>>>this.bitPos,r=8-this.bitPos,this.bitPos=0;r<t;)e|=this.bytes[this.position++]<<r,r+=8;if(r>t){var n=r-t;e&=(1<<t)-1,r-=n,this.position--,this.bitPos=8-n}return e}}},{key:"continueUntil",value:function(t){if(!(this.position>this.length))if(this.bitPos=0,"number"!=typeof t)for(var e=!1;!e&&this.position<this.length;){for(;++this.position<this.length&&this.bytes[this.position]!==t[0];);e=!0;for(var r=1;r<t.length;r++)(this.position+r>this.length||this.bytes[this.position+r]!==t[r])&&(e=!1)}else for(;++this.position<this.length&&this.bytes[this.position]!==t;);}},{key:"consumeIf",value:function(t){this.bytes[this.position]===t&&(this.position++,this.bitPos=0)}},{key:"moveForwardsBy",value:function(t){var e=this.position+t;if(e<0||e>this.length)throw new Error("Cannot move to position "+e+" in stream. Out of bounds.");this.position=e,this.bitPos=0}},{key:"moveBackwardsBy",value:function(t){var e=this.position-t;if(e<0||e>this.length)throw new Error("Cannot move to position "+e+" in stream. Out of bounds.");this.position=e,this.bitPos=0}},{key:"moveBackwardsByBits",value:function(t){if(t<=this.bitPos)this.bitPos-=t;else for(this.bitPos>0&&(t-=this.bitPos,this.bitPos=0);t>0;)this.moveBackwardsBy(1),this.bitPos=8,this.moveBackwardsByBits(t),t-=8}},{key:"moveTo",value:function(t){if(t<0||t>this.length)throw new Error("Cannot move to position "+t+" in stream. Out of bounds.");this.position=t,this.bitPos=0}},{key:"hasMore",value:function(){return this.position<this.length}},{key:"carve",value:function(){return this.bitPos>0&&this.position++,this.bytes.slice(0,this.position)}}]),t}()},104:function(t,e,r){"use strict";var n=r(626),i=r(1183),o=Object.prototype.toString;function a(t){return"[object Array]"===o.call(t)}function s(t){return null!==t&&"object"==typeof t}function u(t){return"[object Function]"===o.call(t)}function f(t,e){if(null!=t)if("object"!=typeof t&&(t=[t]),a(t))for(var r=0,n=t.length;r<n;r++)e.call(null,t[r],r,t);else for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.call(null,t[i],i,t)}t.exports={isArray:a,isArrayBuffer:function(t){return"[object ArrayBuffer]"===o.call(t)},isBuffer:i,isFormData:function(t){return"undefined"!=typeof FormData&&t instanceof FormData},isArrayBufferView:function(t){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer&&t.buffer instanceof ArrayBuffer},isString:function(t){return"string"==typeof t},isNumber:function(t){return"number"==typeof t},isObject:s,isUndefined:function(t){return void 0===t},isDate:function(t){return"[object Date]"===o.call(t)},isFile:function(t){return"[object File]"===o.call(t)},isBlob:function(t){return"[object Blob]"===o.call(t)},isFunction:u,isStream:function(t){return s(t)&&u(t.pipe)},isURLSearchParams:function(t){return"undefined"!=typeof URLSearchParams&&t instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product)&&("undefined"!=typeof window&&"undefined"!=typeof document)},forEach:f,merge:function t(){var e={};function r(r,n){"object"==typeof e[n]&&"object"==typeof r?e[n]=t(e[n],r):e[n]=r}for(var n=0,i=arguments.length;n<i;n++)f(arguments[n],r);return e},extend:function(t,e,r){return f(e,function(e,i){t[i]=r&&"function"==typeof e?n(e,r):e}),t},trim:function(t){return t.replace(/^\s*/,"").replace(/\s*$/,"")}}},11:function(t,e){function r(t,e,r,n,i,o,a){try{var s=t[o](a),u=s.value}catch(t){return void r(t)}s.done?e(u):Promise.resolve(u).then(n,i)}t.exports=function(t){return function(){var e=this,n=arguments;return new Promise(function(i,o){var a=t.apply(e,n);function s(t){r(a,i,o,s,u,"next",t)}function u(t){r(a,i,o,s,u,"throw",t)}s(void 0)})}}},1177:function(t,e,r){const n=r(1178);t.exports={loadLang:r(1180)({gunzip:r(1199),...n}),readImage:r(1201),cache:n}},1178:function(t,e,r){const{set:n,get:i,del:o}=r(1179);t.exports={readCache:i,writeCache:n,deleteCache:o,checkCache:t=>i(t).then(t=>void 0!==t)}},1179:function(t,e,r){"use strict";r.r(e),r.d(e,"Store",function(){return n}),r.d(e,"get",function(){return a}),r.d(e,"set",function(){return s}),r.d(e,"del",function(){return u}),r.d(e,"clear",function(){return f}),r.d(e,"keys",function(){return c});class n{constructor(t="keyval-store",e="keyval"){this.storeName=e,this._dbp=new Promise((r,n)=>{const i=indexedDB.open(t,1);i.onerror=()=>n(i.error),i.onsuccess=()=>r(i.result),i.onupgradeneeded=()=>{i.result.createObjectStore(e)}})}_withIDBStore(t,e){return this._dbp.then(r=>new Promise((n,i)=>{const o=r.transaction(this.storeName,t);o.oncomplete=()=>n(),o.onabort=o.onerror=()=>i(o.error),e(o.objectStore(this.storeName))}))}}let i;function o(){return i||(i=new n),i}function a(t,e=o()){let r;return e._withIDBStore("readonly",e=>{r=e.get(t)}).then(()=>r.result)}function s(t,e,r=o()){return r._withIDBStore("readwrite",r=>{r.put(e,t)})}function u(t,e=o()){return e._withIDBStore("readwrite",e=>{e.delete(t)})}function f(t=o()){return t._withIDBStore("readwrite",t=>{t.clear()})}function c(t=o()){const e=[];return t._withIDBStore("readonly",t=>{(t.openKeyCursor||t.openCursor).call(t).onsuccess=function(){this.result&&(e.push(this.result.key),this.result.continue())}}).then(()=>e)}},1180:function(t,e,r){const n=r(1181),i=r(624),o=r(625);t.exports=t=>({langs:e,...r})=>Promise.all(("string"==typeof e?e.split("+"):e).map((t=>({langPath:e,cachePath:r,cacheMethod:a,gzip:s=!0,...u})=>f=>{const c="string"==typeof f?f:f.code,h=e=>{const r=i(e);return null!==r&&"application/gzip"===r.mime?t.gunzip(new Uint8Array(e)):new Uint8Array(e)},l=(t=>({TessModule:e,dataPath:r,cachePath:n,cacheMethod:i,langCode:o})=>a=>{if(e){if(r)try{e.FS.mkdir(r)}catch(t){}e.FS.writeFile(`${r||"."}/${o}.traineddata`,a)}return["write","refresh",void 0].includes(i)?t.writeCache(`${n||"."}/${o}.traineddata`,a).then(()=>a):a})(t)({cachePath:r,cacheMethod:a,langCode:c,...u});let{readCache:p}=t;return["refresh","none"].includes(a)&&(p=()=>Promise.resolve()),p(`${r||"."}/${c}.traineddata`).then(t=>void 0===t?Promise.reject():l(t)).catch(()=>{if("string"==typeof f){const r=t=>o.get(`${t}/${c}.traineddata${s?".gz":""}`,{responseType:"arraybuffer"}).then(({data:t})=>new Uint8Array(t)).then(h).then(l);return n(e)||e.startsWith("chrome-extension://")?r(e):t.readCache(`${e}/${c}.traineddata${s?".gz":""}`).then(h).then(l)}return Promise.resolve(f.data).then(h).then(l)})})(t)(r)))},1181:function(t,e){t.exports=function(t){if("string"!=typeof t)return!1;var e=t.match(r);if(!e)return!1;var o=e[1];if(!o)return!1;if(n.test(o)||i.test(o))return!0;return!1};var r=/^(?:\w+:)?\/\/(\S+)$/,n=/^localhost[\:?\d]*(?:[^\:?\d]\S*)?$/,i=/^[^\s\.]+\.\S{2,}$/},1182:function(t,e,r){"use strict";var n=r(104),i=r(626),o=r(1184),a=r(392);function s(t){var e=new o(t),r=i(o.prototype.request,e);return n.extend(r,o.prototype,e),n.extend(r,e),r}var u=s(a);u.Axios=o,u.create=function(t){return s(n.merge(a,t))},u.Cancel=r(630),u.CancelToken=r(1197),u.isCancel=r(629),u.all=function(t){return Promise.all(t)},u.spread=r(1198),t.exports=u,t.exports.default=u},1183:function(t,e){
/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */
t.exports=function(t){return null!=t&&null!=t.constructor&&"function"==typeof t.constructor.isBuffer&&t.constructor.isBuffer(t)}},1184:function(t,e,r){"use strict";var n=r(392),i=r(104),o=r(1192),a=r(1193);function s(t){this.defaults=t,this.interceptors={request:new o,response:new o}}s.prototype.request=function(t){"string"==typeof t&&(t=i.merge({url:arguments[0]},arguments[1])),(t=i.merge(n,{method:"get"},this.defaults,t)).method=t.method.toLowerCase();var e=[a,void 0],r=Promise.resolve(t);for(this.interceptors.request.forEach(function(t){e.unshift(t.fulfilled,t.rejected)}),this.interceptors.response.forEach(function(t){e.push(t.fulfilled,t.rejected)});e.length;)r=r.then(e.shift(),e.shift());return r},i.forEach(["delete","get","head","options"],function(t){s.prototype[t]=function(e,r){return this.request(i.merge(r||{},{method:t,url:e}))}}),i.forEach(["post","put","patch"],function(t){s.prototype[t]=function(e,r,n){return this.request(i.merge(n||{},{method:t,url:e,data:r}))}}),t.exports=s},1185:function(t,e,r){"use strict";var n=r(104);t.exports=function(t,e){n.forEach(t,function(r,n){n!==e&&n.toUpperCase()===e.toUpperCase()&&(t[e]=r,delete t[n])})}},1186:function(t,e,r){"use strict";var n=r(628);t.exports=function(t,e,r){var i=r.config.validateStatus;r.status&&i&&!i(r.status)?e(n("Request failed with status code "+r.status,r.config,null,r.request,r)):t(r)}},1187:function(t,e,r){"use strict";t.exports=function(t,e,r,n,i){return t.config=e,r&&(t.code=r),t.request=n,t.response=i,t}},1188:function(t,e,r){"use strict";var n=r(104);function i(t){return encodeURIComponent(t).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}t.exports=function(t,e,r){if(!e)return t;var o;if(r)o=r(e);else if(n.isURLSearchParams(e))o=e.toString();else{var a=[];n.forEach(e,function(t,e){null!=t&&(n.isArray(t)?e+="[]":t=[t],n.forEach(t,function(t){n.isDate(t)?t=t.toISOString():n.isObject(t)&&(t=JSON.stringify(t)),a.push(i(e)+"="+i(t))}))}),o=a.join("&")}return o&&(t+=(-1===t.indexOf("?")?"?":"&")+o),t}},1189:function(t,e,r){"use strict";var n=r(104),i=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];t.exports=function(t){var e,r,o,a={};return t?(n.forEach(t.split("\n"),function(t){if(o=t.indexOf(":"),e=n.trim(t.substr(0,o)).toLowerCase(),r=n.trim(t.substr(o+1)),e){if(a[e]&&i.indexOf(e)>=0)return;a[e]="set-cookie"===e?(a[e]?a[e]:[]).concat([r]):a[e]?a[e]+", "+r:r}}),a):a}},1190:function(t,e,r){"use strict";var n=r(104);t.exports=n.isStandardBrowserEnv()?function(){var t,e=/(msie|trident)/i.test(navigator.userAgent),r=document.createElement("a");function i(t){var n=t;return e&&(r.setAttribute("href",n),n=r.href),r.setAttribute("href",n),{href:r.href,protocol:r.protocol?r.protocol.replace(/:$/,""):"",host:r.host,search:r.search?r.search.replace(/^\?/,""):"",hash:r.hash?r.hash.replace(/^#/,""):"",hostname:r.hostname,port:r.port,pathname:"/"===r.pathname.charAt(0)?r.pathname:"/"+r.pathname}}return t=i(window.location.href),function(e){var r=n.isString(e)?i(e):e;return r.protocol===t.protocol&&r.host===t.host}}():function(){return!0}},1191:function(t,e,r){"use strict";var n=r(104);t.exports=n.isStandardBrowserEnv()?{write:function(t,e,r,i,o,a){var s=[];s.push(t+"="+encodeURIComponent(e)),n.isNumber(r)&&s.push("expires="+new Date(r).toGMTString()),n.isString(i)&&s.push("path="+i),n.isString(o)&&s.push("domain="+o),!0===a&&s.push("secure"),document.cookie=s.join("; ")},read:function(t){var e=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove:function(t){this.write(t,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},1192:function(t,e,r){"use strict";var n=r(104);function i(){this.handlers=[]}i.prototype.use=function(t,e){return this.handlers.push({fulfilled:t,rejected:e}),this.handlers.length-1},i.prototype.eject=function(t){this.handlers[t]&&(this.handlers[t]=null)},i.prototype.forEach=function(t){n.forEach(this.handlers,function(e){null!==e&&t(e)})},t.exports=i},1193:function(t,e,r){"use strict";var n=r(104),i=r(1194),o=r(629),a=r(392),s=r(1195),u=r(1196);function f(t){t.cancelToken&&t.cancelToken.throwIfRequested()}t.exports=function(t){return f(t),t.baseURL&&!s(t.url)&&(t.url=u(t.baseURL,t.url)),t.headers=t.headers||{},t.data=i(t.data,t.headers,t.transformRequest),t.headers=n.merge(t.headers.common||{},t.headers[t.method]||{},t.headers||{}),n.forEach(["delete","get","head","post","put","patch","common"],function(e){delete t.headers[e]}),(t.adapter||a.adapter)(t).then(function(e){return f(t),e.data=i(e.data,e.headers,t.transformResponse),e},function(e){return o(e)||(f(t),e&&e.response&&(e.response.data=i(e.response.data,e.response.headers,t.transformResponse))),Promise.reject(e)})}},1194:function(t,e,r){"use strict";var n=r(104);t.exports=function(t,e,r){return n.forEach(r,function(r){t=r(t,e)}),t}},1195:function(t,e,r){"use strict";t.exports=function(t){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(t)}},1196:function(t,e,r){"use strict";t.exports=function(t,e){return e?t.replace(/\/+$/,"")+"/"+e.replace(/^\/+/,""):t}},1197:function(t,e,r){"use strict";var n=r(630);function i(t){if("function"!=typeof t)throw new TypeError("executor must be a function.");var e;this.promise=new Promise(function(t){e=t});var r=this;t(function(t){r.reason||(r.reason=new n(t),e(r.reason))})}i.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},i.source=function(){var t;return{token:new i(function(e){t=e}),cancel:t}},t.exports=i},1198:function(t,e,r){"use strict";t.exports=function(t){return function(e){return t.apply(null,e)}}},1199:function(t,e,r){t.exports=r(1200).gunzipSync},12:function(t,e,r){"use strict";(function(t){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <<EMAIL>> <http://feross.org>
 * @license  MIT
 */
var n=r(45),i=r(46),o=r(47);function a(){return u.TYPED_ARRAY_SUPPORT?**********:**********}function s(t,e){if(a()<e)throw new RangeError("Invalid typed array length");return u.TYPED_ARRAY_SUPPORT?(t=new Uint8Array(e)).__proto__=u.prototype:(null===t&&(t=new u(e)),t.length=e),t}function u(t,e,r){if(!(u.TYPED_ARRAY_SUPPORT||this instanceof u))return new u(t,e,r);if("number"==typeof t){if("string"==typeof e)throw new Error("If encoding is specified then the first argument must be a string");return h(this,t)}return f(this,t,e,r)}function f(t,e,r,n){if("number"==typeof e)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer?function(t,e,r,n){if(e.byteLength,r<0||e.byteLength<r)throw new RangeError("'offset' is out of bounds");if(e.byteLength<r+(n||0))throw new RangeError("'length' is out of bounds");e=void 0===r&&void 0===n?new Uint8Array(e):void 0===n?new Uint8Array(e,r):new Uint8Array(e,r,n);u.TYPED_ARRAY_SUPPORT?(t=e).__proto__=u.prototype:t=l(t,e);return t}(t,e,r,n):"string"==typeof e?function(t,e,r){"string"==typeof r&&""!==r||(r="utf8");if(!u.isEncoding(r))throw new TypeError('"encoding" must be a valid string encoding');var n=0|d(e,r),i=(t=s(t,n)).write(e,r);i!==n&&(t=t.slice(0,i));return t}(t,e,r):function(t,e){if(u.isBuffer(e)){var r=0|p(e.length);return 0===(t=s(t,r)).length?t:(e.copy(t,0,0,r),t)}if(e){if("undefined"!=typeof ArrayBuffer&&e.buffer instanceof ArrayBuffer||"length"in e)return"number"!=typeof e.length||(n=e.length)!=n?s(t,0):l(t,e);if("Buffer"===e.type&&o(e.data))return l(t,e.data)}var n;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(t,e)}function c(t){if("number"!=typeof t)throw new TypeError('"size" argument must be a number');if(t<0)throw new RangeError('"size" argument must not be negative')}function h(t,e){if(c(e),t=s(t,e<0?0:0|p(e)),!u.TYPED_ARRAY_SUPPORT)for(var r=0;r<e;++r)t[r]=0;return t}function l(t,e){var r=e.length<0?0:0|p(e.length);t=s(t,r);for(var n=0;n<r;n+=1)t[n]=255&e[n];return t}function p(t){if(t>=a())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+a().toString(16)+" bytes");return 0|t}function d(t,e){if(u.isBuffer(t))return t.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;"string"!=typeof t&&(t=""+t);var r=t.length;if(0===r)return 0;for(var n=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return z(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return $(t).length;default:if(n)return z(t).length;e=(""+e).toLowerCase(),n=!0}}function m(t,e,r){var n=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(e>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return T(this,e,r);case"utf8":case"utf-8":return k(this,e,r);case"ascii":return C(this,e,r);case"latin1":case"binary":return S(this,e,r);case"base64":return F(this,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return U(this,e,r);default:if(n)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),n=!0}}function g(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function y(t,e,r,n,i){if(0===t.length)return-1;if("string"==typeof r?(n=r,r=0):r>**********?r=**********:r<-2147483648&&(r=-2147483648),r=+r,isNaN(r)&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length){if(i)return-1;r=t.length-1}else if(r<0){if(!i)return-1;r=0}if("string"==typeof e&&(e=u.from(e,n)),u.isBuffer(e))return 0===e.length?-1:v(t,e,r,n,i);if("number"==typeof e)return e&=255,u.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(t,e,r):Uint8Array.prototype.lastIndexOf.call(t,e,r):v(t,[e],r,n,i);throw new TypeError("val must be string, number or Buffer")}function v(t,e,r,n,i){var o,a=1,s=t.length,u=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return-1;a=2,s/=2,u/=2,r/=2}function f(t,e){return 1===a?t[e]:t.readUInt16BE(e*a)}if(i){var c=-1;for(o=r;o<s;o++)if(f(t,o)===f(e,-1===c?0:o-c)){if(-1===c&&(c=o),o-c+1===u)return c*a}else-1!==c&&(o-=o-c),c=-1}else for(r+u>s&&(r=s-u),o=r;o>=0;o--){for(var h=!0,l=0;l<u;l++)if(f(t,o+l)!==f(e,l)){h=!1;break}if(h)return o}return-1}function b(t,e,r,n){r=Number(r)||0;var i=t.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=e.length;if(o%2!=0)throw new TypeError("Invalid hex string");n>o/2&&(n=o/2);for(var a=0;a<n;++a){var s=parseInt(e.substr(2*a,2),16);if(isNaN(s))return a;t[r+a]=s}return a}function w(t,e,r,n){return Y(z(e,t.length-r),t,r,n)}function A(t,e,r,n){return Y(function(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}(e),t,r,n)}function x(t,e,r,n){return A(t,e,r,n)}function E(t,e,r,n){return Y($(e),t,r,n)}function B(t,e,r,n){return Y(function(t,e){for(var r,n,i,o=[],a=0;a<t.length&&!((e-=2)<0);++a)r=t.charCodeAt(a),n=r>>8,i=r%256,o.push(i),o.push(n);return o}(e,t.length-r),t,r,n)}function F(t,e,r){return 0===e&&r===t.length?n.fromByteArray(t):n.fromByteArray(t.slice(e,r))}function k(t,e,r){r=Math.min(t.length,r);for(var n=[],i=e;i<r;){var o,a,s,u,f=t[i],c=null,h=f>239?4:f>223?3:f>191?2:1;if(i+h<=r)switch(h){case 1:f<128&&(c=f);break;case 2:128==(192&(o=t[i+1]))&&(u=(31&f)<<6|63&o)>127&&(c=u);break;case 3:o=t[i+1],a=t[i+2],128==(192&o)&&128==(192&a)&&(u=(15&f)<<12|(63&o)<<6|63&a)>2047&&(u<55296||u>57343)&&(c=u);break;case 4:o=t[i+1],a=t[i+2],s=t[i+3],128==(192&o)&&128==(192&a)&&128==(192&s)&&(u=(15&f)<<18|(63&o)<<12|(63&a)<<6|63&s)>65535&&u<1114112&&(c=u)}null===c?(c=65533,h=1):c>65535&&(c-=65536,n.push(c>>>10&1023|55296),c=56320|1023&c),n.push(c),i+=h}return function(t){var e=t.length;if(e<=_)return String.fromCharCode.apply(String,t);var r="",n=0;for(;n<e;)r+=String.fromCharCode.apply(String,t.slice(n,n+=_));return r}(n)}e.Buffer=u,e.SlowBuffer=function(t){+t!=t&&(t=0);return u.alloc(+t)},e.INSPECT_MAX_BYTES=50,u.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:function(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()&&"function"==typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(t){return!1}}(),e.kMaxLength=a(),u.poolSize=8192,u._augment=function(t){return t.__proto__=u.prototype,t},u.from=function(t,e,r){return f(null,t,e,r)},u.TYPED_ARRAY_SUPPORT&&(u.prototype.__proto__=Uint8Array.prototype,u.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&u[Symbol.species]===u&&Object.defineProperty(u,Symbol.species,{value:null,configurable:!0})),u.alloc=function(t,e,r){return function(t,e,r,n){return c(e),e<=0?s(t,e):void 0!==r?"string"==typeof n?s(t,e).fill(r,n):s(t,e).fill(r):s(t,e)}(null,t,e,r)},u.allocUnsafe=function(t){return h(null,t)},u.allocUnsafeSlow=function(t){return h(null,t)},u.isBuffer=function(t){return!(null==t||!t._isBuffer)},u.compare=function(t,e){if(!u.isBuffer(t)||!u.isBuffer(e))throw new TypeError("Arguments must be Buffers");if(t===e)return 0;for(var r=t.length,n=e.length,i=0,o=Math.min(r,n);i<o;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:n<r?1:0},u.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},u.concat=function(t,e){if(!o(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return u.alloc(0);var r;if(void 0===e)for(e=0,r=0;r<t.length;++r)e+=t[r].length;var n=u.allocUnsafe(e),i=0;for(r=0;r<t.length;++r){var a=t[r];if(!u.isBuffer(a))throw new TypeError('"list" argument must be an Array of Buffers');a.copy(n,i),i+=a.length}return n},u.byteLength=d,u.prototype._isBuffer=!0,u.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)g(this,e,e+1);return this},u.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)g(this,e,e+3),g(this,e+1,e+2);return this},u.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)g(this,e,e+7),g(this,e+1,e+6),g(this,e+2,e+5),g(this,e+3,e+4);return this},u.prototype.toString=function(){var t=0|this.length;return 0===t?"":0===arguments.length?k(this,0,t):m.apply(this,arguments)},u.prototype.equals=function(t){if(!u.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===u.compare(this,t)},u.prototype.inspect=function(){var t="",r=e.INSPECT_MAX_BYTES;return this.length>0&&(t=this.toString("hex",0,r).match(/.{2}/g).join(" "),this.length>r&&(t+=" ... ")),"<Buffer "+t+">"},u.prototype.compare=function(t,e,r,n,i){if(!u.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),e<0||r>t.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&e>=r)return 0;if(n>=i)return-1;if(e>=r)return 1;if(this===t)return 0;for(var o=(i>>>=0)-(n>>>=0),a=(r>>>=0)-(e>>>=0),s=Math.min(o,a),f=this.slice(n,i),c=t.slice(e,r),h=0;h<s;++h)if(f[h]!==c[h]){o=f[h],a=c[h];break}return o<a?-1:a<o?1:0},u.prototype.includes=function(t,e,r){return-1!==this.indexOf(t,e,r)},u.prototype.indexOf=function(t,e,r){return y(this,t,e,r,!0)},u.prototype.lastIndexOf=function(t,e,r){return y(this,t,e,r,!1)},u.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e|=0,isFinite(r)?(r|=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var i=this.length-e;if((void 0===r||r>i)&&(r=i),t.length>0&&(r<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var o=!1;;)switch(n){case"hex":return b(this,t,e,r);case"utf8":case"utf-8":return w(this,t,e,r);case"ascii":return A(this,t,e,r);case"latin1":case"binary":return x(this,t,e,r);case"base64":return E(this,t,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return B(this,t,e,r);default:if(o)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),o=!0}},u.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var _=4096;function C(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}function S(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}function T(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=e;o<r;++o)i+=M(t[o]);return i}function U(t,e,r){for(var n=t.slice(e,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}function I(t,e,r){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+e>r)throw new RangeError("Trying to access beyond buffer length")}function R(t,e,r,n,i,o){if(!u.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw new RangeError('"value" argument is out of bounds');if(r+n>t.length)throw new RangeError("Index out of range")}function O(t,e,r,n){e<0&&(e=65535+e+1);for(var i=0,o=Math.min(t.length-r,2);i<o;++i)t[r+i]=(e&255<<8*(n?i:1-i))>>>8*(n?i:1-i)}function L(t,e,r,n){e<0&&(e=4294967295+e+1);for(var i=0,o=Math.min(t.length-r,4);i<o;++i)t[r+i]=e>>>8*(n?i:3-i)&255}function P(t,e,r,n,i,o){if(r+n>t.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function D(t,e,r,n,o){return o||P(t,0,r,4),i.write(t,e,r,n,23,4),r+4}function j(t,e,r,n,o){return o||P(t,0,r,8),i.write(t,e,r,n,52,8),r+8}u.prototype.slice=function(t,e){var r,n=this.length;if((t=~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),(e=void 0===e?n:~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),e<t&&(e=t),u.TYPED_ARRAY_SUPPORT)(r=this.subarray(t,e)).__proto__=u.prototype;else{var i=e-t;r=new u(i,void 0);for(var o=0;o<i;++o)r[o]=this[o+t]}return r},u.prototype.readUIntLE=function(t,e,r){t|=0,e|=0,r||I(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n},u.prototype.readUIntBE=function(t,e,r){t|=0,e|=0,r||I(t,e,this.length);for(var n=this[t+--e],i=1;e>0&&(i*=256);)n+=this[t+--e]*i;return n},u.prototype.readUInt8=function(t,e){return e||I(t,1,this.length),this[t]},u.prototype.readUInt16LE=function(t,e){return e||I(t,2,this.length),this[t]|this[t+1]<<8},u.prototype.readUInt16BE=function(t,e){return e||I(t,2,this.length),this[t]<<8|this[t+1]},u.prototype.readUInt32LE=function(t,e){return e||I(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},u.prototype.readUInt32BE=function(t,e){return e||I(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},u.prototype.readIntLE=function(t,e,r){t|=0,e|=0,r||I(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*e)),n},u.prototype.readIntBE=function(t,e,r){t|=0,e|=0,r||I(t,e,this.length);for(var n=e,i=1,o=this[t+--n];n>0&&(i*=256);)o+=this[t+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*e)),o},u.prototype.readInt8=function(t,e){return e||I(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},u.prototype.readInt16LE=function(t,e){e||I(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt16BE=function(t,e){e||I(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt32LE=function(t,e){return e||I(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},u.prototype.readInt32BE=function(t,e){return e||I(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},u.prototype.readFloatLE=function(t,e){return e||I(t,4,this.length),i.read(this,t,!0,23,4)},u.prototype.readFloatBE=function(t,e){return e||I(t,4,this.length),i.read(this,t,!1,23,4)},u.prototype.readDoubleLE=function(t,e){return e||I(t,8,this.length),i.read(this,t,!0,52,8)},u.prototype.readDoubleBE=function(t,e){return e||I(t,8,this.length),i.read(this,t,!1,52,8)},u.prototype.writeUIntLE=function(t,e,r,n){(t=+t,e|=0,r|=0,n)||R(this,t,e,r,Math.pow(2,8*r)-1,0);var i=1,o=0;for(this[e]=255&t;++o<r&&(i*=256);)this[e+o]=t/i&255;return e+r},u.prototype.writeUIntBE=function(t,e,r,n){(t=+t,e|=0,r|=0,n)||R(this,t,e,r,Math.pow(2,8*r)-1,0);var i=r-1,o=1;for(this[e+i]=255&t;--i>=0&&(o*=256);)this[e+i]=t/o&255;return e+r},u.prototype.writeUInt8=function(t,e,r){return t=+t,e|=0,r||R(this,t,e,1,255,0),u.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[e]=255&t,e+1},u.prototype.writeUInt16LE=function(t,e,r){return t=+t,e|=0,r||R(this,t,e,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):O(this,t,e,!0),e+2},u.prototype.writeUInt16BE=function(t,e,r){return t=+t,e|=0,r||R(this,t,e,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):O(this,t,e,!1),e+2},u.prototype.writeUInt32LE=function(t,e,r){return t=+t,e|=0,r||R(this,t,e,4,4294967295,0),u.TYPED_ARRAY_SUPPORT?(this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t):L(this,t,e,!0),e+4},u.prototype.writeUInt32BE=function(t,e,r){return t=+t,e|=0,r||R(this,t,e,4,4294967295,0),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):L(this,t,e,!1),e+4},u.prototype.writeIntLE=function(t,e,r,n){if(t=+t,e|=0,!n){var i=Math.pow(2,8*r-1);R(this,t,e,r,i-1,-i)}var o=0,a=1,s=0;for(this[e]=255&t;++o<r&&(a*=256);)t<0&&0===s&&0!==this[e+o-1]&&(s=1),this[e+o]=(t/a>>0)-s&255;return e+r},u.prototype.writeIntBE=function(t,e,r,n){if(t=+t,e|=0,!n){var i=Math.pow(2,8*r-1);R(this,t,e,r,i-1,-i)}var o=r-1,a=1,s=0;for(this[e+o]=255&t;--o>=0&&(a*=256);)t<0&&0===s&&0!==this[e+o+1]&&(s=1),this[e+o]=(t/a>>0)-s&255;return e+r},u.prototype.writeInt8=function(t,e,r){return t=+t,e|=0,r||R(this,t,e,1,127,-128),u.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),t<0&&(t=255+t+1),this[e]=255&t,e+1},u.prototype.writeInt16LE=function(t,e,r){return t=+t,e|=0,r||R(this,t,e,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):O(this,t,e,!0),e+2},u.prototype.writeInt16BE=function(t,e,r){return t=+t,e|=0,r||R(this,t,e,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):O(this,t,e,!1),e+2},u.prototype.writeInt32LE=function(t,e,r){return t=+t,e|=0,r||R(this,t,e,4,**********,-2147483648),u.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24):L(this,t,e,!0),e+4},u.prototype.writeInt32BE=function(t,e,r){return t=+t,e|=0,r||R(this,t,e,4,**********,-2147483648),t<0&&(t=4294967295+t+1),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):L(this,t,e,!1),e+4},u.prototype.writeFloatLE=function(t,e,r){return D(this,t,e,!0,r)},u.prototype.writeFloatBE=function(t,e,r){return D(this,t,e,!1,r)},u.prototype.writeDoubleLE=function(t,e,r){return j(this,t,e,!0,r)},u.prototype.writeDoubleBE=function(t,e,r){return j(this,t,e,!1,r)},u.prototype.copy=function(t,e,r,n){if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var i,o=n-r;if(this===t&&r<e&&e<n)for(i=o-1;i>=0;--i)t[i+e]=this[i+r];else if(o<1e3||!u.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)t[i+e]=this[i+r];else Uint8Array.prototype.set.call(t,this.subarray(r,r+o),e);return o},u.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),1===t.length){var i=t.charCodeAt(0);i<256&&(t=i)}if(void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!u.isEncoding(n))throw new TypeError("Unknown encoding: "+n)}else"number"==typeof t&&(t&=255);if(e<0||this.length<e||this.length<r)throw new RangeError("Out of range index");if(r<=e)return this;var o;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(o=e;o<r;++o)this[o]=t;else{var a=u.isBuffer(t)?t:z(new u(t,n).toString()),s=a.length;for(o=0;o<r-e;++o)this[o+e]=a[o%s]}return this};var N=/[^+\/0-9A-Za-z-_]/g;function M(t){return t<16?"0"+t.toString(16):t.toString(16)}function z(t,e){var r;e=e||1/0;for(var n=t.length,i=null,o=[],a=0;a<n;++a){if((r=t.charCodeAt(a))>55295&&r<57344){if(!i){if(r>56319){(e-=3)>-1&&o.push(239,191,189);continue}if(a+1===n){(e-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(e-=3)>-1&&o.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;o.push(r)}else if(r<2048){if((e-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function $(t){return n.toByteArray(function(t){if((t=function(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}(t).replace(N,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function Y(t,e,r,n){for(var i=0;i<n&&!(i+r>=e.length||i>=t.length);++i)e[i+r]=t[i];return i}}).call(this,r(28))},1200:function(t,e,r){(function(t,r){/** @license zlib.js 2012 - imaya [ https://github.com/imaya/zlib.js ] The MIT License */(function(){"use strict";function n(t){throw t}var i=void 0,o=!0,a="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Uint32Array&&"undefined"!=typeof DataView;function s(t,e){this.index="number"==typeof e?e:0,this.m=0,this.buffer=t instanceof(a?Uint8Array:Array)?t:new(a?Uint8Array:Array)(32768),2*this.buffer.length<=this.index&&n(Error("invalid index")),this.buffer.length<=this.index&&this.f()}s.prototype.f=function(){var t,e=this.buffer,r=e.length,n=new(a?Uint8Array:Array)(r<<1);if(a)n.set(e);else for(t=0;t<r;++t)n[t]=e[t];return this.buffer=n},s.prototype.d=function(t,e,r){var n,i=this.buffer,o=this.index,a=this.m,s=i[o];if(r&&1<e&&(t=8<e?(p[255&t]<<24|p[t>>>8&255]<<16|p[t>>>16&255]<<8|p[t>>>24&255])>>32-e:p[t]>>8-e),8>e+a)s=s<<e|t,a+=e;else for(n=0;n<e;++n)s=s<<1|t>>e-n-1&1,8==++a&&(a=0,i[o++]=p[s],s=0,o===i.length&&(i=this.f()));i[o]=s,this.buffer=i,this.m=a,this.index=o},s.prototype.finish=function(){var t,e=this.buffer,r=this.index;return 0<this.m&&(e[r]<<=8-this.m,e[r]=p[e[r]],r++),a?t=e.subarray(0,r):(e.length=r,t=e),t};var u,f=new(a?Uint8Array:Array)(256);for(u=0;256>u;++u){for(var c=l=u,h=7,l=l>>>1;l;l>>>=1)c<<=1,c|=1&l,--h;f[u]=(c<<h&255)>>>0}var p=f;function d(t,e,r){var n,i="number"==typeof e?e:e=0,o="number"==typeof r?r:t.length;for(n=-1,i=7&o;i--;++e)n=n>>>8^g[255&(n^t[e])];for(i=o>>3;i--;e+=8)n=(n=(n=(n=(n=(n=(n=(n=n>>>8^g[255&(n^t[e])])>>>8^g[255&(n^t[e+1])])>>>8^g[255&(n^t[e+2])])>>>8^g[255&(n^t[e+3])])>>>8^g[255&(n^t[e+4])])>>>8^g[255&(n^t[e+5])])>>>8^g[255&(n^t[e+6])])>>>8^g[255&(n^t[e+7])];return(4294967295^n)>>>0}var m=[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918e3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117],g=a?new Uint32Array(m):m;function y(){}function v(t){this.buffer=new(a?Uint16Array:Array)(2*t),this.length=0}function b(t){var e,r,n,i,o,s,u,f,c,h,l=t.length,p=0,d=Number.POSITIVE_INFINITY;for(f=0;f<l;++f)t[f]>p&&(p=t[f]),t[f]<d&&(d=t[f]);for(e=1<<p,r=new(a?Uint32Array:Array)(e),n=1,i=0,o=2;n<=p;){for(f=0;f<l;++f)if(t[f]===n){for(s=0,u=i,c=0;c<n;++c)s=s<<1|1&u,u>>=1;for(h=n<<16|f,c=s;c<e;c+=o)r[c]=h;++i}++n,i<<=1,o<<=1}return[r,p,d]}function w(t,e){this.k=x,this.F=0,this.input=a&&t instanceof Array?new Uint8Array(t):t,this.b=0,e&&(e.lazy&&(this.F=e.lazy),"number"==typeof e.compressionType&&(this.k=e.compressionType),e.outputBuffer&&(this.a=a&&e.outputBuffer instanceof Array?new Uint8Array(e.outputBuffer):e.outputBuffer),"number"==typeof e.outputIndex&&(this.b=e.outputIndex)),this.a||(this.a=new(a?Uint8Array:Array)(32768))}v.prototype.getParent=function(t){return 2*((t-2)/4|0)},v.prototype.push=function(t,e){var r,n,i,o=this.buffer;for(r=this.length,o[this.length++]=e,o[this.length++]=t;0<r&&(n=this.getParent(r),o[r]>o[n]);)i=o[r],o[r]=o[n],o[n]=i,i=o[r+1],o[r+1]=o[n+1],o[n+1]=i,r=n;return this.length},v.prototype.pop=function(){var t,e,r,n,i,o=this.buffer;for(e=o[0],t=o[1],this.length-=2,o[0]=o[this.length],o[1]=o[this.length+1],i=0;!((n=2*i+2)>=this.length)&&(n+2<this.length&&o[n+2]>o[n]&&(n+=2),o[n]>o[i]);)r=o[i],o[i]=o[n],o[n]=r,r=o[i+1],o[i+1]=o[n+1],o[n+1]=r,i=n;return{index:t,value:e,length:this.length}};var A,x=2,E={NONE:0,L:1,t:x,X:3},B=[];for(A=0;288>A;A++)switch(o){case 143>=A:B.push([A+48,8]);break;case 255>=A:B.push([A-144+400,9]);break;case 279>=A:B.push([A-256+0,7]);break;case 287>=A:B.push([A-280+192,8]);break;default:n("invalid literal: "+A)}function F(t,e){this.length=t,this.N=e}w.prototype.h=function(){var t,e,r,u,f=this.input;switch(this.k){case 0:for(r=0,u=f.length;r<u;){var c,h,l,p=e=a?f.subarray(r,r+65535):f.slice(r,r+65535),d=(r+=e.length)===u,m=i,g=i,y=this.a,v=this.b;if(a){for(y=new Uint8Array(this.a.buffer);y.length<=v+p.length+5;)y=new Uint8Array(y.length<<1);y.set(this.a)}if(c=d?1:0,y[v++]=0|c,l=65536+~(h=p.length)&65535,y[v++]=255&h,y[v++]=h>>>8&255,y[v++]=255&l,y[v++]=l>>>8&255,a)y.set(p,v),v+=p.length,y=y.subarray(0,v);else{for(m=0,g=p.length;m<g;++m)y[v++]=p[m];y.length=v}this.b=v,this.a=y}break;case 1:var b=new s(a?new Uint8Array(this.a.buffer):this.a,this.b);b.d(1,1,o),b.d(1,2,o);var w,A,E,F=C(this,f);for(w=0,A=F.length;w<A;w++)if(E=F[w],s.prototype.d.apply(b,B[E]),256<E)b.d(F[++w],F[++w],o),b.d(F[++w],5),b.d(F[++w],F[++w],o);else if(256===E)break;this.a=b.finish(),this.b=this.a.length;break;case x:var k,_,S,I,R,O,L,P,D,j,N,M,z,$,Y,G=new s(a?new Uint8Array(this.a.buffer):this.a,this.b),q=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],V=Array(19);for(k=x,G.d(1,1,o),G.d(k,2,o),_=C(this,f),L=U(O=T(this.U,15)),D=U(P=T(this.T,7)),S=286;257<S&&0===O[S-1];S--);for(I=30;1<I&&0===P[I-1];I--);var H,J,W,X,Z,K,Q=S,tt=I,et=new(a?Uint32Array:Array)(Q+tt),rt=new(a?Uint32Array:Array)(316),nt=new(a?Uint8Array:Array)(19);for(H=J=0;H<Q;H++)et[J++]=O[H];for(H=0;H<tt;H++)et[J++]=P[H];if(!a)for(H=0,X=nt.length;H<X;++H)nt[H]=0;for(H=Z=0,X=et.length;H<X;H+=J){for(J=1;H+J<X&&et[H+J]===et[H];++J);if(W=J,0===et[H])if(3>W)for(;0<W--;)rt[Z++]=0,nt[0]++;else for(;0<W;)(K=138>W?W:138)>W-3&&K<W&&(K=W-3),10>=K?(rt[Z++]=17,rt[Z++]=K-3,nt[17]++):(rt[Z++]=18,rt[Z++]=K-11,nt[18]++),W-=K;else if(rt[Z++]=et[H],nt[et[H]]++,3>--W)for(;0<W--;)rt[Z++]=et[H],nt[et[H]]++;else for(;0<W;)(K=6>W?W:6)>W-3&&K<W&&(K=W-3),rt[Z++]=16,rt[Z++]=K-3,nt[16]++,W-=K}for(t=a?rt.subarray(0,Z):rt.slice(0,Z),j=T(nt,7),$=0;19>$;$++)V[$]=j[q[$]];for(R=19;4<R&&0===V[R-1];R--);for(N=U(j),G.d(S-257,5,o),G.d(I-1,5,o),G.d(R-4,4,o),$=0;$<R;$++)G.d(V[$],3,o);for($=0,Y=t.length;$<Y;$++)if(M=t[$],G.d(N[M],j[M],o),16<=M){switch($++,M){case 16:z=2;break;case 17:z=3;break;case 18:z=7;break;default:n("invalid code: "+M)}G.d(t[$],z,o)}var it,ot,at,st,ut,ft,ct,ht,lt=[L,O],pt=[D,P];for(ut=lt[0],ft=lt[1],ct=pt[0],ht=pt[1],it=0,ot=_.length;it<ot;++it)if(at=_[it],G.d(ut[at],ft[at],o),256<at)G.d(_[++it],_[++it],o),st=_[++it],G.d(ct[st],ht[st],o),G.d(_[++it],_[++it],o);else if(256===at)break;this.a=G.finish(),this.b=this.a.length;break;default:n("invalid compression type")}return this.a};var k=function(){function t(t){switch(o){case 3===t:return[257,t-3,0];case 4===t:return[258,t-4,0];case 5===t:return[259,t-5,0];case 6===t:return[260,t-6,0];case 7===t:return[261,t-7,0];case 8===t:return[262,t-8,0];case 9===t:return[263,t-9,0];case 10===t:return[264,t-10,0];case 12>=t:return[265,t-11,1];case 14>=t:return[266,t-13,1];case 16>=t:return[267,t-15,1];case 18>=t:return[268,t-17,1];case 22>=t:return[269,t-19,2];case 26>=t:return[270,t-23,2];case 30>=t:return[271,t-27,2];case 34>=t:return[272,t-31,2];case 42>=t:return[273,t-35,3];case 50>=t:return[274,t-43,3];case 58>=t:return[275,t-51,3];case 66>=t:return[276,t-59,3];case 82>=t:return[277,t-67,4];case 98>=t:return[278,t-83,4];case 114>=t:return[279,t-99,4];case 130>=t:return[280,t-115,4];case 162>=t:return[281,t-131,5];case 194>=t:return[282,t-163,5];case 226>=t:return[283,t-195,5];case 257>=t:return[284,t-227,5];case 258===t:return[285,t-258,0];default:n("invalid length: "+t)}}var e,r,i=[];for(e=3;258>=e;e++)r=t(e),i[e]=r[2]<<24|r[1]<<16|r[0];return i}(),_=a?new Uint32Array(k):k;function C(t,e){function r(t,e){var r,i,a,s,u=t.N,f=[],c=0;switch(r=_[t.length],f[c++]=65535&r,f[c++]=r>>16&255,f[c++]=r>>24,o){case 1===u:i=[0,u-1,0];break;case 2===u:i=[1,u-2,0];break;case 3===u:i=[2,u-3,0];break;case 4===u:i=[3,u-4,0];break;case 6>=u:i=[4,u-5,1];break;case 8>=u:i=[5,u-7,1];break;case 12>=u:i=[6,u-9,2];break;case 16>=u:i=[7,u-13,2];break;case 24>=u:i=[8,u-17,3];break;case 32>=u:i=[9,u-25,3];break;case 48>=u:i=[10,u-33,4];break;case 64>=u:i=[11,u-49,4];break;case 96>=u:i=[12,u-65,5];break;case 128>=u:i=[13,u-97,5];break;case 192>=u:i=[14,u-129,6];break;case 256>=u:i=[15,u-193,6];break;case 384>=u:i=[16,u-257,7];break;case 512>=u:i=[17,u-385,7];break;case 768>=u:i=[18,u-513,8];break;case 1024>=u:i=[19,u-769,8];break;case 1536>=u:i=[20,u-1025,9];break;case 2048>=u:i=[21,u-1537,9];break;case 3072>=u:i=[22,u-2049,10];break;case 4096>=u:i=[23,u-3073,10];break;case 6144>=u:i=[24,u-4097,11];break;case 8192>=u:i=[25,u-6145,11];break;case 12288>=u:i=[26,u-8193,12];break;case 16384>=u:i=[27,u-12289,12];break;case 24576>=u:i=[28,u-16385,13];break;case 32768>=u:i=[29,u-24577,13];break;default:n("invalid distance")}for(r=i,f[c++]=r[0],f[c++]=r[1],f[c++]=r[2],a=0,s=f.length;a<s;++a)y[v++]=f[a];w[f[0]]++,A[f[3]]++,b=t.length+e-1,d=null}var s,u,f,c,h,l,p,d,m,g={},y=a?new Uint16Array(2*e.length):[],v=0,b=0,w=new(a?Uint32Array:Array)(286),A=new(a?Uint32Array:Array)(30),x=t.F;if(!a){for(f=0;285>=f;)w[f++]=0;for(f=0;29>=f;)A[f++]=0}for(w[256]=1,s=0,u=e.length;s<u;++s){for(f=h=0,c=3;f<c&&s+f!==u;++f)h=h<<8|e[s+f];if(g[h]===i&&(g[h]=[]),l=g[h],!(0<b--)){for(;0<l.length&&32768<s-l[0];)l.shift();if(s+3>=u){for(d&&r(d,-1),f=0,c=u-s;f<c;++f)m=e[s+f],y[v++]=m,++w[m];break}0<l.length?(p=S(e,s,l),d?d.length<p.length?(m=e[s-1],y[v++]=m,++w[m],r(p,0)):r(d,-1):p.length<x?d=p:r(p,0)):d?r(d,-1):(m=e[s],y[v++]=m,++w[m])}l.push(s)}return y[v++]=256,w[256]++,t.U=w,t.T=A,a?y.subarray(0,v):y}function S(t,e,r){var n,i,o,a,s,u,f=0,c=t.length;a=0,u=r.length;t:for(;a<u;a++){if(n=r[u-a-1],o=3,3<f){for(s=f;3<s;s--)if(t[n+s-1]!==t[e+s-1])continue t;o=f}for(;258>o&&e+o<c&&t[n+o]===t[e+o];)++o;if(o>f&&(i=n,f=o),258===o)break}return new F(f,e-i)}function T(t,e){var r,n,i,o,s,u=t.length,f=new v(572),c=new(a?Uint8Array:Array)(u);if(!a)for(o=0;o<u;o++)c[o]=0;for(o=0;o<u;++o)0<t[o]&&f.push(o,t[o]);if(r=Array(f.length/2),n=new(a?Uint32Array:Array)(f.length/2),1===r.length)return c[f.pop().index]=1,c;for(o=0,s=f.length/2;o<s;++o)r[o]=f.pop(),n[o]=r[o].value;for(i=function(t,e,r){function n(t){var r=d[t][m[t]];r===e?(n(t+1),n(t+1)):--l[r],++m[t]}var i,o,s,u,f,c=new(a?Uint16Array:Array)(r),h=new(a?Uint8Array:Array)(r),l=new(a?Uint8Array:Array)(e),p=Array(r),d=Array(r),m=Array(r),g=(1<<r)-e,y=1<<r-1;for(c[r-1]=e,o=0;o<r;++o)g<y?h[o]=0:(h[o]=1,g-=y),g<<=1,c[r-2-o]=(c[r-1-o]/2|0)+e;for(c[0]=h[0],p[0]=Array(c[0]),d[0]=Array(c[0]),o=1;o<r;++o)c[o]>2*c[o-1]+h[o]&&(c[o]=2*c[o-1]+h[o]),p[o]=Array(c[o]),d[o]=Array(c[o]);for(i=0;i<e;++i)l[i]=r;for(s=0;s<c[r-1];++s)p[r-1][s]=t[s],d[r-1][s]=s;for(i=0;i<r;++i)m[i]=0;for(1===h[r-1]&&(--l[0],++m[r-1]),o=r-2;0<=o;--o){for(u=i=0,f=m[o+1],s=0;s<c[o];s++)(u=p[o+1][f]+p[o+1][f+1])>t[i]?(p[o][s]=u,d[o][s]=e,f+=2):(p[o][s]=t[i],d[o][s]=i,++i);m[o]=0,1===h[o]&&n(o)}return l}(n,n.length,e),o=0,s=r.length;o<s;++o)c[r[o].index]=i[o];return c}function U(t){var e,r,n,i,o=new(a?Uint16Array:Array)(t.length),s=[],u=[],f=0;for(e=0,r=t.length;e<r;e++)s[t[e]]=1+(0|s[t[e]]);for(e=1,r=16;e<=r;e++)u[e]=f,f+=0|s[e],f<<=1;for(e=0,r=t.length;e<r;e++)for(f=u[t[e]],u[t[e]]+=1,n=o[e]=0,i=t[e];n<i;n++)o[e]=o[e]<<1|1&f,f>>>=1;return o}function I(t,e){this.input=t,this.b=this.c=0,this.g={},e&&(e.flags&&(this.g=e.flags),"string"==typeof e.filename&&(this.filename=e.filename),"string"==typeof e.comment&&(this.w=e.comment),e.deflateOptions&&(this.l=e.deflateOptions)),this.l||(this.l={})}I.prototype.h=function(){var t,e,r,n,o,s,u,f,c=new(a?Uint8Array:Array)(32768),h=0,l=this.input,p=this.c,m=this.filename,g=this.w;if(c[h++]=31,c[h++]=139,c[h++]=8,t=0,this.g.fname&&(t|=L),this.g.fcomment&&(t|=P),this.g.fhcrc&&(t|=O),c[h++]=t,e=(Date.now?Date.now():+new Date)/1e3|0,c[h++]=255&e,c[h++]=e>>>8&255,c[h++]=e>>>16&255,c[h++]=e>>>24&255,c[h++]=0,c[h++]=R,this.g.fname!==i){for(u=0,f=m.length;u<f;++u)255<(s=m.charCodeAt(u))&&(c[h++]=s>>>8&255),c[h++]=255&s;c[h++]=0}if(this.g.comment){for(u=0,f=g.length;u<f;++u)255<(s=g.charCodeAt(u))&&(c[h++]=s>>>8&255),c[h++]=255&s;c[h++]=0}return this.g.fhcrc&&(r=65535&d(c,0,h),c[h++]=255&r,c[h++]=r>>>8&255),this.l.outputBuffer=c,this.l.outputIndex=h,c=(o=new w(l,this.l)).h(),h=o.b,a&&(h+8>c.buffer.byteLength?(this.a=new Uint8Array(h+8),this.a.set(new Uint8Array(c.buffer)),c=this.a):c=new Uint8Array(c.buffer)),n=d(l,i,i),c[h++]=255&n,c[h++]=n>>>8&255,c[h++]=n>>>16&255,c[h++]=n>>>24&255,f=l.length,c[h++]=255&f,c[h++]=f>>>8&255,c[h++]=f>>>16&255,c[h++]=f>>>24&255,this.c=p,a&&h<c.length&&(this.a=c=c.subarray(0,h)),c};var R=255,O=2,L=8,P=16;function D(t,e){switch(this.o=[],this.p=32768,this.e=this.j=this.c=this.s=0,this.input=a?new Uint8Array(t):t,this.u=!1,this.q=N,this.K=!1,!e&&(e={})||(e.index&&(this.c=e.index),e.bufferSize&&(this.p=e.bufferSize),e.bufferType&&(this.q=e.bufferType),e.resize&&(this.K=e.resize)),this.q){case j:this.b=32768,this.a=new(a?Uint8Array:Array)(32768+this.p+258);break;case N:this.b=0,this.a=new(a?Uint8Array:Array)(this.p),this.f=this.S,this.z=this.O,this.r=this.Q;break;default:n(Error("invalid inflate mode"))}}var j=0,N=1;D.prototype.i=function(){for(;!this.u;){var t=it(this,3);switch(1&t&&(this.u=o),t>>>=1){case 0:var e=this.input,r=this.c,s=this.a,u=this.b,f=e.length,c=i,h=s.length,l=i;switch(this.e=this.j=0,r+1>=f&&n(Error("invalid uncompressed block header: LEN")),c=e[r++]|e[r++]<<8,r+1>=f&&n(Error("invalid uncompressed block header: NLEN")),c===~(e[r++]|e[r++]<<8)&&n(Error("invalid uncompressed block header: length verify")),r+c>e.length&&n(Error("input buffer is broken")),this.q){case j:for(;u+c>s.length;){if(c-=l=h-u,a)s.set(e.subarray(r,r+l),u),u+=l,r+=l;else for(;l--;)s[u++]=e[r++];this.b=u,s=this.f(),u=this.b}break;case N:for(;u+c>s.length;)s=this.f({B:2});break;default:n(Error("invalid inflate mode"))}if(a)s.set(e.subarray(r,r+c),u),u+=c,r+=c;else for(;c--;)s[u++]=e[r++];this.c=r,this.b=u,this.a=s;break;case 1:this.r(et,nt);break;case 2:var p,d,m,g,y=it(this,5)+257,v=it(this,5)+1,w=it(this,4)+4,A=new(a?Uint8Array:Array)(Y.length),x=i,E=i,B=i,F=i,k=i;for(k=0;k<w;++k)A[Y[k]]=it(this,3);if(!a)for(k=w,w=A.length;k<w;++k)A[Y[k]]=0;for(p=b(A),x=new(a?Uint8Array:Array)(y+v),k=0,g=y+v;k<g;)switch(E=ot(this,p),E){case 16:for(F=3+it(this,2);F--;)x[k++]=B;break;case 17:for(F=3+it(this,3);F--;)x[k++]=0;B=0;break;case 18:for(F=11+it(this,7);F--;)x[k++]=0;B=0;break;default:B=x[k++]=E}d=b(a?x.subarray(0,y):x.slice(0,y)),m=b(a?x.subarray(y):x.slice(y)),this.r(d,m);break;default:n(Error("unknown BTYPE: "+t))}}return this.z()};var M,z,$=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],Y=a?new Uint16Array($):$,G=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,258,258],q=a?new Uint16Array(G):G,V=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0],H=a?new Uint8Array(V):V,J=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],W=a?new Uint16Array(J):J,X=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],Z=a?new Uint8Array(X):X,K=new(a?Uint8Array:Array)(288);for(M=0,z=K.length;M<z;++M)K[M]=143>=M?8:255>=M?9:279>=M?7:8;var Q,tt,et=b(K),rt=new(a?Uint8Array:Array)(30);for(Q=0,tt=rt.length;Q<tt;++Q)rt[Q]=5;var nt=b(rt);function it(t,e){for(var r,i=t.j,o=t.e,a=t.input,s=t.c,u=a.length;o<e;)s>=u&&n(Error("input buffer is broken")),i|=a[s++]<<o,o+=8;return r=i&(1<<e)-1,t.j=i>>>e,t.e=o-e,t.c=s,r}function ot(t,e){for(var r,i,o=t.j,a=t.e,s=t.input,u=t.c,f=s.length,c=e[0],h=e[1];a<h&&!(u>=f);)o|=s[u++]<<a,a+=8;return(i=(r=c[o&(1<<h)-1])>>>16)>a&&n(Error("invalid code length: "+i)),t.j=o>>i,t.e=a-i,t.c=u,65535&r}function at(t){this.input=t,this.c=0,this.G=[],this.R=!1}function st(t){if("string"==typeof t){var e,r,n=t.split("");for(e=0,r=n.length;e<r;e++)n[e]=(255&n[e].charCodeAt(0))>>>0;t=n}for(var i,o=1,a=0,s=t.length,u=0;0<s;){s-=i=1024<s?1024:s;do{a+=o+=t[u++]}while(--i);o%=65521,a%=65521}return(a<<16|o)>>>0}function ut(t,e){var r,i;switch(this.input=t,this.c=0,!e&&(e={})||(e.index&&(this.c=e.index),e.verify&&(this.V=e.verify)),r=t[this.c++],i=t[this.c++],15&r){case ft:this.method=ft;break;default:n(Error("unsupported compression method"))}0!=((r<<8)+i)%31&&n(Error("invalid fcheck flag:"+((r<<8)+i)%31)),32&i&&n(Error("fdict flag is not supported")),this.J=new D(t,{index:this.c,bufferSize:e.bufferSize,bufferType:e.bufferType,resize:e.resize})}D.prototype.r=function(t,e){var r=this.a,n=this.b;this.A=t;for(var i,o,a,s,u=r.length-258;256!==(i=ot(this,t));)if(256>i)n>=u&&(this.b=n,r=this.f(),n=this.b),r[n++]=i;else for(s=q[o=i-257],0<H[o]&&(s+=it(this,H[o])),i=ot(this,e),a=W[i],0<Z[i]&&(a+=it(this,Z[i])),n>=u&&(this.b=n,r=this.f(),n=this.b);s--;)r[n]=r[n++-a];for(;8<=this.e;)this.e-=8,this.c--;this.b=n},D.prototype.Q=function(t,e){var r=this.a,n=this.b;this.A=t;for(var i,o,a,s,u=r.length;256!==(i=ot(this,t));)if(256>i)n>=u&&(u=(r=this.f()).length),r[n++]=i;else for(s=q[o=i-257],0<H[o]&&(s+=it(this,H[o])),i=ot(this,e),a=W[i],0<Z[i]&&(a+=it(this,Z[i])),n+s>u&&(u=(r=this.f()).length);s--;)r[n]=r[n++-a];for(;8<=this.e;)this.e-=8,this.c--;this.b=n},D.prototype.f=function(){var t,e,r=new(a?Uint8Array:Array)(this.b-32768),n=this.b-32768,i=this.a;if(a)r.set(i.subarray(32768,r.length));else for(t=0,e=r.length;t<e;++t)r[t]=i[t+32768];if(this.o.push(r),this.s+=r.length,a)i.set(i.subarray(n,n+32768));else for(t=0;32768>t;++t)i[t]=i[n+t];return this.b=32768,i},D.prototype.S=function(t){var e,r,n,i=this.input.length/this.c+1|0,o=this.input,s=this.a;return t&&("number"==typeof t.B&&(i=t.B),"number"==typeof t.M&&(i+=t.M)),2>i?r=(n=(o.length-this.c)/this.A[2]/2*258|0)<s.length?s.length+n:s.length<<1:r=s.length*i,a?(e=new Uint8Array(r)).set(s):e=s,this.a=e},D.prototype.z=function(){var t,e,r,n,i,o=0,s=this.a,u=this.o,f=new(a?Uint8Array:Array)(this.s+(this.b-32768));if(0===u.length)return a?this.a.subarray(32768,this.b):this.a.slice(32768,this.b);for(e=0,r=u.length;e<r;++e)for(n=0,i=(t=u[e]).length;n<i;++n)f[o++]=t[n];for(e=32768,r=this.b;e<r;++e)f[o++]=s[e];return this.o=[],this.buffer=f},D.prototype.O=function(){var t,e=this.b;return a?this.K?(t=new Uint8Array(e)).set(this.a.subarray(0,e)):t=this.a.subarray(0,e):(this.a.length>e&&(this.a.length=e),t=this.a),this.buffer=t},at.prototype.i=function(){for(var t=this.input.length;this.c<t;){var e,r,s=new y,u=i,f=i,c=i,h=i,l=i,p=i,m=i,g=this.input,v=this.c;switch(s.C=g[v++],s.D=g[v++],(31!==s.C||139!==s.D)&&n(Error("invalid file signature:"+s.C+","+s.D)),s.v=g[v++],s.v){case 8:break;default:n(Error("unknown compression method: "+s.v))}if(s.n=g[v++],r=g[v++]|g[v++]<<8|g[v++]<<16|g[v++]<<24,s.$=new Date(1e3*r),s.ba=g[v++],s.aa=g[v++],0<(4&s.n)&&(s.W=g[v++]|g[v++]<<8,v+=s.W),0<(s.n&L)){for(p=[],l=0;0<(h=g[v++]);)p[l++]=String.fromCharCode(h);s.name=p.join("")}if(0<(s.n&P)){for(p=[],l=0;0<(h=g[v++]);)p[l++]=String.fromCharCode(h);s.w=p.join("")}0<(s.n&O)&&(s.P=65535&d(g,0,v),s.P!==(g[v++]|g[v++]<<8)&&n(Error("invalid header crc16"))),u=g[g.length-4]|g[g.length-3]<<8|g[g.length-2]<<16|g[g.length-1]<<24,g.length-v-4-4<512*u&&(c=u),f=new D(g,{index:v,bufferSize:c}),s.data=e=f.i(),v=f.c,s.Y=m=(g[v++]|g[v++]<<8|g[v++]<<16|g[v++]<<24)>>>0,d(e,i,i)!==m&&n(Error("invalid CRC-32 checksum: 0x"+d(e,i,i).toString(16)+" / 0x"+m.toString(16))),s.Z=u=(g[v++]|g[v++]<<8|g[v++]<<16|g[v++]<<24)>>>0,(4294967295&e.length)!==u&&n(Error("invalid input size: "+(4294967295&e.length)+" / "+u)),this.G.push(s),this.c=v}this.R=o;var b,w,A,x=this.G,E=0,B=0;for(b=0,w=x.length;b<w;++b)B+=x[b].data.length;if(a)for(A=new Uint8Array(B),b=0;b<w;++b)A.set(x[b].data,E),E+=x[b].data.length;else{for(A=[],b=0;b<w;++b)A[b]=x[b].data;A=Array.prototype.concat.apply([],A)}return A},ut.prototype.i=function(){var t,e=this.input;return t=this.J.i(),this.c=this.J.c,this.V&&((e[this.c++]<<24|e[this.c++]<<16|e[this.c++]<<8|e[this.c++])>>>0!==st(t)&&n(Error("invalid adler-32 checksum"))),t};var ft=8;function ct(t,e){this.input=t,this.a=new(a?Uint8Array:Array)(32768),this.k=ht.t;var r,n={};for(r in!e&&(e={})||"number"!=typeof e.compressionType||(this.k=e.compressionType),e)n[r]=e[r];n.outputBuffer=this.a,this.I=new w(this.input,n)}var ht=E;function lt(t,e){var r;return r=new ct(t).h(),e||(e={}),e.H?r:gt(r)}function pt(t,e){var r;return t.subarray=t.slice,r=new ut(t).i(),e||(e={}),e.noBuffer?r:gt(r)}function dt(t,e){var r;return t.subarray=t.slice,r=new I(t).h(),e||(e={}),e.H?r:gt(r)}function mt(t,e){var r;return t.subarray=t.slice,r=new at(t).i(),e||(e={}),e.H?r:gt(r)}function gt(t){var e,n,i=new r(t.length);for(e=0,n=t.length;e<n;++e)i[e]=t[e];return i}ct.prototype.h=function(){var t,e,r,i,o,s,u,f=0;switch(u=this.a,t=ft){case ft:e=Math.LOG2E*Math.log(32768)-8;break;default:n(Error("invalid compression method"))}switch(r=e<<4|t,u[f++]=r,t){case ft:switch(this.k){case ht.NONE:o=0;break;case ht.L:o=1;break;case ht.t:o=2;break;default:n(Error("unsupported compression type"))}break;default:n(Error("invalid compression method"))}return i=o<<6|0,u[f++]=i|31-(256*r+i)%31,s=st(this.input),this.I.b=f,f=(u=this.I.h()).length,a&&((u=new Uint8Array(u.buffer)).length<=f+4&&(this.a=new Uint8Array(u.length+4),this.a.set(u),u=this.a),u=u.subarray(0,f+4)),u[f++]=s>>24&255,u[f++]=s>>16&255,u[f++]=s>>8&255,u[f++]=255&s,u},e.deflate=function(e,r,n){t.nextTick(function(){var t,i;try{i=lt(e,n)}catch(e){t=e}r(t,i)})},e.deflateSync=lt,e.inflate=function(e,r,n){t.nextTick(function(){var t,i;try{i=pt(e,n)}catch(e){t=e}r(t,i)})},e.inflateSync=pt,e.gzip=function(e,r,n){t.nextTick(function(){var t,i;try{i=dt(e,n)}catch(e){t=e}r(t,i)})},e.gzipSync=dt,e.gunzip=function(e,r,n){t.nextTick(function(){var t,i;try{i=mt(e,n)}catch(e){t=e}r(t,i)})},e.gunzipSync=mt}).call(this)}).call(this,r(27),r(12).Buffer)},1201:function(t,e,r){(function(e){const n=r(1202),i=r(624);t.exports=(t,r,o=70)=>{const a=e.from(r),s=i(a);let u=0,f=null,c=null,h=0,l=0;if(s&&"image/bmp"===s.mime){const e=n.decode(a);f=t._malloc(e.data.length*Uint8Array.BYTES_PER_ELEMENT),t.HEAPU8.set(e.data,f),h=e.width,l=e.height,u=4}else{const e=t._malloc(a.length*Uint8Array.BYTES_PER_ELEMENT);t.HEAPU8.set(a,e),c=t._pixReadMem(e,a.length),0===t.getValue(c+28,"i32")&&t.setValue(c+28,o,"i32"),[h,l]=Array(2).fill(0).map((e,r)=>t.getValue(c+4*r,"i32"))}return{w:h,h:l,bytesPerPixel:u,data:f,pix:c}}}).call(this,r(12).Buffer)},1202:function(t,e,r){var n=r(1203),i=r(1204);t.exports={encode:n,decode:i}},1203:function(t,e,r){(function(e){function r(t){this.buffer=t.data,this.width=t.width,this.height=t.height,this.extraBytes=this.width%4,this.rgbSize=this.height*(3*this.width+this.extraBytes),this.headerInfoSize=40,this.data=[],this.flag="BM",this.reserved=0,this.offset=54,this.fileSize=this.rgbSize+this.offset,this.planes=1,this.bitPP=24,this.compress=0,this.hr=0,this.vr=0,this.colors=0,this.importantColors=0}r.prototype.encode=function(){var t=new e(this.offset+this.rgbSize);this.pos=0,t.write(this.flag,this.pos,2),this.pos+=2,t.writeUInt32LE(this.fileSize,this.pos),this.pos+=4,t.writeUInt32LE(this.reserved,this.pos),this.pos+=4,t.writeUInt32LE(this.offset,this.pos),this.pos+=4,t.writeUInt32LE(this.headerInfoSize,this.pos),this.pos+=4,t.writeUInt32LE(this.width,this.pos),this.pos+=4,t.writeInt32LE(-this.height,this.pos),this.pos+=4,t.writeUInt16LE(this.planes,this.pos),this.pos+=2,t.writeUInt16LE(this.bitPP,this.pos),this.pos+=2,t.writeUInt32LE(this.compress,this.pos),this.pos+=4,t.writeUInt32LE(this.rgbSize,this.pos),this.pos+=4,t.writeUInt32LE(this.hr,this.pos),this.pos+=4,t.writeUInt32LE(this.vr,this.pos),this.pos+=4,t.writeUInt32LE(this.colors,this.pos),this.pos+=4,t.writeUInt32LE(this.importantColors,this.pos),this.pos+=4;for(var r=0,n=3*this.width+this.extraBytes,i=0;i<this.height;i++){for(var o=0;o<this.width;o++){var a=this.pos+i*n+3*o;r++,t[a]=this.buffer[r++],t[a+1]=this.buffer[r++],t[a+2]=this.buffer[r++]}if(this.extraBytes>0){var s=this.pos+i*n+3*this.width;t.fill(0,s,s+this.extraBytes)}}return t},t.exports=function(t,e){return void 0===e&&(e=100),{data:new r(t).encode(),width:t.width,height:t.height}}}).call(this,r(12).Buffer)},1204:function(t,e,r){(function(e){function r(t,e){if(this.pos=0,this.buffer=t,this.is_with_alpha=!!e,this.bottom_up=!0,this.flag=this.buffer.toString("utf-8",0,this.pos+=2),"BM"!=this.flag)throw new Error("Invalid BMP File");this.parseHeader(),this.parseRGBA()}r.prototype.parseHeader=function(){if(this.fileSize=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.reserved=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.offset=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.headerSize=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.width=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.height=this.buffer.readInt32LE(this.pos),this.pos+=4,this.planes=this.buffer.readUInt16LE(this.pos),this.pos+=2,this.bitPP=this.buffer.readUInt16LE(this.pos),this.pos+=2,this.compress=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.rawSize=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.hr=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.vr=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.colors=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.importantColors=this.buffer.readUInt32LE(this.pos),this.pos+=4,16===this.bitPP&&this.is_with_alpha&&(this.bitPP=15),this.bitPP<15){var t=0===this.colors?1<<this.bitPP:this.colors;this.palette=new Array(t);for(var e=0;e<t;e++){var r=this.buffer.readUInt8(this.pos++),n=this.buffer.readUInt8(this.pos++),i=this.buffer.readUInt8(this.pos++),o=this.buffer.readUInt8(this.pos++);this.palette[e]={red:i,green:n,blue:r,quad:o}}}this.height<0&&(this.height*=-1,this.bottom_up=!1)},r.prototype.parseRGBA=function(){var t="bit"+this.bitPP,r=this.width*this.height*4;this.data=new e(r),this[t]()},r.prototype.bit1=function(){var t=Math.ceil(this.width/8),e=t%4,r=this.height>=0?this.height-1:-this.height;for(r=this.height-1;r>=0;r--){for(var n=this.bottom_up?r:this.height-1-r,i=0;i<t;i++)for(var o=this.buffer.readUInt8(this.pos++),a=n*this.width*4+8*i*4,s=0;s<8&&8*i+s<this.width;s++){var u=this.palette[o>>7-s&1];this.data[a+4*s]=0,this.data[a+4*s+1]=u.blue,this.data[a+4*s+2]=u.green,this.data[a+4*s+3]=u.red}0!=e&&(this.pos+=4-e)}},r.prototype.bit4=function(){if(2==this.compress){this.data.fill(255);for(var t=0,e=this.bottom_up?this.height-1:0,r=!1;t<this.data.length;){var n=this.buffer.readUInt8(this.pos++),i=this.buffer.readUInt8(this.pos++);if(0==n){if(0==i){this.bottom_up?e--:e++,t=e*this.width*4,r=!1;continue}if(1==i)break;if(2==i){var o=this.buffer.readUInt8(this.pos++),a=this.buffer.readUInt8(this.pos++);this.bottom_up?e-=a:e+=a,t+=a*this.width*4+4*o}else{for(var s=this.buffer.readUInt8(this.pos++),u=0;u<i;u++)r?f.call(this,15&s):f.call(this,(240&s)>>4),1&u&&u+1<i&&(s=this.buffer.readUInt8(this.pos++)),r=!r;1==(i+1>>1&1)&&this.pos++}}else for(u=0;u<n;u++)r?f.call(this,15&i):f.call(this,(240&i)>>4),r=!r}function f(e){var r=this.palette[e];this.data[t]=0,this.data[t+1]=r.blue,this.data[t+2]=r.green,this.data[t+3]=r.red,t+=4}}else{var c=Math.ceil(this.width/2),h=c%4;for(a=this.height-1;a>=0;a--){var l=this.bottom_up?a:this.height-1-a;for(o=0;o<c;o++){i=this.buffer.readUInt8(this.pos++),t=l*this.width*4+2*o*4;var p=i>>4,d=15&i,m=this.palette[p];if(this.data[t]=0,this.data[t+1]=m.blue,this.data[t+2]=m.green,this.data[t+3]=m.red,2*o+1>=this.width)break;m=this.palette[d],this.data[t+4]=0,this.data[t+4+1]=m.blue,this.data[t+4+2]=m.green,this.data[t+4+3]=m.red}0!=h&&(this.pos+=4-h)}}},r.prototype.bit8=function(){if(1==this.compress){this.data.fill(255);for(var t=0,e=this.bottom_up?this.height-1:0;t<this.data.length;){var r=this.buffer.readUInt8(this.pos++),n=this.buffer.readUInt8(this.pos++);if(0==r){if(0==n){this.bottom_up?e--:e++,t=e*this.width*4;continue}if(1==n)break;if(2==n){var i=this.buffer.readUInt8(this.pos++),o=this.buffer.readUInt8(this.pos++);this.bottom_up?e-=o:e+=o,t+=o*this.width*4+4*i}else{for(var a=0;a<n;a++){var s=this.buffer.readUInt8(this.pos++);u.call(this,s)}!0&n&&this.pos++}}else for(a=0;a<r;a++)u.call(this,n)}function u(e){var r=this.palette[e];this.data[t]=0,this.data[t+1]=r.blue,this.data[t+2]=r.green,this.data[t+3]=r.red,t+=4}}else{var f=this.width%4;for(o=this.height-1;o>=0;o--){var c=this.bottom_up?o:this.height-1-o;for(i=0;i<this.width;i++){n=this.buffer.readUInt8(this.pos++),t=c*this.width*4+4*i;if(n<this.palette.length){var h=this.palette[n];this.data[t]=0,this.data[t+1]=h.blue,this.data[t+2]=h.green,this.data[t+3]=h.red}else this.data[t]=0,this.data[t+1]=255,this.data[t+2]=255,this.data[t+3]=255}0!=f&&(this.pos+=4-f)}}},r.prototype.bit15=function(){for(var t=this.width%3,e=parseInt("11111",2),r=this.height-1;r>=0;r--){for(var n=this.bottom_up?r:this.height-1-r,i=0;i<this.width;i++){var o=this.buffer.readUInt16LE(this.pos);this.pos+=2;var a=(o&e)/e*255|0,s=(o>>5&e)/e*255|0,u=(o>>10&e)/e*255|0,f=o>>15?255:0,c=n*this.width*4+4*i;this.data[c]=f,this.data[c+1]=a,this.data[c+2]=s,this.data[c+3]=u}this.pos+=t}},r.prototype.bit16=function(){var t=this.width%2*2;this.maskRed=31744,this.maskGreen=992,this.maskBlue=31,this.mask0=0,3==this.compress&&(this.maskRed=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.maskGreen=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.maskBlue=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.mask0=this.buffer.readUInt32LE(this.pos),this.pos+=4);for(var e=[0,0,0],r=0;r<16;r++)this.maskRed>>r&1&&e[0]++,this.maskGreen>>r&1&&e[1]++,this.maskBlue>>r&1&&e[2]++;e[1]+=e[0],e[2]+=e[1],e[0]=8-e[0],e[1]-=8,e[2]-=8;for(var n=this.height-1;n>=0;n--){for(var i=this.bottom_up?n:this.height-1-n,o=0;o<this.width;o++){var a=this.buffer.readUInt16LE(this.pos);this.pos+=2;var s=(a&this.maskBlue)<<e[0],u=(a&this.maskGreen)>>e[1],f=(a&this.maskRed)>>e[2],c=i*this.width*4+4*o;this.data[c]=0,this.data[c+1]=s,this.data[c+2]=u,this.data[c+3]=f}this.pos+=t}},r.prototype.bit24=function(){for(var t=this.height-1;t>=0;t--){for(var e=this.bottom_up?t:this.height-1-t,r=0;r<this.width;r++){var n=this.buffer.readUInt8(this.pos++),i=this.buffer.readUInt8(this.pos++),o=this.buffer.readUInt8(this.pos++),a=e*this.width*4+4*r;this.data[a]=0,this.data[a+1]=n,this.data[a+2]=i,this.data[a+3]=o}this.pos+=this.width%4}},r.prototype.bit32=function(){if(3==this.compress){this.maskRed=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.maskGreen=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.maskBlue=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.mask0=this.buffer.readUInt32LE(this.pos),this.pos+=4;for(var t=this.height-1;t>=0;t--)for(var e=this.bottom_up?t:this.height-1-t,r=0;r<this.width;r++){var n=this.buffer.readUInt8(this.pos++),i=this.buffer.readUInt8(this.pos++),o=this.buffer.readUInt8(this.pos++),a=this.buffer.readUInt8(this.pos++),s=e*this.width*4+4*r;this.data[s]=n,this.data[s+1]=i,this.data[s+2]=o,this.data[s+3]=a}}else for(t=this.height-1;t>=0;t--)for(e=this.bottom_up?t:this.height-1-t,r=0;r<this.width;r++){i=this.buffer.readUInt8(this.pos++),o=this.buffer.readUInt8(this.pos++),a=this.buffer.readUInt8(this.pos++),n=this.buffer.readUInt8(this.pos++),s=e*this.width*4+4*r;this.data[s]=n,this.data[s+1]=i,this.data[s+2]=o,this.data[s+3]=a}},r.prototype.getData=function(){return this.data},t.exports=function(t){return new r(t)}}).call(this,r(12).Buffer)},1205:function(t,e,r){const n=r(631),i="undefined"!=typeof window&&void 0!==window.document?r(632):t=>t,o=r(633),a=r(1209),s=r(1210);t.exports=class{constructor(t={}){this.worker=null,this.options={...o.defaultOptions,...t},["corePath","workerPath","langPath"].forEach(e=>{n.not.undefined(t[e])&&(this.options={...this.options,[e]:i(t[e])})}),this._currentJob=null,this._queue=[]}recognize(t,e="eng",r={}){return this._sendJob("recognize",t,e,r)}detect(t,e={}){return this._sendJob("detect",t,"osd",e)}recv(t){this._currentJob.id===t.jobId?this._currentJob.handle({...t,data:"resolve"===t.status&&"recognize"===t.action?a(t.data):t.data}):console.warn(`Job ID ${t.jobId} not known.`)}dequeue(){this._currentJob=null,this._queue.length&&this._queue[0]()}terminate(){this.worker&&o.terminateWorker(this),this.worker=null,this._currentJob=null,this._queue=[]}_sendJob(t,e,r,n){return this._delay(i=>{i.send(t,{image:e,langs:r,params:n,options:this.options})})}_delay(t){n.null(this.worker)&&(this.worker=o.spawnWorker(this,this.options));const e=new s(this);return this._queue.push(()=>{this._queue.shift(),this._currentJob=e,t(e)}),n.null(this._currentJob)&&this.dequeue(),e}}},1206:function(t,e){t.exports=t=>new Uint8Array(atob(t).split("").map(t=>t.charCodeAt(0)))},1207:function(t,e,r){const{OEM:n,PSM:i}=r(634);t.exports={defaultOptions:{langPath:"https://tessdata.projectnaptha.com/4.0.0"},defaultParams:{tessedit_ocr_engine_mode:n.LSTM_ONLY,tessedit_pageseg_mode:i.SINGLE_BLOCK,tessedit_char_whiltelist:"",tessjs_create_pdf:"0",tessjs_create_hocr:"1",tessjs_create_tsv:"1",tessjs_create_box:"0",tessjs_create_unlv:"0",tessjs_create_osd:"0",tessjs_textonly_pdf:"0",tessjs_pdf_name:"tesseract.js-ocr-result",tessjs_pdf_title:"Tesseract.js OCR Result",tessjs_pdf_auto_download:!0,tessjs_pdf_bin:!1,tessjs_image_rectangle_left:0,tessjs_image_rectangle_top:0,tessjs_image_rectangle_width:-1,tessjs_image_rectangle_height:-1}}},1208:function(t){t.exports=JSON.parse('{"_args":[["tesseract.js@2.0.0-alpha.15","/home/<USER>/build/gchq/CyberChef"]],"_from":"tesseract.js@2.0.0-alpha.15","_id":"tesseract.js@2.0.0-alpha.15","_inBundle":false,"_integrity":"sha512-qM1XUFVlTO+tx6oVRpd9QQ8PwQLxo3qhbfIHByUlUVIqWx6y/U9xlHIaG033/Tjfs2EQ0NAehPTOJ+eNElsXEg==","_location":"/tesseract.js","_phantomChildren":{},"_requested":{"type":"version","registry":true,"raw":"tesseract.js@2.0.0-alpha.15","name":"tesseract.js","escapedName":"tesseract.js","rawSpec":"2.0.0-alpha.15","saveSpec":null,"fetchSpec":"2.0.0-alpha.15"},"_requiredBy":["/"],"_resolved":"https://registry.npmjs.org/tesseract.js/-/tesseract.js-2.0.0-alpha.15.tgz","_spec":"2.0.0-alpha.15","_where":"/home/<USER>/build/gchq/CyberChef","author":"","browser":{"./src/node/index.js":"./src/browser/index.js"},"bugs":{"url":"https://github.com/naptha/tesseract.js/issues"},"collective":{"type":"opencollective","url":"https://opencollective.com/tesseractjs"},"contributors":[{"name":"jeromewu"}],"dependencies":{"axios":"^0.18.0","check-types":"^7.4.0","is-url":"1.2.2","node-fetch":"^2.3.0","opencollective-postinstall":"^2.0.2","resolve-url":"^0.2.1","tesseract.js-core":"^2.0.0-beta.11","tesseract.js-utils":"^1.0.0-beta.8"},"description":"Pure Javascript Multilingual OCR","devDependencies":{"@babel/core":"^7.4.5","@babel/preset-env":"^7.4.5","acorn":"^6.1.1","babel-loader":"^8.0.6","cors":"^2.8.5","eslint":"^5.9.0","eslint-config-airbnb":"^17.1.0","eslint-plugin-import":"^2.14.0","eslint-plugin-jsx-a11y":"^6.1.2","eslint-plugin-react":"^7.11.1","expect.js":"^0.3.1","express":"^4.16.4","mocha":"^5.2.0","mocha-headless-chrome":"^2.0.2","npm-run-all":"^4.1.5","nyc":"^13.1.0","rimraf":"^2.6.3","wait-on":"^3.2.0","webpack":"^4.26.0","webpack-cli":"^3.1.2","webpack-dev-middleware":"^3.4.0"},"homepage":"https://github.com/naptha/tesseract.js","jsdelivr":"dist/tesseract.min.js","license":"Apache-2.0","main":"src/index.js","name":"tesseract.js","repository":{"type":"git","url":"git+https://github.com/naptha/tesseract.js.git"},"scripts":{"build":"rimraf dist && webpack --config scripts/webpack.config.prod.js","lint":"eslint src","postinstall":"opencollective-postinstall || true","prepublishOnly":"npm run build","start":"node scripts/server.js","test":"npm-run-all -p -r start test:all","test:all":"npm-run-all wait test:browser:* test:node","test:browser-tpl":"mocha-headless-chrome -a incognito -a no-sandbox -a disable-setuid-sandbox -t 300000","test:browser:detect":"npm run test:browser-tpl -- -f ./tests/detect.test.html","test:browser:recognize":"npm run test:browser-tpl -- -f ./tests/recognize.test.html","test:node":"nyc mocha --exit --bail --require ./scripts/test-helper.js ./tests/*.test.js","wait":"wait-on http://localhost:3000/package.json"},"unpkg":"dist/tesseract.min.js","version":"2.0.0-alpha.15"}')},1209:function(t,e){t.exports=t=>{const e={...t,paragraphs:[],lines:[],words:[],symbols:[]};return e.blocks.forEach(t=>{const r={...t,page:e,lines:[],words:[],symbols:[]};r.paragraphs.forEach(t=>{const n={...t,block:r,page:e,words:[],symbols:[]};n.lines.forEach(t=>{const i={...t,paragraph:n,block:r,page:e,symbols:[]};i.words.forEach(t=>{const o={...t,line:i,paragraph:n,block:r,page:e};o.symbols.forEach(t=>{const a={...t,word:o,line:i,paragraph:n,block:r,page:e};a.line.symbols.push(a),a.paragraph.symbols.push(a),a.block.symbols.push(a),a.page.symbols.push(a)}),o.paragraph.words.push(o),o.block.words.push(o),o.page.words.push(o)}),i.block.lines.push(i),i.page.lines.push(i)}),n.page.paragraphs.push(n)})}),e}},1210:function(t,e,r){const n=r(633);let i=0;t.exports=class{constructor(t){i+=1,this.id=`Job-${i}-${Math.random().toString(16).slice(3,8)}`,this._worker=t,this._resolve=[],this._reject=[],this._progress=[],this._finally=[]}then(t,e){return new Promise((t,e)=>{this._resolve.push?this._resolve.push(t):t(this._result),this.catch(e)}).then(t,e)}catch(t){return this._reject.push?this._reject.push(t):t(this._reject),this}progress(t){return this._progress.push(t),this}finally(t){return this._finally.push(t),this}send(t,e){n.sendPacket(this._worker,{jobId:this.id,action:t,payload:e})}handle(t){const{data:e}=t;let r=!1;"resolve"===t.status?(0===this._resolve.length&&console.log(e),this._resolve.forEach(t=>{const r=t(e);r&&"function"==typeof r.then&&console.warn("TesseractJob instances do not chain like ES6 Promises. To convert it into a real promise, use Promise.resolve.")}),this._resolve=e,this._worker.dequeue(),r=!0):"reject"===t.status?(0===this._reject.length&&console.error(e),this._reject.forEach(t=>t(e)),this._reject=e,this._worker.dequeue(),r=!0):"progress"===t.status?this._progress.forEach(t=>t(e)):console.warn("Message type unknown",t.status),r&&this._finally.forEach(t=>t(e))}}},13:function(t,e,r){var n=r(49),i=r(50),o=r(51);t.exports=function(t,e){return n(t)||i(t,e)||o()}},1346:function(t,e,r){"use strict";r.r(e);var n=r(7),i=r.n(n),o=r(13),a=r.n(o),s=r(11),u=r.n(s),f=r(1),c=r.n(f),h=r(2),l=r.n(h),p=r(4),d=r.n(p),m=r(3),g=r.n(m),y=r(5),v=r.n(y),b=r(6),w=r(9),A=r(18),x=r(14),E=r(0),B=r(711),F=r.n(B),k=r(27),_=r.n(k),C=F.a.TesseractWorker,S=function(t){function e(){var t;return c()(this,e),(t=d()(this,g()(e).call(this))).name="Optical Character Recognition",t.module="OCR",t.description="Optical character recognition or optical character reader (OCR) is the mechanical or electronic conversion of images of typed, handwritten or printed text into machine-encoded text.<br><br>Supported image formats: png, jpg, bmp, pbm.",t.infoURL="https://wikipedia.org/wiki/Optical_character_recognition",t.inputType="ArrayBuffer",t.outputType="string",t.args=[{name:"Show confidence",type:"boolean",value:!0}],t}var r;return v()(e,t),l()(e,[{key:"run",value:(r=u()(i.a.mark(function t(e,r){var n,o,s,u,f,c,h;return i.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(n=a()(r,1),o=n[0],Object(E.d)()){t.next=3;break}throw Object(w.a)("This operation only works in a browser");case 3:if(s=Object(A.b)(e)){t.next=6;break}throw new w.a("Invalid File Type");case 6:return u=Object(E.d)()?`${self.docURL}/assets/`:`${_.a.cwd()}/src/core/vendor/`,t.prev=7,f=`data:${s};base64,${Object(x.b)(e)}`,c=new C({workerPath:`${u}/tesseract/worker.min.js`,langPath:`${u}/tesseract/lang-data/`,corePath:`${u}/tesseract/tesseract-core.wasm.js`}),t.next=12,c.recognize(f).progress(function(t){Object(E.d)()&&self.sendStatusMessage(`Status: ${t.status} - ${(100*parseFloat(t.progress)).toFixed(2)}%`)});case 12:if(h=t.sent,!o){t.next=17;break}return t.abrupt("return",`Confidence: ${h.confidence}%\n\n${h.text}`);case 17:return t.abrupt("return",h.text);case 18:t.next=23;break;case 20:throw t.prev=20,t.t0=t.catch(7),new w.a(`Error performing OCR on image. (${t.t0})`);case 23:case"end":return t.stop()}},t,null,[[7,20]])})),function(t,e){return r.apply(this,arguments)})}]),e}(b.a),T="undefined"==typeof self?{}:self.OpModules||{};T.OCR={"Optical Character Recognition":S};e.default=T},14:function(t,e,r){"use strict";r.d(e,"b",function(){return i}),r.d(e,"a",function(){return o});var n=r(0);
/**
 * Base64 functions.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2016
 * @license Apache-2.0
 */function i(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"A-Za-z0-9+/=";if(!t)return"";t instanceof ArrayBuffer&&(t=new Uint8Array(t)),"string"==typeof t&&(t=n.b.strToByteArray(t)),e=n.b.expandAlphRange(e).join("");for(var r,i,o,a,s,u,f,c="",h=0;h<t.length;)a=(r=t[h++])>>2,s=(3&r)<<4|(i=t[h++])>>4,u=(15&i)<<2|(o=t[h++])>>6,f=63&o,isNaN(i)?u=f=64:isNaN(o)&&(f=64),c+=e.charAt(a)+e.charAt(s)+e.charAt(u)+e.charAt(f);return c}function o(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"A-Za-z0-9+/=",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"string",i=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];if(!t)return"string"===r?"":[];e=e||"A-Za-z0-9+/=",e=n.b.expandAlphRange(e).join("");var o,a,s,u,f,c,h=[],l=0;if(i){var p=new RegExp("[^"+e.replace(/[[\]\\\-^$]/g,"\\$&")+"]","g");t=t.replace(p,"")}for(;l<t.length;)o=e.indexOf(t.charAt(l++))<<2|(u=-1===(u=e.indexOf(t.charAt(l++)||"="))?64:u)>>4,a=(15&u)<<4|(f=-1===(f=e.indexOf(t.charAt(l++)||"="))?64:f)>>2,s=(3&f)<<6|(c=-1===(c=e.indexOf(t.charAt(l++)||"="))?64:c),h.push(o),64!==f&&h.push(a),64!==c&&h.push(s);return"string"===r?n.b.byteArrayToUtf8(h):h}},15:function(t,e,r){"use strict";var n=r(7),i=r.n(n),o=r(11),a=r.n(o),s=r(1),u=r.n(s),f=r(2),c=r.n(f),h=r(0),l=r(4),p=r.n(l),d=r(3),m=r.n(d),g=r(25),y=r.n(g),v=r(5),b=r.n(v);
/**
 * Custom error type for handling Dish type errors.
 * i.e. where the Dish cannot be successfully translated between types
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 */
var w=function(t){function e(){var t;u()(this,e);for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return(t=p()(this,m()(e).call(this,...n))).type="DishError",Error.captureStackTrace&&Error.captureStackTrace(y()(t),e),t}return b()(e,t),e}(function(t){function e(){var e=Reflect.construct(t,Array.from(arguments));return Object.setPrototypeOf(e,Object.getPrototypeOf(this)),e}return e.prototype=Object.create(t.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t,e}(Error)),A=r(16),x=r.n(A),E=r(18),B=r(26),F=r.n(B),k=function(){function t(){u()(this,t)}return c()(t,null,[{key:"checkForValue",value:function(t){if(void 0===t)throw new Error("only use translation methods with .bind")}},{key:"toArrayBuffer",value:function(){throw new Error("toArrayBuffer has not been implemented")}},{key:"fromArrayBuffer",value:function(){arguments.length>0&&void 0!==arguments[0]&&arguments[0];throw new Error("fromArrayBuffer has not been implemented")}}]),t}(),_=function(t){function e(){return u()(this,e),p()(this,m()(e).apply(this,arguments))}return b()(e,t),c()(e,null,[{key:"toArrayBuffer",value:function(){e.checkForValue(this.value),this.value=new Uint8Array(this.value).buffer}},{key:"fromArrayBuffer",value:function(){e.checkForValue(this.value),this.value=Array.prototype.slice.call(new Uint8Array(this.value))}}]),e}(k),C=function(t){function e(){return u()(this,e),p()(this,m()(e).apply(this,arguments))}return b()(e,t),c()(e,null,[{key:"toArrayBuffer",value:function(){e.checkForValue(this.value),this.value=x.a.isBigNumber(this.value)?h.b.strToArrayBuffer(this.value.toFixed()):new ArrayBuffer}},{key:"fromArrayBuffer",value:function(t){e.checkForValue(this.value);try{this.value=new x.a(h.b.arrayBufferToStr(this.value,!t))}catch(t){this.value=new x.a(NaN)}}}]),e}(k),S=function(t){function e(){return u()(this,e),p()(this,m()(e).apply(this,arguments))}return b()(e,t),c()(e,null,[{key:"toArrayBuffer",value:function(){var t=this;if(e.checkForValue(this.value),!Object(h.c)())return new Promise(function(e,r){h.b.readFile(t.value).then(function(e){return t.value=e.buffer}).then(e).catch(r)});this.value=h.b.readFileSync(this.value)}},{key:"fromArrayBuffer",value:function(){e.checkForValue(this.value),this.value=new File(this.value,"unknown")}}]),e}(k),T=function(t){function e(){return u()(this,e),p()(this,m()(e).apply(this,arguments))}return b()(e,t),c()(e,null,[{key:"toArrayBuffer",value:function(){e.checkForValue(this.value),this.value=this.value?h.b.strToArrayBuffer(this.value):new ArrayBuffer}},{key:"fromArrayBuffer",value:function(t){e.checkForValue(this.value),this.value=this.value?h.b.arrayBufferToStr(this.value,!t):""}}]),e}(k),U=function(t){function e(){return u()(this,e),p()(this,m()(e).apply(this,arguments))}return b()(e,t),c()(e,null,[{key:"toArrayBuffer",value:function(){e.checkForValue(this.value),this.value=this.value?h.b.strToArrayBuffer(h.b.unescapeHtml(h.b.stripHtmlTags(this.value,!0))):new ArrayBuffer}}]),e}(T),I=function(t){function e(){return u()(this,e),p()(this,m()(e).apply(this,arguments))}return b()(e,t),c()(e,null,[{key:"toArrayBuffer",value:function(){e.checkForValue(this.value),this.value=this.value?h.b.strToArrayBuffer(JSON.stringify(this.value,null,4)):new ArrayBuffer}},{key:"fromArrayBuffer",value:function(t){e.checkForValue(this.value),this.value=JSON.parse(h.b.arrayBufferToStr(this.value,!t))}}]),e}(k),R=function(t){function e(){return u()(this,e),p()(this,m()(e).apply(this,arguments))}return b()(e,t),c()(e,null,[{key:"toArrayBuffer",value:function(){e.checkForValue(this.value),Object(h.c)()&&(this.value=this.value.map(function(t){return Uint8Array.from(t.data)})),this.value=e.concatenateTypedArrays(...this.value).buffer}},{key:"fromArrayBuffer",value:function(){e.checkForValue(this.value),this.value=[new File(this.value,"unknown")]}},{key:"concatenateTypedArrays",value:function(){for(var t=0,e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];for(var i=0,o=r;i<o.length;i++){var a=o[i];t+=a.length}for(var s=new Uint8Array(t),u=0,f=0,c=r;f<c.length;f++){var h=c[f];s.set(h,u),u+=h.length}return s}}]),e}(k),O=function(t){function e(){return u()(this,e),p()(this,m()(e).apply(this,arguments))}return b()(e,t),c()(e,null,[{key:"toArrayBuffer",value:function(){e.checkForValue(this.value),this.value="number"==typeof this.value?h.b.strToArrayBuffer(this.value.toString()):new ArrayBuffer}},{key:"fromArrayBuffer",value:function(t){e.checkForValue(this.value),this.value=this.value?parseFloat(h.b.arrayBufferToStr(this.value,!t)):0}}]),e}(k),L=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(u()(this,t),this.value=new ArrayBuffer(0),this.type=t.ARRAY_BUFFER,e&&Object.prototype.hasOwnProperty.call(e,"value")&&Object.prototype.hasOwnProperty.call(e,"type"))this.set(e.value,e.type);else if(e&&null!==r)this.set(e,r);else if(e){var n=t.typeEnum(e.constructor.name);this.set(e,n)}}var e;return c()(t,[{key:"get",value:function(e){var r=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return"string"==typeof e&&(e=t.typeEnum(e)),this.type!==e?Object(h.c)()?(this._translate(e,n),this.value):new Promise(function(t,i){r._translate(e,n).then(function(){t(r.value)}).catch(i)}):this.value}},{key:"set",value:function(e,r){if("string"==typeof r&&(r=t.typeEnum(r)),F.a.debug("Dish type: "+t.enumLookup(r)),this.value=e,this.type=r,!this.valid()){var n=h.b.truncate(JSON.stringify(this.value),25);throw new w(`Data is not a valid ${t.enumLookup(r)}: ${n}`)}}},{key:"presentAs",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.clone();return r.get(t,e)}},{key:"detectDishType",value:function(){var t=new Uint8Array(this.value.slice(0,2048)),e=Object(E.a)(t);return e.length&&e[0].mime&&"text/plain"!==!e[0].mime?e[0].mime:null}},{key:"getTitle",value:(e=a()(i.a.mark(function e(r){var n,o;return i.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:n="",e.t0=this.type,e.next=e.t0===t.FILE?4:e.t0===t.LIST_FILE?6:e.t0===t.JSON?8:e.t0===t.NUMBER?10:e.t0===t.BIG_NUMBER?10:e.t0===t.ARRAY_BUFFER?12:e.t0===t.BYTE_ARRAY?12:15;break;case 4:return n=this.value.name,e.abrupt("break",26);case 6:return n=`${this.value.length} file(s)`,e.abrupt("break",26);case 8:return n="application/json",e.abrupt("break",26);case 10:return n=this.value.toString(),e.abrupt("break",26);case 12:if(null===(n=this.detectDishType())){e.next=15;break}return e.abrupt("break",26);case 15:return e.prev=15,(o=this.clone()).value=o.value.slice(0,256),e.next=20,o.get(t.STRING);case 20:n=e.sent,e.next=26;break;case 23:e.prev=23,e.t1=e.catch(15),F.a.error(`${t.enumLookup(this.type)} cannot be sliced. ${e.t1}`);case 26:return e.abrupt("return",n.slice(0,r));case 27:case"end":return e.stop()}},e,this,[[15,23]])})),function(t){return e.apply(this,arguments)})},{key:"valid",value:function(){switch(this.type){case t.BYTE_ARRAY:if(!(this.value instanceof Uint8Array||this.value instanceof Array))return!1;for(var e=0;e<this.value.length;e++)if("number"!=typeof this.value[e]||this.value[e]<0||this.value[e]>255)return!1;return!0;case t.STRING:case t.HTML:return"string"==typeof this.value;case t.NUMBER:return"number"==typeof this.value;case t.ARRAY_BUFFER:return this.value instanceof ArrayBuffer;case t.BIG_NUMBER:if(x.a.isBigNumber(this.value))return!0;if(Object.keys(this.value).sort().equals(["c","e","s"])){var r=new x.a;return r.c=this.value.c,r.e=this.value.e,r.s=this.value.s,this.value=r,!0}return!1;case t.JSON:return!0;case t.FILE:return this.value instanceof File;case t.LIST_FILE:return this.value instanceof Array&&this.value.reduce(function(t,e){return t&&e instanceof File},!0);default:return!1}}},{key:"clone",value:function(){var e=new t;switch(this.type){case t.STRING:case t.HTML:case t.NUMBER:case t.BIG_NUMBER:e.set(this.value,this.type);break;case t.BYTE_ARRAY:case t.JSON:e.set(JSON.parse(JSON.stringify(this.value)),this.type);break;case t.ARRAY_BUFFER:e.set(this.value.slice(0),this.type);break;case t.FILE:e.set(new File([this.value],this.value.name,{type:this.value.type,lastModified:this.value.lastModified}),this.type);break;case t.LIST_FILE:e.set(this.value.map(function(t){return new File([t],t.name,{type:t.type,lastModified:t.lastModified})}),this.type);break;default:throw new w("Cannot clone Dish, unknown type")}return e}},{key:"_translate",value:function(e){var r=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(F.a.debug(`Translating Dish from ${t.enumLookup(this.type)} to ${t.enumLookup(e)}`),!Object(h.c)())return new Promise(function(n,i){r._toArrayBuffer().then(function(){return r.type=t.ARRAY_BUFFER}).then(function(){r._fromArrayBuffer(e),n()}).catch(i)});this._toArrayBuffer(),this.type=t.ARRAY_BUFFER,this._fromArrayBuffer(e,n)}},{key:"_toArrayBuffer",value:function(){var e=this,r={browser:{[t.STRING]:function(){return Promise.resolve(T.toArrayBuffer.bind(e)())},[t.NUMBER]:function(){return Promise.resolve(O.toArrayBuffer.bind(e)())},[t.HTML]:function(){return Promise.resolve(U.toArrayBuffer.bind(e)())},[t.ARRAY_BUFFER]:function(){return Promise.resolve()},[t.BIG_NUMBER]:function(){return Promise.resolve(C.toArrayBuffer.bind(e)())},[t.JSON]:function(){return Promise.resolve(I.toArrayBuffer.bind(e)())},[t.FILE]:function(){return S.toArrayBuffer.bind(e)()},[t.LIST_FILE]:function(){return Promise.resolve(R.toArrayBuffer.bind(e)())},[t.BYTE_ARRAY]:function(){return Promise.resolve(_.toArrayBuffer.bind(e)())}},node:{[t.STRING]:function(){return T.toArrayBuffer.bind(e)()},[t.NUMBER]:function(){return O.toArrayBuffer.bind(e)()},[t.HTML]:function(){return U.toArrayBuffer.bind(e)()},[t.ARRAY_BUFFER]:function(){},[t.BIG_NUMBER]:function(){return C.toArrayBuffer.bind(e)()},[t.JSON]:function(){return I.toArrayBuffer.bind(e)()},[t.FILE]:function(){return S.toArrayBuffer.bind(e)()},[t.LIST_FILE]:function(){return R.toArrayBuffer.bind(e)()},[t.BYTE_ARRAY]:function(){return _.toArrayBuffer.bind(e)()}}};try{return r[Object(h.c)()?"node":"browser"][this.type]()}catch(e){throw new w(`Error translating from ${t.enumLookup(this.type)} to ArrayBuffer: ${e}`)}}},{key:"_fromArrayBuffer",value:function(e,r){var n=this,i={[t.STRING]:function(){return T.fromArrayBuffer.bind(n)(r)},[t.NUMBER]:function(){return O.fromArrayBuffer.bind(n)(r)},[t.HTML]:function(){return U.fromArrayBuffer.bind(n)(r)},[t.ARRAY_BUFFER]:function(){},[t.BIG_NUMBER]:function(){return C.fromArrayBuffer.bind(n)(r)},[t.JSON]:function(){return I.fromArrayBuffer.bind(n)(r)},[t.FILE]:function(){return S.fromArrayBuffer.bind(n)()},[t.LIST_FILE]:function(){return R.fromArrayBuffer.bind(n)()},[t.BYTE_ARRAY]:function(){return _.fromArrayBuffer.bind(n)()}};try{i[e](),this.type=e}catch(r){throw new w(`Error translating from ArrayBuffer to ${t.enumLookup(e)}: ${r}`)}}},{key:"size",get:function(){switch(this.type){case t.BYTE_ARRAY:case t.STRING:case t.HTML:return this.value.length;case t.NUMBER:case t.BIG_NUMBER:return this.value.toString().length;case t.ARRAY_BUFFER:return this.value.byteLength;case t.JSON:return JSON.stringify(this.value).length;case t.FILE:return this.value.size;case t.LIST_FILE:return this.value.reduce(function(t,e){return t+e.size},0);default:return-1}}}],[{key:"typeEnum",value:function(e){switch(e.toLowerCase()){case"bytearray":case"byte array":return t.BYTE_ARRAY;case"string":return t.STRING;case"number":return t.NUMBER;case"html":return t.HTML;case"arraybuffer":case"array buffer":return t.ARRAY_BUFFER;case"bignumber":case"big number":return t.BIG_NUMBER;case"json":case"object":return t.JSON;case"file":return t.FILE;case"list<file>":return t.LIST_FILE;default:throw new w("Invalid data type string. No matching enum.")}}},{key:"enumLookup",value:function(e){switch(e){case t.BYTE_ARRAY:return"byteArray";case t.STRING:return"string";case t.NUMBER:return"number";case t.HTML:return"html";case t.ARRAY_BUFFER:return"ArrayBuffer";case t.BIG_NUMBER:return"BigNumber";case t.JSON:return"JSON";case t.FILE:return"File";case t.LIST_FILE:return"List<File>";default:throw new w("Invalid data type enum. No matching type.")}}}]),t}();L.BYTE_ARRAY=0,L.STRING=1,L.NUMBER=2,L.HTML=3,L.ARRAY_BUFFER=4,L.BIG_NUMBER=5,L.JSON=6,L.FILE=7,L.LIST_FILE=8;e.a=L},16:function(t,e,r){var n;!function(i){"use strict";var o,a=/^-?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?$/i,s=Math.ceil,u=Math.floor,f="[BigNumber Error] ",c=f+"Number primitive has more than 15 significant digits: ",h=1e14,l=14,p=9007199254740991,d=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9,1e10,1e11,1e12,1e13],m=1e7,g=1e9;function y(t){var e=0|t;return t>0||t===e?e:e-1}function v(t){for(var e,r,n=1,i=t.length,o=t[0]+"";n<i;){for(e=t[n++]+"",r=l-e.length;r--;e="0"+e);o+=e}for(i=o.length;48===o.charCodeAt(--i););return o.slice(0,i+1||1)}function b(t,e){var r,n,i=t.c,o=e.c,a=t.s,s=e.s,u=t.e,f=e.e;if(!a||!s)return null;if(r=i&&!i[0],n=o&&!o[0],r||n)return r?n?0:-s:a;if(a!=s)return a;if(r=a<0,n=u==f,!i||!o)return n?0:!i^r?1:-1;if(!n)return u>f^r?1:-1;for(s=(u=i.length)<(f=o.length)?u:f,a=0;a<s;a++)if(i[a]!=o[a])return i[a]>o[a]^r?1:-1;return u==f?0:u>f^r?1:-1}function w(t,e,r,n){if(t<e||t>r||t!==u(t))throw Error(f+(n||"Argument")+("number"==typeof t?t<e||t>r?" out of range: ":" not an integer: ":" not a primitive number: ")+String(t))}function A(t){var e=t.c.length-1;return y(t.e/l)==e&&t.c[e]%2!=0}function x(t,e){return(t.length>1?t.charAt(0)+"."+t.slice(1):t)+(e<0?"e":"e+")+e}function E(t,e,r){var n,i;if(e<0){for(i=r+".";++e;i+=r);t=i+t}else if(++e>(n=t.length)){for(i=r,e-=n;--e;i+=r);t+=i}else e<n&&(t=t.slice(0,e)+"."+t.slice(e));return t}(o=function t(e){var r,n,i,o,B,F,k,_,C,S=$.prototype={constructor:$,toString:null,valueOf:null},T=new $(1),U=20,I=4,R=-7,O=21,L=-1e7,P=1e7,D=!1,j=1,N=0,M={prefix:"",groupSize:3,secondaryGroupSize:0,groupSeparator:",",decimalSeparator:".",fractionGroupSize:0,fractionGroupSeparator:" ",suffix:""},z="0123456789abcdefghijklmnopqrstuvwxyz";function $(t,e){var r,o,s,f,h,d,m,g,y=this;if(!(y instanceof $))return new $(t,e);if(null==e){if(t&&!0===t._isBigNumber)return y.s=t.s,void(!t.c||t.e>P?y.c=y.e=null:t.e<L?y.c=[y.e=0]:(y.e=t.e,y.c=t.c.slice()));if((d="number"==typeof t)&&0*t==0){if(y.s=1/t<0?(t=-t,-1):1,t===~~t){for(f=0,h=t;h>=10;h/=10,f++);return void(f>P?y.c=y.e=null:(y.e=f,y.c=[t]))}g=String(t)}else{if(!a.test(g=String(t)))return i(y,g,d);y.s=45==g.charCodeAt(0)?(g=g.slice(1),-1):1}(f=g.indexOf("."))>-1&&(g=g.replace(".","")),(h=g.search(/e/i))>0?(f<0&&(f=h),f+=+g.slice(h+1),g=g.substring(0,h)):f<0&&(f=g.length)}else{if(w(e,2,z.length,"Base"),10==e)return V(y=new $(t),U+y.e+1,I);if(g=String(t),d="number"==typeof t){if(0*t!=0)return i(y,g,d,e);if(y.s=1/t<0?(g=g.slice(1),-1):1,$.DEBUG&&g.replace(/^0\.0*|\./,"").length>15)throw Error(c+t)}else y.s=45===g.charCodeAt(0)?(g=g.slice(1),-1):1;for(r=z.slice(0,e),f=h=0,m=g.length;h<m;h++)if(r.indexOf(o=g.charAt(h))<0){if("."==o){if(h>f){f=m;continue}}else if(!s&&(g==g.toUpperCase()&&(g=g.toLowerCase())||g==g.toLowerCase()&&(g=g.toUpperCase()))){s=!0,h=-1,f=0;continue}return i(y,String(t),d,e)}d=!1,(f=(g=n(g,e,10,y.s)).indexOf("."))>-1?g=g.replace(".",""):f=g.length}for(h=0;48===g.charCodeAt(h);h++);for(m=g.length;48===g.charCodeAt(--m););if(g=g.slice(h,++m)){if(m-=h,d&&$.DEBUG&&m>15&&(t>p||t!==u(t)))throw Error(c+y.s*t);if((f=f-h-1)>P)y.c=y.e=null;else if(f<L)y.c=[y.e=0];else{if(y.e=f,y.c=[],h=(f+1)%l,f<0&&(h+=l),h<m){for(h&&y.c.push(+g.slice(0,h)),m-=l;h<m;)y.c.push(+g.slice(h,h+=l));h=l-(g=g.slice(h)).length}else h-=m;for(;h--;g+="0");y.c.push(+g)}}else y.c=[y.e=0]}function Y(t,e,r,n){var i,o,a,s,u;if(null==r?r=I:w(r,0,8),!t.c)return t.toString();if(i=t.c[0],a=t.e,null==e)u=v(t.c),u=1==n||2==n&&(a<=R||a>=O)?x(u,a):E(u,a,"0");else if(o=(t=V(new $(t),e,r)).e,s=(u=v(t.c)).length,1==n||2==n&&(e<=o||o<=R)){for(;s<e;u+="0",s++);u=x(u,o)}else if(e-=a,u=E(u,o,"0"),o+1>s){if(--e>0)for(u+=".";e--;u+="0");}else if((e+=o-s)>0)for(o+1==s&&(u+=".");e--;u+="0");return t.s<0&&i?"-"+u:u}function G(t,e){for(var r,n=1,i=new $(t[0]);n<t.length;n++){if(!(r=new $(t[n])).s){i=r;break}e.call(i,r)&&(i=r)}return i}function q(t,e,r){for(var n=1,i=e.length;!e[--i];e.pop());for(i=e[0];i>=10;i/=10,n++);return(r=n+r*l-1)>P?t.c=t.e=null:r<L?t.c=[t.e=0]:(t.e=r,t.c=e),t}function V(t,e,r,n){var i,o,a,f,c,p,m,g=t.c,y=d;if(g){t:{for(i=1,f=g[0];f>=10;f/=10,i++);if((o=e-i)<0)o+=l,a=e,m=(c=g[p=0])/y[i-a-1]%10|0;else if((p=s((o+1)/l))>=g.length){if(!n)break t;for(;g.length<=p;g.push(0));c=m=0,i=1,a=(o%=l)-l+1}else{for(c=f=g[p],i=1;f>=10;f/=10,i++);m=(a=(o%=l)-l+i)<0?0:c/y[i-a-1]%10|0}if(n=n||e<0||null!=g[p+1]||(a<0?c:c%y[i-a-1]),n=r<4?(m||n)&&(0==r||r==(t.s<0?3:2)):m>5||5==m&&(4==r||n||6==r&&(o>0?a>0?c/y[i-a]:0:g[p-1])%10&1||r==(t.s<0?8:7)),e<1||!g[0])return g.length=0,n?(e-=t.e+1,g[0]=y[(l-e%l)%l],t.e=-e||0):g[0]=t.e=0,t;if(0==o?(g.length=p,f=1,p--):(g.length=p+1,f=y[l-o],g[p]=a>0?u(c/y[i-a]%y[a])*f:0),n)for(;;){if(0==p){for(o=1,a=g[0];a>=10;a/=10,o++);for(a=g[0]+=f,f=1;a>=10;a/=10,f++);o!=f&&(t.e++,g[0]==h&&(g[0]=1));break}if(g[p]+=f,g[p]!=h)break;g[p--]=0,f=1}for(o=g.length;0===g[--o];g.pop());}t.e>P?t.c=t.e=null:t.e<L&&(t.c=[t.e=0])}return t}function H(t){var e,r=t.e;return null===r?t.toString():(e=v(t.c),e=r<=R||r>=O?x(e,r):E(e,r,"0"),t.s<0?"-"+e:e)}return $.clone=t,$.ROUND_UP=0,$.ROUND_DOWN=1,$.ROUND_CEIL=2,$.ROUND_FLOOR=3,$.ROUND_HALF_UP=4,$.ROUND_HALF_DOWN=5,$.ROUND_HALF_EVEN=6,$.ROUND_HALF_CEIL=7,$.ROUND_HALF_FLOOR=8,$.EUCLID=9,$.config=$.set=function(t){var e,r;if(null!=t){if("object"!=typeof t)throw Error(f+"Object expected: "+t);if(t.hasOwnProperty(e="DECIMAL_PLACES")&&(w(r=t[e],0,g,e),U=r),t.hasOwnProperty(e="ROUNDING_MODE")&&(w(r=t[e],0,8,e),I=r),t.hasOwnProperty(e="EXPONENTIAL_AT")&&((r=t[e])&&r.pop?(w(r[0],-g,0,e),w(r[1],0,g,e),R=r[0],O=r[1]):(w(r,-g,g,e),R=-(O=r<0?-r:r))),t.hasOwnProperty(e="RANGE"))if((r=t[e])&&r.pop)w(r[0],-g,-1,e),w(r[1],1,g,e),L=r[0],P=r[1];else{if(w(r,-g,g,e),!r)throw Error(f+e+" cannot be zero: "+r);L=-(P=r<0?-r:r)}if(t.hasOwnProperty(e="CRYPTO")){if((r=t[e])!==!!r)throw Error(f+e+" not true or false: "+r);if(r){if("undefined"==typeof crypto||!crypto||!crypto.getRandomValues&&!crypto.randomBytes)throw D=!r,Error(f+"crypto unavailable");D=r}else D=r}if(t.hasOwnProperty(e="MODULO_MODE")&&(w(r=t[e],0,9,e),j=r),t.hasOwnProperty(e="POW_PRECISION")&&(w(r=t[e],0,g,e),N=r),t.hasOwnProperty(e="FORMAT")){if("object"!=typeof(r=t[e]))throw Error(f+e+" not an object: "+r);M=r}if(t.hasOwnProperty(e="ALPHABET")){if("string"!=typeof(r=t[e])||/^.$|[+-.\s]|(.).*\1/.test(r))throw Error(f+e+" invalid: "+r);z=r}}return{DECIMAL_PLACES:U,ROUNDING_MODE:I,EXPONENTIAL_AT:[R,O],RANGE:[L,P],CRYPTO:D,MODULO_MODE:j,POW_PRECISION:N,FORMAT:M,ALPHABET:z}},$.isBigNumber=function(t){if(!t||!0!==t._isBigNumber)return!1;if(!$.DEBUG)return!0;var e,r,n=t.c,i=t.e,o=t.s;t:if("[object Array]"=={}.toString.call(n)){if((1===o||-1===o)&&i>=-g&&i<=g&&i===u(i)){if(0===n[0]){if(0===i&&1===n.length)return!0;break t}if((e=(i+1)%l)<1&&(e+=l),String(n[0]).length==e){for(e=0;e<n.length;e++)if((r=n[e])<0||r>=h||r!==u(r))break t;if(0!==r)return!0}}}else if(null===n&&null===i&&(null===o||1===o||-1===o))return!0;throw Error(f+"Invalid BigNumber: "+t)},$.maximum=$.max=function(){return G(arguments,S.lt)},$.minimum=$.min=function(){return G(arguments,S.gt)},$.random=(o=9007199254740992*Math.random()&2097151?function(){return u(9007199254740992*Math.random())}:function(){return 8388608*(1073741824*Math.random()|0)+(8388608*Math.random()|0)},function(t){var e,r,n,i,a,c=0,h=[],p=new $(T);if(null==t?t=U:w(t,0,g),i=s(t/l),D)if(crypto.getRandomValues){for(e=crypto.getRandomValues(new Uint32Array(i*=2));c<i;)(a=131072*e[c]+(e[c+1]>>>11))>=9e15?(r=crypto.getRandomValues(new Uint32Array(2)),e[c]=r[0],e[c+1]=r[1]):(h.push(a%1e14),c+=2);c=i/2}else{if(!crypto.randomBytes)throw D=!1,Error(f+"crypto unavailable");for(e=crypto.randomBytes(i*=7);c<i;)(a=281474976710656*(31&e[c])+1099511627776*e[c+1]+4294967296*e[c+2]+16777216*e[c+3]+(e[c+4]<<16)+(e[c+5]<<8)+e[c+6])>=9e15?crypto.randomBytes(7).copy(e,c):(h.push(a%1e14),c+=7);c=i/7}if(!D)for(;c<i;)(a=o())<9e15&&(h[c++]=a%1e14);for(i=h[--c],t%=l,i&&t&&(a=d[l-t],h[c]=u(i/a)*a);0===h[c];h.pop(),c--);if(c<0)h=[n=0];else{for(n=-1;0===h[0];h.splice(0,1),n-=l);for(c=1,a=h[0];a>=10;a/=10,c++);c<l&&(n-=l-c)}return p.e=n,p.c=h,p}),$.sum=function(){for(var t=1,e=arguments,r=new $(e[0]);t<e.length;)r=r.plus(e[t++]);return r},n=function(){function t(t,e,r,n){for(var i,o,a=[0],s=0,u=t.length;s<u;){for(o=a.length;o--;a[o]*=e);for(a[0]+=n.indexOf(t.charAt(s++)),i=0;i<a.length;i++)a[i]>r-1&&(null==a[i+1]&&(a[i+1]=0),a[i+1]+=a[i]/r|0,a[i]%=r)}return a.reverse()}return function(e,n,i,o,a){var s,u,f,c,h,l,p,d,m=e.indexOf("."),g=U,y=I;for(m>=0&&(c=N,N=0,e=e.replace(".",""),l=(d=new $(n)).pow(e.length-m),N=c,d.c=t(E(v(l.c),l.e,"0"),10,i,"0123456789"),d.e=d.c.length),f=c=(p=t(e,n,i,a?(s=z,"0123456789"):(s="0123456789",z))).length;0==p[--c];p.pop());if(!p[0])return s.charAt(0);if(m<0?--f:(l.c=p,l.e=f,l.s=o,p=(l=r(l,d,g,y,i)).c,h=l.r,f=l.e),m=p[u=f+g+1],c=i/2,h=h||u<0||null!=p[u+1],h=y<4?(null!=m||h)&&(0==y||y==(l.s<0?3:2)):m>c||m==c&&(4==y||h||6==y&&1&p[u-1]||y==(l.s<0?8:7)),u<1||!p[0])e=h?E(s.charAt(1),-g,s.charAt(0)):s.charAt(0);else{if(p.length=u,h)for(--i;++p[--u]>i;)p[u]=0,u||(++f,p=[1].concat(p));for(c=p.length;!p[--c];);for(m=0,e="";m<=c;e+=s.charAt(p[m++]));e=E(e,f,s.charAt(0))}return e}}(),r=function(){function t(t,e,r){var n,i,o,a,s=0,u=t.length,f=e%m,c=e/m|0;for(t=t.slice();u--;)s=((i=f*(o=t[u]%m)+(n=c*o+(a=t[u]/m|0)*f)%m*m+s)/r|0)+(n/m|0)+c*a,t[u]=i%r;return s&&(t=[s].concat(t)),t}function e(t,e,r,n){var i,o;if(r!=n)o=r>n?1:-1;else for(i=o=0;i<r;i++)if(t[i]!=e[i]){o=t[i]>e[i]?1:-1;break}return o}function r(t,e,r,n){for(var i=0;r--;)t[r]-=i,i=t[r]<e[r]?1:0,t[r]=i*n+t[r]-e[r];for(;!t[0]&&t.length>1;t.splice(0,1));}return function(n,i,o,a,s){var f,c,p,d,m,g,v,b,w,A,x,E,B,F,k,_,C,S=n.s==i.s?1:-1,T=n.c,U=i.c;if(!(T&&T[0]&&U&&U[0]))return new $(n.s&&i.s&&(T?!U||T[0]!=U[0]:U)?T&&0==T[0]||!U?0*S:S/0:NaN);for(w=(b=new $(S)).c=[],S=o+(c=n.e-i.e)+1,s||(s=h,c=y(n.e/l)-y(i.e/l),S=S/l|0),p=0;U[p]==(T[p]||0);p++);if(U[p]>(T[p]||0)&&c--,S<0)w.push(1),d=!0;else{for(F=T.length,_=U.length,p=0,S+=2,(m=u(s/(U[0]+1)))>1&&(U=t(U,m,s),T=t(T,m,s),_=U.length,F=T.length),B=_,x=(A=T.slice(0,_)).length;x<_;A[x++]=0);C=U.slice(),C=[0].concat(C),k=U[0],U[1]>=s/2&&k++;do{if(m=0,(f=e(U,A,_,x))<0){if(E=A[0],_!=x&&(E=E*s+(A[1]||0)),(m=u(E/k))>1)for(m>=s&&(m=s-1),v=(g=t(U,m,s)).length,x=A.length;1==e(g,A,v,x);)m--,r(g,_<v?C:U,v,s),v=g.length,f=1;else 0==m&&(f=m=1),v=(g=U.slice()).length;if(v<x&&(g=[0].concat(g)),r(A,g,x,s),x=A.length,-1==f)for(;e(U,A,_,x)<1;)m++,r(A,_<x?C:U,x,s),x=A.length}else 0===f&&(m++,A=[0]);w[p++]=m,A[0]?A[x++]=T[B]||0:(A=[T[B]],x=1)}while((B++<F||null!=A[0])&&S--);d=null!=A[0],w[0]||w.splice(0,1)}if(s==h){for(p=1,S=w[0];S>=10;S/=10,p++);V(b,o+(b.e=p+c*l-1)+1,a,d)}else b.e=c,b.r=+d;return b}}(),B=/^(-?)0([xbo])(?=\w[\w.]*$)/i,F=/^([^.]+)\.$/,k=/^\.([^.]+)$/,_=/^-?(Infinity|NaN)$/,C=/^\s*\+(?=[\w.])|^\s+|\s+$/g,i=function(t,e,r,n){var i,o=r?e:e.replace(C,"");if(_.test(o))t.s=isNaN(o)?null:o<0?-1:1;else{if(!r&&(o=o.replace(B,function(t,e,r){return i="x"==(r=r.toLowerCase())?16:"b"==r?2:8,n&&n!=i?t:e}),n&&(i=n,o=o.replace(F,"$1").replace(k,"0.$1")),e!=o))return new $(o,i);if($.DEBUG)throw Error(f+"Not a"+(n?" base "+n:"")+" number: "+e);t.s=null}t.c=t.e=null},S.absoluteValue=S.abs=function(){var t=new $(this);return t.s<0&&(t.s=1),t},S.comparedTo=function(t,e){return b(this,new $(t,e))},S.decimalPlaces=S.dp=function(t,e){var r,n,i,o=this;if(null!=t)return w(t,0,g),null==e?e=I:w(e,0,8),V(new $(o),t+o.e+1,e);if(!(r=o.c))return null;if(n=((i=r.length-1)-y(this.e/l))*l,i=r[i])for(;i%10==0;i/=10,n--);return n<0&&(n=0),n},S.dividedBy=S.div=function(t,e){return r(this,new $(t,e),U,I)},S.dividedToIntegerBy=S.idiv=function(t,e){return r(this,new $(t,e),0,1)},S.exponentiatedBy=S.pow=function(t,e){var r,n,i,o,a,c,h,p,d=this;if((t=new $(t)).c&&!t.isInteger())throw Error(f+"Exponent not an integer: "+H(t));if(null!=e&&(e=new $(e)),a=t.e>14,!d.c||!d.c[0]||1==d.c[0]&&!d.e&&1==d.c.length||!t.c||!t.c[0])return p=new $(Math.pow(+H(d),a?2-A(t):+H(t))),e?p.mod(e):p;if(c=t.s<0,e){if(e.c?!e.c[0]:!e.s)return new $(NaN);(n=!c&&d.isInteger()&&e.isInteger())&&(d=d.mod(e))}else{if(t.e>9&&(d.e>0||d.e<-1||(0==d.e?d.c[0]>1||a&&d.c[1]>=24e7:d.c[0]<8e13||a&&d.c[0]<=9999975e7)))return o=d.s<0&&A(t)?-0:0,d.e>-1&&(o=1/o),new $(c?1/o:o);N&&(o=s(N/l+2))}for(a?(r=new $(.5),c&&(t.s=1),h=A(t)):h=(i=Math.abs(+H(t)))%2,p=new $(T);;){if(h){if(!(p=p.times(d)).c)break;o?p.c.length>o&&(p.c.length=o):n&&(p=p.mod(e))}if(i){if(0===(i=u(i/2)))break;h=i%2}else if(V(t=t.times(r),t.e+1,1),t.e>14)h=A(t);else{if(0===(i=+H(t)))break;h=i%2}d=d.times(d),o?d.c&&d.c.length>o&&(d.c.length=o):n&&(d=d.mod(e))}return n?p:(c&&(p=T.div(p)),e?p.mod(e):o?V(p,N,I,void 0):p)},S.integerValue=function(t){var e=new $(this);return null==t?t=I:w(t,0,8),V(e,e.e+1,t)},S.isEqualTo=S.eq=function(t,e){return 0===b(this,new $(t,e))},S.isFinite=function(){return!!this.c},S.isGreaterThan=S.gt=function(t,e){return b(this,new $(t,e))>0},S.isGreaterThanOrEqualTo=S.gte=function(t,e){return 1===(e=b(this,new $(t,e)))||0===e},S.isInteger=function(){return!!this.c&&y(this.e/l)>this.c.length-2},S.isLessThan=S.lt=function(t,e){return b(this,new $(t,e))<0},S.isLessThanOrEqualTo=S.lte=function(t,e){return-1===(e=b(this,new $(t,e)))||0===e},S.isNaN=function(){return!this.s},S.isNegative=function(){return this.s<0},S.isPositive=function(){return this.s>0},S.isZero=function(){return!!this.c&&0==this.c[0]},S.minus=function(t,e){var r,n,i,o,a=this,s=a.s;if(e=(t=new $(t,e)).s,!s||!e)return new $(NaN);if(s!=e)return t.s=-e,a.plus(t);var u=a.e/l,f=t.e/l,c=a.c,p=t.c;if(!u||!f){if(!c||!p)return c?(t.s=-e,t):new $(p?a:NaN);if(!c[0]||!p[0])return p[0]?(t.s=-e,t):new $(c[0]?a:3==I?-0:0)}if(u=y(u),f=y(f),c=c.slice(),s=u-f){for((o=s<0)?(s=-s,i=c):(f=u,i=p),i.reverse(),e=s;e--;i.push(0));i.reverse()}else for(n=(o=(s=c.length)<(e=p.length))?s:e,s=e=0;e<n;e++)if(c[e]!=p[e]){o=c[e]<p[e];break}if(o&&(i=c,c=p,p=i,t.s=-t.s),(e=(n=p.length)-(r=c.length))>0)for(;e--;c[r++]=0);for(e=h-1;n>s;){if(c[--n]<p[n]){for(r=n;r&&!c[--r];c[r]=e);--c[r],c[n]+=h}c[n]-=p[n]}for(;0==c[0];c.splice(0,1),--f);return c[0]?q(t,c,f):(t.s=3==I?-1:1,t.c=[t.e=0],t)},S.modulo=S.mod=function(t,e){var n,i,o=this;return t=new $(t,e),!o.c||!t.s||t.c&&!t.c[0]?new $(NaN):!t.c||o.c&&!o.c[0]?new $(o):(9==j?(i=t.s,t.s=1,n=r(o,t,0,3),t.s=i,n.s*=i):n=r(o,t,0,j),(t=o.minus(n.times(t))).c[0]||1!=j||(t.s=o.s),t)},S.multipliedBy=S.times=function(t,e){var r,n,i,o,a,s,u,f,c,p,d,g,v,b,w,A=this,x=A.c,E=(t=new $(t,e)).c;if(!(x&&E&&x[0]&&E[0]))return!A.s||!t.s||x&&!x[0]&&!E||E&&!E[0]&&!x?t.c=t.e=t.s=null:(t.s*=A.s,x&&E?(t.c=[0],t.e=0):t.c=t.e=null),t;for(n=y(A.e/l)+y(t.e/l),t.s*=A.s,(u=x.length)<(p=E.length)&&(v=x,x=E,E=v,i=u,u=p,p=i),i=u+p,v=[];i--;v.push(0));for(b=h,w=m,i=p;--i>=0;){for(r=0,d=E[i]%w,g=E[i]/w|0,o=i+(a=u);o>i;)r=((f=d*(f=x[--a]%w)+(s=g*f+(c=x[a]/w|0)*d)%w*w+v[o]+r)/b|0)+(s/w|0)+g*c,v[o--]=f%b;v[o]=r}return r?++n:v.splice(0,1),q(t,v,n)},S.negated=function(){var t=new $(this);return t.s=-t.s||null,t},S.plus=function(t,e){var r,n=this,i=n.s;if(e=(t=new $(t,e)).s,!i||!e)return new $(NaN);if(i!=e)return t.s=-e,n.minus(t);var o=n.e/l,a=t.e/l,s=n.c,u=t.c;if(!o||!a){if(!s||!u)return new $(i/0);if(!s[0]||!u[0])return u[0]?t:new $(s[0]?n:0*i)}if(o=y(o),a=y(a),s=s.slice(),i=o-a){for(i>0?(a=o,r=u):(i=-i,r=s),r.reverse();i--;r.push(0));r.reverse()}for((i=s.length)-(e=u.length)<0&&(r=u,u=s,s=r,e=i),i=0;e;)i=(s[--e]=s[e]+u[e]+i)/h|0,s[e]=h===s[e]?0:s[e]%h;return i&&(s=[i].concat(s),++a),q(t,s,a)},S.precision=S.sd=function(t,e){var r,n,i,o=this;if(null!=t&&t!==!!t)return w(t,1,g),null==e?e=I:w(e,0,8),V(new $(o),t,e);if(!(r=o.c))return null;if(n=(i=r.length-1)*l+1,i=r[i]){for(;i%10==0;i/=10,n--);for(i=r[0];i>=10;i/=10,n++);}return t&&o.e+1>n&&(n=o.e+1),n},S.shiftedBy=function(t){return w(t,-p,p),this.times("1e"+t)},S.squareRoot=S.sqrt=function(){var t,e,n,i,o,a=this,s=a.c,u=a.s,f=a.e,c=U+4,h=new $("0.5");if(1!==u||!s||!s[0])return new $(!u||u<0&&(!s||s[0])?NaN:s?a:1/0);if(0==(u=Math.sqrt(+H(a)))||u==1/0?(((e=v(s)).length+f)%2==0&&(e+="0"),u=Math.sqrt(+e),f=y((f+1)/2)-(f<0||f%2),n=new $(e=u==1/0?"1e"+f:(e=u.toExponential()).slice(0,e.indexOf("e")+1)+f)):n=new $(u+""),n.c[0])for((u=(f=n.e)+c)<3&&(u=0);;)if(o=n,n=h.times(o.plus(r(a,o,c,1))),v(o.c).slice(0,u)===(e=v(n.c)).slice(0,u)){if(n.e<f&&--u,"9999"!=(e=e.slice(u-3,u+1))&&(i||"4999"!=e)){+e&&(+e.slice(1)||"5"!=e.charAt(0))||(V(n,n.e+U+2,1),t=!n.times(n).eq(a));break}if(!i&&(V(o,o.e+U+2,0),o.times(o).eq(a))){n=o;break}c+=4,u+=4,i=1}return V(n,n.e+U+1,I,t)},S.toExponential=function(t,e){return null!=t&&(w(t,0,g),t++),Y(this,t,e,1)},S.toFixed=function(t,e){return null!=t&&(w(t,0,g),t=t+this.e+1),Y(this,t,e)},S.toFormat=function(t,e,r){var n,i=this;if(null==r)null!=t&&e&&"object"==typeof e?(r=e,e=null):t&&"object"==typeof t?(r=t,t=e=null):r=M;else if("object"!=typeof r)throw Error(f+"Argument not an object: "+r);if(n=i.toFixed(t,e),i.c){var o,a=n.split("."),s=+r.groupSize,u=+r.secondaryGroupSize,c=r.groupSeparator||"",h=a[0],l=a[1],p=i.s<0,d=p?h.slice(1):h,m=d.length;if(u&&(o=s,s=u,u=o,m-=o),s>0&&m>0){for(o=m%s||s,h=d.substr(0,o);o<m;o+=s)h+=c+d.substr(o,s);u>0&&(h+=c+d.slice(o)),p&&(h="-"+h)}n=l?h+(r.decimalSeparator||"")+((u=+r.fractionGroupSize)?l.replace(new RegExp("\\d{"+u+"}\\B","g"),"$&"+(r.fractionGroupSeparator||"")):l):h}return(r.prefix||"")+n+(r.suffix||"")},S.toFraction=function(t){var e,n,i,o,a,s,u,c,h,p,m,g,y=this,b=y.c;if(null!=t&&(!(u=new $(t)).isInteger()&&(u.c||1!==u.s)||u.lt(T)))throw Error(f+"Argument "+(u.isInteger()?"out of range: ":"not an integer: ")+H(u));if(!b)return new $(y);for(e=new $(T),h=n=new $(T),i=c=new $(T),g=v(b),a=e.e=g.length-y.e-1,e.c[0]=d[(s=a%l)<0?l+s:s],t=!t||u.comparedTo(e)>0?a>0?e:h:u,s=P,P=1/0,u=new $(g),c.c[0]=0;p=r(u,e,0,1),1!=(o=n.plus(p.times(i))).comparedTo(t);)n=i,i=o,h=c.plus(p.times(o=h)),c=o,e=u.minus(p.times(o=e)),u=o;return o=r(t.minus(n),i,0,1),c=c.plus(o.times(h)),n=n.plus(o.times(i)),c.s=h.s=y.s,m=r(h,i,a*=2,I).minus(y).abs().comparedTo(r(c,n,a,I).minus(y).abs())<1?[h,i]:[c,n],P=s,m},S.toNumber=function(){return+H(this)},S.toPrecision=function(t,e){return null!=t&&w(t,1,g),Y(this,t,e,2)},S.toString=function(t){var e,r=this,i=r.s,o=r.e;return null===o?i?(e="Infinity",i<0&&(e="-"+e)):e="NaN":(null==t?e=o<=R||o>=O?x(v(r.c),o):E(v(r.c),o,"0"):10===t?e=E(v((r=V(new $(r),U+o+1,I)).c),r.e,"0"):(w(t,2,z.length,"Base"),e=n(E(v(r.c),o,"0"),10,t,i,!0)),i<0&&r.c[0]&&(e="-"+e)),e},S.valueOf=S.toJSON=function(){return H(this)},S._isBigNumber=!0,null!=e&&$.set(e),$}()).default=o.BigNumber=o,void 0===(n=function(){return o}.call(e,r,e,t))||(t.exports=n)}()},17:function(t,e,r){"use strict";r.d(e,"b",function(){return i}),r.d(e,"c",function(){return o}),r.d(e,"a",function(){return a});var n=r(0);
/**
 * Hexadecimal functions.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2016
 * @license Apache-2.0
 */function i(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:" ",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2;if(!t)return"";t instanceof ArrayBuffer&&(t=new Uint8Array(t));for(var n="",i=0;i<t.length;i++)n+=t[i].toString(16).padStart(r,"0")+e;return"0x"===e&&(n="0x"+n),"\\x"===e&&(n="\\x"+n),e.length?n.slice(0,-e.length):n}function o(t){if(!t)return"";t instanceof ArrayBuffer&&(t=new Uint8Array(t));for(var e=[],r=0;r<t.length;r++)e.push((t[r]>>>4).toString(16)),e.push((15&t[r]).toString(16));return e.join("")}function a(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Auto",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2;if("None"!==e){var i="Auto"===e?/[^a-f\d]/gi:n.b.regexRep(e);t=t.replace(i,"")}for(var o=[],a=0;a<t.length;a+=r)o.push(parseInt(t.substr(a,r),16));return o}["Auto"].concat(["Space","Percent","Comma","Semi-colon","Colon","Line feed","CRLF","0x","\\x","None"])},18:function(t,e,r){"use strict";var n=r(13),i=r.n(n),o=r(10),a={Images:[{name:"Joint Photographic Experts Group image",extension:"jpg,jpeg,jpe,thm,mpo",mime:"image/jpeg",description:"",signature:{0:255,1:216,2:255,3:[192,196,219,221,224,225,226,227,228,229,231,232,234,235,236,237,238,254]},extractor:function(t,e){var r=new o.a(t.slice(e));for(;r.hasMore();){var n=r.getBytes(2);if(255!==n[0])throw new Error(`Invalid marker while parsing JPEG at pos ${r.position}: ${n}`);var i=0;switch(n[1]){case 216:case 1:break;case 217:return r.carve();case 192:case 193:case 194:case 195:case 196:case 197:case 198:case 199:case 200:case 201:case 202:case 203:case 204:case 205:case 206:case 207:case 219:case 222:case 224:case 225:case 226:case 227:case 228:case 229:case 230:case 231:case 232:case 233:case 234:case 235:case 236:case 237:case 238:case 239:case 254:i=r.readInt(2,"be"),r.position+=i-2;break;case 223:r.position++;break;case 220:case 221:r.position+=2;break;case 218:i=r.readInt(2,"be"),r.position+=i-2,r.continueUntil(255);break;case 0:case 208:case 209:case 210:case 211:case 212:case 213:case 214:case 215:default:r.continueUntil(255)}}throw new Error("Unable to parse JPEG successfully")}},{name:"Graphics Interchange Format image",extension:"gif",mime:"image/gif",description:"",signature:{0:71,1:73,2:70,3:56,4:[55,57],5:97},extractor:null},{name:"Portable Network Graphics image",extension:"png",mime:"image/png",description:"",signature:{0:137,1:80,2:78,3:71,4:13,5:10,6:26,7:10},extractor:function(t,e){var r=new o.a(t.slice(e));r.moveForwardsBy(8);var n=0,i="";for(;"IEND"!==i;)n=r.readInt(4,"be"),i=r.readString(4),r.moveForwardsBy(n+4);return r.carve()}},{name:"WEBP Image",extension:"webp",mime:"image/webp",description:"",signature:{8:87,9:69,10:66,11:80},extractor:null},{name:"Camera Image File Format",extension:"crw",mime:"image/x-canon-crw",description:"",signature:{6:72,7:69,8:65,9:80,10:67,11:67,12:68,13:82},extractor:null},{name:"Canon CR2 raw image",extension:"cr2",mime:"image/x-canon-cr2",description:"",signature:[{0:73,1:73,2:42,3:0,8:67,9:82},{0:77,1:77,2:0,3:42,8:67,9:82}],extractor:null},{name:"Tagged Image File Format image",extension:"tif",mime:"image/tiff",description:"",signature:[{0:73,1:73,2:42,3:0},{0:77,1:77,2:0,3:42}],extractor:null},{name:"Bitmap image",extension:"bmp",mime:"image/bmp",description:"",signature:{0:66,1:77,7:0,9:0,14:[12,40,56,64,108,124],15:0,16:0,17:0},extractor:function(t,e){var r=new o.a(t.slice(e));r.moveForwardsBy(2);var n=r.readInt(4,"le");return r.moveForwardsBy(n-6),r.carve()}},{name:"JPEG Extended Range image",extension:"jxr",mime:"image/vnd.ms-photo",description:"",signature:{0:73,1:73,2:188},extractor:null},{name:"Photoshop image",extension:"psd",mime:"image/vnd.adobe.photoshop",description:"",signature:{0:56,1:66,2:80,3:83,4:0,5:1,6:0,7:0,8:0,9:0,10:0,11:0},extractor:null},{name:"Photoshop Large Document",extension:"psb",mime:"application/x-photoshop",description:"",signature:{0:56,1:66,2:80,3:83,4:0,5:2,6:0,7:0,8:0,9:0,10:0,11:0,12:0},extractor:null},{name:"Paint Shop Pro image",extension:"psp",mime:"image/psp",description:"",signature:[{0:80,1:97,2:105,3:110,4:116,5:32,6:83,7:104,8:111,9:112,10:32,11:80,12:114,13:111,14:32,15:73,16:109},{0:126,1:66,2:75,3:0}],extractor:null},{name:"Icon image",extension:"ico",mime:"image/x-icon",description:"",signature:{0:0,1:0,2:1,3:0,4:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],5:0,6:[16,32,48,64,128],7:[16,32,48,64,128],9:0,10:[0,1]},extractor:null},{name:"Radiance High Dynamic Range image",extension:"hdr",mime:"image/vnd.radiance",description:"",signature:{0:35,1:63,2:82,3:65,4:68,5:73,6:65,7:78,8:67,9:69,10:10},extractor:null},{name:"Sony ARW image",extension:"arw",mime:"image/x-raw",description:"",signature:{0:5,1:0,2:0,3:0,4:65,5:87,6:49,7:46},extractor:null},{name:"Fujifilm Raw Image",extension:"raf",mime:"image/x-raw",description:"",signature:{0:70,1:85,2:74,3:73,4:70,5:73,6:76,7:77,8:67,9:67,10:68,11:45,12:82,13:65,14:87},extractor:null},{name:"Minolta RAW image",extension:"mrw",mime:"image/x-raw",description:"",signature:{0:0,1:77,2:82,3:77},extractor:null},{name:"Adobe Bridge Thumbnail Cache",extension:"bct",mime:"application/octet-stream",description:"",signature:{0:108,1:110,2:98,3:116,4:2,5:0,6:0,7:0},extractor:null},{name:"Microsoft Document Imaging",extension:"mdi",mime:"image/vnd.ms-modi",description:"",signature:{0:69,1:80,2:42,3:0},extractor:null}],Video:[{name:"Matroska Multimedia Container",extension:"mkv",mime:"video/x-matroska",description:"",signature:{31:109,32:97,33:116,34:114,35:111,36:115,37:107,38:97},extractor:null},{name:"WEBM video",extension:"webm",mime:"video/webm",description:"",signature:{0:26,1:69,2:223,3:163},extractor:null},{name:"MPEG-4 video",extension:"mp4",mime:"video/mp4",description:"",signature:[{0:0,1:0,2:0,3:[24,32],4:102,5:116,6:121,7:112},{0:51,1:103,2:112,3:53},{0:0,1:0,2:0,3:28,4:102,5:116,6:121,7:112,8:109,9:112,10:52,11:50,16:109,17:112,18:52,19:49,20:109,21:112,22:52,23:50,24:105,25:115,26:111,27:109}],extractor:null},{name:"M4V video",extension:"m4v",mime:"video/x-m4v",description:"",signature:{0:0,1:0,2:0,3:28,4:102,5:116,6:121,7:112,8:77,9:52,10:86},extractor:null},{name:"Quicktime video",extension:"mov",mime:"video/quicktime",description:"",signature:{0:0,1:0,2:0,3:20,4:102,5:116,6:121,7:112},extractor:null},{name:"Audio Video Interleave",extension:"avi",mime:"video/x-msvideo",description:"",signature:{0:82,1:73,2:70,3:70,8:65,9:86,10:73},extractor:null},{name:"Windows Media Video",extension:"wmv",mime:"video/x-ms-wmv",description:"",signature:{0:48,1:38,2:178,3:117,4:142,5:102,6:207,7:17,8:166,9:217},extractor:null},{name:"MPEG video",extension:"mpg",mime:"video/mpeg",description:"",signature:{0:0,1:0,2:1,3:186},extractor:null},{name:"Flash Video",extension:"flv",mime:"video/x-flv",description:"",signature:{0:70,1:76,2:86,3:1},extractor:function(t,e){var r=new o.a(t.slice(e));r.moveForwardsBy(5);var n=r.readInt(4,"be");r.moveForwardsBy(n-9);var i=-11;for(;r.hasMore();){var a=r.readInt(4,"be"),s=r.readInt(1);if([8,9,18].indexOf(s)<0){r.moveBackwardsBy(1);break}if(a!==i+11){r.moveBackwardsBy(i+11+5);break}i=r.readInt(3,"be"),r.moveForwardsBy(7+i)}return r.carve()}}],Audio:[{name:"Waveform Audio",extension:"wav",mime:"audio/x-wav",description:"",signature:{0:82,1:73,2:70,3:70,8:87,9:65,10:86,11:69},extractor:null},{name:"OGG audio",extension:"ogg",mime:"audio/ogg",description:"",signature:{0:79,1:103,2:103,3:83},extractor:null},{name:"Musical Instrument Digital Interface audio",extension:"midi",mime:"audio/midi",description:"",signature:{0:77,1:84,2:104,3:100},extractor:null},{name:"MPEG-3 audio",extension:"mp3",mime:"audio/mpeg",description:"",signature:[{0:73,1:68,2:51},{0:255,1:251}],extractor:null},{name:"MPEG-4 Part 14 audio",extension:"m4a",mime:"audio/m4a",description:"",signature:[{4:102,5:116,6:121,7:112,8:77,9:52,10:65},{0:77,1:52,2:65,3:32}],extractor:null},{name:"Free Lossless Audio Codec",extension:"flac",mime:"audio/x-flac",description:"",signature:{0:102,1:76,2:97,3:67},extractor:null},{name:"Adaptive Multi-Rate audio codec",extension:"amr",mime:"audio/amr",description:"",signature:{0:35,1:33,2:65,3:77,4:82,5:10},extractor:null},{name:"Audacity",extension:"au",mime:"audio/x-au",description:"",signature:{0:100,1:110,2:115,3:46,24:65,25:117,26:100,27:97,28:99,29:105,30:116,31:121,32:66,33:108,34:111,35:99,36:107,37:70,38:105,39:108,40:101},extractor:null},{name:"Audacity Block",extension:"auf",mime:"application/octet-stream",description:"",signature:{0:65,1:117,2:100,3:97,4:99,5:105,6:116,7:121,8:66,9:108,10:111,11:99,12:107,13:70,14:105,15:108,16:101},extractor:null},{name:"Audio Interchange File",extension:"aif",mime:"audio/x-aiff",description:"",signature:{0:70,1:79,2:82,3:77,8:65,9:73,10:70,11:70},extractor:null},{name:"Audio Interchange File (compressed)",extension:"aifc",mime:"audio/x-aifc",description:"",signature:{0:70,1:79,2:82,3:77,8:65,9:73,10:70,11:67},extractor:null}],Documents:[{name:"Portable Document Format",extension:"pdf",mime:"application/pdf",description:"",signature:{0:37,1:80,2:68,3:70},extractor:function(t,e){var r=new o.a(t.slice(e));return r.continueUntil([37,37,69,79,70]),r.moveForwardsBy(5),r.consumeIf(13),r.consumeIf(10),r.carve()}},{name:"PostScript",extension:"ps",mime:"application/postscript",description:"",signature:{0:37,1:33},extractor:null},{name:"Rich Text Format",extension:"rtf",mime:"application/rtf",description:"",signature:{0:123,1:92,2:114,3:116,4:102},extractor:function(t,e){var r=new o.a(t.slice(e)),n=0;if(123!==r.readInt(1))throw new Error("Not a valid RTF file");n++;for(;n>0&&r.hasMore();)switch(r.readInt(1)){case 123:n++;break;case 125:n--;break;case 92:r.consumeIf(92),r.position++}return r.carve()}},{name:"Microsoft Office documents/OLE2",extension:"ole2,doc,xls,dot,ppt,xla,ppa,pps,pot,msi,sdw,db,vsd,msg",mime:"application/msword,application/vnd.ms-excel,application/vnd.ms-powerpoint",description:"Microsoft Office documents",signature:{0:208,1:207,2:17,3:224,4:161,5:177,6:26,7:225},extractor:null},{name:"Microsoft Office 2007+ documents",extension:"docx,xlsx,pptx",mime:"application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.openxmlformats-officedocument.presentationml.presentation",description:"",signature:{38:95,39:84,40:121,41:112,42:101,43:115,44:93,45:46,46:120,47:109,48:108},extractor:s},{name:"EPUB e-book",extension:"epub",mime:"application/epub+zip",description:"",signature:{0:80,1:75,2:3,3:4,30:109,31:105,32:109,33:101,34:116,35:121,36:112,37:101,38:97,39:112,40:112,41:108,42:105,43:99,44:97,45:116,46:105,47:111,48:110,49:47,50:101,51:112,52:117,53:98,54:43,55:122,56:105,57:112},extractor:s}],Applications:[{name:"Windows Portable Executable",extension:"exe,dll,drv,vxd,sys,ocx,vbx,com,fon,scr",mime:"application/x-msdownload",description:"",signature:{0:77,1:90,3:[0,1,2],5:[0,1,2]},extractor:function(t,e){var r=new o.a(t.slice(e));r.moveTo(60);var n=r.readInt(4,"le");r.moveTo(n),r.moveForwardsBy(6);var i=r.readInt(2,"le");r.moveForwardsBy(12);var a=r.readInt(2,"le");r.moveForwardsBy(2+a),r.moveForwardsBy(40*(i-1)),r.moveForwardsBy(16);var s=r.readInt(4,"le"),u=r.readInt(4,"le");return r.moveTo(u+s),r.carve()}},{name:"Executable and Linkable Format file",extension:"elf,bin,axf,o,prx,so",mime:"application/x-executable",description:"Executable and Linkable Format file. No standard file extension.",signature:{0:127,1:69,2:76,3:70},extractor:function(t,e){var r=new o.a(t.slice(e));r.moveForwardsBy(4);var n=1===r.readInt(1),i=1===r.readInt(1)?"le":"be";r.moveForwardsBy(n?26:34);var a=n?r.readInt(4,i):r.readInt(8,i);r.moveForwardsBy(10);var s=r.readInt(2,i),u=r.readInt(2,i);return r.moveTo(a),r.moveForwardsBy(s*u),r.carve()}},{name:"Adobe Flash",extension:"swf",mime:"application/x-shockwave-flash",description:"",signature:{0:[67,70],1:87,2:83},extractor:null},{name:"Java Class",extension:"class",mime:"application/java-vm",description:"",signature:{0:202,1:254,2:186,3:190},extractor:null},{name:"Dalvik Executable",extension:"dex",mime:"application/octet-stream",description:"Dalvik Executable as used by Android",signature:{0:100,1:101,2:120,3:10,4:48,5:51,6:53,7:0},extractor:null},{name:"Google Chrome Extension",extension:"crx",mime:"application/crx",description:"Google Chrome extension or packaged app",signature:{0:67,1:114,2:50,3:52},extractor:null}],Archives:[{name:"PKZIP archive",extension:"zip",mime:"application/zip",description:"",signature:{0:80,1:75,2:[3,5,7],3:[4,6,8]},extractor:s},{name:"TAR archive",extension:"tar",mime:"application/x-tar",description:"",signature:{257:117,258:115,259:116,260:97,261:114},extractor:null},{name:"Roshal Archive",extension:"rar",mime:"application/x-rar-compressed",description:"",signature:{0:82,1:97,2:114,3:33,4:26,5:7,6:[0,1]},extractor:null},{name:"Gzip",extension:"gz",mime:"application/gzip",description:"",signature:{0:31,1:139,2:8},extractor:function(t,e){var r=new o.a(t.slice(e));r.moveForwardsBy(3);var n=r.readInt(1);if(r.moveForwardsBy(4),r.readInt(1),r.moveForwardsBy(1),4&n){var i=r.readInt(2,"le");r.moveForwardsby(i)}8&n&&(r.continueUntil(0),r.moveForwardsBy(1));16&n&&(r.continueUntil(0),r.moveForwardsBy(1));2&n&&r.moveForwardsBy(2);return p(r),r.moveForwardsBy(8),r.carve()}},{name:"Bzip2",extension:"bz2",mime:"application/x-bzip2",description:"",signature:{0:66,1:90,2:104},extractor:null},{name:"7zip",extension:"7z",mime:"application/x-7z-compressed",description:"",signature:{0:55,1:122,2:188,3:175,4:39,5:28},extractor:null},{name:"Zlib Deflate",extension:"zlib",mime:"application/x-deflate",description:"",signature:{0:120,1:[1,156,218,94]},extractor:function(t,e){var r=new o.a(t.slice(e));r.moveForwardsBy(1),32&r.readInt(1)&&r.moveForwardsBy(4);return p(r),r.moveForwardsBy(4),r.carve()}},{name:"xz compression",extension:"xz",mime:"application/x-xz",description:"",signature:{0:253,1:55,2:122,3:88,4:90,5:0},extractor:null},{name:"Tarball",extension:"tar.z",mime:"application/x-gtar",description:"",signature:{0:31,1:[157,160]},extractor:null},{name:"ISO disk image",extension:"iso",mime:"application/octet-stream",description:"ISO 9660 CD/DVD image file",signature:[{32769:67,32770:68,32771:48,32772:48,32773:49},{34817:67,34818:68,34819:48,34820:48,34821:49},{36865:67,36866:68,36867:48,36868:48,36869:49}],extractor:null},{name:"Virtual Machine Disk",extension:"vmdk",mime:"application/vmdk,application/x-virtualbox-vmdk",description:"",signature:{0:75,1:68,2:77},extractor:null},{name:"ARJ Archive",extension:"arj",mime:"application/x-arj-compressed",description:"",signature:{0:96,1:234,8:[0,16,20],9:0,10:2},extractor:null},{name:"WinAce Archive",extension:"ace",mime:"application/x-ace-compressed",description:"",signature:{7:42,8:42,9:65,10:67,11:69,12:42,13:42},extractor:null},{name:"Macintosh BinHex Encoded File",extension:"hqx",mime:"application/mac-binhex",description:"",signature:{11:109,12:117,13:115,14:116,15:32,16:98,17:101,18:32,19:99,20:111,21:110,22:118,23:101,24:114,25:116,26:101,27:100,28:32,29:119,30:105,31:116,32:104,33:32,34:66,35:105,36:110,37:72,38:101,39:120},extractor:null},{name:"ALZip Archive",extension:"alz",mime:"application/octet-stream",description:"",signature:{0:65,1:76,2:90,3:1,4:10,5:0,6:0,7:0},extractor:null},{name:"KGB Compressed Archive",extension:"kgb",mime:"application/x-kgb-compressed",description:"",signature:{0:75,1:71,2:66,3:95,4:97,5:114,6:99,7:104,8:32,9:45},extractor:null}],Miscellaneous:[{name:"UTF-8 text file",extension:"txt",mime:"text/plain",description:"UTF-8 encoded Unicode byte order mark, commonly but not exclusively seen in text files.",signature:{0:239,1:187,2:191},extractor:null},{name:"UTF-32 LE file",extension:"utf32le",mime:"charset/utf32le",description:"Little-endian UTF-32 encoded Unicode byte order mark.",signature:{0:255,1:254,2:0,3:0},extractor:null},{name:"UTF-16 LE file",extension:"utf16le",mime:"charset/utf16le",description:"Little-endian UTF-16 encoded Unicode byte order mark.",signature:{0:255,1:254},extractor:null},{name:"Web Open Font Format",extension:"woff",mime:"application/font-woff",description:"",signature:{0:119,1:79,2:70,3:70,4:0,5:1,6:0,7:0},extractor:null},{name:"Web Open Font Format 2",extension:"woff2",mime:"application/font-woff",description:"",signature:{0:119,1:79,2:70,3:50,4:0,5:1,6:0,7:0},extractor:null},{name:"Embedded OpenType font",extension:"eot",mime:"application/octet-stream",description:"",signature:[{8:2,9:0,10:1,34:76,35:80},{8:1,9:0,10:0,34:76,35:80},{8:2,9:0,10:2,34:76,35:80}],extractor:null},{name:"TrueType Font",extension:"ttf",mime:"application/font-sfnt",description:"",signature:{0:0,1:1,2:0,3:0,4:0},extractor:null},{name:"OpenType Font",extension:"otf",mime:"application/font-sfnt",description:"",signature:{0:79,1:84,2:84,3:79,4:0},extractor:null},{name:"SQLite",extension:"sqlite",mime:"application/x-sqlite3",description:"",signature:{0:83,1:81,2:76,3:105},extractor:null},{name:"BitTorrent link",extension:"torrent",mime:"application/x-bittorrent",description:"",signature:[{0:100,1:56,2:58,3:97,4:110,5:110,6:111,7:117,8:110,9:99,10:101,11:35,12:35,13:58},{0:100,1:52,2:58,3:105,4:110,5:102,6:111,7:100,8:[52,53,54],9:58}],extractor:null}]};function s(t,e){var r=new o.a(t.slice(e));r.continueUntil([80,75,5,6]),r.moveForwardsBy(20);var n=r.readInt(2,"le");return r.moveForwardsBy(n),r.carve()}for(var u=new Array(288),f=0;f<u.length;f++)u[f]=f<=143?8:f<=255?9:f<=279?7:8;var c=y(u),h=y(new Array(30).fill(5)),l=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];function p(t){for(var e=0;!e;){e=t.readBits(1);var r=t.readBits(2);if(0===r){t.moveForwardsBy(1);var n=t.readInt(2,"le");t.moveForwardsBy(2+n)}else if(1===r)g(t,c,h);else{if(2!==r)throw new Error(`Invalid block type while parsing DEFLATE stream at pos ${t.position}`);for(var i=t.readBits(5)+257,o=t.readBits(5)+1,a=t.readBits(4)+4,s=new Uint8Array(l.length),u=0;u<a;u++)s[l[u]]=t.readBits(3);for(var f=y(s),p=new Uint8Array(i+o),d=void 0,m=void 0,b=void 0,w=0;w<i+o;)switch(d=v(t,f)){case 16:for(m=3+t.readBits(2);m--;)p[w++]=b;break;case 17:for(m=3+t.readBits(3);m--;)p[w++]=0;b=0;break;case 18:for(m=11+t.readBits(7);m--;)p[w++]=0;b=0;break;default:p[w++]=d,b=d}g(t,y(p.subarray(0,i)),y(p.subarray(i)))}}t.bitPos>0&&t.moveForwardsBy(1)}var d=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0],m=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13];function g(t,e,r){for(var n,i=0;(n=v(t,e))&&256!==n;){if(++i>1e4)throw new Error("Caught in probable infinite loop while parsing Huffman Block");n<256||(t.readBits(d[n-257]),n=v(t,r),t.readBits(m[n]))}}function y(t){for(var e=Math.max.apply(Math,t),r=Math.min.apply(Math,t),n=1<<e,i=new Uint32Array(n),o=1,a=0,s=2;o<=e;){for(var u=0;u<t.length;u++)if(t[u]===o){var f=void 0,c=void 0,h=void 0;for(f=0,c=a,h=0;h<o;h++)f=f<<1|1&c,c>>=1;for(var l=o<<16|u,p=f;p<n;p+=s)i[p]=l;a++}o++,a<<=1,s<<=1}return[i,e,r]}function v(t,e){var r=i()(e,2),n=r[0],o=r[1],a=n[t.readBits(o)&(1<<o)-1],s=a>>>16;if(s>o)throw new Error(`Invalid Huffman Code length while parsing DEFLATE block at pos ${t.position}: ${s}`);return t.moveBackwardsByBits(o-s),65535&a}r(0);
/**
 * File type functions
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 *
 */
function b(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(t.length){for(var n=0;n<t.length;n++)if(w(t[n],e,r))return!0;return!1}return w(t,e,r)}function w(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;for(var n in t){var i=parseInt(n,10)+r;switch(typeof t[n]){case"number":if(e[i]!==t[n])return!1;break;case"object":if(t[n].indexOf(e[i])<0)return!1;break;case"function":if(!t[n](e[i]))return!1;break;default:throw new Error(`Unrecognised signature type at offset ${n}`)}}return!0}function A(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Object.keys(a);if(t instanceof ArrayBuffer&&(t=new Uint8Array(t)),!(t&&t.length>1))return[];var r=[],n={};for(var i in a)e.includes(i)&&(n[i]=a[i]);for(var o in n){n[o].forEach(function(e){b(e.signature,t)&&r.push(e)})}return r}function x(t){return function(t,e){var r=A(e);if(!r||!r.length)return!1;if("string"==typeof t)return r.reduce(function(e,r){var n=!!r.mime.startsWith(t)&&r.mime;return e||n},!1);if(t instanceof RegExp)return r.reduce(function(e,r){var n=!!t.test(r.mime)&&r.mime;return e||n},!1);throw new Error("Invalid type input.")}("image",t)}r.d(e,"a",function(){return A}),r.d(e,"b",function(){return x})},2:function(t,e){function r(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}t.exports=function(t,e,n){return e&&r(t.prototype,e),n&&r(t,n),t}},24:function(t,e,r){!function(t){var e,r,n,i=String.fromCharCode;function o(t){for(var e,r,n=[],i=0,o=t.length;i<o;)(e=t.charCodeAt(i++))>=55296&&e<=56319&&i<o?56320==(64512&(r=t.charCodeAt(i++)))?n.push(((1023&e)<<10)+(1023&r)+65536):(n.push(e),i--):n.push(e);return n}function a(t){if(t>=55296&&t<=57343)throw Error("Lone surrogate U+"+t.toString(16).toUpperCase()+" is not a scalar value")}function s(t,e){return i(t>>e&63|128)}function u(t){if(0==(4294967168&t))return i(t);var e="";return 0==(4294965248&t)?e=i(t>>6&31|192):0==(4294901760&t)?(a(t),e=i(t>>12&15|224),e+=s(t,6)):0==(4292870144&t)&&(e=i(t>>18&7|240),e+=s(t,12),e+=s(t,6)),e+=i(63&t|128)}function f(){if(n>=r)throw Error("Invalid byte index");var t=255&e[n];if(n++,128==(192&t))return 63&t;throw Error("Invalid continuation byte")}function c(){var t,i;if(n>r)throw Error("Invalid byte index");if(n==r)return!1;if(t=255&e[n],n++,0==(128&t))return t;if(192==(224&t)){if((i=(31&t)<<6|f())>=128)return i;throw Error("Invalid continuation byte")}if(224==(240&t)){if((i=(15&t)<<12|f()<<6|f())>=2048)return a(i),i;throw Error("Invalid continuation byte")}if(240==(248&t)&&(i=(7&t)<<18|f()<<12|f()<<6|f())>=65536&&i<=1114111)return i;throw Error("Invalid UTF-8 detected")}t.version="3.0.0",t.encode=function(t){for(var e=o(t),r=e.length,n=-1,i="";++n<r;)i+=u(e[n]);return i},t.decode=function(t){e=o(t),r=e.length,n=0;for(var a,s=[];!1!==(a=c());)s.push(a);return function(t){for(var e,r=t.length,n=-1,o="";++n<r;)(e=t[n])>65535&&(o+=i((e-=65536)>>>10&1023|55296),e=56320|1023&e),o+=i(e);return o}(s)}}(e)},25:function(t,e){t.exports=function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}},26:function(t,e,r){var n,i;!function(o,a){"use strict";void 0===(i="function"==typeof(n=function(){var t=function(){},e="undefined",r=["trace","debug","info","warn","error"];function n(t,e){var r=t[e];if("function"==typeof r.bind)return r.bind(t);try{return Function.prototype.bind.call(r,t)}catch(e){return function(){return Function.prototype.apply.apply(r,[t,arguments])}}}function i(e,n){for(var i=0;i<r.length;i++){var o=r[i];this[o]=i<e?t:this.methodFactory(o,e,n)}this.log=this.debug}function o(t,r,n){return function(){typeof console!==e&&(i.call(this,r,n),this[t].apply(this,arguments))}}function a(r,i,a){return function(r){return"debug"===r&&(r="log"),typeof console!==e&&(void 0!==console[r]?n(console,r):void 0!==console.log?n(console,"log"):t)}(r)||o.apply(this,arguments)}function s(t,n,o){var s,u=this,f="loglevel";function c(){var t;if(typeof window!==e){try{t=window.localStorage[f]}catch(t){}if(typeof t===e)try{var r=window.document.cookie,n=r.indexOf(encodeURIComponent(f)+"=");-1!==n&&(t=/^([^;]+)/.exec(r.slice(n))[1])}catch(t){}return void 0===u.levels[t]&&(t=void 0),t}}t&&(f+=":"+t),u.name=t,u.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},u.methodFactory=o||a,u.getLevel=function(){return s},u.setLevel=function(n,o){if("string"==typeof n&&void 0!==u.levels[n.toUpperCase()]&&(n=u.levels[n.toUpperCase()]),!("number"==typeof n&&n>=0&&n<=u.levels.SILENT))throw"log.setLevel() called with invalid level: "+n;if(s=n,!1!==o&&function(t){var n=(r[t]||"silent").toUpperCase();if(typeof window!==e){try{return void(window.localStorage[f]=n)}catch(t){}try{window.document.cookie=encodeURIComponent(f)+"="+n+";"}catch(t){}}}(n),i.call(u,n,t),typeof console===e&&n<u.levels.SILENT)return"No console available for logging"},u.setDefaultLevel=function(t){c()||u.setLevel(t,!1)},u.enableAll=function(t){u.setLevel(u.levels.TRACE,t)},u.disableAll=function(t){u.setLevel(u.levels.SILENT,t)};var h=c();null==h&&(h=null==n?"WARN":n),u.setLevel(h,!1)}var u=new s,f={};u.getLogger=function(t){if("string"!=typeof t||""===t)throw new TypeError("You must supply a name when creating a logger.");var e=f[t];return e||(e=f[t]=new s(t,u.getLevel(),u.methodFactory)),e};var c=typeof window!==e?window.log:void 0;return u.noConflict=function(){return typeof window!==e&&window.log===u&&(window.log=c),u},u.getLoggers=function(){return f},u})?n.call(e,r,e,t):n)||(t.exports=i)}()},27:function(t,e){var r,n,i=t.exports={};function o(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function s(t){if(r===setTimeout)return setTimeout(t,0);if((r===o||!r)&&setTimeout)return r=setTimeout,setTimeout(t,0);try{return r(t,0)}catch(e){try{return r.call(null,t,0)}catch(e){return r.call(this,t,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:o}catch(t){r=o}try{n="function"==typeof clearTimeout?clearTimeout:a}catch(t){n=a}}();var u,f=[],c=!1,h=-1;function l(){c&&u&&(c=!1,u.length?f=u.concat(f):h=-1,f.length&&p())}function p(){if(!c){var t=s(l);c=!0;for(var e=f.length;e;){for(u=f,f=[];++h<e;)u&&u[h].run();h=-1,e=f.length}u=null,c=!1,function(t){if(n===clearTimeout)return clearTimeout(t);if((n===a||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(t);try{n(t)}catch(e){try{return n.call(null,t)}catch(e){return n.call(this,t)}}}(t)}}function d(t,e){this.fun=t,this.array=e}function m(){}i.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];f.push(new d(t,e)),1!==f.length||c||s(p)},d.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=m,i.addListener=m,i.once=m,i.off=m,i.removeListener=m,i.removeAllListeners=m,i.emit=m,i.prependListener=m,i.prependOnceListener=m,i.listeners=function(t){return[]},i.binding=function(t){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(t){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},28:function(t,e){var r;r=function(){return this}();try{r=r||new Function("return this")()}catch(t){"object"==typeof window&&(r=window)}t.exports=r},29:function(t,e,r){"use strict";r.d(e,"a",function(){return i});var n=r(0);
/**
 * Binary functions.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 */function i(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Space",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:8,i=n.b.regexRep(e);t=t.replace(i,"");for(var o=[],a=0;a<t.length;a+=r)o.push(parseInt(t.substr(a,r),2));return o}},3:function(t,e){function r(e){return t.exports=r=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},r(e)}t.exports=r},30:function(t,e,r){"use strict";r.d(e,"a",function(){return i});var n=r(0);
/**
 * Decimal functions.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 */function i(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Auto";e=n.b.charRep(e);var r=[],i=t.split(e);""===i[i.length-1]&&(i=i.slice(0,i.length-1));for(var o=0;o<i.length;o++)r[o]=parseInt(i[o],10);return r}},392:function(t,e,r){"use strict";(function(e){var n=r(104),i=r(1185),o={"Content-Type":"application/x-www-form-urlencoded"};function a(t,e){!n.isUndefined(t)&&n.isUndefined(t["Content-Type"])&&(t["Content-Type"]=e)}var s,u={adapter:("undefined"!=typeof XMLHttpRequest?s=r(627):void 0!==e&&(s=r(627)),s),transformRequest:[function(t,e){return i(e,"Content-Type"),n.isFormData(t)||n.isArrayBuffer(t)||n.isBuffer(t)||n.isStream(t)||n.isFile(t)||n.isBlob(t)?t:n.isArrayBufferView(t)?t.buffer:n.isURLSearchParams(t)?(a(e,"application/x-www-form-urlencoded;charset=utf-8"),t.toString()):n.isObject(t)?(a(e,"application/json;charset=utf-8"),JSON.stringify(t)):t}],transformResponse:[function(t){if("string"==typeof t)try{t=JSON.parse(t)}catch(t){}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(t){return t>=200&&t<300}};u.headers={common:{Accept:"application/json, text/plain, */*"}},n.forEach(["delete","get","head"],function(t){u.headers[t]={}}),n.forEach(["post","put","patch"],function(t){u.headers[t]=n.merge(o)}),t.exports=u}).call(this,r(27))},4:function(t,e,r){var n=r(43),i=r(25);t.exports=function(t,e){return!e||"object"!==n(e)&&"function"!=typeof e?i(t):e}},43:function(t,e){function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function n(e){return"function"==typeof Symbol&&"symbol"===r(Symbol.iterator)?t.exports=n=function(t){return r(t)}:t.exports=n=function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":r(t)},n(e)}t.exports=n},44:function(t,e){function r(e,n){return t.exports=r=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},r(e,n)}t.exports=r},45:function(t,e,r){"use strict";e.byteLength=function(t){var e=f(t),r=e[0],n=e[1];return 3*(r+n)/4-n},e.toByteArray=function(t){for(var e,r=f(t),n=r[0],a=r[1],s=new o(function(t,e,r){return 3*(e+r)/4-r}(0,n,a)),u=0,c=a>0?n-4:n,h=0;h<c;h+=4)e=i[t.charCodeAt(h)]<<18|i[t.charCodeAt(h+1)]<<12|i[t.charCodeAt(h+2)]<<6|i[t.charCodeAt(h+3)],s[u++]=e>>16&255,s[u++]=e>>8&255,s[u++]=255&e;2===a&&(e=i[t.charCodeAt(h)]<<2|i[t.charCodeAt(h+1)]>>4,s[u++]=255&e);1===a&&(e=i[t.charCodeAt(h)]<<10|i[t.charCodeAt(h+1)]<<4|i[t.charCodeAt(h+2)]>>2,s[u++]=e>>8&255,s[u++]=255&e);return s},e.fromByteArray=function(t){for(var e,r=t.length,i=r%3,o=[],a=0,s=r-i;a<s;a+=16383)o.push(c(t,a,a+16383>s?s:a+16383));1===i?(e=t[r-1],o.push(n[e>>2]+n[e<<4&63]+"==")):2===i&&(e=(t[r-2]<<8)+t[r-1],o.push(n[e>>10]+n[e>>4&63]+n[e<<2&63]+"="));return o.join("")};for(var n=[],i=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,u=a.length;s<u;++s)n[s]=a[s],i[a.charCodeAt(s)]=s;function f(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");return-1===r&&(r=e),[r,r===e?0:4-r%4]}function c(t,e,r){for(var i,o,a=[],s=e;s<r;s+=3)i=(t[s]<<16&16711680)+(t[s+1]<<8&65280)+(255&t[s+2]),a.push(n[(o=i)>>18&63]+n[o>>12&63]+n[o>>6&63]+n[63&o]);return a.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},46:function(t,e){e.read=function(t,e,r,n,i){var o,a,s=8*i-n-1,u=(1<<s)-1,f=u>>1,c=-7,h=r?i-1:0,l=r?-1:1,p=t[e+h];for(h+=l,o=p&(1<<-c)-1,p>>=-c,c+=s;c>0;o=256*o+t[e+h],h+=l,c-=8);for(a=o&(1<<-c)-1,o>>=-c,c+=n;c>0;a=256*a+t[e+h],h+=l,c-=8);if(0===o)o=1-f;else{if(o===u)return a?NaN:1/0*(p?-1:1);a+=Math.pow(2,n),o-=f}return(p?-1:1)*a*Math.pow(2,o-n)},e.write=function(t,e,r,n,i,o){var a,s,u,f=8*o-i-1,c=(1<<f)-1,h=c>>1,l=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,p=n?0:o-1,d=n?1:-1,m=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(s=isNaN(e)?1:0,a=c):(a=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-a))<1&&(a--,u*=2),(e+=a+h>=1?l/u:l*Math.pow(2,1-h))*u>=2&&(a++,u/=2),a+h>=c?(s=0,a=c):a+h>=1?(s=(e*u-1)*Math.pow(2,i),a+=h):(s=e*Math.pow(2,h-1)*Math.pow(2,i),a=0));i>=8;t[r+p]=255&s,p+=d,s/=256,i-=8);for(a=a<<i|s,f+=i;f>0;t[r+p]=255&a,p+=d,a/=256,f-=8);t[r+p-d]|=128*m}},47:function(t,e){var r={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==r.call(t)}},48:function(t,e,r){var n=function(t){"use strict";var e,r=Object.prototype,n=r.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function u(t,e,r,n){var i=e&&e.prototype instanceof m?e:m,o=Object.create(i.prototype),a=new _(n||[]);return o._invoke=function(t,e,r){var n=c;return function(i,o){if(n===l)throw new Error("Generator is already running");if(n===p){if("throw"===i)throw o;return S()}for(r.method=i,r.arg=o;;){var a=r.delegate;if(a){var s=B(a,r);if(s){if(s===d)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===c)throw n=p,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=l;var u=f(t,e,r);if("normal"===u.type){if(n=r.done?p:h,u.arg===d)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n=p,r.method="throw",r.arg=u.arg)}}}(t,r,a),o}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var c="suspendedStart",h="suspendedYield",l="executing",p="completed",d={};function m(){}function g(){}function y(){}var v={};v[o]=function(){return this};var b=Object.getPrototypeOf,w=b&&b(b(C([])));w&&w!==r&&n.call(w,o)&&(v=w);var A=y.prototype=m.prototype=Object.create(v);function x(t){["next","throw","return"].forEach(function(e){t[e]=function(t){return this._invoke(e,t)}})}function E(t){var e;this._invoke=function(r,i){function o(){return new Promise(function(e,o){!function e(r,i,o,a){var s=f(t[r],t,i);if("throw"!==s.type){var u=s.arg,c=u.value;return c&&"object"==typeof c&&n.call(c,"__await")?Promise.resolve(c.__await).then(function(t){e("next",t,o,a)},function(t){e("throw",t,o,a)}):Promise.resolve(c).then(function(t){u.value=t,o(u)},function(t){return e("throw",t,o,a)})}a(s.arg)}(r,i,e,o)})}return e=e?e.then(o,o):o()}}function B(t,r){var n=t.iterator[r.method];if(n===e){if(r.delegate=null,"throw"===r.method){if(t.iterator.return&&(r.method="return",r.arg=e,B(t,r),"throw"===r.method))return d;r.method="throw",r.arg=new TypeError("The iterator does not provide a 'throw' method")}return d}var i=f(n,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,d;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,d):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,d)}function F(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function k(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function _(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(F,this),this.reset(!0)}function C(t){if(t){var r=t[o];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,a=function r(){for(;++i<t.length;)if(n.call(t,i))return r.value=t[i],r.done=!1,r;return r.value=e,r.done=!0,r};return a.next=a}}return{next:S}}function S(){return{value:e,done:!0}}return g.prototype=A.constructor=y,y.constructor=g,y[s]=g.displayName="GeneratorFunction",t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,s in t||(t[s]="GeneratorFunction")),t.prototype=Object.create(A),t},t.awrap=function(t){return{__await:t}},x(E.prototype),E.prototype[a]=function(){return this},t.AsyncIterator=E,t.async=function(e,r,n,i){var o=new E(u(e,r,n,i));return t.isGeneratorFunction(r)?o:o.next().then(function(t){return t.done?t.value:o.next()})},x(A),A[s]="Generator",A[o]=function(){return this},A.toString=function(){return"[object Generator]"},t.keys=function(t){var e=[];for(var r in t)e.push(r);return e.reverse(),function r(){for(;e.length;){var n=e.pop();if(n in t)return r.value=n,r.done=!1,r}return r.done=!0,r}},t.values=C,_.prototype={constructor:_,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(k),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function i(n,i){return s.type="throw",s.arg=t,r.next=n,i&&(r.method="next",r.arg=e),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),f=n.call(a,"finallyLoc");if(u&&f){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!f)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,d):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),d},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),k(r),d}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;k(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:C(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),d}},t}(t.exports);try{regeneratorRuntime=n}catch(t){Function("r","regeneratorRuntime = r")(n)}},49:function(t,e){t.exports=function(t){if(Array.isArray(t))return t}},5:function(t,e,r){var n=r(44);t.exports=function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&n(t,e)}},50:function(t,e){t.exports=function(t,e){var r=[],n=!0,i=!1,o=void 0;try{for(var a,s=t[Symbol.iterator]();!(n=(a=s.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){i=!0,o=t}finally{try{n||null==s.return||s.return()}finally{if(i)throw o}}return r}},51:function(t,e){t.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}},6:function(t,e,r){"use strict";var n=r(1),i=r.n(n),o=r(2),a=r.n(o),s=r(15),u=r(0),f=r(17),c=function(){function t(e){i()(this,t),this.name="",this.type="",this._value=null,this.disabled=!1,this.hint="",this.rows=0,this.toggleValues=[],this.target=null,this.defaultIndex=0,this.min=null,this.max=null,this.step=1,e&&this._parseConfig(e)}return a()(t,[{key:"_parseConfig",value:function(t){this.name=t.name,this.type=t.type,this.defaultValue=t.value,this.disabled=!!t.disabled,this.hint=t.hint||!1,this.rows=t.rows||!1,this.toggleValues=t.toggleValues,this.target=void 0!==t.target?t.target:null,this.defaultIndex=void 0!==t.defaultIndex?t.defaultIndex:0,this.min=t.min,this.max=t.max,this.step=t.step}},{key:"config",get:function(){return this._value}},{key:"value",set:function(e){this._value=t.prepare(e,this.type)},get:function(){return this._value}}],[{key:"prepare",value:function(t,e){var r;switch(e){case"binaryString":case"binaryShortString":case"editableOption":case"editableOptionShort":return u.b.parseEscapedChars(t);case"byteArray":return"string"==typeof t?(t=t.replace(/\s+/g,""),Object(f.a)(t)):t;case"number":if(r=parseFloat(t),isNaN(r))throw"Invalid ingredient value. Not a number: "+u.b.truncate(t.toString(),10);return r;default:return t}}}]),t}(),h=function(){function t(){i()(this,t),this._inputType=-1,this._outputType=-1,this._presentType=-1,this._breakpoint=!1,this._disabled=!1,this._flowControl=!1,this._manualBake=!1,this._ingList=[],this.name="",this.module="",this.description="",this.infoURL=null}return a()(t,[{key:"run",value:function(t,e){return t}},{key:"highlight",value:function(t,e){return!1}},{key:"highlightReverse",value:function(t,e){return!1}},{key:"present",value:function(t,e){return t}},{key:"addIngredient",value:function(t){this._ingList.push(t)}},{key:"inputType",set:function(t){this._inputType=s.a.typeEnum(t)},get:function(){return s.a.enumLookup(this._inputType)}},{key:"outputType",set:function(t){this._outputType=s.a.typeEnum(t),this._presentType<0&&(this._presentType=this._outputType)},get:function(){return s.a.enumLookup(this._outputType)}},{key:"presentType",set:function(t){this._presentType=s.a.typeEnum(t)},get:function(){return s.a.enumLookup(this._presentType)}},{key:"args",set:function(t){var e=this;t.forEach(function(t){var r=new c(t);e.addIngredient(r)})},get:function(){return this._ingList.map(function(t){var e={name:t.name,type:t.type,value:t.defaultValue};return t.toggleValues&&(e.toggleValues=t.toggleValues),t.hint&&(e.hint=t.hint),t.rows&&(e.rows=t.rows),t.disabled&&(e.disabled=t.disabled),t.target&&(e.target=t.target),t.defaultIndex&&(e.defaultIndex=t.defaultIndex),"number"==typeof t.min&&(e.min=t.min),"number"==typeof t.max&&(e.max=t.max),t.step&&(e.step=t.step),e})}},{key:"config",get:function(){return{op:this.name,args:this._ingList.map(function(t){return t.config})}}},{key:"ingValues",set:function(t){var e=this;t.forEach(function(t,r){e._ingList[r].value=t})},get:function(){return this._ingList.map(function(t){return t.value})}},{key:"breakpoint",set:function(t){this._breakpoint=!!t},get:function(){return this._breakpoint}},{key:"disabled",set:function(t){this._disabled=!!t},get:function(){return this._disabled}},{key:"flowControl",get:function(){return this._flowControl},set:function(t){this._flowControl=!!t}},{key:"manualBake",get:function(){return this._manualBake},set:function(t){this._manualBake=!!t}}]),t}();e.a=h},624:function(module,exports,__webpack_require__){"use strict";(function(Buffer){const toBytes=t=>[...t].map(t=>t.charCodeAt(0)),xpiZipFilename=toBytes("META-INF/mozilla.rsa"),oxmlContentTypes=toBytes("[Content_Types].xml"),oxmlRels=toBytes("_rels/.rels");function readUInt64LE(t,e=0){let r=t[e],n=1,i=0;for(;++i<8;)n*=256,r+=t[e+i]*n;return r}const fileType=t=>{if(!(t instanceof Uint8Array||t instanceof ArrayBuffer||Buffer.isBuffer(t)))throw new TypeError(`Expected the \`input\` argument to be of type \`Uint8Array\` or \`Buffer\` or \`ArrayBuffer\`, got \`${typeof t}\``);const e=t instanceof Uint8Array?t:new Uint8Array(t);if(!(e&&e.length>1))return null;const r=(t,r)=>{r=Object.assign({offset:0},r);for(let n=0;n<t.length;n++)if(r.mask){if(t[n]!==(r.mask[n]&e[n+r.offset]))return!1}else if(t[n]!==e[n+r.offset])return!1;return!0},n=(t,e)=>r(toBytes(t),e);if(r([255,216,255]))return{ext:"jpg",mime:"image/jpeg"};if(r([137,80,78,71,13,10,26,10]))return{ext:"png",mime:"image/png"};if(r([71,73,70]))return{ext:"gif",mime:"image/gif"};if(r([87,69,66,80],{offset:8}))return{ext:"webp",mime:"image/webp"};if(r([70,76,73,70]))return{ext:"flif",mime:"image/flif"};if((r([73,73,42,0])||r([77,77,0,42]))&&r([67,82],{offset:8}))return{ext:"cr2",mime:"image/x-canon-cr2"};if(r([73,73,42,0])||r([77,77,0,42]))return{ext:"tif",mime:"image/tiff"};if(r([66,77]))return{ext:"bmp",mime:"image/bmp"};if(r([73,73,188]))return{ext:"jxr",mime:"image/vnd.ms-photo"};if(r([56,66,80,83]))return{ext:"psd",mime:"image/vnd.adobe.photoshop"};if(r([80,75,3,4])){if(r([109,105,109,101,116,121,112,101,97,112,112,108,105,99,97,116,105,111,110,47,101,112,117,98,43,122,105,112],{offset:30}))return{ext:"epub",mime:"application/epub+zip"};if(r(xpiZipFilename,{offset:30}))return{ext:"xpi",mime:"application/x-xpinstall"};if(n("mimetypeapplication/vnd.oasis.opendocument.text",{offset:30}))return{ext:"odt",mime:"application/vnd.oasis.opendocument.text"};if(n("mimetypeapplication/vnd.oasis.opendocument.spreadsheet",{offset:30}))return{ext:"ods",mime:"application/vnd.oasis.opendocument.spreadsheet"};if(n("mimetypeapplication/vnd.oasis.opendocument.presentation",{offset:30}))return{ext:"odp",mime:"application/vnd.oasis.opendocument.presentation"};const t=(t,e=0)=>t.findIndex((t,r,n)=>r>=e&&80===n[r]&&75===n[r+1]&&3===n[r+2]&&4===n[r+3]);let i=0,o=!1,a=null;do{const s=i+30;if(o||(o=r(oxmlContentTypes,{offset:s})||r(oxmlRels,{offset:s})),a||(n("word/",{offset:s})?a={ext:"docx",mime:"application/vnd.openxmlformats-officedocument.wordprocessingml.document"}:n("ppt/",{offset:s})?a={ext:"pptx",mime:"application/vnd.openxmlformats-officedocument.presentationml.presentation"}:n("xl/",{offset:s})&&(a={ext:"xlsx",mime:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"})),o&&a)return a;i=t(e,s)}while(i>=0);if(a)return a}if(r([80,75])&&(3===e[2]||5===e[2]||7===e[2])&&(4===e[3]||6===e[3]||8===e[3]))return{ext:"zip",mime:"application/zip"};if(r([117,115,116,97,114],{offset:257}))return{ext:"tar",mime:"application/x-tar"};if(r([82,97,114,33,26,7])&&(0===e[6]||1===e[6]))return{ext:"rar",mime:"application/x-rar-compressed"};if(r([31,139,8]))return{ext:"gz",mime:"application/gzip"};if(r([66,90,104]))return{ext:"bz2",mime:"application/x-bzip2"};if(r([55,122,188,175,39,28]))return{ext:"7z",mime:"application/x-7z-compressed"};if(r([120,1]))return{ext:"dmg",mime:"application/x-apple-diskimage"};if(r([51,103,112,53])||r([0,0,0])&&r([102,116,121,112],{offset:4})&&(r([109,112,52,49],{offset:8})||r([109,112,52,50],{offset:8})||r([105,115,111,109],{offset:8})||r([105,115,111,50],{offset:8})||r([109,109,112,52],{offset:8})||r([77,52,86],{offset:8})||r([100,97,115,104],{offset:8})))return{ext:"mp4",mime:"video/mp4"};if(r([77,84,104,100]))return{ext:"mid",mime:"audio/midi"};if(r([26,69,223,163])){const t=e.subarray(4,4100),r=t.findIndex((t,e,r)=>66===r[e]&&130===r[e+1]);if(-1!==r){const e=r+3,n=r=>[...r].every((r,n)=>t[e+n]===r.charCodeAt(0));if(n("matroska"))return{ext:"mkv",mime:"video/x-matroska"};if(n("webm"))return{ext:"webm",mime:"video/webm"}}}if(r([0,0,0,20,102,116,121,112,113,116,32,32])||r([102,114,101,101],{offset:4})||r([102,116,121,112,113,116,32,32],{offset:4})||r([109,100,97,116],{offset:4})||r([109,111,111,118],{offset:4})||r([119,105,100,101],{offset:4}))return{ext:"mov",mime:"video/quicktime"};if(r([82,73,70,70])){if(r([65,86,73],{offset:8}))return{ext:"avi",mime:"video/vnd.avi"};if(r([87,65,86,69],{offset:8}))return{ext:"wav",mime:"audio/vnd.wave"};if(r([81,76,67,77],{offset:8}))return{ext:"qcp",mime:"audio/qcelp"}}if(r([48,38,178,117,142,102,207,17,166,217])){let t=30;do{const n=readUInt64LE(e,t+16);if(r([145,7,220,183,183,169,207,17,142,230,0,192,12,32,83,101],{offset:t})){if(r([64,158,105,248,77,91,207,17,168,253,0,128,95,92,68,43],{offset:t+24}))return{ext:"wma",mime:"audio/x-ms-wma"};if(r([192,239,25,188,77,91,207,17,168,253,0,128,95,92,68,43],{offset:t+24}))return{ext:"wmv",mime:"video/x-ms-asf"};break}t+=n}while(t+24<=e.length);return{ext:"asf",mime:"application/vnd.ms-asf"}}if(r([0,0,1,186])||r([0,0,1,179]))return{ext:"mpg",mime:"video/mpeg"};if(r([102,116,121,112,51,103],{offset:4}))return{ext:"3gp",mime:"video/3gpp"};for(let t=0;t<2&&t<e.length-16;t++){if(r([73,68,51],{offset:t})||r([255,226],{offset:t,mask:[255,226]}))return{ext:"mp3",mime:"audio/mpeg"};if(r([255,228],{offset:t,mask:[255,228]}))return{ext:"mp2",mime:"audio/mpeg"};if(r([255,248],{offset:t,mask:[255,252]}))return{ext:"mp2",mime:"audio/mpeg"};if(r([255,240],{offset:t,mask:[255,252]}))return{ext:"mp4",mime:"audio/mpeg"}}if(r([102,116,121,112,77,52,65],{offset:4}))return{ext:"m4a",mime:"audio/mp4"};if(r([79,112,117,115,72,101,97,100],{offset:28}))return{ext:"opus",mime:"audio/opus"};if(r([79,103,103,83]))return r([128,116,104,101,111,114,97],{offset:28})?{ext:"ogv",mime:"video/ogg"}:r([1,118,105,100,101,111,0],{offset:28})?{ext:"ogm",mime:"video/ogg"}:r([127,70,76,65,67],{offset:28})?{ext:"oga",mime:"audio/ogg"}:r([83,112,101,101,120,32,32],{offset:28})?{ext:"spx",mime:"audio/ogg"}:r([1,118,111,114,98,105,115],{offset:28})?{ext:"ogg",mime:"audio/ogg"}:{ext:"ogx",mime:"application/ogg"};if(r([102,76,97,67]))return{ext:"flac",mime:"audio/x-flac"};if(r([77,65,67,32]))return{ext:"ape",mime:"audio/ape"};if(r([119,118,112,107]))return{ext:"wv",mime:"audio/wavpack"};if(r([35,33,65,77,82,10]))return{ext:"amr",mime:"audio/amr"};if(r([37,80,68,70]))return{ext:"pdf",mime:"application/pdf"};if(r([77,90]))return{ext:"exe",mime:"application/x-msdownload"};if((67===e[0]||70===e[0])&&r([87,83],{offset:1}))return{ext:"swf",mime:"application/x-shockwave-flash"};if(r([123,92,114,116,102]))return{ext:"rtf",mime:"application/rtf"};if(r([0,97,115,109]))return{ext:"wasm",mime:"application/wasm"};if(r([119,79,70,70])&&(r([0,1,0,0],{offset:4})||r([79,84,84,79],{offset:4})))return{ext:"woff",mime:"font/woff"};if(r([119,79,70,50])&&(r([0,1,0,0],{offset:4})||r([79,84,84,79],{offset:4})))return{ext:"woff2",mime:"font/woff2"};if(r([76,80],{offset:34})&&(r([0,0,1],{offset:8})||r([1,0,2],{offset:8})||r([2,0,2],{offset:8})))return{ext:"eot",mime:"application/vnd.ms-fontobject"};if(r([0,1,0,0,0]))return{ext:"ttf",mime:"font/ttf"};if(r([79,84,84,79,0]))return{ext:"otf",mime:"font/otf"};if(r([0,0,1,0]))return{ext:"ico",mime:"image/x-icon"};if(r([0,0,2,0]))return{ext:"cur",mime:"image/x-icon"};if(r([70,76,86,1]))return{ext:"flv",mime:"video/x-flv"};if(r([37,33]))return{ext:"ps",mime:"application/postscript"};if(r([253,55,122,88,90,0]))return{ext:"xz",mime:"application/x-xz"};if(r([83,81,76,105]))return{ext:"sqlite",mime:"application/x-sqlite3"};if(r([78,69,83,26]))return{ext:"nes",mime:"application/x-nintendo-nes-rom"};if(r([67,114,50,52]))return{ext:"crx",mime:"application/x-google-chrome-extension"};if(r([77,83,67,70])||r([73,83,99,40]))return{ext:"cab",mime:"application/vnd.ms-cab-compressed"};if(r([33,60,97,114,99,104,62,10,100,101,98,105,97,110,45,98,105,110,97,114,121]))return{ext:"deb",mime:"application/x-deb"};if(r([33,60,97,114,99,104,62]))return{ext:"ar",mime:"application/x-unix-archive"};if(r([237,171,238,219]))return{ext:"rpm",mime:"application/x-rpm"};if(r([31,160])||r([31,157]))return{ext:"Z",mime:"application/x-compress"};if(r([76,90,73,80]))return{ext:"lz",mime:"application/x-lzip"};if(r([208,207,17,224,161,177,26,225]))return{ext:"msi",mime:"application/x-msi"};if(r([6,14,43,52,2,5,1,1,13,1,2,1,1,2]))return{ext:"mxf",mime:"application/mxf"};if(r([71],{offset:4})&&(r([71],{offset:192})||r([71],{offset:196})))return{ext:"mts",mime:"video/mp2t"};if(r([66,76,69,78,68,69,82]))return{ext:"blend",mime:"application/x-blender"};if(r([66,80,71,251]))return{ext:"bpg",mime:"image/bpg"};if(r([0,0,0,12,106,80,32,32,13,10,135,10])){if(r([106,112,50,32],{offset:20}))return{ext:"jp2",mime:"image/jp2"};if(r([106,112,120,32],{offset:20}))return{ext:"jpx",mime:"image/jpx"};if(r([106,112,109,32],{offset:20}))return{ext:"jpm",mime:"image/jpm"};if(r([109,106,112,50],{offset:20}))return{ext:"mj2",mime:"image/mj2"}}if(r([70,79,82,77]))return{ext:"aif",mime:"audio/aiff"};if(n("<?xml "))return{ext:"xml",mime:"application/xml"};if(r([66,79,79,75,77,79,66,73],{offset:60}))return{ext:"mobi",mime:"application/x-mobipocket-ebook"};if(r([102,116,121,112],{offset:4})){if(r([109,105,102,49],{offset:8}))return{ext:"heic",mime:"image/heif"};if(r([109,115,102,49],{offset:8}))return{ext:"heic",mime:"image/heif-sequence"};if(r([104,101,105,99],{offset:8})||r([104,101,105,120],{offset:8}))return{ext:"heic",mime:"image/heic"};if(r([104,101,118,99],{offset:8})||r([104,101,118,120],{offset:8}))return{ext:"heic",mime:"image/heic-sequence"}}return r([171,75,84,88,32,49,49,187,13,10,26,10])?{ext:"ktx",mime:"image/ktx"}:r([68,73,67,77],{offset:128})?{ext:"dcm",mime:"application/dicom"}:r([77,80,43])?{ext:"mpc",mime:"audio/x-musepack"}:r([77,80,67,75])?{ext:"mpc",mime:"audio/x-musepack"}:r([66,69,71,73,78,58])?{ext:"ics",mime:"text/calendar"}:r([103,108,84,70,2,0,0,0])?{ext:"glb",mime:"model/gltf-binary"}:r([212,195,178,161])||r([161,178,195,212])?{ext:"pcap",mime:"application/vnd.tcpdump.pcap"}:null};module.exports=fileType,module.exports.default=fileType,Object.defineProperty(fileType,"minimumBytes",{value:4100}),module.exports.stream=readableStream=>new Promise((resolve,reject)=>{const stream=eval("require")("stream");readableStream.once("readable",()=>{const t=new stream.PassThrough,e=readableStream.read(module.exports.minimumBytes)||readableStream.read();try{t.fileType=fileType(e)}catch(t){reject(t)}readableStream.unshift(e),stream.pipeline?resolve(stream.pipeline(readableStream,t,()=>{})):resolve(readableStream.pipe(t))})})}).call(this,__webpack_require__(12).Buffer)},625:function(t,e,r){t.exports=r(1182)},626:function(t,e,r){"use strict";t.exports=function(t,e){return function(){for(var r=new Array(arguments.length),n=0;n<r.length;n++)r[n]=arguments[n];return t.apply(e,r)}}},627:function(t,e,r){"use strict";var n=r(104),i=r(1186),o=r(1188),a=r(1189),s=r(1190),u=r(628);t.exports=function(t){return new Promise(function(e,f){var c=t.data,h=t.headers;n.isFormData(c)&&delete h["Content-Type"];var l=new XMLHttpRequest;if(t.auth){var p=t.auth.username||"",d=t.auth.password||"";h.Authorization="Basic "+btoa(p+":"+d)}if(l.open(t.method.toUpperCase(),o(t.url,t.params,t.paramsSerializer),!0),l.timeout=t.timeout,l.onreadystatechange=function(){if(l&&4===l.readyState&&(0!==l.status||l.responseURL&&0===l.responseURL.indexOf("file:"))){var r="getAllResponseHeaders"in l?a(l.getAllResponseHeaders()):null,n={data:t.responseType&&"text"!==t.responseType?l.response:l.responseText,status:l.status,statusText:l.statusText,headers:r,config:t,request:l};i(e,f,n),l=null}},l.onerror=function(){f(u("Network Error",t,null,l)),l=null},l.ontimeout=function(){f(u("timeout of "+t.timeout+"ms exceeded",t,"ECONNABORTED",l)),l=null},n.isStandardBrowserEnv()){var m=r(1191),g=(t.withCredentials||s(t.url))&&t.xsrfCookieName?m.read(t.xsrfCookieName):void 0;g&&(h[t.xsrfHeaderName]=g)}if("setRequestHeader"in l&&n.forEach(h,function(t,e){void 0===c&&"content-type"===e.toLowerCase()?delete h[e]:l.setRequestHeader(e,t)}),t.withCredentials&&(l.withCredentials=!0),t.responseType)try{l.responseType=t.responseType}catch(e){if("json"!==t.responseType)throw e}"function"==typeof t.onDownloadProgress&&l.addEventListener("progress",t.onDownloadProgress),"function"==typeof t.onUploadProgress&&l.upload&&l.upload.addEventListener("progress",t.onUploadProgress),t.cancelToken&&t.cancelToken.promise.then(function(t){l&&(l.abort(),f(t),l=null)}),void 0===c&&(c=null),l.send(c)})}},628:function(t,e,r){"use strict";var n=r(1187);t.exports=function(t,e,r,i,o){var a=new Error(t);return n(a,e,r,i,o)}},629:function(t,e,r){"use strict";t.exports=function(t){return!(!t||!t.__CANCEL__)}},630:function(t,e,r){"use strict";function n(t){this.message=t}n.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},n.prototype.__CANCEL__=!0,t.exports=n},631:function(t,e,r){var n;!function(i){"use strict";var o,a,s,u,f,c,h,l,p,d,m,g,y;function v(t){return null!=t}function b(t){return"number"==typeof t&&t>d&&t<m}function w(t){return"number"==typeof t&&t%1==0}function A(t,e){return b(t)&&t>e}function x(t,e){return b(t)&&t<e}function E(t,e){return b(t)&&t>=e}function B(t,e){return b(t)&&t<=e}function F(t){return"string"==typeof t}function k(t){return F(t)&&""!==t}function _(t){return"[object Object]"===Object.prototype.toString.call(t)}function C(t,e){try{return t instanceof e}catch(t){return!1}}function S(t,e){var r;for(r in e)if(e.hasOwnProperty(r)){if(!1===t.hasOwnProperty(r)||typeof t[r]!=typeof e[r])return!1;if(_(t[r])&&!1===S(t[r],e[r]))return!1}return!0}function T(t){return g(t)}function U(t){return v(t)&&E(t.length,0)}function I(t){return"function"==typeof t}function R(t,e){var r={};return Object.keys(e).forEach(function(n){var i=e[n];I(i)?c.assigned(t)?r[n]=!!i.m:r[n]=i(t[n]):_(i)&&(r[n]=R(t[n],i))}),r}function O(t,e){var r;for(r=0;r<t.length;r+=1)if(t[r]===e)return e;return!e}function L(t,e){var r,n;for(r in t)if(t.hasOwnProperty(r)){if(_(n=t[r])&&L(n,e)===e)return e;if(n===e)return e}return!e}function P(t,e){return Object.keys(e).forEach(function(r){t[r]=e[r]}),t}function D(t,e){return function(){return j(t,arguments,e)}}function j(t,e,r){var n=t.l||t.length,i=e[n],o=e[n+1];return N(t.apply(null,e),k(i)?i:r,I(o)?o:TypeError),e[0]}function N(t,e,r){if(t)return t;throw new(r||Error)(e||"Assertion failed")}function M(t){var e=function(){return z(t.apply(null,arguments))};return e.l=t.length,e}function z(t){return!t}function $(t,e,r){var n=function(){var n,i;if(n=arguments[0],"maybe"===t&&c.assigned(n))return!0;if(!e(n))return!1;n=Y(e,n),i=p.call(arguments,1);try{n.forEach(function(e){if(("maybe"!==t||v(e))&&!r.apply(null,[e].concat(i)))throw 0})}catch(t){return!1}return!0};return n.l=r.length,n}function Y(t,e){switch(t){case U:return p.call(e);case _:return Object.keys(e).map(function(t){return e[t]});default:return e}}function G(t,e){return q([t,s,e])}function q(t){var e,r,n,i;return e=t.shift(),r=t.pop(),n=t.pop(),i=r||{},Object.keys(n).forEach(function(r){Object.defineProperty(i,r,{configurable:!1,enumerable:!0,writable:!1,value:e.apply(null,t.concat(n[r],a[r]))})}),i}function V(t,e){return q([t,e,null])}function H(t,e){l.forEach(function(r){t[r].of=V(e,s[r].of)})}o={v:"value",n:"number",s:"string",b:"boolean",o:"object",t:"type",a:"array",al:"array-like",i:"iterable",d:"date",f:"function",l:"length"},a={},s={},[{n:"equal",f:function(t,e){return t===e},s:"v"},{n:"undefined",f:function(t){return void 0===t},s:"v"},{n:"null",f:function(t){return null===t},s:"v"},{n:"assigned",f:v,s:"v"},{n:"primitive",f:function(t){var e;switch(t){case null:case void 0:case!1:case!0:return!0}return"string"===(e=typeof t)||"number"===e||y&&"symbol"===e},s:"v"},{n:"includes",f:function(t,e){var r,n,i,o,a;if(!v(t))return!1;if(y&&t[Symbol.iterator]&&I(t.values)){r=t.values();do{if((n=r.next()).value===e)return!0}while(!n.done);return!1}for(i=Object.keys(t),o=i.length,a=0;a<o;++a)if(t[i[a]]===e)return!0;return!1},s:"v"},{n:"zero",f:function(t){return 0===t}},{n:"infinity",f:function(t){return t===d||t===m}},{n:"number",f:b},{n:"integer",f:w},{n:"even",f:function(t){return"number"==typeof t&&t%2==0}},{n:"odd",f:function(t){return w(t)&&t%2!=0}},{n:"greater",f:A},{n:"less",f:x},{n:"between",f:function(t,e,r){if(e<r)return A(t,e)&&t<r;return x(t,e)&&t>r}},{n:"greaterOrEqual",f:E},{n:"lessOrEqual",f:B},{n:"inRange",f:function(t,e,r){if(e<r)return E(t,e)&&t<=r;return B(t,e)&&t>=r}},{n:"positive",f:function(t){return A(t,0)}},{n:"negative",f:function(t){return x(t,0)}},{n:"string",f:F,s:"s"},{n:"emptyString",f:function(t){return""===t},s:"s"},{n:"nonEmptyString",f:k,s:"s"},{n:"contains",f:function(t,e){return F(t)&&-1!==t.indexOf(e)},s:"s"},{n:"match",f:function(t,e){return F(t)&&!!t.match(e)},s:"s"},{n:"boolean",f:function(t){return!1===t||!0===t},s:"b"},{n:"object",f:_,s:"o"},{n:"emptyObject",f:function(t){return _(t)&&0===Object.keys(t).length},s:"o"},{n:"nonEmptyObject",f:function(t){return _(t)&&Object.keys(t).length>0},s:"o"},{n:"instanceStrict",f:C,s:"t"},{n:"instance",f:function(t,e){try{return C(t,e)||t.constructor.name===e.name||Object.prototype.toString.call(t)==="[object "+e.name+"]"}catch(t){return!1}},s:"t"},{n:"like",f:S,s:"t"},{n:"array",f:T,s:"a"},{n:"emptyArray",f:function(t){return T(t)&&0===t.length},s:"a"},{n:"nonEmptyArray",f:function(t){return T(t)&&A(t.length,0)},s:"a"},{n:"arrayLike",f:U,s:"al"},{n:"iterable",f:function(t){if(!y)return U(t);return v(t)&&I(t[Symbol.iterator])},s:"i"},{n:"date",f:function(t){return C(t,Date)&&w(t.getTime())},s:"d"},{n:"function",f:I,s:"f"},{n:"hasLength",f:function(t,e){return v(t)&&t.length===e},s:"l"}].map(function(t){var e=t.n;a[e]="Invalid "+o[t.s||"n"],s[e]=t.f}),u={apply:function(t,e){if(f.array(t),I(e))return t.map(function(t){return e(t)});return f.array(e),f.hasLength(t,e.length),t.map(function(t,r){return e[r](t)})},map:function(t,e){if(f.object(t),I(e))return function(t,e){var r={};return Object.keys(t).forEach(function(n){r[n]=e(t[n])}),r}(t,e);return f.object(e),R(t,e)},all:function(t){if(T(t))return O(t,!1);return f.object(t),L(t,!1)},any:function(t){if(T(t))return O(t,!0);return f.object(t),L(t,!0)}},l=["array","arrayLike","iterable","object"],p=Array.prototype.slice,d=Number.NEGATIVE_INFINITY,m=Number.POSITIVE_INFINITY,g=Array.isArray,y="function"==typeof Symbol,u=P(u,s),f=G(D,N),c=G(M,z),h=G(function(t){var e=function(){return!!c.assigned(arguments[0])||t.apply(null,arguments)};return e.l=t.length,e.m=!0,e},function(t){if(!1===v(t))return!0;return t}),f.not=V(D,c),f.maybe=V(D,h),l.forEach(function(t){s[t].of=q([$.bind(null,null),s[t],s,null])}),H(f,D),H(c,M),l.forEach(function(t){h[t].of=q([$.bind(null,"maybe"),s[t],s,null]),f.maybe[t].of=V(D,h[t].of),f.not[t].of=V(D,c[t].of)}),function(i){void 0===(n=function(){return i}.call(e,r,e,t))||(t.exports=n)}(P(u,{assert:f,not:c,maybe:h}))}()},632:function(t,e,r){var n,i;void 0===(i="function"==typeof(n=function(){return function(){var t=arguments.length;if(0===t)throw new Error("resolveUrl requires at least one argument; got none.");var e=document.createElement("base");if(e.href=arguments[0],1===t)return e.href;var r=document.getElementsByTagName("head")[0];r.insertBefore(e,r.firstChild);for(var n,i=document.createElement("a"),o=1;o<t;o++)i.href=arguments[o],n=i.href,e.href=n;return r.removeChild(e),n}})?n.call(e,r,e,t):n)||(t.exports=i)},633:function(t,e,r){(function(t){const n=r(631),i=r(632),o=r(625),a=r(1206),{defaultOptions:s}=r(1207),{version:u}=r(1208),f=(t,e)=>{const r=new FileReader;r.onload=()=>{e(r.result)},r.readAsArrayBuffer(t)},c=t=>{if(n.string(t))return/data:image\/([a-zA-Z]*);base64,([^"]*)/.test(t)?Promise.resolve(a(t.split(",")[1])):o.get(i(t),{responseType:"arraybuffer"}).then(t=>t.data);if(n.instance(t,HTMLElement)){if("IMG"===t.tagName)return c(t.src);if("VIDEO"===t.tagName)return c(t.poster);if("CANVAS"===t.tagName)return new Promise(e=>{t.toBlob(t=>{f(t,e)})})}return n.instance(t,File)||n.instance(t,Blob)?new Promise(e=>{f(t,e)}):Promise.reject()};e.defaultOptions={...s,workerPath:void 0!==t&&"development"===t.env.TESS_ENV?i(`/dist/worker.dev.js?nocache=${Math.random().toString(36).slice(3)}`):`https://unpkg.com/tesseract.js@v${u}/dist/worker.min.js`,corePath:`https://unpkg.com/tesseract.js-core@v2.0.0-beta.10/tesseract-core.${"object"==typeof WebAssembly?"wasm":"asm"}.js`},e.spawnWorker=(t,{workerPath:e})=>{let r;if(Blob&&URL){const t=new Blob([`importScripts("${e}");`],{type:"application/javascript"});r=new Worker(URL.createObjectURL(t))}else r=new Worker(e);return r.onmessage=({data:e})=>{if(e.jobId.startsWith("Job"))t.recv(e);else if(e.jobId.startsWith("Download")){const{path:t,data:r,type:n}=e;((t,e)=>{if(navigator.msSaveBlob)navigator.msSaveBlob(e,t);else{const r=document.createElement("a");if(void 0!==r.download){const n=URL.createObjectURL(e);r.setAttribute("href",n),r.setAttribute("download",t),r.style.visibility="hidden",document.body.appendChild(r),r.click(),document.body.removeChild(r)}}})(t,new Blob([r],{type:n}))}},r},e.terminateWorker=t=>{t.worker.terminate()},e.sendPacket=(t,e)=>{const r={...e};c(r.payload.image).then(t=>new Uint8Array(t)).then(e=>{r.payload.image=Array.from(e),t.worker.postMessage(r)})}}).call(this,r(27))},634:function(t,e){t.exports={OEM:{TESSERACT_ONLY:0,LSTM_ONLY:1,TESSERACT_LSTM_COMBINED:2,DEFAULT:3},PSM:{OSD_ONLY:"0",AUTO_OSD:"1",AUTO_ONLY:"2",AUTO:"3",SINGLE_COLUMN:"4",SINGLE_BLOCK_VERT_TEXT:"5",SINGLE_BLOCK:"6",SINGLE_LINE:"7",SINGLE_WORD:"8",SINGLE_CHAR:"9",SPARSE_TEXT:"10",SPARSE_TEXT_OSD:"11",RAW_LINE:"12"}}},7:function(t,e,r){t.exports=r(48)},711:function(t,e,r){const n=r(1177),i=r(1205),o=r(634);t.exports={TesseractWorker:i,utils:n,...o}},9:function(t,e,r){"use strict";var n=r(1),i=r.n(n),o=r(4),a=r.n(o),s=r(3),u=r.n(s),f=r(25),c=r.n(f),h=r(5),l=r.n(h);
/**
 * Custom error type for handling operation input errors.
 * i.e. where the operation can handle the error and print a message to the screen.
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Crown Copyright 2018
 * @license Apache-2.0
 */
var p=function(t){function e(){var t;i()(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return(t=a()(this,u()(e).call(this,...n))).type="OperationError",Error.captureStackTrace&&Error.captureStackTrace(c()(t),e),t}return l()(e,t),e}(function(t){function e(){var e=Reflect.construct(t,Array.from(arguments));return Object.setPrototypeOf(e,Object.getPrototypeOf(this)),e}return e.prototype=Object.create(t.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t,e}(Error));e.a=p}});