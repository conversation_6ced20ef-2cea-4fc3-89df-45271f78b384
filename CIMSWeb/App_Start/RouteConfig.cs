﻿using System.Web.Mvc;
using System.Web.Routing;

namespace CIMSWeb
{
    public class RouteConfig
    {
        public static void RegisterRoutes(RouteCollection routes)
        {
            routes.IgnoreRoute("{resource}.axd/{*pathInfo}");

            routes.MapRoute(
                name: "ExamComm",
                url: "Exam/Communications/{trackingNumber}",
                defaults: new { controller = "Exam", action = "Communications" }
            );

            routes.MapRoute(
                name: "Default",
                url: "{controller}/{action}/{id}",
                defaults: new { controller = "Home", action = "Index", id = UrlParameter.Optional }
            );

        }
    }
}
