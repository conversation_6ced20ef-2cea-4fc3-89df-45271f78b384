﻿using System.Web.Optimization;

namespace CIMSWeb
{
    public class BundleConfig
    {
        // For more information on bundling, visit http://go.microsoft.com/fwlink/?LinkId=301862
        public static void RegisterBundles(BundleCollection bundles)
        {
            bundles.Add(new ScriptBundle("~/bundles/jquery").Include(
                        "~/Scripts/jquery-2.1.4.min.js"));

            bundles.Add(new ScriptBundle("~/bundles/jqueryval").Include(
                                         "~/Scripts/jquery.validate*"));

            bundles.Add(new ScriptBundle("~/bundles/jqueryvalunobtrusive").Include(
                                         "~/Scripts/jquery.unobtrusive-ajax.js"));

            bundles.Add(new ScriptBundle("~/bundles/kendo").Include(
                            "~/Scripts/kendo/jszip.min.js",
                             "~/Scripts/kendo/kendo.all.min.js",
                             "~/Scripts/kendo/kendo.aspnetmvc.min.js"));

            bundles.Add(new ScriptBundle("~/bundles/signalr").Include(
                        "~/Scripts/jquery.signalR-2.2.0.min.js"));

            //"~/Scripts/jquery.validate.js",
            //"~/Scripts/jquery.validate.unobtrusive.js",
            //"~/Scripts/jquery.unobtrusive-ajax.js"));

            // Use the development version of Modernizr to develop with and learn from. Then, when you're
            // ready for production, use the build tool at http://modernizr.com to pick only the tests you need.
            bundles.Add(new ScriptBundle("~/bundles/modernizr").Include(
                        "~/Scripts/modernizr-*"));

            bundles.Add(new ScriptBundle("~/bundles/bootstrap").Include(
                      "~/Scripts/bootstrap.min.js",
                      "~/Scripts/respond.js"));

            bundles.Add(new ScriptBundle("~/bundles/controls").Include(
                        "~/Scripts/typeahead.bundle.min.js",
                        "~/Scripts/moment.min.js",
                        "~/Scripts/bootstrap-datetimepicker.min.js",
                        "~/Scripts/bootstrap-tagsinput.min.js",
                        "~/Scripts/countUp.min.js",
                        "~/Scripts/easytimer.min.js",
                        "~/Scripts/Chart.bundle.js",
                        "~/Scripts/jqueryFileTree.js",
                        "~/Scripts/jquery.easing.js",
                        "~/Scripts/jquery.easypiechart.min.js",
                        "~/Scripts/vis.min.js"));

            bundles.Add(new ScriptBundle("~/bundles/mvc-spa").Include(
                        "~/Scripts/sammy.min.js",
                        "~/Scripts/routing.js"));

            bundles.Add(new ScriptBundle("~/bundles/cims").Include(
                        "~/Scripts/cims.js"
                    ));

            bundles.Add(new StyleBundle("~/Content/css").Include(
                      "~/Content/bootstrap.min.css",
                      "~/Content/cims.css",
                      "~/Content/entypo.min.css",
                      "~/Content/font-awesome.min.css",
                      "~/Content/normalize.css",
                      "~/Content/typeaheadjs.css",
                      "~/Content/bootstrap-datetimepicker.min.css",
                      "~/Content/bootstrap-tagsinput.css",
                      "~/Content/jqueryFileTree.css",
                      "~/Content/buttons.bootstrap.min.css",
                      "~/Content/vis.min.css",
                      "~/Content/animate.css"));

            bundles.Add(new StyleBundle("~/Content/bootstrap/css").Include(
                "~/Content/bootstrap.min.css"));

            bundles.IgnoreList.Clear();

            //bundles.Add(new StyleBundle("~/Content/kendo/css").Include(
            //"~/Content/kendo/kendo.common-bootstrap.min.css",
            //"~/Content/kendo/kendo.bootstrap.min.css"));
        }
    }
}