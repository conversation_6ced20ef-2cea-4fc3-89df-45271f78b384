using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net.Mime;
using System.Threading.Tasks;
using System.Web.Management;
using System.Web.Mvc;
using CIMSWeb.Models;
using CommonServiceLocator;
using Kendo.Mvc.UI;
using SolrNet;

namespace CIMSWeb.Controllers
{
    public class WordListsController : CIMSController
    {
        // GET
        public ActionResult Index()
        {
            return PartialView();
        }

        public ActionResult Search()
        {
            ViewBag.Delimiters = new List<SelectListItem>
            {
                new SelectListItem()
                    {Text = DelimiterOptions.Line.GetDisplayName(), Value = ((int) DelimiterOptions.Line).ToString()},
                new SelectListItem()
                    {Text = DelimiterOptions.Tab.GetDisplayName(), Value = ((int) DelimiterOptions.Tab).ToString()},
                new SelectListItem()
                    {Text = DelimiterOptions.Comma.GetDisplayName(), Value = ((int) DelimiterOptions.Comma).ToString()},
                new SelectListItem()
                    {Text = DelimiterOptions.Space.GetDisplayName(), Value = ((int) DelimiterOptions.Space).ToString()}
            };
            ViewBag.PasswordFlags = new List<SelectListItem>
            {
                new SelectListItem() {Text = "Can Contain", Value = "null"},
                new SelectListItem() {Text = "Must Contain", Value = "true"},
                new SelectListItem() {Text = "Must Not Contain", Value = "false"}
            };
            return PartialView();
        }

        public ActionResult Manage()
        {
            return PartialView();
        }

        public async Task<ActionResult> ManageRead([DataSourceRequest] DataSourceRequest request)
        {
            return KendoGridResult((await HashSetsManageModel.Get(true)).AsQueryable(), request);
        }
        
        public async Task<ActionResult> ManageDelete(string title)
        {
            await HashSetsManageModel.Delete(title);
            return new EmptyResult();
        }

        public ActionResult Metrics()
        {
            return PartialView();
        }
        
        public async Task<ActionResult> MetricsRead([DataSourceRequest] DataSourceRequest request)
        {
            return KendoGridResult(await HashSetsMetricsModel.Get(), request);
        }

        public ActionResult Upload()
        {
            ViewBag.Delimiters = new List<SelectListItem>
            {
                new SelectListItem()
                    {Text = DelimiterOptions.Line.GetDisplayName(), Value = ((int) DelimiterOptions.Line).ToString()},
                new SelectListItem()
                    {Text = DelimiterOptions.Tab.GetDisplayName(), Value = ((int) DelimiterOptions.Tab).ToString()},
                new SelectListItem()
                    {Text = DelimiterOptions.Comma.GetDisplayName(), Value = ((int) DelimiterOptions.Comma).ToString()},
                new SelectListItem()
                    {Text = DelimiterOptions.Space.GetDisplayName(), Value = ((int) DelimiterOptions.Space).ToString()}
            };
            return PartialView();
        }

        public async Task<ActionResult> UploadForm(HashSetsUploadModel model)
        {
            try
            {
                if (Request.Files.Count < 1)
                {
                    throw new Exception("No files");
                }

                var sw = new Stopwatch();
                sw.Start();

                model.Files = new List<string>();
                for (int i = 0; i < Request.Files.Count; i++)
                {
                    var file = Request.Files[i];
                    var guid = Guid.NewGuid().ToString();
                    var share = ConfigurationManager.AppSettings["WordListsTemp"];
                    var path = $"{share}\\{guid}";
                    file.SaveAs(path);
                    model.Files.Add(path);
                }

                await PostToCimsService("WordLists/Upload", model);
                sw.Stop();
                LogFromController($"upload took {sw.Elapsed.Hours} hours, {sw.Elapsed.Minutes} minutes, & {sw.Elapsed.Seconds} seconds");
                return new EmptyResult();
            }
            catch (Exception e)
            {
                LogFromController($"Something went wrong uploading a hash set.");
                LogFromController(e.Message);
                LogFromController(e.StackTrace);
                throw;
            }
        }

        public async Task<FileContentResult> SearchForm(HashSetsSearchModel model)
        {
            try
            {
                string fileName;
                if (model.AllSources)
                {
                    fileName = $"Results.zip";
                }
                else if (model.PasswordSources.Length == 1)
                {
                    fileName = $"{model.PasswordSources[0]}.zip";
                }
                else
                {
                    var joined = string.Join("_", model.PasswordSources);
                    fileName = $"{joined}.zip";
                }

                var ret = await PostToCimsService("WordLists/Search", model);

                return new FileContentResult(System.IO.File.ReadAllBytes(ret), MediaTypeNames.Application.Zip)
                    {FileDownloadName = fileName};
            }
            catch (Exception e)
            {
                LogFromController($"Something went wrong searching a hash set.");
                LogFromController(e.Message);
                LogFromController(e.StackTrace);
                throw;
            }
        }
    }
}