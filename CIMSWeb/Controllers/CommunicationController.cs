﻿using CIMSData.Database;
using CIMSWeb.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web.Mvc;
using iTextSharp.text;
using iTextSharp.text.pdf;
using CIMSWeb.Models.Comm;
using Kendo.Mvc.UI;
using Kendo.Mvc.Extensions;
using System.Data.Entity;

namespace CIMSWeb.Controllers
{
    public class CommunicationController : CIMSController
    {
        public ActionResult Index()
        {
            var agencies = new List<string>();
            agencies.AddRange((DB11.agency.Select(x => x.name)));
            ViewData["Agencies"] = agencies.OrderBy(a => a);
            return PartialView();
        }

        public ActionResult Get([DataSourceRequest]DataSourceRequest request, int exam_ID)
        {
            var comm = new List<communication>();
            if (exam_ID > 0)
                comm = DB11.communication
                           .Include(x => x.exam)
                           .Include(x => x.exam.@case)
                           .Include(x => x.communication_topic)
                           .Include(x => x.communication_type)
                           .Include(x => x.contact)
                           .Include(x => x.account1)
                           .Include(x => x.account)
                           .Include(x => x.agency)
                           .Where(x => x.exam.exam_id == exam_ID && x.child_id == null).ToList();
            else
                comm = DB11.communication.AsNoTracking()
                           .Include(x => x.exam)
                           .Include(x => x.exam.@case)
                           .Include(x => x.communication_topic)
                           .Include(x => x.communication_type)
                           .Include(x => x.contact)
                           .Include(x => x.account1)
                           .Include(x => x.account)
                           .Include(x => x.agency)
                           .Where(x => x.child_id == null).ToList();

            var vmList = new List<CommViewModel>();
            comm.ForEach(c => vmList.Add(CommViewModel.ConvertToViewModel(c)));

            return KendoGridResult(vmList.OrderByDescending(x => x.CommDate).AsQueryable(), request);
        }
        [HttpPost]
        public ActionResult Update([DataSourceRequest]DataSourceRequest request, CommViewModel comm)
        {
            if (comm != null)
            {
                // All edits to communications actually create brand new entries in the database
                // so that we can store the edit history.
                comm.ExamID = DB11.exam.FirstOrDefault(x => x.exam_number == comm.TrackingNumber)?.exam_id;
                if (comm.ExamID.HasValue)
                {
                    var caseID = DB11.exam.Where(x => x.exam_id == comm.ExamID).Select(x => x.case_id).First();
                    if (caseID != null)
                        comm.CaseNumber = <EMAIL>(x => x.case_id == caseID).Select(x => x.case_number).First();
                }
                communication newComm = CreateDatabaseCommunication(comm);
                DB11.communication.Add(newComm);
                DB11.SaveChanges();
                AuditInsert(newComm);
                DB11.SaveChanges();

                comm.Agency = DB11.agency.Where(ag => ag.agency_id == comm.AgencyID).Select(ag => ag.name).FirstOrDefault();

                // Point to the new child record from the old version of the communication
                communication c = DB11.communication.Where(x => x.communication_id == comm.ID).First();
                AuditUpdate(c);
                c.child_id = newComm.communication_id;
                DB11.Entry(c).State = System.Data.Entity.EntityState.Modified;
                DB11.SaveChanges();
            }
            return Json(new[] { comm }.ToDataSourceResult(request, ModelState));
        }


        public ActionResult Create([DataSourceRequest]DataSourceRequest request, CommViewModel comm, int? examID)
        {
            //if (comm != null && ModelState.IsValid)
            if (comm != null && examID > 0)
            {
                comm.ExamID = examID;

                if (comm.CommDate == null)
                    comm.CommDate = DateTime.Now;

                if (comm.AgencyID > 0)
                    comm.Agency = DB11.agency.Where(x => x.agency_id == comm.AgencyID).Select(x => x.name).First();

                foreach (ContactViewModel inContact in comm.ExternalParticipants ?? new List<ContactViewModel>())
                {
                    if (comm.ExternalList != null)
                        comm.ExternalList =  string.Join(", ", comm.ExternalList, inContact.FirstName.FormatName() + " " + inContact.LastName.FormatName());
                    else
                        comm.ExternalList = inContact.FirstName.FormatName() + " " + inContact.LastName.FormatName();
                }

                foreach (AccountViewModel inAccount in comm.InternalParticipants)
                {
                    comm.InternalList = comm.InternalList != null ? string.Join(", ", comm.InternalList, inAccount.Name.FormatName()) : inAccount.Name.FormatName();
                }

                communication newComm = CreateDatabaseCommunication(comm);
                DB11.SaveChanges();
                AuditInsert(newComm);
                DB11.SaveChanges();
            }
            else if (comm != null && (examID == 0 || examID == null))
            { //Add a new communication
                if (comm.TrackingNumber != null)
                {
                    comm.ExamID = DB11.exam.Where(x => x.exam_number == comm.TrackingNumber).Select(x => x.exam_id).First();

                    var caseID = DB11.exam.Where(x => x.exam_id == comm.ExamID).Select(x => x.case_id).First();
                    if (caseID != null)
                        comm.CaseNumber = <EMAIL>(x => x.case_id == caseID).Select(x => x.case_number).First();
                }
                else
                    comm.TrackingNumber = "";

                if (comm.CommDate == null)
                    comm.CommDate = DateTime.Now;

                if (!string.IsNullOrEmpty(comm.Agency))
                    comm.AgencyID = DB11.agency.Where(x => x.name == comm.Agency).Select(x => x.agency_id).First();

                foreach (ContactViewModel inContact in comm.ExternalParticipants ?? new List<ContactViewModel>())
                {
                    if (comm.ExternalList != null)
                        comm.ExternalList = string.Join(", ", comm.ExternalList, inContact.FirstName.FormatName() + " " + inContact.LastName.FormatName());
                    else
                        comm.ExternalList = inContact.FirstName.FormatName() + " " + inContact.LastName.FormatName();
                }

                foreach (AccountViewModel inAccount in comm.InternalParticipants)
                {
                    comm.InternalList = comm.InternalList != null ? string.Join(", ", comm.InternalList, inAccount.Name.FormatName()) : inAccount.Name.FormatName();
                }

                communication newComm = CreateDatabaseCommunication(comm);
                DB11.SaveChanges();
                AuditInsert(newComm);
                DB11.SaveChanges();
            }
                
            return Json(new[] { comm }.ToDataSourceResult(request, ModelState));
        }


        public ActionResult Details(string id)
        {
            int intid = Int32.Parse(id);
            //return PartialView(DB.communications.Where(x => x.communication_id == intid).First());
            return PartialView(DB11.communication.Where(x => x.communication_id == intid).First());
        }

        public ActionResult New()
        {
            return PartialView("EditLog", new CommunicationViewModel(DB11)
            {
                Action = "Create",
                Source = Request.UrlReferrer.ToString().Replace("#", string.Empty),
                InternalParticipants = new List<AccountViewModel>(new AccountViewModel[] { CurrentUserModel }),
                EditView = "EditLog"
            });
        }

        /// <summary>
        /// Checks to see if current user should be allowed to modify communications associated with provided exam
        /// </summary>
        /// <param name="trackingNumber"></param>
        /// <returns></returns>
        public bool HasCommunicationEditPermission(string trackingNumber)
        {
            var controller = DependencyResolver.Current.GetService<ExamController>();
            controller.ControllerContext = new ControllerContext(this.HttpContext, this.RouteData, controller);
            return controller.HasCommunicationEditPermission(trackingNumber);
        }

        public ActionResult NewForExam(string trackingNumber)
        {
            if (!HasCommunicationEditPermission(trackingNumber))
            {
                throw new UnauthorizedAccessException();
            }
            return PartialView("Edit", new CommunicationViewModel(DB11)
            {
                Action = "Create",
                Exam = trackingNumber,
                Source = Request.UrlReferrer.ToString().Replace("#", string.Empty),
                InternalParticipants = new List<AccountViewModel>(new AccountViewModel[] { CurrentUserModel }),
                EditView = "Edit"
            });
        }

        /// <summary>
        /// Creates new CIMSWeb.Data.communication object and associated the associated communication parties from a 
        /// view model and adds them to the database (but does not commit).
        /// </summary>
        /// <param name="model">CommunicationViewModel representing the new communication</param>
        private communication CreateDatabaseCommunication(CommViewModel model)
        {
            //set model.agencyid and exam_id for new schema
            communication c = model.Create(model, CurrentUserModel.AccountID);

            //c.agency_id = DB11.agency.Where(ag => ag.name == model.Agency).Select(ag => ag.agency_id).FirstOrDefault();

            foreach (var item in model.InternalParticipants)
            {
                account acc = DB11.account.Find(item.AccountID);
                if (acc != null)
                    c.account1.Add(acc);
            }

            foreach (var contacts in model.ExternalParticipants ?? new List<ContactViewModel>())
            {
                //contact exContact = DB11.contact.Where(x => x.contact_id == contacts.ContactID).First();
                contact externContact = DB11.contact.Find(contacts.ContactID);
                if (externContact != null)
                    c.contact.Add(externContact);
            }

            if (model.ExamID == 0)
                c.exam_id = null;

            DB11.communication.Add(c);
            return c;
        }

        public ActionResult Import(int Exam_ID)
        {
            return PartialView("_Import", new ImportCommunicationsViewModel(DB11)
            {
                Action = "ImportSave",
                ExamID = Exam_ID
            });
        }

        [HttpPost]
        //public ActionResult ImportSave(string trackingNumber, string label)
        public ActionResult ImportSave(int Exam_ID, string label)
        {
            var communications = DB11.communication.Where(x => x.label == label && x.exam == null && x.child_id == null).ToList();
            if (communications.Any()) {
                //Update the communication and save
                var newCommList = new List<communication>();
                foreach (var comm in communications)
                {
                    AuditUpdate(comm);
                    comm.exam_id = Exam_ID;
                    DB11.Entry(comm).State = System.Data.Entity.EntityState.Modified;
                }

                DB11.SaveChanges();
            }
            return Json(new { IsValid = true });    
        }

        public ActionResult Communication(int id)
        {
            communication c = DB11.communication.Where(x => x.communication_id == id).First();
            return PartialView("Edit", new CommunicationViewModel(DB11, c)
            {
                Action = "Edit",
                Source = Request.UrlReferrer.ToString().Replace("#", string.Empty)
            });
        }

        public ActionResult CommunicationLog(int id)
        {
            communication c = DB11.communication.Where(x => x.communication_id == id).First();
            return PartialView("EditLog", new CommunicationViewModel(DB11, c)
            {
                Action = "EditLog",
                Source = Request.UrlReferrer.ToString().Replace("#", string.Empty),
                ActionLabel = "Edit"
            });
        }

        public ActionResult NewParticipant()
        {
            ContactViewModel ret = new ContactViewModel()
            {
                RelationshipType = "Customer",
            };
            return PartialView("_Participant", ret);
        }

        [HttpPost]
        public ActionResult SaveParticipant(ContactViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return PartialView("Edit", model);
            }
            // Save the contact
            CIMSData.Database.contact c = model.Create();
            DB11.contact.Add(c);
            DB11.SaveChanges();
            AuditInsert(c);
            DB11.SaveChanges();

            if (!string.IsNullOrEmpty(model.Exam))
            {
                // Point the exam to the contact
                CIMSData.Database.exam ex = DB11.exam.Where(x => x.exam_number == model.Exam).FirstOrDefault();
                switch (model.RelationshipType)
                {
                    case "Customer":
                        AuditUpdate(ex);
                        ex.@case.case_agent_id = c.contact_id;
                        DB11.Entry(ex).State = System.Data.Entity.EntityState.Modified;
                        break;
                    case "DETCO":
                        var subA = ex.@case.agency_org;
                        AuditUpdate(subA);
                        subA.detco_id = c.contact_id;
                        DB11.Entry(subA).State = System.Data.Entity.EntityState.Modified;
                        break;
                    case "Prosecutor":
                        AuditUpdate(ex.exam_standard);
                        ex.@case.prosecutor_id = c.contact_id;
                        DB11.Entry(ex.exam_standard).State = System.Data.Entity.EntityState.Modified;
                        break;
                }
                DB11.SaveChanges();
            }

            //Update Select
            // TODO - port to new schema
            //ViewData.Add("ExternalParticipants", DB.DCFLCONTACTS.Select(x => new ExternalParticipant
            //{
            //    ID = x.ORIGREC,
            //    Name = x.FIRSTNAME + " " + x.LASTNAME + " " + x.EMAIL + " | " + x.COMMERCIAL + " (commercial)"
            //}));

            ViewData.Add("ExternalParticipants", DB11.contact.Select(x => new ExternalParticipant
            {
                ID = x.contact_id,
                Name = x.first_name + " " + x.last_name + " " + x.email + " | " + x.commercial + " (commercial)"
            }));

            return new EmptyResult();
        }

        /// <summary>
        /// Creates new CIMSWeb.Data.communication object and associated the associated communication parties from a 
        /// view model and adds them to the database (but does not commit).
        /// </summary>
        /// <param name="id">Communication label</param>
        [HttpGet]
        public ActionResult GetByLabel(string id)
        {
            string label = id;
            ViewData["label"] = id;
            
            //var dbResult = DB.communications.Where(x => x.label == label && x.dcfltrackingno == null && x.child_id == null).ToList();
            var dbResult = DB11.communication.Where(x => x.label == label && x.exam.exam_number == null && x.child_id == null).ToList();

            var resultList = new List<ImportCommTableViewModel>();

            dbResult.ForEach(r => resultList.Add(new ImportCommTableViewModel
            {
                ID = r.communication_id,
                //Agency = r.agency,
                Agency = r.agency.name,
                //ExternalParticipants = string.Join(", ", r.DCFLCONTACTS.Select(x => x.FIRSTNAME + " " + x.LASTNAME)),
                ExternalParticipants = string.Join(", ", r.contact.Select(x => x.first_name + " " + x.last_name)),
                InternalNotes = r.notes_internal,
                CustomerNotes = r.notes_external
            }));

            return PartialView("_ImportCommtable", resultList);
        }

        /// <summary>
        /// GET import communications by label form
        /// </summary>
        /// <returns></returns>
        //public ActionResult Import()
        //{
        //    return PartialView(new ImportCommunicationsViewModel(DB));
        //}

        /// <summary>
        /// GET export communications
        /// </summary>
        /// <returns></returns>
        private static iTextSharp.text.Font normal = new iTextSharp.text.Font(iTextSharp.text.Font.UNDEFINED, 11, iTextSharp.text.Font.NORMAL);
        private static iTextSharp.text.Font bold = new iTextSharp.text.Font(normal.Family, 11, iTextSharp.text.Font.BOLD);
        private static iTextSharp.text.Font underlined = new iTextSharp.text.Font(normal.Family, 11, iTextSharp.text.Font.UNDERLINE);
        private static iTextSharp.text.Font fontHead = new iTextSharp.text.Font(normal.Family, 20, iTextSharp.text.Font.NORMAL);
        private static iTextSharp.text.Font fontSubHead = new iTextSharp.text.Font(normal.Family, 12, iTextSharp.text.Font.NORMAL);
        private static iTextSharp.text.Font italic = new iTextSharp.text.Font(normal.Family, normal.Size, iTextSharp.text.Font.ITALIC);
        private static iTextSharp.text.Font italicBold = new iTextSharp.text.Font(normal.Family, normal.Size, iTextSharp.text.Font.BOLDITALIC);

        //public ActionResult Export(string trackingNumber)
        public ActionResult Export(int examID)
        {
            //List<communication> c = DB.communications.Where(x => x.dcfltrackingno == trackingNumber && x.child_id == null).OrderBy(x => x.communication_id).ToList();
            //List<communication> c = DB11.communication.Where(x => x.exam.exam_number == trackingNumber && x.child_id == null).OrderBy(x => x.communication_id).ToList();
            List<communication> c = DB11.communication.Where(x => x.exam.exam_id == examID && x.child_id == null).OrderBy(x => x.communication_id).ToList();

            string trackingNumber = DB11.exam.Find(examID).exam_number;

            // Convert to PDF
            var PDFReport = new Document(PageSize.LETTER, 20f, 20f, 25f, 30f); // set margins

            var output = new MemoryStream();

            PdfWriter writer = PdfWriter.GetInstance(PDFReport, output);
            writer.SetFullCompression();
            writer.PageEvent = new PDF_Footer();

            PDFReport.Open();

            PDFReport.Add(new iTextSharp.text.HeaderFooter(new Phrase("Page"), true));

            PdfPTable headerTable = new PdfPTable(1) {WidthPercentage = 100.0f};

            PdfPTable header = new PdfPTable(2) {WidthPercentage = 100.0F};
            header.SetWidths(new float[] { 100, 400 });

            Image pic = Image.GetInstance(System.IO.File.ReadAllBytes(HttpContext.Server.MapPath(@"~/Content/images/DC3_seal.png")));
            PdfPCell picCell = new PdfPCell(pic) {Border = 0};
            header.AddCell(picCell);


            AddCell(headerTable, new Phrase("", fontHead));
            AddCell(headerTable, new Phrase(trackingNumber, fontHead));
            AddCell(headerTable, new Phrase("Customer Communication Report", fontHead));
            //AddCell(headerTable, new Phrase("Provided by the DOD Cyber Crime Center (DC3), TEL: ************, Email: <EMAIL>", normal));
            PdfPCell tCell = new PdfPCell(new Phrase("Generated " + DateTime.Now.ToString(), fontSubHead))
            {
                PaddingTop = 30, Border = 0
            };
            headerTable.AddCell(tCell);
            PdfPCell hCell = new PdfPCell(headerTable) {Border = 0};
            header.AddCell(hCell);

            AddLine(PDFReport, 3);
            PDFReport.Add(header);
            AddLine(PDFReport, -5);

            int commNum = 1;

            // loop to read all records with child_id = null
            foreach (communication inRecord in c.OrderBy(x => x.communication_datetime))
            {
                PDFReport.Add(new Paragraph(Chunk.NEWLINE));

                ProcessCommunication(inRecord, PDFReport, false, commNum);

                //recursively check to see if there were edits to this communication record
                Recurse(inRecord, PDFReport, commNum);
                commNum++;
            }

            PDFReport.Close();

            Response.Clear();
            Response.ContentType = "application/pdf";
            Response.AddHeader("Content-Disposition", string.Format("attachment;filename=" + trackingNumber + "_CommunicationsReport.pdf"));
            Response.BinaryWrite(output.ToArray());
            Response.End();

            return new EmptyResult();
        }

        private void AddLine(Document doc, int pos)
        {
            doc.Add(new Chunk(new iTextSharp.text.pdf.draw.LineSeparator(0.0F, 100.0F, Color.LIGHT_GRAY, Element.ALIGN_LEFT, pos)));
        }

        private void AddCell(PdfPTable table, Phrase p)
        {
            PdfPCell c = new PdfPCell(p) {Border = 0};
            table.AddCell(c);
        }

        private void Recurse(communication checkRecord, Document reportPDF, int commNum)
        {
            communication nextInChain = DB11.communication.FirstOrDefault(x => x.child_id == checkRecord.communication_id);
            if (nextInChain?.exam_id != null)
            {
                ProcessCommunication(nextInChain, reportPDF, true, commNum);
                Recurse(nextInChain, reportPDF, commNum);
            }
        }

        static int padding = 10;
        static float subEditWidth = 93.0f;

        private PdfPTable GetField(string key, string value, bool subEdit, bool padTop, bool padBottom, bool padLeft, bool padRight, bool leftCol, bool rightCol)
        {
            PdfPTable ret = new PdfPTable(2) {
                WidthPercentage = 100.0F
            };
            if (subEdit && !leftCol && !rightCol)
            {
                ret.WidthPercentage = subEditWidth;
                ret.HorizontalAlignment = Element.ALIGN_RIGHT;
            }
            if (leftCol)
            {
                ret.SetWidths(new float[] { 295, 305 });
            }
            else if (rightCol)
            {
                ret.SetWidths(new float[] { 180, 300 });
            }
            else
            {
                ret.SetWidths(new float[] { 130, 400 });
            }
            PdfPCell k = new PdfPCell(new Phrase(key, bold)) {
                HorizontalAlignment = Element.ALIGN_LEFT,
                Border = 0
            };
            if (padTop)
                k.PaddingTop = padding;
            if (padLeft)
                k.PaddingLeft = padding;
            k.PaddingBottom = padBottom ? padding : 5;
            k.PaddingLeft = 10;
            PdfPCell v = new PdfPCell(new Phrase(value, normal)) {Border = 0};
            if (padTop)
                v.PaddingTop = padding;
            if (padRight)
                v.PaddingRight = padding;
            v.PaddingBottom = padBottom ? padding : 5;
            v.PaddingRight = 10;
            if (subEdit)
            {
                k.BackgroundColor = new Color(245, 245, 245);
                v.BackgroundColor = new Color(245, 245, 245);
            }
            else
            {
                k.BackgroundColor = new Color(245, 245, 245);
                v.BackgroundColor = new Color(245, 245, 245);
            }

            ret.AddCell(k);
            ret.AddCell(v);
            return ret;
        }

        private PdfPCell GetEmptyCell()
        {
            PdfPCell c1 = new PdfPCell {Border = 0, Padding = 0};
            return c1;
        }

        private void ProcessCommunication(communication currRec, Document doc, bool subEdit, int commNum)
        {
            account editedBy = DB11.account.Where(x => x.account_id == currRec.entered_by).FirstOrDefault();
            string author = editedBy == null ? string.Empty : editedBy.name;
            if (currRec.communication_type_id == 5)
            {
                author = "[System]";
            }
            string agencyText = string.Empty;
            if (currRec.agency != null)
                agencyText = currRec.agency.name;

            PdfPTable tTable = new PdfPTable(1) {WidthPercentage = 100.0F};

            PdfPCell k = new PdfPCell();
            Phrase p = new Phrase(string.Format("Communication #{0}{1}", commNum, subEdit ? "  (Previous Version)" : string.Empty), bold);
            k.HorizontalAlignment = Element.ALIGN_LEFT;
            k.Border = 0;
            k.PaddingTop = padding;
            k.PaddingLeft = 10;
            k.BackgroundColor = new Color(245, 245, 245);
            if (subEdit)
            {
                tTable.WidthPercentage = subEditWidth;
                tTable.HorizontalAlignment = Element.ALIGN_RIGHT;
            }
            k.AddElement(p);
            k.AddElement(new Chunk(new iTextSharp.text.pdf.draw.LineSeparator(0.0F, 95.0F, Color.LIGHT_GRAY, Element.ALIGN_LEFT, 12)));
            tTable.AddCell(k);
            
            doc.Add(tTable);
            
            PdfPTable doubleWide = new PdfPTable(2);
            if (subEdit)
            {
                doubleWide.HorizontalAlignment = Element.ALIGN_RIGHT;
            }
            doubleWide.WidthPercentage = subEdit ? subEditWidth : 100.0f;
            PdfPCell c1 = GetEmptyCell();
            c1.AddElement(GetField("Authored By", author, subEdit, false, false, true, false, true, false));
            PdfPCell c2 = GetEmptyCell();
            c2.AddElement(GetField("Authored Date", currRec.entered_datetime.ToString(), subEdit, false, false, false, true, false, true));
            doubleWide.AddCell(c1);
            doubleWide.AddCell(c2);
            doc.Add(doubleWide);
            
            doubleWide = new PdfPTable(2);
            if (subEdit)
            {
                doubleWide.HorizontalAlignment = Element.ALIGN_RIGHT;
            }
            doubleWide.WidthPercentage = subEdit ? subEditWidth : 100.0f;
            c1 = GetEmptyCell();
            c1.AddElement(GetField("Communication Date", currRec.communication_datetime.ToString(), subEdit, false, false, true, false, true, false));
            c2 = GetEmptyCell();
            c2.AddElement(GetField("Agency", agencyText, subEdit, false, false, false, true, false, true));
            doubleWide.AddCell(c1);
            doubleWide.AddCell(c2);
            doc.Add(doubleWide);

            doubleWide = new PdfPTable(2);
            if (subEdit)
            {
                doubleWide.HorizontalAlignment = Element.ALIGN_RIGHT;
            }
            doubleWide.WidthPercentage = subEdit ? subEditWidth : 100.0f;
            c1 = GetEmptyCell();
            c1.AddElement(GetField("Type", currRec.communication_type.communication_type1, subEdit, false, false, true, false, true, false));
            c2 = GetEmptyCell();
            c2.AddElement(GetField("Topic", currRec.communication_topic.communication_topic1, subEdit, false, false, false, true, false, true));
            doubleWide.AddCell(c1);
            doubleWide.AddCell(c2);
            doc.Add(doubleWide);

            doubleWide = new PdfPTable(2);
            if (subEdit)
            {
                doubleWide.HorizontalAlignment = Element.ALIGN_RIGHT;
            }
            doubleWide.WidthPercentage = subEdit ? subEditWidth : 100.0f;
            c1 = GetEmptyCell();
            c1.AddElement(GetField("Duration (mins)", currRec.duration.HasValue ? currRec.duration.ToString() : string.Empty, subEdit, false, false, true, false, true, false));
            string followUpText = string.Empty;
            if (currRec.follow_up == 0)
                followUpText = "No";
            else if (currRec.follow_up == 1)
                followUpText = "Yes";
            else
                followUpText = "Complete";

            c2 = GetEmptyCell();
            c2.AddElement(GetField("Follow Up", followUpText, subEdit, false, false, false, true, false, true));
            doubleWide.AddCell(c1);
            doubleWide.AddCell(c2);
            doc.Add(doubleWide);

            doubleWide = new PdfPTable(2);
            if (subEdit)
            {
                doubleWide.HorizontalAlignment = Element.ALIGN_RIGHT;
            }
            doubleWide.WidthPercentage = subEdit ? subEditWidth : 100.0f;
            c1 = GetEmptyCell();
            c1.AddElement(GetField("Title", currRec.label, subEdit, false, false, true, true, true, false));
            c2 = GetEmptyCell();
            //c2.AddElement(GetField("Exam", currRec.dcfltrackingno, subEdit, false, false, true, true, false, true));
            c2.AddElement(GetField("Exam", currRec.exam.exam_number, subEdit, false, false, true, true, false, true));
            doubleWide.AddCell(c1);
            doubleWide.AddCell(c2);
            doc.Add(doubleWide);
            
            
            doc.Add(GetField("Action Required", currRec.action_required, subEdit, false, false, true, true, false, false));

            bool first = true;
            string participantsList = string.Empty;
            //foreach (account internalParticipants in currRec.accounts)
            foreach (account internalParticipants in currRec.account1)
            {
                if (!first)
                    participantsList += ", ";
                else
                    first = false;

                participantsList += internalParticipants.name.FormatName();
            }


            doc.Add(GetField("DCFL Participants", participantsList, subEdit, false, false, true, true, false, false));

            first = true;
            participantsList = string.Empty;
            //foreach (DCFLCONTACT externalParticipants in currRec.DCFLCONTACTS)
            foreach (contact externalParticipants in currRec.contact)
            {
                if (!first)
                    participantsList += ", ";
                else
                    first = false;

                participantsList += (externalParticipants.first_name + " " + externalParticipants.last_name).FormatName();
            }

            doc.Add(GetField("External Participants", participantsList, subEdit, false, false, true, true, false, false));
            doc.Add(GetField("Internal Notes", currRec.notes_internal, subEdit, false, false, true, true, false, false));
            doc.Add(GetField("Customer Notes", currRec.notes_external, subEdit, false, true, true, true, false, false));

            PdfPTable space = new PdfPTable(1);
            PdfPCell spaceCell = new PdfPCell {Border = 0};
            space.AddCell(spaceCell);
            doc.Add(space);
        }
        
        public class PDF_Footer : PdfPageEventHelper
        {
            PdfContentByte cb;
            PdfTemplate template;
            BaseFont timesBold;

            public override void OnOpenDocument(PdfWriter writer, Document document)
            {
                cb = writer.DirectContent;
                template = cb.CreateTemplate(40, 40);
                timesBold = BaseFont.CreateFont(BaseFont.TIMES_BOLD, BaseFont.WINANSI, BaseFont.NOT_EMBEDDED);
            }
            public override void OnEndPage(PdfWriter writer, Document document)
            {
                base.OnEndPage(writer, document);
                cb.BeginText();
                cb.SetFontAndSize(timesBold, 12f);
                cb.SetTextMatrix(document.PageSize.Width / 2, 20);
                cb.ShowText(writer.PageNumber.ToString());
                cb.EndText();
                cb.AddTemplate(template, document.LeftMargin, document.PageSize.GetBottom(document.BottomMargin));
            }
        }

        /// <summary>
        /// GET list of communication labels as JSON
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public JsonResult LabelList()
        {
            //return Json(DB.communications.Select(x => x.label).Distinct().ToList(), JsonRequestBehavior.AllowGet);
            return Json(DB11.communication.Select(x => x.label).Distinct().ToList(), JsonRequestBehavior.AllowGet);
        }
    }
}