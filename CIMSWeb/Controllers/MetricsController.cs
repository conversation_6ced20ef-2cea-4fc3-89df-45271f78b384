﻿using CIMSWeb.Models;
using System;
using System.Data.Entity;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web.Mvc;
using CIMSData.Database;
using CIMSWeb.Models.Metrics;
using CIMSWeb.Attributes;
using CIMSWeb.Workflow;
using Kendo.Mvc.UI;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using WebGrease.Css.Extensions;
using System.Net.Http;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Kendo.Mvc.Extensions;
using Org.BouncyCastle.Asn1.Ocsp;
using System.Web.UI.WebControls;
using Microsoft.Ajax.Utilities;
using System.Text.RegularExpressions;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;

namespace CIMSWeb.Controllers
{
    public class FileTransferRequest
    {
        public string Email { get; set; }
        public List<string> GridData { get; set; }
        public string TableName { get; set; }
    }

    public class MetricsController : CIMSController
    {
        private static readonly HttpClient httpClient = new HttpClient();

        /// <summary>
        /// Exports time entry information to CSV
        /// </summary>
        /// <param name="start"></param>
        /// <param name="end"></param>
        /// <returns></returns>
        [HttpPost]
        [CIMSAuthorize(SecureFunction.TimesheetExport)]
        public ActionResult TimesheetExport(TimesheetExportViewModel p)
        {
            var stream = new MemoryStream();
            var mcCiActivities = new List<int>()
            {
                (int) ActivityType._Perform_Forensic_Examination, (int) ActivityType._Perform_Examination_Review,
                (int) ActivityType._Section_Chief_Examination_Review
            };
            var ieActivities = new List<int>()
            {
                (int) ActivityType._Perform_Forensic_Acquisition, (int) ActivityType._Perform_Acquisition_Review,
                (int) ActivityType._Section_Chief_Acquisition_Review
            };
            using (var package = new ExcelPackage(stream))
            {
                var mcCi = package.Workbook.Worksheets.Add("MC&CI");
                var iE = package.Workbook.Worksheets.Add("I&E");
                PopulateTimeSheetWorkSheets(mcCi, mcCiActivities, p);
                PopulateTimeSheetWorkSheets(iE, ieActivities, p);
                package.Save();
            }

            stream.Position = 0;
            return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        private void PopulateTimeSheetWorkSheets(ExcelWorksheet worksheet, ICollection<int> activityIds,
            TimesheetExportViewModel dateRange)
        {
            var columns = new List<string>
            {
                "Exam", "Date Closed", "Activity", "Activity Section", "User", "User Total Hours on Exam"
            };
            ExtensionMethods.FillInAndStyleHeaderColumnsForExcelWorksheet(worksheet, columns);

            int i = 2;
            foreach (var exam in DB11.exam.Where(x =>
                x.date_closed >= dateRange.Start && x.date_closed < dateRange.End))
            {
                foreach (var ea in exam.exam_activity.Where(x =>
                        x.skipped != true && x.account?.account_type_id == 2 && activityIds.Contains(x.activity_id))
                    .GroupBy(z => new {z.activity_id, z.account_id}).Select(z => z.First()))
                {
                    var hours = exam.account_activity.Where(z => z.account_id == ea.account_id).Sum(z => z.hours);
                    worksheet.Cells[i, 1].Value = exam.exam_number;
                    worksheet.Cells[i, 2].Value = exam.date_closed.Value.ToShortDateString();
                    worksheet.Cells[i, 3].Value = ea.activity.name;
                    worksheet.Cells[i, 4].Value = ea.group?.name ?? exam.group.name;
                    worksheet.Cells[i, 5].Value = ea.account?.name;
                    worksheet.Cells[i, 6].Value = hours;
                    i++;
                }
            }

            worksheet.Cells[1, 1, 50, 6].AutoFitColumns();
        }

        [HttpPost]
        public async Task<ActionResult> SendGridDataEmail(FileTransferRequest request)
        {
            string emailAPI = "http://cims-solr-p.ia.lan/IAExam/SendEmail/";

            string email = request.Email;

            if (!IsValidEmailUsername(email))
            {
                return Json("Error: Invalid Email");
            }

            email += "@us.af.mil";
            string apiUrl = $"{emailAPI}?subject={Uri.EscapeDataString(request.TableName)}&recipient={Uri.EscapeDataString(email)}";
            string csvContent = BuildCSV(request.GridData, true);
            HttpResponseMessage response = new HttpResponseMessage();
            string message;

            try
            {
                // Setup the content of the POST request 
                HttpContent content = new StringContent(csvContent, Encoding.UTF8, "text/plain");

                // Send the POST request
                response = await httpClient.PostAsync(apiUrl, content);
            }
            catch (Exception ex)
            {
                message = ex.Message;
            }
            finally
            {
                if (response.IsSuccessStatusCode)
                {
                    message = $"Successfully sent email {request.TableName} to {request.Email}@us.af.mil";

                    data_transfer_email dte = new data_transfer_email();
                    dte.email = email;
                    dte.subject = request.TableName;
                    dte.data = csvContent;
                    dte.date_sent = DateTime.Now;
                    dte.account_id = CurrentUserModel.AccountID;
                    DB11.data_transfer_email.Add(dte);
                    DB11.SaveChanges();
                }
                else
                {
                    message = $"Failed to send email {request.TableName} to {request.Email}@us.af.mil\r\nError:{response.ReasonPhrase}";
                }
            }
            return Json(message);
        }

        [HttpPost]
        public async Task<ActionResult> SendGridDataFileTransfer(FileTransferRequest request)
        {
            string csvContent = BuildCSV(request.GridData);
            string filename = request.TableName;
            
            // Validate filename
            if (string.IsNullOrWhiteSpace(filename))
            {
                return Json("Error: Filename cannot be empty.");
            }

            if (filename.IndexOfAny(Path.GetInvalidFileNameChars()) >= 0)
            {
                return Json("Error: Filename contains invalid characters.");
            }

            if (filename != Path.GetFileNameWithoutExtension(filename))
            {
                return Json("Error: Filename contains file extension.");
            }

            string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");

            filename = $"{filename} {timestamp}.csv";

            HttpResponseMessage response = new HttpResponseMessage();
            HttpContent content = new StringContent(csvContent, Encoding.UTF8, "text/plain");
            string message;

            //System.IO.File.WriteAllText(@"\\dcci.local\Shares\Transfer\JimmyTrip\debug_cims.txt", csvContent);

            try
            {
                string requestUri = $"http://cims-solr-p.ia.lan/IAExam/SendFileToOpenNet?filename={Uri.EscapeDataString(filename)}";

                // Send the data directly without writing it to a temp file
                response = await httpClient.PostAsync(requestUri, content);
            }
            catch (Exception ex)
            {
                message = ex.Message;
            }
            finally
            {
                if (response.IsSuccessStatusCode)
                {
                    message = $"Successfully sent file {filename} to \\\\opennet.dc3on.lcl\\shares\\Intrusion_DropBox\\Outbound";
                    
                    data_transfer_file dtf = new data_transfer_file();
                    dtf.filename = filename;
                    dtf.data = csvContent;
                    dtf.account_id = CurrentUserModel.AccountID;
                    dtf.date_sent = DateTime.Now;
                    DB11.data_transfer_file.Add(dtf);
                    DB11.SaveChanges();
                }
                else
                {
                    message = $"Failed to send file {filename} \r\nError:{response.StatusCode} {response.ReasonPhrase}";
                }
            }
            return Json(message);
        }

        public static string BuildCSV(List<string> gridData, bool email = false)
        {
            StringBuilder csv = new StringBuilder();

            // Start with two spaces to ensure outlook doesn't trucate the body
            if (email)
            {
                csv.Append("  ");
            }

            // Add data rows
            foreach (var row in gridData)
            {
                csv.AppendLine(row);
            }
            return csv.ToString();
        }

        public static string EscapeCsvValue(object value)
        {
            if (value == null) return "";

            var str = value.ToString();

            // Escape double quotes by doubling them
            if (str.Contains("\""))
                str = str.Replace("\"", "\"\"");

            // Enclose in quotes if it contains commas, quotes, or newlines
            if (str.Contains(",") || str.Contains("\"") || str.Contains("\n") || str.Contains("\r"))
                str = $"\"{str}\"";

            return str;
        }

        public static bool IsValidEmailUsername(string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return false;

            string username;
            string domain = null;

            // Check if input contains '@'
            int atIndex = input.IndexOf('@');
            if (atIndex != -1)
            {
                username = input.Substring(0, atIndex);
                domain = input.Substring(atIndex);

                // Allow only @us.af.mil as valid domain
                if (!domain.Equals("@us.af.mil", StringComparison.OrdinalIgnoreCase))
                    return false;
            }
            else
            {
                username = input;
            }

            // Username must only contain valid characters (RFC 5322 basic set)
            string pattern = @"^[a-zA-Z0-9!#$%&'*+/=?^_`{|}~.-]+$";
            if (!Regex.IsMatch(username, pattern))
                return false;

            // Must not start or end with a dot
            if (username.StartsWith(".") || username.EndsWith("."))
                return false;

            // Must not have consecutive dots
            if (username.Contains(".."))
                return false;

            return true;
        }

        [HttpPost]
        [CIMSAuthorize(SecureFunction.ExamSuspenseExport)]
        public ActionResult ExamSuspenseExportExcel(TimesheetExportViewModel p)
        {
            var stream = new MemoryStream();
            using (var package = new ExcelPackage(stream))
            {
                var mcCi = package.Workbook.Worksheets.Add("MC&CI");
//                var iE = package.Workbook.Worksheets.Add("I&E");
                PopulateExamSuspenseWorkSheets(mcCi, false, p);
//                PopulateExamSuspenseWorkSheets(iE, true, p);
                package.Save();
            }

            stream.Position = 0;
            return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        private void PopulateExamSuspenseWorkSheets(ExcelWorksheet worksheet, bool IE,
            TimesheetExportViewModel dateRange)
        {
            var columns = new List<string>
            {
                "Exam", "Case", "Assigned Section", "Category", "Subcategory", "Lead Examiner",
                "Lead Examiner Account Type", "Date Opened", "Date Closed", "Suspense Date", "Original Suspense Date",
                "# of Times Suspense Date Changed", "QA Review Start", "QA Review Within Suspense Date?",
                "Suspense Date Variance", "Days in Lab"
            };
            ExtensionMethods.FillInAndStyleHeaderColumnsForExcelWorksheet(worksheet, columns);
            int i = 2;

            foreach (var exam in DB11.exam.Where(x => x.date_closed >= dateRange.Start && x.date_closed < dateRange.End
                                                                                       && x.exam_type_id ==
                                                                                       (int) ExamType
                                                                                           .MajorCrimesCounterIntelligence &&
                                                                                       (IE
                                                                                           ? x.exam_standard.account
                                                                                                 .account_type_id == 2
                                                                                           : x.account
                                                                                                 .account_type_id ==
                                                                                             2)))
            {
                var wereAllReviewersContractors = IE
                    ? exam.exam_activity
                        .Where(z => z.activity_id == (int) ActivityType._Perform_Acquisition_Review ||
                                    z.activity_id == (int) ActivityType._Section_Chief_Acquisition_Review)
                        .All(z => z.account.account_type_id == 2)
                    : exam.exam_activity
                        .Where(z => z.activity_id == (int) ActivityType._Perform_Examination_Review ||
                                    z.activity_id == (int) ActivityType._Section_Chief_Examination_Review)
                        .All(z => z.account.account_type_id == 2);
                var suspenseDate = IE ? exam.exam_standard.suspense_ie : exam.exam_standard.suspense_lab;
                DateTime? originalSuspenseDate;
                var originalSuspenseDateRow = exam.exam_event.Where(z =>
                        z.exam_event_type_id ==
                        (IE
                            ? (int) ExamEventTypes.Reset_IE_Suspense_Date
                            : (int) ExamEventTypes.Reset_Lab_Suspense_Date)).OrderBy(z => z.event_date)
                    .FirstOrDefault();
                if (suspenseDate.HasValue && originalSuspenseDateRow == null)
                {
                    // Suspense date was set before exam event table was implemented.
                    originalSuspenseDate = suspenseDate;
                }
                else if (!suspenseDate.HasValue && originalSuspenseDateRow == null)
                {
                    // Suspense date was never set.
                    originalSuspenseDate = null;
                }
                else
                {
                    // Take the earliest suspense date from the exam event table.
                    var split = originalSuspenseDateRow.event_info.Split(' ');
                    var dateFromString = split[split.Length - 1];
                    originalSuspenseDate = DateTime.Parse(dateFromString);
                }

                int numberOfSuspenseUpdates = exam.exam_event.Count(z =>
                    z.exam_event_type_id ==
                    (IE ? (int) ExamEventTypes.Reset_IE_Suspense_Date : (int) ExamEventTypes.Reset_Lab_Suspense_Date));
                if (numberOfSuspenseUpdates > 0) numberOfSuspenseUpdates -= 1;
                string closedWithin = "";
                TimeSpan? suspenseVariance = null;
                var qaStart = exam.exam_activity.First(z => z.activity_id == (int) ActivityType._Perform_QA_Review)
                    .date_start.Value.Date;
                if (suspenseDate.HasValue)
                {
                    closedWithin = suspenseDate >= qaStart ? "Yes" : "No";
                    suspenseVariance = (TimeSpan) (qaStart - suspenseDate);
                }

                int daysInLab = (exam.date_closed - exam.date_opened).Value.Days;

                worksheet.Cells[i, 1].Value = exam.exam_number;
                worksheet.Cells[i, 2].Value = exam.@case?.case_number;
                worksheet.Cells[i, 3].Value = exam.group.name;
                worksheet.Cells[i, 4].Value = exam.exam_category?.exam_category1;
                worksheet.Cells[i, 5].Value = exam.exam_category?.exam_subcategory?.Replace(",", "");
                worksheet.Cells[i, 6].Value = IE ? exam.exam_standard.account?.name : exam.account?.name;
                worksheet.Cells[i, 7].Value =
                    IE
                        ? exam.exam_standard.account?.account_type?.name?.TitleCase()
                        : exam.account?.account_type?.name?.TitleCase();
                worksheet.Cells[i, 8].Value = exam.date_opened.Value.ToShortDateString();
                worksheet.Cells[i, 9].Value = exam.date_closed.Value.ToShortDateString();
                worksheet.Cells[i, 10].Value = suspenseDate.HasValue ? suspenseDate.Value.ToShortDateString() : "";
                worksheet.Cells[i, 11].Value =
                    originalSuspenseDate.HasValue ? originalSuspenseDate.Value.ToShortDateString() : "";
                worksheet.Cells[i, 12].Value = numberOfSuspenseUpdates;
                worksheet.Cells[i, 13].Value = qaStart.ToShortDateString();
                worksheet.Cells[i, 14].Value = closedWithin;
                worksheet.Cells[i, 15].Value = suspenseVariance.HasValue ? suspenseVariance.Value.Days.ToString() : "";
                worksheet.Cells[i, 16].Value = daysInLab;

                if (wereAllReviewersContractors)
                {
                    using (var range = worksheet.Cells[i, 1, i, columns.Count])
                    {
                        range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                        range.Style.Fill.BackgroundColor.SetColor(
                            suspenseVariance.HasValue && suspenseVariance.Value.Days > 0
                                ? System.Drawing.Color.Yellow
                                : System.Drawing.Color.Moccasin);
                    }
                }
                i++;
            }

            worksheet.Cells[1, 1, 50, 16].AutoFitColumns();
        }

        [HttpPost]
        [CIMSAuthorize(SecureFunction.ExamSuspenseExport)]
        public ActionResult ExamSuspenseExport(TimesheetExportViewModel p)
        {
            using (var sw = new StringWriter())
            {
                sw.WriteLine(
                    "Exam, Case, Assigned Section, Category, Subcategory, Lead Examiner, Lead Examiner Account Type, Date Opened, Date Closed," +
                    " Suspense Date Lab, Original Suspense Date Lab, # of Times Suspense Date Changed, QA Review Start, QA Review Within Suspense Date?, Suspense Date Variance, Days in Lab");
                foreach (var exam in DB11.exam.Where(x => x.date_closed >= p.Start && x.date_closed < p.End
                                                                                   && x.exam_type_id ==
                                                                                   (int) ExamType
                                                                                       .MajorCrimesCounterIntelligence &&
                                                                                   x.account.account_type_id == 2))
                {
                    var wereAllReviewersContractors = exam.exam_activity
                        .Where(z => z.activity_id == (int) ActivityType._Perform_Examination_Review ||
                                    z.activity_id == (int) ActivityType._Section_Chief_Examination_Review)
                        .All(z => z.account.account_type_id == 2);
                    var suspenseDate = exam.exam_standard.suspense_lab;
                    DateTime? originalSuspenseDate;
                    var originalSuspenseDateRow = exam.exam_event.Where(z =>
                            z.exam_event_type_id == (int) ExamEventTypes.Reset_Lab_Suspense_Date)
                        .OrderBy(z => z.event_date)
                        .FirstOrDefault();
                    if (suspenseDate.HasValue && originalSuspenseDateRow == null)
                    {
                        // Suspense date was set before exam event table was implemented.
                        originalSuspenseDate = suspenseDate;
                    }
                    else if (!suspenseDate.HasValue && originalSuspenseDateRow == null)
                    {
                        // Suspense date was never set.
                        originalSuspenseDate = null;
                    }
                    else
                    {
                        // Take the earliest suspense date from the exam event table.
                        var split = originalSuspenseDateRow.event_info.Split(' ');
                        var dateFromString = split[split.Length - 1];
                        originalSuspenseDate = DateTime.Parse(dateFromString);
                    }

                    int numberOfSuspenseUpdates = exam.exam_event.Count(z =>
                        z.exam_event_type_id == (int) ExamEventTypes.Reset_Lab_Suspense_Date);
                    if (numberOfSuspenseUpdates > 0) numberOfSuspenseUpdates -= 1;
                    string closedWithin = "";
                    TimeSpan? suspenseVariance = null;
                    var qaStart = exam.exam_activity.First(z => z.activity_id == (int) ActivityType._Perform_QA_Review)
                        .date_start.Value.Date;
                    if (suspenseDate.HasValue)
                    {
                        closedWithin = suspenseDate >= qaStart ? "Yes" : "No";
                        suspenseVariance = (TimeSpan) (qaStart - suspenseDate);
                    }

                    int daysInLab = (exam.date_closed - exam.date_opened).Value.Days;
                    string line = string.Format(
                        "{0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12}, {13}, {14}, {15}", exam.exam_number,
                        exam.@case?.case_number, exam.group.name,
                        exam.exam_category?.exam_category1, exam.exam_category?.exam_subcategory?.Replace(",", ""),
                        exam.account?.name,
                        exam.account?.account_type?.name?.TitleCase(), exam.date_opened.Value.ToShortDateString(),
                        exam.date_closed.Value.ToShortDateString(),
                        suspenseDate.HasValue ? suspenseDate.Value.ToShortDateString() : "",
                        originalSuspenseDate.HasValue ? originalSuspenseDate.Value.ToShortDateString() : "",
                        numberOfSuspenseUpdates, qaStart.ToShortDateString(), closedWithin,
                        suspenseVariance.HasValue ? suspenseVariance.Value.Days.ToString() : string.Empty, daysInLab);
                    sw.WriteLine(line);
                }

                Response.Clear();
                Response.ContentType = "application/octet-stream";
                Response.AddHeader("Content-Disposition",
                    "attachment;filename=ExamSuspense_" + DateTime.Now.ToString().Replace(' ', '_') + ".csv");
                Response.Write(sw.ToString());
                Response.End();
            }

            return new EmptyResult();
        }

        public ActionResult IAMetrics(int? month = null)
        {
            var results = DB11.RunCimsMetrics(month).ToList().Single();
            return Json(results, JsonRequestBehavior.AllowGet);
        }
        
        public ActionResult DCISEMetrics(DateTime? dateEnd = null)
        {
            var results = DB11.TotalDCISEExamsAndHours(dateEnd).ToList();
            return Json(results, JsonRequestBehavior.AllowGet);
        }

        public ActionResult CFLSLA01([DataSourceRequest] DataSourceRequest request)
        {
            var results = DB11.SLA01_2025().OrderByDescending(x => x.Reporting_Month).ToList();
            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult CFLSLA02([DataSourceRequest] DataSourceRequest request)
        {
            var results = DB11.SLA02_2025().OrderByDescending(x => x.Performance_Period).ToList();
            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult CFLSLA03([DataSourceRequest] DataSourceRequest request)
        {
            var results = DB11.SLA03_2025().OrderByDescending(x => x.Reporting_Month).ToList();
            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult CFLSLA04([DataSourceRequest] DataSourceRequest request)
        {
            var results = DB11.SLA04_2025().OrderByDescending(x => x.Reporting_Month).ToList();
            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult DatabaseQueries()
        {
            var dbName = DB11.Database.Connection.Database;
            ViewBag.Views = DB11.Database.SqlQuery<string>($"SELECT TABLE_NAME FROM {dbName}.INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'view'").Select( x=> new {Name = x}).OrderBy(x => x.Name).ToList();
            return PartialView();
        }

        [HttpPost]
        public ActionResult RunDatabaseQuery(DatabaseQueryModel model)
        {
            var dbName = DB11.Database.Connection.Database;
            var stream = new MemoryStream();
            var now = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            using (var package = new ExcelPackage(stream))
            {
                var workSheet = package.Workbook.Worksheets.Add(model.ViewName);
                using (var command = DB11.Database.Connection.CreateCommand())
                {
                    command.CommandText = $"SELECT * FROM[{dbName}].[cims].[{model.ViewName}]";
                    DB11.Database.Connection.Open();
                    using (var reader = command.ExecuteReader())
                    {
                        var i = 1;
                        while (reader.Read())
                        {
                            if (i == 1)
                            {
                                var headers = new List<string>();
                                for (int k = 0; k < reader.FieldCount; k++)
                                {
                                    headers.Add(reader.GetName(k));
                                }
                                ExtensionMethods.FillInAndStyleHeaderColumnsForExcelWorksheet(workSheet, headers);
                                i++;
                            }
                            for (int j = 0; j < reader.FieldCount; j++)
                            {
                                workSheet.Cells[i, j + 1].Value = reader.GetValue(j);
                            }
                            i++;
                        }
                    }
                    DB11.Database.Connection.Close();
                }
                package.Save();
            }

            stream.Position = 0;
            return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{model.ViewName}-{now}.xlsx");
        }


        public ActionResult Communications()
        {
            ToolbarViewModel vm = new ToolbarViewModel();
            vm.SelectedOption = "YN";

            var commViewModel = new CommViewModel
            {
                CommMetrics = new List<CommMetric>(),
                Toolbar = vm
            };

            return PartialView(commViewModel);
        }

        [HttpGet]
        public ActionResult GetCommByDate(ToolbarViewModel vm)
        {
            var commMetrics = new List<CommMetric>();
            double count = commMetrics.Count();
            double rate = 0;
            if (count > 0)
            {
                rate = ((count - (double) commMetrics.Count(x => x.Delta > 4)) / count) * 100;
            }

            var result = new
            {
                score = rate.ToString("n1"),
                metrics = commMetrics
            };
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        public ActionResult TotalClosedExams([DataSourceRequest] DataSourceRequest request, string startString,
            string endString, int[] holdTypes)
        {
            var start = DateTime.Parse(startString);
            var end = DateTime.Parse(endString);

            if (holdTypes == null || holdTypes.Length == 0)
            {
                return KendoGridResult(new List<ClosedExamsMetricsViewModel>().AsQueryable(), request);
            }

            // Query the database for all of the exams that contain holdTypes
            var filteredExams =
                (from e in DB11.exam_hold.Include(nameof(DB11.exam))
                    join ea in DB11.exam_activity on e.exam_activity_id equals ea.exam_activity_id
                    where ea.exam.date_closed >= start && ea.exam.date_closed <= end
                                                       && holdTypes.Contains(e.exam_hold_type_id)
                                                       && ea.exam.exam_status_id == 5
                    select ea.exam).Distinct().AsEnumerable();

            // Get all exams that do not have any holds
            if (holdTypes.Contains(0))
            {
                var examsWithoutHold =
                    (from e in DB11.exam.Include(nameof(DB11.exam_activity))
                        where e.date_closed >= start && e.date_closed <= end
                                                     && e.exam_status_id == 5
                                                     && !e.exam_activity.SelectMany(z => z.exam_hold).ToList().Any()
                        select e).Distinct().AsEnumerable();

                // Combine the results of the queries
                filteredExams = filteredExams.Union(examsWithoutHold);
            }

            var examGroupings = new List<ClosedExamsMetricsViewModel>();

            var ciNotSafety =
                (from e in filteredExams
                    where e.exam_type_id == 3 && e.group_id == 2 && e.exam_category_id != 40 && e.exam_category_id != 21
                    select e).ToList();
            examGroupings.Add(new ClosedExamsMetricsViewModel(ciNotSafety, "CI - Not CI Safety"));

            var imagingAndExtraction =
                (from e in filteredExams
                    where e.exam_type_id == 3 && e.group_id == 6
                    select e).ToList();
            examGroupings.Add(new ClosedExamsMetricsViewModel(imagingAndExtraction, "Imaging & Extraction"));

            var intrusions =
                (from e in filteredExams
                    join ei in DB11.exam_intrusion on e.exam_id equals ei.exam_id
                    where e.exam_type_id == 1
                    select e).ToList();
            examGroupings.Add(new ClosedExamsMetricsViewModel(intrusions, "Intrusions"));

            var litigation =
                (from e in filteredExams
                    where e.exam_type_id == 2
                    select e).ToList();
            examGroupings.Add(new ClosedExamsMetricsViewModel(litigation, "Litigation"));

            var mcNotSafety =
                (from e in filteredExams
                    where e.exam_type_id == 3 && e.group_id == 9 && e.exam_category_id != 40 && e.exam_category_id != 21
                    select e).ToList();
            examGroupings.Add(new ClosedExamsMetricsViewModel(mcNotSafety, "MC - Not MC Safety"));

            var mcCiSafety =
                (from e in filteredExams
                    where (e.group_id == 2 || e.group_id == 9) && (e.exam_category_id == 40 || e.exam_category_id == 21)
                    select e).ToList();
            examGroupings.Add(new ClosedExamsMetricsViewModel(mcCiSafety, "SAFETY - MC and CI Safety"));

            var totals = new ClosedExamsMetricsViewModel()
            {
                Group = "Total/Average",
                Count = examGroupings.Sum(x => x.Count),
                InsiderThreatCount = examGroupings.Sum(x => x.InsiderThreatCount),
                TotalTBs = Math.Round((double) examGroupings.Sum(x => x.TotalTBs), 2),
                AverageTBExam =
                    Math.Round((double) examGroupings.Sum(x => x.TotalTBs) / examGroupings.Sum(x => x.Count), 2),
                AverageEvidenceExam =
                    Math.Round((double) examGroupings.Sum(x => x.TotalEvidence) / examGroupings.Sum(x => x.Count), 2),
                AverageDays = Math.Round(examGroupings.Sum(x => x.TotalDaysInLab) / examGroupings.Sum(x => x.Count), 2),
                AverageHoldDays = Math.Round(examGroupings.Sum(x => x.TotalHoldDays) / examGroupings.Sum(x => x.Count),
                    2),
                AverageDaysWithoutHold =
                    Math.Round(examGroupings.Sum(x => x.TotalDaysWithoutHold) / examGroupings.Sum(x => x.Count), 2),
                AverageHours = Math.Round(examGroupings.Sum(x => x.TotalHours) / examGroupings.Sum(x => x.Count), 2),
                Exams = examGroupings.SelectMany(z => z.Exams).ToList()
            };
            examGroupings.Add(totals);
            return KendoGridResult(examGroupings.AsQueryable(), request);
        }

        public ActionResult TotalClosedExamsByCategory([DataSourceRequest] DataSourceRequest request,
            string startString, string endString,
            int[] holdTypes, MetricsViewModel.CFLMetricsTypes examType)
        {
            var start = DateTime.Parse(startString);
            var end = DateTime.Parse(endString);

            if (holdTypes == null || holdTypes.Length == 0)
            {
                return KendoGridResult(new List<ClosedExamsMetricsViewModel>().AsQueryable(), request);
            }

            // Query the database for all of the exams that contain holdTypes
            var filteredExams =
                (from e in DB11.exam_hold.Include(nameof(DB11.exam))
                    join ea in DB11.exam_activity on e.exam_activity_id equals ea.exam_activity_id
                    where ea.exam.date_closed >= start && ea.exam.date_closed <= end
                                                       && holdTypes.Contains(e.exam_hold_type_id)
                                                       && ea.exam.exam_status_id == 5
                    select ea.exam).Distinct();

            // Get all exams that do not have any holds
            if (holdTypes.Contains(0))
            {
                var examsWithoutHold =
                    (from e in DB11.exam.Include(nameof(DB11.exam_activity))
                        where e.date_closed >= start && e.date_closed <= end
                                                     && e.exam_status_id == 5
                                                     && !e.exam_activity.SelectMany(z => z.exam_hold).ToList().Any()
                        select e).Distinct();

                // Combine the results of the queries
                filteredExams = filteredExams.Union(examsWithoutHold);
            }

            if (examType != MetricsViewModel.CFLMetricsTypes.All)
            {
                switch (examType)
                {
                    case MetricsViewModel.CFLMetricsTypes.All:
                        break;
                    case MetricsViewModel.CFLMetricsTypes.CI:
                        filteredExams = filteredExams.Where(z =>
                            z.exam_investigation_type_id == (int) ExamInvestigationType.CI);
                        break;
                    case MetricsViewModel.CFLMetricsTypes.Criminal:
                        filteredExams = filteredExams.Where(z =>
                            z.exam_investigation_type_id == (int) ExamInvestigationType.Criminal);
                        break;
                    case MetricsViewModel.CFLMetricsTypes.DMR:
                        filteredExams = filteredExams.Where(z => z.exam_evidence.Any(x => x.dmr == true));
                        break;
                    case MetricsViewModel.CFLMetricsTypes.ExtractionOnly:
                        filteredExams = filteredExams.Where(z => z.exam_standard.extraction == true);
                        break;
                    default:
                        throw new ArgumentOutOfRangeException(nameof(examType), examType, null);
                }
            }

            var examCategories = new List<ClosedCategoryMetricsViewModel>();
            var categories =
                (from c in DB11.exam_category
                    orderby c.exam_category1, c.exam_subcategory
                    select c).ToList();

            foreach (var c in categories)
            {
                var temp =
                    (from e in filteredExams
                        where e.exam_category_id == c.exam_category_id
                        select e).ToList();

                if (temp.Any())
                {
                    examCategories.Add(new ClosedCategoryMetricsViewModel(temp, c.exam_category1, c.exam_subcategory));
                }
            }

            var totals = new ClosedCategoryMetricsViewModel()
            {
                Category = "Total/Average",
                SubCategory = "",
                Count = examCategories.Sum(x => x.Count),
                InsiderThreatCount = examCategories.Sum(x => x.InsiderThreatCount),
                TotalTBs = Math.Round((double) examCategories.Sum(x => x.TotalTBs), 2),
                AverageTBExam =
                    Math.Round((double) examCategories.Sum(x => x.TotalTBs) / examCategories.Sum(x => x.Count), 2),
                AverageEvidenceExam =
                    Math.Round((double) examCategories.Sum(x => x.TotalEvidence) / examCategories.Sum(x => x.Count), 2),
                AverageDays = Math.Round(examCategories.Sum(x => x.TotalDaysInLab) / examCategories.Sum(x => x.Count),
                    2),
                AverageHoldDays =
                    Math.Round(examCategories.Sum(x => x.TotalHoldDays) / examCategories.Sum(x => x.Count), 2),
                AverageDaysWithoutHold =
                    Math.Round(examCategories.Sum(x => x.TotalDaysWithoutHold) / examCategories.Sum(x => x.Count), 2),
                AverageHours = Math.Round(examCategories.Sum(x => x.TotalHours) / examCategories.Sum(x => x.Count), 2),
                Exams = examCategories.SelectMany(z => z.Exams).ToList()
            };
            examCategories.Add(totals);

            return KendoGridResult(examCategories.AsQueryable(), request);
        }

        public ActionResult ExamsInSections([DataSourceRequest] DataSourceRequest request, string startString,
            string endString)
        {
            var start = DateTime.Parse(startString);
            var end = DateTime.Parse(endString);

            List<int> activitiesList = new List<int>()
            {
                2 //ARA - Admin Requested Adjustment
                ,
                3 //Acquisition Tech Review
                ,
                6 //Administrative Review
                ,
                27 //Create Acquisition Notes
                ,
                28 //Create Examination Report
                ,
                30 //Create and Sign Examination Report
                ,
                41 //Examination Tech Review
                ,
                42 //Final Section Chief Review of Examination Report
                ,
                43 //Forensic Acquisition
                ,
                44 //Forensic Examination
                ,
                60 //Perform Acquisition Tech Review
                ,
                61 //Perform Admin Review
                ,
                62 //Perform Forensic Acquisition
                ,
                63 //Perform Forensic Examination
                ,
                66 //Perform Media Forensic Acquisition
                ,
                69 //Perform Tech Review
                ,
                93 //Section Chief Review of Acquisition Notes
                ,
                94 //Section Chief Review of Examination Report
                ,
                100 //Sign Acquisition Notes
                ,
                101 //Sign Examination Report
                ,
                102 //TARA - Technical Acquisition Requested Adjustment
                ,
                103 //TRA - Technical Requested Adjustment
                ,
                105 //Technical Review
                ,
                118 //Perform Forensic Acquisition
                ,
                119 //Sign Acquisition Notes
                ,
                121 //Perform Acquisition Review
                ,
                122 //Correct Acquisition From Acquisition Review
                ,
                123 //Section Chief Acquisition Review
                ,
                124 //Perform Forensic Examination
                ,
                125 //Sign Examination Notes
                ,
                127 //Perform Examination Review
                ,
                128 //Correct Examination From Examination Review
                ,
                129 //Section Chief Examination Review
                ,
                130 //Correct Acquisition From Section Chief Review
                ,
                131 //Correct Examination From Section Chief Review
            };
            var activities = (
                from a in DB11.activity
                where activitiesList.Contains(a.activity_id)
                select a.activity_id
            );

            var exams = (
                from e in DB11.exam
                where e.exam_status_id == 5 // DONE
                      && e.exam_type_id == 3 // Standard Exams
                      && e.date_closed >= start
                      && e.date_closed <= end
                select new {e.exam_id, e.date_opened}
            );

            var ie_sec = (
                from ea in DB11.exam_activity
                join e in exams on ea.exam_id equals e.exam_id
                join a in activities on ea.activity_id equals a
                where ea.group_id == 6 && ea.date_end.HasValue && ea.date_start.HasValue
                group ea by new {ea.exam_id, ea.exam}
                into g
                select new
                {
                    exam_id = g.Key.exam_id,
                    exam = g.Key.exam,
                    min_start = g.Min(x => x.date_start),
                    max_end = g.Max(x => x.date_end),
                    days_in_section = (double) DbFunctions.DiffHours(g.Min(x => x.date_start), g.Max(x => x.date_end)) /
                                      24.0
                }
            );

            var ci_sec = (
                from ea in DB11.exam_activity
                join e in exams on ea.exam_id equals e.exam_id
                join a in activities on ea.activity_id equals a
                where ea.group_id == 2 && ea.date_end.HasValue && ea.date_start.HasValue
                group ea by new {ea.exam_id, ea.exam}
                into g
                select new
                {
                    exam_id = g.Key.exam_id,
                    exam = g.Key.exam,
                    min_start = g.Min(x => x.date_start),
                    max_end = g.Max(x => x.date_end),
                    days_in_section = (double) DbFunctions.DiffHours(g.Min(x => x.date_start), g.Max(x => x.date_end)) /
                                      24.0,
                    days_until_examination =
                        (double) DbFunctions.DiffHours(g.Min(x => x.exam.date_opened), g.Min(x => x.date_start)) / 24.0
                }
            );

            var mc_sec = (
                from ea in DB11.exam_activity
                join e in exams on ea.exam_id equals e.exam_id
                join a in activities on ea.activity_id equals a
                where ea.group_id == 9 && ea.date_end.HasValue && ea.date_start.HasValue
                group ea by new {ea.exam_id, ea.exam}
                into g
                select new
                {
                    exam_id = g.Key.exam_id,
                    exam = g.Key.exam,
                    min_start = g.Min(x => x.date_start),
                    max_end = g.Max(x => x.date_end),
                    days_in_section = (double) DbFunctions.DiffHours(g.Min(x => x.date_start), g.Max(x => x.date_end)) /
                                      24.0,
                    days_until_examination =
                        (double) DbFunctions.DiffHours(g.Min(x => x.exam.date_opened), g.Min(x => x.date_start)) / 24.0
                }
            );

            var ieci = (
                from ie in ie_sec
                join ci in ci_sec on ie.exam_id equals ci.exam_id
                select new
                {
                    exam_id = ie.exam_id,
                    overlap = (ci.min_start >= ie.min_start && ci.min_start < ie.max_end)
                        ? (double) DbFunctions.DiffHours(ci.min_start, ie.max_end) / 24.0
                        : 0,
                    days_between_sections = (ci.min_start > ie.max_end)
                        ? (double) DbFunctions.DiffHours(ie.max_end, ci.min_start) / 24.0
                        : 0
                }
            );

            var iemc = (
                from ie in ie_sec
                join mc in mc_sec on ie.exam_id equals mc.exam_id
                select new
                {
                    exam_id = ie.exam_id,
                    overlap = (mc.min_start >= ie.min_start && mc.min_start < ie.max_end)
                        ? (double) DbFunctions.DiffHours(mc.min_start, ie.max_end) / 24.0
                        : 0,
                    days_between_sections = (mc.min_start > ie.max_end)
                        ? (double) DbFunctions.DiffHours(ie.max_end, mc.min_start) / 24.0
                        : 0
                }
            );

            var results = new List<ExamsInSectionsViewModel>();

            results.Add(new ExamsInSectionsViewModel("Days in IE",
                ie_sec.OrderBy(x => x.days_in_section).Select(ie => ie.days_in_section),
                ie_sec.Select(ie => ie.exam).OrderBy(x => x.exam_number).ToList()
            ));

            results.Add(new ExamsInSectionsViewModel("Days in CI",
                ci_sec.OrderBy(x => x.days_in_section).Select(ci => ci.days_in_section),
                ci_sec.Select(ci => ci.exam).OrderBy(x => x.exam_number).ToList()
            ));

            results.Add(new ExamsInSectionsViewModel("Days in MC",
                mc_sec.OrderBy(x => x.days_in_section).Select(mc => mc.days_in_section),
                mc_sec.Select(mc => mc.exam).OrderBy(x => x.exam_number).ToList()
            ));

            results.Add(new ExamsInSectionsViewModel("Days Between Exam Opening and CI Starting",
                ci_sec.OrderBy(x => x.days_until_examination).Select(ci => ci.days_until_examination),
                new List<exam>()
            ));

            results.Add(new ExamsInSectionsViewModel("Days Between Exam Opening and MC Starting",
                mc_sec.OrderBy(x => x.days_until_examination).Select(mc => mc.days_until_examination),
                new List<exam>()
            ));

            results.Add(new ExamsInSectionsViewModel("Days Between IE and CI",
                ieci.OrderBy(x => x.days_between_sections).Select(ic => ic.days_between_sections),
                new List<exam>()
            ));

            results.Add(new ExamsInSectionsViewModel("Days Between IE and MC",
                iemc.OrderBy(x => x.days_between_sections).Select(im => im.days_between_sections),
                new List<exam>()
            ));

            results.Add(new ExamsInSectionsViewModel("Days of Overlap in IE and CI",
                ieci.Where(ic => ic.overlap > 0).OrderBy(x => x.overlap).Select(ic => ic.overlap),
                new List<exam>()
            ));

            results.Add(new ExamsInSectionsViewModel("Days of Overlap in IE and MC",
                iemc.Where(im => im.overlap > 0).OrderBy(x => x.overlap).Select(im => im.overlap),
                new List<exam>()
            ));

            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult TotalIntrusionsClosedExams([DataSourceRequest] DataSourceRequest request,
            string startString, string endString, int[] holdTypes)
        {
            var start = DateTime.Parse(startString);
            var end = DateTime.Parse(endString);

            if (holdTypes == null || holdTypes.Length == 0)
            {
                return KendoGridResult(new List<ClosedExamsMetricsViewModel>().AsQueryable(), request);
            }

            // Query the database for all of the exams that contain holdTypes
            var filteredExams =
                (from e in DB11.exam_hold.Include(nameof(DB11.exam))
                    join ea in DB11.exam_activity on e.exam_activity_id equals ea.exam_activity_id
                    where ea.exam.date_closed >= start && ea.exam.date_closed <= end
                                                       && holdTypes.Contains(e.exam_hold_type_id)
                                                       && ea.exam.exam_status_id == 5 && ea.exam.exam_type_id ==
                                                       (int) ExamType.Intrusions
                    select ea.exam).Distinct().AsEnumerable();

            // Get all exams that do not have any holds
            if (holdTypes.Contains(0))
            {
                var examsWithoutHold =
                    (from e in DB11.exam.Include(nameof(DB11.exam_activity))
                        where e.date_closed >= start && e.date_closed <= end
                                                     && e.exam_status_id == 5
                                                     && !e.exam_activity.SelectMany(z => z.exam_hold).ToList().Any()
                        select e).Distinct().AsEnumerable();

                // Combine the results of the queries
                filteredExams = filteredExams.Union(examsWithoutHold);
            }

            var examGroupings = new List<ClosedExamsMetricsViewModel>();

            var intrusionsAg =
                (from e in filteredExams
                    join ei in DB11.exam_intrusion on e.exam_id equals ei.exam_id
                    where e.exam_type_id == 1 && ei.directorate_id == 2 && ei.is_lab_source == true
                    select e).ToList();
            examGroupings.Add(new ClosedExamsMetricsViewModel(intrusionsAg, "IN-AG"));

            var intrusionsDcise =
                (from e in filteredExams
                    join ei in DB11.exam_intrusion on e.exam_id equals ei.exam_id
                    where e.exam_type_id == 1 && ei.directorate_id == 3 && ei.is_lab_source == true
                    select e).ToList();
            examGroupings.Add(new ClosedExamsMetricsViewModel(intrusionsDcise, "IN-DCISE"));

            var intrusionsOther =
                (from e in filteredExams
                    join ei in DB11.exam_intrusion on e.exam_id equals ei.exam_id
                    where e.exam_type_id == 1 && ei.is_lab_source == true && ei.directorate_id != 2 &&
                          ei.directorate_id != 3
                    select e).ToList();
            examGroupings.Add(new ClosedExamsMetricsViewModel(intrusionsOther, "IN-OTHER"));

            var agEms =
                (from e in filteredExams
                    join ei in DB11.exam_intrusion on e.exam_id equals ei.exam_id
                    where e.exam_type_id == 1 && ei.directorate_id == 2 && ei.is_lab_source == false
                    select e).ToList();
            examGroupings.Add(new ClosedExamsMetricsViewModel(agEms, "IN-AG(EMS)"));

            var dciseEms =
                (from e in filteredExams
                    join ei in DB11.exam_intrusion on e.exam_id equals ei.exam_id
                    where e.exam_type_id == 1 && ei.directorate_id == 3 && ei.is_lab_source == false
                    select e).ToList();
            examGroupings.Add(new ClosedExamsMetricsViewModel(dciseEms, "IN-DCISE(EMS)"));

            var intrusionsOtherEms =
                (from e in filteredExams
                    join ei in DB11.exam_intrusion on e.exam_id equals ei.exam_id
                    where e.exam_type_id == 1 && ei.is_lab_source == false && ei.directorate_id != 2 &&
                          ei.directorate_id != 3
                    select e).ToList();
            examGroupings.Add(new ClosedExamsMetricsViewModel(intrusionsOtherEms, "IN-OTHER(EMS)"));

            var totals = new ClosedExamsMetricsViewModel()
            {
                Group = "Total/Average",
                Count = examGroupings.Sum(x => x.Count),
                Exams = examGroupings.SelectMany(z => z.Exams).ToList(),
                TotalTBs = Math.Round((double) examGroupings.Sum(x => x.TotalTBs), 2),
                AverageTBExam =
                    Math.Round((double) examGroupings.Sum(x => x.TotalTBs) / examGroupings.Sum(x => x.Count), 2),
                AverageEvidenceExam =
                    Math.Round((double) examGroupings.Sum(x => x.TotalEvidence) / examGroupings.Sum(x => x.Count), 2),
                AverageDays = Math.Round(examGroupings.Sum(x => x.TotalDaysInLab) / examGroupings.Sum(x => x.Count), 2),
                AverageHours = Math.Round(examGroupings.Sum(x => x.TotalHours) / examGroupings.Sum(x => x.Count), 2),
                AverageHoldDays = Math.Round(examGroupings.Sum(x => x.TotalHoldDays) / examGroupings.Sum(x => x.Count),
                    2),
                AverageDaysWithoutHold =
                    Math.Round(examGroupings.Sum(x => x.TotalDaysWithoutHold) / examGroupings.Sum(x => x.Count), 2)
            };
            examGroupings.Add(totals);
            return KendoGridResult(examGroupings.AsQueryable(), request);
        }

        public ActionResult ExamsBySubType([DataSourceRequest] DataSourceRequest request, string startString,
            string endString)
        {
            var start = DateTime.Today.AddMonths(-1);
            var end = DateTime.Today;
            if (!string.IsNullOrEmpty(startString)) start = DateTime.Parse(startString);
            if (!string.IsNullOrEmpty(endString)) end = DateTime.Parse(endString);
            var examTypes = DB11.exam.Where(x => x.exam_status_id == (int) ExamStatus.Done &&
                                                 x.exam_category_id.HasValue && x.date_closed <= end &&
                                                 x.date_closed >= start)
                .GroupBy(x => new {Cat = x.exam_category.exam_category1, Kitten = x.exam_category.exam_subcategory})
                .ToList()
                .Select(x => new ExamsBySubTypeViewModel(x.Key.Cat, x.Key.Kitten, x.Count(), start, end, DB11));
            return KendoGridResult(examTypes.AsQueryable(), request);
        }

        public ActionResult EvidenceCounts([DataSourceRequest] DataSourceRequest request, string startString,
            string endString, bool open)
        {
            var start = (string.IsNullOrEmpty(startString))
                ? DateTime.Today.AddMonths(-1)
                : DateTime.Parse(startString);
            var end = (string.IsNullOrEmpty(endString)) ? DateTime.Today : DateTime.Parse(endString);

            var exams = open
                ? DB11.exam.OpenExams()
                : DB11.exam.Where(x =>
                    x.exam_status_id == (int) ExamStatus.Done && x.date_closed <= end && x.date_closed >= start);
            var results = exams
                .SelectMany(x => x.exam_evidence).Select(z => z.evidence).Where(x => x.evidence_parent_id == null).Distinct()
                .GroupBy(x => x.evidence_type.evidence_type1).ToList()
                .Select(x => new EvidenceStatsViewModel
                {
                    EvidenceType = x.Key,
                    Count = x.Count()
                });

            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult EvidenceSizeByGroup()
        {
            var fy = DateTime.Now.MostRecentFiscalYear();
            var start = fy[0];
            var end = fy[1];
            var exams = DB11.exam.Where(z => z.date_closed >= start && z.date_closed <= end);
            var total = exams.SelectMany(z => z.exam_evidence).Sum(z => z.evidence.storage_size)
                .RoundIfNotNull();
            var grouped = exams.GroupBy(z => z.group.name).Select(z => new
            {
                Group = z.Key,
                Sum = z.SelectMany(x => x.exam_evidence).Select(x => x.evidence).Sum(x => x.storage_size)
            }).ToList();
            var rounded = grouped.Select(z => new
            {
                z.Group, Sum = z.Sum.RoundIfNotNull()
            });
            return Json(new {total, grouped = rounded}, JsonRequestBehavior.AllowGet);
        }

        public ActionResult IntrusionsBreakdown([DataSourceRequest] DataSourceRequest request)
        {
            var intrusions = DB11.exam_intrusion.OpenExams()
                .GroupBy(x => new
                {
                    Agency = x.exam.@case.agency_org.agency.name,
                    AgencyUnit = x.exam.@case.agency_org.agency_unit.name,
                    Category = x.exam.exam_category.exam_category1
                })
                .ToList().Select(x =>
                    new IntrusionsBreakDownViewModel(x.Key.Agency, x.Key.AgencyUnit, x.Key.Category, x.Count()));
            return KendoGridResult(intrusions.AsQueryable(), request);
        }

        public ActionResult TotalOpenExams([DataSourceRequest] DataSourceRequest request)
        {
            var examTypes = DB11.exam.OpenExams()
                .GroupBy(x => x.group.name).ToList()
                .Select(x => new TotalOpenExamsViewModel(x.Key, x.Count())).ToList();
            var totals = new TotalOpenExamsViewModel()
            {
                Group = "Total",
                Count = examTypes.Sum(x => x.Count)
            };
            examTypes.Add(totals);
            return KendoGridResult(examTypes.AsQueryable(), request);
        }

        public ActionResult ExamsNearingSuspenseDate()
        {
            return PartialView();
        }

        public ActionResult ExamsNearSuspenseDateKendoGrid([DataSourceRequest] DataSourceRequest request)
        {
            var nowPlusTwoWeeks = DateTime.Now.Date.AddDays(14);
            var exams = DB11.exam.OpenExams().Where(z =>
                    z.exam_type_id == (int) ExamType.MajorCrimesCounterIntelligence && z.lead_examiner_id.HasValue &&
                    z.date_closed == null && z.exam_standard.suspense_lab < nowPlusTwoWeeks)
                .OrderBy(z => z.exam_standard.suspense_lab).ToList()
                .Select(z => new ExamsNearSuspenseDateViewModel(z));
            return KendoGridResult(exams.AsQueryable(), request);
        }

        public ActionResult CategoryAgencyMonthScatterPlot()
        {
            var categories = new List<int>
            {
                18, 11, 9, 6
            }; //child pornography, sex offense and inappropriate, narcotics and substance abuse, death and suicide
            var agencies = new List<int?> {7, 9, 5}; // AFOSI, NCIS, ARMY CID
            var mostRecentFiscalYearStart = DateTime.Now.MostRecentFiscalYearBeginning();
            var catBag = new List<ScatterPlotCategories>();
            foreach (var cat in categories)
            {
                var bag = new List<ScatterPlotViewModel>();
                DB11.exam.ClosedExams()
                    .Where(z => z.date_closed > mostRecentFiscalYearStart && z.exam_category_id == cat &&
                                agencies.Contains(z.@case.agency_org.agency_id))
                    .GroupBy(z => z.@case.agency_org.agency.name).OrderBy(z => z.Key).ForEach(group =>
                    {
                        group.ForEach(exam =>
                        {
                            bag.Add(new ScatterPlotViewModel
                            {
                                Agency = exam.@case.agency_org.agency.name, Date = (DateTime) exam.date_closed,
                                Count = group.Count()
                            });
                        });
                    });
                catBag.Add(new ScatterPlotCategories()
                    {Category = DB11.exam_category.Find(cat).exam_subcategory, Points = bag});
            }

            return PartialView(catBag);
        }
    }
}