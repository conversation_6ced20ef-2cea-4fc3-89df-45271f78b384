﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Web.Mvc;
using System.Net;
using System.IO;
using CIMSWeb.Models;
using System.Linq;
using CIMSWeb.Attributes;
using CIMSWeb.Workflow;
using CommonServiceLocator;
using Kendo.Mvc;
using Kendo.Mvc.UI;
using Newtonsoft.Json;
using SolrNet;
using SolrNet.Commands.Parameters;
using SolrNet.Impl;

namespace CIMSWeb.Controllers
{
    public class SearchController : CIMSController
    {
        private const int PageSize = 12;
        
        private static readonly HighlightingParameters HighlightParams = new HighlightingParameters
        {
            Fields = new[] {"content"},
            BeforeTerm = "<b><i>",
            AfterTerm = "</i></b>",
            Fragsize = 150,
            MaxAnalyzedChars = int.MaxValue
        };

        private static readonly HighlightingParameters HighlightParamsExamData = new HighlightingParameters
        {
            Fields = new[] {"*"},
            BeforeTerm = "<b><i>",
            AfterTerm = "</i></b>",
            Fragsize = 50
        };

        private static readonly List<DocumentTypes> IndexedDocTypes = new List<DocumentTypes>
        {
            DocumentTypes.Form1ForensicServiceRequest,
            DocumentTypes.Form4LaboratoryReport,
            DocumentTypes.Form9LaboratoryNotes,
            DocumentTypes.Form9TechnicianNotes
        };

        private static readonly List<string> ExamTypes = new List<string>
            {"standard", "intrusion", "litigation support"};

        private static readonly List<string> Customers = new List<string>
            {"DC3-AG", "DCISE", "Other"};

        public ActionResult Index(string searchTerm = "")
        {
            var isIntrusions = DB11.account_permission.Where(z => z.account_id == CurrentUserModel.AccountID)
                .Any(z => z.group_id == (int) Group.Intrusions);
            if (isIntrusions)
                return new HttpStatusCodeResult(HttpStatusCode.Forbidden,
                    "Intrusions users cannot search standard docs");

            InitializeExamMetaDataValues();
            ViewBag.CFLDocStatus = GetSolrCoreStatus(ConfigurationManager.AppSettings["SolrCFLDocs"]);
            ViewBag.ExamDataStatus = GetSolrCoreStatus(ConfigurationManager.AppSettings["SolrExamData"]);
            ViewBag.ValidationReportStatus = GetSolrCoreStatus(ConfigurationManager.AppSettings["SolrValidationReports"]);
            ViewBag.ArtRepoStatus = GetSolrCoreStatus(ConfigurationManager.AppSettings["SolrForensicArtifacts"]);
            ViewBag.ExamHelpStatus = GetSolrCoreStatus(ConfigurationManager.AppSettings["SolrExamTickets"]);

            var solrDCFL = ServiceLocator.Current.GetInstance<ISolrOperations<SolrCFLDocument>>();
            var examiners = SolrFacetQuery(solrDCFL, new[] {"examiner_name"}, 4);
            var exams = SolrFacetQuery(solrDCFL, new[] {"exam_number"});
            ViewBag.Examiners = examiners.FacetFields["examiner_name"].Select(z => z.Key.TitleCase());
            ViewBag.ExamNumbers = exams.FacetFields["exam_number"].Select(z => z.Key);
            ViewBag.DocumentTypes = IndexedDocTypes
                .Select(z => new SelectListItem {Text = z.DisplayName(), Value = ((int) z).ToString()});
            
            ViewBag.Tags = SolrForensicArtifactDocument.FacetTags();
            ViewBag.Uploaders = SolrForensicArtifactDocument.FacetUploaders();
            ViewBag.Types = DB11.knowledge_repo_types.OrderBy(z => z.article_type).ToList()
                .Select(z => z.article_type);

            ViewBag.TicketTypes = DB11.eh_ticket_type.OrderBy(x => x.ticket_type).ToList().Select(x => x.ticket_type);
            ViewBag.Requesters = DB11.eh_ticket.GroupBy(x => x.reporter_id).SelectMany(x => x.Select(z => z.account.name)).Distinct().ToList();
            ViewBag.Assignees = DB11.eh_ticket.GroupBy(x => x.assignee_id).SelectMany(x => x.Select(z => z.account1.name)).Distinct().ToList();

            if (!string.IsNullOrEmpty(searchTerm))
            {
                ViewBag.QuickSearch = searchTerm;
            }

            return PartialView();
        }

        public ActionResult Search(SearchCriteriaViewModel searchCriteria)
        {
            var solr = ServiceLocator.Current.GetInstance<ISolrOperations<SolrCFLDocument>>();
            var solrQuery = BuildSolrQuery(searchCriteria);
            var offSet = searchCriteria.Page < 2 ? 0 : (searchCriteria.Page - 1) * PageSize;
            var queryOptions = new QueryOptions
            {
                Fields = {"id", "file_name"},
                Highlight = HighlightParams,
                StartOrCursor = new StartOrCursor.Start(offSet),
                Rows = PageSize,
                Facet = new FacetParameters()
                {
                    Queries = new[] {new SolrFacetFieldQuery("exam_number") {MinCount = 1, Limit = -1}}
                }
            };
            if (string.IsNullOrEmpty(searchCriteria.FreeText))
            {
                queryOptions.OrderBy = new List<SortOrder>() {new SortOrder("exam_number", Order.DESC)};
            }

            var searchResults = solr.Query(solrQuery, queryOptions);

            foreach (var solrDoc in searchResults)
            {
                solrDoc.NearByText = searchResults.Highlights[solrDoc.FilePath].Values.Count > 0
                    ? searchResults.Highlights[solrDoc.FilePath].Values.First().First().HtmlEncode()
                    : "N\\A";
            }

            ViewBag.PageSize = PageSize;
            return PartialView("Results", searchResults);
        }

        public ActionResult IASearchKendo([DataSourceRequest] DataSourceRequest request, 
            SearchCriteriaViewModel searchCriteria, int source)
        {
            searchCriteria.PageSize = request.PageSize;
            searchCriteria.Page = request.Page;
            if (request.Sorts.Any())
            {
                searchCriteria.SortField = request.Sorts.First().Member;
                searchCriteria.SortDir =
                    request.Sorts.First().SortDirection == ListSortDirection.Ascending ? "asc" : "desc";
            }
            IASearchJsonResult results;
            switch (source)
            {
                case 2:
                    results = PostToIASearchService(searchCriteria, "/Search/SearchIA");
                    break;
                case 4:
                    results = PostToIASearchService(searchCriteria, "/Search/SearchIAOpenSource");
                    break;
                case 5:
                    results = PostToIASearchService(searchCriteria, "/Search/SearchGitlab");
                    break;
                default:
                    throw new Exception("Unknown source");
            }
            return Json(new {total = results.NumFound, data = results.Results});
        }

        public ActionResult IntrusionsExamDocSearch(SearchCriteriaViewModel searchCriteria)
        {
            var results = PostToIASearchService(searchCriteria, "/Search/SearchIA");
            ViewBag.PageSize = PageSize;
            ViewBag.Source = 2;
            return PartialView("IAExamSearchResults", results);
        }

        public ActionResult IntrusionsOpenSourceSearch(SearchCriteriaViewModel searchCriteria)
        {
            var results = PostToIASearchService(searchCriteria, "/Search/SearchIAOpenSource");
            ViewBag.PageSize = PageSize;
            ViewBag.Source = 4;
            return PartialView("IAExamSearchResults", results);
        }

        public ActionResult IntrusionsGitlabSearch(SearchCriteriaViewModel searchCriteria)
        {
            var results = PostToIASearchService(searchCriteria, "/Search/SearchGitlab");
            ViewBag.PageSize = PageSize;
            ViewBag.Source = 5;
            ViewBag.Regex = searchCriteria.RegexSearch;
            return PartialView("IAExamSearchResults", results);
        }

        [CIMSAuthorize(SecureFunction.StandardSearch)]
        public ActionResult Download(string path)
        {
            var fileName = new FileInfo(path).Name;
            return File(System.IO.File.ReadAllBytes(path), System.Web.MimeMapping.GetMimeMapping(fileName), fileName);
        }
        
        [CIMSAuthorize(SecureFunction.StandardSearch)]
        public ActionResult DownloadInline(string path)
        {
            var fileName = new FileInfo(path).Name;
            Response.ContentType = System.Web.MimeMapping.GetMimeMapping(fileName);
            Response.AddHeader("Content-disposition", $"inline; filename=\"{fileName}\"");
            Response.BinaryWrite(System.IO.File.ReadAllBytes(path));
            return new EmptyResult();
        }

        public ActionResult ExamDataSearchResultsKendo([DataSourceRequest] DataSourceRequest request,
            SearchCriteriaViewModel searchCriteria)
        {
            var solr = ServiceLocator.Current.GetInstance<ISolrOperations<SolrExamDocument>>();
            var solrQuery = BuildSolrQueryExamData(searchCriteria);
            var offSet = request.Page < 2 ? 0 : (request.Page - 1) * request.PageSize;
            var queryOptions = new QueryOptions
            {
                Fields =
                {
                    "id", "exam_id", "case_agent_string", "category", "subcategory", "evidence_with_details",
                    "exam_closed_date", "examiners", "technicians", "evidence_serial_number", "mail_tracking_number"
                },
                Highlight = HighlightParamsExamData,
                StartOrCursor = new StartOrCursor.Start(offSet),
                Rows = request.PageSize
            };
            if (request.Sorts.Any())
            {
                DefineCustomSort(request.Sorts[0], queryOptions);
            }
            else if (string.IsNullOrEmpty(searchCriteria.FreeText))
            {
                queryOptions.OrderBy = new List<SortOrder>() {new SortOrder("id", Order.DESC)};
            }

            var searchResults = solr.Query(solrQuery, queryOptions);

            foreach (var solrDoc in searchResults)
            {
                var hl = searchResults.Highlights[solrDoc.ExamNumber];
                if (hl.ContainsKey("exam_type")) hl.Remove("exam_type");
                solrDoc.NearByText = hl.Values.Count > 0
                    ? hl.Keys.First() + ": " + hl.Values.First().First()
                    : "N\\A";

            }

            return Json(new {total = searchResults.NumFound, data = searchResults});
        }

        public ActionResult ExamDataSearchResults(SearchCriteriaViewModel searchCriteria)
        {
            return PartialView();
        }

        public ActionResult IntrusionsResultsKendo(string source, bool addHistory)
        {
            ViewBag.Source = source;
            ViewBag.AddHistory = addHistory;
            return PartialView();
        }

        public ActionResult IntrusionsResultsTabStrip()
        {
            return PartialView();
        }

        public ActionResult IntrusionsSearchConfig(IntrusionsSearchConfigModel model)
        {
            return PartialView(model);
        }

        public ActionResult ValidationSearch(SearchCriteriaViewModel searchCriteria)
        {
            var solr = ServiceLocator.Current.GetInstance<ISolrOperations<SolrValidationReport>>();
            var solrQuery = string.IsNullOrEmpty(searchCriteria.FreeText)
                ? SolrQuery.All
                : new SolrQuery(searchCriteria.FreeText.EscapeSolrSpecialChars());
            var offSet = searchCriteria.Page < 2 ? 0 : (searchCriteria.Page - 1) * PageSize;
            var queryOptions = new QueryOptions
            {
                Fields = {"id", "file_name"},
                Highlight = HighlightParams,
                StartOrCursor = new StartOrCursor.Start(offSet),
                Rows = PageSize
            };

            var searchResults = solr.Query(solrQuery, queryOptions);

            foreach (var solrDoc in searchResults)
            {
                solrDoc.NearByText = searchResults.Highlights[solrDoc.FilePath].Values.Count > 0
                    ? searchResults.Highlights[solrDoc.FilePath].Values.First().First().HtmlEncode()
                    : "N\\A";
            }

            ViewBag.PageSize = PageSize;
            return PartialView("ValidationResults", searchResults);
        }

        public ActionResult QuickSearch(string searchTerm)
        {
            var exam = DB11.exam.FirstOrDefault(z => z.exam_number == searchTerm);
            string href;
            if (exam != null)
            {
                href = $"#/Exam/Details?id={exam.exam_id}";
            }
            else
            {
                href = CurrentUserModel.IsIntrusions
                    ? $"#/Search/IntrusionsSearch?searchTerm={System.Web.HttpUtility.UrlEncode(searchTerm)}"
                    : $"#/Search/Index?searchTerm={System.Web.HttpUtility.UrlEncode(searchTerm)}";
            }

            return Json(href, JsonRequestBehavior.AllowGet);
        }

        public ActionResult IntrusionsSearch(string searchTerm = "")
        {
            var isIntrusions = DB11.account_permission.Where(z => z.account_id == CurrentUserModel.AccountID)
                .Any(z => z.group_id == (int) Group.Intrusions);
            if (!isIntrusions)
                return new HttpStatusCodeResult(HttpStatusCode.Forbidden,
                    "You must be an intrusions user to search intrusions documents.");

            InitializeExamMetaDataValues();
            ViewBag.IntruionsExamTypes = DB11.exam_category.Where(z => z.exam_type_id == 1)
                .Select(z => z.exam_subcategory).ToList().Distinct().OrderBy(z => z).Select(z => new SelectListItem
                    {Text = z.TitleCase(), Value = z.TitleCase()});
            ViewBag.Customers = Customers.Select(z => new SelectListItem {Text = z, Value = z});

            if (!string.IsNullOrEmpty(searchTerm))
            {
                ViewBag.QuickSearch = searchTerm;
            }

            return PartialView();
        }

        public ActionResult ValidationReportSearch()
        {
            ViewBag.ValidationReports = ExtensionMethods.BuildTreeViewItemModels(ConfigurationManager.AppSettings["ValidationReports"]);
            return PartialView();
        }

        private void InitializeExamMetaDataValues()
        {
            var solr = ServiceLocator.Current.GetInstance<ISolrOperations<SolrExamDocument>>();
            var fields = new List<string>
                {"case_agent", "case_agent_address", "subjects", "evidence", "evidence_serial_number", "mail_tracking_number"};
            var facets = SolrFacetQuery(solr, fields);
            ViewBag.CaseAgents = facets.FacetFields["case_agent"].Select(z => z.Key.TitleCase());
            ViewBag.CaseAgentAddresses = facets.FacetFields["case_agent_address"].Select(z => z.Key);
            ViewBag.Subjects = facets.FacetFields["subjects"].Select(z => z.Key.TitleCase());
            ViewBag.Evidence = facets.FacetFields["evidence"].Select(z => z.Key.TitleCase());
            ViewBag.serialNumbers = facets.FacetFields["evidence_serial_number"].Select(z => z.Key);
            ViewBag.trackingNumbers = facets.FacetFields["mail_tracking_number"].Select(z => z.Key);
            ViewBag.ExamTypes = ExamTypes
                .Select(z => new SelectListItem {Text = z.TitleCase(), Value = z});
        }
        
        private static CoreResult GetSolrCoreStatus(string core)
        {
            var headerParser = ServiceLocator.Current.GetInstance<ISolrHeaderResponseParser>();
            var statusParser = ServiceLocator.Current.GetInstance<ISolrStatusResponseParser>();
            ISolrCoreAdmin solrCoreAdmin =
                new SolrCoreAdmin(ExtensionMethods.CreateSolrConnection(), headerParser, statusParser);
            return solrCoreAdmin.Status(core);
        }

        private static SolrQueryResults<T> SolrFacetQuery<T>(ISolrBasicReadOnlyOperations<T> solr, IEnumerable<string> fields,
            int minCount = 0, int limit = -1) where T : class
        {
            var facetParams = new FacetParameters();
            foreach (var field in fields)
            {
                facetParams.Queries.Add(new SolrFacetFieldQuery(field) {MinCount = minCount, Limit = limit});
            }
            var queryOptions = new QueryOptions
            {
                Rows = 0,
                Facet = facetParams
            };
            return solr.Query(SolrQuery.All, queryOptions);
        }

        private SolrMultipleCriteriaQuery BuildSolrQuery(SearchCriteriaViewModel searchCriteria)
        {
            var queries = new List<ISolrQuery>();
            bool dateRangeSet = false;
            if (!string.IsNullOrEmpty(searchCriteria.FreeText))
            {
                BuildFreeTextQuery(queries, searchCriteria, "content");
            }

            if (searchCriteria.StartDate.HasValue)
            {
                dateRangeSet = true;
                var endDate = searchCriteria.EndDate ?? DateTime.Now;
                queries.Add(new SolrQueryByRange<DateTime>("exam_closed_date", searchCriteria.StartDate.Value, endDate,
                    true));
            }

            if (searchCriteria.EndDate.HasValue && !dateRangeSet)
            {
                queries.Add(new SolrQueryByRange<DateTime>("exam_closed_date", DateTime.Now.AddYears(-100),
                    searchCriteria.EndDate.Value, true));
            }

            if (!string.IsNullOrEmpty(searchCriteria.ExamNumber))
            {
                queries.Add(new SolrQueryByField("exam_number", searchCriteria.ExamNumber));
            }

            if (!string.IsNullOrEmpty(searchCriteria.Examiner))
            {
                queries.Add(new SolrQueryByField("examiner_name", searchCriteria.Examiner.ToUpper()));
            }

            if (searchCriteria.DocumentType > 0)
            {
                var docType = (DocumentTypes) searchCriteria.DocumentType;
                queries.Add(new SolrQueryByField("document_type", docType.DisplayName()));
            }

            if (queries.Count == 0)
            {
                queries.Add(SolrQuery.All);
            }

            return new SolrMultipleCriteriaQuery(queries, "AND");
        }

        private SolrMultipleCriteriaQuery BuildSolrQueryExamData(SearchCriteriaViewModel searchCriteria)
        {
            var queries = new List<ISolrQuery>();
            bool dateRangeSet = false;
            if (!string.IsNullOrEmpty(searchCriteria.FreeText))
            {
                BuildFreeTextQuery(queries, searchCriteria, "_text_");
            }

            if (searchCriteria.StartDate.HasValue)
            {
                dateRangeSet = true;
                var endDate = searchCriteria.EndDate ?? DateTime.Now;
                queries.Add(new SolrQueryByRange<DateTime>("exam_closed_date", searchCriteria.StartDate.Value, endDate,
                    true));
            }

            if (searchCriteria.EndDate.HasValue && !dateRangeSet)
            {
                queries.Add(new SolrQueryByRange<DateTime>("exam_closed_date", DateTime.Now.AddYears(-100),
                    searchCriteria.EndDate.Value, true));
            }

            if (!string.IsNullOrEmpty(searchCriteria.CaseAgent))
            {
                queries.Add(new SolrQueryByField("case_agent", searchCriteria.CaseAgent));
            }
            
            if (!string.IsNullOrEmpty(searchCriteria.CaseAgentAddress))
            {
                queries.Add(new SolrQueryByField("case_agent_address", searchCriteria.CaseAgentAddress));
            }

            if (!string.IsNullOrEmpty(searchCriteria.Subject))
            {
                queries.Add(new SolrQueryByField("subjects", searchCriteria.Subject));
            }

            if (!string.IsNullOrEmpty(searchCriteria.Evidence))
            {
                queries.Add(new SolrQueryByField("evidence", searchCriteria.Evidence));
            }

            if (!string.IsNullOrEmpty(searchCriteria.ExamType))
            {
                queries.Add(new SolrQueryByField("exam_type", searchCriteria.ExamType));
            }

            if (!string.IsNullOrEmpty(searchCriteria.SerialNumber))
            {
                queries.Add(new SolrQueryByField("evidence_serial_number", searchCriteria.SerialNumber));
            }

            if (!string.IsNullOrEmpty(searchCriteria.MailTrackingNumber))
            {
                queries.Add(new SolrQueryByField("mail_tracking_number", searchCriteria.MailTrackingNumber));
            }

            if (queries.Count == 0)
            {
                queries.Add(SolrQuery.All);
            }

            return new SolrMultipleCriteriaQuery(queries, "AND");
        }

        private void DefineCustomSort(SortDescriptor sort, QueryOptions query)
        {
            var order = sort.SortDirection == ListSortDirection.Ascending ? SolrNet.Order.ASC : SolrNet.Order.DESC;
            if (sort.Member == "ExamNumber") query.OrderBy = new List<SortOrder> {new SortOrder("id", order)};
            else if (sort.Member == "DateClosed")
                query.OrderBy = new List<SortOrder> {new SortOrder("exam_closed_date", order)};
        }

        private void BuildFreeTextQuery(List<ISolrQuery> queries, SearchCriteriaViewModel searchCriteria,
            string fieldName)
        {
            if (searchCriteria.RegexSearch)
            {
                queries.Add(new SolrQueryByFieldRegex(fieldName, searchCriteria.FreeText));
            }
            else
            {
                var split = searchCriteria.FreeText.Split(',');
                foreach (var token in split)
                {
                    var text = token.Trim();
                    text = text.EscapeSolrSpecialChars();
                    if (text.StartsWith("!"))
                    {
                        queries.Add(new SolrNotQuery(new SolrQueryByField(fieldName, text.Substring(1)) {Quoted = false}));
                    }
                    else
                    {
                        queries.Add(new SolrQueryByField(fieldName, text) {Quoted = false});
                    }
                }
            }
        }

        private IASearchJsonResult PostToIASearchService(SearchCriteriaViewModel searchCriteria, string endPoint)
        {
            searchCriteria.FreeText = searchCriteria.FreeText.EscapeSolrSpecialChars();
            var url = ConfigurationManager.AppSettings["IAService"] + endPoint;
            var httpWebRequest = (HttpWebRequest) WebRequest.Create(url);
            httpWebRequest.ContentType = "application/json";
            httpWebRequest.Method = "POST";
            httpWebRequest.PreAuthenticate = true;
            httpWebRequest.UseDefaultCredentials = true;

            using (var streamWriter = new StreamWriter(httpWebRequest.GetRequestStream()))
            {
                streamWriter.Write(JsonConvert.SerializeObject(searchCriteria));
            }

            using (var response = (HttpWebResponse) httpWebRequest.GetResponse())
            using (var stream = response.GetResponseStream())
            using (var streamReader = new StreamReader(stream))
            {
                var ret = streamReader.ReadToEnd();
                var results = JsonConvert.DeserializeObject<IASearchJsonResult>(ret);
                foreach (var solrCflDocument in results.Results)
                {
                    solrCflDocument.FilePath = solrCflDocument.FilePath?.Replace('\\', '/');
                    solrCflDocument.NearByText = string.IsNullOrEmpty(solrCflDocument.NearByText)
                        ? "N\\A"
                        : solrCflDocument.NearByText.HtmlEncode();
                    if (string.IsNullOrEmpty(solrCflDocument.Examiner))
                    {
                        solrCflDocument.Examiner = "Unknown";
                    }
                }
                return results;
            }
        }
    }
}