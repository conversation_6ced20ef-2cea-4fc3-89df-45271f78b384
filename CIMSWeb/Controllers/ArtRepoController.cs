using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Printing;
using System.IO;
using System.Linq;
using System.Speech.Recognition;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Hosting;
using System.Web.Mvc;
using Aspose.Words;
using CIMSData.Database;
using CIMSWeb.Models;
using CIMSWeb.Workflow;
using CommonServiceLocator;
using FFmpeg.NET;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Markdig;
using Newtonsoft.Json;
using SolrNet;
using SolrNet.Commands.Parameters;
using WebGrease.Css.Extensions;
using YamlDotNet.Core;
using YamlDotNet.Core.Events;
using YamlDotNet.RepresentationModel;
using YamlDotNet.Serialization;
using Font = System.Drawing.Font;

namespace CIMSWeb.Controllers
{
    public class ArtRepoController : CIMSController
    {
        // GET
        public ActionResult Index()
        {
            ViewBag.IsAdmin = CurrentUserModel.Permissions.Any(z =>
                z.Role == Role.Admin && (z.Group == Group.Counterintelligence || z.Group == Group.MajorCrimes ||
                                         z.Group == Group.ImagingExtraction));
            return PartialView();
        }

        public ActionResult Overview()
        {
            return PartialView(new ArtRepoModel(DB11));
        }

        public ActionResult QuickSearch(ArtRepoQuickSearch model)
        {
            return string.IsNullOrEmpty(model.EvidenceId)
                ? PartialView(SolrForensicArtifactDocument.QuickSearch(model))
                : PartialView("QuickSearchSimpleConfig", SolrForensicArtifactDocument.QuickSearch(model));
        }

        public ActionResult Search()
        {
            ViewBag.Tags = SolrForensicArtifactDocument.FacetTags();
            ViewBag.Uploaders = SolrForensicArtifactDocument.FacetUploaders();
            ViewBag.Types = DB11.knowledge_repo_types.OrderBy(z => z.article_type).ToList()
                .Select(z => z.article_type);
            return PartialView(new ArtRepoSearchModel());
        }

        public ActionResult ViewArtifact(string id, int version = 0)
        {
            ViewBag.IsFavorite =
                DB11.knowledge_repo_favorites.Any(z =>
                    z.account_id == CurrentUserModel.ActualAccountID && z.article_id == id);
            if (version > 0) return RetrieveOldVersion(id, version);
            var solr = ServiceLocator.Current.GetInstance<ISolrOperations<SolrForensicArtifactDocument>>();
            var queryOptions = new QueryOptions
            {
                MoreLikeThis = new MoreLikeThisParameters(new[] {"tags", "description"})
                    {Count = 10, MinDocFreq = 1, MinTermFreq = 1, Boost = true}
            };
            var queryResults = solr.Query(new SolrQueryByField("id", id), queryOptions);
            var model = queryResults.First();
            model.Attachments = DB11.knowledge_repo_attachments
                .Where(z => model.AttachmentIds.Contains(z.id.ToString())).ToList()
                .Select(z => new FileMetadata(z));
            model.RevisionHistory =
                DB11.knowledge_repo_history.Where(z => z.solr_document_id == id).ToList();
            model.FixDynamicFieldsFromSolr();
            model.AddBorderToTables();
            model.RelatedArtifacts = queryResults.SimilarResults[id];
            ViewBag.Version = 0;
            return PartialView(model);
        }

        private ActionResult RetrieveOldVersion(string id, int version)
        {
            var solr = ServiceLocator.Current.GetInstance<ISolrOperations<SolrForensicArtifactDocumentBackups>>();
            var results = solr.Query(new SolrQueryByField("id", $"{id}--{version.ToString("D3")}--"));
            var model = results.First();
            model.Id = id;
            model.Attachments = DB11.knowledge_repo_attachments
                .Where(z => model.AttachmentIds.Contains(z.id.ToString())).ToList()
                .Select(z => new FileMetadata(z));
            model.RevisionHistory =
                DB11.knowledge_repo_history.Where(z => z.solr_document_id == id).ToList();
            model.FixDynamicFieldsFromSolr();
            model.AddBorderToTables();
            ViewBag.Version = version;
            return PartialView("ViewArtifact", model);
        }

        public ActionResult EditArtifact(string id)
        {
            var solr = ServiceLocator.Current.GetInstance<ISolrOperations<SolrForensicArtifactDocument>>();
            var model = solr.Query(new SolrQueryByField("id", id)).First();
            model.ExistingFiles = DB11.knowledge_repo_attachments.Where(z =>
                    model.AttachmentIds.Contains(z.id.ToString())).ToList()
                .Select(z => new FileMetadata(z)).ToList();
            model.FixDynamicFieldsFromSolr();
            model.DynamicFormFields = model.DynamicSolrFields?.Select(z => new ArtRepoFormKeyValue()
                {Id = Guid.NewGuid().ToString(), Key = z.Key, Value = z.Value}).ToList();
            var curTags = SolrForensicArtifactDocument.FacetTags();
            var startingTags = DB11.knowledge_repo_default_tags.ToList().Select(z => new {Text = z.tag_name});
            ViewBag.Tags = startingTags.Union(curTags);
            ViewBag.Evidence = model.GetEvidenceLabels();
            ViewBag.Artifacts = SolrForensicArtifactDocument.GetAllTitlesAndIds(except: id);
            return PartialView("Upload", model);
        }

        public ActionResult Download(int id, string mimeType)
        {
            var file = DB11.knowledge_repo_attachments.Find(id);
            return File(System.IO.File.ReadAllBytes(file.saved_path), mimeType, file.file_name);
        }

        public ActionResult Upload()
        {
            var curTags = SolrForensicArtifactDocument.FacetTags();
            var startingTags = DB11.knowledge_repo_default_tags.ToList().Select(z => new {Text = z.tag_name});
            ViewBag.Tags = startingTags.Union(curTags);
            var model = new SolrForensicArtifactDocument()
                {DynamicFormFields = new List<ArtRepoFormKeyValue>(), Tags = new List<string>()};
            ViewBag.Evidence = model.GetEvidenceLabels();
            ViewBag.Artifacts = SolrForensicArtifactDocument.GetAllTitlesAndIds();
            return PartialView("Upload", model);
        }

        public ActionResult IsDistinctTitle(string title, string id = null)
        {
            return Json(SolrForensicArtifactDocument.IsDistinctTitle(title, id), JsonRequestBehavior.AllowGet);
        }

        public ActionResult SaveArtifact(SolrForensicArtifactDocument model)
        {
            var files = Enumerable.Empty<HttpPostedFileBase>();
            var bag = new List<HttpPostedFileBase>();
            for (int i = 0; i < Request.Files.Count; i++)
            {
                bag.Add(Request.Files[i]);
            }

            if (bag.Any()) files = bag;
            if (string.IsNullOrEmpty(model.Id))
            {
                model.CreateArtifact(CurrentUserModel, files, DB11);
            }
            else
            {
                model.UpdateArtifact(CurrentUserModel, files, DB11);
            }

            return Content(model.Id);
        }

        public ActionResult MockAsync()
        {
            return Content("");
        }

        public ActionResult Favorite(string id)
        {
            var row = DB11.knowledge_repo_favorites.FirstOrDefault(z =>
                z.account_id == CurrentUserModel.ActualAccountID && z.article_id == id);
            if (row == null)
            {
                DB11.knowledge_repo_favorites.Add(new knowledge_repo_favorites()
                    {account_id = CurrentUserModel.ActualAccountID, article_id = id});
            }
            else
            {
                DB11.knowledge_repo_favorites.Remove(row);
            }

            DB11.SaveChanges();
            return new EmptyResult();
        }

        public ActionResult Delete(string id)
        {
            if (!CurrentUserModel.Permissions.Any(z =>
                z.Role == Role.Admin && (z.Group == Group.Counterintelligence || z.Group == Group.MajorCrimes ||
                                         z.Group == Group.ImagingExtraction)))
            {
                return new HttpStatusCodeResult(403);
            }
            try
            {
                LogFromController($"Deleting article with id of {id}");
                var solr = ServiceLocator.Current.GetInstance<ISolrOperations<SolrForensicArtifactDocument>>();
                solr.Delete(id);
                solr.Commit();
                var solrBackups = ServiceLocator.Current.GetInstance<ISolrOperations<SolrForensicArtifactDocumentBackups>>();
                solrBackups.Delete(new SolrQueryByField("id", $"{id}*") { Quoted = false});
                solrBackups.Commit();
                DB11.knowledge_repo_attachments.RemoveRange(DB11.knowledge_repo_attachments.Where(z => z.solr_document_id == id));
                DB11.knowledge_repo_history.RemoveRange(DB11.knowledge_repo_history.Where(z => z.solr_document_id == id));
                DB11.knowledge_repo_favorites.RemoveRange(DB11.knowledge_repo_favorites.Where(z => z.article_id == id));
                DB11.SaveChanges();
                return new EmptyResult();
            }
            catch (Exception e)
            {
                LogFromController($"Exception thrown while deleting article with id of {id} ");
                LogFromController($"{e.Message}");
                LogFromController($"{e.StackTrace}");
                throw;
            }
        }
        
        public ActionResult ArticleTypesGridData([DataSourceRequest] DataSourceRequest request)
        {
            var solr = ServiceLocator.Current.GetInstance<ISolrOperations<SolrForensicArtifactDocument>>();
            return DB11.knowledge_repo_types.OrderBy(x => x.article_type).ToList()
                .Select(z => new ArticleTypeModel(z, solr)).ToKendoGridResult(request);
        }

        public ActionResult CreateArticleType([DataSourceRequest] DataSourceRequest request, ArticleTypeModel model)
        {
            var row = model.Create();
            DB11.knowledge_repo_types.Add(row);
            DB11.SaveChanges();
            model.Id = row.id;
            model.CanDelete = true;
            return Json(new[] {model}.ToDataSourceResult(request, ModelState));
        }

        public ActionResult UpdateArticleType([DataSourceRequest] DataSourceRequest request, ArticleTypeModel model)
        {
            var row = DB11.knowledge_repo_types.Find(model.Id);
            model.Update(row);
            DB11.Entry(row).State = EntityState.Modified;
            DB11.SaveChanges();
            return Json(new[] {model}.ToDataSourceResult(request, ModelState));
        }
        
        public ActionResult DeleteArticleType(int id)
        {
            var row = DB11.knowledge_repo_types.Find(id);
            var solr = ServiceLocator.Current.GetInstance<ISolrOperations<SolrForensicArtifactDocument>>();
            if (solr.Query(new SolrQueryByField("artifact_type", row.article_type), new QueryOptions() {Rows = 0})
                    .NumFound == 0)
            {
                DB11.knowledge_repo_types.Remove(row);
                DB11.SaveChanges();
            }
            return new EmptyResult();
        }



        public async Task InlineRender(int id)
        {
            var row = DB11.knowledge_repo_attachments.Find(id);
            if (row.mime_type == "application/vnd.openxmlformats-officedocument.presentationml.presentation" ||
                row.mime_type == "application/vnd.ms-powerpoint")
            {
                await ConvertPowerpointToPdf(row);
                return;
            }

            if (row.mime_type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
                row.mime_type == "application/msword")
            {
                ConvertWordDocToPdf(row);
                return;
            }

            if (row.file_name.ToLower().EndsWith(".md"))
            {
                ConvertMarkdownToHtml(row);
                return;
            }

            Response.ContentType = row.mime_type;
            Response.AddHeader("Content-disposition", $"inline; filename=\"{row.file_name}\"");
            var bytes = System.IO.File.ReadAllBytes(row.saved_path);
            if (!string.IsNullOrEmpty(Request.Headers.Get("Range")))
            {
                var range = Request.Headers.Get("Range");
                var start = int.Parse(range.Remove(0, 6).Split('-')[0]);
                Response.StatusCode = 206;
                Response.AddHeader("Content-Range", $"bytes {start}-{bytes.Length - 1}/{bytes.Length}");
                Response.AddHeader("Accept-Ranges", "bytes");
                bytes = bytes.Skip(start).ToArray();
            }

            Response.BinaryWrite(bytes);
        }

        private void ConvertWordDocToPdf(knowledge_repo_attachments row)
        {
            var file = row.file_name;
            var extension = file.Split('.').Last();
            var index = file.LastIndexOf(extension);
            file = file.Remove(index, extension.Length).Insert(index, "pdf");
            var doc = new Aspose.Words.Document(row.saved_path);
            doc.Save(System.Web.HttpContext.Current.Response, file,
                Aspose.Words.ContentDisposition.Inline,
                Aspose.Words.Saving.SaveOptions.CreateSaveOptions(SaveFormat.Pdf));
        }

        private async Task ConvertPowerpointToPdf(knowledge_repo_attachments row)
        {
//            var hardCmdText = @"""""C:\Program Files\LibreOffice 5\program\soffice.exe"" --convert-to pdf:writer_pdf_Export --outdir ""C:\Users\<USER>\CIMSWeb\CIMSWeb\Content\UserFiles"" ""C:\Users\<USER>\CIMSWeb\CIMSWeb\Content\UserFiles\twoslides.pptx""""";
            var share = ConfigurationManager.AppSettings["KnowledgeRepoShare"];
            var file = row.file_name;
            var extension = file.Split('.').Last();
            var index = file.LastIndexOf(extension);
            file = file.Remove(index, extension.Length).Insert(index, "pdf");
            Response.ContentType = "application/pdf";
            Response.AddHeader("Content-disposition", $"inline; filename=\"{file}\"");
            file = $"{row.solr_document_id}-{file}";
            if (System.IO.File.Exists($"{share}\\{file}"))
            {
                Response.BinaryWrite(System.IO.File.ReadAllBytes($"{share}\\{file}"));
                return;
            }

            var libreOffice = ConfigurationManager.AppSettings["LibreOffice"];
            var cmdText =
                $@"""""{libreOffice}"" --convert-to pdf:writer_pdf_Export --outdir ""{share}"" ""{row.saved_path}""""";
            LogFromController(cmdText);
            var results = await ExtensionMethods.ExecuteShellCommand("cmd", "/c " + cmdText, 30000);
            LogFromController(results.Output);
            LogFromController(results.Completed.ToString());
            LogFromController(results.ExitCode?.ToString());
            Response.BinaryWrite(System.IO.File.ReadAllBytes($"{share}\\{file}")); 
//            ProcessStartInfo procStartInfo = new ProcessStartInfo("cmd", "/c " + cmdText)
//            {
//                UseShellExecute = false, CreateNoWindow = true, Verb = "runas"
//            };
//
//
//            using (Process process = new Process())
//            {
//                process.StartInfo = procStartInfo;
//                process.Start();
//                LogFromController($"process started");
//                if (process.WaitForExit(15000))
//                {
//                    LogFromController($"process has exited {process.HasExited}");
//                    LogFromController($"process finished");
//                    Response.BinaryWrite(System.IO.File.ReadAllBytes($"{share}\\{file}"));   
//                }
//                else
//                {
//                    LogFromController($"eval to false");
//                }
//            }
        }

        private void ConvertMarkdownToHtml(knowledge_repo_attachments row)
        {
            Response.ContentType = "text/html";
            Response.AddHeader("Content-disposition", $"inline; filename=\"{row.file_name}\"");
            var file = System.IO.File.ReadAllBytes(row.saved_path);
            var pipeline = new MarkdownPipelineBuilder().UseAdvancedExtensions().Build();
            var result = Markdown.ToHtml(System.Text.Encoding.UTF8.GetString(file), pipeline);
            Response.Write(result);
        }

        public async Task<ActionResult> ExtractTextFromAudio()
        {
            var x = SpeechRecognitionEngine.InstalledRecognizers();
            foreach (var y in x)
            {
                LogFromController($"{y.Culture}");
            }
            LogFromController($"out of installed recognizers loop");
            Task<EmptyResult> task = Task.Run(async () =>
            {
                var solr = ServiceLocator.Current.GetInstance<ISolrOperations<SolrForensicArtifactDocument>>();
                var docsToParse = solr.Query(new SolrQueryByField("needs_audio_extraction", "true"));

                if (!docsToParse.Any()) return new EmptyResult();

                var share = ConfigurationManager.AppSettings["KnowledgeRepoShare"];
                var ffmpeg = ConfigurationManager.AppSettings["FFmpeg"] ?? "ffmpeg";
                foreach (var doc in docsToParse)
                {
                    var extractedSpeech = new Dictionary<string, string>();
                    foreach (var attachment in doc.AttachmentIds)
                    {
                        knowledge_repo_attachments row;
                        using (var db = new cims11Entities())
                        {
                            row = db.knowledge_repo_attachments.Find(Int32.Parse(attachment));
                        }

                        if (row.mime_type.Contains("audio") || row.mime_type.Contains("video"))
                        {
                            var newFileName = row.file_name;
                            var extension = newFileName.Split('.').Last();
                            var index = newFileName.LastIndexOf(extension);
                            newFileName = newFileName.Remove(index, extension.Length).Insert(index, "wav");

                            var input = new MediaFile(row.saved_path);
                            var output = new MediaFile($@"{share}\{row.solr_document_id}-{newFileName}");
                            var ffmpegEngine = new Engine(ffmpeg);
                            await ffmpegEngine.ConvertAsync(input, output);

                            using (var recognizer = new SpeechRecognitionEngine(new System.Globalization.CultureInfo("en-US")))
                            {
                                LogFromController($"in using clause for recognizer");
                                var sb = new StringBuilder();
                                var extracting = true;
                                recognizer.LoadGrammar(new DictationGrammar());
                                LogFromController($"loaded grammar for renognizer");
                                recognizer.SetInputToWaveFile($@"{share}\{row.solr_document_id}-{newFileName}");
                                LogFromController($"set input to file");
                                recognizer.SpeechRecognized += (sender, args) =>
                                {
                                    var text = args.Result.Text;
                                    LogFromController($"extracted text {text}");
                                    sb.Append(text);
                                };
                                recognizer.RecognizeCompleted += (sender, args) =>
                                {
                                    LogFromController($"recognize completed");
                                    extracting = false;
                                    LogFromController($"extracting set to false");
                                };
                                recognizer.RecognizeAsync(RecognizeMode.Multiple);
                                LogFromController($"recognize async started");
                                while (extracting)
                                {
                                }

                                LogFromController($"out of recognize loop and extracted text is {sb.ToString()}");
                                extractedSpeech["_t" + row.file_name + "_t"] = sb.ToString();
                            }

                            System.IO.File.Delete($@"{share}\{row.solr_document_id}-{newFileName}");
                        }
                    }

                    if (extractedSpeech.Any())
                    {
                        LogFromController($"atomic update");
                        foreach (var file in extractedSpeech)
                        {
                            solr.AtomicUpdate(doc,
                                new[]
                                {
                                    new AtomicUpdateSpec(file.Key, AtomicUpdateType.Set, file.Value)
                                });
                        }

                        solr.AtomicUpdate(doc,
                            new[]
                            {
                                new AtomicUpdateSpec("needs_audio_extraction", AtomicUpdateType.Set, "false")
                            });
                    }
                }

                LogFromController($"commit");
                solr.Commit();
                LogFromController($"returnng");
                return new EmptyResult();
            });
            return await task;
        }

        public ActionResult SearchQuery(ArtRepoSearchModel searchCriteria)
        {
            ViewBag.PageSize = 12;
            return PartialView("Results", SolrForensicArtifactDocument.Search(searchCriteria));
        }

        public ActionResult ArtifactKendoGridData([DataSourceRequest] DataSourceRequest request)
        {
            var results = SolrForensicArtifactDocument.GetAll();
            var result = new ContentResult()
            {
                Content = JsonConvert.SerializeObject(results.ToDataSourceResult(request)),
                ContentType = "application/json"
            };
            return result;
        }

        public ActionResult UserFavoritesKendoGridData([DataSourceRequest] DataSourceRequest request)
        {
            var results = SolrForensicArtifactDocument.GetUserFavorites(CurrentUserModel.ActualAccountID);
            var result = new ContentResult()
            {
                Content = JsonConvert.SerializeObject(results.ToDataSourceResult(request)),
                ContentType = "application/json"
            };
            return result;
        }

        /// <summary>
        /// Ingest directory of YAML files from https://github.com/ForensicArtifacts/artifacts/tree/master/data 
        /// </summary>
        /// <returns></returns>
        public ActionResult IngestYaml()
        {
            var solr = ServiceLocator.Current.GetInstance<ISolrOperations<SolrForensicArtifactDocument>>();
            var sqlBag = new List<knowledge_repo_history>();
            var cimsAcct = new AccountViewModel(DB11.account.First(z => z.name == "CIMS"));
            foreach (var file in Directory.EnumerateFiles(Server.MapPath(@"~/Content/artifacts-master/data")))
            {
                using (TextReader textReader = System.IO.File.OpenText(file))
                {
                    var input = new StringReader(textReader.ReadToEnd());
                    var yaml = new YamlStream();
                    yaml.Load(input);
                    foreach (var yamlDoc in yaml.Documents)
                    {
                        var mapping = (YamlMappingNode) yamlDoc.RootNode;
                        var name = mapping.Children[new YamlScalarNode("name")].ToString();
                        var doc = mapping.Children[new YamlScalarNode("doc")].ToString();
                        YamlNode labels;
                        var tags = new List<string>();
                        if (mapping.Children.TryGetValue(new YamlScalarNode("labels"), out labels))
                        {
                            tags.AddRange(from label in (YamlSequenceNode) labels select label.ToString());
                        }

                        var sb = new StringBuilder();
                        if (doc.Length > 128)
                        {
                            sb.Append($"<b>Description</b>: {doc} <br />");
                        }

                        mapping.Children
                            .Where(z => z.Key.ToString() != "name" && z.Key.ToString() != "doc" &&
                                        z.Key.ToString() != "labels").ForEach(z =>
                            {
                                if (z.Value.NodeType == YamlNodeType.Sequence)
                                {
                                    TraverseYamlNode(z, sb);
                                }
                                else
                                {
                                    sb.Append($"<b>{z.Key}</b>: {z.Value} <br />");
                                }
                            });
                        var article = new SolrForensicArtifactDocument
                        {
                            Title = doc.Length <= 128 ? doc : name,
                            ArtifactType = "Forensic Artifact",
                            Tags = tags,
                            Content = sb.ToString()
                        };
                        sqlBag.Add(
                            article.CreateArtifact(cimsAcct, Enumerable.Empty<HttpPostedFileBase>(), DB11, false));
                    }
                }
            }

            solr.Commit();
            DB11.knowledge_repo_history.AddRange(sqlBag);
            DB11.SaveChanges();
            return new EmptyResult();
        }

        private static void TraverseYamlNode(KeyValuePair<YamlNode, YamlNode> node, StringBuilder sb)
        {
            if (node.Value.NodeType == YamlNodeType.Mapping)
            {
                var mapping = (YamlMappingNode) node.Value;
                foreach (var child in mapping.Children)
                {
                    TraverseYamlNode(child, sb);
                }

                return;
            }

            if (node.Value.NodeType != YamlNodeType.Sequence)
            {
                sb.Append($"<b>{node.Key}</b>: {node.Value} <br />");
                return;
            }

            foreach (var subNode in (YamlSequenceNode) node.Value)
            {
                if (subNode.NodeType == YamlNodeType.Mapping)
                {
                    var mapping = (YamlMappingNode) subNode;
                    foreach (var child in mapping.Children)
                    {
                        TraverseYamlNode(child, sb);
                    }
                }
                else
                {
                    TraverseYamlNode(new KeyValuePair<YamlNode, YamlNode>(node.Key, subNode), sb);
                }
            }
        }

        public ActionResult IngestFolder(bool autoTag = false)
        {
            var solr = ServiceLocator.Current.GetInstance<ISolrOperations<SolrForensicArtifactDocument>>();
            var sqlBag = new List<knowledge_repo_history>();
            var cimsAcct = new AccountViewModel(DB11.account.First(z => z.name == "CIMS"));
            var dir = ConfigurationManager.AppSettings["KnowledgeRepoIngestFolder"];
            var extensions = ConfigurationManager.AppSettings["KnowledgeRepoIngestFolderExtensions"].Split(',')
                .Select(z => z.Trim());
            var rootFolderIndex = dir.Split('\\').Length - 1;
            var results = new List<object>();
            var titleSet = new HashSet<string>();
            foreach (var file in new DirectoryInfo(dir).EnumerateFiles(".", SearchOption.AllDirectories)
                .Where(z => extensions.Any(x => z.Name.EndsWith(x))))
            {
                var title = System.IO.Path.GetFileNameWithoutExtension(file.FullName).Replace("_", " ")
                    .Replace("-", " ");
                if (titleSet.Contains(title))
                {
                    results.Add(new
                    {
                        file = file.FullName,
                        success = false,
                        error =
                            "A file with this name has already been uploaded. Please use distinct titles for your documents."
                    });
                    continue;
                }
                var article = new SolrForensicArtifactDocument
                {
                    Title = title,
                    ArtifactType = "Uncategorized"
                };
                if (autoTag)
                {
                    var curPath = file.DirectoryName.Split('\\');
                    var tags = new List<string>();
                
                    for (int i = rootFolderIndex + 1; i < curPath.Length; i++)
                    {
                        if (curPath[i].Length >= 3) tags.Add(curPath[i]);
                    }

                    if (tags.Any()) article.Tags = tags;   
                }

                var wrapper = new FileFromDirectory(file);

                try
                {
                    var create = article.CreateArtifact(cimsAcct, new List<HttpPostedFileBase> {wrapper}, DB11, false);
                    sqlBag.Add(create);
                    titleSet.Add(title);
                    results.Add(new { file = file.FullName, success = true});
                }
                catch (KnowledgeRepositoryException e)
                {
                    results.Add(new { file = file.FullName, success = false, error = e.Message});
                }
            }

            solr.Commit();
            DB11.knowledge_repo_history.AddRange(sqlBag);
            DB11.SaveChanges();
            return Json(results, JsonRequestBehavior.AllowGet);
        }
    }
}