﻿using CIMSWeb.Models;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Web.Mvc;
using iTextSharp.text;
using iTextSharp.text.pdf;
using Kendo.Mvc.UI;
using Kendo.Mvc.Extensions;
using CIMSWeb.Attributes;
using CIMSWeb.Workflow;

namespace CIMSWeb.Controllers
{
    /// <summary>
    /// Controller for all things Evidence
    /// </summary>
    public class EvidenceController : CIMSController
    {
        /// <summary>
        /// Master list of evidence
        /// </summary>
        /// <returns></returns>
        public ActionResult Index()
        {
            return PartialView();
        }

        public ActionResult Tag()
        {
            ViewBag.tagWindow = "#newTagWindow";
            return PartialView("Tag");
        }

        public ActionResult EvidenceConfigurationSettings()
        {
            var vm = new EvidenceConfigurationSettings();
            return PartialView(vm);
        }


        public ActionResult EvidenceNote(int evidenceId, int examId)
        {
            ViewBag.examId = examId;
            ViewBag.evidenceId = evidenceId;
            ViewBag.userId = CurrentUserModel.AccountID;
            ViewBag.name = CurrentUserModel.Name;
            return PartialView();
        }

        public ActionResult ConfigurationItem()
        {
            ViewBag.IsAdmin = CurrentUserModel.Permissions.Any(z =>
                z.Role == Role.Admin && (z.Group == Group.Counterintelligence || z.Group == Group.ImagingExtraction ||
                                         z.Group == Group.MajorCrimes));
            var models = DB11.configuration_item.OrderBy(z => z.ci_number).ToList()
                .Select(z => new ConfigurationItemViewModel(z));
            return PartialView("ConfigurationItem", models);
        }

        public ActionResult IandEPlatform(int evidenceId, int examId, bool hardware)
        {
            var model = new ImagingAndExtractionPlatformViewModel (examId, evidenceId, hardware, DB11);
            ViewBag.hardware = hardware;
            return PartialView("ImagingAndExtractionPlatform", model);
        }

        public ActionResult GetCiForEvidence(int evidenceId, int examId)
        {
            var ci = DB11.evidence_configuration_item.Where(x => x.evidence_id == evidenceId && x.exam_id == examId).Include(x => x.configuration_item).ToList().Select(x => new { CiId = x.configuration_item_id, MBNum = x.MB_number, Software = x.configuration_item.software});
            return Json(ci, JsonRequestBehavior.AllowGet);
        }

        public ActionResult GetAdditionalCiForTag(int evidenceId, int examId, bool? hardware = null)
        {
            if (hardware.HasValue)
            {
                var models = DB11.evidence_config_item_detail.Where(z =>
        z.evidence_id== evidenceId && z.evidence_ci_detail_type.software != hardware && z.exam_id == examId).ToList()
    .Select(z => new CIAdditionalItemViewModel(z)).GroupBy(z => new { z.CiKey, z.CiValue })
    .Select(z => z.First());
                return Json(models, JsonRequestBehavior.AllowGet);
            }
            else
            {
                var models = DB11.evidence_config_item_detail.Where(z =>
        z.evidence_id== evidenceId && z.exam_id == examId).ToList()
    .Select(z => new CIAdditionalItemViewModel(z)).GroupBy(z => new { z.CiKey, z.CiValue })
    .Select(z => z.First());
                return Json(models, JsonRequestBehavior.AllowGet);
            }
        }

        public ActionResult CurrentEvidencePictures(int evidenceId)
        {
            var models = DB11.evidence_image.Where(z => z.evidence_id == evidenceId).ToList()
                .Select(z => new EvidencePictureViewModel(z));
            return PartialView("_EvidencePictures", models);
        }

        public ActionResult SaveTag(TagViewModel model)
        {
            if (model.EvidenceTagID == 0)
            {
                CIMSData.Database.evidence_tag t = new CIMSData.Database.evidence_tag()
                {
                    evidence_tag_type_id = model.TypeID,
                    tag_number = model.TagNumber,
                    date_seized = model.DateSeized
                };
                DB11.evidence_tag.Add(t);
                AuditInsert(t);
                DB11.SaveChanges();
                model.EvidenceTagID = t.evidence_tag_id;
                model.TagType = DB11.evidence_tag_type.First(x => x.evidence_tag_type_id == model.TypeID).tag_type;
                return Json(model);
            }
            var e = DB11.evidence_tag.First(x => x.evidence_tag_id == model.EvidenceTagID);
            AuditUpdate(e);
            model.Update(e);
            DB11.Entry(e).State = System.Data.Entity.EntityState.Modified;
            DB11.SaveChanges();
            return Json(model);
        }

        public ActionResult EditTag(int tagId)
        {
            ViewBag.tagWindow = "#editTagWindow";
            var e = new TagViewModel(DB11.evidence_tag.First(x => x.evidence_tag_id == tagId));
            return PartialView("Tag", e);
        }

        public ActionResult SavePlatform(ImagingAndExtractionPlatformViewModel model)
        {
            model.DB = DB11;
            model.UpdateAdditionalCi();
            return Json(new {id = new Random().Next(1, int.MaxValue)}, JsonRequestBehavior.AllowGet);
        }

        public ActionResult SaveBulkCI(MultiCiViewModel model)
        {
            model.DB = DB11;
            model.UpdateAdditionalCi();
            return Json(new { id = new Random().Next(1, int.MaxValue) }, JsonRequestBehavior.AllowGet);
        }

        public ActionResult Details(int evidenceID)
        {
            var model = new EvidenceViewModel(DB11.evidence.Find(evidenceID), DB11)
            {
                Pictures = DB11.evidence_image.Where(z => z.evidence_id == evidenceID).ToList()
                    .Select(z => new EvidencePictureViewModel(z)).ToList()
            };
            ViewBag.AnyArtifacts = SolrForensicArtifactDocument.AnyArtifactsForEvidence(evidenceID);
            return PartialView("Details", model);
        }

        public ActionResult DownloadPicture(string path)
        {
            return File(System.IO.File.ReadAllBytes(path), "image/jpeg", new FileInfo(path).Name);
        }

        public ActionResult GetEvidencePicture(int id)
        {
            var picture = DB11.evidence_image.First(z => z.evidence_image_id == id);
            return File(picture.image, "image/jpg");
        }

        public ActionResult IntrusionsEvidence()
        {
            ViewBag.Intrusions = true;
            return PartialView("Index");
        }

        public ActionResult EvidenceGridData([DataSourceRequest]DataSourceRequest request)
        {
            var data = DB11.vw_evidence_log.ToList().Select(x => new MiniEvidenceViewModel(x));
            return KendoGridResult(data.AsQueryable(), request);
        }

        /// <summary>
        /// Returns partial view for Media Package Tracking
        /// </summary>
        /// <param name="trackingNumber"></param>
        /// <returns></returns>
        //public ActionResult MediaPackageTracking(string trackingNumber)
        //{
        //    var viewItems = DB11.vw_evidence.Where(x => x.case_number == trackingNumber).ToList();
        //    List<MediaPackageTrackingViewModel> ret = new List<MediaPackageTrackingViewModel>();
        //    foreach (var v in viewItems)
        //    {
        //        foreach (var mr in DB.media_released.Where(x => x.evidence_id == v.evidence_id))
        //        {
        //            ret.Add(new MediaPackageTrackingViewModel()
        //            {
        //                Media = string.Format("Item {0} : {1} ", v.label, v.evidence_type),
        //                DateReleased = mr.changed_date.GetValueOrDefault(),
        //                ReleasedBy = mr.changed_by.FormatName(),
        //                Location = mr.location,
        //                Tracking = mr.tracking
        //            });
        //        }
        //    }
        //    return PartialView("_MediaPackageTracking", ret);
        //}

        /// <summary>
        /// Returns grid data for Chain of Custody
        /// </summary>
        /// <param name="trackingNumber"></param>
        /// <returns></returns>
        //public ActionResult ChainOfCustodyGridData([DataSourceRequest] DataSourceRequest request, int evidenceID)
        //{
        //    return Json(DB11.evidence_history.Where(x => x.evidence_id == evidenceID).ToList().Select(x => new ChainOfCustodyViewModel(x)), JsonRequestBehavior.AllowGet);
        //}

        /// <summary>
        /// Returns grid data for package tracking
        /// </summary>
        /// <param name="trackingNumber"></param>
        /// <returns></returns>
        public ActionResult EvidencePackageTrackingGridData([DataSourceRequest] DataSourceRequest request, int evidenceID)
        {
            var results = DB11.evidence.Where(x => x.evidence_id == evidenceID && x.exam_package_id.HasValue).ToList()
                .Select(x => new EvidencePackageTrackingViewModel(DB11.exam_package.First(y => y.exam_package_id == x.exam_package_id)));
            return KendoGridResult(results.AsQueryable(), request);
        }

        /// <summary>
        /// Evidence inventory view
        /// </summary>
        /// <returns></returns>
        public ActionResult Inventory()
        {
            var exams = DB11.exam.Where(x => x.exam_status.name != "DONE" && x.exam_status.name != "INVALID" && x.exam_status.name != "REJECTED");
            List<SelectListItem> examList = exams.Select(x => new SelectListItem() { Text = x.exam_number, Value = x.exam_number }).OrderBy(x => x.Text).ToList();
            examList.Insert(0, new SelectListItem() { Text = "Select exam" });
            List<SelectListItem> userList = DB11.account.Select(x => new SelectListItem() { Text = x.name, Value = x.sam_account_name }).OrderBy(x => x.Text).ToList();
            userList.Insert(0, new SelectListItem() { Text = "Select user" });
            return PartialView(new EvidenceInventoryPageModel()
            {
                Exams = examList,
                Users = userList
            });
        }

        public ActionResult DeleteEvidenceExam(int examID, int evidenceID)
        {
            var row = DB11.evidence.First(x => x.evidence_id == evidenceID);
            DB11.exam_activity.RemoveRange(row.exam_activity.ToList());
            DB11.exam_evidence.Remove(DB11.exam_evidence.First(z =>
                z.exam_id == examID && z.evidence_id == evidenceID));
            DB11.SaveChanges();
            var evidenceLabel = new EvidenceViewModel(DB11.evidence.First(z => z.evidence_id == evidenceID)).LabelLong;
            LogExamEvent(examID, $"Removed evidence item - {evidenceLabel}", ExamEventTypes.Evidence_Removed);
            return new EmptyResult();
        }

        public ActionResult DeleteEvidence(int evidenceID)
        {

            var row = DB11.evidence.First(x => x.evidence_id == evidenceID);
            //If the evidence item we are deleting has any children, we have to update the parent ids of its children to 0.This will allow it to default back to the tag
            var orphanedChildren = DB11.evidence.Where(x => x.evidence_parent_id == evidenceID).ToList();
            orphanedChildren.ForEach(x =>
            {
                AuditUpdate(x);
                x.evidence_parent_id = null;
                DB11.Entry(x).State = System.Data.Entity.EntityState.Modified;
            });
            AuditDelete(row);
            DB11.exam_activity.RemoveRange(row.exam_activity.ToList());
            DB11.evidence_image.RemoveRange(row.evidence_image.ToList());
            DB11.cm_history_evidence.RemoveRange(row.cm_history_evidence.ToList());
            DB11.evidence_config_item_detail.RemoveRange(row.evidence_config_item_detail.ToList());
            DB11.evidence_configuration_item.RemoveRange(row.evidence_configuration_item.ToList());
            DB11.evidence_note.RemoveRange(row.evidence_note.ToList());
            DB11.evidence.Remove(row);
            DB11.SaveChanges();
            return new EmptyResult();
        }

        public ActionResult ReadEvidenceNote([DataSourceRequest] DataSourceRequest request, int evidenceId, int examId)
        {
            var models = DB11.evidence_note
                .Where(z => z.evidence_id == evidenceId && z.exam_id == examId).ToList()
                .Select(z => new EvidenceNoteViewModel(z));
            return KendoGridResult(models.AsQueryable(), request);
        }

        public ActionResult AddEvidenceNote([DataSourceRequest] DataSourceRequest request, EvidenceNoteViewModel model)
        {
            var note = model.Create();
            DB11.evidence_note.Add(note);
            DB11.SaveChanges();
            model.Id = note.evidence_note_id;
            return Json(new[] { model }.ToDataSourceResult(request, ModelState));
        }

        public ActionResult UpdateEvidenceNote([DataSourceRequest] DataSourceRequest request, EvidenceNoteViewModel model)
        {
            var note = DB11.evidence_note.First(z => z.evidence_note_id == model.Id);
            model.Update(note);
            DB11.Entry(note).State = System.Data.Entity.EntityState.Modified;
            DB11.SaveChanges();
            return Json(new[] { model }.ToDataSourceResult(request, ModelState));
        }

        public ActionResult DeleteEvidenceNote([DataSourceRequest] DataSourceRequest request, EvidenceNoteViewModel model)
        {
            DB11.evidence_note.Remove(DB11.evidence_note.First(z => z.evidence_note_id == model.Id));
            DB11.SaveChanges();
            return Json(new[] { model }.ToDataSourceResult(request, ModelState));
        }

        public ActionResult AddConfigurationItem([DataSourceRequest] DataSourceRequest request, ConfigurationItemViewModel model)
        {
            var ci = model.Create();
            DB11.configuration_item.Add(ci);
            DB11.SaveChanges();
            model.Id = ci.configuration_item_id;
            return Json(new[] { model }.ToDataSourceResult(request, ModelState));
        }

        public ActionResult UpdateConfigurationItem([DataSourceRequest] DataSourceRequest request, ConfigurationItemViewModel model)
        {
            var ci = DB11.configuration_item.First(z => z.configuration_item_id == model.Id);
            model.Update(ci);
            DB11.Entry(ci).State = System.Data.Entity.EntityState.Modified;
            DB11.SaveChanges();
            return Json(new[] { model }.ToDataSourceResult(request, ModelState));
        }

        [CIMSAuthorize(SecureFunction.EvidenceConfigurationAdministration)]
        public JsonResult DeleteEvidenceType(int evidence_type_id)
        {
            var evidenceIsUsingThisType = DB11.evidence.FirstOrDefault(z => z.evidence_type_id == evidence_type_id);
            if (evidenceIsUsingThisType == null)
            {
                var evidence_type_fields = DB11.evidence_type_field.Where(z => z.evidence_type_id == evidence_type_id);
                foreach (var row in evidence_type_fields)
                {
                    AuditDelete(row);
                }
                DB11.evidence_type_field.RemoveRange(evidence_type_fields);
                var evidence_type = DB11.evidence_type.FirstOrDefault(z => z.evidence_type_id == evidence_type_id);
                AuditDelete(evidence_type);
                DB11.evidence_type.Remove(evidence_type);
                DB11.SaveChanges();
                return Json(true, JsonRequestBehavior.AllowGet);
            }
            else
            {
                return Json(false, JsonRequestBehavior.AllowGet);
            }
        }

        [CIMSAuthorize(SecureFunction.EvidenceConfigurationAdministration)]
        public JsonResult SaveEvidenceType(String evidence_type_name, String abbrev, int? evidence_type_id)
        {
            var found_evidence_type = DB11.evidence_type.FirstOrDefault(x => x.evidence_type_id == evidence_type_id);
            //For dupes
            var evidence_type_exists = DB11.evidence_type.Where(x => x.evidence_type1 == evidence_type_name).Select(x => x.evidence_type1).FirstOrDefault();
            if (evidence_type_exists == null)
            {
                if (found_evidence_type == null && evidence_type_name != null)
                {
                    var et = new CIMSData.Database.evidence_type()
                    {
                        evidence_type1 = evidence_type_name,
                        type_abbr = abbrev
                    };
                    DB11.evidence_type.Add(et);
                    AuditInsert(et);
                }

                if (found_evidence_type != null && evidence_type_name != null)
                {
                    found_evidence_type.evidence_type1 = evidence_type_name;
                    found_evidence_type.type_abbr = abbrev;
                    DB11.Entry(found_evidence_type).State = System.Data.Entity.EntityState.Modified;
                    AuditUpdate(found_evidence_type);
                }
            }
            else if(evidence_type_exists != null && found_evidence_type != null) //Same type already exists, just delete this one that is edited to have the same name.  Can delete because can edit, so no evidence is using this evidence type
            {
                AuditDelete(found_evidence_type);
                DB11.evidence_type.Remove(found_evidence_type);
            }
            else //when adding a duplicate evidence type (not editing, doesnt already exist)
            {
                //do nothing
            }

            DB11.SaveChanges();
            return Json(true, JsonRequestBehavior.AllowGet);
        }

        [CIMSAuthorize(SecureFunction.EvidenceConfigurationAdministration)]
        public ActionResult SaveDefaultEvidenceDetails(int evidence_type_id, string detail_type, int detailTypeId = 0)
        {

            var doesDetailTypeExist = DB11.evidence_detail_type.FirstOrDefault(z => z.detail_type == detail_type);
            var doesDefaultDetailTypeExistForEvidenceType = DB11.evidence_type_field.FirstOrDefault(z => z.evidence_detail_type.detail_type == detail_type && z.evidence_type_id == evidence_type_id);

            var isEdit = detailTypeId > 0;

            if (doesDetailTypeExist == null && detail_type != null)
            {//Add New detail Type
                doesDetailTypeExist = new CIMSData.Database.evidence_detail_type()
                {
                    detail_type = detail_type
                };
                DB11.evidence_detail_type.Add(doesDetailTypeExist);
                AuditInsert(doesDetailTypeExist);
                DB11.SaveChanges();
            }

            if (isEdit)//Editing
            {
                var detailToEdit = DB11.evidence_type_field.FirstOrDefault(z=> z.evidence_detail_type_id == detailTypeId && z.evidence_type_id == evidence_type_id);
                if (doesDefaultDetailTypeExistForEvidenceType != null)
                {//Default Detail already exists for evidence, just remove edited one
                    if (detailToEdit != null)
                    {
                        DB11.evidence_type_field.Remove(detailToEdit);
                        DB11.SaveChanges();
                    }
                }
                else
                {//Assign default detail to evidence type
                    if (detailToEdit != null)
                    {
                        if (doesDetailTypeExist != null)
                        {
                            detailTypeId = doesDetailTypeExist.evidence_detail_type_id;
                        }
                        detailToEdit.evidence_detail_type_id = detailTypeId;
                        DB11.Entry(detailToEdit).State = System.Data.Entity.EntityState.Modified;
                    }
                }
                DB11.SaveChanges();
            }
            else//adding, not editing
            {
                if(doesDefaultDetailTypeExistForEvidenceType == null && detail_type != null)
                {
                    if (doesDetailTypeExist != null)
                    {
                        detailTypeId = doesDetailTypeExist.evidence_detail_type_id;
                    }
                    var etf = new CIMSData.Database.evidence_type_field()
                    {
                        evidence_type_id = evidence_type_id,
                        evidence_detail_type_id = detailTypeId
                    };
                    DB11.evidence_type_field.Add(etf);
                    AuditInsert(etf);
                    DB11.SaveChanges();
                }
            }
           
            return Json(true, JsonRequestBehavior.AllowGet);
        }

        [CIMSAuthorize(SecureFunction.EvidenceConfigurationAdministration)]
        public JsonResult DeleteDefaultEvidenceDetails(int evidence_type_id, string detail_type)
        {
            var detailToDeleteId = DB11.evidence_detail_type.Where(z => z.detail_type == detail_type).Select(z => z.evidence_detail_type_id).FirstOrDefault();
            var detailToDelete = DB11.evidence_type_field.FirstOrDefault(z => z.evidence_detail_type_id == detailToDeleteId && z.evidence_type_id == evidence_type_id);
            if (detailToDelete == null) return Json(true, JsonRequestBehavior.AllowGet);
            DB11.evidence_type_field.Remove(detailToDelete);
            DB11.SaveChanges();

            return Json(true, JsonRequestBehavior.AllowGet);
        }

        public ActionResult ConfigureDefaultDetailsByEvidenceType([DataSourceRequest]DataSourceRequest request, int evidence_type_id)
        {
            var evidenceTypeFields = DB11.evidence_type_field.Where(x => x.evidence_type_id == evidence_type_id)
                .Select(z => new {Evidence_detail_type_id = z.evidence_detail_type_id});
            var results = DB11.evidence_detail_type
                .Where(x => evidenceTypeFields.Any(p => p.Evidence_detail_type_id == x.evidence_detail_type_id))
                .ToList().Select(
                    z => new DefaultDetail()
                        {Default_detail_type = z.detail_type, Default_detail_type_id = z.evidence_detail_type_id});
            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult GetEvidenceTypes([DataSourceRequest]DataSourceRequest request)
        {
            var results = DB11.evidence_type.Select(z => new EvidenceType{Evidence_type = z.evidence_type1, EvidenceTypeId = z.evidence_type_id, Abbreviation = z.type_abbr, IsUsed = 1}).ToList();
            foreach(var result in results)
            {
                var isFound = DB11.evidence.FirstOrDefault(z => z.evidence_type_id == result.EvidenceTypeId);
                if (isFound == null)
                {
                    result.IsUsed = 0;
                }
            }
            return KendoGridResult(results.AsQueryable(), request);
        }

        /// <summary>
        /// Provides the JSON for individual evidence details grids
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public ActionResult DefaultDetailsGridData([DataSourceRequest]DataSourceRequest request, int evidence_id, int evidence_type_id)
        {
            var results = DB11.evidence_detail.Where(x => x.evidence_id == evidence_id).ToList()
                .Select(x => new EvidenceDetailViewModel(x)).ToList();
            var detail_types = results.Select(z => z.DetailKeyID).ToList();
            var defaultDetails = DB11.evidence_type_field.Where(
                    x => x.evidence_type_id == evidence_type_id && !detail_types
                             .Any(p => p == x.evidence_detail_type_id))
                .Select(z => new EvidenceDetailViewModel
                {
                    DetailKeyID = z.evidence_detail_type_id, DetailValue = "", DetailValueID = 0,
                    ID = z.evidence_type_field_id.ToString(), DetailKey = z.evidence_detail_type.detail_type
                }).ToList();

            results.AddRange(defaultDetails);

            return KendoGridResult(results.AsQueryable(), request);
        }


        private static iTextSharp.text.Font normal = new iTextSharp.text.Font(iTextSharp.text.Font.UNDEFINED, 11, iTextSharp.text.Font.NORMAL);
        private static iTextSharp.text.Font fontHead = new iTextSharp.text.Font(normal.Family, 20, iTextSharp.text.Font.NORMAL);
        private static iTextSharp.text.Font fontSubHead = new iTextSharp.text.Font(normal.Family, 12, iTextSharp.text.Font.NORMAL);
        private static iTextSharp.text.Font bold = new iTextSharp.text.Font(normal.Family, 11, iTextSharp.text.Font.BOLD);

        public ActionResult EvidenceReport(EvidenceReportSettings model)
        {
            var exam = DB11.exam.First(z => z.exam_id == model.ExamId);
            var user = new AccountViewModel(DB11.account.First(z => z.account_id == model.UserId));
            var report = new EvidenceReportModel(exam, user, Server.MapPath(@"~/"), Server.MapPath(ImageBrowserController.contentFolderRoot), DB11, model.EvidenceIds).BuildEvidenceReport();
            var fileName = $"{exam.exam_number}.Technician_Notes.{user.LastName.ToUpper()}.CIMS_Generated";
            fileName += model.Pdf ? ".pdf" : ".docx";
            var fileFormat = model.Pdf ? Aspose.Words.SaveFormat.Pdf : Aspose.Words.SaveFormat.Docx;
            report.Save(System.Web.HttpContext.Current.Response, fileName,
                Aspose.Words.ContentDisposition.Attachment,
                Aspose.Words.Saving.SaveOptions.CreateSaveOptions(fileFormat));
            return new EmptyResult();
        }

        public ActionResult MigrateEvidenceImages()
        {
            var images = DB11.evidence_image.ToList();
            foreach (var image in images)
            {
                System.IO.File.WriteAllBytes(@"C:\Users\<USER>\CIMSWeb\CIMSWeb\Content\Evidence_Images", image.image);
                // image.md5 = image.image.md5();
                image.image = new byte[0];
                DB11.Entry(image).State = EntityState.Modified;
            }
            DB11.SaveChanges();
            return new EmptyResult();
        }

        public class PDF_Footer : PdfPageEventHelper
        {
            PdfContentByte cb;
            PdfTemplate template;
            BaseFont timesBold;

            public override void OnOpenDocument(PdfWriter writer, Document document)
            {
                cb = writer.DirectContent;
                template = cb.CreateTemplate(40, 40);
                timesBold = BaseFont.CreateFont(BaseFont.TIMES_BOLD, BaseFont.WINANSI, BaseFont.NOT_EMBEDDED);
            }
            public override void OnEndPage(PdfWriter writer, Document document)
            {
                base.OnEndPage(writer, document);
                cb.BeginText();
                cb.SetFontAndSize(timesBold, 12f);
                cb.SetTextMatrix(document.PageSize.Width / 2, 20);
                cb.ShowText(writer.PageNumber.ToString());
                cb.EndText();
                cb.AddTemplate(template, document.LeftMargin, document.PageSize.GetBottom(document.BottomMargin));
            }
        }
    }
}