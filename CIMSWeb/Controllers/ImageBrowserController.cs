﻿using System.IO;
using Kendo.Mvc.UI;

namespace CIMSWeb.Controllers
{
    public class ImageBrowserController : EditorImageBrowserController
    {
        public const string contentFolderRoot = "~/form9a/";


        /// <summary>
        /// Gets the base paths from which content will be served.
        /// </summary>
        public override string ContentPath
        {
            get
            {
                return CreateUserFolder();
            }
        }


        private string CreateUserFolder()
        {
            var path = Server.MapPath(contentFolderRoot);
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }
            return contentFolderRoot;
        }
    }
}