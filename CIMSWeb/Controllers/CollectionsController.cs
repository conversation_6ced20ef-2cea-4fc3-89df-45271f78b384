﻿using CIMSWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Mvc;
using CIMSWeb.Workflow;
using Kendo.Mvc.Extensions;
using Microsoft.Ajax.Utilities;
using WebGrease.Css.Extensions;
using SolrNet.Utils;

namespace CIMSWeb.Controllers
{
    /// <summary>
    /// Controller for harvesting values that go into autocomplete fields, dropdowns, etc
    /// </summary>
    public class CollectionsController : CIMSController
    {
        private readonly List<int> deprecatedCategoryIds = new List<int> { 12, 16, 38, 39 }; // 12 = Digital Multimedia, 16 = FDE, 38 = Old Style LS, 39 = PVO
        /// <summary>
        /// All exam categories
        /// </summary>
        /// <param name="examTypeID">Exam type identifier - ie standard, litigation support, intrusions</param>
        /// <returns></returns>
        public JsonResult ExamCategories(int examTypeID, int? examId)
        {
            var cats = DB11.exam_category.Where(x => x.exam_type_id == examTypeID).OrderBy(x => x.exam_category1).ToList();
            bool removeDeprecated = true;
            int deprecatedCatId = 0;
            if (examId.HasValue)
            {
                var cat = DB11.exam.Find(examId).exam_category_id;
                if (cat.HasValue && deprecatedCategoryIds.Contains((int)cat))
                {
                    removeDeprecated = false;
                    deprecatedCatId = (int)cat;
                }
            }

            cats = removeDeprecated
                ? cats.Where(z => !deprecatedCategoryIds.Contains(z.exam_category_id)).ToList()
                : cats.Where(z => !deprecatedCategoryIds.Where(x => x != deprecatedCatId).Contains(z.exam_category_id)).ToList();
            return Json(cats.Select(x => x.exam_category1.TitleCase()).Distinct(), JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// All exam subcategories
        /// </summary>
        /// <param name="category">Parent category name</param>
        /// <returns></returns>
        public JsonResult ExamSubCategories(string category, int? examId)
        {
            var subcats = DB11.exam_category.Where(x => x.exam_category1 == category.ToUpper())
                .OrderBy(x => x.exam_subcategory).ToList();
            bool removeDeprecated = true;
            int deprecatedCatId = 0;
            if (examId.HasValue)
            {
                var cat = DB11.exam.Find(examId).exam_category_id;
                if (cat.HasValue && deprecatedCategoryIds.Contains((int)cat))
                {
                    removeDeprecated = false;
                    deprecatedCatId = (int)cat;
                }
            }

            subcats = removeDeprecated
                ? subcats.Where(z => !deprecatedCategoryIds.Contains(z.exam_category_id)).ToList()
                : subcats.Where(z => !deprecatedCategoryIds.Where(x => x != deprecatedCatId).Contains(z.exam_category_id)).ToList();
            return Json(subcats.Select(x => new { Value = x.exam_category_id.ToString(), Text = x.exam_subcategory.TitleCase() }), JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// All investigation types
        /// </summary>
        /// <returns></returns>
        public JsonResult InvestigationTypes(string subcat)
        {
            var inv_type = (
                from it in DB11.exam_investigation_type
                join ec in DB11.exam_category on it.exam_investigation_type_id equals ec.exam_investigation_type_id
                where ec.exam_subcategory == subcat
                select new SelectListItem()
                {
                    Value = it.exam_investigation_type_id.ToString(),
                    Text = it.exam_investigation_type1
                }
                ).Distinct();
            var selectedValue = inv_type.First().Text;
            return Json(selectedValue, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// All database groups
        /// </summary>
        /// <returns></returns>
        public JsonResult Groups()
        {
            return Json(DB11.group.ToList().Select(x => new SelectListItem() { Value = x.group_id.ToString(), Text = x.name }), JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// All lab sections
        /// </summary>
        /// <returns></returns>
        public JsonResult Sections()
        {
            return Json(DB11.group.Where(x => x.group_id != 1 && x.group_id != 11 && x.group_id != 4).ToList().Select(x => new SelectListItem() { Value = x.group_id.ToString(), Text = x.name }), JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// Types of evidence as defined in the database
        /// </summary>
        /// <returns></returns>
        public JsonResult EvidenceTypes()
        {
            return Json(DB11.evidence_type.OrderBy(z => z.evidence_type1)
                          .ToList()
                          .Select(x => new { Text = x.evidence_type1, Value = x.evidence_type_id }),
                        JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// All possible evidence make values
        /// </summary>
        /// <returns></returns>
        public JsonResult EvidenceMakes()
        {
            return Json(DB11.evidence.Select(x => x.make).Distinct().OrderBy(x => x), JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// All possible evidence model values
        /// </summary>
        /// <returns></returns>
        public JsonResult EvidenceModels()
        {
            return Json(DB11.evidence.Select(x => x.model).Distinct().OrderBy(x => x), JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// All tags that currently exist for a given case
        /// </summary>
        /// <param name="caseID"></param>
        /// <returns></returns>
        public JsonResult EvidenceTags(int caseID)
        {
            return Json(DB11.evidence.Where(x => x.case_id == caseID && x.evidence_tag_id != null).ToList()
                .Select(x => new { Text = x.evidence_tag.evidence_tag_type.tag_type + " " + x.evidence_tag.tag_number, Value = x.evidence_tag_id })
                .OrderBy(x => x.Text).Distinct(), JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// All tag types 
        /// </summary>
        /// <returns></returns>
        public JsonResult EvidenceTagTypes()
        {
            return Json(DB11.evidence_tag_type.Select(x => new { Value = x.evidence_tag_type_id, Text = x.tag_type }).OrderBy(x => x.Text), JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// All existing case numbers in the system
        /// </summary>
        /// <returns></returns>
        public JsonResult CaseNumbers()
        {
            return Json(<EMAIL>(x => x.case_number).Select(x => new { Value = x.case_id, Text = x.case_number }), JsonRequestBehavior.AllowGet);
        }
        
        /// <summary>
        /// All existing non-intrusions case numbers in the system
        /// </summary>
        /// <returns></returns>
        public JsonResult NonIntrusionsCaseNumbers()
        {
            var non_intrusions_cases =
                DB11.exam.Where(x => x.@case != null)
                    .GroupBy(x => x.@case)
                    .Where(x => x.All(t => t.exam_type_id != (int) ExamType.Intrusions))
                    .OrderBy(x => x.Key.case_number)
                    .Select(x => new
                    {
                        Value = x.Key.case_id,
                        Text = x.Key.case_number
                    }).Distinct();
            return Json(non_intrusions_cases, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// All existing exam/tracking numbers in the system
        /// </summary>
        /// <returns></returns>
        public JsonResult ExamNumbers()
        {
            return Json(DB11.exam.OrderByDescending(z => z.exam_number).Select(x => new { ID = x.exam_id, DisplayName = x.exam_number }), JsonRequestBehavior.AllowGet);
        }
        
        public JsonResult OpenExamNumbers()
        {
            return Json(DB11.exam.OpenExams().OrderByDescending(z => z.exam_number).Select(x => new { ID = x.exam_id, DisplayName = x.exam_number }), JsonRequestBehavior.AllowGet);
        }

        public JsonResult ExamNumbersOnly()
        {
            return Json(DB11.exam.Select(x => x.exam_number).OrderBy(x => x), JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// All classification options
        /// </summary>
        /// <returns></returns>
        public JsonResult Classifications()
        {
            return Json(DB11.classification.OrderBy(x => x.list_order).Select(x => new { Value = x.classification_id.ToString(), Text = x.level }), JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// All jurisdiction options
        /// </summary>
        /// <returns></returns>
        public JsonResult Jurisdictions()
        {
            return Json(DB11.jurisdiction.OrderBy(x => x.list_order).Select(x => new { Value = x.jurisdiction_id.ToString(), Text = x.name }),
                        JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// All proceeding type options
        /// </summary>
        /// <returns></returns>
        public JsonResult ProceedingTypes()
        {
            return Json(DB11.proceeding.OrderBy(x => x.list_order).Select(x => new { Value = x.proceeding_id.ToString(), Text = x.name }),
                        JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// Jurisdiction options for a case
        /// </summary>
        /// <returns></returns>
        public JsonResult CaseJurisdictions()
        {
            return Json(new string[] { "Military", "Federal", "State", "Other" }, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// All customer agencies in the database
        /// </summary>
        /// <returns></returns>
        public JsonResult Agencies()
        {
            return Json(DB11.agency.Select(x => new { Value = x.agency_id, Text = x.name }).OrderBy(x => x.Text), JsonRequestBehavior.AllowGet);
        }

        /// <summary>All customer agencies' name in the database</summary>
        /// <returns></returns>
        public JsonResult AgenciesName()
        {
            return Json(DB11.agency.Select(x => new { Agency = x.name }).OrderBy(x => x.Agency), JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// All agency units under a given agency
        /// </summary>
        /// <param name="agencyID">Parent agency</param>
        /// <returns></returns>
        public JsonResult AgencyUnits(int? agencyID)
        {
            return Json(DB11.agency_org.Where(x => x.agency_id == agencyID).OrderBy(x => x.agency_unit.name)
                                       .Select(x => new { Value = x.agency_unit_id.ToString(), Text = x.agency_unit.name }),
                        JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// Types of Intrusions exams
        /// </summary>
        /// <returns></returns>
        public JsonResult IntrusionsExamTypes()
        {
            return Json(new string[] { "", "Malware", "Network", "Systems" }, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// All contacts in address book
        /// </summary>
        /// <returns></returns>
        public JsonResult Contacts()
        {
            return Json(DB11.contact.ToList().Select(x => new ContactSelectViewModel(x)), JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// All countries in db
        /// </summary>
        /// <returns></returns>
        public JsonResult Countries()
        {
            var countries = DB11.country.ToList().Select(x => new { Text = x.name, Value = (int?)x.country_id }).ToList();
            // Insert a blank value at the start of the list so a country does not default to a value
            countries.Insert(0, new { Text = "", Value = (int?)null });
            var usa = countries.First(x => x.Text == "United States");
            // Remove United States, then insert it back at the beginning of the list (after the initial empty one)
            countries.RemoveAt(countries.IndexOf(usa));
            countries.Insert(1, new { Text = "United States", Value = usa.Value });
            return Json(countries.AsEnumerable(), JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// All states in db
        /// </summary>
        /// <returns></returns>
        public JsonResult States()
        {
            var states = DB11.state.ToList().Select(x => new { Text = x.name, Value = (int?)x.state_id }).ToList();
            // Insert a blank value at the start of the list so a state does not default to a value
            states.Insert(0, new { Text = "", Value = (int?)null });
            return Json(states.AsEnumerable(), JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// All accounts in db
        /// </summary>
        /// <returns></returns>
        public JsonResult Accounts()
        {
            return Json(DB11.account.Where(x => x.enabled).ToList().Select(x => new { Text = x.name.FormatName(), Value = x.account_id }).OrderBy(x => x.Text), JsonRequestBehavior.AllowGet);
        }

        public JsonResult EnabledAccountsWithPermissions()
        {
            return Json(DB11.account.Where(x => x.enabled && x.account_permission.Any()).ToList().Select(x => new { Text = x.name.FormatName(), Value = x.account_id }).OrderBy(x => x.Text), JsonRequestBehavior.AllowGet);
        }

        public JsonResult SamAccounts()
        {
            return Json(DB11.account.Where(x => x.enabled).ToList().Select(x => new {
                ID = x.account_id,
                NameSAMname = x.sam_account_name
            }).OrderBy(x => x.NameSAMname), JsonRequestBehavior.AllowGet);
        }

        public JsonResult AccountModels()
        {
            return Json(DB11.account.Where(x => x.enabled).ToList().Select(x => new CIMSWeb.Models.AccountViewModel(x)).OrderBy(x => x.Name), JsonRequestBehavior.AllowGet);
        }

        public JsonResult AccountModelsByGroup(int groupID)
        {
            var hits = DB11.account_permission.Where(x => x.group_id == groupID && x.account.enabled).ToList().Select(x => x.account).Distinct().Select(x => new AccountViewModel(x)).OrderBy(x => x.Name).ToList();
            foreach (var x in hits)
            {
                x.CountActiveExams(groupID, DB11);
            }
            return Json(hits, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// All accounts in db associated with a specified group
        /// </summary>
        /// <returns></returns>
        public JsonResult AccountsByGroup(int? groupID = null)
        {
            if(groupID == null)
                return Json(DB11.account.Where(x => x.enabled).ToList().Select(x => new { Text = x.name.FormatName(), Value = x.account_id }).OrderBy(x => x.Text), JsonRequestBehavior.AllowGet);

            return Json(DB11.account_permission.Where(x => x.group_id == groupID && x.account.enabled).ToList()
                .Select(x => new { Text = x.account.name.FormatName(), Value = x.account_id }).Distinct().OrderBy(x => x.Text), JsonRequestBehavior.AllowGet);
        }
        
        public JsonResult AccountsByGroupsList(IEnumerable<int> groups)
        {
            return Json(DB11.account_permission.Where(x => groups.Contains(x.group_id) && x.account.enabled).ToList()
                .Select(x => new { Text = x.account.name.FormatName(), Value = x.account_id }).Distinct().OrderBy(x => x.Text), JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// Evidence detail types in the db
        /// </summary>
        /// <returns></returns>
        public JsonResult EvidenceDetailTypes()
        {
            return Json(DB11.evidence_detail_type.OrderBy(x => x.detail_type).Select(x => x.detail_type), JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// Evidence detail values in the db
        /// </summary>
        /// <returns></returns>
        public JsonResult EvidenceDetailValues()
        {
            return Json(DB11.evidence_detail_value.OrderBy(x => x.detail_value).Select(x => x.detail_value), JsonRequestBehavior.AllowGet);
        }

        public JsonResult MailServices()
        {
            return Json(DB11.mail_service.ToList().OrderBy(x => x.name)
                .Select(x => new { Text = x.name, Value = x.mail_service_id }), JsonRequestBehavior.AllowGet);
        }

        public JsonResult HardDrives()
        {
            return Json(DB11.hard_drive.ToList().OrderBy(x => x.mb_number)
                .Select(x => new HardDriveViewModel(x)), JsonRequestBehavior.AllowGet);
        }

        public JsonResult HardDriveStatuses()
        {
            return Json(DB11.hard_drive_status.Select(x =>
                new { Text = x.hard_drive_status1, Value = x.hard_drive_status_id }), JsonRequestBehavior.AllowGet);
        }

        /// <summary>All Communication types</summary>
        /// <returns></returns>
        public JsonResult CommunicationTypes()
        {
            return Json(DB11.communication_type.Where(x => x.communication_type_id != 5).Select(x => new CIMSWeb.Models.Comm.CommType { Text = x.communication_type1, ID = x.communication_type_id }), JsonRequestBehavior.AllowGet);
        }

        /// <summary>All Communication topics</summary>
        /// <returns></returns>
        public JsonResult CommunicationTopics()
        {
            return Json(DB11.communication_topic.Select(x => new CIMSWeb.Models.Comm.CommTopic { Text = x.communication_topic1, ID = x.communication_topic_id }), JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// All directorates
        /// </summary>
        /// <returns></returns>
        public JsonResult Directorates()
        {
            return Json(DB11.directorate.Select(x => new { Value = x.directorate_id, Text = x.name }).OrderBy(x => x.Text), JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// All exam hold types
        /// </summary>
        /// <returns></returns>
        public JsonResult ExamHoldTypes()
        {
            // hold type 5 is the "Unknown" type
            return Json(DB11.exam_hold_type.Where(x => x.exam_hold_type_id != 5).Select(x => new { Value = x.exam_hold_type_id, Text = x.name }).OrderBy(x => x.Text), JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// All document types
        /// </summary>
        /// <returns></returns>
        public JsonResult DocumentTypes()
        {
            var list = Enum.GetValues(typeof(DocumentTypes)).Cast<DocumentTypes>()
                    .Select(x => new { Value = (int)x, Text = x.GetDisplayName() }).ToList();
            // move the form 8 to be before the form 9s.
            list.Insert(5, list.Last());
            list.RemoveAt(list.Count -1);
            // move the form 9 fast to be next to the other form 9s.
            list.Insert(8, list.Last());
            list.RemoveAt(list.Count - 1);
            return Json(list, JsonRequestBehavior.AllowGet);
        }

        public JsonResult MD5MatchValues()
        {
            return Json(Enum.GetValues(typeof(MD5Match)).Cast<MD5Match>().Select(x => new { Value = (int)x, Text = x.DisplayName() }), JsonRequestBehavior.AllowGet);
        }

        public JsonResult ConfigurationItems(bool isOs, int examId = -1, int evidenceId = -1)
        {
            var items = new List<EvidenceReportDropDownModel>();
            if (examId > -1 && evidenceId > -1)
            {
                var curCi = DB11.evidence_configuration_item.Where(z =>
    z.exam_id == examId && z.evidence_id == evidenceId && z.configuration_item.software == isOs).ToList();
                if (curCi.Count > 0)
                {
                    var ci = curCi.First().configuration_item;
                    items.Add(new EvidenceReportDropDownModel
                    {
                        Value = ci.configuration_item_id,
                        Text = ci.ci_number + " | " + ci.ci_name + " | " + ci.ci_version,
                        Selected = true
                    });
                }
            }

            items.AddRange(DB11.configuration_item.Where(z => z.active == true && z.software == isOs)
                .Select(z => new EvidenceReportDropDownModel
                {
                    Value = z.configuration_item_id,
                    Text = z.ci_number + " | " + z.ci_name + " | " + z.ci_version
                }));
            return Json(items.DistinctBy(z => z.Value).OrderBy(z => z.Text), JsonRequestBehavior.AllowGet);
        }

        public JsonResult AdditionalCiDetailTypes(bool software)
        {
            return Json(
                DB11.evidence_ci_detail_type.Where(z => z.software == software).OrderBy(x => x.ci_detail_type)
                    .Select(x => x.ci_detail_type), JsonRequestBehavior.AllowGet);
        }

        public JsonResult AdditionalCiDetailValues(bool software)
        {
            return Json(
                DB11.evidence_ci_detail_value.Where(z => z.software == software).OrderBy(x => x.ci_detail_value)
                    .Select(x => x.ci_detail_value), JsonRequestBehavior.AllowGet);
        }

        public JsonResult AccountTypes()
        {
            return Json(DB11.account_type.ToList().Select(z => new { Text = z.name.TitleCase(), Value = z.account_type_id }),
                JsonRequestBehavior.AllowGet);
        }
        
        public JsonResult CiMbNumbers(bool software)
        {
            return Json(
                DB11.evidence_configuration_item.Where(z =>
                        z.configuration_item.software == software && z.MB_number != null && z.MB_number != "")
                    .GroupBy(z => z.MB_number).Select(z => z.FirstOrDefault()).ToList()
                    .Select(z => new {Text = z.MB_number}),
                JsonRequestBehavior.AllowGet);
        }

        public JsonResult ForensicArtifactTypes()
        {
            return Json(DB11.knowledge_repo_types.OrderBy(z => z.article_type).ToList().Select(z => new
                {Text = z.article_type, Value = z.article_type}), JsonRequestBehavior.AllowGet);
        }

        public JsonResult CMInventory(int? id)
        {
            return Json(
                DB11.cm_inventory.Where(z => (z.status_id == (int) CMStatusTypes.In_Service &&
                                             (z.group_id == (int) Group.Counterintelligence ||
                                              z.group_id == (int) Group.MajorCrimes ||
                                              z.group_id == (int) Group.ImagingExtraction ||
                                              z.group_id == (int) Group.ADAT)) || z.id == id)
                    .Select(z => new {z.asset_tag, z.serial_number, z.mb_number, z.id, z.manufacturer, z.model_number})
                    .ToList().Select(z => new
                    {
                        Text =
                            $"{z.asset_tag.NullIfEmpty() ?? z.serial_number.NullIfEmpty() ?? z.mb_number} ({z.manufacturer} {z.model_number})",
                        Value = z.id
                    }).OrderBy(z => z.Text),
                JsonRequestBehavior.AllowGet);
        }
        
        public JsonResult CMBuilds(bool sw)
        {
            return Json(
                DB11.configuration_item.Where(z => z.active == true && z.software == sw).ToList().Select(z => new
                    {Text = z.ci_number + " | " + z.ci_name + " | " + z.ci_version, Value = z.configuration_item_id}).OrderBy(z => z.Text),
                JsonRequestBehavior.AllowGet);
        }
        
        public JsonResult CMInventoryTypes()
        {
            return Json(
                DB11.cm_inventory_type.ToList().Select(z => new {Text = z.type, Value = z.id}).OrderBy(z => z.Text),
                JsonRequestBehavior.AllowGet);
        }

        public async Task<JsonResult> PasswordSetsSources()
        {
            var sources = await HashSetsManageModel.Get(false);
            return Json(sources.Select(z => new {Text = z.Title, Value = z.Title}), JsonRequestBehavior.AllowGet);
        }
        
        public JsonResult TicketTypes(int id)
        {
            int typeId = 0;
            if (id > 0)
            {
                typeId = DB11.eh_ticket.Find(id).type_id;
            }
            var query = id == 0
                ? DB11.eh_ticket_type.Where(z => z.enabled)
                : DB11.eh_ticket_type.Where(z => z.enabled || z.id == typeId);
            return Json(query.OrderBy(z => z.ticket_type).ToList().Select(z => new
                {Text = z.ticket_type, Value = z.id}), JsonRequestBehavior.AllowGet);
        }
    }
}
