using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading;
using System.Web;
using System.Web.Mvc;
using Aspose.Words;
using CIMSData.Database;
using CIMSWeb.Models;
using CIMSWeb.Workflow;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;

namespace CIMSWeb.Controllers
{
    public class ExamHelpController : CIMSController
    {
        public const string WorkingText = "Marked this ticket as being worked on.";
        public const string ClosedText = "Closed the ticket.";
        public const string ReopenText = "Re-opened the ticket.";
        public const string OpenText = "Created this Ticket.";
        // GET
        public ActionResult Index()
        {
            ViewBag.IsAdmin = CurrentUserModel.Permissions.Any(z =>
                z.Role == Role.Admin && (z.Group == Group.Counterintelligence || z.Group == Group.MajorCrimes ||
                                         z.Group == Group.ImagingExtraction));
            return PartialView();
        }

        public ActionResult Overview()
        {
            return PartialView(new ExamHelpOverview(DB11, CurrentUserModel.AccountID));
        }

        public ActionResult Messages()
        {
            return PartialView();
        }

        public ActionResult MessageGridData([DataSourceRequest] DataSourceRequest request)
        {
            return DB11.message_recipients.Where(x => x.recipient_id == CurrentUserModel.AccountID)
                .OrderByDescending(x => x.message.date)
                .Include(x => x.message.account).Include(x => x.message).ToList()
                .Select(x => new MessageModel(x)).ToKendoGridResult(request);
        }

        public ActionResult CountUnreadMessages()
        {
            return Json(DB11.message_recipients.Count(x => x.recipient_id == CurrentUserModel.AccountID && !x.is_read),
                JsonRequestBehavior.AllowGet);
        }

        public ActionResult UserTicketsGridData([DataSourceRequest] DataSourceRequest request)
        {
            var users = DB11.eh_ticket
                .Where(z => z.reporter_id == CurrentUserModel.ActualAccountID ||
                            z.assignee_id == CurrentUserModel.ActualAccountID).ToList();
            var subs = DB11.eh_user_subscriptions.Where(z => z.account_id == CurrentUserModel.AccountID)
                .Select(x => x.eh_ticket).ToList();
            return users.Union(subs).ToList().Select(z => new MiniExamHelpModel(z)).ToKendoGridResult(request);
        }

        public ActionResult ExamTicketsGridData([DataSourceRequest] DataSourceRequest request, int examId)
        {
            return DB11.eh_ticket.Where(z => z.exam_id == examId).ToList()
                .Select(z => new ExamHelpModel(z)).ToKendoGridResult(request);
        }

        public ActionResult AllTicketsGridData([DataSourceRequest] DataSourceRequest request)
        {
            return DB11.eh_ticket.IncludeForExamHelpTickets().ToList().Select(z => new MiniExamHelpModel(z)).ToKendoGridResult(request);
        }

        public ActionResult TicketTypesGridData([DataSourceRequest] DataSourceRequest request)
        {
            return DB11.eh_ticket_type.ToList().Select(z => new TicketTypeModel(z, DB11)).ToKendoGridResult(request);
        }

        public ActionResult CreateTicketType([DataSourceRequest] DataSourceRequest request, TicketTypeModel model)
        {
            var row = model.Create();
            DB11.eh_ticket_type.Add(row);
            DB11.SaveChanges();
            model.Id = row.id;
            model.CanDelete = true;
            using (var db = new cims11Entities())
            {
                var newModel = new TicketTypeModel(db.eh_ticket_type.Find(model.Id), db);
                return Json(new[] {newModel}.ToDataSourceResult(request, ModelState));
            }
        }

        public ActionResult UpdateTicketType([DataSourceRequest] DataSourceRequest request, TicketTypeModel model)
        {
            var row = DB11.eh_ticket_type.Find(model.Id);
            model.Update(row, DB11);
            DB11.Entry(row).State = EntityState.Modified;
            DB11.SaveChanges();
            using (var db = new cims11Entities())
            {
                var newModel = new TicketTypeModel(db.eh_ticket_type.Find(model.Id), db);
                return Json(new[] {newModel}.ToDataSourceResult(request, ModelState));
            }
        }

        public ActionResult DeleteTicketType(int id)
        {
            if (!DB11.eh_ticket.Any(z => z.type_id == id))
            {
                var row = DB11.eh_ticket_type.Find(id);
                DB11.eh_ticket_type_members.RemoveRange(DB11.eh_ticket_type_members.Where(x => x.ticket_type_id == id));
                DB11.eh_ticket_type.Remove(row);
                DB11.SaveChanges();
            }

            return new EmptyResult();
        }

        public ActionResult ViewTicket(int id)
        {
            var model = new ExamHelpModel(DB11.eh_ticket.Find(id));
            SetViewBagFlags(model);
            return PartialView(model);
        }

        public ActionResult Subscribe(int id)
        {
            var cur = DB11.eh_user_subscriptions.FirstOrDefault(x =>
                x.account_id == CurrentUserModel.AccountID && x.ticket_id == id);
            if (cur == null)
            {
                DB11.eh_user_subscriptions.Add(new eh_user_subscriptions()
                    {account_id = CurrentUserModel.AccountID, ticket_id = id});
            }
            else
            {
                DB11.eh_user_subscriptions.RemoveRange(DB11.eh_user_subscriptions.Where(x =>
                    x.account_id == CurrentUserModel.AccountID && x.ticket_id == id));
            }

            DB11.SaveChanges();
            return new EmptyResult();
        }
        
        public ActionResult Download(int id, string mimeType)
        {
            var file = DB11.eh_attachments.Find(id);
            return File(System.IO.File.ReadAllBytes(file.saved_path), mimeType, file.file_name);
        }

        public ActionResult SelfAssign(int id)
        {
            var row = DB11.eh_ticket.Find(id);
            row.assignee_id = CurrentUserModel.AccountID;
            row.status_id = 2;
            DB11.Entry(row).State = EntityState.Modified;
            var comment = $"{CurrentUserModel.Name} assigned themselves to this ticket.";
            DB11.eh_history.Add(LogHistory(comment, id));
            DB11.SaveChanges();
            ExamHelpModel model;
            using (var db = new cims11Entities())
            {
                model = new ExamHelpModel(db.eh_ticket.Find(id));
            }

            SetViewBagFlags(model);
            return PartialView("ViewTicket", model);
        }

        public ActionResult AssignTicket(int id, int? assigneeId)
        {
            ViewBag.TicketId = id;
            ViewBag.AssigneeId = assigneeId;
            return PartialView(DB11.account.Where(z => z.enabled && z.account_permission.Any()).OrderBy(z => z.name)
                .ToList().Select(z => new AccountViewModel(z)));
        }

        public ActionResult AssignUser(int ticketId, int userId)
        {
            var row = DB11.eh_ticket.Find(ticketId);
            var unassignedFlag = row.status_id == 1;
            string prevAssignee = "";
            if (!unassignedFlag)
            {
                if (userId == row.assignee_id)
                {
                    var modl = new ExamHelpModel(row);
                    SetViewBagFlags(modl);
                    return PartialView("ViewTicket", modl);
                }

                prevAssignee = row.account1?.name;
            }

            row.status_id = 2;
            row.assignee_id = userId;
            DB11.Entry(row).State = EntityState.Modified;
            var user = DB11.account.Find(userId);
            var comment = unassignedFlag
                ? $"Assigned {user.name} to this ticket."
                : $"Changed Assignee from {prevAssignee} to {user.name}.";
            DB11.eh_history.Add(LogHistory(comment, ticketId));
            DB11.SaveChanges();
            ExamHelpModel model;
            using (var db = new cims11Entities())
            {
                model = new ExamHelpModel(db.eh_ticket.Find(ticketId));
            }

            SetViewBagFlags(model);
            return PartialView("ViewTicket", model);
        }

        public ActionResult Create(int? examId = null)
        {
            if (examId.HasValue)
            {
                var exam = DB11.exam.Find(examId);
                return PartialView(new ExamHelpModel() {ExamId = exam.exam_id, ExamNumber = exam.exam_number});
            }

            return PartialView(new ExamHelpModel());
        }

        public ActionResult EditTicket(int id)
        {
            var model = new ExamHelpModel(DB11.eh_ticket.Find(id));
            model.ExistingFiles = DB11.eh_attachments.Where(z => z.ticket_id == id).ToList()
                .Select(z => new FileMetadata(z)).ToList();
            return PartialView("Create", model);
        }

        public ActionResult EvidenceTree(int id, int examId)
        {
            ViewBag.ExamId = examId;
            if (id == 0)
            {
                return PartialView();
            }

            var model = new ExamHelpModel(DB11.eh_ticket.Find(id));
            return PartialView(model);
        }

        public ActionResult SaveTicket(ExamHelpModel model)
        {
            var files = Enumerable.Empty<HttpPostedFileBase>();
            var bag = new List<HttpPostedFileBase>();
            for (int i = 0; i < Request.Files.Count; i++)
            {
                bag.Add(Request.Files[i]);
            }

            if (bag.Any()) files = bag;
            if (model.Id == 0)
            {
                var row = model.Create(CurrentUserModel.AccountID, files);
                DB11.eh_ticket.Add(row);
                DB11.SaveChanges();
                var typeString = DB11.eh_ticket_type.Find(model.TicketType).ticket_type;
                var message =
                    $"{CurrentUserModel.Name} has created a new {typeString} ticket titled '{model.Title}'<br /><br /><a href='/#/ExamHelp/ViewTicket/{row.id}' target='_blank'>View the ticket here.</a>";
                var messageRow = new message()
                {
                    composer_id = CurrentUserModel.AccountID,
                    date = DateTime.Now,
                    message1 = message,
                    message_recipients = DB11.eh_ticket_type_members.Where(x => x.ticket_type_id == model.TicketType)
                        .ToList().Select(x => new message_recipients()
                        {
                            recipient_id = x.account_id, is_read = false
                        }).ToList()
                };
                DB11.message.Add(messageRow);
                DB11.SaveChanges();
                new Thread(() => SolrExamHelpTicket.Index(new List<string>()
                    {ClosedText, ReopenText, WorkingText, OpenText}, row.id)).Start();

                return Content(row.id.ToString());
            }
            else
            {
                var row = DB11.eh_ticket.Find(model.Id);
                model.Update(row, CurrentUserModel.AccountID, files, DB11);
                DB11.Entry(row).State = EntityState.Modified;
                DB11.SaveChanges();
                new Thread(() => SolrExamHelpTicket.Index(new List<string>()
                    {ClosedText, ReopenText, WorkingText, OpenText}, row.id)).Start();
                return Content(model.Id.ToString());
            }
        }

        public ActionResult SaveComment(TicketComment model)
        {
            if (!string.IsNullOrEmpty(model.Comment))
            {
                var history = LogHistory(model.Comment, model.TicketId);
                DB11.eh_history.Add(history);
                DB11.SaveChanges();
                using (var db = new cims11Entities())
                {
                    var ticketModel = new ExamHelpModel(db.eh_ticket.Find(model.TicketId));
                    SetViewBagFlags(ticketModel);
                    new Thread(() => SolrExamHelpTicket.Index(new List<string>()
                        {ClosedText, ReopenText, WorkingText, OpenText}, model.TicketId)).Start();
                    return PartialView("ViewTicket", ticketModel);
                }
            }
            else
            {
                throw new Exception("Comment is empty.");
            }
        }

        public ActionResult StartTicket(int ticketId)
        {
            var ticket = DB11.eh_ticket.Find(ticketId);
            ticket.status_id = 3;
            DB11.Entry(ticket).State = EntityState.Modified;
            var history = LogHistory(WorkingText, ticketId);
            DB11.eh_history.Add(history);
            DB11.SaveChanges();
            using (var db = new cims11Entities())
            {
                var model = new ExamHelpModel(db.eh_ticket.Find(ticketId));
                SetViewBagFlags(model);
                return PartialView("ViewTicket", model);
            }
        }

        public ActionResult CloseTicket(int ticketId)
        {
            var ticket = DB11.eh_ticket.Find(ticketId);
            ticket.status_id = 4;
            DB11.Entry(ticket).State = EntityState.Modified;
            var history = LogHistory(ClosedText, ticketId);
            DB11.eh_history.Add(history);
            DB11.SaveChanges();
            using (var db = new cims11Entities())
            {
                var model = new ExamHelpModel(db.eh_ticket.Find(ticketId));
                SetViewBagFlags(model);
                return PartialView("ViewTicket", model);
            }
        }

        public ActionResult ReopenTicket(int ticketId)
        {
            var ticket = DB11.eh_ticket.Find(ticketId);
            ticket.status_id = 1;
            DB11.Entry(ticket).State = EntityState.Modified;
            var history = LogHistory(ReopenText, ticketId);
            DB11.eh_history.Add(history);
            DB11.SaveChanges();
            using (var db = new cims11Entities())
            {
                var model = new ExamHelpModel(db.eh_ticket.Find(ticketId));
                SetViewBagFlags(model);
                return PartialView("ViewTicket", model);
            }
        }

        public void InlineRender(int id)
        {
            var row = DB11.eh_attachments.Find(id);
            if (row.mime_type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
                row.mime_type == "application/msword")
            {
                var file = row.file_name;
                var extension = file.Split('.').Last();
                var index = file.LastIndexOf(extension);
                file = file.Remove(index, extension.Length).Insert(index, "pdf");
                var doc = new Aspose.Words.Document(row.saved_path);
                doc.Save(System.Web.HttpContext.Current.Response, file,
                    Aspose.Words.ContentDisposition.Inline,
                    Aspose.Words.Saving.SaveOptions.CreateSaveOptions(SaveFormat.Pdf));
                return;
            }

            Response.ContentType = row.mime_type;
            Response.AddHeader("Content-disposition", $"inline; filename=\"{row.file_name}\"");
            var bytes = System.IO.File.ReadAllBytes(row.saved_path);
            if (!string.IsNullOrEmpty(Request.Headers.Get("Range")))
            {
                var range = Request.Headers.Get("Range");
                var start = int.Parse(range.Remove(0, 6).Split('-')[0]);
                Response.StatusCode = 206;
                Response.AddHeader("Content-Range", $"bytes {start}-{bytes.Length - 1}/{bytes.Length}");
                Response.AddHeader("Accept-Ranges", "bytes");
                bytes = bytes.Skip(start).ToArray();
            }

            Response.BinaryWrite(bytes);
        }

        private void SetViewBagFlags(ExamHelpModel model)
        {
            ViewBag.IsRequester = CurrentUserModel.AccountID == model.RequesterId;
            ViewBag.IsAdmin = CurrentUserModel.Permissions.Any(z =>
                z.Role == Role.Admin && (z.Group == Group.Counterintelligence || z.Group == Group.MajorCrimes ||
                                         z.Group == Group.ImagingExtraction));
            ViewBag.IsAssignee = CurrentUserModel.AccountID == model.AssigneeId;
            ViewBag.IsSubscribed =
                DB11.eh_user_subscriptions.Any(x =>
                    x.account_id == CurrentUserModel.AccountID && x.ticket_id == model.Id);
            ViewBag.IsGroupMember = DB11.eh_ticket_type_members.Any(x =>
                x.account_id == CurrentUserModel.AccountID && x.ticket_type_id == model.TicketType);
        }

        private eh_history LogHistory(string comment, int ticketId, bool addMessage = true)
        {
            var now = DateTime.Now;
            var history = new eh_history
            {
                comment = comment,
                date_updated = now,
                ticket_id = ticketId,
                updated_by = CurrentUserModel.AccountID
            };
            if (addMessage)
            {
                var ticket = DB11.eh_ticket.Find(ticketId);
                var messageText =
                    $"Ticket {ticket.title} has been updated by {CurrentUserModel.Name} with the following comment<br /><br />{comment}<br /><br /><a href='/#/ExamHelp/ViewTicket/{ticketId}' target='_blank'>View the updated ticket here.</a>";
                var message = DB11.message.Add(new message()
                {
                    composer_id = CurrentUserModel.AccountID,
                    date = now,
                    message1 = messageText,
                    message_recipients = new List<message_recipients>()
                });
                foreach (var followers in ticket.eh_user_subscriptions.Where(x =>
                    x.account_id != CurrentUserModel.AccountID))
                {
                    message.message_recipients.Add(new message_recipients()
                        {recipient_id = followers.account_id, is_read = false});
                }

                if (ticket.reporter_id != CurrentUserModel.AccountID)
                {
                    message.message_recipients.Add(new message_recipients()
                        {recipient_id = ticket.reporter_id, is_read = false});
                }

                if (ticket.assignee_id.HasValue && CurrentUserModel.AccountID != ticket.assignee_id)
                {
                    message.message_recipients.Add(new message_recipients()
                        {recipient_id = (int) ticket.assignee_id, is_read = false});
                }

                DB11.message.Add(message);
            }

            return history;
        }

        public ActionResult ReadMessage(int id)
        {
            var message = DB11.message_recipients.Find(id);
            message.is_read = true;
            DB11.Entry(message).State = EntityState.Modified;
            DB11.SaveChanges();
            return new EmptyResult();
        }

        public ActionResult MultiReadMessage(IEnumerable<int> ids)
        {
            foreach (var id in ids ?? new int[] { })
            {
                var message = DB11.message_recipients.Find(id);
                if (!message.is_read)
                {
                    message.is_read = true;
                    DB11.Entry(message).State = EntityState.Modified;
                }
            }

            DB11.SaveChanges();
            return new EmptyResult();
        }
        
        public ActionResult Search()
        {
            ViewBag.TicketTypes = DB11.eh_ticket_type.OrderBy(x => x.ticket_type).ToList().Select(x => x.ticket_type);
            ViewBag.Requesters = DB11.eh_ticket.GroupBy(x => x.reporter_id).SelectMany(x => x.Select(z => z.account.name)).Distinct().ToList();
            ViewBag.Assignees = DB11.eh_ticket.GroupBy(x => x.assignee_id).SelectMany(x => x.Select(z => z.account1.name)).Distinct().ToList();
            return PartialView(new ExamHelpSearchModel());
        }
        
        public ActionResult SearchQuery(ExamHelpSearchModel searchCriteria)
        {
            ViewBag.PageSize = 12;
            return PartialView("Results", SolrExamHelpTicket.Search(searchCriteria));
        }

        public ActionResult ReIndex()
        {
            SolrExamHelpTicket.Index(new List<string>()
                {ClosedText, ReopenText, WorkingText, OpenText});
            return new EmptyResult();
        }
    }
}