using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Web.Mvc;
using CIMSData.Database;
using CIMSWeb.Models;
using CIMSWeb.Workflow;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;

namespace CIMSWeb.Controllers
{
    public class ConfigurationManagementController : CIMSController
    {
        // GET
        public ActionResult Index()
        {
            ViewBag.IsAdmin = CurrentUserModel.Permissions.Any(z =>
                z.Role == Role.Admin && (z.Group == Group.Counterintelligence || z.Group == Group.ImagingExtraction ||
                                         z.Group == Group.MajorCrimes));
            return PartialView();
        }

        public ActionResult CMHistory()
        {
            return PartialView();
        }

        public ActionResult CMHistoryGridData([DataSourceRequest] DataSourceRequest request)
        {
            var models = DB11.cm_history.Include(z => z.account).Include(z => z.cm_inventory).Include(z => z.exam)
                .Include(z => z.configuration_item).OrderByDescending(z => z.date)
                .ToList().Select(z => new CMHistoryMiniViewModel(z));
            return KendoGridResult(models.AsQueryable(), request);
        }


        public ActionResult CMHistoryForItem(int id)
        {
            var models = DB11.cm_history.Where(z => z.inventory_id == id).OrderByDescending(z => z.date).ToList()
                .Select(z => new CMHistoryViewModel(z));
            return Json(models, JsonRequestBehavior.AllowGet);
        }

        public ActionResult CMHistoryForExam([DataSourceRequest] DataSourceRequest request, int id)
        {
            var models = DB11.cm_history.Where(z => z.exam_id == id).OrderByDescending(z => z.date).ToList()
                .Select(z => new CMHistoryViewModel(z));
            return KendoGridResult(models.AsQueryable(), request);
        }

        public ActionResult CMInventoryGridData([DataSourceRequest] DataSourceRequest request)
        {
            var models = DB11.cm_inventory.Include(z => z.cm_history).ToList().OrderByDescending(z =>
                    z.cm_history.OrderByDescending(x => x.date).FirstOrDefault()?.date).ToList()
                .Select(z => new ConfigurationManagementInventory(z, DB11));
            return KendoGridResult(models.AsQueryable(), request);
        }

        public ActionResult CreateInventoryItem([DataSourceRequest] DataSourceRequest request,
            ConfigurationManagementInventory model)
        {
            var ci = model.Create(DB11, false, CurrentUserModel.AccountID);
            DB11.cm_inventory.Add(ci);
            DB11.SaveChanges();
            model.Id = ci.id;
            model.Section = DB11.group.Find(ci.group_id).name;
            model.InventoryType = (CMInventoryTypes) ci.cm_inventory_type_id;
            return Json(new[] {model}.ToDataSourceResult(request, ModelState));
        }

        public ActionResult UpdateInventoryItem([DataSourceRequest] DataSourceRequest request,
            ConfigurationManagementInventory model)
        {
            var ci = DB11.cm_inventory.First(z => z.id == model.Id);
            model.Update(ci, DB11, CurrentUserModel.AccountID);
            DB11.Entry(ci).State = System.Data.Entity.EntityState.Modified;
            DB11.SaveChanges();
            model.Section = DB11.group.Find(ci.group_id).name;
            model.InventoryType = (CMInventoryTypes) ci.cm_inventory_type_id;
            return Json(new[] {model}.ToDataSourceResult(request, ModelState));
        }

        public ActionResult SetInventoryStatus()
        {
            foreach (var retiredItem in DB11.cm_history.Where(z => z.history_type_id == (int) CMHistoryTypes.Retired)
                .Select(z => z.inventory_id).Distinct().ToList())
            {
                var item = DB11.cm_inventory.Find(retiredItem);
                item.status_id = (int) CMStatusTypes.Retired;
                DB11.Entry(item).State = System.Data.Entity.EntityState.Modified;
            }

            DB11.SaveChanges();
            return new EmptyResult();
        }

        public ActionResult CreatePOSTRecord(POSTResultsViewModel model)
        {
            if (model.Id == 0)
            {
                var row = model.Create(DB11);
                DB11.cm_history.Add(row);
                if (!model.Success && model.VDE == false)
                {
                    var item = DB11.cm_inventory.Find(model.CMInventoryId);
                    item.status_id = (int) CMStatusTypes.In_Maintenance;
                    DB11.Entry(item).State = EntityState.Modified;
                }
            }
            else
            {
                var updateRow = DB11.cm_history.Find(model.Id);
                model.Update(updateRow);
                if (model.isIE)
                {
                    model.UpdateEvidence(updateRow, DB11);
                }
                DB11.Entry(updateRow).State = EntityState.Modified;
            }
            DB11.SaveChanges();
            return Json(model.Success, JsonRequestBehavior.AllowGet);
        }

        public ActionResult IsDistinctAssetTag(int id, string assetTag)
        {
            var tag = DB11.cm_inventory.Where(z => z.asset_tag == assetTag).ToList();
            if (tag.Count == 0)
            {
                return Json(true, JsonRequestBehavior.AllowGet);
            }

            if (id > 0 && tag.Count == 1 && tag.First().id == id)
            {
                return Json(true, JsonRequestBehavior.AllowGet);
            }

            return Json("This Asset Tag is already in use.", JsonRequestBehavior.AllowGet);
        }
    }
}