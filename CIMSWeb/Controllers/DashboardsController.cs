﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Web.Mvc;
using CIMSData.Database;
using CIMSWeb.Models;
using CIMSWeb.Workflow;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.Ajax.Utilities;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using SolrNet.Utils;

namespace CIMSWeb.Controllers
{
    public class DashboardsController : CIMSController
    {
        public ActionResult Lab()
        {
            return PartialView();
        }

        public ActionResult Unclassified()
        {
            return PartialView();
        }

        public ActionResult Classified()
        {
            return PartialView();
        }

        public ActionResult Section(int id)
        {
            if (id == (int)Group.Intrusions)
            {
                var defaultTab = DB11.user_config_value.SingleOrDefault(x =>
                    x.user_id == CurrentUserModel.AccountID &&
                    x.config_type_id == (int)UserConfigTypes.DefaultTab);
                int parsedDefaultTab = 0;
                if (defaultTab != null && int.TryParse(defaultTab.config_value, out parsedDefaultTab))
                {
                    ViewBag.TabIndex = parsedDefaultTab;
                }
                else
                {
                    ViewBag.TabIndex = 0;
                }
            }
            return PartialView(DB11.group.FirstOrDefault(x => x.group_id == id));
        }

        public ActionResult PreExams()
        {
            return PartialView();
        }

        public ActionResult PreExamsActive()
        {
            ViewBag.ActionName = "PreExamsActiveGridData";
            ViewBag.AccessibleName = "Active Pre Exams Table";
            ViewBag.Closed = false;
            return PartialView("_SectionExams");
        }

        public ActionResult PreExamsRejected()
        {
            ViewBag.ActionName = "PreExamsRejectedGridData";
            ViewBag.AccessibleName = "Rejected Pre Exams Table";
            ViewBag.Closed = true;
            return PartialView("_SectionExams");
        }

        public ActionResult PreExamsAll()
        {
            ViewBag.ActionName = "PreExamsAllGridData";
            ViewBag.AccessibleName = "All Pre Exams Table";
            ViewBag.Closed = true;
            ViewBag.All = true;
            return PartialView("_SectionExams");
        }

        public ActionResult PreExamsActiveGridData([DataSourceRequest] DataSourceRequest request)
        {
            var query = DB11.exam.OpenExams().Where(x => x.exam_type_id == 4).IncludeEverythingForExamDashboardViewModel()
                .ToList();
            var results = query.Select(x => new DashboardExamViewModel(x));
            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult PreExamsRejectedGridData([DataSourceRequest] DataSourceRequest request)
        {
            var query = DB11.exam.Where(x => x.exam_type_id == 4 && (x.exam_status_id == 3 || x.exam_status_id == 5)).IncludeEverythingForExamDashboardViewModel()
                .ToList();
            var results = query.Select(x => new DashboardExamViewModel(x));
            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult PreExamsAllGridData([DataSourceRequest] DataSourceRequest request)
        {
            var query = DB11.exam_activity.Where(x => x.activity_id == (int)ActivityType._Pre_Exam_Coordination)
                .Select(x => x.exam).Distinct().IncludeEverythingForExamDashboardViewModel().ToList();
            var results = query.Select(x => new DashboardExamViewModel(x));
            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult User(int? id = null)
        {
            if (id == null) id = CurrentUserModel.AccountID;
            var model = new UserDashboardViewModel(DB11, (int)id)
            {
                CurrentUser = id == CurrentUserModel.AccountID
            };
            return PartialView(model);
        }

        public ActionResult SelectedUser(string id)
        {
            int userID = DB11.account.Where(ac => ac.name.ToLower() == id.ToLower()).Select(ac => ac.account_id)
                .FirstOrDefault();

            if (userID > 0)
                User(userID);
            return new EmptyResult();
        }

        public ActionResult OpenExamsBySection()
        {
            var openExams = DB11.exam.OpenExams()
                .GroupBy(x => x.group.name)
                .ToList()
                .Select(x => new { Section = x.Key, Count = x.Count(), Color = GetRandomColor() }).ToList();
            return Json(openExams, JsonRequestBehavior.AllowGet);
        }

        public ActionResult OpenExamsByType()
        {
            var openExams = DB11.exam.OpenExams().Where(x => x.exam_category_id.HasValue)
                .GroupBy(x => new { Cat = x.exam_category.exam_category1, Kitten = x.exam_category.exam_subcategory })
                .ToList()
                .Select(x => new
                {
                    ExamType =
                        (x.Key.Cat == x.Key.Kitten ? x.Key.Kitten : x.Key.Cat + " / " + x.Key.Kitten).TitleCase(),
                    Count = x.Count()
                }).ToList();
            return Json(openExams, JsonRequestBehavior.AllowGet);
        }

        public ActionResult OpenExamsOverTime(string startString, string endString)
        {
            var start = DateTime.Today.AddMonths(-1);
            var end = DateTime.Today;
            if (!string.IsNullOrEmpty(startString)) start = DateTime.Parse(startString);
            if (!string.IsNullOrEmpty(endString)) end = DateTime.Parse(endString);
            List<OpenExamDataPoint> openExams = new List<OpenExamDataPoint>();
            for (DateTime date = start; date < end; date = date.AddDays(1))
            {
                var examsOnDay = DB11.exam.Where(x =>
                    x.exam_status_id != 3 && x.date_opened <= date &&
                    (x.date_closed >= date || x.date_closed == null) &&
                    !(x.exam_status_id == 5 && x.date_closed == null));
                int count = examsOnDay.Count();
                openExams.Add(new OpenExamDataPoint()
                {
                    Date = date.ToShortDateString(),
                    TotalExams = count
                });
            }

            return Json(openExams, JsonRequestBehavior.AllowGet);
        }

        public ActionResult OpenCasesByAgency()
        {
            var cases = DB11.exam
                .OpenExams()
                .Select(x => new { CaseNumber = x.@case.case_number, Agency = x.@case.agency_org.agency.name })
                .Distinct()
                .GroupBy(x => x.Agency)
                .ToList()
                .Select(x => new { Agency = x.Key, Count = x.Count(), Color = GetRandomColor() }).ToList();
            return Json(cases, JsonRequestBehavior.AllowGet);
        }

        public ActionResult RecentlyOpenedExams([DataSourceRequest] DataSourceRequest request)
        {
            var exams = DB11.exam.OrderByDescending(x => x.date_opened).Take(10).ToList()
                .Select(x => new ExamViewModel(x));
            return KendoGridResult(exams.AsQueryable(), request);
        }

        public ActionResult RecentlyClosedExams([DataSourceRequest] DataSourceRequest request)
        {
            var exams = DB11.exam.OrderByDescending(x => x.date_closed).Take(10).ToList()
                .Select(x => new ExamViewModel(x));
            return KendoGridResult(exams.AsQueryable(), request);
        }

        public ActionResult OpenExams([DataSourceRequest] DataSourceRequest request, int group)
        {
            var exams = DB11.exam.OpenExams()
                .OrderByDescending(x => x.date_opened).Where(x => x.group_id == group).ToList()
                .Select(x => new ExamViewModel(x));
            return KendoGridResult(exams.AsQueryable(), request);
        }

        public ActionResult OpenExamCountSection(int group)
        {
            var exams = DB11.exam.OpenExams()
                .OrderByDescending(x => x.date_opened).Where(x => x.group_id == group).Count();
            return Json(exams, JsonRequestBehavior.AllowGet);
        }

        public ActionResult OpenExamCount()
        {
            var exams = DB11.exam.OpenExams().Count();
            return Json(exams, JsonRequestBehavior.AllowGet);
        }

        public ActionResult RecentlyClosedExamsGroup([DataSourceRequest] DataSourceRequest request, int group)
        {
            var exams = DB11.exam.ClosedExams().OrderByDescending(x => x.date_opened).Where(x => x.group_id == group)
                .ToList().Select(x => new ExamViewModel(x));
            return KendoGridResult(exams.AsQueryable(), request);
        }

        private int CountOpenExams(int accountID)
        {
            return DB11.exam_activity.OpenExams().ActionableBy(accountID, DB11).Select(x => x.exam_id).Distinct()
                .Count();
        }

        private int CountOpenExamsWithOpenActivities(int accountID)
        {
            return DB11.exam_activity.OpenExams()
                .ActionableBy(accountID, DB11)
                .Where(x => x.date_start.HasValue && !x.date_end.HasValue)
                .Select(x => x.exam_id)
                .Distinct()
                .Count();
        }

        public ActionResult SectionTeamGridData([DataSourceRequest] DataSourceRequest request, int sectionID)
        {
            var teamMembers = DB11.account_permission.Where(x => x.account.enabled && x.group_id == sectionID)
                .OrderBy(x => x.account.name).ToList()
                .Select(x => new TeamMemberViewModel
                {
                    AccountId = x.account_id,
                    Name = x.account.name.FormatName(),
                    OpenExamCount = DB11.exam_activity.OpenExams().Where(z => z.date_start.HasValue && !z.date_end.HasValue && z.account_id == x.account_id).Select(z => z.exam_id).Distinct().Count(),
                    //OpenExamWithOpenActivityCount = CountOpenExamsWithOpenActivities(x.account_id),
                    TotalStorageSize = TotalEvidenceSize(x.account_id)
                });
            return KendoGridResult(teamMembers.AsQueryable(), request);
        }

        private double? TotalEvidenceSize(int id)
        {
            int[] examIDs = DB11.exam_activity.Where(x => x.account_id == id &&
                                                          (x.activity_id ==
                                                           (int)ActivityType._Perform_Forensic_Examination ||
                                                           x.activity_id == 63) &&
                                                          x.date_end == null)
                .Select(x => x.exam_id).Distinct().ToArray();
            var allEvidence = DB11.exam.Where(x => examIDs.Contains(x.exam_id))
                .SelectMany(z => z.exam_evidence.Select(x => x.evidence));
            double? total = 0;
            if (allEvidence.Any())
            {
                total = allEvidence.Sum(x => x.storage_size);
            }

            return total;
        }

        /// <summary>
        /// Returns exams that are active and assigned to this user's id
        /// OR are active, not yet assigned to a user, and this user has sufficient permissions to complete
        /// </summary>
        /// <param name="request"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public ActionResult UserCurrentExams([DataSourceRequest] DataSourceRequest request, int id)
        {
            var perms = DB11.account_permission.Where(x => x.account_id == id);
            if (!perms.Any()) return new EmptyResult();
            var exams = DB11.exam_activity.IncludeExamAndCaseItems()
                .OpenExams()
                .ActionableBy(id, DB11)
                .OrderByDescending(x => x.exam.date_opened).DistinctBy(x => x.exam_id).ToList()
                .Select(x => new DashboardExamsViewModel(x.exam, id, true))
                .ToList();
            return KendoGridResult(exams.AsQueryable(), request);
        }

        public ActionResult UserAssignedActivities([DataSourceRequest] DataSourceRequest request, int id)
        {
            var perms = DB11.account_permission.Where(x => x.account_id == id);
            if (!perms.Any()) return new EmptyResult();
            var exams = DB11.exam_activity.IncludeExamAndCaseItems()
                .OpenExams().Where(x => x.date_start.HasValue && !x.date_end.HasValue && x.account_id == id)
                .OrderByDescending(x => x.exam.date_opened).DistinctBy(x => x.exam_id).Select(x => x.exam).ToList()
                .Select(x => new ExamViewModel(x))
                .ToList();
            foreach (var e in exams)
            {
                e.ForAccount(id, DB11);
            }
            return KendoGridResult(exams.AsQueryable(), request);
        }
        /// <summary>
        /// Returns exams that this user had an activity on and has been completed.
        /// </summary>
        /// <param name="request"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public ActionResult UserClosedExams([DataSourceRequest] DataSourceRequest request, int id)
        {
            var exams = DB11.exam_activity.IncludeExamAndCaseItems().ClosedExams().Where(x => x.account_id == id)
                .OrderByDescending(x => x.exam.date_closed).DistinctBy(x => x.exam_id).ToList()
                .Select(x => new DashboardExamsViewModel(x.exam, id, false)).ToList();
            return KendoGridResult(exams.AsQueryable(), request);
        }

        public JsonResult UserCurrentExamsDropdown()
        {
            var perms = DB11.account_permission.Where(x => x.account_id == CurrentUserModel.AccountID);
            return Json(DB11.exam_activity.OpenExams()
                    .Where(x => x.account_id == CurrentUserModel.AccountID ||
                                x.account_id == null && perms.Any(p =>
                                    p.account_role_id == x.account_role_id && p.group_id == x.group_id))
                    .OrderByDescending(x => x.exam.date_opened).DistinctBy(x => x.exam_id)
                    .Select(x => new { Text = x.exam.exam_number, Value = x.exam_id }).ToList(),
                JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// Returns timecodes by userid and examid
        /// </summary>
        /// <param name="ExamId"></param>
        /// <returns></returns>
        public JsonResult TimeCodes(int ExamId)
        {
            var currentUserID = CurrentUserModel.AccountID;
            var timeCodes = GetTimeCodes(ExamId, currentUserID);

            return Json(timeCodes, JsonRequestBehavior.AllowGet);
        }

        public ActionResult TimesheetCreate(TimesheetViewModel model)
        {
            var c = model.Create();
            DB11.account_activity.Add(c);
            DB11.SaveChanges();
            AuditInsert(c);
            DB11.SaveChanges();
            return new EmptyResult();
        }

        public ActionResult DeleteTimeEntry(int timeEntryId)
        {
            DB11.account_activity.Remove(DB11.account_activity.First(x => x.account_activity_id == timeEntryId));
            DB11.SaveChanges();
            return new EmptyResult();
        }

        public ActionResult UserExamActivitiesPerformedMetrics([DataSourceRequest] DataSourceRequest request, int id,
            string startString, string endString)
        {
            var start = DateTime.Today.AddYears(-1);
            var end = DateTime.Today;
            if (!string.IsNullOrEmpty(startString)) start = DateTime.Parse(startString);
            if (!string.IsNullOrEmpty(endString)) end = DateTime.Parse(endString);

            //--Total Exams Supported
            var total_exams_supported = DB11.exam_activity
                .Where(x => x.account_id == id && x.exam.exam_status_id != (int)ExamStatus.Rejected &&
                            x.date_start >= start && x.date_end <= end).AsEnumerable()
                .DistinctBy(x => x.exam_id).Count();

            //-- Total Acquisitions Performed
            var total_acquisitions = DB11.exam_activity.Count(x =>
                x.account_id == id &&
                x.activity_id == (int)ActivityType._Perform_Forensic_Acquisition &&
                x.exam.exam_status_id != (int)ExamStatus.Rejected &&
                x.date_end != null &&
                x.skipped != true &&
                x.date_start >= start &&
                x.date_end <= end);

            //-- Total Examinations Performed
            var total_examinations = DB11.exam_activity.Count(x =>
                x.account_id == id &&
                x.activity_id == (int)ActivityType._Perform_Forensic_Examination &&
                x.exam.exam_status_id != (int)ExamStatus.Rejected &&
                x.date_end != null &&
                x.skipped != true &&
                x.date_start >= start &&
                x.date_end <= end);

            //-- Total Tech Reviews Performed
            var total_tech_reviews = DB11.exam_activity.Count(x =>
                x.account_id == id &&
                x.activity_id == (int)ActivityType._Perform_Acquisition_Review &&
                x.exam.exam_status_id != (int)ExamStatus.Rejected &&
                x.date_end != null &&
                x.skipped != true &&
                x.date_start >= start &&
                x.date_end <= end);

            //-- Total Trial Preps Performed
            var total_trial_preps = DB11.exam_activity.Count(x =>
                x.account_id == id &&
                x.activity_id == (int)ActivityType._Trial_Preparation &&
                x.exam.exam_status_id != (int)ExamStatus.Rejected &&
                x.date_end != null &&
                x.skipped != true &&
                x.date_start >= start &&
                x.date_end <= end);

            var results = new List<UserExamActivitiesPerformedMetrics>
            {
                new UserExamActivitiesPerformedMetrics("Exams Supported", total_exams_supported),
                new UserExamActivitiesPerformedMetrics("Acquistions Completed", total_acquisitions),
                new UserExamActivitiesPerformedMetrics("Examinations Completed", total_examinations),
                new UserExamActivitiesPerformedMetrics("Tech Reviews Completed", total_tech_reviews),
                new UserExamActivitiesPerformedMetrics("Trial Preps Completed", total_trial_preps)
            }.AsQueryable();

            return KendoGridResult(results, request);
        }

        public ActionResult UserEvidenceItemMetrics([DataSourceRequest] DataSourceRequest request, int id,
            string startString, string endString)
        {
            var start = DateTime.Today.AddYears(-1);
            var end = DateTime.Today;
            if (!string.IsNullOrEmpty(startString)) start = DateTime.Parse(startString);
            if (!string.IsNullOrEmpty(endString)) end = DateTime.Parse(endString);

            //-- Evidence Items Types and Count
            var evidence_item_groups = (from ex in DB11.exam_evidence
                                        join evhist in DB11.evidence_history on ex.evidence_id equals evhist.evidence_id
                                        where ex.exam.exam_status_id != (int)ExamStatus.Rejected && evhist.entered_by == id &&
                                              evhist.date_logged >= start && evhist.date_logged <= end
                                        group evhist by evhist.evidence.evidence_type.evidence_type1
                into g
                                        select new { g.Key, Count = g.Count() }).ToList();

            //--Total Evidence Items Handled 
            var total_ev_items_handled = 0;

            //Total TBs acquired
            var total_tbs_acquired = 0.0;
            var results = new List<UserEvidenceItemMetrics>();
            foreach (var ev_type in evidence_item_groups)
            {
                var ev_items_storage_size_list = (from ex in DB11.exam_evidence
                                                  join evhist in DB11.evidence_history on ex.evidence_id equals evhist.evidence_id
                                                  where ex.exam.exam_status_id != (int)ExamStatus.Rejected && evhist.entered_by == id &&
                                                        evhist.evidence.evidence_type.evidence_type1 == ev_type.Key &&
                                                        evhist.date_logged >= start && evhist.date_logged <= end
                                                  select evhist.evidence.storage_size).ToList();
                double? total_tbs_for_item_type = 0;
                foreach (var size in ev_items_storage_size_list)
                {
                    total_tbs_for_item_type += size;
                }

                var tb = total_tbs_for_item_type / 1000;
                if (tb != null) total_tbs_acquired += (double)tb;
                if (tb == null) tb = 0.0;
                total_ev_items_handled += ev_type.Count;
                results.Add(new UserEvidenceItemMetrics(ev_type.Key, ev_type.Count, Math.Round((double)tb, 2)));
            }

            results.Insert(0,
                new UserEvidenceItemMetrics("All", total_ev_items_handled, Math.Round((double)total_tbs_acquired, 2)));

            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult ClosedUserExamsByCategory(int Id, string startString, string endString)
        {
            var start = DateTime.Today.AddYears(-1);
            var end = DateTime.Today;
            if (!string.IsNullOrEmpty(startString)) start = DateTime.Parse(startString);
            if (!string.IsNullOrEmpty(endString)) end = DateTime.Parse(endString);
            var exams = DB11.exam_activity.Where(x =>
                    x.account_id == Id && x.exam.exam_status_id == (int)ExamStatus.Done && x.date_end != null &&
                    x.date_start != null
                    && x.date_end >= start && x.date_end <= end)
                .Select(x => x.exam).Distinct();

            var results = exams
                .GroupBy(x => x.exam_category.exam_category1)
                .ToList()
                .Select(x => new { Category = x.Key, Count = x.Count(), Color = GetRandomColor() }).ToList();
            return Json(results, JsonRequestBehavior.AllowGet);
        }

        public ActionResult ClosedUserExamsByAgency(int Id, string startString, string endString)
        {
            var start = DateTime.Today.AddYears(-1);
            var end = DateTime.Today;
            if (!string.IsNullOrEmpty(startString)) start = DateTime.Parse(startString);
            if (!string.IsNullOrEmpty(endString)) end = DateTime.Parse(endString);
            var exams = DB11.exam_activity.Where(x =>
                    x.account_id == Id && x.exam.exam_status_id == (int)ExamStatus.Done && x.date_end != null &&
                    x.date_start != null
                    && x.date_end >= start && x.date_end <= end)
                .Select(x => x.exam).Distinct();

            var results = exams.Where(x => x.case_id.HasValue && x.@case.agency_org_id.HasValue)
                .GroupBy(x => x.@case.agency_org.agency.name)
                .ToList()
                .Select(x => new { Category = x.Key, Count = x.Count(), Color = GetRandomColor() }).ToList();
            return Json(results, JsonRequestBehavior.AllowGet);
        }

        public ActionResult DeviceTypeByUser(int accountId, string startString, string endString)
        {
            var start = DateTime.Today.AddYears(-1);
            var end = DateTime.Today;
            if (!string.IsNullOrEmpty(startString)) start = DateTime.Parse(startString);
            if (!string.IsNullOrEmpty(endString)) end = DateTime.Parse(endString);
            var deviceTypes = DB11.evidence_history
                .Where(x => x.entered_by == accountId && x.date_logged >= start && x.date_logged <= end).AsEnumerable()
                .DistinctBy(x => x.evidence_id)
                .GroupBy(x => x.evidence.evidence_type.evidence_type1)
                .ToList()
                .Select(x => new { Category = x.Key, Count = x.Count(), Color = GetRandomColor() }).ToList();
            return Json(deviceTypes, JsonRequestBehavior.AllowGet);
        }

        public ActionResult UserCheckedOutHardDrivesGridData([DataSourceRequest] DataSourceRequest request, int id)
        {
            var dbItems = DB11.hard_drive_history.Where(x => x.account.account_id == id).Select(x => x.hard_drive)
                .Distinct();
            List<HardDriveViewModel> models = new List<HardDriveViewModel>();
            foreach (var dbItem in dbItems)
            {
                hard_drive_history mostRecentHistoryItem =
                    dbItem.hard_drive_history.OrderByDescending(x => x.date_logged).First();
                if (mostRecentHistoryItem.hard_drive_status_id == 1)
                    models.Add(new HardDriveViewModel(mostRecentHistoryItem));
            }

            return KendoGridResult(models.OrderBy(x => x.DateLogged).AsQueryable(), request);
        }

        public ActionResult UserCheckedOutEvidenceGridData([DataSourceRequest] DataSourceRequest request, int id)
        {
            var accountName = DB11.account.First(x => x.account_id == id).sam_account_name;
            var evidence = DB11.vw_evidence_log
                .Where(x => x.sam_account_name == accountName && x.evidence_status == "checked out").ToList()
                .Select(x => new EvidenceViewModel(x));
            return KendoGridResult(evidence.AsQueryable(), request);
        }

        /// <summary>
        /// Returns account activity rows for a given account id
        /// </summary>
        /// <param name="request"></param>
        /// <param name="id">account id</param>
        /// <returns></returns>
        public ActionResult UserTimeGridData([DataSourceRequest] DataSourceRequest request, int id)
        {
            var results = DB11.account_activity.Where(x => x.account_id == id)
                .OrderByDescending(x => x.date_worked).ToList().Select(x => new AccountActivityViewModel(x));
            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult UserWeekTimeGridData([DataSourceRequest] DataSourceRequest request, int id)
        {
            var start = DateTime.Today.StartOfWeek(DayOfWeek.Sunday);
            var end = start.AddDays(7);
            var results = DB11.account_activity
                .Where(x => x.account_id == id && x.date_worked >= start && x.date_worked < end)
                .OrderBy(x => x.date_worked).ToList().Select(x => new AccountActivityViewModel(x));
            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult UserPermissionsGridData([DataSourceRequest] DataSourceRequest request, int id)
        {
            var results = DB11.account_permission.Where(x => x.account_id == id).ToList()
                .Select(x => new DashboardPermissionsViewModel(x));
            return KendoGridResult(results.AsQueryable(), request);
        }

        /// <summary>
        /// Returns the total hours worked in the current work week
        /// for a given account id in json.
        /// </summary>
        /// <param name="id">account id</param>
        /// <returns></returns>
        public JsonResult WeeklyHours(int id)
        {
            double currentWeekHours = 0;
            var start = DateTime.Today.StartOfWeek(DayOfWeek.Sunday);
            var currentWeeksTimeEntries =
                DB11.account_activity.Where(x => x.account_id == id && x.date_worked >= start);
            if (currentWeeksTimeEntries.Any())
            {
                currentWeekHours = currentWeeksTimeEntries.Sum(x => x.hours);
            }

            return Json(new { Hours = currentWeekHours }, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// Returns last 5 communication entries for the given user id
        /// </summary>
        /// <param name="request"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public ActionResult RecentCommunicationGridData([DataSourceRequest] DataSourceRequest request, int id)
        {
            var results = DB11.communication.Where(x => x.entered_by == id).OrderBy(x => x.communication_datetime)
                .Take(5).ToList()
                .Select(x => new DashboardsCommViewModel(x));
            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult ExamsInSectionGridData([DataSourceRequest] DataSourceRequest request, int sectionID)
        {
            var results = DB11.exam_activity.OpenExams().Where(x => x.group_id == sectionID &&
                                                                    x.date_start.HasValue &&
                                                                    !x.date_end.HasValue).Select(x => x.exam).Distinct()
                .IncludeEverythingForExamDashboardViewModel()
                .ToList().Select(x => new DashboardExamViewModel(x));
            // Also include Ship_Out_Evidence activities from rejected exams
            results = results.Union(DB11.exam_activity.RejectedExams().Where(x => x.group_id == sectionID &&
                                                                                  x.activity_id ==
                                                                                  (int)ActivityType
                                                                                      ._Ship_Out_Evidence &&
                                                                                  x.date_start.HasValue &&
                                                                                  !x.date_end.HasValue)
                .Select(x => x.exam).Distinct().IncludeEverythingForExamDashboardViewModel()
                .ToList().Select(x => new DashboardExamViewModel(x)));
            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult ExamsOpenEvidenceSectionGridData([DataSourceRequest] DataSourceRequest request,
            int sectionID)
        {
            //var results = DB11.exam_activity.OpenExams().Where(x => x.group_id == sectionID &&
            //                                                   x.date_start.HasValue &&
            //                                                   !x.date_end.HasValue).Select(x => x.exam).Distinct().IncludeAccountItems()
            //                                                   .ToList().Select(x => new ExamViewModel(x));

            var result1 = DB11.exam.Where(z =>
                    z.exam_type_id != (int)ExamType.Intrusions &&
                    (z.exam_status_id == (int)ExamStatus.Active || z.exam_status_id == (int)ExamStatus.Hold))
                .Distinct().IncludeEverythingForExamDashboardViewModel().ToList()
                .Select(x => new DashboardExamViewModel(x));

            var result2 = DB11.exam_intrusion.Where(x => x.is_lab_source == true).Select(x => x.exam).Where(x =>
                    x.exam_status_id == (int)ExamStatus.Active || x.exam_status_id == (int)ExamStatus.Hold)
                .Distinct().IncludeEverythingForExamDashboardViewModel().ToList()
                .Select(x => new DashboardExamViewModel(x));

            var results = result1.Concat(result2).ToList();

            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult ExamsAssignedToSectionGridData([DataSourceRequest] DataSourceRequest request, int sectionID)
        {
            var results = DB11.exam.OpenExams().IncludeEverythingForExamDashboardViewModel()
                .Where(x => x.group_id == sectionID).ToList().Select(x => new DashboardExamViewModel(x));
            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult OpenExamsPassedQaReviewGridData([DataSourceRequest] DataSourceRequest request, int sectionID)
        {
            var results = DB11.exam.OpenExams().IncludeEverythingForExamDashboardViewModel()
                .Where(x => x.exam_type_id != (int)ExamType.Intrusions && x.exam_activity.Any(z => z.activity_id == (int)ActivityType._Perform_QA_Review && z.date_end.HasValue && (z.skipped == null || !z.skipped.Value))).ToList().Select(x => new DashboardExamViewModel(x));
            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult ClosedExamsPassedQaReviewGridData([DataSourceRequest] DataSourceRequest request, int sectionID)
        {
            var results = DB11.exam.ClosedExams().IncludeEverythingForExamDashboardViewModel()
                .Where(x => x.exam_type_id != (int)ExamType.Intrusions && x.exam_activity.Any(z => z.activity_id == (int)ActivityType._Perform_QA_Review && z.date_end.HasValue && (z.skipped == null || !z.skipped.Value))).ToList().Select(x => new DashboardExamViewModel(x));
            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult ClosedExamsAssignedToSectionGridData([DataSourceRequest] DataSourceRequest request,
            int sectionID)
        {
            var results = DB11.exam.ClosedExams().IncludeEverythingForExamDashboardViewModel()
                .Where(x => x.group_id == sectionID).ToList().Select(x => new DashboardExamViewModel(x));
            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult IntrusionsExamGridData([DataSourceRequest] DataSourceRequest request)
        {
            var intrusions = DB11.exam_intrusion.OpenExams().IncludeEverythingForExamDashboardViewModel().ToList()
                .Select(x => new IntrusionsDashboardExamViewModel(x));
            return KendoGridResult(intrusions.AsQueryable(), request);
        }

        public ActionResult IntrusionsMalwareExamGridData([DataSourceRequest] DataSourceRequest request)
        {
            var intrusions = DB11.exam_intrusion.OpenExams()
                .Where(x => x.exam.exam_category.exam_category1 == "Malware")
                .IncludeEverythingForExamDashboardViewModel().ToList()
                .Select(x => new IntrusionsDashboardExamViewModel(x));
            return KendoGridResult(intrusions.AsQueryable(), request);
        }

        public ActionResult IntrusionsNonMalwareExamGridData([DataSourceRequest] DataSourceRequest request)
        {
            var intrusions = DB11.exam_intrusion.OpenExams()
                .Where(x => x.exam.exam_category.exam_category1 != "Malware")
                .IncludeEverythingForExamDashboardViewModel().ToList()
                .Select(x => new IntrusionsDashboardExamViewModel(x));
            return KendoGridResult(intrusions.AsQueryable(), request);
        }

        public ActionResult IntrusionsClosedExamGridData([DataSourceRequest] DataSourceRequest request)
        {
            var intrusions = DB11.exam_intrusion.ClosedExams().IncludeEverythingForExamDashboardViewModel().ToList()
                .Select(x => new IntrusionsDashboardExamViewModel(x));
            return KendoGridResult(intrusions.AsQueryable(), request);
        }

        public ActionResult IntrusionsAllExamGridData([DataSourceRequest] DataSourceRequest request)
        {
            var intrusions = DB11.exam_intrusion.IncludeEverythingForExamDashboardViewModel().ToList()
                .Select(x => new IntrusionsDashboardExamViewModel(x));
            return KendoGridResult(intrusions.AsQueryable(), request);
        }

        public ActionResult LitigationSupportExamGridData([DataSourceRequest] DataSourceRequest request)
        {
            var intrusions = DB11.exam.IncludeEverythingForExamDashboardViewModel().OpenExams()
                .Where(x => x.exam_category_id.HasValue && x.exam_category.exam_category1 == "LITIGATION SUPPORT")
                .ToList().Select(x => new DashboardExamViewModel(x));
            return KendoGridResult(intrusions.AsQueryable(), request);
        }

        public ActionResult LitigationSupportClosedExamGridData([DataSourceRequest] DataSourceRequest request)
        {
            var intrusions = DB11.exam.IncludeEverythingForExamDashboardViewModel().ClosedExams()
                .Where(x => x.exam_category_id.HasValue && x.exam_category.exam_category1 == "LITIGATION SUPPORT")
                .ToList().Select(x => new DashboardExamViewModel(x));
            return KendoGridResult(intrusions.AsQueryable(), request);
        }

        public ActionResult IntakeExamGridData([DataSourceRequest] DataSourceRequest request)
        {
            var exams = DB11.exam.IncludeEverythingForIntakeExamDashboardViewModel().OpenExams()
                .Where(z => z.exam_type_id == 2 || z.exam_type_id == 3)
                .ToList().Select(x => new IntakeExamViewModel(x));
            return KendoGridResult(exams.AsQueryable(), request);
        }

        public ActionResult IntakeClosedExamGridData([DataSourceRequest] DataSourceRequest request)
        {
            var exams = DB11.exam.IncludeEverythingForIntakeExamDashboardViewModel().ClosedExams()
                .Where(z => z.exam_type_id == 2 || z.exam_type_id == 3)
                .ToList().Select(x => new IntakeExamViewModel(x));
            return KendoGridResult(exams.AsQueryable(), request);
        }

        public ActionResult CFLOpenExamGridData([DataSourceRequest] DataSourceRequest request)
        {
            var results = DB11.exam.OpenExams().IncludeEverythingForExamDashboardViewModel()
                .ToList().Select(x => new DashboardExamViewModel(x));
            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult CFLClosedExamGridData([DataSourceRequest] DataSourceRequest request)
        {
            var query = DB11.exam.ClosedExams().IncludeEverythingForExamDashboardViewModel()
                .ToList();
            var results = query.Select(x => new DashboardExamViewModel(x));
            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult CFLAllExamsGridData([DataSourceRequest] DataSourceRequest request)
        {
            var results = DB11.exam.IncludeEverythingForExamDashboardViewModel()
                .ToList().Select(x => new DashboardExamViewModel(x));
            return KendoGridResult(results.AsQueryable(), request);
        }

        [HttpPost]
        public ActionResult ExamByCategoryCountsGridData([DataSourceRequest] DataSourceRequest request, DateTime start,
            DateTime end, bool openedWithin)
        {
            var q = DB11.exam.AsQueryable();
            q = openedWithin
                ? q.Where(x => start <= x.date_opened && x.date_opened <= end)
                : q.Where(x => start <= x.date_closed && x.date_closed <= end);
            var results = q.GroupBy(x => new
            { Category = x.exam_category.exam_category1, Subcategory = x.exam_category.exam_subcategory })
                .ToList()
                .Select(x => new ExamCategoryStatsViewModel()
                {
                    Category = x.Key.Category.TitleCase(),
                    Subcategory = x.Key.Subcategory.TitleCase(),
                    Count = x.Count()
                }).ToList();
            return KendoGridResult(results.AsQueryable(), request);
        }

        [HttpPost]
        public ActionResult EvidenceCountsGridData([DataSourceRequest] DataSourceRequest request, DateTime start,
            DateTime end, bool openedWithin)
        {
            var q = DB11.exam.AsQueryable();
            q = openedWithin
                ? q.Where(x => start <= x.date_opened && x.date_opened <= end)
                : q.Where(x => start <= x.date_closed && x.date_closed <= end);
            var results = q.SelectMany(x => x.exam_evidence.Select(z => z.evidence)).Distinct()
                .GroupBy(x => x.evidence_type.evidence_type1)
                .ToList()
                .Select(x => new EvidenceStatsViewModel() { EvidenceType = x.Key, Count = x.Count() }).ToList();
            return KendoGridResult(results.AsQueryable(), request);
        }

        private IQueryable<exam_activity> OpenActivities(int sectionID)
        {
            return DB11.exam.OpenExamsNotOnHold().Where(z => z.group_id == sectionID).SelectMany(z => z.exam_activity)
                .Where(z => z.date_end == null);
        }

        private IQueryable<exam_activity> OpenIntrusionsActivities()
        {
            return DB11.exam_intrusion.IncludeEverythingForExamDashboardViewModel().OpenExams().SelectMany(x => x.exam.exam_activity).Where(x => x.date_end == null);
        }

        public ActionResult IntrusionsOpenExamsByStatus([DataSourceRequest] DataSourceRequest request)
        {
            var models = new List<ExamsByStateViewModel>();
            var assignReviewer = OpenIntrusionsActivities().Where(x => x.activity_id == (int)ActivityType._Assign_Analysis_Reviewer).Select(x => x.exam).Distinct().ToList();
            models.Add(new ExamsByStateViewModel(assignReviewer, "Assign Analysis Reviewer"));
            var assignExamTeam = OpenIntrusionsActivities().Where(x => x.activity_id == (int)ActivityType._Assign_Examination_Team).Select(x => x.exam).Distinct().ToList();
            models.Add(new ExamsByStateViewModel(assignExamTeam, "Assign Exam Team"));
            var pendingRelease = OpenIntrusionsActivities().Where(x => x.activity_id == (int)ActivityType._Package_Exam_Results || x.activity_id == (int)ActivityType._Release_Exam_Results).Select(x => x.exam).Distinct().ToList();
            models.Add(new ExamsByStateViewModel(pendingRelease, "Pending Release"));
            var fourteenDaysAgo = DateTime.Now.AddDays(-14);
            var greaterFourteen = DB11.exam_intrusion.IncludeEverythingForExamDashboardViewModel().OpenExams().Where(x => x.exam.date_opened.HasValue && x.exam.date_opened.Value < fourteenDaysAgo).Select(x => x.exam).Distinct().ToList();
            models.Add(new ExamsByStateViewModel(greaterFourteen, "Greater Than 14 Days in CIMS"));
            var threefis = DB11.exam_intrusion.IncludeEverythingForExamDashboardViewModel().OpenExams().Where(x => x.exam.@case.agency_org.agency.name == "3FIS" || x.exam.@case.agency_org.agency.name == "3 FIS"
            || x.exam.@case.agency_org.agency_unit.name == "3FIS" || x.exam.@case.agency_org.agency_unit.name == "3 FIS").Select(x => x.exam).Distinct().ToList();
            models.Add(new ExamsByStateViewModel(threefis, "3FIS Exams"));
            var highPriority = DB11.exam_intrusion.IncludeEverythingForExamDashboardViewModel().OpenExams().Where(x => x.high_priority == true).Select(x => x.exam).Distinct().ToList();
            models.Add(new ExamsByStateViewModel(highPriority, "High Priority Exams"));
            var allOpen = DB11.exam_intrusion.IncludeEverythingForExamDashboardViewModel().OpenExams().Select(x => x.exam).ToList();
            models.Add(new ExamsByStateViewModel(allOpen, "All Open Exams"));
            return KendoGridResult(models.AsQueryable(), request);
        }

        public ActionResult OpenExamsByStatus([DataSourceRequest] DataSourceRequest request, int sectionID)
        {
            var models = new List<ExamsByStateViewModel>();

            var acqReview = OpenActivities(sectionID).Where(z =>
                    z.activity_id == (int)ActivityType._Perform_Acquisition_Review ||
                    z.activity_id == (int)ActivityType._Correct_Acquisition_From_Acquisition_Review ||
                    z.activity_id == (int)ActivityType._Section_Chief_Acquisition_Review ||
                    z.activity_id == (int)ActivityType._Correct_Acquisition_From_Section_Chief_Review
                ).Select(z => z.exam).IncludeEverythingForExamDashboardViewModel().Distinct()
                .ToList();
            models.Add(new ExamsByStateViewModel(acqReview, "Acquisition Review"));

            var assignExamTeam = OpenActivities(sectionID)
                .Where(z => z.activity_id == (int)ActivityType._Assign_Examination_Team)
                .Select(z => z.exam).IncludeEverythingForExamDashboardViewModel().Distinct().ToList();
            models.Add(new ExamsByStateViewModel(assignExamTeam, "Assign Exam Team"));

            var pendingStart = OpenActivities(sectionID)
                .Where(z => z.activity_id == (int)ActivityType._Start_Examination).Select(z => z.exam)
                .IncludeEverythingForExamDashboardViewModel().Distinct()
                .ToList();
            models.Add(new ExamsByStateViewModel(pendingStart, "Pending Start Examination"));

            var examReview = OpenActivities(sectionID).Where(z =>
                    z.activity_id == (int)ActivityType._Perform_Examination_Review || z.activity_id ==
                    (int)ActivityType._Correct_Examination_From_Examination_Review).Select(z => z.exam)
                .IncludeEverythingForExamDashboardViewModel().Distinct()
                .ToList();
            models.Add(new ExamsByStateViewModel(examReview, "Examination Review"));

            var examSectionChiefReview = OpenActivities(sectionID)
                .Where(z => z.activity_id == (int)ActivityType._Section_Chief_Examination_Review ||
                            z.activity_id == (int)ActivityType._Correct_Examination_From_Section_Chief_Review)
                .Select(z => z.exam).IncludeEverythingForExamDashboardViewModel().Distinct().ToList();
            models.Add(new ExamsByStateViewModel(examSectionChiefReview, "Examination Section Chief Review"));

            var qaReview = OpenActivities(sectionID)
                .Where(z => z.activity_id == (int)ActivityType._Perform_QA_Review ||
                            z.activity_id == (int)ActivityType._Correct_From_QA_Review)
                .Select(z => z.exam).IncludeEverythingForExamDashboardViewModel().Distinct().ToList();
            models.Add(new ExamsByStateViewModel(qaReview, "QA Review"));

            var onHold = DB11.exam.IncludeEverythingForExamDashboardViewModel()
                .Where(z => z.exam_status_id == (int)ExamStatus.Hold && z.group_id == sectionID).ToList();
            models.Add(new ExamsByStateViewModel(onHold, "Exams on Hold"));

            var unassigned =
                from ea in DB11.exam_activity
                where ea.activity_id == 115 && ea.date_end == null
                select ea.exam_id;
            var query =
                (from ea in DB11.exam_activity
                 join ua in unassigned on ea.exam_id equals ua
                 where ea.activity_id == 121 && ea.date_end != null && ea.exam.exam_status_id == 6
                 select ea.exam).IncludeEverythingForExamDashboardViewModel().Distinct().ToList();
            models.Add(new ExamsByStateViewModel(query, "Queue"));


            return KendoGridResult(models.AsQueryable(), request);
        }

        public ActionResult ClosedSectionExams(int sectionID)
        {
            ViewBag.SectionID = sectionID;
            ViewBag.ActionName = "ClosedExamsAssignedToSectionGridData";
            ViewBag.AccessibleName = "Closed Exams Assigned To Section Table";
            ViewBag.Closed = true;
            return PartialView("_SectionExams");
        }

        public ActionResult ExamsInSection(int sectionID)
        {
            ViewBag.SectionID = sectionID;
            ViewBag.ActionName = "ExamsInSectionGridData";
            ViewBag.AccessibleName = "Exams in Section Table";
            ViewBag.Closed = false;
            ViewBag.AutoRefreshThisGrid = true;
            return PartialView("_SectionExams");
        }

        public ActionResult ExamsOpenEvidenceSection(int sectionID)
        {
            ViewBag.SectionID = sectionID;
            ViewBag.ActionName = "ExamsOpenEvidenceSectionGridData";
            ViewBag.AccessibleName = "Exams in Open Evidence Section Table";
            ViewBag.Closed = false;
            return PartialView("_SectionExams");
        }

        public ActionResult OpenExamsPassedQaReview()
        {
            ViewBag.SectionID = (int)Group.QualityAssurance;
            ViewBag.ActionName = "OpenExamsPassedQaReviewGridData";
            ViewBag.AccessibleName = "Open Exams Passed QA Review Table";
            ViewBag.Closed = false;
            return PartialView("_SectionExams");
        }

        public ActionResult ClosedExamsPassedQaReview()
        {
            ViewBag.SectionID = (int)Group.QualityAssurance;
            ViewBag.ActionName = "ClosedExamsPassedQaReviewGridData";
            ViewBag.AccessibleName = "Closed Exams Passed QA Review Table";
            ViewBag.Closed = true;
            return PartialView("_SectionExams");
        }

        public ActionResult ExamsAssignedToSection(int sectionID)
        {
            ViewBag.SectionID = sectionID;
            ViewBag.ActionName = "ExamsAssignedToSectionGridData";
            ViewBag.AccessibleName = "Exams Assigned To Section Table";
            ViewBag.Closed = false;
            ViewBag.AutoRefreshThisGrid = true;
            return PartialView("_SectionExams");
        }

        public ActionResult SendGrid()
        {
            ViewBag.hardware = true;
            return PartialView("_SendGrid");
        }

        public ActionResult CFLDashboardOpenExams([DataSourceRequest] DataSourceRequest request)
        {
            var exams = DB11.vw_all_exams.OrderByDescending(x => x.date_opened)
                .Where(x => x.exam_status == "Active" || x.exam_status == "Hold").ToList();
            return KendoGridResult(exams.AsQueryable(), request);
        }

        public ActionResult CFLDashboardGridOpenExams()
        {
            ViewBag.AccessibleName = "CFL Dashboard Open Exams";
            ViewBag.Closed = false;
            ViewBag.AutoRefreshThisGrid = true;
            return PartialView("_CFLDashboardGridOpenExams");
        }

        public ActionResult CFLDashboardClosedExams([DataSourceRequest] DataSourceRequest request)
        {
            var twoYearsAgoFY = new DateTime(DateTime.Now.Year - 2, 10, 1);
            var exams = DB11.vw_all_exams.OrderByDescending(x => x.date_opened)
                .Where(
                    x => x.exam_status == "Done"
                    && x.date_closed >= twoYearsAgoFY
                ).ToList();
            return KendoGridResult(exams.AsQueryable(), request);
        }

        public ActionResult CFLDashboardGridClosedExams()
        {
            ViewBag.AccessibleName = "CFL Dashboard Closed Exams";
            ViewBag.Closed = false;
            ViewBag.AutoRefreshThisGrid = true;
            return PartialView("_CFLDashboardGridClosedExams");
        }

        public ActionResult CFLDashboardAllExams([DataSourceRequest] DataSourceRequest request)
        {
            var exams = DB11.vw_all_exams.OrderByDescending(x => x.date_opened)
                .Where(
                    x => x.exam_status != "Rejected"
                    && x.date_opened != null
                ).ToList();
            return KendoGridResult(exams.AsQueryable(), request);
        }

        public ActionResult CFLDashboardGridAllExams()
        {
            ViewBag.AccessibleName = "CFL Dashboard All Exams";
            ViewBag.Closed = false;
            ViewBag.AutoRefreshThisGrid = true;
            return PartialView("_CFLDashboardGridAllExams");
        }

        public ActionResult ClassifiedDashboardOpenExams([DataSourceRequest] DataSourceRequest request)
        {
            var exams = DB11.vw_all_exams.OrderByDescending(x => x.date_opened)
                .Where(
                    x => (x.exam_status == "Active" || x.exam_status == "Hold") 
                    && x.date_opened != null    // removes pre-exams
                    && x.exam_type == "standard" 
                    && x.section_id != 3    // evidence
                    && x.section_id != 6    // I&E
                    && (x.classification == "SECRET" || x.classification == "TOP SECRET" || x.classification == "TS//NOFORN" || x.classification == "TS//SCI")
                ).ToList();
            return KendoGridResult(exams.AsQueryable(), request);
        }

        public ActionResult ClassifiedDashboardClosedExams([DataSourceRequest] DataSourceRequest request)
        {
            var twoYearsAgoFY = new DateTime(DateTime.Now.Year - 2, 10, 1);
            var exams = DB11.vw_all_exams.OrderByDescending(x => x.date_opened)
                .Where(
                    x => x.exam_status == "Done" 
                    && x.date_closed >= twoYearsAgoFY
                    && x.exam_type == "standard"
                    && x.section_id != 3    // evidence
                    && x.section_id != 6    // I&E
                    && (x.classification == "SECRET" || x.classification == "TOP SECRET" || x.classification == "TS//NOFORN" || x.classification == "TS//SCI")
                ).ToList();
            return KendoGridResult(exams.AsQueryable(), request);
        }

        public ActionResult ClassifiedDashboardAllExams([DataSourceRequest] DataSourceRequest request)
        {
            var exams = DB11.vw_all_exams.OrderByDescending(x => x.date_opened)
                .Where(
                    x => x.exam_status != "Rejected" 
                    && x.date_opened != null
                    && x.exam_type == "standard"
                    && x.section_id != 3    // evidence
                    && x.section_id != 6    // I&E
                    && (x.classification == "SECRET" || x.classification == "TOP SECRET" || x.classification == "TS//NOFORN" || x.classification == "TS//SCI")
                ).ToList();
            return KendoGridResult(exams.AsQueryable(), request);
        }

        public ActionResult ClassifiedDashboardGridOpenExams()
        {
            ViewBag.AccessibleName = "Classified Dashboard Open Exams";
            ViewBag.Closed = false;
            ViewBag.AutoRefreshThisGrid = true;
            return PartialView("_ClassifiedDashboardGridOpenExams");
        }

        public ActionResult ClassifiedDashboardGridClosedExams()
        {
            ViewBag.AccessibleName = "Classified Dashboard Closed Exams";
            ViewBag.Closed = false;
            ViewBag.AutoRefreshThisGrid = true;
            return PartialView("_ClassifiedDashboardGridClosedExams");
        }

        public ActionResult ClassifiedDashboardGridAllExams()
        {
            ViewBag.AccessibleName = "Classified Dashboard All Exams";
            ViewBag.Closed = false;
            ViewBag.AutoRefreshThisGrid = true;
            return PartialView("_ClassifiedDashboardGridAllExams");
        }

        public ActionResult UnclassifiedDashboardOpenExams([DataSourceRequest] DataSourceRequest request)
        {
            var exams = DB11.vw_all_exams.OrderByDescending(x => x.date_opened)
                .Where(
                    x => (x.exam_status == "Active" || x.exam_status == "Hold")
                    && x.date_opened != null    // removes pre-exams
                    && x.exam_type == "standard"
                    && x.section_id != 3    // evidence
                    && x.section_id != 6    // I&E
                    && (x.classification != "SECRET" && x.classification != "TOP SECRET" && x.classification != "TS//NOFORN" && x.classification != "TS//SCI")
                ).ToList();
            return KendoGridResult(exams.AsQueryable(), request);
        }

        public ActionResult UnclassifiedDashboardClosedExams([DataSourceRequest] DataSourceRequest request)
        {
            var twoYearsAgoFY = new DateTime(DateTime.Now.Year - 2, 10, 1);
            var exams = DB11.vw_all_exams.OrderByDescending(x => x.date_opened)
                .Where(
                    x => x.exam_status == "Done" 
                    && x.date_closed >= twoYearsAgoFY
                    && x.exam_type == "standard"
                    && x.section_id != 3    // evidence
                    && x.section_id != 6    // I&E
                    && (x.classification != "SECRET" && x.classification != "TOP SECRET" && x.classification != "TS//NOFORN" && x.classification != "TS//SCI")
                ).ToList();
            return KendoGridResult(exams.AsQueryable(), request);
        }

        public ActionResult UnclassifiedDashboardAllExams([DataSourceRequest] DataSourceRequest request)
        {
            var exams = DB11.vw_all_exams.OrderByDescending(x => x.date_opened)
                .Where(
                    x => x.exam_status != "Rejected"
                    && x.date_opened != null    // removes pre-exams
                    && x.exam_type == "standard"
                    && x.section_id != 3    // evidence
                    && x.section_id != 6    // I&E
                    && (x.classification != "SECRET" && x.classification != "TOP SECRET" && x.classification != "TS//NOFORN" && x.classification != "TS//SCI")
                ).ToList();
            return KendoGridResult(exams.AsQueryable(), request);
        }

        public ActionResult UnclassifiedDashboardGridOpenExams()
        {
            ViewBag.AccessibleName = "Unclassified Dashboard Open Exams";
            ViewBag.Closed = false;
            ViewBag.AutoRefreshThisGrid = true;
            return PartialView("_UnclassifiedDashboardGridOpenExams");
        }

        public ActionResult UnclassifiedDashboardGridClosedExams()
        {
            ViewBag.AccessibleName = "Unclassified Dashboard Closed Exams";
            ViewBag.Closed = false;
            ViewBag.AutoRefreshThisGrid = true;
            return PartialView("_UnclassifiedDashboardGridClosedExams");
        }

        public ActionResult UnclassifiedDashboardGridAllExams()
        {
            ViewBag.AccessibleName = "Unclassified Dashboard All Exams";
            ViewBag.Closed = false;
            ViewBag.AutoRefreshThisGrid = true;
            return PartialView("_UnclassifiedDashboardGridAllExams");
        }

        public ActionResult QADashboardOpenExams([DataSourceRequest] DataSourceRequest request)
        {
            var exams = DB11.vw_all_exams.OrderByDescending(x => x.date_opened)
                .Where(x => (x.exam_status == "Active" || x.exam_status == "Hold") && x.date_opened != null && x.section_id != 8).ToList();
            return KendoGridResult(exams.AsQueryable(), request);
        }

        public ActionResult QADashboardAllExams([DataSourceRequest] DataSourceRequest request)
        {
            var exams = DB11.vw_all_exams.OrderByDescending(x => x.date_opened)
                .Where(x => (x.exam_status != "Rejected") && x.date_opened != null && x.section_id != 8).ToList();
            return KendoGridResult(exams.AsQueryable(), request);
        }

        public ActionResult QADashboardGridOpenExams()
        {
            ViewBag.AccessibleName = "QA Dashboard Open Exams";
            ViewBag.Closed = false;
            ViewBag.AutoRefreshThisGrid = true;
            return PartialView("_QADashboardGridOpenExams");
        }

        public ActionResult QADashboardGridAllExams()
        {
            ViewBag.AccessibleName = "QA Dashboard All Exams";
            ViewBag.Closed = false;
            ViewBag.AutoRefreshThisGrid = true;
            return PartialView("_QADashboardGridAllExams");
        }

        public ActionResult IntrusionSectionExams()
        {
            ViewBag.ActionName = "IntrusionsExamGridData";
            ViewBag.Closed = false;
            var refresh = DB11.user_config_value.SingleOrDefault(x =>
                x.user_id == CurrentUserModel.AccountID &&
                x.config_type_id == (int)UserConfigTypes.Refresh)?.config_value == "1";
            if (refresh)
            {
                ViewBag.AutoRefreshThisGrid = true;
            }
            return PartialView("_IntrusionsSectionExams");
        }

        public ActionResult IntrusionsRefreshInterval()
        {
            var refreshInterval = DB11.user_config_value.SingleOrDefault(x =>
                x.user_id == CurrentUserModel.AccountID &&
                x.config_type_id == (int)UserConfigTypes.RefreshInterval);
            int parsedRefreshInterval = 0;
            if (refreshInterval != null && int.TryParse(refreshInterval.config_value, out parsedRefreshInterval))
            {
                return Json(parsedRefreshInterval, JsonRequestBehavior.AllowGet);
            }
            else
            {
                return Json(300, JsonRequestBehavior.AllowGet);
            }
        }

        public ActionResult IntrusionsColorThresholds()
        {
            var model = new IntrusionsDashboardConfigModel();
            model.AssignedRedThreshold = DB11.dashboard_state_thresholds_value.Single(x =>
                x.group_id == (int)Group.Intrusions &&
                x.threshold_type_id == (int)DashboardThresholdTypes.AssignedRed).threshold_value;
            model.AssignedYellowThreshold = DB11.dashboard_state_thresholds_value.Single(x =>
                x.group_id == (int)Group.Intrusions &&
                x.threshold_type_id == (int)DashboardThresholdTypes.AssignedYellow).threshold_value;
            model.UnassignedRedThreshold = DB11.dashboard_state_thresholds_value.Single(x =>
                x.group_id == (int)Group.Intrusions &&
                x.threshold_type_id == (int)DashboardThresholdTypes.UnassignedRed).threshold_value;
            model.UnassignedYellowThreshold = DB11.dashboard_state_thresholds_value.Single(x =>
                x.group_id == (int)Group.Intrusions &&
                x.threshold_type_id == (int)DashboardThresholdTypes.UnassignedYellow).threshold_value;
            return Json(model, JsonRequestBehavior.AllowGet);
        }

        public ActionResult IntrusionsDashboardConfig()
        {
            var model = new IntrusionsDashboardConfigModel();
            model.Refresh = DB11.user_config_value.SingleOrDefault(x =>
                x.user_id == CurrentUserModel.AccountID &&
                x.config_type_id == (int)UserConfigTypes.Refresh)?.config_value == "1";
            var refreshInterval = DB11.user_config_value.SingleOrDefault(x =>
                x.user_id == CurrentUserModel.AccountID &&
                x.config_type_id == (int)UserConfigTypes.RefreshInterval);
            int parsedRefreshInterval = 0;
            if (refreshInterval != null && int.TryParse(refreshInterval.config_value, out parsedRefreshInterval))
            {
                model.RefreshInterval = parsedRefreshInterval;
            }
            else
            {
                model.RefreshInterval = 300;
            }
            var defaultTab = DB11.user_config_value.SingleOrDefault(x =>
                x.user_id == CurrentUserModel.AccountID &&
                x.config_type_id == (int)UserConfigTypes.DefaultTab);
            int parsedDefaultTab = 0;
            if (defaultTab != null && int.TryParse(defaultTab.config_value, out parsedDefaultTab))
            {
                model.DefaultTab = parsedDefaultTab;
            }
            else
            {
                model.DefaultTab = 0;
            }

            model.AssignedRedThreshold = DB11.dashboard_state_thresholds_value.Single(x =>
                x.group_id == (int)Group.Intrusions &&
                x.threshold_type_id == (int)DashboardThresholdTypes.AssignedRed).threshold_value;
            model.AssignedYellowThreshold = DB11.dashboard_state_thresholds_value.Single(x =>
                x.group_id == (int)Group.Intrusions &&
                x.threshold_type_id == (int)DashboardThresholdTypes.AssignedYellow).threshold_value;
            model.UnassignedRedThreshold = DB11.dashboard_state_thresholds_value.Single(x =>
                x.group_id == (int)Group.Intrusions &&
                x.threshold_type_id == (int)DashboardThresholdTypes.UnassignedRed).threshold_value;
            model.UnassignedYellowThreshold = DB11.dashboard_state_thresholds_value.Single(x =>
                x.group_id == (int)Group.Intrusions &&
                x.threshold_type_id == (int)DashboardThresholdTypes.UnassignedYellow).threshold_value;

            ViewBag.IntrusionsAdmin = CurrentUserModel.Permissions.Any(x => x.Group == Group.Intrusions && x.Role == Role.Admin);

            return PartialView(model);
        }

        public ActionResult SaveIntrusionsDashboardConfig(IntrusionsDashboardConfigModel model)
        {
            model.Save(DB11, CurrentUserModel.AccountID,
                CurrentUserModel.Permissions.Any(x => x.Group == Group.Intrusions && x.Role == Role.Admin));
            return new EmptyResult();
        }

        public ActionResult IntrusionSectionMalwareExams()
        {
            ViewBag.ActionName = "IntrusionsMalwareExamGridData";
            ViewBag.Closed = false;
            var refresh = DB11.user_config_value.SingleOrDefault(x =>
                x.user_id == CurrentUserModel.AccountID &&
                x.config_type_id == (int)UserConfigTypes.Refresh)?.config_value == "1";
            if (refresh)
            {
                ViewBag.AutoRefreshThisGrid = true;
            }
            return PartialView("_IntrusionsSectionExams");
        }

        public ActionResult IntrusionSectionNonMalwareExams()
        {
            ViewBag.ActionName = "IntrusionsNonMalwareExamGridData";
            ViewBag.Closed = false;
            var refresh = DB11.user_config_value.SingleOrDefault(x =>
                x.user_id == CurrentUserModel.AccountID &&
                x.config_type_id == (int)UserConfigTypes.Refresh)?.config_value == "1";
            if (refresh)
            {
                ViewBag.AutoRefreshThisGrid = true;
            }
            return PartialView("_IntrusionsSectionExams");
        }

        public ActionResult IntrusionSectionClosedExams()
        {
            ViewBag.ActionName = "IntrusionsClosedExamGridData";
            ViewBag.Closed = true;
            return PartialView("_IntrusionsSectionExams");
        }

        public ActionResult IntrusionSectionAllExams()
        {
            ViewBag.ActionName = "IntrusionsAllExamGridData";
            ViewBag.Closed = true;
            ViewBag.All = true;
            return PartialView("_IntrusionsSectionExams");
        }

        public ActionResult LitigationSupportSectionExams()
        {
            ViewBag.ActionName = "LitigationSupportExamGridData";
            ViewBag.AccessibleName = "Litigation Support Exam Table";
            ViewBag.Closed = false;
            return PartialView("_SectionExams");
        }

        public ActionResult LitigationSupportSectionClosedExams()
        {
            ViewBag.ActionName = "LitigationSupportClosedExamGridData";
            ViewBag.AccessibleName = "Litigation Support Closed Exam Table";
            ViewBag.Closed = true;
            return PartialView("_SectionExams");
        }

        public ActionResult IntakeExams(bool closed)
        {
            ViewBag.ActionName = closed ? "IntakeClosedExamGridData" : "IntakeExamGridData";
            ViewBag.Closed = closed;
            return PartialView("_IntakeSectionExams");
        }

        public ActionResult SectionTeam(int sectionID)
        {
            ViewBag.SectionID = sectionID;
            return PartialView("_SectionTeam");
        }

        public ActionResult SectionGantt(int sectionID)
        {
            ViewBag.SectionID = sectionID;
            return PartialView("_SectionGantt");
        }

        public ActionResult CFLExams()
        {
            ViewBag.ActionName = "CFLOpenExamGridData";
            ViewBag.Closed = false;
            ViewBag.AutoRefreshThisGrid = true;
            ViewBag.AccessibleName = "Open Exams Table";
            return PartialView("_SectionExams");
        }

        public ActionResult CFLClosedExams()
        {
            ViewBag.ActionName = "CFLClosedExamGridData";
            ViewBag.Closed = true;
            ViewBag.AccessibleName = "Closed Exams Table";
            return PartialView("_SectionExams");
        }

        public ActionResult CFLAllExams()
        {
            ViewBag.ActionName = "CFLAllExamsGridData";
            ViewBag.All = true;
            ViewBag.Closed = true;
            ViewBag.AccessibleName = "All Exams Table";
            return PartialView("_SectionExams");
        }

        public ActionResult CFLOverview()
        {
            var model = new LabDashboardViewModel(DB11);
            return PartialView("_CFLOverview", model);
        }

        public ActionResult CFLSLAs()
        {
            var model = new LabDashboardViewModel(DB11);
            return PartialView("_CFLSLAs", model);
        }

        public ActionResult CFLMetrics()
        {
            ViewBag.HoldTypes = DB11.exam_hold_type.Select(x => new SelectListItem
            { Text = x.name, Value = x.exam_hold_type_id.ToString() }).ToList();
            return PartialView("_CFLMetrics");
        }

        public ActionResult IntrusionsMetrics()
        {
            ViewBag.HoldTypes = DB11.exam_hold_type.Select(x => new SelectListItem
            { Text = x.name, Value = x.exam_hold_type_id.ToString() }).ToList();
            return PartialView("_IntrusionsMetrics");
        }

        public ActionResult ExamsByStatus(int sectionID)
        {
            ViewBag.SectionID = sectionID;
            return PartialView("_ExamsByState");
        }

        public ActionResult IntrusionsExamsByStatus()
        {
            return PartialView("_IntrusionsExamsByState");
        }

        public ActionResult TechReviews(int sectionID)
        {
            ViewBag.SectionID = sectionID;
            return PartialView("_TechReviews");
        }

        public ActionResult ExaminerTechReviews([DataSourceRequest] DataSourceRequest request, string startString,
            string endString, int sectionID)
        {
            var start = DateTime.Parse(startString);
            var end = DateTime.Parse(endString);

            var activities = DB11.exam_activity
                .Where(ea =>
                    ea.group_id == sectionID &&
                    ea.activity_id == (int)ActivityType._Perform_Examination_Review &&
                    ea.date_start >= start && ea.date_start <= end
                ).ToList();

            var reviews = activities
                .GroupBy(ea => new { ea.account_id, ea.account.name })
                .OrderBy(group => group.Key.name)
                .Select(group => new ExaminerTechReviewsModel
                {
                    Id = Guid.NewGuid().ToString(),
                    Examiner = group.Key.name,
                    TechReviews = group.Select(ea => ea.exam_id).Distinct().Count(),
                    Exams = group.ToList().GroupTechReviewActivities(DB11)
                }).ToList();

            return KendoGridResult(reviews.AsQueryable(), request);
        }
    }
}