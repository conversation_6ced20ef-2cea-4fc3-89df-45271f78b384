﻿using CIMSWeb.Models;
using Kendo.Mvc.UI;
using System.Linq;
using System.Web.Mvc;
using System.Collections.Generic;
using System;

namespace CIMSWeb.Controllers
{
    public class UnlockServiceController : CIMSController
    {
        public ActionResult Index()
        {
            var model = new UnlockServiceViewModel(DB11);
            return PartialView(model);
        }

        public ActionResult UnlockServiceConfigurationSettings()
        {
            var uscs = new UnlockServiceConfigurationSettings();
            return PartialView(uscs);
        }

        public ActionResult UnlockServiceGridData([DataSourceRequest]DataSourceRequest request)
        {
           var data = DB11.unlock_srvc_evidence.ToList()
                    .Select(ue => new UnlockServiceDetailsModel(ue)).ToList();
           
            return KendoGridResult(data.AsQueryable(), request);
        }

        public JsonResult GetPurchasedUnlocks()
        {
            return Json(new UnlockServiceViewModel(DB11), JsonRequestBehavior.AllowGet);
        }

        public ActionResult GetUnlockServices([DataSourceRequest]DataSourceRequest request)
        {
            var results = DB11.unlock_srvc.Select(z => new UnlockService { Unlock_service = z.unlock_srvc1, Unlock_service_id = z.unlock_srvc_id, IsUsed = 1 }).ToList();
            foreach (var result in results)
            {
                if (DB11.unlock_srvc_evidence.FirstOrDefault(z => z.unlock_srvc_id == result.Unlock_service_id) == null)
                {
                    result.IsUsed = 0;
                }
            }
            return KendoGridResult(results.AsQueryable(), request);
        }

        public JsonResult DeleteUnlockService(int unlock_service_id)
        {
            if (DB11.unlock_srvc.FirstOrDefault(z => z.unlock_srvc_id == unlock_service_id) != null)
            {
                AuditDelete(DB11.unlock_srvc.FirstOrDefault(z => z.unlock_srvc_id == unlock_service_id));
                DB11.unlock_srvc.Remove(DB11.unlock_srvc.FirstOrDefault(z => z.unlock_srvc_id == unlock_service_id));
                DB11.SaveChanges();
                return Json(true, JsonRequestBehavior.AllowGet);
            }
            else
            {
                return Json(false, JsonRequestBehavior.AllowGet);
            }
        }

        public JsonResult SaveUnlockService(string unlock_service, int? unlock_service_id)
        {
            //For dupes
            if (DB11.unlock_srvc.Where(x => x.unlock_srvc1 == unlock_service).Select(x => x.unlock_srvc1).FirstOrDefault() == null)
            {
                if (DB11.unlock_srvc.FirstOrDefault(x => x.unlock_srvc_id == unlock_service_id) == null && unlock_service != null)
                {
                    var ue = new CIMSData.Database.unlock_srvc()
                    {
                        unlock_srvc1 = unlock_service,
                    };
                    DB11.unlock_srvc.Add(ue);
                    AuditInsert(ue);
                }

                if (DB11.unlock_srvc.FirstOrDefault(x => x.unlock_srvc_id == unlock_service_id) != null && unlock_service != null)
                {
                    DB11.unlock_srvc.FirstOrDefault(x => x.unlock_srvc_id == unlock_service_id).unlock_srvc1 = unlock_service;
                    DB11.Entry(DB11.unlock_srvc.FirstOrDefault(x => x.unlock_srvc_id == unlock_service_id)).State = System.Data.Entity.EntityState.Modified;
                    AuditUpdate(DB11.unlock_srvc.FirstOrDefault(x => x.unlock_srvc_id == unlock_service_id));
                }
            }
            else //when adding a duplicate evidence type (not editing, doesnt already exist)
            {
                //do nothing
            }

            DB11.SaveChanges();
            return Json(true, JsonRequestBehavior.AllowGet);
        }

        public ActionResult GetPurchasedTokens([DataSourceRequest]DataSourceRequest request)
        {
            var results = DB11.unlock_srvc_purchased_token.Select(z => new PurchasedUFEDToken { token_num = z.token_num, purchased_date = z.purchased_date, purchased_token_uid = z.unlock_srvc_purchased_token_id }).ToList();
            return KendoGridResult(results.AsQueryable(), request);
        }

        public JsonResult DeletePurchasedToken(int ufed_purchased_token_id)
        {
            var purchased_token = DB11.unlock_srvc_purchased_token.FirstOrDefault(z => z.unlock_srvc_purchased_token_id == ufed_purchased_token_id);
            if (purchased_token != null)
            {
                AuditDelete(purchased_token);
                DB11.unlock_srvc_purchased_token.Remove(purchased_token);
                DB11.SaveChanges();
                return Json(true, JsonRequestBehavior.AllowGet);
            }
            else
            {
                return Json(false, JsonRequestBehavior.AllowGet);
            }
        }

        public JsonResult SavePurchasedToken(int ufed_purchased_token_id, int token_num, string purchased_date)
        {
            var tokenToSave = DB11.unlock_srvc_purchased_token.FirstOrDefault(x => x.unlock_srvc_purchased_token_id == ufed_purchased_token_id);
            if (tokenToSave == null && token_num != 0)
            {
                var upt = new CIMSData.Database.unlock_srvc_purchased_token()
                {
                    token_num = token_num,
                    purchased_date = DateTime.Parse(purchased_date)
                };
                DB11.unlock_srvc_purchased_token.Add(upt);
                AuditInsert(upt);
            }else if(tokenToSave != null && token_num != 0)
            {
                tokenToSave.token_num = token_num;
                tokenToSave.purchased_date = DateTime.Parse(purchased_date);
                DB11.Entry(tokenToSave).State = System.Data.Entity.EntityState.Modified;
                AuditUpdate(tokenToSave);
            }

            DB11.SaveChanges();
            return Json(true, JsonRequestBehavior.AllowGet);
        }
   
        public ActionResult UnlockMetricsAllEvidenceTypes([DataSourceRequest]DataSourceRequest request, string startString, string endString)
        {
            var start = DateTime.Parse(startString);
            var end = DateTime.Parse(endString);
            var data = new List<AllEvidenceTypeUnlockMetrics>();
            var allEvidenceTypes = DB11.unlock_srvc_evidence.Where(x => x.unlock_status == true && x.exam.exam_status_id != (int)ExamStatus.Rejected && x.unlock_started_date >= start && x.unlock_ended_date <= end).ToList();
            var allEvidenceTypesMetric = new AllEvidenceTypeUnlockMetrics(allEvidenceTypes) { Evidence_type = "All" };
            if (allEvidenceTypesMetric.NumOfEvidenceForType > 0)
            {
                data.Add(allEvidenceTypesMetric);

                //Get top 10 most used evidence types
                var devicesGroupedByDeviceType = from device in allEvidenceTypes
                                          group device by device.evidence.evidence_type_id
                    into deviceGroup
                    select new
                    {
                        Count = deviceGroup.Count(),
                        Evidence_type = DB11.evidence_type.Find(deviceGroup.Key)?.evidence_type1
                    };
                var topTenDevices = devicesGroupedByDeviceType.OrderBy(z => z.Evidence_type).Take(10);

                data.AddRange(topTenDevices.Select(x => DB11.unlock_srvc_evidence.Where(z => z.unlock_status == true && Equals(z.evidence.evidence_type.evidence_type1, x.Evidence_type) && z.unlock_started_date >= start && z.unlock_ended_date <= end).ToList()).Select(evidenceTypeList => new AllEvidenceTypeUnlockMetrics(evidenceTypeList)));
            }
            return KendoGridResult(data.AsQueryable(), request);
        }

        public ActionResult SuccessfulUnlocksPerEvidenceType(string startString, string endString)
        {
            var start = DateTime.Parse(startString);
            var end = DateTime.Parse(endString);
            var unlocks = DB11.unlock_srvc_evidence
                .Where(x=> x.unlock_status == true && x.exam.exam_status_id != (int)ExamStatus.Rejected && x.unlock_started_date >= start && x.unlock_ended_date <= end)
                .Select(x => x)
                .Distinct()
                .GroupBy(x => x.evidence.evidence_type_id)
                .ToList()
                .Select(x => new { EvidenceType = DB11.evidence_type.Find(x.Key) == null ? "No Type" : DB11.evidence_type.Find(x.Key)?.evidence_type1, Count = x.Count(), Color = GetRandomColor() }).Take(10).ToList();
            return Json(unlocks, JsonRequestBehavior.AllowGet);
        }

        public ActionResult UnlockMetricsPhoneBrand([DataSourceRequest]DataSourceRequest request, string startString, string endString)
        {
            var start = DateTime.Parse(startString);
            var end = DateTime.Parse(endString);
            var data = new List<PhoneBrandUnlockMetrics>();
            var allPhones = DB11.unlock_srvc_evidence.Where(x => x.evidence.evidence_type_id == 1 && x.evidence.make != null && x.unlock_status == true && x.exam.exam_status_id != (int)ExamStatus.Rejected  && x.unlock_started_date >= start && x.unlock_ended_date <= end).ToList();
            //Get metrics for all phones
            var allPhoneMetric = new PhoneBrandUnlockMetrics(allPhones) { Make = "All" };
            if (allPhoneMetric.NumOfPhones > 0)
            {
                data.Add(allPhoneMetric);

                var phonesWithNoMake = DB11.unlock_srvc_evidence.Where(x =>
                    x.evidence.evidence_type_id == 1 && x.evidence.make == null && x.unlock_status == true && x.exam.exam_status_id != (int)ExamStatus.Rejected  && 
                    x.unlock_started_date >= start && x.unlock_ended_date <= end).ToList();
                if (phonesWithNoMake.Count > 0)
                {
                    var phonesWithNoMakeMetric = new PhoneBrandUnlockMetrics(phonesWithNoMake) {Make = "Unknown"};
                    data.Add(phonesWithNoMakeMetric);
                }

                //Get top 10 most used phone makes
                var phonesGroupedByMake = from phone in allPhones
                    group phone by phone.evidence.make.Trim().ToLower()
                    into phoneGroup
                    select new
                    {
                        Count = phoneGroup.Count(),
                        Make = phoneGroup.Key
                    };
                var topTenPhones = phonesGroupedByMake.OrderBy(z => z.Make).Take(10);

                data.AddRange(topTenPhones.Select(x => DB11.unlock_srvc_evidence.Where(z =>
                        z.evidence.evidence_type_id == 1 && z.unlock_status == true &&
                        string.Equals(z.evidence.make.Trim().ToLower(), x.Make.Trim().ToLower()) &&
                        z.unlock_started_date >= start && z.unlock_ended_date <= end).ToList())
                    .Select(phoneList => new PhoneBrandUnlockMetrics(phoneList)));
            }

            return KendoGridResult(data.AsQueryable(), request);
        }

        public ActionResult SuccessfulUnlocksPerPhoneBrand(string startString, string endString)
        {
            var start = DateTime.Parse(startString);
            var end = DateTime.Parse(endString);
            var unlocks = DB11.unlock_srvc_evidence
                .Where(x => x.evidence.evidence_type_id == 1 && x.evidence.make != null && x.unlock_status == true && x.exam.exam_status_id != (int)ExamStatus.Rejected && x.unlock_started_date >= start && x.unlock_ended_date <= end)
                .Select(x => x)
                .Distinct()
                .GroupBy(x => x.evidence.make)
                .ToList()
                .Select(x => new { PhoneBrand = x.Key, Count = x.Count(), Color = GetRandomColor() }).Take(10).ToList();

            var unknown = DB11.unlock_srvc_evidence
                .Where(x => x.evidence.evidence_type_id == 1 && x.evidence.make == null && x.unlock_status == true && x.exam.exam_status_id != (int)ExamStatus.Rejected && x.unlock_started_date >= start && x.unlock_ended_date <= end).ToList();
            if (unknown.Count > 0)
            {
                unlocks.Add(new { PhoneBrand = "Not Specified", Count = unknown.Count(), Color = GetRandomColor() });
            }

            return Json(unlocks, JsonRequestBehavior.AllowGet);
        }

        public ActionResult UnlockMetricsAllLockTypes([DataSourceRequest]DataSourceRequest request, string startString, string endString)
        {
            var start = DateTime.Parse(startString);
            var end = DateTime.Parse(endString);
            var data = new List<LockTypeUnlockMetrics>();
            var allLockTypes = DB11.unlock_srvc_evidence.Where(x => x.unlock_status == true && x.exam.exam_status_id != (int)ExamStatus.Rejected && x.unlock_started_date >= start && x.unlock_ended_date <= end).ToList();
            var allLockTypesMetric = new LockTypeUnlockMetrics(allLockTypes) { LockType = "All" };
            if (allLockTypesMetric.NumUsingLockType > 0)
            {

                data.Add(allLockTypesMetric);

                var evidenceWithNoLockType = allLockTypes.Where(x =>
                    x.unlock_srvc_lock_type_id == null && x.unlock_status == true && x.unlock_started_date >= start &&
                    x.unlock_ended_date <= end).ToList();
                if (evidenceWithNoLockType.Count > 0)
                {
                    var evidenceWithNoLockTypeMetric = new LockTypeUnlockMetrics(evidenceWithNoLockType)
                        {LockType = "Not Specified"};
                    data.Add(evidenceWithNoLockTypeMetric);
                }

                evidenceWithNoLockType = null;
                allLockTypes.RemoveAll(z => z.unlock_srvc_lock_type_id == null);
                //Get top 10 most used evidence types
                var devicesGroupedByLockType = from device in allLockTypes
                    group device by device.unlock_srvc_lock_type_id
                    into deviceGroup
                    select new
                    {
                        Count = deviceGroup.Count(),
                        Lock_type = DB11.unlock_srvc_lock_type.Find(deviceGroup.Key).unlock_srvc_lock_type1
                    };
                var topTenDevices = devicesGroupedByLockType.OrderBy(z => z.Lock_type).Take(10);

                data.AddRange(topTenDevices.Select(x => DB11.unlock_srvc_evidence.Where(z =>
                        z.unlock_srvc_lock_type_id != null && z.unlock_status == true &&
                        Equals(z.unlock_srvc_lock_type.unlock_srvc_lock_type1, x.Lock_type) &&
                        z.unlock_started_date >= start && z.unlock_ended_date <= end).ToList())
                    .Select(evidenceTypeList => new LockTypeUnlockMetrics(evidenceTypeList)));
            }

            return KendoGridResult(data.AsQueryable(), request);
        }

        public ActionResult SuccessfulUnlocksPerLockType(string startString, string endString)
        {
            var start = DateTime.Parse(startString);
            var end = DateTime.Parse(endString);
            var unlocks = DB11.unlock_srvc_evidence
                .Where(x => x.unlock_status == true && x.unlock_srvc_lock_type_id != null && x.exam.exam_status_id != (int)ExamStatus.Rejected && x.unlock_started_date >= start && x.unlock_ended_date <= end)
                .Select(x => x)
                .Distinct()
                .GroupBy(x => x.unlock_srvc_lock_type_id)
                .ToList()
                .Select(x => new { LockType = DB11.unlock_srvc_lock_type.Find(x.Key).unlock_srvc_lock_type1, Count = x.Count(), Color = GetRandomColor() }).Take(10).ToList();

            if (DB11.unlock_srvc_evidence
                .Where(x => x.unlock_status == true && x.unlock_srvc_lock_type_id == null && x.exam.exam_status_id != (int)ExamStatus.Rejected && x.unlock_started_date >= start &&
                            x.unlock_ended_date <= end).ToList().Any())
            {
                unlocks.Add(new { LockType = "Not Specified", Count = DB11.unlock_srvc_evidence
                .Where(x => x.unlock_status == true && x.unlock_srvc_lock_type_id == null && x.exam.exam_status_id != (int)ExamStatus.Rejected && x.unlock_started_date >= start &&
                            x.unlock_ended_date <= end).ToList().Count(), Color = GetRandomColor() });
            }
            return Json(unlocks, JsonRequestBehavior.AllowGet);
        }

        public double? getDaysRan(DateTime? DateStarted, DateTime? DateEnded)
        {
            double? daysRan;
            if (!DateStarted.HasValue)
            {
                daysRan = null;
            }
            else if (!DateEnded.HasValue && DateStarted.HasValue)
            {
                daysRan = Math.Round((DateTime.Today - DateStarted).Value.TotalDays, 2);
            }
            else
            {
                daysRan = Math.Round((DateEnded - DateStarted).Value.TotalDays, 2);
            }

            return daysRan;
        }

        public JsonResult GetUnlockMetricsHeaders(string startString, string endString)
        {
            var start = DateTime.Parse(startString);
            var end = DateTime.Parse(endString);
            var successfulUnlocks = DB11.unlock_srvc_evidence.Where(x => x.unlock_status == true && x.exam.exam_status_id != (int)ExamStatus.Rejected && x.unlock_ended_date != null /*&& x.unlock_started_date >= start && x.unlock_ended_date <= end*/).ToList();
            var ongoingUnlockCount =
                DB11.unlock_srvc_evidence.Count(x => x.unlock_status != true && x.unlock_ended_date == null && x.exam.exam_status_id != (int)ExamStatus.Rejected );
            double? totalDaysRan = 0;
            var totalUnlocksCountedTowardsMetrics = 0;
            var listOfDaysRan = new List<double>();
            foreach (var x in successfulUnlocks)
            {
                var daysRan = getDaysRan(x.unlock_started_date, x.unlock_ended_date);
                if (daysRan == null)
                {
                    continue;
                }
                totalDaysRan += daysRan;
                listOfDaysRan.Add((double)daysRan);
                totalUnlocksCountedTowardsMetrics++;
            }
            var avgDaysToUnlock = Math.Round((double)totalDaysRan / totalUnlocksCountedTowardsMetrics, 2);
            if (double.IsNaN(avgDaysToUnlock))
            {
                avgDaysToUnlock = 0.0;
            }

            var stdDevToUnlock = Math.Round(Math.Sqrt(listOfDaysRan.Sum(x => Math.Pow(x - avgDaysToUnlock, 2)) / totalUnlocksCountedTowardsMetrics), 2);
            var metrics = new Dictionary<string, double?>();

            var ufed_premium_service =
                DB11.unlock_srvc.FirstOrDefault(x => x.unlock_srvc1 == "UFED Premium").unlock_srvc_id;

            var tokensUsed = DB11.unlock_srvc_evidence.Where(x =>
                x.token_used == true && x.unlock_srvc_id == ufed_premium_service && x.unlock_srvc_group_id != null && x.exam.exam_status_id != (int)ExamStatus.Rejected && (x.unlock_started_date >= start || x.unlock_started_date == null) && (x.unlock_ended_date <= end || x.unlock_ended_date == null)).ToList();

            var cfl_group_id = DB11.unlock_srvc_group.FirstOrDefault(x => x.name == "CFL").unlock_srvc_group_id;
            var afosi_group_id = DB11.unlock_srvc_group.FirstOrDefault(x => x.name == "AFOSI").unlock_srvc_group_id;
            var ncis_group_id = DB11.unlock_srvc_group.FirstOrDefault(x => x.name == "NCIS").unlock_srvc_group_id;
            var armyCID_group_id = DB11.unlock_srvc_group.FirstOrDefault(x => x.name == "Army CID").unlock_srvc_group_id;

            metrics.Add("ongoingUnlockCount", ongoingUnlockCount);
            metrics.Add("successfulUnlocks", successfulUnlocks.Count);
            metrics.Add("cflUsedTokens", tokensUsed.Count(x => x.unlock_srvc_group_id == cfl_group_id));
            metrics.Add("afosiUsedTokens", tokensUsed.Count(x => x.unlock_srvc_group_id == afosi_group_id));
            metrics.Add("ncisUsedTokens", tokensUsed.Count(x => x.unlock_srvc_group_id == ncis_group_id));
            metrics.Add("armyCIDUsedTokens", tokensUsed.Count(x => x.unlock_srvc_group_id == armyCID_group_id));

            var min_value = avgDaysToUnlock - (2 * stdDevToUnlock);
            var max_value = avgDaysToUnlock + (2 * stdDevToUnlock);
            totalUnlocksCountedTowardsMetrics = 0;
            totalDaysRan = 0;
            foreach (var x in successfulUnlocks)
            {
                var daysRan = getDaysRan(x.unlock_started_date, x.unlock_ended_date);
                if (daysRan != null && daysRan < max_value && daysRan > min_value)
                {
                    totalDaysRan += daysRan;
                    totalUnlocksCountedTowardsMetrics++;
                }

            }

            double avgDaysToUnlockMinusOutliers = 0;
            if (totalUnlocksCountedTowardsMetrics > 0)
            {
                avgDaysToUnlockMinusOutliers = Math.Round((double)totalDaysRan / totalUnlocksCountedTowardsMetrics, 2);
            }

            var percentageOfUnlocksUsedInAvg = 0.0;
            if (successfulUnlocks.Count > 0)
            {
                percentageOfUnlocksUsedInAvg = Math.Round((double)totalUnlocksCountedTowardsMetrics / successfulUnlocks.Count, 4);
            }
            metrics.Add("avgDaysToUnlockMinusOutliers", avgDaysToUnlockMinusOutliers);
            metrics.Add("percentageOfUnlocksUsedInAvg", percentageOfUnlocksUsedInAvg);

            return Json(metrics, JsonRequestBehavior.AllowGet);
        }
    }
}