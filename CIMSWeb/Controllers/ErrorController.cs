﻿using System.Web.Mvc;

namespace CIMSWeb.Controllers
{
    public class ErrorController : CIMSController
    {
        // GET: Error
        public ActionResult Index()
        {
            return View();
        }

        public ActionResult Unauthorized()
        {
            ViewBag.Error = "You are not authorized to access the requested resource.";
            return View("Error");
        }

        public ActionResult NotFound()
        {
            ViewBag.Error = "Unable to find the requested resource.";
            return View("Error");
        }
    }
}