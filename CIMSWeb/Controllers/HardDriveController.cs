﻿using CIMSData.Database;
using CIMSWeb.Models;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web.Mvc;
using OfficeOpenXml;

namespace CIMSWeb.Controllers
{
    /// <summary>
    /// Controller for the library of hard drives
    /// </summary>
    public class HardDriveController : CIMSController
    {
        // GET: HardDrive
        public ActionResult Index()
        {
            return PartialView();
        }

        /// <summary>
        /// Provides the JSON for the master evidence Kendo grid
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public ActionResult HardDriveGridData([DataSourceRequest]DataSourceRequest request)
        {
            var data = DB11.hard_drive.ToList().Select(x => new HardDriveViewModel(x, DB11));
            return KendoGridResult(data.AsQueryable(), request);
        }

        public ActionResult HardDriveHistoryGridData([DataSourceRequest]DataSourceRequest request, int HardDriveId)
        {
            var data = DB11.hard_drive_history.Where(x => x.hard_drive_id == HardDriveId).ToList().Select(x => new HardDriveHistoryViewModel(x));
            return KendoGridResult(data.AsQueryable(), request);
        }


        public ActionResult HardDriveExamGridData([DataSourceRequest]DataSourceRequest request, int ExamId)
        {
            var dbItems = DB11.hard_drive_history.Where(x => x.exam_id == ExamId).Select(x => x.hard_drive).Distinct();
            List<HardDriveViewModel> models = new List<HardDriveViewModel>();
            foreach (var dbItem in dbItems)
            {
                HardDriveViewModel newModel = new HardDriveViewModel(dbItem);
                hard_drive_history lastHistItem = dbItem.hard_drive_history.OrderByDescending(x => x.hard_drive_history_id).FirstOrDefault();
                if (lastHistItem != null)
                {
                    if (lastHistItem.hard_drive_status_id == 1)
                    {
                        newModel.Status = "Checked Out To " + lastHistItem.account.name.FormatName();
                    }
                    else
                    {
                        newModel.Status = lastHistItem.hard_drive_status.hard_drive_status1.TitleCase();
                    }
                }
                models.Add(newModel);
            }
            return KendoGridResult(models.AsQueryable(), request);
        }

        public JsonResult HardDriveExamMultiSelect([DataSourceRequest]DataSourceRequest request, int ExamId)
        {
            var dbItems = DB11.hard_drive_history.Where(x => x.exam_id == ExamId).Select(x => x.hard_drive).Distinct();
            List<HardDriveViewModel> models = new List<HardDriveViewModel>();
            foreach (var dbItem in dbItems)
            {
                HardDriveViewModel newModel = new HardDriveViewModel(dbItem);
                hard_drive_history mostRecentHistoryItem = dbItem.hard_drive_history.OrderByDescending(x => x.date_logged).First();
                newModel.CheckedOutTo = mostRecentHistoryItem.account.sam_account_name.FormatName();
                newModel.CheckedOutToID = mostRecentHistoryItem.account.account_id;
                newModel.Status = mostRecentHistoryItem.hard_drive_status.hard_drive_status1;
                models.Add(newModel);
            }
            return Json(models.AsEnumerable(), JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// Create a hard drive
        /// </summary>
        /// <param name="request"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult HardDriveCreate([DataSourceRequest] DataSourceRequest request, HardDriveViewModel model)
        {
            if (model.MBNum != null && DB11.hard_drive.FirstOrDefault(x => x.mb_number == model.MBNum) != null)
            {
                throw new Exception("MB Number is not unique");
            }
            hard_drive d = model.Create();
            DB11.hard_drive.Add(d);
            DB11.SaveChanges();
            AuditInsert(d);
            DB11.SaveChanges();
            model.ID = d.hard_drive_id;
            // Set the new hard drive to checked in
            model.Status = "Checked In";
            var hdh = new hard_drive_history
            {
                hard_drive_id = model.ID,
                hard_drive_status_id = 2,
                entered_by = CurrentUserModel.AccountID,
                date_logged = DateTime.Now
            };
            DB11.hard_drive_history.Add(hdh);
            DB11.SaveChanges();
            AuditInsert(hdh);
            DB11.SaveChanges();
            return Json(new[] { model }.ToDataSourceResult(request, ModelState));
        }


        /// <summary>
        /// Update a hard drive
        /// </summary>
        /// <param name="request"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult HardDriveUpdate([DataSourceRequest] DataSourceRequest request, HardDriveViewModel model)
        {
            if (model != null && ModelState.IsValid)
            {
                hard_drive d = DB11.hard_drive.First(x => x.hard_drive_id == model.ID);
                AuditUpdate(d);
                model.Update(d);
                DB11.Entry(d).State = System.Data.Entity.EntityState.Modified;
                DB11.SaveChanges();
            }
            return Json(ModelState.ToDataSourceResult());
        }

        /// <summary>
        /// Remote validator for checking if an mbnumber is unique when creating/updating a hard drive.
        /// </summary>
        /// <param name="hardDriveId"></param>
        /// <param name="mbNumber"></param>
        /// <returns></returns>
        public JsonResult IsUniqueMB(int hardDriveId, double? mbNumber)
        {
            if (hardDriveId > 0)
            {
                if (mbNumber == null || DB11.hard_drive.First(x => x.hard_drive_id == hardDriveId).mb_number == mbNumber)
                    return Json(true, JsonRequestBehavior.AllowGet);
            }
            if(mbNumber == null || DB11.hard_drive.FirstOrDefault(x => x.mb_number == mbNumber) == null)
                return Json(true, JsonRequestBehavior.AllowGet);
            return Json("MB Number must be unique.", JsonRequestBehavior.AllowGet);
        }

        public ActionResult MissingHardDrives()
        {
            var sixMonthsAgo = DateTime.Now.AddMonths(-6);
            var jan2016 = DateTime.Parse("2016/01/01");
            var hds = DB11.vw_missing_hard_drives.Where(z => z.date_sent < sixMonthsAgo && z.date_sent > jan2016).ToList();
            var stream = new MemoryStream();
            using (var package = new ExcelPackage(stream))
            {
                var worksheet = package.Workbook.Worksheets.Add("Missing Hard Drives");
                var columns = new List<string>
                {
                    "MB Number", "Make", "Model", "Serial Number", "Size (GB)", "Description", "Date Checked Out",
                    "Date Sent", "Tracking Number", "Mail Service", "Exam Number", "Case Number", "Case Agent", "Case Agent Commercial #", "Case Agent Cell #"
                };
                ExtensionMethods.FillInAndStyleHeaderColumnsForExcelWorksheet(worksheet, columns);
                int i = 2;
                foreach (var hd in hds)
                {
                    worksheet.Cells[i, 1].Value = hd.mb_number;
                    worksheet.Cells[i, 2].Value = hd.make;
                    worksheet.Cells[i, 3].Value = hd.model;
                    worksheet.Cells[i, 4].Value = hd.serialno;
                    worksheet.Cells[i, 5].Value = hd.storage_size;
                    worksheet.Cells[i, 6].Value = hd.description;
                    worksheet.Cells[i, 7].Value = hd.date_checked_out.ToShortDateString();
                    worksheet.Cells[i, 8].Value = hd.date_sent.Value.ToShortDateString();
                    worksheet.Cells[i, 9].Value = hd.tracking_number;
                    worksheet.Cells[i, 10].Value = hd.name;
                    worksheet.Cells[i, 11].Value = hd.exam_number;
                    worksheet.Cells[i, 12].Value = hd.case_number;
                    worksheet.Cells[i, 13].Value = hd.first_name + " " + hd.last_name;
                    worksheet.Cells[i, 14].Value = hd.commercial;
                    worksheet.Cells[i, 15].Value = hd.cellphone;
                    i++;
                }
                worksheet.Cells[1, 1, 50, 15].AutoFitColumns();
                package.Save();
            }
            stream.Position = 0;
            return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }
    }
}