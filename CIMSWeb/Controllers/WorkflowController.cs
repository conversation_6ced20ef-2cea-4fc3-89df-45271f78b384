﻿using CIMSData.Database;
using CIMSWeb.Models;
using CIMSWeb.Workflow;
using Kendo.Mvc.UI;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web.Mvc;

namespace CIMSWeb.Controllers
{
    public class WorkflowController : CIMSController
    {
        [HttpPost]
        public ActionResult ReopenActivity(int examActivityID)
        {
            exam_activity ea = DB11.exam_activity.Find(examActivityID);
            ActivityViewModel vm = new ActivityViewModel(ea, DB11);
            switch (ea.exam.exam_type_id)
            {
                case 1:
                    IntrusionsWorkflow iWorkflow = new IntrusionsWorkflow(ea.exam, DB11);
                    iWorkflow.ReopenActivity(vm);
                    break;
                case 2:
                    if (ea.exam.exam_category_id == (int)ExamCategory.LitigationSupportTestimony)
                    {
                        LitigationSupportTestimonyWorkflow testimonyWorkflow = new LitigationSupportTestimonyWorkflow(ea.exam, DB11);
                        testimonyWorkflow.ReopenActivity(vm);
                    }
                    else
                    {
                        LitigationSupportDefenseImagesWorkflow defenseImagesWorkflow = new LitigationSupportDefenseImagesWorkflow(ea.exam, DB11);
                        defenseImagesWorkflow.ReopenActivity(vm);
                    }
                    break;
                case 3:
                    StandardExamWorkflow sWorkflow = new StandardExamWorkflow(ea.exam, DB11);
                    sWorkflow.ReopenActivity(vm);
                    break;
                case 4:
                    var w = new PreExamCoordinationWorkflow(ea.exam, DB11);
                    w.ReopenActivity(vm);
                    break;
                case 5:
                    var ww = new IntrusionsDamoWorkflow(ea.exam, DB11);
                    ww.ReopenActivity(vm);
                    break;
            }
            LogExamEvent(vm.ExamID, $"Workflow activity reopened - {vm.ActivityName}", ExamEventTypes.Workflow_Activity_Reopened);
            return new EmptyResult();
        }

        [HttpPost]
        public ActionResult CompleteActivity(int examActivityID, 
                                             int? assigneeID = null, 
                                             bool? passed = null, 
                                             TeamParticipantViewModel[] teamMembers = null, 
                                             int? leadTeamMemberID = null, 
                                             bool? correctionNeedsRedo = null, 
                                             int[] correctionUsers = null,
                                             int[] correctionEvidence = null,
                                             bool? skipped = null,
                                             bool skipDocCheck = false,
                                             bool proceedThroughWarning = false,
                                             int? preExamTypeId = null)
        {
            exam_activity ea = DB11.exam_activity.Where(x => x.exam_activity_id == examActivityID).Include(x => x.exam).First();
            if (skipped != true && !proceedThroughWarning)
            {
                var errorText = ValidateWorkflowState(ea);
                if (!string.IsNullOrEmpty(errorText.Item1)) return Json(new { IncorrectState = errorText.Item1, MustFix = errorText.Item2 });
            }

            if (skipped == true && (ea.exam.exam_status_id == (int) ExamStatus.Done ||
                                    ea.exam.exam_status_id == (int) ExamStatus.Rejected))
            {
                // If they skipped a lingering activity on a closed or rejected exam, don't progress the workflow any.
                ea.date_end = DateTime.Now;
                if (ea.account_id == null)
                {
                    ea.account_id = CurrentUserModel.AccountID;
                }
                ea.skipped = true;
                DB11.Entry(ea).State = EntityState.Modified;
                DB11.SaveChanges();   
                LogExamEvent(ea.exam_id, $"Workflow activity skipped - {ea.activity.name}", ExamEventTypes.Workflow_Activity_Skipped);
                return new EmptyResult();
            }
            ActivityViewModel vm = new ActivityViewModel(ea, DB11);
            string assigneeSamAcct = null;
            if (assigneeID.HasValue)
            {
                vm.AssigneeID = assigneeID.Value;
                var account = DB11.account.Find(assigneeID.Value);
                vm.Assignee = new AccountViewModel(account);
                assigneeSamAcct = "DCCI\\" + account.sam_account_name;
            }
            vm.Passed = passed;
            vm.LeadTeamMemberID = leadTeamMemberID;
            vm.TeamMembers = teamMembers;
            vm.CorrectionRedo = correctionNeedsRedo;
            vm.CorrectionUsers = correctionUsers;
            vm.CorrectionEvidence = correctionEvidence;
            vm.Skipped = skipped.GetValueOrDefault();
            switch (ea.exam.exam_type_id)
            {
                case 1:
                    IntrusionsWorkflow iWorkflow = new IntrusionsWorkflow(ea.exam, DB11);
                    var response = iWorkflow.CompleteActivity(vm, CurrentUserModel);
                    LogWorkflowActivity(vm);
                    if (!string.IsNullOrEmpty(response))
                    {
                        return Json(new {ResponseMessage = response});
                    }
                    break;
                case 2:
                    if (ea.exam.exam_category_id == (int) ExamCategory.LitigationSupportTestimony)
                    {
                        LitigationSupportTestimonyWorkflow testimonyWorkflow = new LitigationSupportTestimonyWorkflow(ea.exam, DB11);
                        testimonyWorkflow.CompleteActivity(vm, CurrentUserModel);
                    }
                    else
                    {
                        LitigationSupportDefenseImagesWorkflow defenseImagesWorkflow = new LitigationSupportDefenseImagesWorkflow(ea.exam, DB11);
                        defenseImagesWorkflow.CompleteActivity(vm, CurrentUserModel);
                    }
                    LogWorkflowActivity(vm);
                    break;
                case 3:
                    StandardExamWorkflow workflow = new StandardExamWorkflow(ea.exam, DB11);
                    if (skipDocCheck)
                    {
                        var documentMessages = workflow.CompleteActivity(vm, CurrentUserModel);
                        LogWorkflowActivity(vm);
                        var nonEmptyMessages = documentMessages.Where(z => !string.IsNullOrEmpty(z)).ToList();
                        if (nonEmptyMessages.Any())
                        {
                            return Json(new {DocumentMessages = nonEmptyMessages.Select(x => x.ToString())});
                        }
                    }
                    else
                    {
                        var missingDocs = workflow.GetMissingDocumentation(vm, CurrentUserModel, DB11);
                        var documentInfos = missingDocs.ToList();
                        if (documentInfos.Any())
                        {
                            return Json(new {MissingDocuments = documentInfos.Select(x => x.ToString())});
                        }
                        else
                        {
                            var documentMessages = workflow.CompleteActivity(vm, CurrentUserModel);
                            LogWorkflowActivity(vm);
                            var nonEmptyMessages = documentMessages.Where(z => !string.IsNullOrEmpty(z)).ToList();
                            if (nonEmptyMessages.Any())
                            {
                                return Json(new {DocumentMessages = nonEmptyMessages.Select(x => x.ToString())});
                            }
                        }
                    }
                    break;
                case 4:
                    if (ea.exam.exam_intrusion == null)
                    {
                        if (preExamTypeId.HasValue)
                        {
                            if (preExamTypeId.Value == 3)
                            {
                                goto case 3;
                            }
                            switch(preExamTypeId.Value)
                            {
                                case 2:
                                    LitigationSupportDefenseImagesWorkflow defenseImagesWorkflow = new LitigationSupportDefenseImagesWorkflow(ea.exam, DB11);
                                    defenseImagesWorkflow.CompleteActivity(vm, CurrentUserModel);
                                    break;
                                case 5:
                                    LitigationSupportTestimonyWorkflow testimonyWorkflow = new LitigationSupportTestimonyWorkflow(ea.exam, DB11);
                                    testimonyWorkflow.CompleteActivity(vm, CurrentUserModel);
                                    break;
                            }
                        }
                        else
                        {
                            goto case 3;
                        }
                    }
                    else
                    {
                        goto case 1;
                    }
                    break;
                case 5:
                    var w = new IntrusionsDamoWorkflow(ea.exam, DB11);
                    w.CompleteActivity(vm, CurrentUserModel);
                    LogWorkflowActivity(vm);
                    break;
            }

            if (!string.IsNullOrEmpty(assigneeSamAcct))
            {
                //var context = Microsoft.AspNet.SignalR.GlobalHost.ConnectionManager.GetHubContext<SignalRComm.CommHub>();
                //context.Clients.User(assigneeSamAcct).addNewMessageToPage("New Activity", ea.exam.exam_number);
            }

            return new EmptyResult();
        }

        /// <summary>
        /// Helper method for logging workflow-related events across exam types
        /// </summary>
        /// <param name="vm"></param>
        private void LogWorkflowActivity(ActivityViewModel vm)
        {
            if (vm.Skipped)
            {
                LogExamEvent(vm.ExamID, $"Workflow activity skipped - {vm.ActivityName}", ExamEventTypes.Workflow_Activity_Skipped);
            }
            else
            {
                if (vm.IsPassFail)
                {
                    LogExamEvent(vm.ExamID, $"Workflow activity completed - {vm.ActivityName} - {(vm.Passed.GetValueOrDefault() ? "Approved" : "Changes Required")}", ExamEventTypes.Workflow_Activity_Completed);
                }
                else
                {
                    LogExamEvent(vm.ExamID, $"Workflow activity completed - {vm.ActivityName}", ExamEventTypes.Workflow_Activity_Completed);
                }
            }
        }

        public ActionResult Assignees()
        {
            return Json(DB11.account.OrderBy(x => x.name).ToList().Select(x => new { Text = x.name.FormatName(), Value = x.account_id }).ToList(), JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// Determines open activities for current user and returns as Kendo grid JSON
        /// </summary>
        /// <param name="request"></param>
        /// <param name="examID"></param>
        /// <returns></returns>
        public ActionResult OpenActivities([DataSourceRequest]DataSourceRequest request, int? examID, int? userId)
        {
            var currentUser = CurrentUserModel;
            if (userId.HasValue)
            {
                currentUser = new AccountViewModel(DB11.account.First(x => x.account_id == userId));
            }
            int accountID = currentUser.AccountID;
            // This function gets reused in User Dashboard as well as Exam overview
            var activities = examID.HasValue
                ? DB11.exam_activity.IncludeForExamActivities()
                    .Where(x => x.exam_id == examID && x.date_start != null && x.date_end == null &&
                                (x.account_id == accountID || x.account_id == null))
                : DB11.exam_activity.IncludeForExamActivities()
                    .Where(x => x.date_start != null && x.date_end == null &&
                                (x.account_id == accountID || x.account_id == null));
            List<ActivityViewModel> avms = new List<ActivityViewModel>();
            // Activity applies to current user if it explicity identifies user via account_id or if current user
            // fits group & role criteria
            foreach (var a in activities)
            {
                // Skip activities for exams that are on hold or rejected, unless its a rejected exam with a ship_out_evidence activity
                if (a.exam.exam_status_id == (int)ExamStatus.Hold || 
                    (a.exam.exam_status_id == (int)ExamStatus.Rejected && a.activity_id != (int)ActivityType._Ship_Out_Evidence))
                {
                    continue;
                }
                if (a.account_id != null)
                {
                    avms.Add(new ActivityViewModel(a, DB11));
                }
                else
                {
                    foreach (var p in currentUser.Permissions)
                    {
                        if ((int)p.Group == a.group_id && (int)p.Role <= a.account_role_id)
                        {
                            avms.Add(new ActivityViewModel(a, DB11));
                        }
                    }
                }
            }
            return KendoGridResult(avms.OrderBy(x => x.StartDate).AsQueryable(), request);
        }

        /// <summary>
        /// validate the state of the exam if necessary for a certain activity of the workflow
        /// </summary>
        /// <param name="ea"></param>
        /// <returns> a string with the error message to display, and a boolean to determine if this must be resolved before proceeding to the next workflow step or not.</returns>
        private Tuple<string, bool> ValidateWorkflowState(exam_activity ea)
        {
            switch (ea.activity_id)
            {
                case (int)ActivityType._Create_Exam:
                case (int)ActivityType._Intake_Review:
                    if (ExamCategoryNotSet(ea)) return new Tuple<string, bool>("Please set a category for the exam before completing this activity.", true);
                    if (!AssignedOffIntake(ea)) return new Tuple<string, bool>("This exam is still assigned to the Fast2.0 section.<br /><br /> Continue Anyways?", false);
                    break;
                case (int)ActivityType._Perform_Forensic_Acquisition:
                    var evidenceId = ea.evidence.Single().evidence_id;
                    if (!DB11.cm_history.Any(z =>
                        z.exam_id == ea.exam_id && z.account_id == ea.account_id &&
                        z.cm_history_evidence.Any(x => x.evidence_id == evidenceId) &&
                        z.history_type_id == (int) CMHistoryTypes.POST_Success))
                    {
                        var label = new EvidenceViewModel(ea.evidence.Single()).LabelLong;
                        return new Tuple<string, bool>($"No successful POST record found for {label}. <br /><br /> Continue Anyways?", false);
                    }
                    break;
                case (int)ActivityType._Start_Examination:
                    if (!DB11.cm_history.Any(z =>
                        z.exam_id == ea.exam_id && z.account_id == ea.account_id &&
                        z.history_type_id == (int) CMHistoryTypes.POST_Success))
                    {
                        return new Tuple<string, bool>(
                            $"No successful POST record found for {CurrentUserModel.ImpersonatedName ?? CurrentUserModel.Name} on this exam. <br /><br /> Continue Anyways?",
                            false);
                    }
                    break;
                case (int)ActivityType._Ship_Out_Evidence:
                    var evidence = EvidenceNotReleased(ea);
                    if (evidence.Count > 0)
                    {
                        string ret = "The following evidence items are not set to released: <br /><br />";
                        foreach (var item in evidence)
                        {
                            ret += "<strong><em>" + item.LabelLong + "</em></strong> <br />";
                        }
                        ret += "<br />";
                        ret += "Continue Anyways?";
                        return new Tuple<string, bool>(ret, false);
                    }
                    break;
            }
            return new Tuple<string, bool>("", false);
        }

        private bool ExamCategoryNotSet(exam_activity ea)
        {
            var activity = ea.exam.exam_type_id == (int) ExamType.Intrusions
                ? (int) ActivityType._Create_Exam
                : (int) ActivityType._Intake_Review;
            return ea.activity_id == activity && !ea.exam.exam_category_id.HasValue;
        }

        private bool AssignedOffIntake(exam_activity ea)
        {
            if (ea.exam.exam_type_id == (int)ExamType.Intrusions)
            {
                // don't do this for intrusions.
                return true;
            }

            if (ea.activity_id == (int)ActivityType._Intake_Review)
            {
                // before completing intake review, check that Intake assigned the exam to another section.
                return ea.exam.group_id != (int)Group.Intake;
            }

            return true;
        }

        private List<EvidenceViewModel> EvidenceNotReleased(exam_activity ea)
        {
            var evidence = new List<EvidenceViewModel>();
            ea.exam.exam_evidence.Select(z => z.evidence).SelectMany(z => z.evidence_history).GroupBy(z => z.evidence_id,
                    (key, g) => g.OrderByDescending(z => z.date_logged).First())
                .Select(z => new {z.evidence_id, z.evidence_status_id}).ToList().ForEach(z =>
                {
                    if (z.evidence_status_id != 7)
                        evidence.Add(new EvidenceViewModel(DB11.evidence.Find(z.evidence_id)));
                });
            return evidence;
        }
    }

    public class TeamParticipantViewModel
    {
        public int AccountID { get; set; }
        public int[] EvidenceIDs { get; set; }
    }
}