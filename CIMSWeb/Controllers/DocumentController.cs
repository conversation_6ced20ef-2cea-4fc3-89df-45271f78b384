﻿using CIMSWeb.Models;
using CIMSWeb.Workflow;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Configuration;
using System.Web.Mvc;
using Microsoft.Ajax.Utilities;
using CommonServiceLocator;
using SolrNet;
using System.Net;
using CIMSData.Database;
using CIMSWeb.Attributes;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Kendo.Mvc.UI;

namespace CIMSWeb.Controllers
{
    public class DocumentController : CIMSController
    {

        public DocumentController()
        {
            cmRoot = WebConfigurationManager.AppSettings["DocumentRoot"];
        }

        public ActionResult UploadDocumentForm(string fileName, int examID)
        {
            return PartialView("UploadDocumentForm",
                new DocumentUploadViewModel(DB11.exam.Find(examID).exam_number, fileName,
                    examID));
        }

        public ActionResult Download(string path, string contentType = "application/pdf")
        {
            string parentPath;
            string truePath = FindItemPath(path, out parentPath);

            return File(System.IO.File.ReadAllBytes(truePath), contentType, new FileInfo(truePath).Name);
        }

        public ActionResult AttachmentDownload(string path)
        {
            var fileName = new FileInfo(path).Name;
            return File(System.IO.File.ReadAllBytes(path), System.Web.MimeMapping.GetMimeMapping(fileName), fileName);
        }

        public ActionResult InlineDownload(string path)
        {
            var fileName = new FileInfo(path).Name;
            Response.ContentType = System.Web.MimeMapping.GetMimeMapping(fileName);
            Response.AddHeader("Content-disposition", $"inline; filename=\"{fileName}\"");
            Response.BinaryWrite(System.IO.File.ReadAllBytes(path));
            return new EmptyResult();
        }

        public JsonResult GetFileName(string trackingNumber, string docType, string docOption, string descriptor, string fileExtension)
        {
            var doc = new CIMSDocument(trackingNumber, docType, fileExtension, descriptor, docOption);
            return Json(new { FullPath = doc.GetFilePath() }, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        public JsonResult UploadDocument()
        {
            if (Request.Files.Count != 1)
            {
                throw new Exception("Internal Server Error Uploading Document");
            }
            var doc = new CIMSDocument(Request.Form["trackingNumber"], Request.Form["docType"],
                Request.Form["fileExtension"], Request.Form["descriptor"], Request.Form["docOptions"], Request.Files[0]);
            string ret = doc.SaveDocument();
            LogExamEvent(Int32.Parse(Request.Form["examID"]), $"Document uploaded - {doc.FileName}", ExamEventTypes.Document_Uploaded);
            return Json(ret, JsonRequestBehavior.AllowGet);
        }

        public ActionResult IADocIngest()
        {
            foreach (var e in DB11.exam_intrusion)
            {
                var exam = new IntrusionsExamViewModel(e, DB11);
                var url = ConfigurationManager.AppSettings["IAService"] +
                          $"/IAExam/IndexLegacyExam?examName={exam.TrackingNumber}&examType={exam.Type}&customer={exam.SubAgencyOnly}&examinerName={exam.ExamLead}&examDate={exam.DateClosed}";
                var httpWebRequest = (HttpWebRequest)WebRequest.Create(url);

                using (var response = (HttpWebResponse)httpWebRequest.GetResponse())
                using (var stream = response.GetResponseStream())
                using (var streamReader = new StreamReader(stream))
                {
                    var ret = streamReader.ReadToEnd();
                    LogFromController(ret);
                }
            }
            return new EmptyResult();
        }

        public ActionResult GetIAExamInfo(string trackingNumber)
        {
            var exam = DB11.exam_intrusion.FirstOrDefault(z => z.exam.exam_number == trackingNumber);
            if (exam == null) return new HttpStatusCodeResult(HttpStatusCode.NotFound);
            var model = new IntrusionsExamViewModel(exam, DB11);
            model.Case = new CaseViewModel(<EMAIL>(model.CaseID), DB11);
            return Json(model, JsonRequestBehavior.AllowGet);
            return Json(
                new { model.Type, model.SubAgencyOnly, model.ExamLead, model.DateClosed, model.CaseNumber, model.Family },
                JsonRequestBehavior.AllowGet);
        }

        public ActionResult IngestValidationReports()
        {
            SolrValidationReport.IngestValidationReports();
            return new EmptyResult();
        }

        private static bool IngestToSolr(BinarySolrDocument doc)
        {
            if (doc == null) return false;
            var solr = ServiceLocator.Current.GetInstance<ISolrOperations<SolrCFLDocument>>();
            var alreadyIndexed = solr.Query(new SolrQueryByField("id", doc.FullPath)).NumFound == 1;
            if (alreadyIndexed)
            {
                return false;
            }
            using (var file = System.IO.File.OpenRead(doc.FullPath))
                lock (solrLock)
                {
                    if (file.Length == 0)
                    {
                        LogFromController($"{doc.FileName} is 0 bytes");
                        return false;
                    }
                    LogFromController($"Begin extracting {doc.FileName}");
                    var extractImages = doc.DocumentType == DocumentTypes.Form1ForensicServiceRequest;
                    ConfigureSolrInlineImageExtraction(extractImages);
                    var fields = new List<ExtractField>
                {
                    new ExtractField("exam_number", doc.ExamNumber),
                    new ExtractField("document_type", doc.DocumentType.DisplayName()),
                    new ExtractField("exam_closed_date", doc.Date.GetSolrDate()),
                    new ExtractField("exam_type", doc.ExamType),
                    new ExtractField("file_name", doc.FileName)
                };
                    if (!string.IsNullOrEmpty(doc.ExaminerName))
                    {
                        fields.Add(new ExtractField("examiner_name", doc.ExaminerName));
                    }
                    solr.Extract(new ExtractParameters(file, doc.FullPath)
                    {
                        Fields = fields,
                        AutoCommit = false,
                        LowerNames = true
                    });
                    return true;
                }
        }

        public ActionResult RecentExamIngest()
        {
            IngestNewlyFinishedExamsIntoSolr();
            return new EmptyResult();
        }

        public static void IngestNewlyFinishedExamsIntoSolr()
        {
            using (var db = new cims11Entities())
                lock (IngestNewlyFinishedExamsLock)
                    try
                    {
                        LogFromController("Ingest new exams function call starting");
                        var monthAgo = DateTime.Now.AddMonths(-1);
                        var standardExamsFinishedInPastMonth = db.exam.Where(z =>
                            z.exam_type_id == (int)ExamType.MajorCrimesCounterIntelligence && z.date_closed > monthAgo &&
                            z.exam_status_id == (int)ExamStatus.Done).Select(z => z.exam_number).AsEnumerable();
                        foreach (var trackingNumber in standardExamsFinishedInPastMonth)
                        {
                            var rootExamDir = CIMSDocument.GetFileSharePathFromDCFLTrackingNumber(trackingNumber);
                            if (!Directory.Exists(rootExamDir))
                            {
                                LogFromController($"No documents to ingest into solr because {rootExamDir} does not exist.");
                                continue;
                            }

                            foreach (var file in Directory
                                .EnumerateFiles(rootExamDir, "*.*", SearchOption.AllDirectories)
                                .Where(z => (z.Contains(DocumentTypeConstants.PFX_FORM1_SERVICE_RQST) ||
                                             z.Contains("FINAL") && FinalFormstoIngest.Any(z.Contains)) &&
                                            FileExtensionsToIngest.Any(z.EndsWith)))
                            {
                                try
                                {
                                    var doc = new BinarySolrDocument(file, db);
                                    if (IngestToSolr(doc))
                                    {
                                        LogFromController($"Document {doc.FileName} finished extracting");
                                    }
                                }
                                catch (DCFLDocException ex)
                                {
                                    LogFromController(ex.Message);
                                }
                            }
                        }

                        ServiceLocator.Current.GetInstance<ISolrOperations<SolrCFLDocument>>().Commit();
                        LogFromController("Ingest new exams function call finished");
                    }
                    catch (Exception e)
                    {
                        LogFromController("Something went wrong in the IngestNewlyFinishedExamsIntoSolr function");
                        LogFromController(e.Message);
                        LogFromController(e.StackTrace);
                    }
        }

        private static string ConfigureSolrInlineImageExtraction(bool imageExtract)
        {
            var httpWebRequest = (HttpWebRequest)WebRequest.Create(
                ConfigurationManager.AppSettings["Solr"] + ConfigurationManager.AppSettings["SolrCFLDocs"] + "/config");
            httpWebRequest.ContentType = "application/json";
            httpWebRequest.Method = "POST";

            using (var streamWriter = new StreamWriter(httpWebRequest.GetRequestStream()))
            {
                var tikaConfig = imageExtract ? "tika-config-inline-extract.xml" : "tika-config.xml";
                string json = "{\"update-requesthandler\": {\"name\": \"/update/extract\",\"startup\": \"lazy\",\"class\": \"solr.extraction.ExtractingRequestHandler\",\"defaults\": {\"lowernames\": \"true\",\"uprefix\": \"ignored_\",\"captureAttr\": \"true\",\"fmap.a\": \"links\",\"fmap.div\": \"ignored_\"},\"tika.config\": \"" + tikaConfig + "\"}}";

                streamWriter.Write(json);
            }

            var httpResponse = (HttpWebResponse)httpWebRequest.GetResponse();
            using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
            {
                return streamReader.ReadToEnd();
            }
        }

        public ActionResult InitialIngestOfCaseManagementFolderIntoSolr(bool save = true)
        {
            int i = 0;
            foreach (var file in GetAllCaseManagementFilesToIngest())
            {
                if (save)
                {
                    if (IngestToSolr(file))
                    {
                        LogFromController($"Document {file.FileName} finished extracting");
                    }
                }
                i++;
            }
            if (save) ServiceLocator.Current.GetInstance<ISolrOperations<SolrCFLDocument>>().Commit();
            return Json(i, JsonRequestBehavior.AllowGet);
        }

        public ActionResult SolrCommit()
        {
            ServiceLocator.Current.GetInstance<ISolrOperations<SolrCFLDocument>>().Commit();
            return new EmptyResult();
        }

        public ActionResult InitialIngestOfSolrExamData()
        {
            var solr = ServiceLocator.Current.GetInstance<ISolrOperations<SolrExamDocument>>();
            solr.Delete(SolrQuery.All);
            solr.Commit();
            var bag = new List<SolrExamDocument>();
            foreach (var e in DB11.exam)
            {
                bag.Add(new SolrExamDocument(e));
            }
            solr.AddRange(bag);
            solr.Commit();
            return new EmptyResult();
        }

        [CIMSAuthorize(SecureFunction.IntrusionSearch)]
        public ActionResult IAPublish(string examNumber)
        {
            var publish =
                new IntrusionsExamViewModel(DB11.exam.First(z => z.exam_number == examNumber).exam_intrusion, DB11)
                    .IAPublish();
            return Json(publish, JsonRequestBehavior.AllowGet);
        }

        public ActionResult ApproveForm(string path, string examNumber)
        {
            var file = new FileInfo(path);
            var split = file.Name.Split('.');
            DocumentTypes docType;
            DocumentTypeConstants.FormReviewStep step;
            DocumentTypeConstants.FormReviewStage stage;
            int stepIndex;
            int stageIndex;
            var name = "";
            if (split[1].StartsWith("[F4]"))
            {
                docType = split[1] == "[F4]Lab_Report_Supplemental"
                    ? DocumentTypes.Form4LaboratoryReportSupplemental
                    : DocumentTypes.Form4LaboratoryReport;
                if (split[2] == "CONTRABAND" || split[2] == "SANITIZED")
                {
                    name = split[2];
                    stepIndex = 4;
                    stageIndex = 3;
                }
                else
                {
                    stepIndex = 3;
                    stageIndex = 2;
                }
            }
            else if (split[1].StartsWith("[F9]"))
            {
                docType = split[1] == "[F9]Lab_Notes"
                    ? DocumentTypes.Form9LaboratoryNotes
                    : DocumentTypes.Form9TechnicianNotes;
                name = split[2];
                stepIndex = 4;
                stageIndex = 3;
            }
            else
            {
                return Json("Document not detected as either Form 4 or Form 9", JsonRequestBehavior.AllowGet);
            }
            switch (split[stepIndex])
            {
                case "INITIAL":
                    step = DocumentTypeConstants.FormReviewStep.INITIAL;
                    break;
                case "REVISED":
                    step = DocumentTypeConstants.FormReviewStep.REVISED;
                    break;
                default:
                    return Json("Document step must be initial or revised", JsonRequestBehavior.AllowGet);
            }

            switch (split[stageIndex])
            {
                case "TECH":
                    stage = DocumentTypeConstants.FormReviewStage.TECH;
                    break;
                case "ADMIN":
                    stage = DocumentTypeConstants.FormReviewStage.ADMIN;
                    break;
                case "QA":
                    stage = DocumentTypeConstants.FormReviewStage.QA;
                    break;
                default:
                    return Json("Unrecognized document stage", JsonRequestBehavior.AllowGet);
            }
            var doc = new CIMSDocument(examNumber, docType, name, step, stage, file.Extension);
            return Json(doc.ApproveWorkflowForm(false, CurrentUserModel.AccountID), JsonRequestBehavior.AllowGet);
        }

        private IEnumerable<BinarySolrDocument> GetAllCaseManagementFilesToIngest()
        {
            foreach (var caseFolder in GetCaseFolders())
            {
                foreach (var file in Directory
                    .EnumerateFiles(caseFolder, "*.*", SearchOption.AllDirectories)
                    .Where(z => (z.Contains(DocumentTypeConstants.PFX_FORM1_SERVICE_RQST) ||
                                 z.Contains("FINAL") && FinalFormstoIngest.Any(z.Contains)) &&
                                FileExtensionsToIngest.Any(z.EndsWith)))
                {
                    BinarySolrDocument solrDoc = null;
                    try
                    {
                        solrDoc = new BinarySolrDocument(file, DB11);
                    }
                    catch (DCFLDocException ex)
                    {
                        LogFromController(ex.Message);
                        continue;
                    }
                    yield return solrDoc;
                }
            }
        }

        private IEnumerable<string> GetCaseFolders()
        {
            foreach (var rootCaseFolder in Directory
                .EnumerateDirectories(ConfigurationManager.AppSettings["DocumentRoot"], ".",
                    SearchOption.TopDirectoryOnly)
                .Where(z => ParentCaseFoldersCIMSTracks.Any(z.Contains)))
            {
                foreach (var caseYearFolder in Directory
                    .EnumerateDirectories(rootCaseFolder + "\\", ".", SearchOption.TopDirectoryOnly)
                    .Where(z => CaseYearsToIngest.Any(z.EndsWith)))
                {
                    yield return caseYearFolder;
                }
            }
        }

        private string FindItemPath(string partialPath, out string parentPath)
        {
            string ret = null;
            parentPath = null;
            partialPath = HttpUtility.UrlDecode(partialPath);
            string[] pathTokens = partialPath.Split('/');
            string trackingNumber = pathTokens[1];
            if (trackingNumber.StartsWith("DCFL-"))
            {
                string[] tokens = trackingNumber.Split('-');
                string year = tokens[1];
                string exam = tokens[2];
                DirectoryInfo di = new DirectoryInfo(cmRoot);
                DirectoryInfo subFolder = di
                    .GetDirectories().FirstOrDefault(x => x.Name.StartsWith("Case Folders") && x.Name.Contains(year));
                if (subFolder != null)
                {
                    subFolder = subFolder.GetDirectories().FirstOrDefault(x => x.Name.StartsWith(year));
                    if (subFolder != null)
                    {
                        int num;
                        if (Int32.TryParse(exam.Substring(1), out num))
                        {
                            foreach (DirectoryInfo nextFolder in subFolder.GetDirectories()
                                .Where(x => x.Name.Contains("Cases")))
                            {
                                try
                                {
                                    string range = nextFolder.Name.Substring(nextFolder.Name.LastIndexOf(' '));
                                    string[] rangeTokens = range.Split('-');
                                    int low;
                                    int high;
                                    if (Int32.TryParse(rangeTokens[0], out low) &&
                                        Int32.TryParse(rangeTokens[1], out high) &&
                                        num >= low &&
                                        num <= high)
                                    {
                                        parentPath = nextFolder.FullName;
                                        ret = nextFolder.FullName + partialPath.Replace(@"/", @"\");
                                        break;
                                    }
                                }
                                catch
                                {
                                }
                            }
                        }
                    }
                }
            }

            return ret;
        }

        [HttpPost]
        public ActionResult Tree(string dir)
        {
            List<FileViewModel> ret = new List<FileViewModel>();

            string parentPath;
            string fullDir = FindItemPath(dir, out parentPath);
            DirectoryInfo theDir = new DirectoryInfo(fullDir);
            if (theDir.Exists)
            {
                foreach (var x in theDir.EnumerateDirectories().OrderBy(x => x.Name))
                {
                    ret.Add(new FileViewModel()
                    {
                        Name = x.Name,
                        IsDirectory = true,
                        Path = x.FullName.Substring(parentPath.Length).Replace(@"\", @"/")
                    });
                }

                foreach (var x in theDir.EnumerateFiles().Where(x => x.Name.EndsWith("pdf")).OrderBy(x => x.Name))
                {
                    ret.Add(new FileViewModel()
                    {
                        Name = x.Name,
                        Path = x.FullName.Substring(parentPath.Length).Replace(@"\", @"/"),
                        IconClass = x.Name.ToLower().EndsWith("pdf") ? "ext_pdf" : "ext_txt"
                    });
                }
            }

            return PartialView("DocumentsTree", ret);
        }

        public ActionResult Documents(int examID)
        {
            var exam = DB11.exam.Find(examID);
            string trackingNumber = exam.exam_number;
            ViewData["trackingNumber"] = trackingNumber;
            ViewData["examID"] = examID;
            string parentPath;
            string rootDir = FindItemPath(@"/" + trackingNumber, out parentPath);
            var iaLanPaths = new List<TreeViewItemModel>();
            if (exam.exam_type_id == (int)(ExamType.Intrusions))
            {
                // check what's on ialan.
                var ret = CIMSController.GetToIAService($"IAExam/GetPublishedDocumentsForExam?examNumber={exam.exam_number}");
                if (!string.IsNullOrEmpty(ret))
                {
                    var o = JsonConvert.DeserializeObject<JArray>(ret);
                    var service = ConfigurationManager.AppSettings["IAService"];
                    ViewBag.IAService = service;
                    var parents = new Dictionary<string, List<TreeViewItemModel>>();
                    foreach (var item in o)
                    {
                        var path = item.SelectToken("path").Value<string>();
                        var split = path.Split('\\');
                        var name = split.Last();
                        var parent = split[split.Length - 2];
                        var model = new TreeViewItemModel()
                        {
                            Id = path,
                            Text = name,
                            Url = HttpUtility.HtmlEncode($"{service}/Search/Download?path={path}"),
                            SpriteCssClass = "pdf",
                            Encoded = false
                        };
                        if (parents.ContainsKey(parent))
                        {
                            parents[parent].Add(model);
                        }
                        else
                        {
                            parents[parent] = new List<TreeViewItemModel>() { model };
                        }
                    }
                    foreach (var parent in parents)
                    {
                        var isRoot = parent.Key.Equals(exam.exam_number, StringComparison.OrdinalIgnoreCase);
                        if (!isRoot)
                        {
                            var node = new TreeViewItemModel()
                            {
                                Id = Guid.NewGuid().ToString(),
                                Text = parent.Key,
                                HasChildren = true,
                                SpriteCssClass = "folder",
                                Expanded = true,
                                Items = parent.Value
                            };
                            iaLanPaths.Add(node);
                        }
                        else
                        {
                            iaLanPaths.AddRange(parent.Value);
                        }
                    }
                }
            }
            if (rootDir == null && !iaLanPaths.Any())
            {
                return PartialView("NoDocuments");
            }

            if (rootDir != null)
            {
                DirectoryInfo theDir = new DirectoryInfo(rootDir);
                if (!theDir.Exists && !iaLanPaths.Any())
                {
                    ViewData["rootPath"] = theDir.FullName;
                    return PartialView("NoDocuments");
                }
            }

            var docs = exam.exam_status_id == (int)ExamStatus.Done
                ? ExtensionMethods.BuildTreeViewItemModels(
                    CIMSDocument.GetFileSharePathFromDCFLTrackingNumber(trackingNumber))
                : ExtensionMethods.BuildTreeViewItemModels(
                    CIMSDocument.GetFileSharePathFromDCFLTrackingNumber(trackingNumber),
                    new Func<FileInfo, Tuple<bool, string>>(CIMSDocument.IsMostRecentFormVersion));
            docs.AddRange(iaLanPaths);
            ViewBag.Documents = docs;
            ViewBag.IsExamDone = exam.exam_status_id == (int)ExamStatus.Done;
            return PartialView("Documents");


        }

        private List<SelectListItem> GetExaminers(exam_standard exam, string documentType)
        {
            var examiners = new List<SelectListItem>();
            examiners.Add(new SelectListItem
            {
                Text = exam.exam.account?.name.FormatName(),
                Value = exam.exam.account?.surname.ToUpper()
            });
            examiners = examiners.Concat(exam.exam.exam_activity
                .Where(x => x.activity_id == (int)ActivityType._Perform_Forensic_Examination &&
                            x.account_id != exam.exam.lead_examiner_id && x.skipped != true).ToList()
                .Select(x => new SelectListItem
                {
                    Text = x.account.name.FormatName(),
                    Value = x.account.surname.ToUpper()
                }).DistinctBy(z => z.Value)).ToList();
            examiners = examiners.Concat(new CIMSDocument(exam.exam.exam_number, documentType)
                .GetForm9SupplementalValues().Select(z => new SelectListItem
                {
                    Text = z,
                    Value = z
                }
                )).DistinctBy(z => z.Value).ToList();
            return examiners;
        }


        private List<SelectListItem> GetTechnicians(exam_standard exam, string documentType)
        {
            var technicians = new List<SelectListItem>();
            technicians.Add(new SelectListItem
            {
                Text = exam.account?.name.FormatName(),
                Value = exam.account?.surname.ToUpper()
            });
            technicians = technicians.Concat(exam.exam.exam_activity
                .Where(x => x.activity_id == (int)ActivityType._Perform_Forensic_Acquisition &&
                            x.account_id != exam.lead_acquisition_id && x.skipped != true).ToList()
                .Select(x => new SelectListItem
                {
                    Text = x.account.name.FormatName(),
                    Value = x.account.surname.ToUpper()
                }).DistinctBy(z => z.Value)).ToList();
            technicians = technicians.Concat(new CIMSDocument(exam.exam.exam_number, documentType)
                .GetForm9SupplementalValues().Select(z => new SelectListItem
                {
                    Text = z,
                    Value = z
                }
                )).DistinctBy(z => z.Value).ToList();
            return technicians;
        }

        private List<SelectListItem> GetPreProcessors(exam_standard exam, string documentType)
        {
            var examiners = new List<SelectListItem>();
            examiners = examiners.Concat(exam.exam.exam_activity
                .Where(x => (x.activity_id == (int)ActivityType._Perform_PreProcessing) && x.skipped != true).ToList()
                .Select(x => new SelectListItem
                {
                    Text = x.account.name.FormatName(),
                    Value = x.account.surname.ToUpper()
                }).DistinctBy(z => z.Value)).ToList();
            examiners = examiners.Concat(new CIMSDocument(exam.exam.exam_number, documentType)
                .GetForm9SupplementalValues().Select(z => new SelectListItem
                {
                    Text = z,
                    Value = z
                }
                )).DistinctBy(z => z.Value).ToList();
            if (!examiners.Any())
            {
                examiners.Add(new SelectListItem { Text = null, Value = null });
            }
            return examiners;
        }

        public JsonResult GetDocumentOptions(string documentType, int examId)
        {
            var options = DocumentTypeConstants.LookUpDocumentOptions[documentType];
            var exam = DB11.exam_standard.FirstOrDefault(z => z.exam_id == examId);
            switch (options)
            {
                case DocumentTypeConstants.DocumentOptionTypes.None:
                    return Json(null, JsonRequestBehavior.AllowGet);
                case DocumentTypeConstants.DocumentOptionTypes.Deviation:
                    var doc = new CIMSDocument(DB11.exam.First(z => z.exam_id == examId).exam_number, documentType);
                    var deviations = doc.GetUnApprovedDeviations();
                    var newDeviation = new
                    {
                        Value = DocumentTypeConstants.NEW_DEVIATION_VALUE,
                        Text = DocumentTypeConstants.NEW_DEVIATION_LABEL,
                        Label = "Deviation: "
                    };
                    if (deviations.Count == 0) return Json(newDeviation, JsonRequestBehavior.AllowGet);
                    var returnDeviations = new List<object>();
                    returnDeviations.Add(newDeviation);
                    returnDeviations.AddRange(deviations.Select(z => new { z.Value, z.Text, Label = "Deviation: " }));
                    return Json(returnDeviations, JsonRequestBehavior.AllowGet);
                case DocumentTypeConstants.DocumentOptionTypes.LabarotoryReportCaveats:
                    return Json(DocumentTypeConstants.LabarotoryReportCaveats.Select(z => new { z.Value, z.Text, Label = "Caveats: " }),
                        JsonRequestBehavior.AllowGet);
                case DocumentTypeConstants.DocumentOptionTypes.TechnicalReviewStages:
                    return Json(DocumentTypeConstants.TechnicalReviewStages.Select(z => new { z.Value, z.Text, Label = "Stage: " }),
                        JsonRequestBehavior.AllowGet);
                case DocumentTypeConstants.DocumentOptionTypes.TechnicalSupportDocumentTypes:
                    return Json(DocumentTypeConstants.TechnicalSupportDocumentTypes.Select(z => new { z.Value, z.Text, Label = "Document Type: " }),
                        JsonRequestBehavior.AllowGet);
                case DocumentTypeConstants.DocumentOptionTypes.Examiner:
                    if (exam == null) return Json(null, JsonRequestBehavior.AllowGet);
                    var examiners = GetExaminers(exam, documentType);
                    return Json(examiners.Select(z => new {Value = z.Value, Text = z.Text, Label = "Examiner: "}),
                        JsonRequestBehavior.AllowGet);
                case DocumentTypeConstants.DocumentOptionTypes.Technician:
                    if (exam == null) return Json(null, JsonRequestBehavior.AllowGet);
                    var technicians = GetTechnicians(exam, documentType);
                    return Json(technicians.Select(z => new {Value = z.Value, Text = z.Text, Label = "Technician: "}),
                        JsonRequestBehavior.AllowGet);
                case DocumentTypeConstants.DocumentOptionTypes.Fast:
                    if (exam == null) return Json(null, JsonRequestBehavior.AllowGet);
                    var preproessors = GetPreProcessors(exam, documentType);
                    return Json(preproessors.Select(z => new { Value = z.Value, Text = z.Text, Label = "Pre-Processor: " }),
                        JsonRequestBehavior.AllowGet);
                case DocumentTypeConstants.DocumentOptionTypes.Correspondence:
                    if (exam == null) return Json(null, JsonRequestBehavior.AllowGet);
                    var MCexaminers = GetExaminers(exam, documentType);
                    var IEtechnicians = GetTechnicians(exam, documentType);
                    var pp = GetPreProcessors(exam, documentType);
                    var distinct = MCexaminers.Where(x => x.Value != null).Concat(IEtechnicians).Concat(pp).DistinctBy(x => x.Value).ToList();
                    return Json(distinct.Select(z => new { Value = z.Value, Text = z.Text, Label = "Examiner: " }),
                        JsonRequestBehavior.AllowGet);
                default:
                    return Json(null, JsonRequestBehavior.AllowGet);
            }
        }

        public static string cmRoot;
        private static readonly Object solrLock = new Object();
        private static readonly object IngestNewlyFinishedExamsLock = new object();

        private static readonly List<string> FinalFormstoIngest = new List<string>
        {
            DocumentTypeConstants.PFX_FORM4_LAB_REPORT, DocumentTypeConstants.PFX_FORM9_FORENSIC_NOTES,
            DocumentTypeConstants.PFX_FORM9_TECHNICIAN_NOTES, DocumentTypeConstants.PFX_FORM9_FAST
        };
        private static readonly List<string> FileExtensionsToIngest = new List<string>
        {
            "pdf", "doc", "docx", "html", "htm"
        };
        private static readonly List<string> ParentCaseFoldersCIMSTracks = new List<string>
        {
            "Case Folders 2014-2015", "Case Folders 2016-2017", "Case Folders 2018-2019", "Case Folders 2020-2021"
        };
        private static readonly List<string> CaseYearsToIngest = new List<string>
        {
            "2015 Cases", "2016 Cases", "2017 Cases", "2018 Cases", "2019 Cases", "2020 Cases", "2021 Cases"
        };
    }
}