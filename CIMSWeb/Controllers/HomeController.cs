﻿using System;
using System.Linq;
using System.Web.Mvc;
using CIMSWeb.Models;
using CIMSWeb.Workflow;

namespace CIMSWeb.Controllers
{
    public class HomeController : CIMSController
    {
        public ActionResult Index()
        {
            //List<account_permission> permissions = DB11.account_permission.Where(x => x.account_id == a.account_id).ToList();
            //return PartialView(permissions);
            //return View(permissions);
            
            CIMSData.Database.account userAcct = DB11.account.Where(x => x.account_id == CurrentUserModel.AccountID).FirstOrDefault();

            //Retrieve bookmarked HomePage if it exists 
            var homePage = userAcct.home_page_url; 

            if ((homePage == null) || (homePage == ""))
            {
                // set default homepage based on permission/role
                homePage = "/Dashboards/User";
                var permission = userAcct.account_permission.FirstOrDefault();
                if (permission != null)
                {
                    if (permission.group_id == (int)Group.FrontOffice)
                    {
                        homePage = "/Dashboards/Lab";
                    }
                    else
                    {
                        if (permission.account_role_id == (int) Role.Admin)
                        {
                            homePage = "/Dashboards/Section/" + permission.group_id;
                        }
                    }
                }
            }
            ViewBag.URLlocation = homePage;

            return View();
        }

        public ActionResult Home()
        {
            CIMSData.Database.account userAcct = DB11.account.Where(x => x.account_id == CurrentUserModel.AccountID).FirstOrDefault();

            //Retrieve bookmarked HomePage if it exists
            var homePage = userAcct.home_page_url;

            if ((homePage == null) || (homePage == ""))
            {
                // set default homepage based on permission/role
                homePage = "/Dashboards/User";
                var permission = userAcct.account_permission.FirstOrDefault();
                if (permission != null)
                {
                    if (permission.group_id == (int)Group.FrontOffice)
                    {
                        homePage = "/Dashboards/Lab";
                    }
                    else
                    {
                        if (permission.account_role_id == (int)Role.Admin)
                        {
                            homePage = "/Dashboards/Section/" + permission.group_id;
                        }
                    }
                }
            }

            return Json(new { homePage = homePage }, JsonRequestBehavior.AllowGet);
        }

        #region impersonation

        public ActionResult ChangeUser()
        {
            return PartialView(DB11.account.OrderBy(x => x.name).ToList().Select(x => new AccountViewModel(x)).ToList());
        }

        [HttpPost]
        public ActionResult ChangeUserGroupRole(int group, int role, string source)
        {
            var ap = DB11.account_permission.FirstOrDefault(x => x.group_id == group && x.account_role_id <= role && x.account.enabled == true);
            if (ap != null)
            {
                return ChangeUser(ap.account_id, source);
            }
            return null;
        }

        [HttpPost]
        public ActionResult ChangeUser(int accountIDToImpersonate, string source)
        {
            CIMSData.Database.account dbAccount = DB11.account.Find(CurrentUserModel.ActualAccountID);
            AuditUpdate(dbAccount);
            dbAccount.impersonated_account_id = accountIDToImpersonate;
            DB11.Entry(dbAccount).State = System.Data.Entity.EntityState.Modified;
            DB11.SaveChanges();
            Session["Account"] = new AccountViewModel(dbAccount);
            return Redirect(source);
        }

        #endregion

        private static DateTime? buildDate;

        public static DateTime GetBuildDate()
        {
            if (!buildDate.HasValue)
            {
                var path = System.Reflection.Assembly.GetExecutingAssembly().Location;
                byte[] buf = new byte[2048];
                using (var s = System.IO.File.OpenRead(path))
                {
                    s.Read(buf, 0, buf.Length);
                }
                var offset = BitConverter.ToInt32(buf, 60); // PE header offset
                var secondsSince1970 = BitConverter.ToInt32(buf, offset + 8); // Linker timestamp offset
                var epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                var linkTimeUTC = epoch.AddSeconds(secondsSince1970);
                buildDate = TimeZoneInfo.ConvertTimeFromUtc(linkTimeUTC, TimeZoneInfo.Local);
            }
            return buildDate.Value;
        }
        
        public ActionResult SetHomePage(string homepageURL)
        {
            CIMSData.Database.account userAcct = DB11.account.Where(x => x.account_id == CurrentUserModel.AccountID).FirstOrDefault();
            AuditUpdate(userAcct);

            homepageURL = homepageURL.Substring(homepageURL.IndexOf("#")+1);
            userAcct.home_page_url = homepageURL;
            DB11.Entry(userAcct).State = System.Data.Entity.EntityState.Modified;
            DB11.SaveChanges();
            return Json( new { homePage = homepageURL });
        }
    }
}