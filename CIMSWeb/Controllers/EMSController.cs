﻿using CIMSData.Database;
using CIMSWeb.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Web.Mvc;
using Newtonsoft.Json;
using CIMSWeb.Workflow;
using System.Web.WebPages;
using System.Text;

namespace CIMSWeb.Controllers
{
    public class EMSController : CIMSController
    {
        public static object FormOneIngestLock = new object();

        [HttpPost]
        public ActionResult EMSFormOneIngest()
        {
            lock (FormOneIngestLock)
            {
                try
                {
                    Stream req = Request.InputStream;
                    req.Seek(0, SeekOrigin.Begin);
                    string json = new StreamReader(req).ReadToEnd();
                    var emsExam = JsonConvert.DeserializeObject<EMSForm1>(json);
                    var form1 = emsExam.form1_contents;
                    // Create the intrusion sub exam
                    exam_intrusion newExamIntrusion = new exam_intrusion
                    {
                        is_lab_source = false,
                        background = form1.CaseSummary,
                        submission_date = DateTime.Now,
                    };
                    if (!string.IsNullOrWhiteSpace(form1.InitialCaseNumber))
                    {
                        newExamIntrusion.additional_info = $"Follow on of Exam {form1.InitialCaseNumber}";
                    }

                    // Check if the case already exists
                    var emptyCaseNumber = string.IsNullOrWhiteSpace(form1.AgencyCaseNumber);
                    @case emsCase = <EMAIL>(c => c.case_number == form1.AgencyCaseNumber);
                    var existingCase = emsCase != null && !emptyCaseNumber; // flag for setting the exam number to be -A style.
                    // Need to create a new case
                    if (emsCase == null)
                    {
                        emsCase = new @case
                        {
                            case_number = form1.AgencyCaseNumber,
                            classification_id = GetClassificationId(form1.ClassificationType)
                        };

                        // Check if the case agent already exists
                        contact caseAgent = DB11.contact.Where(z => z.last_name == form1.LastName && z.email == form1.Email)
                            .FirstOrDefault();

                        // Need to create the case agent
                        if (caseAgent == null)
                        {
                            caseAgent = new contact
                            {
                                commercial = form1.PhoneNumber,
                                country = DB11.country.First(z => z.name == "United States"),
                                email = form1.Email,
                                first_name = form1.FirstName,
                                last_name = form1.LastName,
                                address1 = form1.Address
                            };
                        }

                        emsCase.contact = caseAgent;
                        if (!string.IsNullOrWhiteSpace(form1.AlternativeEmail) && form1.Email.ToLowerInvariant() != form1.AlternativeEmail.ToLowerInvariant())
                        {
                            // External submission likely. Add the external contact as the prosecutor.
                            contact prosecutor = DB11.contact
                                .FirstOrDefault(z => z.last_name == form1.AlternativeLastName && z.email == form1.AlternativeEmail);
                            if (prosecutor == null)
                            {
                                prosecutor = new contact
                                {
                                    commercial = form1.AlternativePhoneNumber,
                                    country = DB11.country.First(z => z.name == "United States"),
                                    email = form1.AlternativeEmail,
                                    first_name = form1.AlternativeFirstName,
                                    last_name = form1.AlternativeLastName,
                                };
                            }

                            emsCase.contact1 = prosecutor;
                        }

                        // Check if the agency org exists
                        agency_org agencyOrg =
                            (from org in DB11.agency_org
                             where org.agency.name.ToLower() == form1.Agency.Trim().ToLower()
                                   && org.agency_unit.name.ToLower() == form1.Unit.Trim().ToLower()
                             select org).FirstOrDefault();

                        // Need to create a new agency org
                        if (agencyOrg == null)
                        {
                            agencyOrg = new agency_org();

                            // Check if the agency exists
                            agency emsAgency =
                                DB11.agency.FirstOrDefault(z => z.name.ToLower() == form1.Agency.Trim().ToLower());

                            // Need to create a new agency
                            if (emsAgency == null)
                            {
                                emsAgency = new agency { name = form1.Agency.Trim() };
                            }

                            // Check if the unit exists
                            agency_unit emsUnit =
                                DB11.agency_unit.FirstOrDefault(z => z.name.ToLower() == form1.Unit.Trim().ToLower());

                            // Need to create a new agency
                            if (emsUnit == null)
                            {
                                emsUnit = new agency_unit
                                {
                                    name = form1.Unit.Trim()
                                };
                            }

                            agencyOrg.agency = emsAgency;
                            agencyOrg.agency_unit = emsUnit;
                            agencyOrg.contact = caseAgent;
                        }

                        emsCase.agency_org = agencyOrg;
                    }

                    DateTime now = DateTime.Now;
                    int year = now.Year;
                    string identifier = "I";
                    if (now.Month >= 10) // Fiscal year adjustment
                    {
                        year++;
                    }
                    string latestExamNumber = "";
                    if (existingCase)
                    {
                        //Get earliest exam unless we have exams that have already have new appended lettering system.
                        var earliestExamNumForCase = DB11.exam.Where(x => x.case_id == emsCase.case_id).OrderByDescending(x => x.exam_number.Length).ThenBy(x => x.exam_number).Select(x => x.exam_number).FirstOrDefault();
                        if (!string.IsNullOrEmpty(earliestExamNumForCase) && char.IsLetter(earliestExamNumForCase[earliestExamNumForCase.Length - 1]))
                        {//If we have exam with new lettering system appended, get the latest one to increment the next exam number.
                            earliestExamNumForCase = DB11.exam.Where(x => x.case_id == emsCase.case_id).OrderByDescending(x => x.exam_number.Length).ThenByDescending(x => x.exam_number).Select(x => x.exam_number).FirstOrDefault();
                        }
                        var caseHasFollowOnExamAlready = char.IsLetter(earliestExamNumForCase[earliestExamNumForCase.Length - 1]);
                        if (caseHasFollowOnExamAlready)//Make sure this is at least 2nd exam under this case and already has an appended -A or -B.
                        {
                            var indexOfFollowOnLetters = earliestExamNumForCase.LastIndexOf("-") + 1;
                            var appended_chars = earliestExamNumForCase.Substring(indexOfFollowOnLetters);
                            var new_appended_chars = string.Copy(appended_chars);
                            for (var i = appended_chars.Length - 1; i >= 0; i--)
                            {
                                StringBuilder sb = new StringBuilder(new_appended_chars);
                                if (appended_chars[i] == 'Z')
                                {
                                    sb.Remove(i, 1);
                                    sb.Insert(i, 'A');
                                    new_appended_chars = sb.ToString();
                                    if (i == 0)//Add another character. So Z becomes AA.  Only when all follow-on letters are Zs
                                    {
                                        new_appended_chars = string.Concat(new_appended_chars, "A");
                                        break;
                                    }
                                }
                                else
                                {//Just increment character and stop.  B becomes C. ZAB becomes ZAC. ZAZ becomes ZBA
                                    var char_to_increment = appended_chars[i];
                                    sb.Remove(i, 1);
                                    sb.Insert(i, ++char_to_increment);
                                    new_appended_chars = sb.ToString();
                                    break;
                                }
                            }
                            latestExamNumber = string.Concat(earliestExamNumForCase.Substring(0, indexOfFollowOnLetters), new_appended_chars);
                        }
                        else
                        {//Second exam under the case.  Append '-A'
                            latestExamNumber = string.Concat(earliestExamNumForCase, "-A");
                        }
                    }
                    else
                    {
                        var numbers = DB11.exam.Where(x => x.exam_number.Contains(identifier) && x.exam_number.Contains(year.ToString())).OrderByDescending(x => x.exam_number).Select(x => x.exam_number).AsEnumerable();
                        foreach (var item in numbers)
                        {
                            if (char.IsNumber(item.Last()))
                            {
                                var split = item.Split('-');
                                var sliced = new string(split.Last().Skip(1).ToArray());
                                var parseAndIncrement = int.Parse(sliced) + 1;
                                latestExamNumber = string.Format("DCFL-{0}-{1}{2:D6}", year, identifier, parseAndIncrement);
                                break;
                            }
                        }
                    }
                    if (string.IsNullOrWhiteSpace(latestExamNumber))
                    {
                        latestExamNumber = $"DCFL-{year}-{identifier}000001";
                    }
                    // Create the new exam
                    exam newExam = new exam
                    {
                        @case = emsCase,
                        exam_category_id = GetExamCategoryId(form1.ExamType, form1.ExamProcess),
                        exam_intrusion = newExamIntrusion,
                        exam_number = latestExamNumber,
                        exam_status_id = 6,
                        exam_type_id = 4,
                        group_id = 8
                    };
                    // Active
                    // Pre-Exam type
                    // Intrusions group

                    const int maxQuantity = 500;
                    foreach (var item in form1.SubmittedMedia ?? new SubmittedMedium[0])
                    {
                        var quantity = int.Parse(item.Quantity);
                        if (quantity <= 0)
                        {
                            quantity = 1;
                        }
                        quantity = Math.Min(quantity, maxQuantity);
                        var type = DB11.evidence_type.First(x => x.evidence_type1.Equals(item.EvidenceType, StringComparison.OrdinalIgnoreCase));
                        for (var i = 0; i < quantity; i++)
                        {
                            var e = new evidence() { evidence_type_id = type.evidence_type_id, evidence_description = item.Description, @case = emsCase };
                            DB11.evidence.Add(e);
                            var ee = new exam_evidence() { evidence = e };
                            newExam.exam_evidence.Add(ee);
                            CIMSData.Database.evidence_history hist = new CIMSData.Database.evidence_history()
                            {
                                evidence = e,
                                date_logged = DateTime.Now,
                                entered_by = 185,
                                evidence_status_id = 5
                            };
                            DB11.evidence_history.Add(hist);
                        }
                    }

                    foreach (var pw in form1.Passwords ?? new string[0])
                    {
                        exam_note note = new exam_note
                        {
                            date_logged = DateTime.Now,
                            entered_by = CurrentUserModel.AccountID
                        };

                        note.note = "Password: " + pw;

                        newExam.exam_note.Add(note);
                    }

                    // Save all of this to the database
                    DB11.exam.Add(newExam);
                    if (emptyCaseNumber)
                    {
                        var exists = <EMAIL>(x => x.case_number == latestExamNumber);
                        if (exists == null)
                        {
                            emsCase.case_number = latestExamNumber;
                        }
                        else
                        {
                            emsCase.case_number = new string(Guid.NewGuid().ToString().Take(6).ToArray());
                        }
                    }
                    DB11.SaveChanges();

                    // Start the workflows for the new exams
                    var ww = new PreExamCoordinationWorkflow(newExam, DB11);
                    ww.StartWorkflow(Group.Intake, Role.User);
                    return Json(new { ExamNumber = latestExamNumber, CaseNumber = emsCase.case_number });
                }
                catch (Exception e)
                {
                    LogFromController($"Form1 ingest from EMS failed.");
                    LogFromController($"{e.Message}");
                    LogFromController($"{e.StackTrace}");
                    throw;
                }
            }
        }

        [HttpPost]
        public ActionResult EMSIngestFromAmp()
        {
            try
            {
                Stream req = Request.InputStream;
                req.Seek(0, SeekOrigin.Begin);
                string json = new StreamReader(req).ReadToEnd();
                var emsExam = JsonConvert.DeserializeObject<EMSViewModel>(json);

                // Check if its a test submission - If so, CIMS doesn't ingest it.
                if (emsExam.TestSubmission)
                {
                    return new HttpStatusCodeResult(HttpStatusCode.OK);
                }

                // Check if the exam was already created
                exam existing = DB11.exam.FirstOrDefault(x => x.exam_number == emsExam.ExamNumber);
                if (existing != null)
                {
                    return new HttpStatusCodeResult(HttpStatusCode.Conflict);
                }

                // IALAN provides these dates in EST. ExLAN is UTC.
                var submissionDate = TimeZoneInfo.ConvertTimeToUtc(emsExam.SubmissionDate,
                    TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time"));
                var approvalDate = TimeZoneInfo.ConvertTimeToUtc(emsExam.ApprovalDate,
                    TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time"));
                // Create the intrusion sub exam
                exam_intrusion newExamIntrusion = new exam_intrusion
                {
                    additional_info = emsExam.AdditionalInfo,
                    background = emsExam.Background,
                    directorate_id = GetDirectorateId(emsExam.Directorate),
                    is_lab_source = false,
                    submission_date = submissionDate,
                    approval_date = approvalDate,
                    high_priority = emsExam.HighPriority,
                    exam_title = emsExam.ExamTitle
                };

                // Check if the case already exists
                @case emsCase = <EMAIL>(c => c.case_number == emsExam.CaseNumber);

                // Need to create a new case
                if (emsCase == null)
                {
                    emsCase = new @case
                    {
                        case_number = emsExam.CaseNumber,
                        classification_id = GetClassificationId(emsExam.ClassificationType)
                    };

                    // Check if the case agent already exists
                    contact caseAgent = DB11.contact.Where(z => z.last_name == emsExam.PrimaryAnalystLastName && z.email == emsExam.PrimaryAnalystEmail)
                        .FirstOrDefault();

                    // Need to create the case agent
                    if (caseAgent == null)
                    {
                        caseAgent = new contact
                        {
                            commercial = emsExam.PrimaryAnalystPhoneNumber,
                            country = DB11.country.First(z => z.name == "United States"),
                            email = emsExam.PrimaryAnalystEmail,
                            first_name = emsExam.PrimaryAnalystFirstName,
                            last_name = emsExam.PrimaryAnalystLastName,
                        };
                    }

                    emsCase.contact = caseAgent;
                    if (emsExam.Email.ToLowerInvariant() != emsExam.PrimaryAnalystEmail.ToLowerInvariant())
                    {
                        // External submission likely. Add the external contact as the prosecutor.
                        contact prosecutor = DB11.contact
                            .FirstOrDefault(z => z.last_name == emsExam.LastName && z.email == emsExam.Email);
                        if (prosecutor == null)
                        {
                            prosecutor = new contact
                            {
                                commercial = emsExam.PhoneNumber,
                                country = DB11.country.First(z => z.name == "United States"),
                                email = emsExam.Email,
                                first_name = emsExam.FirstName,
                                last_name = emsExam.LastName,
                            };
                        }

                        emsCase.contact1 = prosecutor;
                    }

                    // Check if the agency org exists
                    agency_org agencyOrg =
                        (from org in DB11.agency_org
                            where org.agency.name.ToLower() == emsExam.Agency.Trim().ToLower()
                                  && org.agency_unit.name.ToLower() == emsExam.Agency.Trim().ToLower()
                            select org).FirstOrDefault();

                    // Need to create a new agency org
                    if (agencyOrg == null)
                    {
                        agencyOrg = new agency_org();

                        // Check if the agency exists
                        agency emsAgency =
                            DB11.agency.FirstOrDefault(z => z.name.ToLower() == emsExam.Agency.Trim().ToLower());

                        // Need to create a new agency
                        if (emsAgency == null)
                        {
                            emsAgency = new agency {name = emsExam.Agency.Trim()};
                        }

                        // Check if the unit exists
                        agency_unit emsUnit =
                            DB11.agency_unit.FirstOrDefault(z => z.name.ToLower() == emsExam.Agency.Trim().ToLower());

                        // Need to create a new agency
                        if (emsUnit == null)
                        {
                            emsUnit = new agency_unit
                            {
                                name = emsExam.Agency.Trim()
                            };
                        }

                        agencyOrg.agency = emsAgency;
                        agencyOrg.agency_unit = emsUnit;
                        agencyOrg.contact = caseAgent;
                    }

                    emsCase.agency_org = agencyOrg;
                }

                // Create the new exam
                exam newExam = new exam
                {
                    @case = emsCase,
                    date_opened = DateTime.Now,
                    exam_category_id = GetExamCategoryId(emsExam.ExamType, emsExam.ExamProcess),
                    exam_intrusion = newExamIntrusion,
                    exam_number = emsExam.ExamNumber,
                    exam_status_id = 6,
                    exam_type_id = 1,
                    group_id = 8
                };
                // Active
                // Intrusions type
                // Intrusions group

                // Save all of the files as notes on the exam
                foreach (var file in emsExam.Files ?? new List<string>())
                {
                    exam_note note = new exam_note
                    {
                        date_logged = emsExam.SubmissionDate,
                        entered_by = CurrentUserModel.AccountID
                    };
                    
                    note.note = "Submitted File: " + file;

                    newExam.exam_note.Add(note);
                }

                foreach (var pw in emsExam.Passwords ?? new List<string>())
                {
                    exam_note note = new exam_note
                    {
                        date_logged = emsExam.SubmissionDate,
                        entered_by = CurrentUserModel.AccountID
                    };
                    
                    note.note = "Password: " + pw;

                    newExam.exam_note.Add(note);
                }

                // Save all of this to the database
                DB11.exam.Add(newExam);
                DB11.SaveChanges();

                // Start the workflows for the new exams
                IntrusionsWorkflow workflow = new IntrusionsWorkflow(newExam, DB11);
                workflow.StartWorkflowEMS(CurrentUserModel);
                return new HttpStatusCodeResult(HttpStatusCode.Created);
            }
            catch (Exception e)
            {
                LogFromController($"EMS Ingest from AMP uncaught exception.");
                LogFromController($"{e.Message}");
                LogFromController($"{e.StackTrace}");
                throw;
            }
        }

        public ActionResult UpdateEMS()
        {
            EMSStatus.UpdateEMS();
            return new EmptyResult();
        }

        private int? GetDirectorateId(string directorate)
        {
            // Get the directorate for this exam
            int? directorateId = null;

            if (directorate.Contains("CFL"))
            {
                directorateId = 1;
            }
            else if (directorate.Contains("AG"))
            {
                directorateId = 2;
            }
            else if (directorate.Contains("DCISE"))
            {
                directorateId = 3;
            }

            return directorateId;
        }

        private int GetClassificationId(string c)
        {
            var id = DB11.classification.FirstOrDefault(x => x.level.Equals(c, StringComparison.OrdinalIgnoreCase));
            if (id != null)
            {
                return id.classification_id;
            }
            var cuiId = DB11.classification.FirstOrDefault(x => x.level == "CUI");
            if (cuiId != null)
            {
                return c.ToLowerInvariant().StartsWith("cui") ? cuiId.classification_id : 1;
            }
            return 1;
        }


        private int GetExamCategoryId(string examType, string examProcess)
        {
            examType = examType.Trim();
            examProcess = examProcess.Trim();
            string category = "";
            string subcategory = "";
            switch (examType)
            {
                case "Systems":
                    category = "SYSTEMS";
                    switch (examProcess) 
                    {
                        case "Standard Exam":
                            subcategory = "STANDARD";
                            break;
                        case "Triage Exam":
                            subcategory = "TRIAGE";
                            break;
                        case "Focused Exam":
                            subcategory = "FOCUSED";
                            break;
                    }
                    break;
                case "Malicious Files":
                    category = "MALWARE";
                    switch (examProcess)
                    {
                        case "Indicator Based Exam":
                            subcategory = "INDICATOR BASED EXAM";
                            break;
                        case "Operational Leads Exam":
                            subcategory = "OPERATIONAL LEADS EXAM";
                            break;
                        case "Focused Analysis Exam":
                            subcategory = "FOCUSED ANALYSIS EXAM";
                            break;
                        case "Internal Indicator Exam":
                            subcategory = "INTERNAL INDICATOR EXAM";
                            break;
                    }

                    break;
                case "Compromised Systems/Media":
                    category = "SYSTEMS";
                    switch (examProcess)
                    {
                        case "System Triage Exam":
                            subcategory = "TRIAGE";
                            break;
                        case "System Standard Exam":
                            subcategory = "STANDARD";
                            break;
                        case "System Focused Exam":
                            subcategory = "FOCUSED";
                            break;
                    }

                    break;
                case "Network Based Artifacts":
                    category = "NETWORK";
                    switch (examProcess)
                    {
                        case "Full Network Capture Analysis":
                            subcategory = "FULL NETWORK CAPTURE ANALYSIS";
                            break;
                        case "Text Based Log Analysis":
                            subcategory = "TEXT BASED LOG ANALYSIS";
                            break;
                    }

                    break;
                case "Volatile Data Artifacts":
                    category = "VOLATILE";
                    switch (examProcess)
                    {
                        case "Volatile Triage Exam":
                            subcategory = "TRIAGE";
                            break;
                        case "Volatile Standard Exam":
                            subcategory = "STANDARD";
                            break;
                    }

                    break;
            }

            return DB11.exam_category.First(z => z.exam_category1 == category && z.exam_subcategory == subcategory)
                .exam_category_id;
        }
    }
}