﻿using CIMSData.Database;
using CIMSWeb.Models;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

namespace CIMSWeb.Controllers
{
    /// <summary>
    /// Facilitates sub agency management
    /// </summary>
    public class SubAgencyController : CIMSController
    {
        public ActionResult Index()
        {
            List<SelectListItem> agencyList = DB11.agency.Select(x => new SelectListItem() { Text = x.name, Value = x.name }).ToList();
            agencyList.Insert(0, new SelectListItem() { Text = "Select agency" });
            return PartialView(agencyList);
        }

        /// <summary>
        /// Provides the JSON for the master evidence Kendo grid
        /// </summary>
        /// <param name="request"></param>
        /// <param name="agencyID"></param>
        /// <returns></returns>
        public ActionResult SubAgencyGridData([DataSourceRequest]DataSourceRequest request, int? agencyID = null)
        {
            if (agencyID.HasValue)
            {
                var data = DB11.agency_org.Where(x => x.agency_id == agencyID).OrderBy(x => x.agency_unit.name).ToList().Select(x => new SubAgencyViewModel(x));
                return KendoGridResult(data.AsQueryable(), request);
            }
            return null;
        }

        public ActionResult AgencyModal (int? agencyId = null)
        {
            if (agencyId != null)
            {
                var editAgency = DB11.agency.First(x => x.agency_id == agencyId);
                return PartialView("AgencyModal", new AgencyViewModel(editAgency));
            }
            return PartialView("AgencyModal");
        }

        public ActionResult AgencyCreate(AgencyViewModel avm)
        {
            if (avm.Id == 0)
            {
                var agency = avm.Create();
                DB11.agency.Add(agency);
                DB11.SaveChanges();
                AuditInsert(agency);
                DB11.SaveChanges();
            }
            else
            {
                var agency = DB11.agency.First(x => x.agency_id == avm.Id);
                AuditUpdate(agency);
                avm.Update(agency);
                DB11.Entry(agency).State = System.Data.Entity.EntityState.Modified;
                DB11.SaveChanges();
            }
            return new EmptyResult();
        }

        /// <summary>
        /// Create a sub agency
        /// </summary>
        /// <param name="request"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult SubAgencyCreate([DataSourceRequest] DataSourceRequest request, SubAgencyViewModel model)
        {
            contact c = DB11.contact.FirstOrDefault(x => x.contact_id == model.ORIGREC);
            // If the contact is a new one, create it; otherwise, update the existing one
            if (c == null)
            {
                c = model.CreateContact();
                DB11.contact.Add(c);
                DB11.SaveChanges();
                AuditInsert(c);
                DB11.SaveChanges();
            }
            else
            {
                AuditUpdate(c);
                model.Update(c);
                DB11.Entry(c).State = System.Data.Entity.EntityState.Modified;
            }

            var au = DB11.agency_unit.FirstOrDefault(z => z.name == model.SubAgency);
            if (au == null)
            {
                au = model.Create();
                DB11.agency_unit.Add(au);
            }
            agency a = DB11.agency.First(x => x.agency_id == model.AgencyID);
            agency_org ao = new agency_org
            {
                agency = a,
                agency_id = model.AgencyID,
                agency_unit = au,
                contact = c,
                detco_id = c.contact_id
            };
            DB11.agency_org.Add(ao);
            DB11.SaveChanges();
            AuditInsert(ao);
            DB11.SaveChanges();
            return Json(new[] { model }.ToDataSourceResult(request, ModelState));
        }


        /// <summary>
        /// Update a sub agency
        /// </summary>
        /// <param name="request"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult SubAgencyUpdate([DataSourceRequest] DataSourceRequest request, SubAgencyViewModel model)
        {
            if (model != null)
            {
                var row = DB11.agency_org.First(x => x.agency_org_id == model.ID);
                AuditUpdate(row);
                if (row.contact == null)
                {
                    var newDETCO = new contact();
                    DB11.contact.Add(newDETCO);
                    row.contact = newDETCO;
                    DB11.Entry(row).State = System.Data.Entity.EntityState.Modified;
                    DB11.SaveChanges();
                    model.ORIGREC = row.detco_id;
                }
                model.Update(row.contact);
                DB11.Entry(row.contact).State = System.Data.Entity.EntityState.Modified;
                model.Update(row);
                DB11.Entry(row).State = System.Data.Entity.EntityState.Modified;
                DB11.SaveChanges();
            }
            return Json(ModelState.ToDataSourceResult());
        }

        public ActionResult GetContact(int id)
        {
            return Json(new ContactViewModel(DB11.contact.First(x => x.contact_id == id)), JsonRequestBehavior.AllowGet);
        }
    }
}