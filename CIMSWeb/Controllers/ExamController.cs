﻿using CIMSWeb.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Configuration;
using System.Web.Mvc;
using System.Text;
using CIMSWeb.Attributes;
using Kendo.Mvc.UI;
using Kendo.Mvc.Extensions;
using System.Data.Entity;
using CIMSData.Database;
using CIMSWeb.Workflow;
using WebGrease.Css.Extensions;
using System.IO;
using System.Threading;
using System.Web;
using Microsoft.Ajax.Utilities;
using EntityState = System.Data.Entity.EntityState;
using System.Web.Mvc.Html;
using SolrNet.Utils;

namespace CIMSWeb.Controllers
{
    /// <summary>
    /// Constructor for all things exam
    /// </summary>
    public class ExamController : CIMSController
    {
        public ActionResult Index()
        {
            return PartialView();
        }

        /// <summary>
        /// Returns view for event log tab
        /// </summary>
        /// <param name="examID"></param>
        /// <returns></returns>
        public ActionResult EventLog(int examID)
        {
            ViewBag.ExamID = examID;
            return PartialView("_EventLog");

        }

        /// <summary>
        /// Returns data for exam event log entries
        /// </summary>
        /// <param name="request"></param>
        /// <param name="examID"></param>
        /// <returns></returns>
        public ActionResult EventLogData([DataSourceRequest]DataSourceRequest request, int examID)
        {
            var data = DB11.exam_event.Where(x => x.exam_id == examID).ToList().Select(x => new ExamEventViewModel(x)).OrderBy(x => x.Date);
            return KendoGridResult(data.AsQueryable(), request);
        }

        private bool RejectingExam(int examID, string reason)
        {
            var holdActivities = DB11.exam_activity.Where(x => x.exam_id == examID && x.activity_id == (int)ActivityType._Exam_Hold && x.date_end == null);
            foreach(var ha in holdActivities)
            {
                AuditUpdate(ha);
                ha.date_end = DateTime.Now;
                DB11.Entry(ha).State = System.Data.Entity.EntityState.Modified;
            }
            var exam = DB11.exam.Find(examID);
            AuditUpdate(exam);
            exam.exam_status_id = (int)ExamStatus.Rejected;
            exam.rejected_reason = reason;
            if (exam.exam_type_id == 1)
            {
                // Reset the ems updated flag in the event it was already assigned.
                exam.exam_intrusion.ems_updated = false;
            }
            DB11.Entry(exam).State = System.Data.Entity.EntityState.Modified;
            DB11.SaveChanges();
            switch(exam.exam_type_id)
            {
                case 1:
                    IntrusionsWorkflow iWorkflow = new IntrusionsWorkflow(exam, DB11);
                    iWorkflow.RejectExam();
                    new Thread(() => EMSStatus.UpdateEMS()).Start();
                    break;
                case 3:
                    StandardExamWorkflow sWorkflow = new StandardExamWorkflow(exam, DB11);
                    sWorkflow.RejectExam();
                    break;
            }

            LogExamEvent(examID, "Exam rejected- " + exam.rejected_reason, ExamEventTypes.Exam_Rejected);
            return true;
        }

        [CIMSAuthorize(SecureFunction.ExamAdministration)]
        public ActionResult RejectExam(int examID)
        {
            ExamRejectViewModel model = new ExamRejectViewModel {ExamID = examID, RejectReason = ""};
            return PartialView("_RejectExamReason", model);
        }

        /// <summary>
        /// Closes exam and sets status to rejected
        /// </summary>
        /// <param name="examID"></param>
        /// <returns></returns>
        [CIMSAuthorize(SecureFunction.ExamAdministration)]
        public ActionResult ConfirmRejectExam(int examID, string rejectReason)
        {
            RejectingExam(examID, rejectReason);
            return new EmptyResult();
        }

        public JsonResult isIE()
        {
            bool ie = false;
            CurrentUserModel.Permissions.ForEach(x =>
            {
                if (x.Group == Group.ImagingExtraction) ie = true;
            });
            return Json(new {isIe = ie}, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// Places exam on hold by creating a hold activity
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult HoldExam(ExamHoldViewModel model)
        {
            if (!model.IsHold)
            {
                var hold = DB11.exam_activity.First(z =>
                        z.exam_id == model.ExamID && z.activity_id == (int) ActivityType._Exam_Hold &&
                        !z.date_end.HasValue).exam_hold
                    .OrderByDescending(z => z.exam_activity.date_start).First();
                return UnholdExam(model.ExamID, hold, model.HoldRemovedAdditionalInformation);
            }
            if (!model.ExamHoldID.HasValue)
            {
                exam_activity ea = new exam_activity()
                {
                    exam_id = model.ExamID,
                    activity_id = (int) ActivityType._Exam_Hold,
                    date_start = DateTime.Now,
                    associated_account_id = CurrentUserModel.AccountID
                };
                DB11.exam_activity.Add(ea);
                var exam = DB11.exam.Find(model.ExamID);
                AuditUpdate(exam);
                exam.exam_status_id = (int) ExamStatus.Hold;
                DB11.Entry(exam).State = System.Data.Entity.EntityState.Modified;
                DB11.SaveChanges();
                exam_hold eh = new exam_hold()
                {
                    exam_activity_id = ea.exam_activity_id,
                    details_on_hold = model.AdditionalInformation,
                    exam_hold_type_id = model.HoldTypeID
                };
                DB11.exam_hold.Add(eh);
                DB11.SaveChanges();
                AuditInsert(eh);
                AuditInsert(ea);
                DB11.SaveChanges();
                LogExamEvent(model.ExamID, $"Exam placed on hold - {DB11.exam_hold_type.Find(model.HoldTypeID).name}", ExamEventTypes.Exam_Placed_On_Hold);
            }
            else
            {
                exam_hold eh = DB11.exam_hold.Find(model.ExamHoldID.Value);
                AuditUpdate(eh);
                if (eh.exam_activity.date_start != model.StartDate)
                {
                    LogExamEvent(model.ExamID, $"Exam hold of type \"{eh.exam_hold_type.name}\" start date updated from {eh.exam_activity.date_start} to {model.StartDate}", ExamEventTypes.Exam_Hold_Start_Date_Updated);
                    eh.exam_activity.date_start = model.StartDate;
                }
                if (eh.exam_activity.date_end != model.EndDate && eh.exam_activity.date_end.HasValue)
                {
                    LogExamEvent(model.ExamID, $"Exam hold of type \"{eh.exam_hold_type.name}\" end date updated from {eh.exam_activity.date_end} to {model.EndDate}", ExamEventTypes.Exam_Hold_End_Date_Updated);
                    eh.exam_activity.date_end = model.EndDate;
                }
                eh.details_on_hold = model.AdditionalInformation;
                eh.exam_hold_type_id = model.HoldTypeID;
                eh.details_off_hold = model.HoldRemovedAdditionalInformation;
                DB11.SaveChanges();
            }
            return new EmptyResult();
        }

        /// <summary>
        /// Removes an exam's hold status by completing all hold activities
        /// </summary>
        /// <param name="examID"></param>
        /// <returns></returns>
        private ActionResult UnholdExam(int examID, exam_hold hold, string reason)
        {
            var holdActivities = DB11.exam_activity.Where(x => x.exam_id == examID && x.activity_id == (int)ActivityType._Exam_Hold && x.date_end == null);
            foreach (var ha in holdActivities)
            {
                AuditUpdate(ha);
                ha.date_end = DateTime.Now;
                DB11.Entry(ha).State = System.Data.Entity.EntityState.Modified;
            }
            var exam = DB11.exam.Find(examID);
            AuditUpdate(exam);
            exam.exam_status_id = (int)ExamStatus.Active;
            DB11.Entry(exam).State = System.Data.Entity.EntityState.Modified;
            hold.details_off_hold = reason;
            DB11.Entry(hold).State = EntityState.Modified;
            DB11.SaveChanges();
            LogExamEvent(examID, $"Exam removed from hold", ExamEventTypes.Exam_Removed_From_Hold);
            return new EmptyResult();
        }

        /// <summary>
        /// Sets an exams status to active
        /// </summary>
        /// <param name="examID"></param>
        /// <returns></returns>
        [CIMSAuthorize(SecureFunction.ExamAdministration)]
        public ActionResult ReopenExam(int examID)
        {
            var exam = DB11.exam.Find(examID);
            AuditUpdate(exam);
            exam.exam_status_id = (int)ExamStatus.Active;
            DB11.Entry(exam).State = System.Data.Entity.EntityState.Modified;
            DB11.SaveChanges();
            LogExamEvent(examID, "Exam reopened", ExamEventTypes.Exam_Reopened);
            return new EmptyResult();
        }

        /// <summary>
        /// Sets an exams status to active
        /// </summary>
        /// <param name="examID"></param>
        /// <returns></returns>
        [CIMSAuthorize(SecureFunction.ExamAdministration)]
        public ActionResult ConvertToDamoExam(int examID)
        {
            var exam = DB11.exam.Find(examID);
            exam.exam_status_id = (int)ExamStatus.Active;
            exam.exam_type_id = 5;
            exam.exam_category_id = DB11.exam_category.First(x => x.exam_category1 == "DAMO" && x.exam_subcategory == "DAMO IMAGING").exam_category_id;
            var now = DateTime.Now;
            foreach (var wf in exam.exam_activity.Where(x => !x.date_end.HasValue))
            {
                wf.date_end = now;
                wf.skipped = true;
                DB11.Entry(wf).State = System.Data.Entity.EntityState.Modified;
            }
            DB11.Entry(exam).State = System.Data.Entity.EntityState.Modified;
            DB11.SaveChanges();
            var w = new IntrusionsDamoWorkflow(exam, DB11);
            w.StartWorkflow(CurrentUserModel, Group.Intrusions);
            return new EmptyResult();
        }

        /// <summary>
        /// Sets an exams status to done
        /// </summary>
        /// <param name="examID"></param>
        /// <returns></returns>
        [CIMSAuthorize(SecureFunction.ExamAdministration)]
        public ActionResult ForceExamComplete(int examID)
        {
            var holdActivities = DB11.exam_activity.Where(x => x.exam_id == examID && x.activity_id == (int)ActivityType._Exam_Hold && x.date_end == null);
            foreach (var ha in holdActivities)
            {
                AuditUpdate(ha);
                ha.date_end = DateTime.Now;
                DB11.Entry(ha).State = System.Data.Entity.EntityState.Modified;
            }
            var exam = DB11.exam.Find(examID);
            AuditUpdate(exam);
            exam.exam_status_id = (int)ExamStatus.Done;
            DB11.Entry(exam).State = System.Data.Entity.EntityState.Modified;
            DB11.SaveChanges();
            LogExamEvent(examID, "Exam forcefully closed", ExamEventTypes.Exam_Forcefully_Closed);
            if (exam.exam_type_id == (int)ExamType.Intrusions && exam.exam_number.Contains('E'))
            {
                exam.exam_intrusion.ems_updated  = false;
                DB11.Entry(exam).State = System.Data.Entity.EntityState.Modified;
                DB11.SaveChanges();
                new Thread(() => EMSStatus.UpdateEMS()).Start();
            }
            return new EmptyResult();
        }


        public ActionResult Create(int? caseID = null, int? examID = null)
        {
            ExamCreateViewModel model = new ExamCreateViewModel()
            {
                CaseID = caseID,
                ExamID = examID
            };
            return PartialView("_Create", model);
        }
        public ActionResult CreateNewExamForm(int? caseID = null, int? examID = null)
        {
            ExamCreateViewModel model = new ExamCreateViewModel() {
                CaseID = caseID,
                ExamID = examID,
                Case = new CaseViewModel()
            };
         
            ViewBag.caseDirectory = false;
            return PartialView("_CreateNewExam", model);
        }
        
        public ActionResult CreateNewExamCaseForm(int? examTypeId)
        {
            ViewBag.caseDirectory = false;
            if (examTypeId == 1 || examTypeId == 7)
            {
                return PartialView("_EditIntrusionsCase", new CaseViewModel());
            }//else
            return PartialView("_EditCaseExam", new CaseViewModel());
        }

        public ActionResult CopyExamToDiffCase(int examID)
        {
            ViewBag.CaseNumbersList = <EMAIL>(x => x.case_number).Select(x => new { Value = x.case_id, Text = x.case_number });

            CopyExamToDiffCaseModel model = new CopyExamToDiffCaseModel() {
                ExamID = examID,
                Case = new CaseViewModel()
            };

            ViewBag.caseDirectory = false;

            return PartialView("_CopyExamToDiffCase", model);
        }

        public ActionResult SubmitCopyExamToDiffCase(int examID, CaseViewModel caseModel)
        {
            exam copied_exam = DB11.exam.Find(examID);
            var dict = SavingCase(caseModel, null);//Make updates to case if any.  Add new case if case num not found. Dont update copied exam's assigned case (make param null).

            var examCreateViewModel = new ExamCreateViewModel();
            examCreateViewModel.Case = new CaseViewModel((@case)dict["case"], DB11);
            examCreateViewModel.CaseID = examCreateViewModel.Case.CaseID;
            examCreateViewModel.ExamType = (int)copied_exam.exam_type_id;
            examCreateViewModel.ExamID = copied_exam.exam_id;
            var new_exam = CreatingExam(examCreateViewModel);

            //Reject Old Case here
            RejectingExam(examID, "Exam copied to new case [ New Case # : " + new_exam.@case.case_number + ". New Exam # : " + new_exam.exam_number + " ]");

            return Json(new { ExamID = new_exam.exam_id, CaseID = new_exam.case_id }, JsonRequestBehavior.AllowGet);
        }

        public Dictionary<string, int> getExamDetailsCounts(exam ex)
        {
            var documentCount = 0;
            var rootExamDir = CIMSDocument.GetFileSharePathFromDCFLTrackingNumber(ex.exam_number);
            if (Directory.Exists(rootExamDir))
            {
                documentCount = Directory.GetFiles(rootExamDir, "*.*", SearchOption.AllDirectories).Count();
            }

            var activity_count = ex.exam_activity.Count(x => x.date_start.HasValue && x.activity_id != 0);
            var examDetailsCounts = new Dictionary<string, int>
            {
                {"Communications", ex.communication.Count(x => x.child_id == null)},
                {"Documents", documentCount},
                {"Event Log", ex.exam_event.Count},
                {"Evidence", ex.exam_evidence.Any() ? ex.exam_evidence.Select(z => z.evidence).Count() : 0},
                {"Hard Drives", ex.hard_drive_history.Select(x => x.hard_drive_id).Distinct().Count()},
                {"Notes", ex.exam_note.Count},
                {"Packages", ex.exam_package.Count},
                {"Tickets", ex.eh_ticket.Count},
                {"Timesheet" , ex.account_activity.Count},
                {"Workflow" , activity_count}
            };

            return examDetailsCounts;
        }

        public ActionResult UpdateNavColumnTotals(int examID)
        {
            var ex = DB11.exam.Find(examID);
            return Json(getExamDetailsCounts(ex), JsonRequestBehavior.AllowGet);
        }

        public ActionResult EditAcquisitionTeam(int groupID, int examID, bool isIE=true, bool isPreProcessing = false)
        {
            ViewBag.GroupID = groupID;
            //think these flags are dead code.
            ViewBag.isIE = isIE;
            ViewBag.isPreProcessing = isPreProcessing;
            var exam = DB11.exam.Find(examID);
            var standardExam = DB11.exam_standard.FirstOrDefault(x => x.exam_id == examID);
            TeamModel mod = new TeamModel()
            {
                ExamID = examID
            };
            List<TeamMember> teamMembers = new List<TeamMember>();
            Dictionary<string, List<string>> evidenceAssignments = new Dictionary<string, List<string>>();
            ViewBag.Evidence = true;
            int[] evidenceIDs = exam.exam_evidence.Select(x => x.evidence_id).ToArray();
            var evidence = DB11.evidence.Where(x => evidenceIDs.Contains(x.evidence_id)).ToList().Select(x => new EvidenceViewModel(x));
            var tagIds = evidence.Select(x => x.TagID).ToArray();
            var tags = DB11.evidence_tag.Where(x => tagIds.Contains(x.evidence_tag_id)).ToList().Select(x => new EvidenceViewModel(x));
            var all = tags.Concat(evidence).ToList();
            Dictionary<string, EvidenceViewModel> allEvidenceDict = new Dictionary<string, EvidenceViewModel>();
            foreach (var x in all)
            {
                allEvidenceDict[x.ViewId] = x;
            }

            mod.AllExamEvidence = allEvidenceDict;
            List<string> assigned = new List<string>();
            var activityId = 0;
            if (isIE)
            {
                activityId = (int)ActivityType._Perform_Forensic_Acquisition;
            }
            if (isPreProcessing)
            {
                //isie flag comes in as true so overwrite it if this ispreprocessing flag is set.
                activityId = (int)ActivityType._Perform_PreProcessing;
            }
            else
            {
                activityId = (int)ActivityType._Perform_Forensic_Examination;
            }
            foreach (var ea in DB11.exam_activity.Where(x => x.exam_id == examID && x.activity_id == activityId))
            {
                var tm = teamMembers.FirstOrDefault(x => x.AccountID == ea.account_id);
                if (tm == null)
                {
                    tm = new TeamMember(ea.account);
                    var leadId = isIE ? standardExam.lead_acquisition_id : exam.lead_examiner_id;
                    if (tm.AccountID == leadId)
                    {
                        tm.Lead = true;
                    }
                    teamMembers.Add(tm);
                    evidenceAssignments[tm.AccountID.ToString()] = new List<string>();
                }
                var ee = ea.evidence.FirstOrDefault();
                if (ee != null)
                {
                    if (!evidenceAssignments[tm.AccountID.ToString()].Contains(ee.evidence_id.ToString()))
                    {
                        evidenceAssignments[tm.AccountID.ToString()].Add(ee.evidence_id.ToString());

                    }
                    assigned.Add(ee.evidence_id.ToString());
                }
            }
            evidenceAssignments["unassigned"] = all.Where(x => !assigned.Contains(x.ViewId)).Select(x => x.ViewId).ToList();
            mod.EvidenceAssignments = evidenceAssignments;
            mod.TeamMembers = teamMembers;
            return PartialView("_EditTeam", mod);
        }


        public ActionResult EditExamTeam(int groupID, int examID)
        {
            ViewBag.GroupID = groupID;
            var exam = DB11.exam.Find(examID);
            TeamModel mod = new TeamModel()
            {
                ExamID = examID
            };
            List<TeamMember> teamMembers = new List<TeamMember>();
            ViewBag.Evidence = false;
            foreach (var ea in DB11.exam_activity.Where(x => x.exam_id == examID && 
                                                             (x.activity_id == (int)ActivityType._Perform_Forensic_Examination || x.activity_id == (int)ActivityType._Perform_Analysis) && 
                                                             x.date_end == null))
            {
                var tm = teamMembers.Where(x => x.AccountID == ea.account_id).FirstOrDefault();
                if (tm == null)
                {
                    tm = new TeamMember(ea.account);
                    if (tm.AccountID == exam.lead_examiner_id)
                    {
                        tm.Lead = true;
                    }
                    teamMembers.Add(tm);
                }
            }
            mod.TeamMembers = teamMembers;
            return PartialView("_EditTeam", mod);
        }

        public ActionResult ReassignTechnician(int activityId, int userId)
        {
            var activity = DB11.exam_activity.Find(activityId);
            activity.account_id = userId;
            DB11.Entry(activity).State = EntityState.Modified;
            DB11.SaveChanges();
            var examID = activity.exam_id;
            var exam = DB11.exam.Find(examID);
            // If a user was re-assigned and has already completed at least one acquisition activitiy, need to start the sign acquisition notes activity for them.
            DB11.exam_activity
                .Where(z => z.exam_id == examID && z.activity_id == (int)ActivityType._Perform_Forensic_Acquisition &&
                            z.account_id.HasValue)
                .Select(z => (int)z.account_id).Distinct().ToList().ForEach(z =>
                {
                    var standardWorkFlow = new StandardExamWorkflow(exam, DB11);
                    standardWorkFlow.CreateSignAcquisitionNotesActivityIfNecessary(z);
                });
            return new EmptyResult();
        }

        public ActionResult ReassignExaminer(int activityId, int userId)
        {
            var activity = DB11.exam_activity.Find(activityId);
            activity.account_id = userId;
            DB11.Entry(activity).State = EntityState.Modified;
            DB11.SaveChanges();
            var examID = activity.exam_id;
            var exam = DB11.exam.Find(examID);
            // If a user was re-assigned and has already completed at least one examination activitiy, need to start the sign examination notes activity for them.
            DB11.exam_activity
                .Where(z => z.exam_id == examID && z.activity_id == (int)ActivityType._Perform_Forensic_Examination &&
                            z.account_id.HasValue)
                .Select(z => (int)z.account_id).Distinct().ToList().ForEach(z =>
                {
                    var standardWorkFlow = new StandardExamWorkflow(exam, DB11);
                    standardWorkFlow.CreateSignExaminationNotesActivityIfNecessary(z);
                });
            return new EmptyResult();
        }

        public ActionResult ReassignPreProcessor(int activityId, int userId)
        {
            var activity = DB11.exam_activity.Find(activityId);
            activity.account_id = userId;
            DB11.Entry(activity).State = EntityState.Modified;
            DB11.SaveChanges();
            var examID = activity.exam_id;
            var exam = DB11.exam.Find(examID);
            // If a user was re-assigned and has already completed at least one examination activitiy, need to start the sign pre processing notes activity for them.
            DB11.exam_activity
                .Where(z => z.exam_id == examID && z.activity_id == (int)ActivityType._Perform_PreProcessing &&
                            z.account_id.HasValue)
                .Select(z => (int)z.account_id).Distinct().ToList().ForEach(z =>
                {
                    var standardWorkFlow = new StandardExamWorkflow(exam, DB11);
                    standardWorkFlow.CreateSignPreProcessingNotesActivityIfNecessary(z);
                });
            return new EmptyResult();
        }

        public ActionResult SaveAcquisitionTeam(int examID, TeamParticipantViewModel[] teamMembers, int leadTeamMemberID, bool isIE, bool isPreProcessing)
        {
            // Saving changes to examination team
            exam exam = DB11.exam.Find(examID);
            exam_standard exs = DB11.exam_standard.Where(x => x.exam_id == examID).First();
            // Update examination team
            AuditUpdate(exs);
            if (isIE)
            {
                exs.lead_acquisition_id = leadTeamMemberID;
                DB11.Entry(exs).State = System.Data.Entity.EntityState.Modified;
            }
            else
            {
                exam.lead_examiner_id = leadTeamMemberID;
                DB11.Entry(exam).State = System.Data.Entity.EntityState.Modified;
            }
            DB11.SaveChanges();
            int[] teamMemberIDs = teamMembers.Select(x => x.AccountID).ToArray();
            var theActivityId = 0;
            if (isIE)
            {
                theActivityId = (int)ActivityType._Perform_Forensic_Acquisition;
            }
            else if (isPreProcessing)
            {
                theActivityId = (int)ActivityType._Perform_PreProcessing;
            }
            else
            {
                theActivityId = (int)ActivityType._Perform_Forensic_Examination;
            }
            // Go through existing acquisition activities and remove the ones that are no longer needed
            foreach (var ea in exam.exam_activity.Where(z =>
                                                        z.activity_id == theActivityId &&
                                                        (z.skipped == false || z.skipped == null)).ToList())
            {
                int? eeID = ea.evidence.Select(x => x.evidence_id).FirstOrDefault();
                if (eeID != null)
                {
                    if (!teamMembers.Any(x => x.AccountID == ea.account_id && x.EvidenceIDs != null && x.EvidenceIDs.Contains(eeID.Value)))
                    {
                        // Remove
                        AuditDelete(ea);
                        foreach (var e in ea.evidence.ToList())
                        {
                            ea.evidence.Remove(e);
                        }
                        DB11.exam_activity.Remove(ea);
                        DB11.SaveChanges();
                    }
                }
            }

            // Go through each saved team member/evidence item pair and store new acquisition activity if needed
            foreach (var tm in teamMembers)
            {
                if (tm.EvidenceIDs != null)
                {
                    foreach (var eID in tm.EvidenceIDs)
                    {
                        var existingAcquisitionActivity = exam.exam_activity.Where(z =>
                                                            z.activity_id == theActivityId &&
                                                            (z.skipped == false || z.skipped == null) &&
                                                            z.account_id == tm.AccountID &&
                                                            z.evidence.Any(x => x.evidence_id == eID)).FirstOrDefault();
                        if (existingAcquisitionActivity == null)
                        {
                            exam_activity ea = new exam_activity
                            {
                                account_id = tm.AccountID,
                                activity_id = theActivityId,
                                exam_id = exam.exam_id
                            };
                            ea.evidence.Add(DB11.evidence.Find(eID));
                            ea.date_start = DateTime.Now;
                            var groupid = 0;
                            if (isIE)
                            {
                                groupid = (int)Group.ImagingExtraction;
                            }
                            else if (isPreProcessing)
                            {
                                groupid = (int)Group.PreProcessing;
                            }
                            else
                            {
                                groupid = exam.group_id;
                            }
                            ea.group_id = groupid;
                            DB11.exam_activity.Add(ea);
                            DB11.SaveChanges();
                            AuditInsert(ea);
                            DB11.SaveChanges();
                        }
                    }
                }
            }

            // If a user was re-assigned and has already completed at least one acquisition activitiy, need to start the sign acquisition notes activity for them.
            DB11.exam_activity
                .Where(z => z.exam_id == examID && z.activity_id == theActivityId &&
                            z.account_id.HasValue)
                .Select(z => (int) z.account_id).Distinct().ToList().ForEach(z =>
                {
                    var standardWorkFlow = new StandardExamWorkflow(exam, DB11);
                    standardWorkFlow.CreateSignAcquisitionNotesActivityIfNecessary(z);
                    standardWorkFlow.CreateSignExaminationNotesActivityIfNecessary(z);
                    standardWorkFlow.CreateSignPreProcessingNotesActivityIfNecessary(z);
                });
            return new EmptyResult();
        }


        public ActionResult SaveExamTeam(int examID, TeamParticipantViewModel[] teamMembers, int leadTeamMemberID, bool isIntrusions = false)
        {
            // Saving changes to examination team
            exam exam = DB11.exam.Find(examID);
            // Update examination team
            exam.lead_examiner_id = leadTeamMemberID;
            DB11.Entry(exam).State = System.Data.Entity.EntityState.Modified;
            int[] teamMemberIDs = teamMembers.Select(x => x.AccountID).ToArray();
            // Store new members by creating new examination activities
            int[] existingMembers = exam.exam_activity.Where(z => (z.activity_id == (int)ActivityType._Perform_Forensic_Examination || z.activity_id == (int)ActivityType._Perform_Analysis) &&
                                                                    (z.skipped == false || z.skipped == null) &&
                                                                    z.account_id.HasValue)
                                                                .Select(x => x.account_id.Value).Distinct().ToArray();
            int[] newMembers = teamMemberIDs.Where(x => !existingMembers.Contains(x)).ToArray();
            foreach (var x in newMembers)
            {
                exam_activity ea = new exam_activity
                {
                    account_id = x,
                    activity_id = (isIntrusions
                        ? (int) ActivityType._Perform_Analysis
                        : (int) ActivityType._Perform_Forensic_Examination),
                    exam_id = exam.exam_id,
                    group_id = exam.group_id
                };
                // if started
                if (isIntrusions || exam.exam_activity.Any(z => z.activity_id == (int)ActivityType._Perform_Forensic_Examination && z.date_start.HasValue))
                {
                    ea.date_start = DateTime.Now;
                }
                DB11.exam_activity.Add(ea);
                DB11.SaveChanges();
                AuditInsert(ea);
                DB11.SaveChanges();
            }
            // Remove old members
            foreach (var x in exam.exam_activity.Where(x => (x.activity_id == (int)ActivityType._Perform_Forensic_Examination || x.activity_id == (int)ActivityType._Perform_Analysis) &&
                                                            x.date_end == null &&
                                                            x.account_id != null &&
                                                            !(teamMemberIDs.Contains(x.account_id.Value))).ToList())
            {
                AuditDelete(x);
                DB11.exam_activity.Remove(x);
            }
            // Make sure any existing, open Start Exam activities are assigned to new exam lead
            foreach (exam_activity startActivity in exam.exam_activity.Where(x => x.activity_id == (int)ActivityType._Start_Examination && x.date_end == null))
            {
                AuditUpdate(startActivity);
                startActivity.account_id = leadTeamMemberID;
                DB11.Entry(startActivity).State = System.Data.Entity.EntityState.Modified;
            }

            if (exam.exam_type_id == 1 && exam.exam_number.Contains("E"))
            {
                exam.exam_intrusion.ems_updated = false;
                DB11.SaveChanges();
                new Thread(() => EMSStatus.UpdateEMS()).Start();
                return new EmptyResult();
            }
            DB11.SaveChanges();
            return new EmptyResult();
        }

        public ActionResult ActivityCorrectionUser(int examActivityID)
        {
            var ea = DB11.exam_activity.Find(examActivityID);
            if (ea.activity_id == (int)ActivityType._Section_Chief_Acquisition_Review)
            {
                ViewBag.CorrectionUsers = ea.exam.exam_activity.Where(x => x.activity_id == (int)ActivityType._Perform_Forensic_Acquisition).Select(x => x.account).Distinct().ToList().Select(x => new AccountViewModel(x));
            }
            else // should be group chief examination review
            {
                ViewBag.CorrectionUsers = ea.exam.exam_activity.Where(x => x.activity_id == (int)ActivityType._Perform_Forensic_Examination).Select(x => x.account).Distinct().ToList().Select(x => new AccountViewModel(x));
            }
            return PartialView("_ActivityCorrectionUser");
        }

        public ActionResult EditUser(int activityTypeID, int assignedGroupID, int? userID, int examID)
        {
            // Acquisition reviewers must come from IE
            if (activityTypeID == (int)ActivityType._Assign_Acquisition_Reviewer)
            {
                ViewBag.Users = DB11.account_permission.Where(x => x.group_id == (int) Group.ImagingExtraction && x.account.enabled && x.account_id != userID)
                                                                .ToList().Select(x => new {Text = x.account.name.FormatName(), Value = x.account_id}).Distinct().OrderBy(x => x.Text);
            }
            // Acquisition reviewers must come from IE
            else if (activityTypeID == (int)ActivityType._Assign_PreProcessing_Reviewer)
            {
                ViewBag.Users = DB11.account_permission.Where(x => x.group_id == (int)Group.PreProcessing && x.account.enabled && x.account_id != userID)
                                                                .ToList().Select(x => new { Text = x.account.name.FormatName(), Value = x.account_id }).Distinct().OrderBy(x => x.Text);
            }
            // Litigation support examiners can be from a number of sections
            else if (activityTypeID == (int) ActivityType._Assign_Examiner_For_Trial_Prep ||
                     activityTypeID == (int) ActivityType._Assign_Examiner_To_Restore_Images)
            {
                int[] groupIDs = new int[] { (int) Group.ImagingExtraction, (int) Group.MajorCrimes, (int) Group.Counterintelligence};
                ViewBag.Users = DB11.account_permission.Where(x => groupIDs.Contains(x.group_id) && x.account.enabled)
                    .ToList()
                    .Select(x => new {Text = x.account.name.FormatName(), Value = x.account_id}).Distinct()
                    .OrderBy(x => x.Text);
            }
            // Intrusions analysis reviewer has to be an intrusions power user that was not on the analysis team
            else if (activityTypeID == (int) ActivityType._Assign_Analysis_Reviewer)
            {
                exam ex = DB11.exam.First(x => x.exam_id == examID);
                int[] excludedAccountIDs = ex.exam_activity.Where(x => x.activity_id == (int) ActivityType._Perform_Analysis && x.date_end.HasValue && x.account_id.HasValue).Select(x => x.account_id.Value).ToArray();
                ViewBag.Users = DB11.account_permission.Where(x => x.group_id == (int)Group.Intrusions && x.account.enabled && !excludedAccountIDs.Contains(x.account_id) && x.account_role_id <= (int)Role.PowerUser)
                               .ToList().Select(x => new { Text = x.account.name.FormatName(), Value = x.account_id }).Distinct().OrderBy(x => x.Text);
            }
            // Get Intake Users
            else if (activityTypeID == (int) ActivityType._Assign_Intake_Reviewer)
            {
                ViewBag.Users = DB11.account_permission
                    .Where(z => z.group_id == (int) Group.Intake && z.account.enabled).ToList()
                    .Select(x => new {Text = x.account.name.FormatName(), Value = x.account_id}).Distinct()
                    .OrderBy(x => x.Text);
            }
            // Get examiners from assigned section
            else
            {
                ViewBag.Users = DB11.account_permission.Where(x => x.group_id == assignedGroupID && x.account.enabled && x.account_id != userID)
                    .ToList().Select(x => new { Text = x.account.name.FormatName(), Value = x.account_id }).Distinct().OrderBy(x => x.Text);
            }
            return PartialView("_EditUser");
        }

        private string GettingNextExamNumber(ExamCreateViewModel model)
        {
            DateTime now = DateTime.Now;
            int year = now.Year;
            string identifier = "I";
            if(now.Month >= 10) // Fiscal year adjustment
            {
                year++;
            }

            string returned_exam_number = "";

            string earliestExamNumForCase = "";
            if(model.CaseID.HasValue)
            {
                //Get earliest exam unless we have exams that have already have new appended lettering system.
                earliestExamNumForCase = DB11.exam.Where(x => x.case_id == model.CaseID).OrderByDescending(x => x.exam_number.Length).ThenBy(x => x.exam_number).Select(x => x.exam_number).FirstOrDefault();
                if(!string.IsNullOrEmpty(earliestExamNumForCase) && char.IsLetter(earliestExamNumForCase[earliestExamNumForCase.Length - 1]))
                {//If we have exam with new lettering system appended, get the latest one to increment the next exam number.
                    earliestExamNumForCase = DB11.exam.Where(x => x.case_id == model.CaseID).OrderByDescending(x => x.exam_number.Length).ThenByDescending(x => x.exam_number).Select(x => x.exam_number).FirstOrDefault();
                }
            }

            if(!string.IsNullOrEmpty(earliestExamNumForCase))
            {
                var caseHasFollowOnExamAlready = char.IsLetter(earliestExamNumForCase[earliestExamNumForCase.Length - 1]);
                if(caseHasFollowOnExamAlready)//Make sure this is at least 2nd exam under this case and already has an appended -A or -B.
                {
                    var indexOfFollowOnLetters = earliestExamNumForCase.LastIndexOf("-") + 1;
                    var appended_chars = earliestExamNumForCase.Substring(indexOfFollowOnLetters);
                    var new_appended_chars = string.Copy(appended_chars);
                    for(var i = appended_chars.Length - 1; i >= 0; i--)
                    {
                        StringBuilder sb = new StringBuilder(new_appended_chars);
                        if(appended_chars[i] == 'Z')
                        {
                            sb.Remove(i, 1);
                            sb.Insert(i, 'A');
                            new_appended_chars = sb.ToString();
                            if(i == 0)//Add another character. So Z becomes AA.  Only when all follow-on letters are Zs
                            {
                                new_appended_chars = string.Concat(new_appended_chars, "A");
                                break;
                            }
                        }
                        else
                        {//Just increment character and stop.  B becomes C. ZAB becomes ZAC. ZAZ becomes ZBA
                            var char_to_increment = appended_chars[i];
                            sb.Remove(i, 1);
                            sb.Insert(i, ++char_to_increment);
                            new_appended_chars = sb.ToString();
                            break;
                        }
                    }
                    returned_exam_number = string.Concat(earliestExamNumForCase.Substring(0, indexOfFollowOnLetters), new_appended_chars);
                }
                else
                {//Second exam under the case.  Append '-A'
                    returned_exam_number = string.Concat(earliestExamNumForCase, "-A");
                }
            }
            else
            {
                string find = year + $"-{identifier}";
                string highestExamNum = DB11.exam.Where(x => x.exam_number.Contains(find)).AsEnumerable().OrderByDescending(x => x.exam_number.Split('-')[2]).Select(x => x.exam_number).FirstOrDefault();
                int highNum = 0;
                if(highestExamNum != null)
                {
                    if(Char.IsLetter(highestExamNum[highestExamNum.Length - 1]))
                    {
                        var last_hyphen_index = highestExamNum.LastIndexOf("-");
                        var beginning_index = highestExamNum.IndexOf($"-{identifier}") + 2;
                        highNum = Int32.Parse(highestExamNum.Substring(beginning_index, last_hyphen_index - beginning_index));
                    }
                    else
                    {
                        highNum = Int32.Parse(highestExamNum.Substring(highestExamNum.IndexOf($"-{identifier}") + 2));
                    }
                }
                highNum++;
                returned_exam_number = string.Format("DCFL-{0}-{1}{2:D6}", year, identifier, highNum);
            }

            return returned_exam_number;
        }

        [CIMSAuthorize(SecureFunction.ExamCreation)]
        public ActionResult GetNextExamNumber(ExamCreateViewModel model)
        {
            string exam_num = GettingNextExamNumber(model);
            return Json(new { exam_number = exam_num }, JsonRequestBehavior.AllowGet);
        }

        private exam CreatingExam(ExamCreateViewModel model)
        {
            exam exam = new exam();
            exam.exam_number = GettingNextExamNumber(model);
            exam.exam_status_id = 6;
            exam.date_opened = DateTime.Now;
            if(model.CaseID.HasValue)
            {
                exam.case_id = model.CaseID.Value;
            }
            exam otherExam = null;
            if(model.ExamID.HasValue)
            {
                otherExam = DB11.exam.Find(model.ExamID);
                exam.exam_category_id = otherExam.exam_category_id;
                exam.group_id = otherExam.group_id;
                exam.exam_investigation_type_id = otherExam.exam_investigation_type_id;
            }
            ExamDBType examDBType;
            switch (model.ExamType)
            {
                case (int)ExamType.Intrusions:
                    examDBType = ExamDBType.Intrusions;
                    break;
                case (int)ExamType.LitigationSupportTestimony:
                case (int)ExamType.LitigationSupportDefenseImages:
                    examDBType = ExamDBType.LitigationSupport;
                    break;
                case (int)ExamType.PreExamCoordination:
                    examDBType = ExamDBType.PreExamCoordination;
                    break;
                case (int)ExamType.IntrusionsDamo:
                    examDBType = ExamDBType.IntrusionsDamo;
                    break;
                default:
                    examDBType = ExamDBType.Standard;
                    break;
            }
            exam.exam_type_id = (int)examDBType;
            if(model.ExamType == (int)ExamType.Intrusions || model.ExamType == (int)ExamType.IntrusionsDamo)
            {
                exam.group_id = 8;
                exam.exam_investigation_type_id = (int)ExamInvestigationType.Other;
                if (model.ExamType == (int) ExamType.IntrusionsDamo)
                {
                    exam.exam_category_id = DB11.exam_category.First(x => x.exam_category1 == "DAMO" && x.exam_subcategory == "DAMO IMAGING").exam_category_id;
                }
                exam_intrusion intrusionsExam = new exam_intrusion {
                    exam = exam
                };
                DB11.exam.Add(exam);
                DB11.exam_intrusion.Add(intrusionsExam);
                DB11.SaveChanges();
                if (model.ExamType == (int)ExamType.Intrusions)
                {
                    IntrusionsWorkflow workflow = new IntrusionsWorkflow(exam, DB11);
                    workflow.StartWorkflow(CurrentUserModel, Group.Intrusions);
                }
                else if (model.ExamType == (int) ExamType.IntrusionsDamo)
                {
                    var w = new IntrusionsDamoWorkflow(exam, DB11);
                    w.StartWorkflow(CurrentUserModel, Group.Intrusions);
                }
            }
            else if(model.ExamType == (int)ExamType.LitigationSupportTestimony ||
                     model.ExamType == (int)ExamType.LitigationSupportDefenseImages)
            {
                exam.group_id = (int)Group.Intake;
                exam.exam_investigation_type_id = (int)ExamInvestigationType.Criminal;
                if(model.ExamType == (int)ExamType.LitigationSupportTestimony)
                {
                    exam.exam_category_id = (int)ExamCategory.LitigationSupportTestimony;
                }
                else
                {
                    exam.exam_category_id = (int)ExamCategory.LitigationSupportDefenseImages;
                }

                DB11.exam.Add(exam);
                DB11.SaveChanges();
                if(model.ExamType == (int)ExamType.LitigationSupportTestimony)
                {
                    LitigationSupportTestimonyWorkflow
                        workflow = new LitigationSupportTestimonyWorkflow(exam, DB11);
                    workflow.StartWorkflow(CurrentUserModel, Group.LitigationSupport);
                }
                else
                {
                    LitigationSupportDefenseImagesWorkflow workflow =
                        new LitigationSupportDefenseImagesWorkflow(exam, DB11);
                    workflow.StartWorkflow(CurrentUserModel, Group.LitigationSupport);
                }
            }
            else if(model.ExamType == (int)ExamType.MajorCrimesCounterIntelligence || model.ExamType == (int)ExamType.PreExamCoordination)
            {
                if(otherExam == null) // If its new, not a copy
                {
                    exam.group_id = (int)Group.Intake;
                }

                exam_standard standardExam = new exam_standard {
                    exam = exam
                };
                if(otherExam?.exam_standard != null)
                {
                    exam.evidence_return_address_id = otherExam.evidence_return_address_id;
                    exam.report_return_address_id = otherExam.report_return_address_id;
                }

                exam.date_opened = null; // Standard exams get their date opened set when the `Create Exam` workflow activity is completed. 

                DB11.exam.Add(exam);
                DB11.exam_standard.Add(standardExam);
                DB11.SaveChanges();
                if (model.ExamType == (int)ExamType.MajorCrimesCounterIntelligence)
                {
                    StandardExamWorkflow workflow = new StandardExamWorkflow(exam, DB11);
                    workflow.StartWorkflow(CurrentUserModel, Group.Evidence);
                }
                else if (model.ExamType == (int)ExamType.PreExamCoordination)
                {
                    var wf = new PreExamCoordinationWorkflow(exam, DB11);
                    wf.StartWorkflow(CurrentUserModel, Group.Intake);
                }
            }

            if (otherExam == null)
            {
                LogExamEvent(exam.exam_id, "Exam created", ExamEventTypes.Exam_Created);
            }
            else
            {
                LogExamEvent(exam.exam_id, "Exam created from a copy of Exam " + DB11.exam.Find(model.ExamID)?.exam_number, ExamEventTypes.Exam_Created);
            }

            return exam;
        }

        /// <summary>
        /// Brand new exam for the database
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CIMSAuthorize(SecureFunction.ExamAdministration)]
        public ActionResult CreateExam(ExamCreateViewModel model)
        {
            var exam = CreatingExam(model);
            return Json(new { ExamID = exam.exam_id, CaseID = exam.case_id });
        }

        private List<ExamViewModel> GetExamList(IEnumerable<exam> exams)
        {
            var results = exams.Select(x => new ExamViewModel(x)
            {
                CurrentSectionID = x.group_id,
                CurrentSection = x.group.name
            });
            return results.ToList();
        }


        public ExamViewModel GetExamViewModel(int examID)
        {
            CIMSData.Database.exam ex = DB11.exam.Find(examID);
            switch (ex.exam_type_id)
            {
                case 1:
                case 5:
                    return GetIntrusionsExamDetails(ex);
                case 2:
                    return GetLitigationSupportExamDetails(ex);
                case 3:
                    return GetStandardExamDetails(ex);
                case 4:
                    if (ex.exam_intrusion == null)
                    {
                        return GetStandardExamDetails(ex);
                    }
                    else
                    {
                        return GetIntrusionsExamDetails(ex);
                    }
                default:
                    throw new Exception("Unrecognized exam type");
            }
        }

        public ActionResult Overview(int examID)
        {
            ViewBag.ExamId = examID;
            CIMSData.Database.exam ex = DB11.exam.Find(examID);
            var model = GetExamViewModel(examID);
            if (ex.exam_type_id == 1 || ex.exam_type_id == 5 || ex.exam_type_id == 4)
            {
                if (ex.exam_type_id == 4 && ex.exam_intrusion == null)
                {
                    return PartialView("_Overview", model);
                }
                ViewBag.RepublishFlag = ex.exam_activity.Any(z =>
                    z.activity_id == (int) ActivityType._Release_Exam_Results && z.date_end.HasValue);
                return PartialView("_OverviewIN", model);
            }
            else if (ex.exam_type_id == 2)
            {
                return PartialView("_OverviewLS", model);
            }
            else
            {
                return PartialView("_Overview", model);
            }
        }


        /// <summary>
        /// Yields json for Kendo grid of all closed exams
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public ActionResult ClosedExamsGridData([DataSourceRequest]DataSourceRequest request)
        {
            var results = DB11.exam.Where(x => x.exam_status.name == "DONE" || x.exam_status.name == "INVALID" || x.exam_status.name == "REJECTED").ToList()
                .Select(x => new ExamViewModel(x));
            return KendoGridResult(results.AsQueryable(), request);
        }

        /// <summary>
        /// Yields json for Kendo grid of all exams currently in a given group
        /// </summary>
        /// <param name="request"></param>
        /// <param name="group"></param>
        /// <returns></returns>
        public ActionResult ExamsByCurrentSectionGridData([DataSourceRequest]DataSourceRequest request, int group)
        {
            var results = DB11.exam.OpenExams()
                .ToList().Select(x => new ExamViewModel(x)
                {
                    CurrentSectionID = x.group_id,
                    CurrentSection = x.group.name
                });
            return KendoGridResult(results.Where(x => x.CurrentSectionID == group).AsQueryable(), request);
            //var results = DB.DCFLEXAMS.Where(x => x.STATUS != "DONE" && x.STATUS != "INVALID" && x.STATUS != "REJECTED");
            //var examList = GetExamList(results);
            //return KendoGridResult(examList.Where(x => x.CurrentSectionID == group).AsQueryable(), request);
        }

        /// <summary>
        /// Yields json for Kendo grid of all exams assigned to a given group
        /// </summary>
        /// <param name="request"></param>
        /// <param name="group"></param>
        /// <returns></returns>
        public ActionResult ExamsByAssignedSectionGridData([DataSourceRequest]DataSourceRequest request, int sectionID)
        {
            var results = DB11.exam.OpenExams().Where(x => x.group_id == sectionID)
                .ToList().Select(x => new ExamViewModel(x)
                {
                    CurrentSectionID = x.group_id,
                    CurrentSection = x.group.name
                });
            return KendoGridResult(results.AsQueryable(), request);
            //return KendoGridResult(GetExamList(results).AsQueryable(), request);
        }

        public IEnumerable<CIMSData.Database.exam> GetActiveExams()
        {
            int accountID = CurrentUserModel.AccountID;
            return DB11.exam_activity.Where(x => x.account_id == accountID).Select(x => x.exam).Distinct().OpenExams();
        }

        public ActionResult CurrentUserActiveExamMenu()
        {
            return PartialView("../Home/MyExamsMenu", GetActiveExams().Select(x => x.exam_number).OrderByDescending(x => x).ToList());
        }

        public ActionResult Timeline(int examID)
        {
            ViewBag.ExamID = examID;
            return PartialView("_TimeLine");
        }

        public ActionResult ConfigurationManagementHistory(int examID)
        {
            ViewBag.ExamID = examID;
            ViewBag.AccountId = CurrentUserModel.AccountID;
            ViewBag.IsExamActive = DB11.exam.Find(examID).exam_status_id == (int) ExamStatus.Active;
            return PartialView("_CMHistory");
        }
        
        public ActionResult ExamTickets(int examID)
        {
            var exam = new ExamViewModel(DB11.exam.Find(examID));
            return PartialView("_ExamTickets", exam);
        }

        public ActionResult Workflow(int examID)
        {
            ViewBag.ExamID = examID;
            return PartialView("_Workflow", GetExamViewModel(examID));
        }

        public ActionResult TimelineUpdate([DataSourceRequest] DataSourceRequest request, TimelineEntryModel model)
        {
            var ea = DB11.exam_activity.Find(model.ExamActivityID);
            AuditUpdate(ea);
            ea.account_id = model.AssigneeID;
            DB11.Entry(ea).State = System.Data.Entity.EntityState.Modified;
            DB11.SaveChanges();
            model.AssignedTo = ea.account?.name.TitleCase();
            return Json(ModelState.ToDataSourceResult());
        }

        public ActionResult TimelineGridData([DataSourceRequest]DataSourceRequest request, int examID)
        {
            var results = DB11.exam_activity.Include(x => x.account).Include(x => x.activity).Where(x => x.exam_id == examID).ToList().OrderBy(x => x.date_start).Select(x => new TimelineEntryModel(x, DB11));
            return KendoGridResult(results.AsQueryable(), request);
        }

        /// <summary>
        /// Checks to see if current user should be allowed to modify communications associated with provided exam
        /// </summary>
        /// <param name="trackingNumber"></param>
        /// <returns></returns>
        public bool HasCommunicationEditPermission(string trackingNumber)
        {
            // TODO - reimplement communication permission control
            return true;
        }

        /// <summary>Retrieves all the communications associated with this case</summary>
        /// <param name="trackingNumber"></param>
        /// <returns></returns>
        public ActionResult Communications(string trackingNumber)
        {
            ViewData["DCFLTrackingNo"] = trackingNumber;
            var comm = GetCommByTrackingNumber(trackingNumber);
            var vmList = new List<Models.Comm.CommViewModel>();
            comm.ForEach(c => vmList.Add(Models.Comm.CommViewModel.ConvertToViewModel(c)));

            ViewData["ExamID"] = DB11.exam.Where(x => x.exam_number == trackingNumber).Select(x => x.exam_id).FirstOrDefault();

            return PartialView("_Comm", vmList);
        }

        /// <summary>Retrieves all the communications associated with this case</summary>
        /// <param name="Exam ID"></param>
        /// <returns></returns>
        public ActionResult GetCommunications(int exam_ID)
        {
            ViewData["DCFLTrackingNo"] = DB11.exam.Where(x => x.exam_id == exam_ID).Select(x => x.exam_number).FirstOrDefault();
            ViewData["ExamID"] = exam_ID;

            return PartialView("_Comm");
        }

        public JsonResult EvidenceTree([DataSourceRequest]DataSourceRequest request, int examID)
        {
            var exam = DB11.exam.First(x => x.exam_id == examID);
            int[] evidenceIDs = exam.exam_evidence.Select(x => x.evidence_id).ToArray();
            var evidence = DB11.evidence.Where(x => evidenceIDs.Contains(x.evidence_id)).ToList().Select(x => new EvidenceViewModel(x, examID)).ToList();
            var tagIds = evidence.Select(x => x.TagID).ToArray();
            var tags = DB11.evidence_tag.Where(x => tagIds.Contains(x.evidence_tag_id)).ToList().Select(x => new EvidenceViewModel(x));
            var results = tags.Concat(evidence).ToList();
            // If a parent id is set to a view id that is not in the collection, it is an orphaned child node and its parent id needs to be set to the tag.
            var viewIds = results.Select(x => x.ViewId).ToList();
            foreach (var item in results.Where(x => x.ParentID != null && !viewIds.Contains(x.ParentID)))
            {
                item.ParentID = "TAG" + item.TagID;
            }
            // If a tag has no children, remove it from the result list
            var parentIds = results.Select(x => x.ParentID).ToList();
            results.RemoveAll(x => x.IsTag && !parentIds.Contains(x.ViewId));
            results = results.OrderBy(z => z.TagType).ThenBy(x => x.TagNumber).ThenBy(z => z.LabelLong).ToList();
            var json = results.ToTreeDataSourceResult(request, e => e.ViewId, e => e.ParentID, e => e);
            return Json(json, JsonRequestBehavior.AllowGet);
        }

        public JsonResult EvidenceStats(int examID)
        {
            var exam = DB11.exam.Find(examID);
            return Json(new
            {
                TagCount = exam.exam_evidence.Select(z => z.evidence).Where(x => x.evidence_tag_id.HasValue).Select(x => x.evidence_tag_id).Distinct().Count(),
                ItemCount = exam.exam_evidence.Count,
                TotalSize = (exam.exam_evidence.Select(z => z.evidence).Sum(x => x.storage_size).GetValueOrDefault() / 1000.0).ToString("n2")
        }, JsonRequestBehavior.AllowGet);
        }

        public JsonResult EvidenceMultiSelect([DataSourceRequest]DataSourceRequest request, int examID)
        {
            var exam = DB11.exam.Find(examID);
            int[] evidenceIDs = exam.exam_evidence.Select(x => x.evidence_id).ToArray();
            var results = DB11.vw_evidence.Where(x => evidenceIDs.Contains(x.evidence_id)).ToList().Select(x => new EvidenceViewModel(x, DB11));
            return Json(results.AsEnumerable(), JsonRequestBehavior.AllowGet);
        }

        public JsonResult EvidenceTreeCheckedIn([DataSourceRequest]DataSourceRequest request, int examID)
        {
            var evidenceQuery = $"select * from cims.vw_evidence where evidence_id in (select evidence_id from cims.exam_evidence where exam_id = {examID}) and evidence_status_id = 5";
            //if (CurrentUserModel.IsIntrusions)
            //{
            //    evidenceQuery += $"and entered_by in (select account_id from cims.account_permission where cims.account_permission.group_id = {(int) Group.Intrusions})";
            //}
            var evidence = DB11.vw_evidence.SqlQuery(evidenceQuery).ToList().Select(x => new EvidenceViewModel(x, DB11)).ToList();

            var tagIds = evidence.Select(x => x.TagID).ToArray();
            var tags = DB11.evidence_tag.Where(x => tagIds.Contains(x.evidence_tag_id)).ToList().Select(x => new EvidenceViewModel(x));
            var results = tags.Concat(evidence).ToList();
            // If a parent id is set to a view id that is not in the collection, it is an orphaned child node and its parent id needs to be set to the tag.
            var viewIds = results.Select(x => x.ViewId).ToList();
            foreach (var item in results.Where(x => x.ParentID != null && !viewIds.Contains(x.ParentID)))
            {
                item.ParentID = "TAG" + item.TagID;
            }
            var json = results.ToTreeDataSourceResult(request, e => e.ViewId, e => e.ParentID, e => e);
            return Json(json, JsonRequestBehavior.AllowGet);
        }

        public JsonResult PackageEvidence([DataSourceRequest]DataSourceRequest request, int examID, int packageID)
        {
            var evidence = DB11.vw_evidence.SqlQuery(
                    $"select * from cims.vw_evidence where evidence_id in (select evidence_id from cims.exam_evidence where exam_id = {examID}) and evidence_status_id = 5")
                .ToList().Select(x => new EvidenceViewModel(x, DB11)).ToList();
            if (packageID != 0)
            {
                var package = DB11.exam_package.Find((packageID));
                foreach (var e in package.evidence)
                {
                    if (!evidence.Any(x => x.ID == e.evidence_id))
                    {
                        evidence.Add(new EvidenceViewModel(e, DB11) { Checked = true });
                    }
                }
            }
            var tagIds = evidence.Select(x => x.TagID).ToArray();
            var tags = DB11.evidence_tag.Where(x => tagIds.Contains(x.evidence_tag_id)).ToList().Select(x => new EvidenceViewModel(x));
            var results = tags.Concat(evidence).ToList();
            // If a parent id is set to a view id that is not in the collection, it is an orphaned child node and its parent id needs to be set to the tag.
            var viewIds = results.Select(x => x.ViewId).ToList();
            foreach (var item in results.Where(x => x.ParentID != null && !viewIds.Contains(x.ParentID)))
            {
                item.ParentID = "TAG" + item.TagID;
            }
            
            var json = results.ToTreeDataSourceResult(request, e => e.ViewId, e => e.ParentID, e => e);
            return Json(json, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        public JsonResult UserEvidenceInventoryTree([DataSourceRequest]DataSourceRequest request, int accountID, int examID)
        {
            var evidence = DB11.vw_evidence.SqlQuery(
                    $"select * from cims.vw_evidence where evidence_id in (select evidence_id from cims.exam_evidence where exam_id = {examID}) and evidence_status_id = 4 and entered_by = {accountID}")
                .ToList().Select(x => new EvidenceViewModel(x, DB11)).ToList();
            var tagIds = evidence.Select(x => x.TagID).ToArray();
            var tags = DB11.evidence_tag.Where(x => tagIds.Contains(x.evidence_tag_id)).ToList().Select(x => new EvidenceViewModel(x));
            var results = tags.Concat(evidence).ToList();
            // If a parent id is set to a view id that is not in the collection, it is an orphaned child node and its parent id needs to be set to the tag.
            var viewIds = results.Select(x => x.ViewId).ToList();
            foreach (var item in results.Where(x => x.ParentID != null && !viewIds.Contains(x.ParentID)))
            {
                item.ParentID = "TAG" + item.TagID;
            }
            var json = results.ToTreeDataSourceResult(request, e => e.ViewId, e => e.ParentID, e => e);
            return Json(json, JsonRequestBehavior.AllowGet);
        }

        public JsonResult ExistingEvidenceTree([DataSourceRequest]DataSourceRequest request, int examID)
        {
            var exam = DB11.exam.Find(examID);
            int[] evidenceIDs = <EMAIL>(x => x.evidence_id).ToArray();
            int[] examEvidenceIDs = exam.exam_evidence.Select(x => x.evidence_id).ToArray();
            var evidence = DB11.evidence.Where(x => evidenceIDs.Contains(x.evidence_id) && !examEvidenceIDs.Contains(x.evidence_id)).ToList()
                .Select(x => new EvidenceViewModel(x)).ToList();
            var tagIds = evidence.Select(x => x.TagID).ToArray();
            var tags = DB11.evidence_tag.Where(x => tagIds.Contains(x.evidence_tag_id)).ToList().Select(x => new EvidenceViewModel(x));
            var results = tags.Concat(evidence).ToList();
            // If a parent id is set to a view id that is not in the collection, it is an orphaned child node and its parent id needs to be set to the tag.
            var viewIds = results.Select(x => x.ViewId).ToList();
            foreach (var item in results.Where(x => x.ParentID != null && !viewIds.Contains(x.ParentID)))
            {
                item.ParentID = "TAG" + item.TagID;
            }
            var json = results.ToTreeDataSourceResult(request, e => e.ViewId, e => e.ParentID, e => e);
            return Json(json, JsonRequestBehavior.AllowGet);
        }

        public ActionResult AddExistingEvidenceExam(int[] evidenceIDs, int ExamID)
        {
            var exam = DB11.exam.Find(ExamID);
            foreach (int eid in evidenceIDs)
            {
                var e = DB11.evidence.Find(eid);
                exam.exam_evidence.Add(new exam_evidence() {evidence = e});
                LogExamEvent(ExamID, $"Added existing evidence item - {new EvidenceViewModel(e).LabelLong}", ExamEventTypes.Add_Existing_Evidence_Item);
            }
            DB11.SaveChanges();
            return new EmptyResult();
        }

        public ActionResult Evidence(int examID)
        {
            var exam = DB11.exam.Find(examID);
            if (exam.case_id == null)
            {
                return PartialView("_NoCase");
            }
            ViewBag.ExamID = examID;
            ViewBag.CaseID = exam.case_id;
            ViewBag.isIntrusionsExam = exam.group_id == (int) Group.Intrusions;
            return PartialView("_Evidence");
        }

        public ActionResult CheckInOut(int examID)
        {
            ViewBag.ExamID = examID;
            return PartialView("../Evidence/Inventory");
        }

        public ActionResult AddExistingEvidence(int examID)
        {
            ViewBag.ExamID = examID;
            return PartialView("../Evidence/AddExisting");
        }

        public ActionResult AddBulkCi(int examID)
        {
            var model = new MultiCiViewModel(examID, DB11);
            ViewBag.hardware = true;
            return PartialView("_AddBulkCiView", model);
        }

        public ActionResult EvidenceReportSettings(int examID)
        {
            var users = DB11.exam_activity.Where(z =>
                    z.exam_id == examID && z.activity_id == (int) ActivityType._Perform_Forensic_Acquisition && z.skipped != true)
                .Select(z => z.account).ToList().DistinctBy(z => z.account_id).Select(z => new EvidenceReportDropDownModel()
                    {Text = z.name, Value = z.account_id}).ToList();
            var curUser = users.FirstOrDefault(z => z.Value == CurrentUserModel.AccountID);
            if (curUser != null)
            {
                users.First(z => z == curUser).Selected = true;
            }

            var labelDict = new Dictionary<int, IEnumerable<string>>();
            foreach (var user in users)
            {
                var labels = DB11.evidence_note.Where(z => z.exam_id == examID && z.account_id == user.Value).Select(z => z.evidence).Distinct().ToList()
                    .Select(z => new EvidenceViewModel(z, examID)).Select(z => z.ViewId);
                labelDict.Add(user.Value, labels);
            }

            return PartialView("../Evidence/EvidenceReportSettings",
                new EvidenceReportSettings { ExamId = examID, Users = users, EvidenceLabels = labelDict });

        }

        public ActionResult EvidenceItems(int examId, int caseId, int evidenceId, int tagId)
        {
            var evidence = tagId > 0
                ? DB11.exam_evidence.Select(z => z.evidence)
                    .Where(x =>
                        x.case_id == caseId && x.evidence_tag_id == tagId && x.evidence_id != evidenceId &&
                        x.exam_evidence.Any(z => z.exam_id == examId)).ToList()
                    .Select(x => new EvidenceViewModel(x)).ToList().RemoveEvidenceChildren(evidenceId, DB11)
                : DB11.exam_evidence.Select(z => z.evidence).Where(x =>
                        x.case_id == caseId && x.evidence_id != evidenceId &&
                        x.exam_evidence.Any(z => z.exam_id == examId))
                    .ToList()
                    .Select(x => new EvidenceViewModel(x)).ToList().RemoveEvidenceChildren(evidenceId, DB11);
            return Json(evidence, JsonRequestBehavior.AllowGet);
        }

        [CIMSAuthorize(SecureFunction.CheckInOutFunctions)]
        public ActionResult EvidenceCheckOut(int[] evidenceIDs, int accountID)
        {
            foreach (int eid in evidenceIDs)
            {
                CIMSData.Database.evidence_history h = new CIMSData.Database.evidence_history()
                {
                    evidence_id = eid,
                    entered_by = accountID,
                    date_logged = DateTime.Now,
                    evidence_status_id = 4
                };
                DB11.evidence_history.Add(h);
                DB11.SaveChanges();
                AuditInsert(h);
                DB11.SaveChanges();
            }
            return new EmptyResult();
        }

        [CIMSAuthorize(SecureFunction.CheckInOutFunctions)]
        public ActionResult EvidenceCheckIn(int[] evidenceIDs, int accountID)
        {
            foreach (int eid in evidenceIDs)
            {
                CIMSData.Database.evidence_history h = new CIMSData.Database.evidence_history()
                {
                    evidence_id = eid,
                    entered_by = accountID,
                    date_logged = DateTime.Now,
                    evidence_status_id = 5
                };
                DB11.evidence_history.Add(h);
                DB11.SaveChanges();
                AuditInsert(h);
                DB11.SaveChanges();
            }
            return new EmptyResult();
        }
        
        public ActionResult AddEvidence(int examID)
        {
            var e = DB11.exam.Find(examID);
            ViewBag.isNewEvidence = 1;
            return PartialView("EditorTemplates/Evidence", new EvidenceViewModel() { ExamID = examID, CaseID = e.case_id.GetValueOrDefault() });
        }

        public ActionResult EditEvidence(int evidenceID, int examID, int? tab = null, int? uid = null)
        {
            var exam = DB11.exam.Where(x => x.exam_id == examID).ToList().Select(x => new UnlockServiceExamModel(x.exam_id, x.exam_number)).FirstOrDefault();
            ViewBag.isNewEvidence = 0;

            var groups = new List<UnlockServiceGroupModel>();
            groups.AddRange(DB11.unlock_srvc_group.ToList().Select(g => new UnlockServiceGroupModel(g)));
            ViewData["groups"] = groups;

            var types = new List<UnlockServiceLockTypeModel>();
            types.AddRange(DB11.unlock_srvc_lock_type.ToList().Select(t => new UnlockServiceLockTypeModel(t)));
            ViewData["types"] = types;

            var unlockServices = new List<UnlockService>();
            unlockServices.AddRange(DB11.unlock_srvc.ToList().Select(t => new UnlockService(t)));
            ViewData["unlockServices"] = unlockServices;

            if (tab == null)
            {
                tab = CurrentUserModel.Permissions.Any(z => z.Group == Group.Evidence) ? 0 : 1;
            }
            var e = new EvidenceViewModel(DB11.evidence.First(z => z.evidence_id == evidenceID), examID, tab);
            var unlockserviceunlockslist = new List<SelectListItem>();
            var i = 0;
            e.UnlockServiceViewID = 0;
            foreach (var uu in e.UnlockServiceUnlocks){
                var text = "";
                var id = "0";
                if (uu.Exam != null)
                {
                    if (uu.UID == uid)
                    {
                        e.UnlockServiceViewID = i;
                    }
                    text = uu.Exam.ExamNumber + " / " + uu.MakeModel + " / ";
                    if (uu.DateStarted.HasValue)
                    {
                        text += "(" + string.Format("{0:MM/dd/yy}", uu.DateStarted) + " - ";
                    }
                    else
                    {
                        text += "(Unknown Start Date - ";
                    }

                    if (uu.DateEnded.HasValue)
                    {
                        text += string.Format("{0:MM/dd/yy}", uu.DateEnded) + ")";
                    }
                    else
                    {
                        text += "Unknown End Date)";
                    }
                    id = uu.UID.ToString();
                }
                else
                {
                    text = "Create New Unlock Attempt...";
                    uu.Exam = exam;
                }
              
                var item = new SelectListItem { Text = text, Value = id };
                unlockserviceunlockslist.Add(item);
                i++;
            }
            ViewData["unlockserviceunlocks"] = unlockserviceunlockslist;
            ViewData["unlockservicetrileanenum"] = EnumHelper.GetSelectList(typeof(UnlockServiceTrileanEnum));
            ViewData["laptop_colors"] = EnumHelper.GetSelectList(typeof(LaptopColorEnum));
            return PartialView("EditorTemplates/Evidence", e);
        }

        public ActionResult DeleteUnlockServiceEvidence(int us_uid)
        {
            var comments = DB11.unlock_srvc_comment.Where(z=> z.unlock_srvc_evidence_id == us_uid).ToList();
            foreach(var comm in comments)
            {
                AuditDelete(comm);
            }
            DB11.unlock_srvc_comment.RemoveRange(comments);
            DB11.SaveChanges();

            var unlockservice_evidence = DB11.unlock_srvc_evidence.FirstOrDefault(z => z.unlock_srvc_evidence_id == us_uid);
            AuditDelete(unlockservice_evidence);
            DB11.unlock_srvc_evidence.Remove(unlockservice_evidence);
            DB11.SaveChanges();
            return new EmptyResult();
        }

        public ActionResult TransferEvidence()
        {
            return PartialView("../Evidence/Transfer");
        }

        [HttpPost]
        public ActionResult SaveEvidencePictures(int evidenceId)
        {
            for (var i = 0; i < Request.Files.Count; i++)
            {
                var picture = Request.Files[i];
                var evidencePicture = new evidence_image {evidence_id = evidenceId, file_name = picture.FileName};
                using (var reader = new BinaryReader(picture.InputStream))
                {
                    evidencePicture.image = reader.ReadBytes(picture.ContentLength);
                }

                DB11.evidence_image.Add(evidencePicture);
                DB11.SaveChanges();
            }
            return new EmptyResult();
        }

        public ActionResult DeleteEvidencePicture(int picId)
        {
            var pic = DB11.evidence_image.First(z => z.evidence_image_id == picId);
            DB11.evidence_image.Remove(pic);
            DB11.SaveChanges();
            return new EmptyResult();
        }

        private string BuildMD5Hash(HttpPostedFileBase picture)
        {
            byte[] fileData;
            using (var binaryReader = new BinaryReader(picture.InputStream, Encoding.UTF8, true))
            {
                fileData = binaryReader.ReadBytes(picture.ContentLength);
            }
            var md5 = System.Security.Cryptography.MD5.Create();
            var hash = md5.ComputeHash(fileData);
            var sb = new StringBuilder();
            foreach (var t in hash)
            {
                sb.Append(t.ToString("X2"));
            }
            return sb.ToString();
        }

        [HttpPost]
        public ActionResult SaveEvidence(EvidenceViewModel model)
        {
            if (model.ID == 0)
            {
                if (model.OpticalCount > 1)
                {
                    // If the evidence is of type optical media and there is more than one,
                    // loop through adding a row for each one
                    var label = model.Item;
                    for (var i = 1; i <= model.OpticalCount; i++)
                    {
                        if (label != null) model.Item = label + "-" + i;
                        else model.Item = i.ToString();

                        SaveEvidenceDB(model);
                    }
                }
                else
                {
                    // Otherwise, just create the one row as usual
                    SaveEvidenceDB(model);
                }
            }
            else
            {
                evidence e = DB11.evidence.Find(model.ID);
                AuditUpdate(e);
                model.Update(e);
                DB11.Entry(e).State = EntityState.Modified;
                DB11.SaveChanges();
                model.UpdateConfigurationItems(DB11);

                var exev = DB11.exam_evidence.FirstOrDefault(z => z.evidence_id == e.evidence_id);
                if (exev != null)
                {
                    exev.dmr = model.IsDMR;
                    AuditUpdate(exev);
                    DB11.Entry(exev).State = EntityState.Modified;
                    DB11.SaveChanges();
                }
                else
                {
                    DB11.exam_evidence.Add(new exam_evidence() { evidence_id = e.evidence_id, exam_id = model.ExamID, dmr = model.IsDMR });
                    DB11.SaveChanges();
                }

                // Blowing away old details and replacing. Would be a ton of code to update everything in place.
                foreach (var existingDbDetail in e.evidence_detail.ToList())
                {
                    AuditDelete(existingDbDetail);
                    DB11.evidence_detail.Remove(existingDbDetail);
                }
                DB11.SaveChanges();

                // Save details
                SaveEvidenceDetails(model, e);

                var ufed_premium_unlock_service = DB11.unlock_srvc.FirstOrDefault(x =>
                    x.unlock_srvc1 == "UFED Premium");
                // Loop through UnlockServiceUnlocks and Save each
                foreach (var u in model.UnlockServiceUnlocks)
                {
                    if (ufed_premium_unlock_service.unlock_srvc_id != u.UnlockService.Unlock_service_id){
                        u.TokenRestored = UnlockServiceTrileanEnum.No;
                        u.DongleNumber = null;
                        u.TokenUsed = BoolToYesNo.No;
                        u.LaptopColor = LaptopColorEnum.Unknown;
                    }
                    if (u.hasDefaults())
                        continue;
                    if (u.UID == 0)
                    {
                        // Create/Save Unlock Service Details
                        u.EvidenceID = model.ID;
                      
                        InsertUnlockService(u);
                    }
                    else
                    {
                        // Update/Save Unlock Service Details
                        var ue = DB11.unlock_srvc_evidence.Find(u.UID);
                        AuditUpdate(ue);
                        u.Update(ue);
                    }
                    // Blowing away old comments and replacing.
                    var old_comment = DB11.unlock_srvc_comment.Where(z => z.unlock_srvc_evidence_id == u.UID).ToList();

                    foreach (var existingComment in old_comment)
                    {
                        AuditDelete(existingComment);
                        DB11.unlock_srvc_comment.Remove(existingComment);
                    }
                    DB11.SaveChanges();
                    if (u.Comments == null) continue;
                    foreach (var c in u.Comments)
                    {
                        if (c.Comment == "")
                        {
                            continue;
                        }
                        // Create/Save Comments
                        c.EvidenceID = u.UID;
                        InsertComment(c);
                    }
                }
                DB11.SaveChanges();
            }
            return Json(new { EvidenceId = model.ID }, JsonRequestBehavior.AllowGet);
        }

        public string GenerateNas(int examId, int tagId, string item, int typeId)
        {
            string tracking = DB11.exam.First(z => z.exam_id == examId).exam_number;
            // ie DCFL-2018-I000082 -> 180082
            tracking = tracking.Substring(7, 2) + tracking.Substring(tracking.Length - 4);
            string tag = "";
            if (tagId > 0)
            {
                var dbTag = DB11.evidence_tag.Find(tagId);
                if (dbTag != null)
                {
                    tag = string.Format("{0}{1}", dbTag.evidence_tag_type.tag_type, dbTag.tag_number);
                }
            }
            string label = item;
            string evType = DB11.evidence_type.Where(x => x.evidence_type_id == typeId).Select(x => x.type_abbr).FirstOrDefault();
            int num = 1;
            string nasName = null;
            bool done = false;
            while (!done)
            {
                StringBuilder sb = new StringBuilder();
                sb.Append(tracking);
                if (!string.IsNullOrEmpty(tag))
                {
                    sb.AppendFormat("_{0}", tag);
                }
                if (!string.IsNullOrEmpty(label))
                {
                    sb.AppendFormat("_{0}", label);
                }
                if (!string.IsNullOrEmpty(evType))
                {
                    sb.AppendFormat("_{0}", evType);
                }
                sb.AppendFormat("_{0:D3}", num);
                nasName = sb.ToString();
                if (!DB11.evidence.Any(x => x.nas_image == nasName))
                {
                    done = true;
                }
                else
                {
                    num++;
                }
            }
            return nasName;
        }


        [AcceptVerbs(HttpVerbs.Post)]
        public ActionResult AddUnlockServiceComment([DataSourceRequest] DataSourceRequest request, UnlockServiceCommentModel product)
        {
            return Json(new[] { product }.ToDataSourceResult(request, ModelState));
        }

        public ActionResult GetAdditionalCiForExam(int examId, bool hardware)
        {
            var models = DB11.evidence_config_item_detail.Where(z =>
                    z.exam_id == examId && z.evidence_ci_detail_type.software != hardware).ToList()
                .Select(z => new CIAdditionalItemViewModel(z)).GroupBy(z => new {z.CiKey, z.CiValue})
                .Select(z => z.First());
            return Json(models, JsonRequestBehavior.AllowGet);
        }

        private void SaveEvidenceDB(EvidenceViewModel model)
        {
            CIMSData.Database.evidence newEvidence = model.Create();
            var exam = DB11.exam.Find(model.ExamID);
            DB11.evidence.Add(newEvidence);
            
            AuditInsert(newEvidence);
            DB11.SaveChanges();

            var new_exam_evidence = new exam_evidence() {evidence = newEvidence};
            new_exam_evidence.dmr = model.IsDMR;
            exam.exam_evidence.Add(new_exam_evidence);
            AuditInsert(new_exam_evidence);
            DB11.SaveChanges();

            model.ID = newEvidence.evidence_id;
            model.UpdateConfigurationItems(DB11);

            CIMSData.Database.evidence_history hist = new CIMSData.Database.evidence_history()
            {
                evidence_id = newEvidence.evidence_id,
                date_logged = DateTime.Now,
                entered_by = CurrentUserModel.AccountID
            };
            // An I&E user is adding a subitem so check it out to them and add an acquisition activity for them
            if (CurrentUserModel.Permissions.Any(z => z.Group == Group.ImagingExtraction))
            {
                var evidenceList =
                    new List<evidence> {DB11.evidence.First(x => x.evidence_id == newEvidence.evidence_id)};
                var ea = new exam_activity
                {
                    account_id = CurrentUserModel.AccountID,
                    activity_id = (int) ActivityType._Perform_Forensic_Acquisition,
                    exam_id = model.ExamID,
                    evidence = evidenceList,
                    group_id = (int)Group.ImagingExtraction,
                    date_start = DateTime.Now
                };
                DB11.exam_activity.Add(ea);
                DB11.SaveChanges();
                AuditInsert(ea);
                DB11.SaveChanges();
                hist.evidence_status_id = 4; // Evidence is checked out to start
            }
            else
            {
                hist.evidence_status_id = 5; // Evidence is checked in to start
            }
            
            DB11.evidence_history.Add(hist);
            DB11.SaveChanges();
            AuditInsert(hist);
            DB11.SaveChanges();
            SaveEvidenceDetails(model, newEvidence);
            using (var newContext = new cims11Entities()) // have to use a new db handle to get the evidence object with its foreign keys resolved into complex objects
            {
                var label = new EvidenceViewModel(newContext.evidence.Find(newEvidence.evidence_id)).LabelLong;
                LogExamEvent(model.ExamID, $"Added new evidence item - {label}", ExamEventTypes.Add_New_Evidence_Item);
            }
        }

        private void InsertUnlockService(UnlockServiceDetailsModel udm)
        {
            // Cancel the insert, if the Unlock Service Details are still the default values
            if (udm.hasDefaults()) return;
          
            CIMSData.Database.unlock_srvc_evidence newUnlockSrvcEv = udm.Create();
            DB11.unlock_srvc_evidence.Add(newUnlockSrvcEv);
            DB11.SaveChanges();

            AuditInsert(newUnlockSrvcEv);
            DB11.SaveChanges();

            udm.UID = newUnlockSrvcEv.unlock_srvc_evidence_id;
        }

        private void InsertComment(UnlockServiceCommentModel ucm)
        {
            CIMSData.Database.unlock_srvc_comment newComment = ucm.Create();

            DB11.unlock_srvc_comment.Add(newComment);
            DB11.SaveChanges();

            AuditInsert(newComment);
            DB11.SaveChanges();

            ucm.CommentID = newComment.unlock_srvc_comment_id;
        }

        private void SaveEvidenceDetails(EvidenceViewModel model, CIMSData.Database.evidence evidence)
        {
            foreach (var det in model.Details.Where(z => z.DetailKey.HasValue() && z.DetailValue.HasValue()))
            {
                // Check for existing detail type entry
                var dt = DB11.evidence_detail_type.Where(x => x.detail_type == det.DetailKey).FirstOrDefault();
                if (dt == null)
                {
                    dt = new CIMSData.Database.evidence_detail_type()
                    {
                        detail_type = det.DetailKey
                    };
                    DB11.evidence_detail_type.Add(dt);
                    DB11.SaveChanges();
                    AuditInsert(dt);
                    DB11.SaveChanges();
                }
                // Check for existing detail value entry
                var dv = DB11.evidence_detail_value.Where(x => x.detail_value == det.DetailValue).FirstOrDefault();
                if (dv == null)
                {
                    dv = new CIMSData.Database.evidence_detail_value()
                    {
                        detail_value = det.DetailValue
                    };
                    DB11.evidence_detail_value.Add(dv);
                    AuditInsert(dv);
                    DB11.SaveChanges();
                }
                var existing_evidence_detail = DB11.evidence_detail
                    .FirstOrDefault(x => x.evidence_id == evidence.evidence_id &&
                                         x.evidence_detail_type_id == dt.evidence_detail_type_id &&
                                         x.evidence_detail_value_id == dv.evidence_detail_value_id);
                if(existing_evidence_detail == null)
                {
                    // Create new detail entry
                    CIMSData.Database.evidence_detail ed = new CIMSData.Database.evidence_detail()
                    {
                        evidence_id = evidence.evidence_id,
                        evidence_detail_type_id = dt.evidence_detail_type_id,
                        evidence_detail_value_id = dv.evidence_detail_value_id
                    };
                    DB11.evidence_detail.Add(ed);
                    AuditInsert(ed);
                    DB11.SaveChanges();
                }
            }
        }

        private string GetEvidenceLink(string trackingNumber, string nasImage)
        {
            string ret = null;
            string imagesRoot = WebConfigurationManager.AppSettings["ImagesRoot"];
            if (trackingNumber.StartsWith("DCFL-"))
            {
                string[] tokens = trackingNumber.Split('-');
                string year = tokens[1];
                string exam = tokens[2];
                string examNum = exam.Substring(exam.Length - 4);
                int examNumInt = 0;
                if (Int32.TryParse(examNum, out examNumInt))
                {
                    string examFolder = string.Format("{0}{1}", year.Substring(2), examNum);
                    int low = examNumInt / 100 * 100;
                    int high = low + 99;
                    string examGroupFolder = string.Format("{0}-{1}", low, high);
                    ret = string.Format(@"file://{0}/{1}/{2}/{3}/{4}", imagesRoot, year, examGroupFolder, examFolder, nasImage);
                }
            }
            return ret;
        }

        private StandardExamViewModel GetStandardExamDetails(CIMSData.Database.exam exam)
        {
            StandardExamViewModel standardExamViewModel = new StandardExamViewModel(exam.exam_standard, DB11);
            standardExamViewModel.EvidenceReturnAddress = new ContactViewModel(DB11.contact.FirstOrDefault(x => x.contact_id == exam.evidence_return_address_id)) { RelationshipType = "EvidenceReturnAddress", ExamID = exam.exam_id };
            standardExamViewModel.ReportReturnAddress = new ContactViewModel(DB11.contact.FirstOrDefault(x => x.contact_id == exam.report_return_address_id)) { RelationshipType = "ReportReturnAddress", ExamID = exam.exam_id };
            standardExamViewModel.Case = new CaseViewModel(exam.@case, DB11);
            var comm = DB11.communication.Where(x => x.exam_id == exam.exam_id && x.child_id == null);
            standardExamViewModel.NumOfCommunications = comm.Count();
            standardExamViewModel.NumOfSytemGeneratedCommunications = comm.Where(x => x.communication_type_id == 5).Count();
            standardExamViewModel.CaseEvidenceTB = exam.@case != null ? <EMAIL>(x => x.storage_size).GetValueOrDefault() / 1000.0 : 0.0;
            standardExamViewModel.OpenActivityCount = exam.exam_activity.Count(x => x.skipped != true && x.date_start.HasValue && !x.date_end.HasValue);
            return standardExamViewModel;
        }

        private IntrusionsExamViewModel GetIntrusionsExamDetails(CIMSData.Database.exam exam)
        {
            exam_intrusion examIntrusion = exam.exam_intrusion;
            var isEMS = exam.exam_number.Contains("E") && !exam.exam_number.EndsWith("E");
            var caseAgentLabel = isEMS ? "DC3 Analyst" : "Case Agent";
            var prosecutorLabel = isEMS ? "External Contact" : "Alternate Contact";
            IntrusionsExamViewModel intrusionsExamViewModel = new IntrusionsExamViewModel(examIntrusion, DB11)
            {
                Case = new CaseViewModel(exam.@case, DB11) {CaseAgent = {RelationshipType = caseAgentLabel}, 
                    Prosecutor = {RelationshipType = prosecutorLabel}},
                AdditionalExaminers = examIntrusion.exam.exam_activity
                    .Where(x => x.activity_id == (int) ActivityType._Perform_Analysis &&
                                x.account_id != examIntrusion.exam.lead_examiner_id && x.skipped != true).ToList()
                    .Select(x => x.account.name.FormatName()).Distinct().ToArray()
            };
            List<int> AdtlExaminersIDs = examIntrusion.exam.exam_activity.Where(x => x.activity_id == (int)ActivityType._Perform_Analysis && x.account_id != examIntrusion.exam.lead_examiner_id && x.skipped != true).Select(x => x.account.account_id).Distinct().ToList();
            List<AccountViewModel> inAcct = new List<AccountViewModel>();
            foreach (int inID in AdtlExaminersIDs)
            {
                inAcct.Add(new AccountViewModel(DB11.account.Where(x => x.account_id == inID).First()));
            }
            intrusionsExamViewModel.AdditionalExaminersIDs = inAcct;

            return intrusionsExamViewModel;
        }

        private LitigationSupportExamViewModel GetLitigationSupportExamDetails(CIMSData.Database.exam exam)
        {
            LitigationSupportExamViewModel lvm = new LitigationSupportExamViewModel(exam, DB11)
            {
                Case = new CaseViewModel(exam.@case, DB11),
                EvidenceReturnAddress = new ContactViewModel(exam.contact)
                {
                    RelationshipType = "EvidenceReturnAddress", ExamID = exam.exam_id
                },
                ReportReturnAddress = new ContactViewModel(exam.contact)
                {
                RelationshipType = "ReportReturnAddress", ExamID = exam.exam_id
            }
            };
            return lvm;
        }

        /// <summary>
        /// GET for info pertaining to a specific exam
        /// </summary>
        ///  <param name="trackingNumber"></param>
        /// <param name="examID"></param>
        /// <returns></returns>
        public ActionResult Details(int id)
        {
            return PartialView(GetExamViewModel(id));
        }

        public ActionResult DetailsExamNumber(string examNumber)
        {
            var id = DB11.exam.First(z => z.exam_number == examNumber).exam_id;
            return PartialView("Details", GetExamViewModel(id));
        }


        /// <summary>
        /// GET list of exams as JSON
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public JsonResult List()
        {
            return Json(DB11.exam.Select(x => x.exam_number).OrderByDescending(x => x).ToList(), JsonRequestBehavior.AllowGet);
        }

        public ActionResult EditExam(int id)
        {
            StandardExamViewModel model = new StandardExamViewModel(DB11.exam_standard.Where(x => x.exam_id == id).First(), DB11);
            ViewBag.IsCategorySet = !string.IsNullOrEmpty(model.Category);
            ViewBag.InvestigationTypes = DB11.exam_investigation_type
                   .ToList()
                   .Select(x => new SelectListItem() { Value = x.exam_investigation_type_id.ToString(), Text = x.exam_investigation_type1 });
            ViewBag.IsContractor = CurrentUserModel.IsContractor;
            return PartialView("_EditExam", model);
        }

        public ActionResult ExamHold(int id, bool isHold = true, int examHoldId = 0)
        {
            var model = new ExamHoldViewModel {ExamID = id, IsHold = isHold};
            if (examHoldId > 0) model.ExamHoldID = examHoldId;
            return PartialView("_ExamHold", model);
        }

        public ActionResult SuspenseHistory(int id, bool ie)
        {
            var models = new List<ExamEventViewModel>();
            var eventType = ie ? (int)ExamEventTypes.Reset_IE_Suspense_Date : (int)ExamEventTypes.Reset_Lab_Suspense_Date;
            DB11.exam_event.Where(z => z.exam_id == id && z.exam_event_type_id == eventType).ForEach(z => models.Add(new ExamEventViewModel(z)));
            return PartialView("_ExamSuspenseDates", models);
        }

        public ActionResult EditExamHold(int examActivityID, bool readOnly = false)
        {
            exam_activity ex = DB11.exam_activity.Find(examActivityID);
            var mod = new ExamHoldViewModel() { ExamID = ex.exam_id };
            exam_hold eh = ex.exam_hold.FirstOrDefault();
            if (eh != null)
            {
                mod.AdditionalInformation = eh.details_on_hold;
                if (ex.date_end.HasValue)
                {
                    mod.HoldRemovedAdditionalInformation = eh.details_off_hold;
                    mod.DoesExamActivityHaveEndDate = true;
                    mod.EndDate = ex.date_end.Value;
                }
                mod.HoldTypeID = eh.exam_hold_type_id;
                mod.ExamHoldID = eh.exam_hold_id;
                if (ex.date_start.HasValue) mod.StartDate = ex.date_start.Value;
            }

            var view = readOnly ? "_ExamHoldReadOnly" : "_ExamHold";
            ViewBag.IsEdit = true;
            return PartialView(view, mod);
        }

        public ActionResult POSTResults(int examId, bool isIE)
        {
            var mostRecentBuild = DB11.cm_history.Where(z =>
                    z.account_id == CurrentUserModel.AccountID &&
                    (z.history_type_id == (int) CMHistoryTypes.Set_Build ||
                     z.history_type_id == (int) CMHistoryTypes.POST_Success))
                .OrderByDescending(z => z.date).ToList();
            int? inventoryId = null;
            int? buildId = null;
            bool vde = false;
            if (mostRecentBuild.Any())
            {
                inventoryId = mostRecentBuild.First().inventory_id;
                buildId = mostRecentBuild.First().build_id;
                if (mostRecentBuild.First().vde == true)
                {
                    vde = true;
                }
            }
            var model = new POSTResultsViewModel()
                {ExamId = examId, AccountId = CurrentUserModel.AccountID, isIE = isIE, Date = DateTime.Now, VDE = vde};
            if (inventoryId.HasValue) model.CMInventoryId = (int) inventoryId;
            if (buildId.HasValue) model.CMBuildId = (int) buildId;
            if (isIE)
            {
                ViewBag.Evidence = DB11.exam_evidence.Where(z => z.exam_id == examId).ToList()
                    .Select(z => new EvidenceViewModel(z.evidence)).OrderBy(z => z.TagType).ThenBy(z => z.TagNumber)
                    .ThenBy(z => z.LabelLong).Select(z => new {Text = z.LabelLong, Value = z.ID});
            }
            return PartialView("_POSTResults", model);
        }
        
        public ActionResult UpdatePOSTResults(int id)
        {
            var model = new POSTResultsViewModel(DB11.cm_history.Find(id));
            if (model.isIE)
            {
                ViewBag.Evidence = DB11.exam_evidence.Where(z => z.exam_id == model.ExamId).ToList()
                    .Select(z => new EvidenceViewModel(z.evidence)).OrderBy(z => z.TagType).ThenBy(z => z.TagNumber)
                    .ThenBy(z => z.LabelLong).Select(z => new {Text = z.LabelLong, Value = z.ID});   
            }
            return PartialView("_POSTResults", model);
        }

        public ActionResult DeletePOSTResult(int id)
        {
            DB11.cm_history_evidence.RemoveRange(DB11.cm_history_evidence.Where(z => z.cm_history_id == id));
            DB11.cm_history.Remove(DB11.cm_history.Find(id));
            DB11.SaveChanges();
            return new EmptyResult();
        }

        [CIMSAuthorize(SecureFunction.IntrusionsExamAdministration)]
        public ActionResult EditIntrusionExam(int id)
        {
            IntrusionsExamViewModel model = new IntrusionsExamViewModel(DB11.exam_intrusion.First(x => x.exam_id == id), DB11);
            return PartialView("_EditIntrusionsExam", model);
        }

        public ActionResult EditLitigationExam(int id)
        {
            // Does this get deleted?
            LitigationSupportExamViewModel model = new LitigationSupportExamViewModel(DB11.exam.Find(id), DB11);
            return PartialView("_EditLitigationExam", model);
        }

        [CIMSAuthorize(SecureFunction.IntrusionsExamAdministration)]
        public ActionResult EditIntrusionsCase(int? caseID, int examID)
        {
            ViewBag.ExamID = examID;
            if (!caseID.HasValue)
            {
                return PartialView("_EditIntrusionsCase", new CaseViewModel());
            }
            var c = <EMAIL>(x => x.case_id == caseID);
            CaseViewModel model = new CaseViewModel(c, DB11);
            return PartialView("_EditIntrusionsCase", model);
        }

        [HttpPost]
        public ActionResult NewIntrusionsCase()
        {
            CaseViewModel model = new CaseViewModel();
            return PartialView("_EditIntrusionsCase", model);
        }

        public ActionResult NewCase()
        {
            CaseViewModel model = new CaseViewModel();
            ViewBag.caseDirectory = false;
            return PartialView("_EditCase", model);
        }

        public ActionResult EditCase(int? caseID, int? examID=null, bool caseDirectory = false)
        {
            CaseViewModel model = new CaseViewModel();
            ViewBag.ExamID = examID;
            ViewBag.caseDirectory = caseDirectory;
            if (caseID.HasValue)
            {
                model = new CaseViewModel(<EMAIL>(caseID), DB11);
            }
            if (examID.HasValue)
            {
                ViewBag.PreExamFlag = DB11.exam.First(x => x.exam_id == examID).exam_type_id == 4;
            }

            return PartialView("_EditCaseExam", model);
        }

        /// <summary>
        /// Used for quick ajax call to determine if user is trying to use an existing or new case on focusout event.
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="examID"></param>
        /// <returns></returns>
        public ActionResult DoesCaseNumberExist(string caseNumber, int? examID = null)
        {
            var getCase = <EMAIL>(z => z.case_number == caseNumber);
            if (getCase != null)
            {
                return EditCase(getCase.case_id, examID);
            }
            return Json(false, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// Edit a contact
        /// </summary>
        /// <param name="type">Relationship Type</param>
        /// <param name="id">ORIGREC</param>
        /// <param name="examId">exam ID</param>
        /// <returns></returns>
        public ActionResult EditContact(string type, int? id, int? examID, int? caseID)
        {
            ContactViewModel ret = null;
            if (id.HasValue)
            {
                ret = new ContactViewModel(DB11.contact.First(x => x.contact_id == id.Value))
                {
                    RelationshipType = type
                };
            }
            else
            {
                ret = new ContactViewModel()
                {
                    RelationshipType = type
                };
            }

            //Map contact to exam
            if (examID.HasValue) ret.ExamID = examID.Value;
            if (caseID.HasValue) ret.CaseID = caseID.Value;
            return PartialView("_EditExamContact", ret);
        }

        [HttpPost]
        public ActionResult SaveExam(StandardExamViewModel vm)
        {
            CIMSData.Database.exam_standard e = DB11.exam_standard.Where(x => x.exam_id == vm.ExamID).First();
            if (vm.SuspenseDateExam != e.suspense_lab)
            {
                AddExamEventForChangedSuspenseDate(vm.ExamID, vm.SuspenseDateExam, false);
            }
            if (vm.SuspenseDateIE != e.suspense_ie)
            {
                AddExamEventForChangedSuspenseDate(vm.ExamID, vm.SuspenseDateIE, true);
            }
            if (vm.AssignedSectionID != e.exam.group_id.ToString())
            {
                var section = DB11.group.First(z => z.group_id.ToString() == vm.AssignedSectionID).name;
                LogExamEvent(vm.ExamID, $"Assigned section changed to {section}", ExamEventTypes.Exam_Assigned_Section_Changed);
            }
            if (e.exam.group_id != int.Parse(vm.AssignedSectionID))
            {
                // Need to update open activities to new section
                var openActivities = DB11.exam_activity.Where(z =>
                    z.exam_id == vm.ExamID && !z.date_end.HasValue && z.group_id.HasValue &&
                    (z.activity_id == (int) ActivityType._Assign_Examination_Team ||
                     z.activity_id == (int) ActivityType._Assign_Examination_Reviewer ||
                     z.activity_id == (int) ActivityType._Section_Chief_Examination_Review ||
                     z.activity_id == (int) ActivityType._Section_Chief_QA_Check)).ToList();
                openActivities.ForEach(x =>
                {
                    x.group_id = int.Parse(vm.AssignedSectionID);
                    AuditUpdate(x);
                    DB11.Entry(x).State = System.Data.Entity.EntityState.Modified;
                });
            }
            AuditUpdate(e);
            vm.Update(e, DB11);
            DB11.Entry(e).State = System.Data.Entity.EntityState.Modified;
            DB11.SaveChanges();
            vm.Subcategory = e.exam.exam_category.exam_subcategory.TitleCase();
            vm.AssignedSection = e.exam.group.name;
            vm.ExamInvestigation = e.exam.exam_investigation_type.exam_investigation_type1;
            return PartialView("_ExamInfo", vm);
        }

        private void AddExamEventForChangedSuspenseDate(int examId, DateTime? suspenseDate, bool ie)
        {
            var eventType =
                ie ? ExamEventTypes.Reset_IE_Suspense_Date : ExamEventTypes.Reset_Lab_Suspense_Date;
            var examEvent = new exam_event
            {
                event_date = DateTime.Now, account_id = CurrentUserModel.AccountID, exam_id = examId,
                exam_event_type_id = (int)eventType
            };
            var type = eventType == ExamEventTypes.Reset_IE_Suspense_Date ? "I&E" : "Lab";
            var eventInfo = $"{type} Suspense date was removed.";
            if (suspenseDate.HasValue)
            {
                var date = (DateTime)suspenseDate;
                eventInfo = $"{type} Suspense date changed to " + date.ToShortDateString();
            }

            examEvent.event_info = eventInfo;
            DB11.exam_event.Add(examEvent);
            DB11.SaveChanges();
        }

        [HttpPost]
        [CIMSAuthorize(SecureFunction.IntrusionsExamAdministration)]
        public ActionResult SaveIntrusionsExam(IntrusionsExamViewModel vm)
        {
            CIMSData.Database.exam_intrusion e = DB11.exam_intrusion.First(x => x.exam_id == vm.ExamID);
            AuditUpdate(e);
            vm.Update(e, DB11);
            DB11.Entry(e).State = System.Data.Entity.EntityState.Modified;
            DB11.SaveChanges();
            vm = GetIntrusionsExamDetails(e.exam);
            return PartialView("_IntrusionsExamInfo", vm);
        }

        [HttpPost]
        public ActionResult SaveLitigationExam(LitigationSupportExamViewModel vm)
        {
            CIMSData.Database.exam e = DB11.exam.Find(vm.ExamID);
            if (e.group_id != int.Parse(vm.AssignedSectionID))
            {
                // Need to update open activities to new section
                var openActivities = DB11.exam_activity.Where(z =>
                    z.exam_id == vm.ExamID && !z.date_end.HasValue && z.group_id.HasValue &&
                    (z.activity_id == (int) ActivityType._Assign_Examiner_For_Trial_Prep ||
                     z.activity_id == (int) ActivityType._Assign_Examiner_To_Restore_Images)).ToList();
                openActivities.ForEach(x =>
                {
                    x.group_id = int.Parse(vm.AssignedSectionID);
                    AuditUpdate(x);
                    DB11.Entry(x).State = System.Data.Entity.EntityState.Modified;
                });
                var section = DB11.group.First(z => z.group_id.ToString() == vm.AssignedSectionID).name;
                LogExamEvent(vm.ExamID, $"Assigned section changed to {section}", ExamEventTypes.Exam_Assigned_Section_Changed);
            }
            AuditUpdate(e);
            vm.Update(e, DB11);
            DB11.Entry(e).State = System.Data.Entity.EntityState.Modified;
            DB11.SaveChanges();
            vm.Subcategory = e.exam_category.exam_subcategory.TitleCase();
            vm.AssignedSection = e.group.name;
            return PartialView("_LitigationExamInfo", vm);
        }

        private Dictionary<string, object> SavingCase(CaseViewModel vm, int? examID = null)
        {
            @case c;
            exam e = null;
            
            if(vm.CaseID == 0) // New case
            {
                c = new @case {
                    case_number = vm.CaseNumber
                };
                vm.Update(c, DB11);
                <EMAIL>(c);
                DB11.SaveChanges();
                AuditInsert(c);
                DB11.SaveChanges();
                // Refresh the case so that its links to other objects are updated
                DB11.Entry(c).State = System.Data.Entity.EntityState.Detached;
                c = <EMAIL>(c.case_id);
                vm.CaseID = c.case_id;
            }
            else
            {
                c = <EMAIL>(x => x.case_id == vm.CaseID);
                AuditUpdate(c);
                vm.Update(c, DB11);
                DB11.Entry(c).State = EntityState.Modified;
                DB11.SaveChanges();
            }
            if(examID.HasValue)
            {
                e = DB11.exam.First(x => x.exam_id == examID.Value);
                AuditUpdate(e);
                e.@case = c;
                DB11.Entry(e).State = EntityState.Modified;
                DB11.SaveChanges();
            }

            var subjIDs = vm.Subjects.Where(x => x.EntryID != 0).Select(x => x.EntryID).ToArray();
            foreach(var subjToDel in c.case_subject.Where(x => !subjIDs.Contains(x.case_subject_id)).ToList())
            {
                AuditDelete(subjToDel);
                DB11.case_subject.Remove(subjToDel);
            }
            var victimIDs = vm.Victims.Where(x => x.EntryID != 0).Select(x => x.EntryID).ToArray();
            foreach(var victToDel in c.case_victim.Where(x => !victimIDs.Contains(x.case_victim_id)).ToList())
            {
                AuditDelete(victToDel);
                DB11.case_victim.Remove(victToDel);
            }
            foreach(var s in vm.Subjects)
            {
                if(s.EntryID != 0)
                {
                    case_subject dbS = DB11.case_subject.First(x => x.case_subject_id == s.EntryID);
                    AuditUpdate(dbS);
                    s.Update(dbS);
                    DB11.Entry(dbS).State = EntityState.Modified;
                }
                else
                {
                    case_subject dbS = s.CreateSubject(vm.CaseID);
                    DB11.case_subject.Add(dbS);
                    DB11.SaveChanges();
                    AuditInsert(dbS);
                    DB11.SaveChanges();
                }
            }
            foreach(var v in vm.Victims)
            {
                if(v.EntryID != 0)
                {
                    case_victim dbV = DB11.case_victim.First(x => x.case_victim_id == v.EntryID);
                    AuditUpdate(dbV);
                    v.Update(dbV);
                    DB11.Entry(dbV).State = EntityState.Modified;
                }
                else
                {
                    case_victim dbV = v.CreateVictim(vm.CaseID);
                    DB11.case_victim.Add(dbV);
                    DB11.SaveChanges();
                    AuditInsert(dbV);
                    DB11.SaveChanges();
                }
            }
            c.account.Clear();
            if(vm.DCFLMemberConsultedIDs != null)
            {
                foreach(int dcflID in vm.DCFLMemberConsultedIDs)
                {
                    CIMSData.Database.account a = DB11.account.Where(x => x.account_id == dcflID).First();
                    c.account.Add(a);
                }
            }
            DB11.SaveChanges();
            var returned_list = new Dictionary<string, object>();
            returned_list.Add("case", c);
            returned_list.Add("exam", e);
            return returned_list;
        }

        [HttpPost]
        public ActionResult SaveCase(CaseViewModel vm, int? examID = null)
        {
            var isCreateExam = vm.IsCreateExam;
            
            var dict = SavingCase(vm, examID);
            var c = (@case)dict["case"];
            exam e = (exam)dict["exam"];

            vm = new CaseViewModel(c, DB11);

            if (isCreateExam == true)
            {
                return Content(vm.CaseID.ToString(), "application/json");
            }

            if (e == null)
            {
                return PartialView("_CaseInfo", vm);
            }
            else
            {
                return RedirectToAction("Overview", new { examID = e.exam_id});
            }
        }

        [CIMSAuthorize(SecureFunction.CustomerPortalExport)]
        [HttpPost]
        public ActionResult CustomerPortalExport()
        {
            CustomerPortalModel custPortalExport = new CustomerPortalModel();
            string json = custPortalExport.GenerateJson(DB11);

            Response.Clear();
            Response.ContentType = "application/octet-stream";
            Response.AddHeader("Content-Disposition", "attachment;filename=CustomerPortal_" + DateTime.Now.ToString().Replace(' ', '_') + ".txt");
            Response.Write(json);
            Response.End();
            
            return new EmptyResult();
        }

        private List<CIMSData.Database.communication> GetCommByTrackingNumber(string trackingNumber)
        {
            //return DB.communications.Where(x => x.dcfltrackingno == trackingNumber && x.child_id == null).ToList();
            return DB11.communication.Where(x => x.exam.exam_number == trackingNumber && x.child_id == null).ToList();
        }

        public JsonResult GetCaseAgent(int examId)
        {
            var agent = DB11.exam.First(x => x.exam_id == examId).@case?.case_agent_id;
            return Json(agent, JsonRequestBehavior.AllowGet);
        }

        public ActionResult TimesheetView(int examId)
        {
            var currentUserID = CurrentUserModel.AccountID;
            var timeCodes = GetTimeCodes(examId, currentUserID);

            ViewData["examID"] = examId;
            ViewData["TimeCodes"] = timeCodes;
            ViewData["DefaultTimeCode"] = timeCodes.First().ID;
            ViewData["CurrentUser"] = "";

            //check to see if impersonating someone
            ViewData["CurrentUser"] = DB11.account.Find(currentUserID).name.FormatName();
            return PartialView("_TimeSheet");
        }

        /// <summary>
        /// Get all data from Timesheet grid
        /// </summary>
        /// <param name="request"></param>
        /// <param name="examId"></param>
        /// <returns></returns>
        public ActionResult Timesheet([DataSourceRequest]DataSourceRequest request, int examId)
        {
            var data = DB11.account_activity.Where(x => x.exam_id == examId).OrderBy(z => z.date_worked)
                .ToList().Select(x => new TimesheetViewModel(x));
            return KendoGridResult(data.AsQueryable(), request);
        }

        /// <summary>
        /// Create a time entry
        /// </summary>
        /// <param name="request"></param>
        /// <param name="model"></param>
        /// <param name="examId"></param>
        /// <returns></returns>
        public ActionResult TimesheetCreate([DataSourceRequest] DataSourceRequest request, TimesheetViewModel model)
        {
            account_activity c = model.Create();
            
            c.account_id = CurrentUserModel.AccountID;
            DB11.account_activity.Add(c);
            DB11.SaveChanges();
            AuditInsert(c);
            DB11.SaveChanges();
            model.ID = c.account_activity_id;
            return Json(new[] { model }.ToDataSourceResult(request, ModelState));
        }
        /// <summary>
        /// Update a time entry
        /// </summary>
        /// <param name="request"></param>
        /// <param name="order"></param>
        /// <returns></returns>
        public ActionResult TimesheetUpdate([DataSourceRequest] DataSourceRequest request, TimesheetViewModel model)
        {
            if (model != null && ModelState.IsValid)
            {
                var c = DB11.account_activity.First(x => x.account_activity_id == model.ID);
                AuditUpdate(c);
                model.Update(c);
                DB11.Entry(c).State = System.Data.Entity.EntityState.Modified;
                DB11.SaveChanges();
                model.TimeCodeEnabled = DB11.activity.Where(x => x.activity_id == model.TimeCodeID).Select(x => x.enabled).First();
            }
            return Json(new[] { model }.ToDataSourceResult(request, ModelState));
        }

        public ActionResult TimesheetGraph(int id)
        {
            var openExams = DB11.account_activity.Where(x => x.exam_id == id)
                .GroupBy(x => x.account.sam_account_name)
                .ToList()
                .Select(x => new { Group = x.Key.FormatName(), Count = x.Sum(y => y.hours), Color = GetRandomColor() }).ToList();
            return Json(openExams, JsonRequestBehavior.AllowGet);
        }

        public ActionResult ExamNotesView(int examID)
        {
            ViewBag.ExamID = examID;
            return PartialView("_ExamNotes");
        }

        /// <summary>
        /// Get all data for the Exam Notes grid
        /// </summary>
        /// <param name="request"></param>
        /// <param name="examID"></param>
        /// <returns></returns>
        public ActionResult ExamNotes([DataSourceRequest]DataSourceRequest request, int examID)
        {
            var data = DB11.exam_note.Where(x => x.exam_id == examID).ToList().Select(x => new ExamNoteViewModel(x)).OrderBy(x => x.ExamNoteDate);
            return KendoGridResult(data.AsQueryable(), request);
        }

        /// <summary>
        /// Create an Exam Note
        /// </summary>
        /// <param name="request"></param>
        /// <param name="m"></param>
        /// <returns></returns>
        [HttpPost]
        public ActionResult ExamNoteCreate([DataSourceRequest] DataSourceRequest request, ExamNoteViewModel model)
        {
            if (!String.IsNullOrEmpty(model.ExamNote)) // make sure the note field is note blank
            {
                //get Current User
                var currentUser = CurrentUserModel;
                model.ExamNoteBy = currentUser.AccountID;

                exam_note noteIn = new exam_note
                {
                    date_logged = DateTime.Now,
                    entered_by = model.ExamNoteBy,
                    exam_id = model.ExamID,
                    note = model.ExamNote
                };


                DB11.exam_note.Add(noteIn);
                DB11.SaveChanges();
                AuditInsert(noteIn);
                DB11.SaveChanges();

                account inExaminer = DB11.account.FirstOrDefault(x => x.account_id == model.ExamNoteBy);
                model.ExaminerName = inExaminer.name.FormatName();

                LogExamEvent(model.ExamID, $"Created note- " + model.ExamNote, ExamEventTypes.Created_Note);
            }

            return Json(new[] { model }.ToDataSourceResult(request, ModelState));
        }

        /// <summary>
        /// Update an Exam Note
        /// </summary>
        /// <param name="request"></param>
        /// <param name="m"></param>
        /// <returns></returns>
        public ActionResult ExamNoteUpdate([DataSourceRequest] DataSourceRequest request, ExamNoteViewModel model)
        {
            CIMSData.Database.exam_note orgRec = DB11.exam_note.FirstOrDefault(x => x.exam_note_id == model.ExamNoteID);

            // verify a change in the record
            if ((orgRec.date_logged != model.ExamNoteDate) || (orgRec.entered_by != model.ExamNoteBy) || (orgRec.note != model.ExamNote))
            {
                AuditUpdate(orgRec);   // add the initial information to the audit log

                //oldRec.date_logged = model.ExamNoteDate;
                orgRec.date_logged = DateTime.Now;
                orgRec.entered_by = model.ExamNoteBy;
                orgRec.note = model.ExamNote;

                DB11.Entry(orgRec).State = System.Data.Entity.EntityState.Modified;
                DB11.SaveChanges();

                CIMSData.Database.account inExaminer = new CIMSData.Database.account();
                inExaminer = DB11.account.FirstOrDefault(x => x.account_id == model.ExamNoteBy);
                //model.ExaminerName = inExaminer.name + " (" + inExaminer.sam_account_name + ")";
                model.ExaminerName = inExaminer.name.FormatName();
                model.ExamNoteDate = orgRec.date_logged;

                LogExamEvent(model.ExamID, $"Edited note", ExamEventTypes.Edited_Note);
            }
            return Json(new[] { model }.ToDataSourceResult(request, ModelState));
        }

        /// <summary> All Users in the database </summary>
        /// <returns> List of Users' Name</returns>
        public JsonResult UsersList()
        {
            //return Json(DB.accounts.Select(x => new UserAccount
            return Json(DB11.account.ToList().Select(x => new UserAccount
            {
                UserAccountID = x.account_id,
                NameSAMname = x.name.FormatName() + " (" + x.sam_account_name + ")"
            }).OrderBy(x => x.NameSAMname), JsonRequestBehavior.AllowGet);
        }

        public ActionResult Packages(int examId)
        {
            ViewData["examID"] = examId;
            return PartialView("_Packages");
        }

        public ActionResult PackagesGridData([DataSourceRequest]DataSourceRequest request, int examId)
        {
            var results = DB11.exam_package.Where(x => x.exam_id == examId).OrderBy(x => x.date_logged).ToList()
                .Select(x => new EvidencePackageTrackingViewModel(x, DB11));
            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult PackagesGridSubGridHardDrive([DataSourceRequest]DataSourceRequest request, int packageId)
        {
            var results = DB11.hard_drive_history.Where(x => x.exam_package_id == packageId).ToList()
                .Select(x => new MiniHardDriveViewModel(x));
            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult PackagesGridSubGridEvidence([DataSourceRequest]DataSourceRequest request, int packageId)
        {
            var results = DB11.evidence.Where(x => x.exam_package_id == packageId).ToList()
                .Select(x => new EvidenceViewModel(DB11.vw_evidence.First(y => y.evidence_id == x.evidence_id)));
            return KendoGridResult(results.AsQueryable(), request);
        }

        public ActionResult LoadContactsForm()
        {
            return PartialView("NewContact");
        }

        public ActionResult PackageCreate([DataSourceRequest] DataSourceRequest request, EvidencePackageTrackingViewModel model)
        {
            var c = model.Create();
            c.entered_by = CurrentUserModel.AccountID;
            DB11.exam_package.Add(c);
            DB11.SaveChanges();
            AuditInsert(c);
            DB11.SaveChanges();

            if (model.IntoLab == "Sent")
            {
                foreach (var item in model.Evidence ?? new int[] { })
                {
                    var row = DB11.evidence.First(x => x.evidence_id == item);
                    AuditUpdate(row);
                    row.exam_package_id = c.exam_package_id;
                    DB11.Entry(row).State = System.Data.Entity.EntityState.Modified;

                    CIMSData.Database.evidence_history h = new CIMSData.Database.evidence_history()
                    {
                        evidence_id = item,
                        entered_by = CurrentUserModel.AccountID,
                        date_logged = DateTime.Now,
                        evidence_status_id = 7
                    };
                    DB11.evidence_history.Add(h);
                    DB11.SaveChanges();
                    AuditInsert(h);
                    DB11.SaveChanges();
                }
                foreach (var item in model.HardDrives ?? new int[] { })
                {
                    CIMSData.Database.hard_drive_history hh = new CIMSData.Database.hard_drive_history()
                    {
                        hard_drive_id = item,
                        entered_by = CurrentUserModel.AccountID,
                        date_logged = DateTime.Now,
                        hard_drive_status_id = 3,
                        exam_id = c.exam_id,
                        exam_package_id = c.exam_package_id
                    };
                    DB11.hard_drive_history.Add(hh);
                    DB11.SaveChanges();
                    AuditInsert(hh);
                    DB11.SaveChanges();
                }
            }

            model.MailService = DB11.mail_service.First(x => x.mail_service_id == model.MailServiceId).name;
            model.EnteredBy = DB11.account.First(x => x.account_id == CurrentUserModel.AccountID).sam_account_name.FormatName();
            model.PackageId = c.exam_package_id;
            LogExamEvent(model.ExamId,
                $"{model.IntoLab} package with mail service {model.MailService} with tracking number {model.MailTrackingNumber}",
                ExamEventTypes.Package_Added);
            return Json(new[] { model }.ToDataSourceResult(request, ModelState));
        }

        public ActionResult PackageUpdate([DataSourceRequest] DataSourceRequest request, EvidencePackageTrackingViewModel model)
        {
            if (model != null)
            {
                var row = DB11.exam_package.First(x => x.exam_package_id == model.PackageId);
                AuditUpdate(row);
                model.Update(row);
                DB11.Entry(row).State = System.Data.Entity.EntityState.Modified;
                DB11.SaveChanges();

                if (model.IntoLab == "Sent")
                {
                    List<int> newEvidence = new List<int>();
                    List<int> removedEvidence = new List<int>();
                    if (model.Evidence == null) model.Evidence = new int[] {};
                    if (model.HardDrives == null) model.HardDrives = new int[] { };
                    List<evidence> existing = DB11.evidence.Where(x => x.exam_package_id == row.exam_package_id).ToList();
                    foreach (var e in model.Evidence)
                    {
                        if (!existing.Any(x => x.evidence_id == e))
                        {
                            newEvidence.Add(e);
                        }
                    }
                    foreach (var e in existing)
                    {
                        if (!model.Evidence.Any(x => x == e.evidence_id))
                        {
                            removedEvidence.Add(e.evidence_id);
                        }
                    }
                    foreach (var item in removedEvidence)
                    {
                        evidence x = DB11.evidence.Find(item);
                        AuditUpdate(x);
                        x.exam_package_id = null;
                        DB11.Entry(x).State = System.Data.Entity.EntityState.Modified;
                        // check it back in
                        CIMSData.Database.evidence_history h = new CIMSData.Database.evidence_history()
                        {
                            evidence_id = item,
                            entered_by = CurrentUserModel.AccountID,
                            date_logged = DateTime.Now,
                            evidence_status_id = 5
                        };
                        DB11.evidence_history.Add(h);
                        DB11.SaveChanges();
                        AuditInsert(h);
                    }

                    // Get the current hard drives associated with the package according to the db
                    var curPackageHds = new List<int>();
                    DB11.hard_drive_history.Where(x => x.exam_package_id == model.PackageId).ForEach(x =>
                    {
                        curPackageHds.Add(x.hard_drive_id);
                    });
                    var deletedHds = new List<int>();
                    // If the model no longer has a hard drive that the db has, it can be removed if its status is released.
                    curPackageHds.ForEach(x =>
                    {
                        if (!model.HardDrives.Contains(x)) deletedHds.Add(x);
                    });
                    deletedHds.ForEach(x =>
                    {
                        var hhRow = DB11.hard_drive_history.Where(z =>
                                z.exam_package_id == model.PackageId && z.hard_drive_id == x)
                            .OrderByDescending(z => z.hard_drive_history_id).FirstOrDefault();
                        if (hhRow != null && hhRow.hard_drive_status_id == 3)
                        {
                            DB11.hard_drive_history.Remove(hhRow);
                            DB11.SaveChanges();
                        }
                    });

                    foreach (var item in newEvidence)
                    {
                        var evRow = DB11.evidence.First(x => x.evidence_id == item);
                        AuditUpdate(evRow);
                        evRow.exam_package_id = row.exam_package_id;
                        DB11.Entry(evRow).State = System.Data.Entity.EntityState.Modified;

                        CIMSData.Database.evidence_history h = new CIMSData.Database.evidence_history()
                        {
                            evidence_id = item,
                            entered_by = CurrentUserModel.AccountID,
                            date_logged = DateTime.Now,
                            evidence_status_id = 7
                        };
                        DB11.evidence_history.Add(h);
                        DB11.SaveChanges();
                        AuditInsert(h);
                        DB11.SaveChanges();

                    }
                    foreach (var item in model.HardDrives ?? new int[] { })
                    {
                        // Delete the most recent hd history where it is released for this hard drive & package 
                        // so we don't get duplicate released on a hard drive that ultimately wasn't changed.
                        var hdRow = DB11.hard_drive_history
                            .Where(x => x.hard_drive_id == item && x.exam_package_id == model.PackageId)
                            .OrderByDescending(x => x.hard_drive_history_id).FirstOrDefault();
                        if (hdRow != null && hdRow.hard_drive_status_id == 3)
                        {
                            DB11.hard_drive_history.Remove(hdRow);
                            DB11.SaveChanges();
                        }
                        CIMSData.Database.hard_drive_history hh = new CIMSData.Database.hard_drive_history()
                        {
                            hard_drive_id = item,
                            entered_by = CurrentUserModel.AccountID,
                            date_logged = DateTime.Now,
                            hard_drive_status_id = 3,
                            exam_id = model.ExamId,
                            exam_package_id = row.exam_package_id
                        };
                        DB11.hard_drive_history.Add(hh);
                        DB11.SaveChanges();
                        AuditInsert(hh);
                        DB11.SaveChanges();
                    }
                }
            }
            return Json(ModelState.ToDataSourceResult());
        }

        public ActionResult HardDrives(int examId)
        {
            ViewData["examID"] = examId;
            return PartialView("_HardDrives");
        }

        [HttpPost]
        public ActionResult CheckOutHardDrive(int examId, int MBNum)
        {
            var hard_drive_found = DB11.hard_drive.FirstOrDefault(x => x.mb_number == MBNum);
            if (DB11.hard_drive.FirstOrDefault(x => x.mb_number == MBNum) == null)
            {
                return Json("error", JsonRequestBehavior.AllowGet);
            }

            if (DB11.hard_drive_history.FirstOrDefault(x =>
                x.exam_id == examId && x.hard_drive_id == hard_drive_found.hard_drive_id) != null)
            {
                return Json("fail", JsonRequestBehavior.AllowGet);
            }
            var hdh = new hard_drive_history
            {
                date_logged = DateTime.Now,
                entered_by = CurrentUserModel.AccountID,
                exam_id = examId,
                hard_drive_id = hard_drive_found.hard_drive_id,
                hard_drive_status_id = 1
            };
            DB11.hard_drive_history.Add(hdh);
            DB11.SaveChanges();
            AuditInsert(hdh);
            DB11.SaveChanges();
            LogExamEvent(examId, $"Checked out hard drive - {MBNum}", ExamEventTypes.Checked_Out_Hard_Drive);
            return Json("success", JsonRequestBehavior.AllowGet);
        }

        public ActionResult CheckInHardDrive (int? examId, int mbNumber)
        {
            var hdh = new hard_drive_history
            {
                date_logged = DateTime.Now,
                entered_by = CurrentUserModel.AccountID,
                exam_id = examId,
                hard_drive_id = DB11.hard_drive.First(x => x.mb_number == mbNumber).hard_drive_id,
                hard_drive_status_id = 2
            };
            DB11.hard_drive_history.Add(hdh);
            DB11.SaveChanges();
            AuditInsert(hdh);
            DB11.SaveChanges();
            if (examId.HasValue)
            {
                LogExamEvent(examId.Value, $"Checked in hard drive - {mbNumber}", ExamEventTypes.Checked_In_Hard_Drive);
            }
            return new EmptyResult();
        }

        [HttpPost]
        public ActionResult SaveContact(ContactViewModel vm)
        {
            CIMSData.Database.contact contact;
            if (!vm.ORIGREC.HasValue)
            {
                // Create the contact
                contact = vm.Create();
                DB11.contact.Add(contact);
                DB11.SaveChanges();
                AuditInsert(contact);
                vm.ORIGREC = contact.contact_id;
                //Handle litigation support contacts
                if (vm.RelationshipType == "Trial Counsel" || vm.RelationshipType == "Senior Trial Counsel")
                {
                    var cimsCase = <EMAIL>(vm.CaseID);
                    if(cimsCase.case_trial == null)
                    {
                        if (vm.RelationshipType == "Trial Counsel")
                        {
                            cimsCase.case_trial = new case_trial
                            {
                                trial_counsel_id = contact.contact_id
                            };
                        }

                        if (vm.RelationshipType == "Senior Trial Counsel")
                        {
                            cimsCase.case_trial = new case_trial
                            {
                                trial_counsel_senior_id = contact.contact_id
                            };
                        }
                    }
                    else
                    {
                        if (vm.RelationshipType == "Trial Counsel")
                        {
                            cimsCase.case_trial.trial_counsel_id = contact.contact_id;
                        }

                        if (vm.RelationshipType == "Senior Trial Counsel")
                        {
                            cimsCase.case_trial.trial_counsel_senior_id = contact.contact_id;
                        }
                    }
                    AuditUpdate(cimsCase);
                    DB11.Entry(cimsCase).State = System.Data.Entity.EntityState.Modified;
                    DB11.SaveChanges();
                }
            }
            else
            {
                //Update Contact
                contact = DB11.contact.First(x => x.contact_id == vm.ORIGREC);
                AuditUpdate(contact);
                vm.Update(contact);
                DB11.Entry(contact).State = System.Data.Entity.EntityState.Modified;
                DB11.SaveChanges();
            }
            if (vm.ExamID > 0 && vm.RelationshipType == "Evidence Return Address")
            {
                CIMSData.Database.exam ex = DB11.exam.FirstOrDefault(x => x.exam_id == vm.ExamID);
                ex.evidence_return_address_id = vm.ORIGREC;
                DB11.Entry(ex).State = System.Data.Entity.EntityState.Modified;
                DB11.SaveChanges();
            }
            else if (vm.ExamID > 0 && vm.RelationshipType == "Report Return Address")
            {
                CIMSData.Database.exam ex = DB11.exam.FirstOrDefault(x => x.exam_id == vm.ExamID);
                ex.report_return_address_id = vm.ORIGREC;
                DB11.Entry(ex).State = System.Data.Entity.EntityState.Modified;
                DB11.SaveChanges();
            }
            else if (vm.ExamID > 0 && vm.SetReturnAddressesToCaseAgent)
            {
                CIMSData.Database.exam ex = DB11.exam.FirstOrDefault(x => x.exam_id == vm.ExamID);
                ex.report_return_address_id = vm.ORIGREC;
                ex.evidence_return_address_id = vm.ORIGREC;
                DB11.Entry(ex).State = System.Data.Entity.EntityState.Modified;
                DB11.SaveChanges();
            }
            if (vm.CaseID > 0)
            {
                //Swap contact
                CIMSData.Database.@case cas = <EMAIL>(vm.CaseID);

                switch (vm.RelationshipType)
                {
                    case "CaseAgent":
                    case "Case Agent":
                    case "Customer":
                    case "Analyst":
                        AuditUpdate(cas);
                        cas.case_agent_id = vm.ORIGREC;
                        DB11.Entry(cas).State = System.Data.Entity.EntityState.Modified;
                        break;
                    case "DETCO":
                        var subA = cas.agency_org;
                        AuditUpdate(subA);
                        subA.detco_id = vm.ORIGREC;
                        DB11.Entry(subA).State = System.Data.Entity.EntityState.Modified;
                        break;
                    case "Prosecutor":
                        AuditUpdate(cas);
                        cas.prosecutor_id = vm.ORIGREC;
                        DB11.Entry(cas).State = System.Data.Entity.EntityState.Modified;
                        break;
                    case "TrialCounsel":
                    case "Trial Counsel":
                        AuditUpdate(cas);
                        if (cas.case_trial == null)
                        {
                            cas.case_trial = new case_trial { trial_counsel_id = vm.ORIGREC };
                        }
                        else
                        {
                            cas.case_trial.trial_counsel_id = vm.ORIGREC;
                        }
                        DB11.Entry(cas).State = System.Data.Entity.EntityState.Modified;
                        break;
                    case "SeniorTrialCounsel":
                    case "Senior Trial Counsel":
                        AuditUpdate(cas);
                        if (cas.case_trial == null)
                        {
                            cas.case_trial = new case_trial { trial_counsel_senior_id = vm.ORIGREC };
                        }
                        else
                        {
                            cas.case_trial.trial_counsel_senior_id = vm.ORIGREC;
                        }
                        DB11.Entry(cas).State = System.Data.Entity.EntityState.Modified;
                        break;
                }
                DB11.SaveChanges();
            }
            vm.RelationshipType = vm.RelationshipType.Replace(" ", "");
            if (vm.StateID.HasValue && vm.StateID > 0)
            {
                vm.State = DB11.state.Find(vm.StateID.Value)?.name;
            }
            ViewBag.ExamId = vm.ExamID;
            return PartialView("_Contact", vm);
        }

        public ActionResult Contact(string type, int examId)
        {
            var exam = DB11.exam.Find(examId);
            int? id = 0;
            switch (type)
            {
                case "Report Return Address":
                    id = exam.report_return_address_id;
                    break;
                case "Evidence Return Address":
                    id = exam.evidence_return_address_id;
                    break;
            }
            if (!id.HasValue)
            {
                throw new Exception("couldn't find contact");
            }
            ContactViewModel ret = new ContactViewModel(DB11.contact.First(x => x.contact_id == id))
            {
                RelationshipType = type,
                ExamID = examId
            };
            ViewBag.ExamId = examId;
            return PartialView("_Contact", ret);
        }

        public ActionResult BulkTimesheet(int examId)
        {
            ViewData["TimeCodes"] = GetTimeCodes(examId, CurrentUserModel.AccountID);
            ViewData["ExamID"] = examId;
            return PartialView("_BulkTimeSheetEdit", new MultiTimeEntryViewModel());
        }

        public ActionResult SaveAccountActivity(MultiTimeEntryViewModel model)
        {
            var startOfWeek = model.MondaysDate.StartOfWeek(DayOfWeek.Sunday);
            AddAccountActivity(model.Monday, startOfWeek, DayOfWeek.Monday, model.ExamId, model.TimeCodeId);
            AddAccountActivity(model.Tuesday, startOfWeek, DayOfWeek.Tuesday, model.ExamId, model.TimeCodeId);
            AddAccountActivity(model.Wednesday, startOfWeek, DayOfWeek.Wednesday, model.ExamId, model.TimeCodeId);
            AddAccountActivity(model.Thursday, startOfWeek, DayOfWeek.Thursday, model.ExamId, model.TimeCodeId);
            AddAccountActivity(model.Friday, startOfWeek, DayOfWeek.Friday, model.ExamId, model.TimeCodeId);
            return new EmptyResult();
        }


        private void AddAccountActivity(TimesheetViewModel model, DateTime date, DayOfWeek day, int examId, int timecodeId)
        {
            if (model.Hours > 0)
            {
                model.Date = date.AddDays((double)day);
                model.ExamId = examId;
                model.TimeCodeID = timecodeId;
                account_activity c = model.Create();
                c.account_id = CurrentUserModel.AccountID;
                DB11.account_activity.Add(c);
                DB11.SaveChanges();
                AuditInsert(c);
                DB11.SaveChanges();
                LogExamEvent(examId, $"Logged time entry - {model.Date} - {c.hours} hours", ExamEventTypes.Logged_Time_Entry);
            }
        }

        public JsonResult ExamFromViewAllExams(int examID)
        {
            var exam = DB11.vw_all_exams.Find(examID);
            return Json(exam, JsonRequestBehavior.AllowGet);
        }
    }
}