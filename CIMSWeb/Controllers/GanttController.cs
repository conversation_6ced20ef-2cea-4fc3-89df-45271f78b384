﻿using Kendo.Mvc.UI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using CIMSWeb.Models;
using CIMSWeb.Workflow;
using Kendo.Mvc.Extensions;

namespace CIMSWeb.Controllers
{
    public class GanttController : CIMSController
    {   
        public ActionResult SectionActivity([DataSourceRequest] DataSourceRequest request, int sectionId)
        {
            int idCounter = 1000000000;
            List<TaskViewModel> allTasks = new List<TaskViewModel>();
            foreach (var x in DB11.exam.OpenExams().Where(x => x.group_id == sectionId))
            {
                var examTasks = GetExamTasks(x.exam_id, ref idCounter);
                if (examTasks.Any())
                {
                    var examRoot = examTasks.Where(z => z.ParentID == null).FirstOrDefault();
                    if (examRoot != null)
                    {
                        examRoot.Expanded = false;
                    }
                    allTasks.AddRange(examTasks);
                }
            }
            return Json(allTasks.ToDataSourceResult(request), JsonRequestBehavior.AllowGet);
        }

        public ActionResult Activity([DataSourceRequest] DataSourceRequest request, int examId)
        {
            int idCounter = 1000000000;
            var tasks = GetExamTasks(examId, ref idCounter);
            return Json(tasks.ToDataSourceResult(request), JsonRequestBehavior.AllowGet);
        }

        private List<TaskViewModel> GetExamTasks(int examId, ref int idCounter)
        {
            List<TaskViewModel> parentTasks = new List<TaskViewModel>();
            var exam = DB11.exam.Find(examId);
            var tasks = DB11.exam_activity.Where(x => x.exam_id == examId && x.date_start.HasValue).OrderBy(x => x.date_start).ToList().Select(x => new TaskViewModel(x)).ToList();
            Dictionary<string, DateTime> currentGroupEndTimes = new Dictionary<string, DateTime>();
            foreach (var t in tasks.OrderBy(x => x.Start))
            {
                if (t.Group != Group.Unknown)
                {
                    string groupName = t.Group.FormatName();
                    var groupTask = parentTasks.LastOrDefault(x => x.Title == groupName);
                    if (groupTask == null ||
                        (currentGroupEndTimes.ContainsKey(groupName) && (currentGroupEndTimes[groupName].AddMinutes(2) < t.Start)))
                    {
                        groupTask = new TaskViewModel()
                        {
                            TaskID = ++idCounter,
                            Title = groupName,
                            Assignee = string.Empty,
                            CompletedBy = string.Empty,
                            Summary = true
                        };
                        parentTasks.Add(groupTask);
                    }
                    if ((!currentGroupEndTimes.ContainsKey(groupName)) ||
                        (currentGroupEndTimes[groupName] < t.End))
                    {
                        currentGroupEndTimes[groupName] = t.End;
                    }
                    t.ParentID = groupTask.TaskID;
                }
            }
            foreach (var p in parentTasks)
            {
                p.Start = tasks.Where(x => x.ParentID == p.TaskID).Min(x => x.Start);
                if (tasks.Any(x => x.ParentID == p.TaskID && p.End == null))
                {
                    p.End = DateTime.Now;
                }
                else
                {
                    p.End = tasks.Where(x => x.ParentID == p.TaskID).Max(x => x.End);
                }
            }
            TaskViewModel rootTask = new TaskViewModel()
            {
                TaskID = ++idCounter,
                Title = exam.exam_number,
                Assignee = string.Empty,
                CompletedBy = string.Empty,
                Summary = true,
                Expanded = true

            };
            if (tasks.Any())
            {
                rootTask.Start = tasks.OrderBy(x => x.Start).Min(x => x.Start);
                rootTask.End = tasks.OrderBy(x => x.End).Max(x => x.End);
                foreach (var pt in parentTasks.Union(tasks.Where(x => x.ParentID == null)))
                {
                    pt.ParentID = rootTask.TaskID;
                }
                tasks.AddRange(parentTasks);
                tasks.Add(rootTask);
            }
            return tasks;
        }
    }
}
