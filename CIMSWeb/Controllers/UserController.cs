﻿using CIMSWeb.Models;
using System.Collections.Generic;
using System.Web.Mvc;

namespace CIMSWeb.Controllers
{
    public class UserController : CIMSController
    {
        /// <summary>
        /// GET list of CIMS users as JSON
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public JsonResult List()
        {
            List<AccountViewModel> avms = new List<AccountViewModel>();

            //foreach (var a in DB.accounts)
            foreach (var a in DB11.account)
            {
                avms.Add(new AccountViewModel(a));
            }
            return Json(avms, JsonRequestBehavior.AllowGet);
        }
    }
}