﻿using System;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;

namespace CIMSWeb.Attributes
{
    public enum SecureFunction
    {
        AccountManagement,
        AgencyManagement,
        CustomerPortalExport,
        ExamAdministration,
        EMSImport,
        ExamCreation,
        ExamCreationMainMenu,
        ExamCommunicationEdit,
        EvidenceCheckInOut,
        IntrusionsCheckInOut,
        CheckInOutFunctions,
        IntrusionsExamCreation,
        IntrusionsExamAdministration,
        LitigationSupportExamCreation,
        StandardExamCreation,
        TagCreation,
        TimesheetExport,
        ExamSuspenseExport,
        StandardSearch,
        IntrusionSearch,
        EvidenceConfigurationAdministration
    }

    [AttributeUsage(AttributeTargets.Method | AttributeTargets.Class, Inherited = true, AllowMultiple = true)]
    public class CIMSAuthorizeAttribute : AuthorizeAttribute
    {
        private SecureFunction function;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="function"></param>
        public CIMSAuthorizeAttribute(SecureFunction function)
        {
            this.function = function;
        }

        /// <summary>
        /// Checks to see if we are authorized for a certain action
        /// </summary>
        /// <param name="httpContext"></param>
        /// <returns></returns>
        protected override bool AuthorizeCore(HttpContextBase httpContext)
        {
            return httpContext.CIMSAuthorized(function);
        }

        /// <summary>
        /// Action to take when authorization fails
        /// </summary>
        /// <param name="filterContext"></param>
        protected override void HandleUnauthorizedRequest(AuthorizationContext filterContext)
        {
            filterContext.Result = new RedirectToRouteResult(new RouteValueDictionary(new { action = "Unauthorized", controller = "Error" }));
        }

    }
}