﻿using System;
using System.Web.Mvc;

namespace CIMSWeb.Attributes
{
    [AttributeUsage(AttributeTargets.Method | AttributeTargets.Class, Inherited = true, AllowMultiple = true)]
    public class CimsHandleErrorAttribute : HandleErrorAttribute
    {
        public override void OnException(ExceptionContext filterContext)
        {
            if (filterContext.ExceptionHandled)
            {
                return;
            }

            if (filterContext.HttpContext.Request.IsAjaxRequest())
            {
                var error = new JsonResult
                {
                    ContentType = "application/json",
                    Data = new { Error = filterContext.Exception.Message }
                };
                filterContext.Result = error;
                filterContext.ExceptionHandled = true;
            }
            else
            {
                var ctrlName = (string)filterContext.RouteData.Values["controller"];
                var action = (string)filterContext.RouteData.Values["action"];

                ViewResult view = new ViewResult
                {
                    ViewName = "Error",
                    ViewData = new ViewDataDictionary<HandleErrorInfo>(new HandleErrorInfo(new Exception(filterContext.Exception.Message), ctrlName, action))
                };

                filterContext.Result = view;
            }
        }
    }
}