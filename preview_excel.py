#!/usr/bin/env python3
"""
Script to preview the generated Excel documentation.
"""

import pandas as pd

def preview_excel_file():
    """Preview the contents of the generated Excel file."""
    
    try:
        # Read the main sheet
        df = pd.read_excel('CIMS_Database_Documentation.xlsx', sheet_name='All_Tables_and_Fields')
        
        print("CIMS Database Documentation Preview")
        print("=" * 50)
        print(f"Total records: {len(df)}")
        print(f"Columns: {list(df.columns)}")
        print("\nFirst 10 records:")
        print(df.head(10).to_string(index=False))
        
        print("\n" + "=" * 50)
        print("Summary by Logical Group:")
        group_summary = df.groupby('Logical_Group').agg({
            'Table_Name': 'nunique',
            'Column_Name': 'count'
        }).rename(columns={'Table_Name': 'Tables', 'Column_Name': 'Fields'})
        print(group_summary.to_string())
        
        print("\n" + "=" * 50)
        print("Sample records from each group:")
        for group in df['Logical_Group'].unique():
            group_data = df[df['Logical_Group'] == group]
            print(f"\n{group} (Sample):")
            print(group_data.head(3)[['Table_Name', 'Column_Name', 'Data_Type']].to_string(index=False))
        
        # Read summary sheet
        try:
            summary_df = pd.read_excel('CIMS_Database_Documentation.xlsx', sheet_name='Summary_by_Group')
            print("\n" + "=" * 50)
            print("Summary Sheet:")
            print(summary_df[['Logical_Group', 'Table_Count', 'Field_Count']].to_string(index=False))
        except:
            print("\nSummary sheet not found or couldn't be read")
            
    except Exception as e:
        print(f"Error reading Excel file: {e}")

if __name__ == "__main__":
    preview_excel_file()
