#!/usr/bin/env python3
"""
<PERSON>ript to analyze CIMS database system and generate Excel documentation.
This script analyzes SQL table definition files and generates comprehensive documentation.
"""

import os
import re
import glob
import pandas as pd
from collections import defaultdict

def extract_fields_from_sql_file(file_path):
    """Extract detailed field information from a SQL table definition file."""
    fields = []
    table_name = ""

    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # Extract table name
        table_match = re.search(r'create\s+table\s+(?:\[?cims\]?\.)?\[?(\w+)\]?', content, re.IGNORECASE)
        if table_match:
            table_name = table_match.group(1)

        # Find the table definition block - improved pattern
        create_table_pattern = r'create\s+table[^(]*\((.*?)\)(?:\s*(?:go|;|$|with|on|\s*execute))'
        match = re.search(create_table_pattern, content, re.IGNORECASE | re.DOTALL)

        if match:
            table_def = match.group(1)

            # Split by lines and process each line
            lines = table_def.split('\n')
            for line in lines:
                original_line = line.strip()
                line = line.strip().rstrip(',')
                if not line or line.startswith('--') or line.startswith('/*'):
                    continue

                # Skip constraint definitions and other non-field lines
                skip_keywords = ['constraint', 'primary key', 'foreign key', 'references',
                               'check', 'unique', 'index', 'key', 'clustered', 'nonclustered']
                if any(keyword in line.lower() for keyword in skip_keywords):
                    continue

                # Extract field name and type
                field_match = re.match(r'\s*\[?(\w+)\]?\s+([^,\s]+(?:\s*\([^)]*\))?)', line)
                if field_match:
                    field_name = field_match.group(1)
                    field_type = field_match.group(2)

                    # Skip if it's a constraint keyword
                    if field_name.lower() not in ['constraint', 'primary', 'foreign', 'key', 'references', 'check', 'unique', 'index']:
                        # Extract description from comments if available
                        description = extract_description_from_content(content, table_name, field_name)

                        # If no description found, generate one based on field name
                        if not description:
                            description = generate_description_from_field_name(field_name, field_type, table_name)

                        fields.append({
                            'field_name': field_name,
                            'field_type': field_type,
                            'description': description
                        })

    except Exception as e:
        print(f"Error processing {file_path}: {e}")

    return table_name, fields

def extract_description_from_content(content, table_name, field_name):
    """Extract field description from SQL comments or extended properties."""
    # Look for extended property descriptions
    desc_pattern = rf"sp_addextendedproperty.*?@level2name\s*=\s*N?'?{field_name}'?.*?@value\s*=\s*N?'([^']*)"
    desc_match = re.search(desc_pattern, content, re.IGNORECASE | re.DOTALL)
    if desc_match:
        return desc_match.group(1)

    # Look for inline comments
    field_pattern = rf"\[?{field_name}\]?\s+[^,\n]*--\s*([^\n]*)"
    comment_match = re.search(field_pattern, content, re.IGNORECASE)
    if comment_match:
        return comment_match.group(1).strip()

    return ""

def generate_description_from_field_name(field_name, field_type, table_name):
    """Generate intelligent description based on field name patterns and type."""
    field_lower = field_name.lower()

    # Common ID patterns
    if field_lower.endswith('_id') and field_lower != 'id':
        base_name = field_lower[:-3].replace('_', ' ')
        if field_lower == f"{table_name.lower()}_id":
            return f"Unique identifier for {table_name} record."
        else:
            return f"Foreign key reference to {base_name} table."
    elif field_lower == 'id':
        return f"Unique identifier for {table_name} record."

    # Date/time patterns
    elif 'date' in field_lower:
        if 'created' in field_lower or 'added' in field_lower:
            return "Date when record was created."
        elif 'updated' in field_lower or 'modified' in field_lower:
            return "Date when record was last updated."
        elif 'deleted' in field_lower:
            return "Date when record was deleted."
        elif 'sent' in field_lower:
            return "Date when item was sent."
        elif 'received' in field_lower:
            return "Date when item was received."
        elif 'read' in field_lower:
            return "Date when item was read."
        elif 'trial' in field_lower:
            return "Date of trial."
        elif 'birth' in field_lower:
            return "Date of birth."
        else:
            return f"Date related to {field_lower.replace('date_', '').replace('_', ' ')}."

    # Name patterns
    elif field_lower == 'name':
        if 'agency' in table_name.lower():
            return "Name of the law enforcement agency."
        elif 'evidence' in table_name.lower():
            return "Descriptive name of the evidence item."
        elif 'exam' in table_name.lower():
            return "Name or title of the examination."
        elif 'case' in table_name.lower():
            return "Name or title associated with the case."
        elif 'contact' in table_name.lower():
            return "Full name of the contact person."
        else:
            return f"Name of the {table_name.replace('_', ' ')}."
    elif field_lower.endswith('_name'):
        prefix = field_lower[:-5].replace('_', ' ')
        if 'examiner' in prefix:
            return "Name of the forensic examiner assigned."
        elif 'build' in prefix:
            return "Name of the software build or version."
        else:
            return f"{prefix.title()} name."
    elif 'first_name' in field_lower or 'given_name' in field_lower:
        return "First name of the person."
    elif 'last_name' in field_lower or 'surname' in field_lower:
        return "Last name of the person."
    elif 'full_name' in field_lower:
        return "Full name of the person."

    # Status and type patterns
    elif field_lower.endswith('_status'):
        return f"Current status."
    elif field_lower.endswith('_type'):
        return f"Type classification."
    elif field_lower == 'status':
        return "Current status."
    elif field_lower == 'type':
        return "Type classification."

    # Boolean/bit patterns
    elif field_type.lower() == 'bit':
        if 'enabled' in field_lower:
            return "Indicates if item is enabled."
        elif 'active' in field_lower:
            return "Indicates if item is active."
        elif 'deleted' in field_lower:
            return "Indicates if item is deleted."
        elif 'acquired' in field_lower:
            return "Indicates if item was acquired."
        elif 'match' in field_lower:
            return "Indicates if values match."
        else:
            return f"Boolean flag for {field_lower.replace('_', ' ')}."

    # Size and count patterns
    elif 'size' in field_lower:
        return "Size measurement."
    elif 'count' in field_lower:
        return "Count value."
    elif 'number' in field_lower:
        if 'case' in field_lower:
            return "Case number."
        elif 'tag' in field_lower:
            return "Tag number."
        elif 'serial' in field_lower:
            return "Serial number."
        else:
            return f"{field_lower.replace('_', ' ').title()}."

    # Order and priority patterns
    elif 'order' in field_lower:
        return "Display order."
    elif 'priority' in field_lower:
        return "Priority level."

    # Content patterns
    elif field_lower in ['content', 'body', 'description']:
        return f"{field_lower.title()} text."
    elif field_lower == 'subject':
        return "Subject line."
    elif field_lower == 'title':
        return "Title."
    elif field_lower == 'comment':
        return "Comment text."
    elif field_lower == 'note':
        return "Note text."

    # Contact information patterns
    elif 'email' in field_lower:
        return "Email address."
    elif 'phone' in field_lower:
        return "Phone number."
    elif 'address' in field_lower:
        return "Address information."

    # Technical patterns
    elif 'url' in field_lower or 'path' in field_lower:
        return "File path or URL."
    elif 'hash' in field_lower or 'md5' in field_lower or 'checksum' in field_lower:
        return "Hash or checksum value."
    elif 'mime' in field_lower:
        return "MIME type."
    elif 'extension' in field_lower:
        return "File extension."

    # Location patterns
    elif 'country' in field_lower:
        return "Country information."
    elif 'state' in field_lower:
        return "State information."
    elif 'code' in field_lower:
        return "Code value."

    # Default fallback
    else:
        # Clean up field name for generic description
        clean_name = field_lower.replace('_', ' ').strip()
        return f"{clean_name.title()} value."

def count_all_fields():
    """Count all fields in all CIMS database tables."""

    # Base table structure files
    base_tables_path = "SQL/database/STRUCTURE/TABLES/*.sql"

    # Additional version-specific tables
    version_paths = [
        "SQL/versions/*/000-*.sql",
        "SQL/versions/*/install.sql",
        "SQL/versions/knowledge_repo/*.sql"
    ]

    all_files = []

    # Get base table files (this should include all the main tables)
    all_files.extend(glob.glob(base_tables_path))

    # Get version-specific files
    for pattern in version_paths:
        all_files.extend(glob.glob(pattern))

    # Remove duplicates
    all_files = list(set(all_files))

    table_fields = {}
    total_fields = 0

    print("CIMS Database Field Analysis")
    print("=" * 50)

    for file_path in sorted(all_files):
        table_name, fields = extract_fields_from_sql_file(file_path)

        if table_name and fields:
            # Avoid counting the same table multiple times
            if table_name not in table_fields:
                table_fields[table_name] = fields
                field_count = len(fields)
                total_fields += field_count
                print(f"{table_name}: {field_count} fields")
            else:
                print(f"{table_name}: (duplicate - skipped)")

    # Manually add missing tables with their actual field names based on SQL files and EF model
    missing_tables = {
        'agency': [
            {'field_name': 'agency_id', 'field_type': 'int', 'description': 'Unique identifier for each row of data in the table.'},
            {'field_name': 'name', 'field_type': 'nvarchar(255)', 'description': 'Name of the agency.'}
        ],
        'agency_unit': [
            {'field_name': 'agency_unit_id', 'field_type': 'int', 'description': 'Unique identifier for each row of data in the table.'},
            {'field_name': 'name', 'field_type': 'nvarchar(255)', 'description': 'The name of the agency_unit.'}
        ],
        'case_info_type': [
            {'field_name': 'case_info_type_id', 'field_type': 'int', 'description': 'Unique identifier for case info type.'},
            {'field_name': 'name', 'field_type': 'nvarchar(255)', 'description': 'Name of the case info type.'}
        ],
        'classification': [
            {'field_name': 'classification_id', 'field_type': 'int', 'description': 'Unique identifier for each row of data in the table.'},
            {'field_name': 'level', 'field_type': 'nvarchar(255)', 'description': 'Level of classification.'},
            {'field_name': 'list_order', 'field_type': 'int', 'description': 'Order to display the classifications.'}
        ],
        'communication_topic': [
            {'field_name': 'communication_topic_id', 'field_type': 'int', 'description': 'Unique identifier for communication topic.'},
            {'field_name': 'name', 'field_type': 'nvarchar(255)', 'description': 'Name of the communication topic.'}
        ],
        'communication_type': [
            {'field_name': 'communication_type_id', 'field_type': 'int', 'description': 'Unique identifier for communication type.'},
            {'field_name': 'name', 'field_type': 'nvarchar(255)', 'description': 'Name of the communication type.'}
        ],
        'country': [
            {'field_name': 'country_id', 'field_type': 'int', 'description': 'Unique identifier for each row of data in the table.'},
            {'field_name': 'code', 'field_type': 'nvarchar(255)', 'description': 'Two character code for country.'},
            {'field_name': 'name', 'field_type': 'nvarchar(255)', 'description': 'The name of the country.'}
        ],
        'directorate': [
            {'field_name': 'directorate_id', 'field_type': 'int', 'description': 'Unique identifier for directorate.'},
            {'field_name': 'name', 'field_type': 'nvarchar(255)', 'description': 'Name of the directorate.'}
        ],
        'discharge': [
            {'field_name': 'discharge_id', 'field_type': 'int', 'description': 'Unique identifier for discharge type.'},
            {'field_name': 'name', 'field_type': 'nvarchar(255)', 'description': 'Name of the discharge type.'}
        ],
        'eh_attachments': [
            {'field_name': 'id', 'field_type': 'int', 'description': 'Unique identifier for attachment.'},
            {'field_name': 'file_name', 'field_type': 'nvarchar(260)', 'description': 'Name of the attached file.'},
            {'field_name': 'mime_type', 'field_type': 'nvarchar(max)', 'description': 'MIME type of the file.'},
            {'field_name': 'file_size', 'field_type': 'int', 'description': 'Size of the file in bytes.'},
            {'field_name': 'saved_path', 'field_type': 'nvarchar(max)', 'description': 'Path where file is saved.'},
            {'field_name': 'md5', 'field_type': 'nvarchar(255)', 'description': 'MD5 hash of the file.'},
            {'field_name': 'ticket_id', 'field_type': 'int', 'description': 'Foreign key to eh_ticket table.'}
        ],
        'eh_evidence': [
            {'field_name': 'id', 'field_type': 'int', 'description': 'Unique identifier for evidence link.'},
            {'field_name': 'ticket_id', 'field_type': 'int', 'description': 'Foreign key to eh_ticket table.'},
            {'field_name': 'evidence_id', 'field_type': 'int', 'description': 'Foreign key to evidence table.'}
        ],
        'eh_history': [
            {'field_name': 'id', 'field_type': 'int', 'description': 'Unique identifier for history entry.'},
            {'field_name': 'date_updated', 'field_type': 'datetime', 'description': 'Date when the update occurred.'},
            {'field_name': 'updated_by', 'field_type': 'int', 'description': 'Account ID of user who made the update.'},
            {'field_name': 'comment', 'field_type': 'nvarchar(max)', 'description': 'Comment about the update.'},
            {'field_name': 'ticket_id', 'field_type': 'int', 'description': 'Foreign key to eh_ticket table.'}
        ],
        'eh_ticket': [
            {'field_name': 'id', 'field_type': 'int', 'description': 'Unique identifier for ticket.'},
            {'field_name': 'content', 'field_type': 'nvarchar(max)', 'description': 'Content/description of the ticket.'},
            {'field_name': 'exam_id', 'field_type': 'int', 'description': 'Foreign key to exam table.'},
            {'field_name': 'type_id', 'field_type': 'int', 'description': 'Foreign key to eh_ticket_type table.'},
            {'field_name': 'reporter_id', 'field_type': 'int', 'description': 'Account ID of person who reported the ticket.'},
            {'field_name': 'status_id', 'field_type': 'int', 'description': 'Foreign key to eh_status_type table.'},
            {'field_name': 'assignee_id', 'field_type': 'int', 'description': 'Account ID of person assigned to the ticket.'},
            {'field_name': 'title', 'field_type': 'nvarchar(max)', 'description': 'Title of the ticket.'}
        ],
        'eh_ticket_type': [
            {'field_name': 'id', 'field_type': 'int', 'description': 'Unique identifier for ticket type.'},
            {'field_name': 'ticket_type', 'field_type': 'nvarchar(255)', 'description': 'Name of the ticket type.'},
            {'field_name': 'enabled', 'field_type': 'bit', 'description': 'Whether this ticket type is enabled.'}
        ],
        'eh_ticket_type_members': [
            {'field_name': 'id', 'field_type': 'int', 'description': 'Unique identifier for membership.'},
            {'field_name': 'account_id', 'field_type': 'int', 'description': 'Foreign key to account table.'},
            {'field_name': 'ticket_type_id', 'field_type': 'int', 'description': 'Foreign key to eh_ticket_type table.'}
        ],
        'eh_user_subscriptions': [
            {'field_name': 'id', 'field_type': 'int', 'description': 'Unique identifier for subscription.'},
            {'field_name': 'account_id', 'field_type': 'int', 'description': 'Foreign key to account table.'},
            {'field_name': 'ticket_id', 'field_type': 'int', 'description': 'Foreign key to eh_ticket table.'}
        ],
        'evidence_detail_type': [
            {'field_name': 'evidence_detail_type_id', 'field_type': 'int', 'description': 'Unique identifier for evidence detail type.'},
            {'field_name': 'name', 'field_type': 'nvarchar(255)', 'description': 'Name of the evidence detail type.'}
        ],
        'evidence_detail_value': [
            {'field_name': 'evidence_detail_value_id', 'field_type': 'int', 'description': 'Unique identifier for evidence detail value.'},
            {'field_name': 'name', 'field_type': 'nvarchar(255)', 'description': 'Value of the evidence detail.'}
        ],
        'evidence_status': [
            {'field_name': 'evidence_status_id', 'field_type': 'int', 'description': 'Unique identifier for evidence status.'},
            {'field_name': 'name', 'field_type': 'nvarchar(255)', 'description': 'Name of the evidence status.'}
        ],
        'exam_hold_type': [
            {'field_name': 'exam_hold_type_id', 'field_type': 'int', 'description': 'Unique identifier for exam hold type.'},
            {'field_name': 'name', 'field_type': 'nvarchar(255)', 'description': 'Name of the exam hold type.'}
        ],
        'exam_status': [
            {'field_name': 'exam_status_id', 'field_type': 'int', 'description': 'Unique identifier for exam status.'},
            {'field_name': 'name', 'field_type': 'nvarchar(255)', 'description': 'Name of the exam status.'}
        ],
        'group': [
            {'field_name': 'group_id', 'field_type': 'int', 'description': 'Unique identifier for group.'},
            {'field_name': 'name', 'field_type': 'nvarchar(255)', 'description': 'Name of the group.'}
        ],
        'jurisdiction': [
            {'field_name': 'jurisdiction_id', 'field_type': 'int', 'description': 'Unique identifier for each row of data in the table.'},
            {'field_name': 'name', 'field_type': 'nvarchar(255)', 'description': 'Name of the jurisdiction.'},
            {'field_name': 'list_order', 'field_type': 'int', 'description': 'Order to display the jurisdictions.'}
        ],
        'mail_service': [
            {'field_name': 'mail_service_id', 'field_type': 'int', 'description': 'Unique identifier for mail service.'},
            {'field_name': 'name', 'field_type': 'nvarchar(255)', 'description': 'Name of the mail service.'}
        ],
        'message': [
            {'field_name': 'message_id', 'field_type': 'int', 'description': 'Unique identifier for message.'},
            {'field_name': 'subject', 'field_type': 'nvarchar(255)', 'description': 'Subject of the message.'},
            {'field_name': 'body', 'field_type': 'nvarchar(max)', 'description': 'Body content of the message.'},
            {'field_name': 'date_sent', 'field_type': 'datetime', 'description': 'Date when message was sent.'},
            {'field_name': 'sender_id', 'field_type': 'int', 'description': 'Account ID of message sender.'},
            {'field_name': 'priority', 'field_type': 'int', 'description': 'Priority level of the message.'}
        ],
        'message_recipients': [
            {'field_name': 'message_recipient_id', 'field_type': 'int', 'description': 'Unique identifier for message recipient.'},
            {'field_name': 'message_id', 'field_type': 'int', 'description': 'Foreign key to message table.'},
            {'field_name': 'recipient_id', 'field_type': 'int', 'description': 'Account ID of message recipient.'},
            {'field_name': 'read_date', 'field_type': 'datetime', 'description': 'Date when message was read.'}
        ],
        'military_rank': [
            {'field_name': 'military_rank_id', 'field_type': 'int', 'description': 'Unique identifier for military rank.'},
            {'field_name': 'name', 'field_type': 'nvarchar(255)', 'description': 'Name of the military rank.'},
            {'field_name': 'list_order', 'field_type': 'int', 'description': 'Order to display the military ranks.'}
        ],
        'pay_forfiture': [
            {'field_name': 'pay_forfiture_id', 'field_type': 'int', 'description': 'Unique identifier for pay forfiture.'},
            {'field_name': 'name', 'field_type': 'nvarchar(255)', 'description': 'Name of the pay forfiture type.'}
        ],
        'proceeding': [
            {'field_name': 'proceeding_id', 'field_type': 'int', 'description': 'Unique identifier for proceeding.'},
            {'field_name': 'name', 'field_type': 'nvarchar(255)', 'description': 'Name of the proceeding type.'}
        ],
        'state': [
            {'field_name': 'state_id', 'field_type': 'int', 'description': 'Unique identifier for each row of data in the table.'},
            {'field_name': 'name', 'field_type': 'nvarchar(255)', 'description': 'Name of the state.'},
            {'field_name': 'state_code', 'field_type': 'nvarchar(255)', 'description': 'Two character code for the state.'}
        ],
        'evidence_backup': [
            {'field_name': 'evidence_id', 'field_type': 'int', 'description': 'Backup copy of evidence_id from evidence table.'},
            {'field_name': 'case_id', 'field_type': 'int', 'description': 'Backup copy of case_id from evidence table.'},
            {'field_name': 'evidence_description', 'field_type': 'nvarchar(max)', 'description': 'Backup copy of evidence description.'},
            {'field_name': 'evidence_parent_id', 'field_type': 'int', 'description': 'Backup copy of parent evidence ID.'},
            {'field_name': 'evidence_tag_id', 'field_type': 'int', 'description': 'Backup copy of evidence tag ID.'},
            {'field_name': 'evidence_type_id', 'field_type': 'int', 'description': 'Backup copy of evidence type ID.'},
            {'field_name': 'exam_package_id', 'field_type': 'int', 'description': 'Backup copy of exam package ID.'},
            {'field_name': 'label', 'field_type': 'nvarchar(255)', 'description': 'Backup copy of evidence label.'},
            {'field_name': 'make', 'field_type': 'nvarchar(255)', 'description': 'Backup copy of evidence make.'},
            {'field_name': 'model', 'field_type': 'nvarchar(255)', 'description': 'Backup copy of evidence model.'},
            {'field_name': 'nas_image', 'field_type': 'nvarchar(255)', 'description': 'Backup copy of NAS image path.'},
            {'field_name': 'serialno', 'field_type': 'nvarchar(255)', 'description': 'Backup copy of serial number.'},
            {'field_name': 'storage_size', 'field_type': 'float', 'description': 'Backup copy of storage size.'},
            {'field_name': 'acquired', 'field_type': 'bit', 'description': 'Backup copy of acquired flag.'},
            {'field_name': 'marked', 'field_type': 'nvarchar(256)', 'description': 'Backup copy of marked field.'},
            {'field_name': 'referred_to', 'field_type': 'nvarchar(256)', 'description': 'Backup copy of referred_to field.'},
            {'field_name': 'md5_match', 'field_type': 'bit', 'description': 'Backup copy of MD5 match flag.'},
            {'field_name': 'md5_mismatch_reason', 'field_type': 'nvarchar(4000)', 'description': 'Backup copy of MD5 mismatch reason.'}
        ],
        'evidence_tag_backup': [
            {'field_name': 'evidence_tag_id', 'field_type': 'int', 'description': 'Backup copy of evidence tag ID.'},
            {'field_name': 'evidence_tag_type_id', 'field_type': 'int', 'description': 'Backup copy of evidence tag type ID.'},
            {'field_name': 'tag_number', 'field_type': 'nvarchar(255)', 'description': 'Backup copy of tag number.'}
        ],
        'data_transfer_email': [
            {'field_name': 'id', 'field_type': 'int', 'description': 'Unique identifier for email transfer.'},
            {'field_name': 'to_email', 'field_type': 'nvarchar(255)', 'description': 'Recipient email address.'},
            {'field_name': 'from_email', 'field_type': 'nvarchar(255)', 'description': 'Sender email address.'},
            {'field_name': 'subject', 'field_type': 'nvarchar(255)', 'description': 'Email subject line.'},
            {'field_name': 'body', 'field_type': 'nvarchar(max)', 'description': 'Email body content.'},
            {'field_name': 'date_sent', 'field_type': 'datetime', 'description': 'Date when email was sent.'},
            {'field_name': 'attachment_count', 'field_type': 'int', 'description': 'Number of attachments.'},
            {'field_name': 'status', 'field_type': 'nvarchar(50)', 'description': 'Status of the email transfer.'}
        ],
        'data_transfer_file': [
            {'field_name': 'id', 'field_type': 'int', 'description': 'Unique identifier for file transfer.'},
            {'field_name': 'file_name', 'field_type': 'nvarchar(255)', 'description': 'Name of the transferred file.'},
            {'field_name': 'file_path', 'field_type': 'nvarchar(500)', 'description': 'Path to the transferred file.'},
            {'field_name': 'file_size', 'field_type': 'bigint', 'description': 'Size of the file in bytes.'},
            {'field_name': 'upload_date', 'field_type': 'datetime', 'description': 'Date when file was uploaded.'},
            {'field_name': 'download_date', 'field_type': 'datetime', 'description': 'Date when file was downloaded.'},
            {'field_name': 'status', 'field_type': 'nvarchar(50)', 'description': 'Status of the file transfer.'},
            {'field_name': 'checksum', 'field_type': 'nvarchar(255)', 'description': 'Checksum of the file.'},
            {'field_name': 'user_id', 'field_type': 'int', 'description': 'Account ID of user who transferred the file.'},
            {'field_name': 'description', 'field_type': 'nvarchar(500)', 'description': 'Description of the file transfer.'}
        ],
        'dashboard_state_thresholds_type': [
            {'field_name': 'id', 'field_type': 'int', 'description': 'Unique identifier for threshold type.'},
            {'field_name': 'threshold_type', 'field_type': 'nvarchar(max)', 'description': 'Name of the threshold type.'}
        ],
        'dashboard_state_thresholds_value': [
            {'field_name': 'id', 'field_type': 'int', 'description': 'Unique identifier for threshold value.'},
            {'field_name': 'threshold_type_id', 'field_type': 'int', 'description': 'Foreign key to dashboard_state_thresholds_type.'},
            {'field_name': 'threshold_value', 'field_type': 'int', 'description': 'The threshold value.'},
            {'field_name': 'group_id', 'field_type': 'int', 'description': 'Foreign key to group table.'}
        ],
        'user_config_value': [
            {'field_name': 'date_updated', 'field_type': 'datetime', 'description': 'Date when configuration was last updated.'},
            {'field_name': 'user_id', 'field_type': 'int', 'description': 'Foreign key to account table.'},
            {'field_name': 'config_value', 'field_type': 'nvarchar(max)', 'description': 'The configuration value.'},
            {'field_name': 'config_type_id', 'field_type': 'int', 'description': 'Foreign key to user_config_type table.'}
        ]
    }

    print(f"\nAdding missing tables from Entity Framework model...")
    for table_name, field_list in missing_tables.items():
        if table_name not in table_fields:
            table_fields[table_name] = field_list
            total_fields += len(field_list)
            print(f"Added {table_name}: {len(field_list)} fields")

    # Fill in missing descriptions only (don't change existing ones)
    print(f"\nFilling in missing descriptions...")
    filled_count = 0

    for table_name, fields in table_fields.items():
        for field_info in fields:
            if isinstance(field_info, dict):
                current_desc = field_info.get('description', '').strip()

                # Only generate description if completely missing or empty
                if not current_desc or current_desc == '':
                    field_info['description'] = generate_description_from_field_name(
                        field_info['field_name'],
                        field_info['field_type'],
                        table_name
                    )
                    filled_count += 1

    print(f"Generated descriptions for {filled_count} fields with missing descriptions.")

    print("\n" + "=" * 50)
    print(f"TOTAL TABLES: {len(table_fields)}")
    print(f"TOTAL FIELDS: {total_fields}")
    print("=" * 50)

    return total_fields, len(table_fields), table_fields

def generate_excel_documentation(table_fields):
    """Generate Excel file with comprehensive database documentation."""

    # Prepare data for Excel
    excel_data = []

    # Define logical groups
    logical_groups = {
        'Case & Legal Management': [
            'case', 'case_info', 'case_info_type', 'case_info_value', 'case_note',
            'case_subject', 'case_victim', 'case_consultant', 'case_trial',
            'classification', 'jurisdiction', 'proceeding', 'military_rank',
            'pay_forfiture', 'discharge'
        ],
        'Evidence & Digital Assets': [
            'evidence', 'evidence_history', 'evidence_status', 'evidence_note',
            'evidence_tag', 'evidence_tag_type', 'evidence_tag_backup', 'evidence_type',
            'evidence_type_field', 'evidence_activity', 'evidence_detail',
            'evidence_detail_type', 'evidence_detail_value', 'evidence_image',
            'evidence_backup', 'evidence_tag_staging1', 'evidence_ci_detail_type',
            'evidence_ci_detail_value', 'evidence_config_item_detail',
            'evidence_configuration_item', 'configuration_item', 'unlock_srvc_evidence',
            'unlock_srvc', 'unlock_srvc_comment', 'unlock_srvc_group'
        ],
        'Examination & Workflow Management': [
            'exam', 'exam_status', 'exam_type', 'exam_category', 'exam_activity',
            'exam_evidence', 'exam_note', 'exam_package', 'exam_standard', 'exam_hold',
            'exam_hold_type', 'exam_event', 'exam_event_type', 'exam_investigation_type',
            'exam_intrusion', 'activity', 'directorate', 'unlock_srvc_lock_type',
            'unlock_srvc_purchased_token', 'unlock_srvc_input_temp', 'unlock_srvc_input_staging_temp'
        ],
        'User & Access Management': [
            'account', 'account_type', 'account_role', 'account_permission',
            'account_activity', 'user_config_type', 'user_config_value', 'group',
            'audit_log', 'eh_user_subscriptions', 'eh_ticket_type_members',
            'knowledge_repo_favorites', 'message', 'message_recipients', 'data_transfer_email'
        ],
        'Infrastructure & Asset Management': [
            'hard_drive', 'hard_drive_history', 'hard_drive_status', 'cm_inventory',
            'cm_inventory_type', 'cm_history', 'cm_history_types', 'cm_history_evidence',
            'contact', 'agency', 'agency_org', 'agency_unit', 'country', 'state',
            'mail_service', 'data_transfer_file', 'dashboard_state_thresholds_type',
            'dashboard_state_thresholds_value'
        ],
        'Communication & Knowledge Management': [
            'communication', 'communication_type', 'communication_topic',
            'communication_party_external', 'communication_party_internal',
            'eh_ticket', 'eh_ticket_type', 'eh_status_type', 'eh_attachments',
            'eh_evidence', 'eh_history', 'knowledge_repo_types', 'knowledge_repo_history',
            'knowledge_repo_attachments', 'knowledge_repo_default_tags',
            'unlock_srvc_input_lock_type_mapping_temp'
        ]
    }

    # Create reverse mapping for group lookup
    table_to_group = {}
    for group_name, tables in logical_groups.items():
        for table in tables:
            table_to_group[table] = group_name

    # Process each table and its fields
    for table_name, fields in sorted(table_fields.items()):
        group_name = table_to_group.get(table_name, 'Other')

        for field_info in fields:
            if isinstance(field_info, dict):
                excel_data.append({
                    'Logical_Group': group_name,
                    'Table_Name': table_name,
                    'Column_Name': field_info['field_name'],
                    'Data_Type': field_info['field_type'],
                    'Description': field_info['description']
                })
            else:
                # Handle old format (just field names)
                excel_data.append({
                    'Logical_Group': group_name,
                    'Table_Name': table_name,
                    'Column_Name': field_info,
                    'Data_Type': 'unknown',
                    'Description': 'Legacy field - type information not available'
                })

    # Create DataFrame and save to Excel
    df = pd.DataFrame(excel_data)

    # Create Excel file with multiple sheets
    with pd.ExcelWriter('CIMS_Database_Documentation.xlsx', engine='openpyxl') as writer:
        # Main sheet with all data
        df.to_excel(writer, sheet_name='All_Tables_and_Fields', index=False)

        # Summary sheet by logical group
        summary_data = []
        for group_name, tables in logical_groups.items():
            table_count = len([t for t in tables if t in table_fields])
            field_count = sum(len(table_fields.get(t, [])) for t in tables if t in table_fields)
            summary_data.append({
                'Logical_Group': group_name,
                'Table_Count': table_count,
                'Field_Count': field_count,
                'Tables': ', '.join([t for t in tables if t in table_fields])
            })

        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='Summary_by_Group', index=False)

        # Individual sheets for each logical group
        for group_name in logical_groups.keys():
            group_df = df[df['Logical_Group'] == group_name]
            if not group_df.empty:
                sheet_name = group_name.replace(' & ', '_').replace(' ', '_')[:31]  # Excel sheet name limit
                group_df.to_excel(writer, sheet_name=sheet_name, index=False)

    print(f"\nExcel documentation generated: CIMS_Database_Documentation.xlsx")
    print(f"Total records: {len(excel_data)}")
    return len(excel_data)

if __name__ == "__main__":
    total_fields, total_tables, table_fields = count_all_fields()

    # Generate Excel documentation
    excel_records = generate_excel_documentation(table_fields)

    print(f"\nFINAL SUMMARY:")
    print(f"Total Database Tables: {total_tables}")
    print(f"Total Data Fields: {total_fields}")
    print(f"Excel Records Generated: {excel_records}")
    print(f"Documentation saved as: CIMS_Database_Documentation.xlsx")
