﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\EntityFramework.6.4.0\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.4.0\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{E528B02E-CAD5-45DB-A94F-F0712B91C7E7}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>CIMSData</RootNamespace>
    <AssemblyName>CIMSData</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <HintPath>..\packages\EntityFramework.6.4.0\lib\net45\EntityFramework.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <HintPath>..\packages\EntityFramework.6.4.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Database\account.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\account_activity.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\account_permission.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\account_role.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\account_type.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\activity.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\agency.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\agency_org.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\agency_unit.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\audit_log.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\case.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\case_info_type.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\case_info_value.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\case_note.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\case_subject.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\case_trial.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\case_victim.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\cims11Model.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>cims11Model.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\cims11Model.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\cims11Model.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>cims11Model.edmx</DependentUpon>
    </Compile>
    <Compile Include="Database\classification.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\cm_history.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\cm_history_evidence.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\cm_history_types.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\cm_inventory.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\cm_inventory_type.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\communication.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\communication_topic.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\communication_type.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\configuration_item.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\contact.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\country.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\dashboard_state_thresholds_type.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\dashboard_state_thresholds_value.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\data_transfer_email.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\data_transfer_file.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\directorate.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\discharge.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\eh_attachments.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\eh_evidence.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\eh_history.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\eh_status_type.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\eh_ticket.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\eh_ticket_type.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\eh_ticket_type_members.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\eh_user_subscriptions.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\evidence.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\evidence_backup.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\evidence_ci_detail_type.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\evidence_ci_detail_value.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\evidence_configuration_item.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\evidence_config_item_detail.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\evidence_detail.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\evidence_detail_type.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\evidence_detail_value.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\evidence_history.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\evidence_image.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\evidence_note.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\evidence_status.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\evidence_tag.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\evidence_tag_backup.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\evidence_tag_type.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\evidence_type.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\evidence_type_field.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\exam.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\exam_activity.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\exam_category.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\exam_event.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\exam_event_type.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\exam_evidence.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\exam_hold.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\exam_hold_type.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\exam_intrusion.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\exam_investigation_type.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\exam_note.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\exam_package.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\exam_standard.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\exam_status.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\exam_type.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\group.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\hard_drive.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\hard_drive_history.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\hard_drive_status.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\jurisdiction.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\knowledge_repo_attachments.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\knowledge_repo_default_tags.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\knowledge_repo_favorites.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\knowledge_repo_history.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\knowledge_repo_types.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\mail_service.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\message.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\message_recipients.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\military_rank.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\pay_forfiture.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\proceeding.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\RunCimsMetrics_Result.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\SLA01_2025_Result.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\SLA01_2025_Result1.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\SLA02_2025_Result.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\SLA03_2025_Result.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\SLA03_2025_Result1.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\SLA04_2025_Result.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\sp_helpdiagramdefinition_Result.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\sp_helpdiagrams_Result.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\state.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\TotalDCISEExamsAndHours_Result.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\unlock_srvc.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\unlock_srvc_comment.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\unlock_srvc_evidence.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\unlock_srvc_group.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\unlock_srvc_input_temp.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\unlock_srvc_lock_type.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\unlock_srvc_purchased_token.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\user_config_type.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\user_config_value.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\vw_acquisition_time.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\vw_all_exams.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\vw_evidence.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\vw_evidence_details.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\vw_evidence_log.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\vw_exam_task_time.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Database\vw_missing_hard_drives.cs">
      <DependentUpon>cims11Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <EntityDeploy Include="Database\cims11Model.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>cims11Model.Designer.cs</LastGenOutput>
    </EntityDeploy>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.Config" />
    <None Include="ClassDiagram2.cd" />
    <None Include="Database\cims11Model.edmx.diagram">
      <DependentUpon>cims11Model.edmx</DependentUpon>
    </None>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Database\cims11Model.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>cims11Model.edmx</DependentUpon>
      <LastGenOutput>cims11Model.Context.cs</LastGenOutput>
    </Content>
    <Content Include="Database\cims11Model.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>cims11Model.edmx</DependentUpon>
      <LastGenOutput>cims11Model.cs</LastGenOutput>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Enable NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105.The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.0\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.0\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.0\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.0\build\EntityFramework.targets'))" />
  </Target>
  <Import Project="..\packages\EntityFramework.6.4.0\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.4.0\build\EntityFramework.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>