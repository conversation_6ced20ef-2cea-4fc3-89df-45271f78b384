//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CIMSData.Database
{
    using System;
    using System.Collections.Generic;
    
    public partial class cm_inventory
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public cm_inventory()
        {
            this.cm_history = new HashSet<cm_history>();
        }
    
        public int id { get; set; }
        public string asset_tag { get; set; }
        public string mb_number { get; set; }
        public string manufacturer { get; set; }
        public string model_number { get; set; }
        public string serial_number { get; set; }
        public string building { get; set; }
        public string floor { get; set; }
        public string suite { get; set; }
        public string cubicle { get; set; }
        public string network { get; set; }
        public string examiner_name { get; set; }
        public Nullable<int> examiner_id { get; set; }
        public int group_id { get; set; }
        public bool maintain { get; set; }
        public Nullable<System.DateTime> warranty_expire_date { get; set; }
        public Nullable<System.DateTime> last_inventory_date { get; set; }
        public int status_id { get; set; }
        public int cm_inventory_type_id { get; set; }
    
        public virtual account account { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<cm_history> cm_history { get; set; }
        public virtual cm_history_types cm_history_types { get; set; }
        public virtual cm_inventory_type cm_inventory_type { get; set; }
        public virtual group group { get; set; }
    }
}
