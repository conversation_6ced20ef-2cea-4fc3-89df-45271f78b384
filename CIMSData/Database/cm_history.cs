//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CIMSData.Database
{
    using System;
    using System.Collections.Generic;
    
    public partial class cm_history
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public cm_history()
        {
            this.cm_history_evidence = new HashSet<cm_history_evidence>();
        }
    
        public int id { get; set; }
        public Nullable<int> inventory_id { get; set; }
        public int history_type_id { get; set; }
        public Nullable<int> account_id { get; set; }
        public Nullable<int> build_id { get; set; }
        public Nullable<int> group_id { get; set; }
        public Nullable<int> exam_id { get; set; }
        public string remarks { get; set; }
        public string examiner_name { get; set; }
        public System.DateTime date { get; set; }
        public string build_name { get; set; }
        public Nullable<bool> vde { get; set; }
    
        public virtual account account { get; set; }
        public virtual cm_history_types cm_history_types { get; set; }
        public virtual cm_inventory cm_inventory { get; set; }
        public virtual configuration_item configuration_item { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<cm_history_evidence> cm_history_evidence { get; set; }
        public virtual exam exam { get; set; }
        public virtual group group { get; set; }
    }
}
