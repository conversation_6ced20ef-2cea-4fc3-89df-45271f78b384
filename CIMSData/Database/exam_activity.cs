//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CIMSData.Database
{
    using System;
    using System.Collections.Generic;
    
    public partial class exam_activity
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public exam_activity()
        {
            this.account_activity = new HashSet<account_activity>();
            this.exam_hold = new HashSet<exam_hold>();
            this.evidence = new HashSet<evidence>();
        }
    
        public int exam_activity_id { get; set; }
        public Nullable<int> account_id { get; set; }
        public Nullable<int> account_role_id { get; set; }
        public int activity_id { get; set; }
        public Nullable<int> associated_account_id { get; set; }
        public Nullable<System.DateTime> date_end { get; set; }
        public Nullable<System.DateTime> date_start { get; set; }
        public int exam_id { get; set; }
        public Nullable<int> group_id { get; set; }
        public Nullable<bool> skipped { get; set; }
    
        public virtual account account { get; set; }
        public virtual account account1 { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<account_activity> account_activity { get; set; }
        public virtual account_role account_role { get; set; }
        public virtual activity activity { get; set; }
        public virtual exam exam { get; set; }
        public virtual group group { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<exam_hold> exam_hold { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<evidence> evidence { get; set; }
    }
}
