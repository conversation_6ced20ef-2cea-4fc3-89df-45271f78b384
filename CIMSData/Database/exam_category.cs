//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CIMSData.Database
{
    using System;
    using System.Collections.Generic;
    
    public partial class exam_category
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public exam_category()
        {
            this.exam = new HashSet<exam>();
        }
    
        public int exam_category_id { get; set; }
        public string exam_category1 { get; set; }
        public string exam_subcategory { get; set; }
        public int exam_type_id { get; set; }
        public Nullable<int> exam_investigation_type_id { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<exam> exam { get; set; }
        public virtual exam_investigation_type exam_investigation_type { get; set; }
        public virtual exam_type exam_type { get; set; }
    }
}
