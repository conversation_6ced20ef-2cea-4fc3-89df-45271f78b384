//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CIMSData.Database
{
    using System;
    using System.Collections.Generic;
    
    public partial class eh_ticket
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public eh_ticket()
        {
            this.eh_attachments = new HashSet<eh_attachments>();
            this.eh_evidence = new HashSet<eh_evidence>();
            this.eh_history = new HashSet<eh_history>();
            this.eh_user_subscriptions = new HashSet<eh_user_subscriptions>();
        }
    
        public int id { get; set; }
        public string content { get; set; }
        public Nullable<int> exam_id { get; set; }
        public int type_id { get; set; }
        public int reporter_id { get; set; }
        public int status_id { get; set; }
        public Nullable<int> assignee_id { get; set; }
        public string title { get; set; }
    
        public virtual account account { get; set; }
        public virtual account account1 { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<eh_attachments> eh_attachments { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<eh_evidence> eh_evidence { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<eh_history> eh_history { get; set; }
        public virtual eh_status_type eh_status_type { get; set; }
        public virtual eh_ticket_type eh_ticket_type { get; set; }
        public virtual exam exam { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<eh_user_subscriptions> eh_user_subscriptions { get; set; }
    }
}
