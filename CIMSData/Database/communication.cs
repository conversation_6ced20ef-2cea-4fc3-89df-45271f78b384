//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CIMSData.Database
{
    using System;
    using System.Collections.Generic;
    
    public partial class communication
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public communication()
        {
            this.communication1 = new HashSet<communication>();
            this.contact = new HashSet<contact>();
            this.account1 = new HashSet<account>();
        }
    
        public int communication_id { get; set; }
        public string action_required { get; set; }
        public Nullable<int> agency_id { get; set; }
        public Nullable<int> child_id { get; set; }
        public System.DateTime communication_datetime { get; set; }
        public int communication_topic_id { get; set; }
        public int communication_type_id { get; set; }
        public Nullable<int> duration { get; set; }
        public Nullable<int> entered_by { get; set; }
        public System.DateTime entered_datetime { get; set; }
        public Nullable<int> exam_id { get; set; }
        public int follow_up { get; set; }
        public string label { get; set; }
        public string notes_internal { get; set; }
        public string notes_external { get; set; }
    
        public virtual account account { get; set; }
        public virtual agency agency { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<communication> communication1 { get; set; }
        public virtual communication communication2 { get; set; }
        public virtual communication_topic communication_topic { get; set; }
        public virtual communication_type communication_type { get; set; }
        public virtual exam exam { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<contact> contact { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<account> account1 { get; set; }
    }
}
