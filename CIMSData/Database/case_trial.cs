//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CIMSData.Database
{
    using System;
    using System.Collections.Generic;
    
    public partial class case_trial
    {
        public int case_id { get; set; }
        public bool adjudged { get; set; }
        public bool charge_sheet { get; set; }
        public bool confinement_life { get; set; }
        public byte confinement_months { get; set; }
        public byte confinement_years { get; set; }
        public Nullable<System.DateTime> date_feedback { get; set; }
        public bool defense_expert_info { get; set; }
        public Nullable<int> discharge_id { get; set; }
        public bool examiner_testified { get; set; }
        public bool examiner_traveled { get; set; }
        public Nullable<int> military_rank_id { get; set; }
        public bool online_notes { get; set; }
        public Nullable<int> pay_forfiture_id { get; set; }
        public bool prior_notes { get; set; }
        public bool rel_info { get; set; }
        public bool sex_offender { get; set; }
        public bool signed_pta { get; set; }
        public byte times_continued { get; set; }
        public Nullable<int> trial_counsel_id { get; set; }
        public Nullable<int> trial_counsel_senior_id { get; set; }
        public string trial_location { get; set; }
    
        public virtual @case @case { get; set; }
        public virtual discharge discharge { get; set; }
        public virtual military_rank military_rank { get; set; }
        public virtual pay_forfiture pay_forfiture { get; set; }
        public virtual contact contact { get; set; }
        public virtual contact contact1 { get; set; }
    }
}
