﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CIMSData.Database
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    using System.Data.Entity.Core.Objects;
    using System.Linq;
    
    public partial class cims11Entities : DbContext
    {
        public cims11Entities()
            : base("name=cims11Entities")
        {
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
        public virtual DbSet<account> account { get; set; }
        public virtual DbSet<account_activity> account_activity { get; set; }
        public virtual DbSet<account_permission> account_permission { get; set; }
        public virtual DbSet<account_role> account_role { get; set; }
        public virtual DbSet<account_type> account_type { get; set; }
        public virtual DbSet<activity> activity { get; set; }
        public virtual DbSet<agency> agency { get; set; }
        public virtual DbSet<agency_org> agency_org { get; set; }
        public virtual DbSet<agency_unit> agency_unit { get; set; }
        public virtual DbSet<audit_log> audit_log { get; set; }
        public virtual DbSet<@case> @case { get; set; }
        public virtual DbSet<case_info_type> case_info_type { get; set; }
        public virtual DbSet<case_info_value> case_info_value { get; set; }
        public virtual DbSet<case_note> case_note { get; set; }
        public virtual DbSet<case_subject> case_subject { get; set; }
        public virtual DbSet<case_trial> case_trial { get; set; }
        public virtual DbSet<case_victim> case_victim { get; set; }
        public virtual DbSet<classification> classification { get; set; }
        public virtual DbSet<cm_history> cm_history { get; set; }
        public virtual DbSet<cm_history_evidence> cm_history_evidence { get; set; }
        public virtual DbSet<cm_history_types> cm_history_types { get; set; }
        public virtual DbSet<cm_inventory> cm_inventory { get; set; }
        public virtual DbSet<cm_inventory_type> cm_inventory_type { get; set; }
        public virtual DbSet<communication> communication { get; set; }
        public virtual DbSet<communication_topic> communication_topic { get; set; }
        public virtual DbSet<communication_type> communication_type { get; set; }
        public virtual DbSet<configuration_item> configuration_item { get; set; }
        public virtual DbSet<contact> contact { get; set; }
        public virtual DbSet<country> country { get; set; }
        public virtual DbSet<dashboard_state_thresholds_type> dashboard_state_thresholds_type { get; set; }
        public virtual DbSet<dashboard_state_thresholds_value> dashboard_state_thresholds_value { get; set; }
        public virtual DbSet<directorate> directorate { get; set; }
        public virtual DbSet<discharge> discharge { get; set; }
        public virtual DbSet<eh_attachments> eh_attachments { get; set; }
        public virtual DbSet<eh_evidence> eh_evidence { get; set; }
        public virtual DbSet<eh_history> eh_history { get; set; }
        public virtual DbSet<eh_status_type> eh_status_type { get; set; }
        public virtual DbSet<eh_ticket> eh_ticket { get; set; }
        public virtual DbSet<eh_ticket_type> eh_ticket_type { get; set; }
        public virtual DbSet<eh_ticket_type_members> eh_ticket_type_members { get; set; }
        public virtual DbSet<eh_user_subscriptions> eh_user_subscriptions { get; set; }
        public virtual DbSet<evidence> evidence { get; set; }
        public virtual DbSet<evidence_ci_detail_type> evidence_ci_detail_type { get; set; }
        public virtual DbSet<evidence_ci_detail_value> evidence_ci_detail_value { get; set; }
        public virtual DbSet<evidence_config_item_detail> evidence_config_item_detail { get; set; }
        public virtual DbSet<evidence_configuration_item> evidence_configuration_item { get; set; }
        public virtual DbSet<evidence_detail> evidence_detail { get; set; }
        public virtual DbSet<evidence_detail_type> evidence_detail_type { get; set; }
        public virtual DbSet<evidence_detail_value> evidence_detail_value { get; set; }
        public virtual DbSet<evidence_history> evidence_history { get; set; }
        public virtual DbSet<evidence_image> evidence_image { get; set; }
        public virtual DbSet<evidence_note> evidence_note { get; set; }
        public virtual DbSet<evidence_status> evidence_status { get; set; }
        public virtual DbSet<evidence_tag> evidence_tag { get; set; }
        public virtual DbSet<evidence_tag_type> evidence_tag_type { get; set; }
        public virtual DbSet<evidence_type> evidence_type { get; set; }
        public virtual DbSet<evidence_type_field> evidence_type_field { get; set; }
        public virtual DbSet<exam> exam { get; set; }
        public virtual DbSet<exam_activity> exam_activity { get; set; }
        public virtual DbSet<exam_category> exam_category { get; set; }
        public virtual DbSet<exam_event> exam_event { get; set; }
        public virtual DbSet<exam_event_type> exam_event_type { get; set; }
        public virtual DbSet<exam_evidence> exam_evidence { get; set; }
        public virtual DbSet<exam_hold> exam_hold { get; set; }
        public virtual DbSet<exam_hold_type> exam_hold_type { get; set; }
        public virtual DbSet<exam_intrusion> exam_intrusion { get; set; }
        public virtual DbSet<exam_investigation_type> exam_investigation_type { get; set; }
        public virtual DbSet<exam_note> exam_note { get; set; }
        public virtual DbSet<exam_package> exam_package { get; set; }
        public virtual DbSet<exam_standard> exam_standard { get; set; }
        public virtual DbSet<exam_status> exam_status { get; set; }
        public virtual DbSet<exam_type> exam_type { get; set; }
        public virtual DbSet<group> group { get; set; }
        public virtual DbSet<hard_drive> hard_drive { get; set; }
        public virtual DbSet<hard_drive_history> hard_drive_history { get; set; }
        public virtual DbSet<hard_drive_status> hard_drive_status { get; set; }
        public virtual DbSet<jurisdiction> jurisdiction { get; set; }
        public virtual DbSet<knowledge_repo_attachments> knowledge_repo_attachments { get; set; }
        public virtual DbSet<knowledge_repo_default_tags> knowledge_repo_default_tags { get; set; }
        public virtual DbSet<knowledge_repo_favorites> knowledge_repo_favorites { get; set; }
        public virtual DbSet<knowledge_repo_history> knowledge_repo_history { get; set; }
        public virtual DbSet<knowledge_repo_types> knowledge_repo_types { get; set; }
        public virtual DbSet<mail_service> mail_service { get; set; }
        public virtual DbSet<message> message { get; set; }
        public virtual DbSet<message_recipients> message_recipients { get; set; }
        public virtual DbSet<military_rank> military_rank { get; set; }
        public virtual DbSet<pay_forfiture> pay_forfiture { get; set; }
        public virtual DbSet<proceeding> proceeding { get; set; }
        public virtual DbSet<state> state { get; set; }
        public virtual DbSet<unlock_srvc> unlock_srvc { get; set; }
        public virtual DbSet<unlock_srvc_comment> unlock_srvc_comment { get; set; }
        public virtual DbSet<unlock_srvc_evidence> unlock_srvc_evidence { get; set; }
        public virtual DbSet<unlock_srvc_group> unlock_srvc_group { get; set; }
        public virtual DbSet<unlock_srvc_lock_type> unlock_srvc_lock_type { get; set; }
        public virtual DbSet<unlock_srvc_purchased_token> unlock_srvc_purchased_token { get; set; }
        public virtual DbSet<user_config_type> user_config_type { get; set; }
        public virtual DbSet<user_config_value> user_config_value { get; set; }
        public virtual DbSet<evidence_backup> evidence_backup { get; set; }
        public virtual DbSet<evidence_tag_backup> evidence_tag_backup { get; set; }
        public virtual DbSet<unlock_srvc_input_temp> unlock_srvc_input_temp { get; set; }
        public virtual DbSet<vw_acquisition_time> vw_acquisition_time { get; set; }
        public virtual DbSet<vw_evidence> vw_evidence { get; set; }
        public virtual DbSet<vw_evidence_details> vw_evidence_details { get; set; }
        public virtual DbSet<vw_evidence_log> vw_evidence_log { get; set; }
        public virtual DbSet<vw_exam_task_time> vw_exam_task_time { get; set; }
        public virtual DbSet<vw_missing_hard_drives> vw_missing_hard_drives { get; set; }
        public virtual DbSet<vw_all_exams> vw_all_exams { get; set; }
        public virtual DbSet<data_transfer_email> data_transfer_email { get; set; }
        public virtual DbSet<data_transfer_file> data_transfer_file { get; set; }
    
        public virtual ObjectResult<RunCimsMetrics_Result> RunCimsMetrics(Nullable<int> monthOfReport)
        {
            var monthOfReportParameter = monthOfReport.HasValue ?
                new ObjectParameter("MonthOfReport", monthOfReport) :
                new ObjectParameter("MonthOfReport", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<RunCimsMetrics_Result>("RunCimsMetrics", monthOfReportParameter);
        }
    
        [DbFunction("cims11Entities", "SLA01_2025")]
        public virtual IQueryable<SLA01_2025_Result> SLA01_2025()
        {
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<SLA01_2025_Result>("[cims11Entities].[SLA01_2025]()");
        }
    
        [DbFunction("cims11Entities", "TotalDCISEExamsAndHours")]
        public virtual IQueryable<TotalDCISEExamsAndHours_Result> TotalDCISEExamsAndHours(Nullable<System.DateTime> date_end)
        {
            var date_endParameter = date_end.HasValue ?
                new ObjectParameter("date_end", date_end) :
                new ObjectParameter("date_end", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<TotalDCISEExamsAndHours_Result>("[cims11Entities].[TotalDCISEExamsAndHours](@date_end)", date_endParameter);
        }
    
        [DbFunction("cims11Entities", "SLA03_2025")]
        public virtual IQueryable<SLA03_2025_Result1> SLA03_2025()
        {
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<SLA03_2025_Result1>("[cims11Entities].[SLA03_2025]()");
        }
    
        public virtual int sp_alterdiagram(string diagramname, Nullable<int> owner_id, Nullable<int> version, byte[] definition)
        {
            var diagramnameParameter = diagramname != null ?
                new ObjectParameter("diagramname", diagramname) :
                new ObjectParameter("diagramname", typeof(string));
    
            var owner_idParameter = owner_id.HasValue ?
                new ObjectParameter("owner_id", owner_id) :
                new ObjectParameter("owner_id", typeof(int));
    
            var versionParameter = version.HasValue ?
                new ObjectParameter("version", version) :
                new ObjectParameter("version", typeof(int));
    
            var definitionParameter = definition != null ?
                new ObjectParameter("definition", definition) :
                new ObjectParameter("definition", typeof(byte[]));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("sp_alterdiagram", diagramnameParameter, owner_idParameter, versionParameter, definitionParameter);
        }
    
        public virtual int sp_creatediagram(string diagramname, Nullable<int> owner_id, Nullable<int> version, byte[] definition)
        {
            var diagramnameParameter = diagramname != null ?
                new ObjectParameter("diagramname", diagramname) :
                new ObjectParameter("diagramname", typeof(string));
    
            var owner_idParameter = owner_id.HasValue ?
                new ObjectParameter("owner_id", owner_id) :
                new ObjectParameter("owner_id", typeof(int));
    
            var versionParameter = version.HasValue ?
                new ObjectParameter("version", version) :
                new ObjectParameter("version", typeof(int));
    
            var definitionParameter = definition != null ?
                new ObjectParameter("definition", definition) :
                new ObjectParameter("definition", typeof(byte[]));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("sp_creatediagram", diagramnameParameter, owner_idParameter, versionParameter, definitionParameter);
        }
    
        public virtual int sp_dropdiagram(string diagramname, Nullable<int> owner_id)
        {
            var diagramnameParameter = diagramname != null ?
                new ObjectParameter("diagramname", diagramname) :
                new ObjectParameter("diagramname", typeof(string));
    
            var owner_idParameter = owner_id.HasValue ?
                new ObjectParameter("owner_id", owner_id) :
                new ObjectParameter("owner_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("sp_dropdiagram", diagramnameParameter, owner_idParameter);
        }
    
        public virtual ObjectResult<sp_helpdiagramdefinition_Result> sp_helpdiagramdefinition(string diagramname, Nullable<int> owner_id)
        {
            var diagramnameParameter = diagramname != null ?
                new ObjectParameter("diagramname", diagramname) :
                new ObjectParameter("diagramname", typeof(string));
    
            var owner_idParameter = owner_id.HasValue ?
                new ObjectParameter("owner_id", owner_id) :
                new ObjectParameter("owner_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<sp_helpdiagramdefinition_Result>("sp_helpdiagramdefinition", diagramnameParameter, owner_idParameter);
        }
    
        public virtual ObjectResult<sp_helpdiagrams_Result> sp_helpdiagrams(string diagramname, Nullable<int> owner_id)
        {
            var diagramnameParameter = diagramname != null ?
                new ObjectParameter("diagramname", diagramname) :
                new ObjectParameter("diagramname", typeof(string));
    
            var owner_idParameter = owner_id.HasValue ?
                new ObjectParameter("owner_id", owner_id) :
                new ObjectParameter("owner_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<sp_helpdiagrams_Result>("sp_helpdiagrams", diagramnameParameter, owner_idParameter);
        }
    
        public virtual int sp_renamediagram(string diagramname, Nullable<int> owner_id, string new_diagramname)
        {
            var diagramnameParameter = diagramname != null ?
                new ObjectParameter("diagramname", diagramname) :
                new ObjectParameter("diagramname", typeof(string));
    
            var owner_idParameter = owner_id.HasValue ?
                new ObjectParameter("owner_id", owner_id) :
                new ObjectParameter("owner_id", typeof(int));
    
            var new_diagramnameParameter = new_diagramname != null ?
                new ObjectParameter("new_diagramname", new_diagramname) :
                new ObjectParameter("new_diagramname", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("sp_renamediagram", diagramnameParameter, owner_idParameter, new_diagramnameParameter);
        }
    
        public virtual int sp_upgraddiagrams()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("sp_upgraddiagrams");
        }
    
        [DbFunction("cims11Entities", "SLA02_2025")]
        public virtual IQueryable<SLA02_2025_Result> SLA02_2025()
        {
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<SLA02_2025_Result>("[cims11Entities].[SLA02_2025]()");
        }
    
        [DbFunction("cims11Entities", "SLA04_2025")]
        public virtual IQueryable<SLA04_2025_Result> SLA04_2025()
        {
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<SLA04_2025_Result>("[cims11Entities].[SLA04_2025]()");
        }
    }
}
