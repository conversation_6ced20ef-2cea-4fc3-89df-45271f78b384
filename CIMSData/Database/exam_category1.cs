//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CIMSData.Database
{
    using System;
    using System.Collections.Generic;
    
    public partial class exam_category1
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public exam_category1()
        {
            this.tasks = new HashSet<tasks>();
        }
    
        public int category_id { get; set; }
        public int base_cost { get; set; }
        public double size_factor { get; set; }
        public double section_load_factor { get; set; }
        public double acquisition_load_factor { get; set; }
        public int force_multithreading { get; set; }
        public int empty_low { get; set; }
        public int empty_high { get; set; }
        public int reliable_low { get; set; }
        public int reliable_high { get; set; }
        public int lower_bound { get; set; }
        public int upper_bound { get; set; }
    
        public virtual exam_category exam_category { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<tasks> tasks { get; set; }
    }
}
