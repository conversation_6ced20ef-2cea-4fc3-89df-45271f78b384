//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CIMSData.Database
{
    using System;
    using System.Collections.Generic;
    
    public partial class exam_package
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public exam_package()
        {
            this.evidence = new HashSet<evidence>();
            this.hard_drive_history = new HashSet<hard_drive_history>();
        }
    
        public int exam_package_id { get; set; }
        public Nullable<int> contact_id { get; set; }
        public System.DateTime date_logged { get; set; }
        public string description { get; set; }
        public int entered_by { get; set; }
        public Nullable<int> exam_id { get; set; }
        public bool into_lab { get; set; }
        public int mail_service_id { get; set; }
        public string tracking_number { get; set; }
    
        public virtual account account { get; set; }
        public virtual contact contact { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<evidence> evidence { get; set; }
        public virtual exam exam { get; set; }
        public virtual mail_service mail_service { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<hard_drive_history> hard_drive_history { get; set; }
    }
}
