//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CIMSData.Database
{
    using System;
    using System.Collections.Generic;
    
    public partial class evidence_backup
    {
        public int evidence_id { get; set; }
        public int case_id { get; set; }
        public Nullable<System.DateTime> date_seized { get; set; }
        public string evidence_description { get; set; }
        public Nullable<int> evidence_parent_id { get; set; }
        public Nullable<int> evidence_tag_id { get; set; }
        public int evidence_type_id { get; set; }
        public Nullable<int> exam_package_id { get; set; }
        public string label { get; set; }
        public string make { get; set; }
        public string model { get; set; }
        public string nas_image { get; set; }
        public string serialno { get; set; }
        public Nullable<double> storage_size { get; set; }
        public Nullable<bool> acquired { get; set; }
        public string marked { get; set; }
        public string referred_to { get; set; }
        public Nullable<bool> md5_match { get; set; }
        public string md5_mismatch_reason { get; set; }
    }
}
