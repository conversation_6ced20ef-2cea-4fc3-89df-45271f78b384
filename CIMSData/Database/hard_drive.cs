//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CIMSData.Database
{
    using System;
    using System.Collections.Generic;
    
    public partial class hard_drive
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public hard_drive()
        {
            this.hard_drive_history = new HashSet<hard_drive_history>();
        }
    
        public int hard_drive_id { get; set; }
        public string description { get; set; }
        public string make { get; set; }
        public Nullable<int> mb_number { get; set; }
        public string model { get; set; }
        public string serialno { get; set; }
        public Nullable<double> storage_size { get; set; }
        public bool available { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<hard_drive_history> hard_drive_history { get; set; }
    }
}
