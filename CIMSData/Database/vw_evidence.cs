//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CIMSData.Database
{
    using System;
    using System.Collections.Generic;
    
    public partial class vw_evidence
    {
        public int evidence_id { get; set; }
        public int case_id { get; set; }
        public string case_number { get; set; }
        public Nullable<System.DateTime> date_seized { get; set; }
        public System.DateTime date_logged { get; set; }
        public int evidence_type_id { get; set; }
        public string evidence_type { get; set; }
        public string evidence_description { get; set; }
        public Nullable<int> evidence_parent_id { get; set; }
        public string label { get; set; }
        public string make { get; set; }
        public string model { get; set; }
        public string nas_image { get; set; }
        public string serialno { get; set; }
        public Nullable<double> storage_size { get; set; }
        public Nullable<int> evidence_tag_id { get; set; }
        public string tag_number { get; set; }
        public string tag_type { get; set; }
        public Nullable<int> evidence_tag_type_id { get; set; }
        public System.DateTime date_updated { get; set; }
        public int evidence_status_id { get; set; }
        public string evidence_status { get; set; }
        public int entered_by { get; set; }
        public string name { get; set; }
        public Nullable<System.DateTime> date_acquisition_started { get; set; }
    }
}
