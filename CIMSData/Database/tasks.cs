//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CIMSData.Database
{
    using System;
    using System.Collections.Generic;
    
    public partial class tasks
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public tasks()
        {
            this.evidence_times = new HashSet<evidence_times>();
            this.exam_category1 = new HashSet<exam_category1>();
        }
    
        public int id { get; set; }
        public string task_name { get; set; }
        public int base_threads { get; set; }
        public int max_threads { get; set; }
        public int max_per_type { get; set; }
        public int multithreading_divisor { get; set; }
        public int default_low { get; set; }
        public int default_high { get; set; }
        public bool run_on_duplicate { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<evidence_times> evidence_times { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<exam_category1> exam_category1 { get; set; }
    }
}
