//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CIMSData.Database
{
    using System;
    using System.Collections.Generic;
    
    public partial class unlock_srvc_evidence
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public unlock_srvc_evidence()
        {
            this.unlock_srvc_comment = new HashSet<unlock_srvc_comment>();
        }
    
        public int unlock_srvc_evidence_id { get; set; }
        public int evidence_id { get; set; }
        public int exam_id { get; set; }
        public Nullable<System.DateTime> unlock_started_date { get; set; }
        public Nullable<System.DateTime> unlock_ended_date { get; set; }
        public Nullable<int> unlock_srvc_lock_type_id { get; set; }
        public Nullable<bool> unlock_status { get; set; }
        public Nullable<bool> unlock_request_approved { get; set; }
        public string dongle_number { get; set; }
        public string software_version { get; set; }
        public string laptop_color { get; set; }
        public Nullable<bool> token_restored { get; set; }
        public Nullable<int> unlock_srvc_group_id { get; set; }
        public int unlock_srvc_id { get; set; }
        public Nullable<bool> token_used { get; set; }
        public string passcode { get; set; }
    
        public virtual evidence evidence { get; set; }
        public virtual exam exam { get; set; }
        public virtual unlock_srvc unlock_srvc { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<unlock_srvc_comment> unlock_srvc_comment { get; set; }
        public virtual unlock_srvc_lock_type unlock_srvc_lock_type { get; set; }
        public virtual unlock_srvc_group unlock_srvc_group { get; set; }
    }
}
