//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CIMSData.Database
{
    using System;
    using System.Collections.Generic;
    
    public partial class contact
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public contact()
        {
            this.agency_org = new HashSet<agency_org>();
            this.@case = new HashSet<@case>();
            this.case1 = new HashSet<@case>();
            this.case_trial = new HashSet<case_trial>();
            this.case_trial1 = new HashSet<case_trial>();
            this.exam = new HashSet<exam>();
            this.exam1 = new HashSet<exam>();
            this.exam_package = new HashSet<exam_package>();
            this.communication = new HashSet<communication>();
        }
    
        public int contact_id { get; set; }
        public string address1 { get; set; }
        public string address2 { get; set; }
        public string cellphone { get; set; }
        public string city { get; set; }
        public string commercial { get; set; }
        public Nullable<int> country_id { get; set; }
        public string dsn { get; set; }
        public string email { get; set; }
        public string fax { get; set; }
        public string first_name { get; set; }
        public string last_name { get; set; }
        public string sid { get; set; }
        public Nullable<int> state_id { get; set; }
        public string title { get; set; }
        public string zip { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<agency_org> agency_org { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<@case> @case { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<@case> case1 { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<case_trial> case_trial { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<case_trial> case_trial1 { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<exam> exam { get; set; }
        public virtual country country { get; set; }
        public virtual state state { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<exam> exam1 { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<exam_package> exam_package { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<communication> communication { get; set; }
    }
}
