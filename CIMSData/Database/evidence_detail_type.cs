//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CIMSData.Database
{
    using System;
    using System.Collections.Generic;
    
    public partial class evidence_detail_type
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public evidence_detail_type()
        {
            this.evidence_detail = new HashSet<evidence_detail>();
            this.evidence_type_field = new HashSet<evidence_type_field>();
            this.evidence_type_field1 = new HashSet<evidence_type_field>();
        }
    
        public int evidence_detail_type_id { get; set; }
        public string detail_type { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<evidence_detail> evidence_detail { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<evidence_type_field> evidence_type_field { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<evidence_type_field> evidence_type_field1 { get; set; }
    }
}
