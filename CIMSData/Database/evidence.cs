//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CIMSData.Database
{
    using System;
    using System.Collections.Generic;
    
    public partial class evidence
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public evidence()
        {
            this.cm_history_evidence = new HashSet<cm_history_evidence>();
            this.eh_evidence = new HashSet<eh_evidence>();
            this.evidence_note = new HashSet<evidence_note>();
            this.evidence_image = new HashSet<evidence_image>();
            this.evidence_config_item_detail = new HashSet<evidence_config_item_detail>();
            this.evidence_configuration_item = new HashSet<evidence_configuration_item>();
            this.evidence_history = new HashSet<evidence_history>();
            this.evidence_detail = new HashSet<evidence_detail>();
            this.evidence1 = new HashSet<evidence>();
            this.evidence11 = new HashSet<evidence>();
            this.exam_evidence = new HashSet<exam_evidence>();
            this.unlock_srvc_evidence = new HashSet<unlock_srvc_evidence>();
            this.exam_activity = new HashSet<exam_activity>();
        }
    
        public int evidence_id { get; set; }
        public int case_id { get; set; }
        public string evidence_description { get; set; }
        public Nullable<int> evidence_parent_id { get; set; }
        public Nullable<int> evidence_tag_id { get; set; }
        public int evidence_type_id { get; set; }
        public Nullable<int> exam_package_id { get; set; }
        public string label { get; set; }
        public string make { get; set; }
        public string model { get; set; }
        public string nas_image { get; set; }
        public string serialno { get; set; }
        public Nullable<double> storage_size { get; set; }
        public Nullable<bool> acquired { get; set; }
        public string marked { get; set; }
        public string referred_to { get; set; }
        public Nullable<bool> md5_match { get; set; }
        public string md5_mismatch_reason { get; set; }
        public Nullable<System.DateTime> date_acquisition_started { get; set; }
    
        public virtual @case @case { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<cm_history_evidence> cm_history_evidence { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<eh_evidence> eh_evidence { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<evidence_note> evidence_note { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<evidence_image> evidence_image { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<evidence_config_item_detail> evidence_config_item_detail { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<evidence_configuration_item> evidence_configuration_item { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<evidence_history> evidence_history { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<evidence_detail> evidence_detail { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<evidence> evidence1 { get; set; }
        public virtual evidence evidence2 { get; set; }
        public virtual evidence_tag evidence_tag { get; set; }
        public virtual evidence_type evidence_type { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<evidence> evidence11 { get; set; }
        public virtual evidence evidence3 { get; set; }
        public virtual exam_package exam_package { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<exam_evidence> exam_evidence { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<unlock_srvc_evidence> unlock_srvc_evidence { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<exam_activity> exam_activity { get; set; }
    }
}
