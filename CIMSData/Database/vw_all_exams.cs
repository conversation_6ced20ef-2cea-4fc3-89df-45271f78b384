//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CIMSData.Database
{
    using System;
    using System.Collections.Generic;
    
    public partial class vw_all_exams
    {
        public int exam_id { get; set; }
        public string exam_number { get; set; }
        public string exam_status { get; set; }
        public string exam_type { get; set; }
        public Nullable<System.DateTime> date_opened { get; set; }
        public Nullable<System.DateTime> date_closed { get; set; }
        public Nullable<int> DIL { get; set; }
        public string section { get; set; }
        public int section_id { get; set; }
        public string exam_investigation_type { get; set; }
        public string lead_acq { get; set; }
        public string examiner { get; set; }
        public Nullable<System.DateTime> suspense_ie { get; set; }
        public Nullable<System.DateTime> suspense_lab { get; set; }
        public string workflow_status { get; set; }
        public Nullable<System.DateTime> pre_exam_start { get; set; }
        public Nullable<System.DateTime> pre_exam_end { get; set; }
        public Nullable<int> days_in_pre_exam { get; set; }
        public Nullable<System.DateTime> intake_start { get; set; }
        public Nullable<System.DateTime> intake_end { get; set; }
        public Nullable<int> days_in_intake { get; set; }
        public Nullable<System.DateTime> acq_queue_start { get; set; }
        public Nullable<System.DateTime> acq_queue_end { get; set; }
        public Nullable<int> days_in_acq_queue { get; set; }
        public Nullable<System.DateTime> acq_start { get; set; }
        public Nullable<System.DateTime> acq_end { get; set; }
        public Nullable<int> days_in_acq { get; set; }
        public Nullable<System.DateTime> acq_review_start { get; set; }
        public Nullable<System.DateTime> acq_review_end { get; set; }
        public Nullable<int> days_in_acq_review { get; set; }
        public Nullable<int> total_days_acquisition { get; set; }
        public Nullable<System.DateTime> exam_queue_start { get; set; }
        public Nullable<System.DateTime> exam_queue_end { get; set; }
        public Nullable<int> days_in_exam_queue { get; set; }
        public Nullable<System.DateTime> exam_start { get; set; }
        public Nullable<System.DateTime> exam_end { get; set; }
        public Nullable<int> days_in_examination { get; set; }
        public Nullable<System.DateTime> exam_review_start { get; set; }
        public Nullable<System.DateTime> exam_review_end { get; set; }
        public Nullable<int> days_in_exam_review { get; set; }
        public Nullable<int> total_days_examination { get; set; }
        public Nullable<System.DateTime> qa_start { get; set; }
        public Nullable<System.DateTime> qa_end { get; set; }
        public Nullable<int> days_in_QA { get; set; }
        public Nullable<System.DateTime> ship_out_start { get; set; }
        public Nullable<System.DateTime> ship_out_end { get; set; }
        public Nullable<int> days_in_ship_out { get; set; }
        public Nullable<bool> correct_acq { get; set; }
        public Nullable<bool> correct_exam { get; set; }
        public Nullable<bool> correct_qa { get; set; }
        public Nullable<int> ev_items { get; set; }
        public string ev_size { get; set; }
        public string ev_short_list { get; set; }
        public string exam_category { get; set; }
        public string exam_subcategory { get; set; }
        public string agency { get; set; }
        public string unit { get; set; }
        public string directorate { get; set; }
        public bool expedite { get; set; }
        public bool pretrial_confinement { get; set; }
        public bool sap_handling { get; set; }
        public string classification { get; set; }
        public string subjects { get; set; }
    }
}
