//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CIMSData.Database
{
    using System;
    using System.Collections.Generic;
    
    public partial class @case
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public @case()
        {
            this.case_note = new HashSet<case_note>();
            this.case_subject = new HashSet<case_subject>();
            this.case_victim = new HashSet<case_victim>();
            this.evidence = new HashSet<evidence>();
            this.exam = new HashSet<exam>();
            this.account = new HashSet<account>();
            this.case_info_value = new HashSet<case_info_value>();
        }
    
        public int case_id { get; set; }
        public Nullable<int> agency_org_id { get; set; }
        public Nullable<int> case_agent_id { get; set; }
        public string case_number { get; set; }
        public int classification_id { get; set; }
        public Nullable<System.DateTime> date_trial { get; set; }
        public bool government_owned { get; set; }
        public Nullable<int> jurisdiction_id { get; set; }
        public bool permission { get; set; }
        public bool pretrial_confinement { get; set; }
        public bool priorlook { get; set; }
        public Nullable<int> proceeding_id { get; set; }
        public Nullable<int> prosecutor_id { get; set; }
        public bool sap_handling { get; set; }
        public bool statement_subject { get; set; }
        public bool statement_witness { get; set; }
        public bool warrant { get; set; }
        public bool inside_threat { get; set; }
    
        public virtual agency_org agency_org { get; set; }
        public virtual contact contact { get; set; }
        public virtual classification classification { get; set; }
        public virtual jurisdiction jurisdiction { get; set; }
        public virtual proceeding proceeding { get; set; }
        public virtual contact contact1 { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<case_note> case_note { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<case_subject> case_subject { get; set; }
        public virtual case_trial case_trial { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<case_victim> case_victim { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<evidence> evidence { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<exam> exam { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<account> account { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<case_info_value> case_info_value { get; set; }
    }
}
