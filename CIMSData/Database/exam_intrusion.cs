//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CIMSData.Database
{
    using System;
    using System.Collections.Generic;
    
    public partial class exam_intrusion
    {
        public int exam_id { get; set; }
        public string additional_info { get; set; }
        public string background { get; set; }
        public bool decoder { get; set; }
        public Nullable<int> directorate_id { get; set; }
        public string family { get; set; }
        public Nullable<bool> is_lab_source { get; set; }
        public bool parser { get; set; }
        public Nullable<System.DateTime> submission_date { get; set; }
        public Nullable<System.DateTime> approval_date { get; set; }
        public Nullable<bool> high_priority { get; set; }
        public Nullable<bool> ems_updated { get; set; }
        public string exam_title { get; set; }
    
        public virtual directorate directorate { get; set; }
        public virtual exam exam { get; set; }
    }
}
