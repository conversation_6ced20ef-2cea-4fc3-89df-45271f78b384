//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CIMSData.Database
{
    using System;
    using System.Collections.Generic;
    
    public partial class hard_drive_history
    {
        public int hard_drive_history_id { get; set; }
        public System.DateTime date_logged { get; set; }
        public int entered_by { get; set; }
        public Nullable<int> exam_id { get; set; }
        public Nullable<int> exam_package_id { get; set; }
        public int hard_drive_id { get; set; }
        public int hard_drive_status_id { get; set; }
    
        public virtual account account { get; set; }
        public virtual exam exam { get; set; }
        public virtual exam_package exam_package { get; set; }
        public virtual hard_drive hard_drive { get; set; }
        public virtual hard_drive_status hard_drive_status { get; set; }
    }
}
