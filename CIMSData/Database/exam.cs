//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CIMSData.Database
{
    using System;
    using System.Collections.Generic;
    
    public partial class exam
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public exam()
        {
            this.account_activity = new HashSet<account_activity>();
            this.cm_history = new HashSet<cm_history>();
            this.communication = new HashSet<communication>();
            this.eh_ticket = new HashSet<eh_ticket>();
            this.evidence_config_item_detail = new HashSet<evidence_config_item_detail>();
            this.evidence_configuration_item = new HashSet<evidence_configuration_item>();
            this.evidence_note = new HashSet<evidence_note>();
            this.exam_activity = new HashSet<exam_activity>();
            this.exam_event = new HashSet<exam_event>();
            this.exam_evidence = new HashSet<exam_evidence>();
            this.exam_note = new HashSet<exam_note>();
            this.exam_package = new HashSet<exam_package>();
            this.hard_drive_history = new HashSet<hard_drive_history>();
            this.unlock_srvc_evidence = new HashSet<unlock_srvc_evidence>();
        }
    
        public int exam_id { get; set; }
        public Nullable<int> case_id { get; set; }
        public Nullable<System.DateTime> date_closed { get; set; }
        public Nullable<System.DateTime> date_opened { get; set; }
        public Nullable<int> exam_category_id { get; set; }
        public string exam_number { get; set; }
        public int exam_status_id { get; set; }
        public Nullable<int> exam_type_id { get; set; }
        public bool expedite { get; set; }
        public int group_id { get; set; }
        public Nullable<int> lead_examiner_id { get; set; }
        public Nullable<int> evidence_return_address_id { get; set; }
        public Nullable<int> exam_investigation_type_id { get; set; }
        public Nullable<int> report_return_address_id { get; set; }
        public string rejected_reason { get; set; }
    
        public virtual account account { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<account_activity> account_activity { get; set; }
        public virtual @case @case { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<cm_history> cm_history { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<communication> communication { get; set; }
        public virtual contact contact { get; set; }
        public virtual contact contact1 { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<eh_ticket> eh_ticket { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<evidence_config_item_detail> evidence_config_item_detail { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<evidence_configuration_item> evidence_configuration_item { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<evidence_note> evidence_note { get; set; }
        public virtual exam_investigation_type exam_investigation_type { get; set; }
        public virtual exam_category exam_category { get; set; }
        public virtual exam_status exam_status { get; set; }
        public virtual exam_type exam_type { get; set; }
        public virtual group group { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<exam_activity> exam_activity { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<exam_event> exam_event { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<exam_evidence> exam_evidence { get; set; }
        public virtual exam_intrusion exam_intrusion { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<exam_note> exam_note { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<exam_package> exam_package { get; set; }
        public virtual exam_standard exam_standard { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<hard_drive_history> hard_drive_history { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<unlock_srvc_evidence> unlock_srvc_evidence { get; set; }
    }
}
