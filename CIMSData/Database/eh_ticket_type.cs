//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CIMSData.Database
{
    using System;
    using System.Collections.Generic;
    
    public partial class eh_ticket_type
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public eh_ticket_type()
        {
            this.eh_ticket = new HashSet<eh_ticket>();
            this.eh_ticket_type_members = new HashSet<eh_ticket_type_members>();
        }
    
        public int id { get; set; }
        public string ticket_type { get; set; }
        public bool enabled { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<eh_ticket> eh_ticket { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<eh_ticket_type_members> eh_ticket_type_members { get; set; }
    }
}
